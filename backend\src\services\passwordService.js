/**
 * Password Management Service
 * Handles password hashing, validation, reset, and security policies
 */

const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');
const smsService = require('./sms');

class PasswordService {
  constructor() {
    this.saltRounds = config.security.hashSaltRounds;
    this.passwordHistoryLimit = 5; // Remember last 5 passwords
    this.resetTokenExpiry = 10 * 60; // 10 minutes
    this.maxResetAttempts = 3; // Max reset attempts per hour
  }

  /**
   * Hash a password
   */
  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, this.saltRounds);
    } catch (error) {
      logger.error('Failed to hash password:', error);
      throw new Error('Password hashing failed');
    }
  }

  /**
   * Verify a password against hash
   */
  async verifyPassword(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      logger.error('Failed to verify password:', error);
      return false;
    }
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password) {
    const errors = [];
    
    // Minimum length
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    // Maximum length
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters long');
    }
    
    // Must contain lowercase letter
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    // Must contain number
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    // Must contain special character (optional but recommended)
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      // This is a warning, not an error
    }
    
    // Check for common weak passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', '12345678', 'welcome', 'admin', 'letmein'
    ];
    
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common and easily guessable');
    }
    
    // Check for sequential characters
    if (/123456|abcdef|qwerty/i.test(password)) {
      errors.push('Password should not contain sequential characters');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      strength: this.calculatePasswordStrength(password)
    };
  }

  /**
   * Calculate password strength score
   */
  calculatePasswordStrength(password) {
    let score = 0;
    
    // Length bonus
    score += Math.min(password.length * 2, 20);
    
    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/\d/.test(password)) score += 5;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 10;
    
    // Penalty for common patterns
    if (/(.)\1{2,}/.test(password)) score -= 10; // Repeated characters
    if (/123|abc|qwe/i.test(password)) score -= 5; // Sequential patterns
    
    // Normalize to 0-100 scale
    score = Math.max(0, Math.min(100, score));
    
    if (score < 30) return 'weak';
    if (score < 60) return 'medium';
    if (score < 80) return 'strong';
    return 'very_strong';
  }

  /**
   * Check if password was used recently
   */
  async checkPasswordHistory(userId, newPassword) {
    try {
      const historyKey = `password_history:${userId}`;
      const passwordHistory = await redisService.get(historyKey) || [];
      
      for (const oldHash of passwordHistory) {
        if (await this.verifyPassword(newPassword, oldHash)) {
          return true; // Password was used before
        }
      }
      
      return false;
    } catch (error) {
      logger.error('Failed to check password history:', error);
      return false; // Allow password change if check fails
    }
  }

  /**
   * Update password history
   */
  async updatePasswordHistory(userId, passwordHash) {
    try {
      const historyKey = `password_history:${userId}`;
      const passwordHistory = await redisService.get(historyKey) || [];
      
      // Add new password hash to history
      passwordHistory.unshift(passwordHash);
      
      // Keep only the last N passwords
      if (passwordHistory.length > this.passwordHistoryLimit) {
        passwordHistory.splice(this.passwordHistoryLimit);
      }
      
      // Store for 1 year
      await redisService.set(historyKey, passwordHistory, 365 * 24 * 60 * 60);
      
      logger.audit('Password history updated', { userId });
    } catch (error) {
      logger.error('Failed to update password history:', error);
    }
  }

  /**
   * Initiate password reset
   */
  async initiatePasswordReset(phoneNumber, userId) {
    try {
      // Check reset attempt limits
      const attemptKey = `reset_attempts:${phoneNumber}`;
      const attempts = await redisService.get(attemptKey) || 0;
      
      if (attempts >= this.maxResetAttempts) {
        throw new Error('Too many reset attempts. Please try again later.');
      }
      
      // Generate secure reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetOTP = this.generateOTP();
      
      // Store reset data
      const resetData = {
        token: resetToken,
        otp: resetOTP,
        userId,
        phoneNumber,
        timestamp: Date.now(),
        attempts: 0
      };
      
      const resetKey = `password_reset:${phoneNumber}`;
      await redisService.set(resetKey, resetData, this.resetTokenExpiry);
      
      // Increment attempt counter
      await redisService.set(attemptKey, attempts + 1, 60 * 60); // 1 hour
      
      // Send OTP via SMS
      await smsService.sendOTP(phoneNumber, resetOTP, 'password_reset');
      
      logger.audit('Password reset initiated', {
        userId,
        phoneNumber,
        resetToken: resetToken.substring(0, 8) + '...' // Log partial token for debugging
      });
      
      return {
        success: true,
        resetToken,
        expiresIn: this.resetTokenExpiry
      };
      
    } catch (error) {
      logger.error('Failed to initiate password reset:', error);
      throw error;
    }
  }

  /**
   * Verify password reset OTP
   */
  async verifyPasswordResetOTP(phoneNumber, otp, resetToken) {
    try {
      const resetKey = `password_reset:${phoneNumber}`;
      const resetData = await redisService.get(resetKey);
      
      if (!resetData) {
        throw new Error('Reset request not found or expired');
      }
      
      if (resetData.token !== resetToken) {
        throw new Error('Invalid reset token');
      }
      
      if (resetData.otp !== otp) {
        // Increment failed attempts
        resetData.attempts = (resetData.attempts || 0) + 1;
        
        if (resetData.attempts >= 3) {
          await redisService.del(resetKey);
          throw new Error('Too many failed attempts. Please start over.');
        }
        
        await redisService.set(resetKey, resetData, this.resetTokenExpiry);
        throw new Error('Invalid OTP');
      }
      
      // Mark as verified
      resetData.verified = true;
      await redisService.set(resetKey, resetData, this.resetTokenExpiry);
      
      logger.audit('Password reset OTP verified', {
        userId: resetData.userId,
        phoneNumber
      });
      
      return {
        success: true,
        userId: resetData.userId
      };
      
    } catch (error) {
      logger.error('Failed to verify password reset OTP:', error);
      throw error;
    }
  }

  /**
   * Complete password reset
   */
  async completePasswordReset(phoneNumber, resetToken, newPassword) {
    try {
      const resetKey = `password_reset:${phoneNumber}`;
      const resetData = await redisService.get(resetKey);
      
      if (!resetData || !resetData.verified || resetData.token !== resetToken) {
        throw new Error('Invalid or unverified reset request');
      }
      
      // Validate new password
      const validation = this.validatePasswordStrength(newPassword);
      if (!validation.isValid) {
        throw new Error(`Password validation failed: ${validation.errors.join(', ')}`);
      }
      
      // Check password history
      const wasUsedBefore = await this.checkPasswordHistory(resetData.userId, newPassword);
      if (wasUsedBefore) {
        throw new Error('Cannot reuse a recent password');
      }
      
      // Hash new password
      const passwordHash = await this.hashPassword(newPassword);
      
      // Update password history
      await this.updatePasswordHistory(resetData.userId, passwordHash);
      
      // Clean up reset data
      await redisService.del(resetKey);
      await redisService.del(`reset_attempts:${phoneNumber}`);
      
      logger.audit('Password reset completed', {
        userId: resetData.userId,
        phoneNumber,
        passwordStrength: validation.strength
      });
      
      return {
        success: true,
        passwordHash,
        userId: resetData.userId
      };
      
    } catch (error) {
      logger.error('Failed to complete password reset:', error);
      throw error;
    }
  }

  /**
   * Change password (authenticated user)
   */
  async changePassword(userId, currentPassword, newPassword, currentPasswordHash) {
    try {
      // Verify current password
      const isCurrentValid = await this.verifyPassword(currentPassword, currentPasswordHash);
      if (!isCurrentValid) {
        throw new Error('Current password is incorrect');
      }
      
      // Validate new password
      const validation = this.validatePasswordStrength(newPassword);
      if (!validation.isValid) {
        throw new Error(`Password validation failed: ${validation.errors.join(', ')}`);
      }
      
      // Check if new password is same as current
      const isSamePassword = await this.verifyPassword(newPassword, currentPasswordHash);
      if (isSamePassword) {
        throw new Error('New password must be different from current password');
      }
      
      // Check password history
      const wasUsedBefore = await this.checkPasswordHistory(userId, newPassword);
      if (wasUsedBefore) {
        throw new Error('Cannot reuse a recent password');
      }
      
      // Hash new password
      const newPasswordHash = await this.hashPassword(newPassword);
      
      // Update password history
      await this.updatePasswordHistory(userId, newPasswordHash);
      
      logger.audit('Password changed', {
        userId,
        passwordStrength: validation.strength
      });
      
      return {
        success: true,
        passwordHash: newPasswordHash,
        strength: validation.strength
      };
      
    } catch (error) {
      logger.error('Failed to change password:', error);
      throw error;
    }
  }

  /**
   * Generate OTP
   */
  generateOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Check if account is locked due to failed login attempts
   */
  async checkAccountLockout(phoneNumber) {
    try {
      const lockoutKey = `lockout:${phoneNumber}`;
      const lockoutData = await redisService.get(lockoutKey);
      
      if (lockoutData && lockoutData.lockedUntil > Date.now()) {
        return {
          isLocked: true,
          lockedUntil: new Date(lockoutData.lockedUntil),
          attempts: lockoutData.attempts
        };
      }
      
      return { isLocked: false };
    } catch (error) {
      logger.error('Failed to check account lockout:', error);
      return { isLocked: false };
    }
  }

  /**
   * Record failed login attempt
   */
  async recordFailedLogin(phoneNumber) {
    try {
      const lockoutKey = `lockout:${phoneNumber}`;
      const lockoutData = await redisService.get(lockoutKey) || { attempts: 0 };
      
      lockoutData.attempts += 1;
      lockoutData.lastAttempt = Date.now();
      
      // Lock account after 5 failed attempts
      if (lockoutData.attempts >= 5) {
        lockoutData.lockedUntil = Date.now() + (30 * 60 * 1000); // 30 minutes
        
        logger.security('Account locked due to failed login attempts', {
          phoneNumber,
          attempts: lockoutData.attempts
        });
      }
      
      await redisService.set(lockoutKey, lockoutData, 60 * 60); // 1 hour
      
      return lockoutData;
    } catch (error) {
      logger.error('Failed to record failed login:', error);
      return null;
    }
  }

  /**
   * Clear failed login attempts
   */
  async clearFailedLogins(phoneNumber) {
    try {
      const lockoutKey = `lockout:${phoneNumber}`;
      await redisService.del(lockoutKey);
    } catch (error) {
      logger.error('Failed to clear failed logins:', error);
    }
  }
}

// Create singleton instance
const passwordService = new PasswordService();

module.exports = passwordService;
