#!/usr/bin/env node

/**
 * Data Rights Production Verification Script
 * 
 * Verifies that all "Your Data Rights" features are production-ready and legally compliant
 * with East African data protection regulations.
 */

const fs = require('fs');
const path = require('path');

class DataRightsVerifier {
  constructor() {
    this.results = {
      overall: 'PENDING',
      checks: [],
      errors: [],
      warnings: [],
      recommendations: []
    };
  }

  async runVerification() {
    console.log('🔍 Starting Data Rights Production Verification...\n');

    try {
      // Check screen implementations
      await this.checkScreenImplementations();
      
      // Check navigation setup
      await this.checkNavigationSetup();
      
      // Check legal compliance
      await this.checkLegalCompliance();
      
      // Check database schema
      await this.checkDatabaseSchema();
      
      // Check service implementations
      await this.checkServiceImplementations();
      
      // Generate final report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Verification failed:', error);
      this.results.overall = 'FAILED';
      this.results.errors.push(`Verification error: ${error.message}`);
    }
  }

  async checkScreenImplementations() {
    console.log('📱 Checking Screen Implementations...');
    
    const requiredScreens = [
      'screens/PrivacyPolicyScreen.js',
      'screens/DataProtectionScreen.js',
      'screens/PrivacyControlsScreen.js'
    ];

    for (const screen of requiredScreens) {
      const screenPath = path.join(__dirname, '..', screen);
      if (fs.existsSync(screenPath)) {
        this.addCheck(`✅ ${screen} exists`);
        
        const content = fs.readFileSync(screenPath, 'utf8');
        this.checkScreenContent(screen, content);
      } else {
        this.addError(`❌ Missing screen: ${screen}`);
      }
    }
  }

  checkScreenContent(screenName, content) {
    const screenChecks = {
      'PrivacyPolicyScreen.js': [
        'East African data protection',
        'Uganda Data Protection Act',
        'Kenya Data Protection Act',
        'Tanzania Data Protection Act',
        'GDPR',
        '<EMAIL>',
        'expandedSections',
        'privacyPolicySections'
      ],
      'DataProtectionScreen.js': [
        'data subject requests',
        'Right to Access',
        'Right to Rectification',
        'Right to Erasure',
        'Right to Data Portability',
        'submitDataRequest',
        'handleComplaint'
      ],
      'PrivacyControlsScreen.js': [
        'Export My Data',
        'Privacy Policy',
        'Data Protection',
        'navigation.navigate',
        'PrivacyPolicy',
        'DataProtection'
      ]
    };

    const fileName = screenName.split('/').pop();
    const checks = screenChecks[fileName] || [];
    
    for (const check of checks) {
      if (content.includes(check)) {
        this.addCheck(`✅ ${screenName} includes ${check}`);
      } else {
        this.addError(`❌ ${screenName} missing ${check}`);
      }
    }
  }

  async checkNavigationSetup() {
    console.log('🧭 Checking Navigation Setup...');
    
    const appPath = path.join(__dirname, '..', 'App.js');
    if (fs.existsSync(appPath)) {
      const content = fs.readFileSync(appPath, 'utf8');
      
      if (content.includes('PrivacyPolicyScreen')) {
        this.addCheck('✅ PrivacyPolicyScreen imported in App.js');
      } else {
        this.addError('❌ PrivacyPolicyScreen not imported in App.js');
      }
      
      if (content.includes('DataProtectionScreen')) {
        this.addCheck('✅ DataProtectionScreen imported in App.js');
      } else {
        this.addError('❌ DataProtectionScreen not imported in App.js');
      }
      
      if (content.includes('name="PrivacyPolicy"')) {
        this.addCheck('✅ PrivacyPolicy route configured');
      } else {
        this.addError('❌ PrivacyPolicy route not configured');
      }
      
      if (content.includes('name="DataProtection"')) {
        this.addCheck('✅ DataProtection route configured');
      } else {
        this.addError('❌ DataProtection route not configured');
      }
    } else {
      this.addError('❌ App.js not found');
    }
  }

  async checkLegalCompliance() {
    console.log('⚖️ Checking Legal Compliance...');
    
    const complianceChecks = [
      {
        name: 'Privacy Policy Content',
        check: () => this.checkPrivacyPolicyContent()
      },
      {
        name: 'Data Protection Rights',
        check: () => this.checkDataProtectionRights()
      },
      {
        name: 'Legal Contact Information',
        check: () => this.checkLegalContactInfo()
      },
      {
        name: 'Regulatory Compliance',
        check: () => this.checkRegulatoryCompliance()
      }
    ];

    for (const complianceCheck of complianceChecks) {
      try {
        const result = complianceCheck.check();
        if (result) {
          this.addCheck(`✅ ${complianceCheck.name} compliant`);
        } else {
          this.addError(`❌ ${complianceCheck.name} not compliant`);
        }
      } catch (error) {
        this.addError(`❌ Error checking ${complianceCheck.name}: ${error.message}`);
      }
    }
  }

  checkPrivacyPolicyContent() {
    const policyPath = path.join(__dirname, '..', 'screens/PrivacyPolicyScreen.js');
    if (!fs.existsSync(policyPath)) return false;
    
    const content = fs.readFileSync(policyPath, 'utf8');
    const requiredSections = [
      'Information We Collect',
      'How We Use Your Information',
      'Legal Basis for Processing',
      'Data Sharing & Disclosure',
      'Data Security & Protection',
      'Your Rights Under East African Law',
      'Data Retention',
      'International Transfers'
    ];
    
    return requiredSections.every(section => content.includes(section));
  }

  checkDataProtectionRights() {
    const protectionPath = path.join(__dirname, '..', 'screens/DataProtectionScreen.js');
    if (!fs.existsSync(protectionPath)) return false;
    
    const content = fs.readFileSync(protectionPath, 'utf8');
    const requiredRights = [
      'Right to Access',
      'Right to Rectification',
      'Right to Erasure',
      'Right to Data Portability',
      'Right to Restrict Processing',
      'Right to Object'
    ];
    
    return requiredRights.every(right => content.includes(right));
  }

  checkLegalContactInfo() {
    const policyPath = path.join(__dirname, '..', 'screens/PrivacyPolicyScreen.js');
    if (!fs.existsSync(policyPath)) return false;
    
    const content = fs.readFileSync(policyPath, 'utf8');
    return content.includes('<EMAIL>') && 
           content.includes('+256') && 
           content.includes('Kampala');
  }

  checkRegulatoryCompliance() {
    const policyPath = path.join(__dirname, '..', 'screens/PrivacyPolicyScreen.js');
    if (!fs.existsSync(policyPath)) return false;
    
    const content = fs.readFileSync(policyPath, 'utf8');
    return content.includes('Uganda Data Protection Act 2019') &&
           content.includes('Kenya Data Protection Act 2019') &&
           content.includes('Tanzania Data Protection Act 2022') &&
           content.includes('GDPR');
  }

  async checkDatabaseSchema() {
    console.log('🗄️ Checking Database Schema...');
    
    const schemaFiles = [
      'database/legal_compliance_schema.sql'
    ];

    for (const schemaFile of schemaFiles) {
      const schemaPath = path.join(__dirname, '..', schemaFile);
      if (fs.existsSync(schemaPath)) {
        this.addCheck(`✅ ${schemaFile} exists`);
        
        const content = fs.readFileSync(schemaPath, 'utf8');
        this.checkSchemaContent(schemaFile, content);
      } else {
        this.addError(`❌ Missing schema file: ${schemaFile}`);
      }
    }
  }

  checkSchemaContent(fileName, content) {
    const requiredTables = [
      'privacy_policy_versions',
      'user_policy_acceptances',
      'data_subject_requests',
      'regulatory_compliance_logs',
      'data_breach_incidents',
      'legal_document_templates',
      'compliance_audit_trail'
    ];

    for (const table of requiredTables) {
      if (content.includes(table)) {
        this.addCheck(`✅ ${fileName} includes ${table} table`);
      } else {
        this.addError(`❌ ${fileName} missing ${table} table`);
      }
    }

    // Check for required functions
    const requiredFunctions = [
      'log_compliance_event',
      'check_user_policy_acceptance'
    ];

    for (const func of requiredFunctions) {
      if (content.includes(func)) {
        this.addCheck(`✅ ${fileName} includes ${func} function`);
      } else {
        this.addError(`❌ ${fileName} missing ${func} function`);
      }
    }
  }

  async checkServiceImplementations() {
    console.log('🔧 Checking Service Implementations...');
    
    const requiredServices = [
      'services/legalComplianceService.js'
    ];

    for (const service of requiredServices) {
      const servicePath = path.join(__dirname, '..', service);
      if (fs.existsSync(servicePath)) {
        this.addCheck(`✅ ${service} exists`);
        
        const content = fs.readFileSync(servicePath, 'utf8');
        this.checkServiceMethods(service, content);
      } else {
        this.addError(`❌ Missing service: ${service}`);
      }
    }
  }

  checkServiceMethods(serviceName, content) {
    const requiredMethods = [
      'checkPolicyAcceptance',
      'recordPolicyAcceptance',
      'submitDataSubjectRequest',
      'getUserDataSubjectRequests',
      'logComplianceEvent',
      'reportDataBreach',
      'getCurrentPrivacyPolicy'
    ];
    
    for (const method of requiredMethods) {
      if (content.includes(method)) {
        this.addCheck(`✅ ${serviceName} has ${method} method`);
      } else {
        this.addError(`❌ ${serviceName} missing ${method} method`);
      }
    }
  }

  addCheck(message) {
    this.results.checks.push(message);
    console.log(message);
  }

  addError(message) {
    this.results.errors.push(message);
    console.log(message);
  }

  addWarning(message) {
    this.results.warnings.push(message);
    console.log(message);
  }

  addRecommendation(message) {
    this.results.recommendations.push(message);
  }

  generateReport() {
    console.log('\n📊 VERIFICATION REPORT');
    console.log('='.repeat(50));
    
    const totalChecks = this.results.checks.length;
    const totalErrors = this.results.errors.length;
    const totalWarnings = this.results.warnings.length;
    
    console.log(`✅ Passed Checks: ${totalChecks}`);
    console.log(`❌ Errors: ${totalErrors}`);
    console.log(`⚠️ Warnings: ${totalWarnings}`);
    
    if (totalErrors === 0) {
      this.results.overall = totalWarnings === 0 ? 'EXCELLENT' : 'GOOD';
    } else if (totalErrors <= 2) {
      this.results.overall = 'NEEDS_IMPROVEMENT';
    } else {
      this.results.overall = 'FAILED';
    }
    
    console.log(`\n🎯 Overall Status: ${this.results.overall}`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ CRITICAL ISSUES TO FIX:');
      this.results.errors.forEach(error => console.log(`  ${error}`));
    }
    
    if (this.results.warnings.length > 0) {
      console.log('\n⚠️ WARNINGS:');
      this.results.warnings.forEach(warning => console.log(`  ${warning}`));
    }
    
    // Generate recommendations
    this.generateRecommendations();
    
    if (this.results.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      this.results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
    
    console.log('\n' + '='.repeat(50));
    
    if (this.results.overall === 'EXCELLENT' || this.results.overall === 'GOOD') {
      console.log('🎉 Data Rights system is production-ready and legally compliant!');
    } else {
      console.log('🔧 Please address the issues above before production deployment.');
    }
  }

  generateRecommendations() {
    if (this.results.errors.length > 0) {
      this.addRecommendation('Fix all critical errors before production deployment');
    }
    
    if (this.results.warnings.length > 0) {
      this.addRecommendation('Address warnings to improve compliance score');
    }
    
    this.addRecommendation('Run database migration scripts in production');
    this.addRecommendation('Test all data rights features with real user scenarios');
    this.addRecommendation('Verify legal compliance with local data protection authorities');
    this.addRecommendation('Conduct legal review of privacy policy content');
    this.addRecommendation('Test data export functionality with various data formats');
  }
}

// Run verification
const verifier = new DataRightsVerifier();
verifier.runVerification().catch(console.error);
