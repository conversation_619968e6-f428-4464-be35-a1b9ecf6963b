import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Platform,
  Clipboard,
} from 'react-native';
import { Colors } from '../constants/Colors';

/**
 * Enhanced OTP Input Component with SMS Auto-Detection and Auto-Focus
 * 
 * Features:
 * - SMS auto-detection on Android and iOS
 * - Auto-focus progression between inputs
 * - Paste support for full OTP
 * - Backspace navigation
 * - Visual feedback for filled inputs
 */
const OTPInput = ({ 
  length = 6, 
  onComplete, 
  onChangeText, 
  value = [], 
  disabled = false,
  autoFocus = true 
}) => {
  const [otp, setOtp] = useState(value);
  const [focusedIndex, setFocusedIndex] = useState(autoFocus ? 0 : -1);
  const inputRefs = useRef([]);
  const isProcessingPaste = useRef(false);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Update internal state when value prop changes
  useEffect(() => {
    if (Array.isArray(value) && value.length <= length) {
      setOtp(value);
    }
  }, [value, length]);

  // Auto-focus first input on mount
  useEffect(() => {
    if (autoFocus && inputRefs.current[0] && !disabled) {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
        setFocusedIndex(0);
      }, 100);
    }
  }, [autoFocus, disabled]);

  /**
   * Handle text change for individual input
   */
  const handleChangeText = (text, index) => {
    if (isProcessingPaste.current) return;

    // Only allow numeric input
    const numericText = text.replace(/[^0-9]/g, '');
    
    // Handle paste operation (multiple characters)
    if (numericText.length > 1) {
      handlePasteOperation(numericText, index);
      return;
    }

    // Handle single character input
    const newOtp = [...otp];
    newOtp[index] = numericText;
    
    setOtp(newOtp);
    onChangeText?.(newOtp);

    // Auto-focus next input if current input is filled
    if (numericText && index < length - 1) {
      focusNextInput(index + 1);
    }

    // Call onComplete if all inputs are filled
    if (newOtp.every(digit => digit !== '') && newOtp.length === length) {
      onComplete?.(newOtp.join(''));
    }
  };

  /**
   * Handle paste operation for full OTP
   */
  const handlePasteOperation = (pastedText, startIndex) => {
    isProcessingPaste.current = true;
    
    const digits = pastedText.slice(0, length).split('');
    const newOtp = [...otp];
    
    // Fill from the start index
    for (let i = 0; i < digits.length && (startIndex + i) < length; i++) {
      newOtp[startIndex + i] = digits[i];
    }
    
    setOtp(newOtp);
    onChangeText?.(newOtp);
    
    // Focus the next empty input or last input
    const nextEmptyIndex = newOtp.findIndex(digit => digit === '');
    const targetIndex = nextEmptyIndex !== -1 ? nextEmptyIndex : length - 1;
    
    setTimeout(() => {
      focusNextInput(targetIndex);
      isProcessingPaste.current = false;
      
      // Call onComplete if all inputs are filled
      if (newOtp.every(digit => digit !== '') && newOtp.length === length) {
        onComplete?.(newOtp.join(''));
      }
    }, 50);
  };

  /**
   * Handle key press events (backspace navigation)
   */
  const handleKeyPress = (key, index) => {
    if (key === 'Backspace') {
      if (!otp[index] && index > 0) {
        // If current input is empty, move to previous input
        focusNextInput(index - 1);
      } else if (otp[index]) {
        // If current input has value, clear it
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
        onChangeText?.(newOtp);
      }
    }
  };

  /**
   * Focus specific input
   */
  const focusNextInput = (index) => {
    if (index >= 0 && index < length && inputRefs.current[index]) {
      setTimeout(() => {
        inputRefs.current[index]?.focus();
        setFocusedIndex(index);
      }, 10);
    }
  };

  /**
   * Handle focus events
   */
  const handleFocus = (index) => {
    setFocusedIndex(index);
  };

  /**
   * Handle blur events
   */
  const handleBlur = () => {
    setFocusedIndex(-1);
  };

  /**
   * Clear all inputs
   */
  const clearInputs = () => {
    const emptyOtp = new Array(length).fill('');
    setOtp(emptyOtp);
    onChangeText?.(emptyOtp);
    if (inputRefs.current[0]) {
      focusNextInput(0);
    }
  };

  // Expose clear method to parent component
  useEffect(() => {
    if (onChangeText) {
      onChangeText.clearInputs = clearInputs;
    }
  }, [onChangeText]);

  return (
    <View style={styles.container}>
      {Array.from({ length }, (_, index) => (
        <TextInput
          key={index}
          ref={(ref) => (inputRefs.current[index] = ref)}
          style={[
            styles.input,
            otp[index] && styles.inputFilled,
            focusedIndex === index && styles.inputFocused,
            disabled && styles.inputDisabled,
          ]}
          value={otp[index] || ''}
          onChangeText={(text) => handleChangeText(text, index)}
          onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
          onFocus={() => handleFocus(index)}
          onBlur={handleBlur}
          keyboardType="numeric"
          maxLength={Platform.OS === 'android' ? 1 : length} // Android: single char, iOS: allow paste
          textAlign="center"
          editable={!disabled}
          selectTextOnFocus
          autoComplete={index === 0 ? "sms-otp" : "off"} // SMS auto-fill only on first input
          textContentType={index === 0 ? "oneTimeCode" : "none"} // iOS SMS auto-fill
          autoFocus={index === 0 && autoFocus}
          blurOnSubmit={false}
          returnKeyType="next"
          contextMenuHidden={false}
          importantForAutofill={index === 0 ? "yes" : "no"} // Android auto-fill
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 10,
  },
  input: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: Colors.neutral.warmGrayLight,
    borderRadius: 12,
    backgroundColor: Colors.neutral.white,
    color: Colors.neutral.charcoal,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  inputFilled: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.light,
  },
  inputFocused: {
    borderColor: Colors.primary.main,
    borderWidth: 2,
    shadowColor: Colors.primary.main,
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  inputDisabled: {
    opacity: 0.6,
    backgroundColor: Colors.neutral.warmGrayLight,
  },
});

export default OTPInput;
