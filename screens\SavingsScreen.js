import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import savingsService from '../services/savingsService';
import walletService from '../services/walletService';
import currencyService from '../services/currencyService';

const SavingsScreen = ({ navigation, route }) => {
  // Use theme and currency contexts
  const { theme } = useTheme();
  const { convertAndFormat } = useCurrencyContext();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(true);
  const [savingsAccounts, setSavingsAccounts] = useState([]);
  const [savingsSummary, setSavingsSummary] = useState(null);
  const [walletBalance, setWalletBalance] = useState(0);
  const [transferModalVisible, setTransferModalVisible] = useState(false);
  const [withdrawModalVisible, setWithdrawModalVisible] = useState(false);
  const [createAccountModalVisible, setCreateAccountModalVisible] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [transferAmount, setTransferAmount] = useState('');
  const [newAccountName, setNewAccountName] = useState('');
  const [newAccountGoal, setNewAccountGoal] = useState('');

  useEffect(() => {
    loadSavingsData();

    // Handle navigation from Budget Insights
    if (route?.params?.createGoal) {
      setNewAccountName(route.params.createGoal);
      if (route.params.targetAmount) {
        setNewAccountGoal(route.params.targetAmount.toString());
      }
      // Auto-open create account modal after data loads
      setTimeout(() => {
        setCreateAccountModalVisible(true);
      }, 1000);
    }
  }, [route?.params]);

  const loadSavingsData = async () => {
    try {
      console.log('🔄 Loading savings data...');
      setLoading(true);

      // Initialize services
      if (!savingsService.initialized) {
        console.log('🔧 Initializing savings service...');
        await savingsService.initialize();
      }

      // Load savings summary
      console.log('📊 Loading savings summary...');
      const summary = await savingsService.getSavingsSummary();
      console.log('📊 Savings summary result:', summary);

      if (summary.success && summary.data) {
        console.log('✅ Setting savings data:', summary.data);
        setSavingsSummary(summary.data);
        setSavingsAccounts(summary.data.accounts || []);
      } else {
        console.log('⚠️ No savings data, using defaults');
        // Ensure we always have valid data structures
        setSavingsSummary({
          total_savings: 0,
          total_accounts: 0,
          total_goals: 0,
          completed_goals: 0,
          accounts: []
        });
        setSavingsAccounts([]);
      }

      // Load wallet balance
      console.log('💰 Loading wallet balance...');
      const wallet = await walletService.getWalletBalance();
      console.log('💰 Wallet result:', wallet);

      if (wallet.success && wallet.data) {
        console.log('✅ Setting wallet balance:', wallet.data.balance);
        setWalletBalance(wallet.data.balance || 0);
      } else {
        console.log('⚠️ No wallet data, using default balance');
        setWalletBalance(0);
      }

    } catch (error) {
      console.error('❌ Error loading savings data:', error);
      Alert.alert('Error', 'Failed to load savings data');
    } finally {
      setLoading(false);
    }
  };

  // Use dynamic currency formatting from context
  const formatCurrency = (amount) => {
    try {
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return convertAndFormat(numAmount);
    } catch (error) {
      console.log('⚠️ Currency formatting error:', error);
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return convertAndFormat(numAmount);
    }
  };

  const handleTransferToSavings = async () => {
    if (!selectedAccount || !transferAmount) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    const amount = parseFloat(transferAmount);
    if (amount <= 0 || amount > walletBalance) {
      Alert.alert('Error', 'Invalid amount or insufficient balance');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const result = await savingsService.transferToSavings(
        selectedAccount.id,
        amount,
        `Transfer to ${selectedAccount.account_name}`
      );

      if (result.success) {
        Alert.alert('Success', `${formatCurrency(amount)} transferred to ${selectedAccount.account_name}`);
        setTransferModalVisible(false);
        setTransferAmount('');
        setSelectedAccount(null);
        loadSavingsData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Transfer error:', error);
      Alert.alert('Error', 'Transfer failed');
    }
  };

  const handleWithdrawFromSavings = async () => {
    if (!selectedAccount || !transferAmount) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    const amount = parseFloat(transferAmount);
    if (amount <= 0 || amount > selectedAccount.balance) {
      Alert.alert('Error', 'Invalid amount or insufficient savings balance');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const result = await savingsService.withdrawFromSavings(
        selectedAccount.id,
        amount,
        `Withdrawal from ${selectedAccount.account_name}`
      );

      if (result.success) {
        Alert.alert('Success', `${formatCurrency(amount)} withdrawn from ${selectedAccount.account_name}`);
        setWithdrawModalVisible(false);
        setTransferAmount('');
        setSelectedAccount(null);
        loadSavingsData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Withdrawal error:', error);
      Alert.alert('Error', 'Withdrawal failed');
    }
  };

  const handleCreateAccount = async () => {
    if (!newAccountName.trim()) {
      Alert.alert('Error', 'Please enter an account name');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      const targetAmount = newAccountGoal ? parseFloat(newAccountGoal) : 0;
      const result = await savingsService.createSavingsAccount(
        newAccountName.trim(),
        'custom',
        targetAmount
      );

      if (result.success) {
        Alert.alert('Success', `${newAccountName} savings account created!`);
        setCreateAccountModalVisible(false);
        setNewAccountName('');
        setNewAccountGoal('');
        loadSavingsData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Create account error:', error);
      Alert.alert('Error', 'Failed to create account');
    }
  };

  const openTransferModal = (account) => {
    setSelectedAccount(account);
    setTransferModalVisible(true);
  };

  const openWithdrawModal = (account) => {
    setSelectedAccount(account);
    setWithdrawModalVisible(true);
  };

  const renderSavingsSummary = () => (
    <LinearGradient
      colors={Colors.gradients.prosperity}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.summaryCard}
    >
      <View style={styles.summaryHeader}>
        <Text style={styles.summaryTitle}>Total Savings</Text>
        <Ionicons name="trending-up" size={24} color={theme.colors.white} />
      </View>
      <Text style={styles.summaryAmount}>
        {formatCurrency(savingsSummary?.total_savings || 0)}
      </Text>
      <View style={styles.summaryStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{savingsSummary?.total_accounts || 0}</Text>
          <Text style={styles.statLabel}>Accounts</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{savingsSummary?.total_goals || 0}</Text>
          <Text style={styles.statLabel}>Goals</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{savingsSummary?.completed_goals || 0}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </View>
      </View>
    </LinearGradient>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setCreateAccountModalVisible(true)}
      >
        <Ionicons name="add-circle-outline" size={24} color={theme.colors.primary} />
        <Text style={styles.quickActionText}>New Goal</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => {
          if (savingsAccounts.length > 0) {
            openTransferModal(savingsAccounts[0]);
          } else {
            Alert.alert('No Accounts', 'Create a savings account first');
          }
        }}
      >
        <Ionicons name="arrow-up-circle-outline" size={24} color={theme.colors.success} />
        <Text style={styles.quickActionText}>Save Money</Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('AutomaticSavings')}
      >
        <Ionicons name="repeat-outline" size={24} color={Colors.secondary.savanna} />
        <Text style={styles.quickActionText}>Auto Save</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('BudgetInsights')}
      >
        <Ionicons name="analytics-outline" size={24} color={Colors.accent.amber} />
        <Text style={styles.quickActionText}>Insights</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSavingsAccount = (account) => {
    // Safety check for account object
    if (!account || typeof account !== 'object') {
      console.warn('⚠️ Invalid account object:', account);
      return null;
    }

    const progress = account.metadata?.target_amount > 0
      ? (account.balance / account.metadata.target_amount) * 100
      : 0;
    const isGoalComplete = progress >= 100;

    return (
      <View key={account.id} style={styles.accountCard}>
        <View style={styles.accountHeader}>
          <View style={styles.accountInfo}>
            <Text style={styles.accountName}>{account.account_name}</Text>
            <Text style={styles.accountNumber}>{account.account_number}</Text>
          </View>
          <View style={styles.accountActions}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => openTransferModal(account)}
            >
              <Ionicons name="add" size={16} color={theme.colors.success} />
            </TouchableOpacity>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => openWithdrawModal(account)}
            >
              <Ionicons name="remove" size={16} color={theme.colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.accountBalance}>
          {formatCurrency(account.balance)}
        </Text>

        {account.metadata?.target_amount > 0 && (
          <View style={styles.goalSection}>
            <View style={styles.goalHeader}>
              <Text style={styles.goalText}>
                Goal: {formatCurrency(account.metadata.target_amount)}
              </Text>
              <Text style={[
                styles.goalProgress,
                { color: isGoalComplete ? theme.colors.success : theme.colors.textSecondary }
              ]}>
                {progress.toFixed(1)}%
              </Text>
            </View>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill,
                  { 
                    width: `${Math.min(progress, 100)}%`,
                    backgroundColor: isGoalComplete ? theme.colors.success : theme.colors.primary
                  }
                ]} 
              />
            </View>
            {isGoalComplete && (
              <Text style={styles.goalCompleteText}>🎉 Goal Achieved!</Text>
            )}
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading savings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Savings</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSavingsSummary()}
        {renderQuickActions()}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Savings Accounts</Text>
          {savingsAccounts && savingsAccounts.length > 0 ? (
            savingsAccounts.map(renderSavingsAccount)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="wallet-outline" size={48} color={theme.colors.textSecondary} />
              <Text style={styles.emptyStateText}>No savings accounts yet</Text>
              <TouchableOpacity 
                style={styles.createFirstButton}
                onPress={() => setCreateAccountModalVisible(true)}
              >
                <Text style={styles.createFirstButtonText}>Create Your First Savings Goal</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Transfer Modal */}
      <Modal
        visible={transferModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setTransferModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Transfer to Savings</Text>
            <Text style={styles.modalSubtitle}>
              Available: {formatCurrency(walletBalance)}
            </Text>
            
            <TextInput
              style={styles.amountInput}
              placeholder="Enter amount"
              value={transferAmount}
              onChangeText={setTransferAmount}
              keyboardType="numeric"
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setTransferModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleTransferToSavings}
              >
                <Text style={styles.confirmButtonText}>Transfer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Withdraw Modal */}
      <Modal
        visible={withdrawModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setWithdrawModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Withdraw from Savings</Text>
            <Text style={styles.modalSubtitle}>
              Available: {formatCurrency(selectedAccount?.balance || 0)}
            </Text>
            
            <TextInput
              style={styles.amountInput}
              placeholder="Enter amount"
              value={transferAmount}
              onChangeText={setTransferAmount}
              keyboardType="numeric"
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setWithdrawModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleWithdrawFromSavings}
              >
                <Text style={styles.confirmButtonText}>Withdraw</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Create Account Modal */}
      <Modal
        visible={createAccountModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setCreateAccountModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create Savings Goal</Text>
            
            <TextInput
              style={styles.textInput}
              placeholder="Goal name (e.g., Emergency Fund)"
              value={newAccountName}
              onChangeText={setNewAccountName}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Target amount (optional)"
              value={newAccountGoal}
              onChangeText={setNewAccountGoal}
              keyboardType="numeric"
            />
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setCreateAccountModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleCreateAccount}
              >
                <Text style={styles.confirmButtonText}>Create</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  summaryCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    color: theme.colors.white,
    opacity: 0.9,
  },
  summaryAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.white,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.white,
    opacity: 0.8,
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    minWidth: 70,
    flex: 1,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: 4,
    textAlign: 'center',
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  accountCard: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  accountHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  accountNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  accountActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  accountBalance: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  goalSection: {
    marginTop: 8,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  goalText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  goalProgress: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  goalCompleteText: {
    fontSize: 14,
    color: theme.colors.success,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
    marginBottom: 20,
  },
  createFirstButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  createFirstButtonText: {
    color: theme.colors.white,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  amountInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: theme.colors.inputBackground,
  },
  cancelButtonText: {
    textAlign: 'center',
    color: theme.colors.text,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    marginLeft: 8,
    backgroundColor: theme.colors.primary,
  },
  confirmButtonText: {
    textAlign: 'center',
    color: theme.colors.white,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 20,
  },
});

export default SavingsScreen;
