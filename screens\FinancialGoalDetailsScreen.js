/**
 * Financial Goal Details Screen
 * Screen for viewing and managing individual financial goal details
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import financialPlanningService from '../services/financialPlanningService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const FinancialGoalDetailsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { goalId } = route.params;

  // State
  const [goal, setGoal] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadGoalDetails();
  }, []);

  const loadGoalDetails = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view goal details');
        navigation.goBack();
        return;
      }

      // For now, we'll get all goals and find the specific one
      // In a real implementation, you'd have a getGoalById method
      const result = await financialPlanningService.getUserFinancialGoals(userId);

      if (result.success) {
        const foundGoal = result.goals.find(g => g.id === goalId);
        if (foundGoal) {
          setGoal(foundGoal);
        } else {
          Alert.alert('Error', 'Goal not found');
          navigation.goBack();
        }
      } else {
        Alert.alert('Error', result.error || 'Failed to load goal details');
      }

    } catch (error) {
      console.error('❌ Error loading goal details:', error);
      Alert.alert('Error', 'Failed to load goal details');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadGoalDetails(true);
  };

  const getGoalIcon = (goalType) => {
    const icons = {
      savings: 'wallet',
      emergency_fund: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      home_purchase: 'home',
      retirement: 'time',
      debt_payoff: 'card',
      investment: 'trending-up'
    };
    return icons[goalType] || 'flag';
  };

  const getGoalColor = (goalType) => {
    const colors = {
      savings: '#4ECDC4',
      emergency_fund: '#96CEB4',
      vacation: '#FECA57',
      education: '#A8E6CF',
      home_purchase: '#FFB6C1',
      retirement: '#6C5CE7',
      debt_payoff: '#FF6B35',
      investment: '#45B7D1'
    };
    return colors[goalType] || '#4ECDC4';
  };

  const getPriorityLabel = (level) => {
    const labels = {
      1: 'Low Priority',
      2: 'Medium-Low',
      3: 'Medium',
      4: 'Medium-High',
      5: 'High Priority'
    };
    return labels[level] || 'Medium';
  };

  const calculateTimeRemaining = () => {
    if (!goal?.targetDate) return null;
    
    const now = new Date();
    const target = new Date(goal.targetDate);
    const diffTime = target - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return '1 day remaining';
    if (diffDays < 30) return `${diffDays} days remaining`;
    
    const diffMonths = Math.ceil(diffDays / 30);
    if (diffMonths === 1) return '1 month remaining';
    if (diffMonths < 12) return `${diffMonths} months remaining`;
    
    const diffYears = Math.floor(diffMonths / 12);
    const remainingMonths = diffMonths % 12;
    
    if (diffYears === 1 && remainingMonths === 0) return '1 year remaining';
    if (remainingMonths === 0) return `${diffYears} years remaining`;
    return `${diffYears}y ${remainingMonths}m remaining`;
  };

  const renderGoalHeader = () => (
    <View style={styles.headerCard}>
      <View style={styles.goalHeaderContent}>
        <View style={[styles.goalIcon, { backgroundColor: getGoalColor(goal.goalType) }]}>
          <Ionicons name={getGoalIcon(goal.goalType)} size={32} color={theme.colors.white} />
        </View>
        <View style={styles.goalHeaderInfo}>
          <Text style={styles.goalName}>{goal.goalName}</Text>
          <Text style={styles.goalType}>
            {goal.goalType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Goal
          </Text>
          {goal.description && (
            <Text style={styles.goalDescription}>{goal.description}</Text>
          )}
        </View>
      </View>
    </View>
  );

  const renderProgressSection = () => (
    <View style={styles.progressCard}>
      <Text style={styles.cardTitle}>Progress</Text>
      
      <View style={styles.progressHeader}>
        <Text style={styles.progressPercentage}>{goal.progressPercentage.toFixed(1)}%</Text>
        <Text style={styles.timeRemaining}>{calculateTimeRemaining()}</Text>
      </View>
      
      <View style={styles.progressBar}>
        <View 
          style={[
            styles.progressFill, 
            { 
              width: `${Math.min(goal.progressPercentage, 100)}%`,
              backgroundColor: getGoalColor(goal.goalType)
            }
          ]} 
        />
      </View>
      
      <View style={styles.amountInfo}>
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Current Amount</Text>
          <Text style={styles.currentAmount}>
            {formatCurrency(goal.currentAmount, 'UGX')}
          </Text>
        </View>
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Target Amount</Text>
          <Text style={styles.targetAmount}>
            {formatCurrency(goal.targetAmount, 'UGX')}
          </Text>
        </View>
        <View style={styles.amountRow}>
          <Text style={styles.amountLabel}>Remaining</Text>
          <Text style={styles.remainingAmount}>
            {formatCurrency(goal.targetAmount - goal.currentAmount, 'UGX')}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderDetailsSection = () => (
    <View style={styles.detailsCard}>
      <Text style={styles.cardTitle}>Goal Details</Text>
      
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>Target Date</Text>
        <Text style={styles.detailValue}>
          {goal.targetDate ? formatDate(goal.targetDate) : 'Not set'}
        </Text>
      </View>
      
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>Monthly Contribution</Text>
        <Text style={styles.detailValue}>
          {goal.monthlyContribution > 0 
            ? formatCurrency(goal.monthlyContribution, 'UGX')
            : 'Not set'
          }
        </Text>
      </View>
      
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>Recommended Monthly</Text>
        <Text style={[styles.detailValue, { color: theme.colors.primary }]}>
          {formatCurrency(goal.recommendedMonthly, 'UGX')}
        </Text>
      </View>
      
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>Priority Level</Text>
        <Text style={styles.detailValue}>
          {getPriorityLabel(goal.priorityLevel)}
        </Text>
      </View>
      
      <View style={styles.detailRow}>
        <Text style={styles.detailLabel}>Created</Text>
        <Text style={styles.detailValue}>
          {formatDate(goal.createdAt)}
        </Text>
      </View>
    </View>
  );

  const renderActionButtons = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.cardTitle}>Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Add contribution feature will be available soon!')}
        >
          <Ionicons name="add-circle" size={24} color={theme.colors.success} />
          <Text style={styles.actionButtonText}>Add Contribution</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Edit goal feature will be available soon!')}
        >
          <Ionicons name="create" size={24} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Edit Goal</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Goal history feature will be available soon!')}
        >
          <Ionicons name="time" size={24} color={theme.colors.info} />
          <Text style={styles.actionButtonText}>View History</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => Alert.alert('Coming Soon', 'Share goal feature will be available soon!')}
        >
          <Ionicons name="share" size={24} color={theme.colors.warning} />
          <Text style={styles.actionButtonText}>Share Goal</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading goal details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!goal) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={64} color={theme.colors.error} />
          <Text style={styles.errorText}>Goal not found</Text>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Goal Details</Text>
        <TouchableOpacity onPress={() => Alert.alert('Coming Soon', 'Goal settings will be available soon!')}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderGoalHeader()}
        {renderProgressSection()}
        {renderDetailsSection()}
        {renderActionButtons()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 18,
    color: theme.colors.error,
    marginTop: 16,
    marginBottom: 24,
  },
  backButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  backButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  headerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  goalHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  goalIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  goalHeaderInfo: {
    flex: 1,
  },
  goalName: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  goalType: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  goalDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  progressCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressPercentage: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.primary,
  },
  timeRemaining: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'right',
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 20,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  amountInfo: {
    gap: 12,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  amountLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  currentAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.success,
  },
  targetAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  remainingAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.warning,
  },
  detailsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  actionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.background,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default FinancialGoalDetailsScreen;
