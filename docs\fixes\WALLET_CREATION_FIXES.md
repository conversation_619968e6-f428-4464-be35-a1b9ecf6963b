# JiraniPay Wallet Creation Fixes

## Overview
This document outlines the comprehensive fixes implemented to resolve wallet creation issues in JiraniPay, specifically addressing the errors shown in the console logs:

1. **Error creating production wallet**: Row-level security policy violation for table `payment_accounts`
2. **No phone number found for user**
3. **Failed to create new wallet**
4. **Account: Not Available** display issue

## Root Causes Identified

### 1. RLS Policy Issues
- The `payment_accounts` table had restrictive RLS policies that were causing violations
- Policies were not properly configured for wallet creation scenarios
- Missing proper permissions for authenticated users

### 2. Phone Number Storage Problems
- Phone numbers weren't being stored in user metadata during registration
- The wallet service couldn't retrieve phone numbers for account number generation
- Multiple registration flows had inconsistent phone number handling

### 3. Error Handling Gaps
- Wallet creation failures weren't properly handled in the UI
- No retry mechanisms for failed wallet creation
- Poor user feedback for wallet creation issues

## Fixes Implemented

### 1. Database RLS Policy Fix (`database/fix_wallet_creation_rls.sql`)

**What it does:**
- Drops existing conflicting RLS policies on `payment_accounts` table
- Creates new, properly configured policies for SELECT, INSERT, UPDATE, DELETE operations
- Adds a secure database function `create_user_wallet_safe()` for safe wallet creation
- Grants proper permissions to authenticated users

**Key improvements:**
- Uses `SECURITY DEFINER` function to bypass RLS during wallet creation
- Proper error handling and JSON response format
- Automatic account number generation with phone number fallback
- Prevents duplicate wallet creation

### 2. Authentication Service Updates (`services/authService.js`)

**Changes made:**
- Added phone number storage in user metadata during registration
- Updated both password and OTP registration flows
- Included additional user data in Supabase Auth signup options

**Code changes:**
```javascript
// Before
const { data: authData, error: authError } = await supabase.auth.signUp({
  phone: formattedPhone,
  password: password,
});

// After
const { data: authData, error: authError } = await supabase.auth.signUp({
  phone: formattedPhone,
  password: password,
  options: {
    data: {
      phone: formattedPhone,
      full_name: profileData.fullName,
      country_code: countryCode,
      preferred_language: profileData.preferredLanguage || 'en'
    }
  }
});
```

### 3. Wallet Service Improvements (`services/walletService.js`)

**Enhanced wallet creation:**
- Added multiple phone number retrieval strategies
- Integrated with the new database function
- Improved error handling and fallback mechanisms
- Better logging for debugging

**Key features:**
- Tries to get phone from auth metadata first
- Falls back to user profile table if needed
- Uses secure database function for wallet creation
- Maintains development mode fallbacks

### 4. Wallet Screen UI Updates (`screens/WalletScreen.js`)

**Improved user experience:**
- Automatic wallet creation when wallet not found
- Better error messages and status indicators
- Proper handling of wallet creation failures
- Informative account number display

**Status indicators:**
- `Creation Failed` - When wallet creation fails
- `Login Required` - When user is not authenticated
- `Not Available` - When all attempts fail (fallback)

## Database Function Details

### `create_user_wallet_safe(p_user_id, p_phone_number)`

**Purpose:** Safely create user wallets with proper error handling and RLS compliance.

**Features:**
- Checks for existing wallets before creating new ones
- Generates account numbers from phone numbers or creates JP fallback numbers
- Returns structured JSON responses with success/error status
- Handles all database errors gracefully

**Usage:**
```sql
SELECT * FROM create_user_wallet_safe(
  'user-uuid-here',
  '+************'
);
```

## Testing

### Test Script (`test_wallet_creation.js`)
A comprehensive test suite that validates:
- Phone number formatting and storage
- Account number generation logic
- Database function operation
- Wallet service integration
- End-to-end wallet creation flow

### Running Tests
```javascript
import WalletCreationTester from './test_wallet_creation.js';

const tester = new WalletCreationTester();
const results = await tester.runAllTests();
console.log('Test Results:', results);
```

## Deployment Steps

### 1. Database Updates
1. Run the SQL script in Supabase SQL Editor:
   ```sql
   -- Execute the contents of database/fix_wallet_creation_rls.sql
   ```

2. Verify policies are created:
   ```sql
   SELECT * FROM pg_policies WHERE tablename = 'payment_accounts';
   ```

### 2. Application Updates
1. The code changes are already implemented in the service files
2. Test the wallet creation flow with a new user registration
3. Verify existing users can access their wallets

### 3. Verification
1. Register a new user and check wallet creation
2. Verify account numbers are generated from phone numbers
3. Test wallet retrieval and display
4. Run the test script to validate all components

## Expected Outcomes

After implementing these fixes:

1. **✅ Wallet Creation Success**: New users will have wallets created automatically
2. **✅ Phone-based Account Numbers**: Account numbers will be based on user phone numbers (e.g., `**********`)
3. **✅ No RLS Violations**: Database operations will comply with security policies
4. **✅ Better Error Handling**: Users will see informative messages instead of "Not Available"
5. **✅ Consistent Experience**: Both development and production modes will work reliably

## Monitoring

### Key Metrics to Watch
- Wallet creation success rate
- Account number format consistency
- RLS policy violation errors (should be zero)
- User registration completion rates

### Logging
Enhanced logging has been added throughout the wallet creation flow:
- Phone number retrieval attempts
- Database function calls and responses
- Wallet creation success/failure events
- Account number generation details

## Troubleshooting

### Common Issues
1. **Still seeing RLS violations**: Ensure the SQL script was run completely
2. **Phone numbers not found**: Check user registration flow includes metadata
3. **Account numbers still showing "Not Available"**: Verify database function is working

### Debug Steps
1. Check Supabase logs for RLS policy errors
2. Verify user metadata contains phone numbers
3. Test database function directly in SQL editor
4. Run the test script to isolate issues

## Future Improvements

1. **Automatic Wallet Creation Trigger**: Database trigger to create wallets on user profile creation
2. **Phone Number Validation**: Enhanced validation during registration
3. **Account Number Customization**: Allow users to customize their account numbers
4. **Wallet Recovery**: Mechanisms to recover from wallet creation failures
