import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { isProductionMode } from '../config/environment';

const DevelopmentBanner = () => {
  const [showInstructions, setShowInstructions] = useState(false);

  // Hide banner in production mode
  if (isProductionMode()) {
    return null;
  }

  return (
    <>
      {/* Development Banner */}
      <TouchableOpacity
        style={styles.banner}
        onPress={() => setShowInstructions(true)}
      >
        <View style={styles.bannerContent}>
          <Ionicons name="information-circle" size={20} color={Colors.accent.coral} />
          <Text style={styles.bannerText}>Development Mode - Tap for Login Info</Text>
        </View>
      </TouchableOpacity>

      {/* Instructions Modal */}
      <Modal
        visible={showInstructions}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowInstructions(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>🚀 Development Login Instructions</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowInstructions(false)}
            >
              <Ionicons name="close" size={24} color={Colors.neutral.charcoal} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>📱 OTP Login Method</Text>
              <Text style={styles.instruction}>
                1. Enter any valid phone number for your selected country
              </Text>
              <Text style={styles.instruction}>
                2. Tap "Send OTP" - no actual SMS will be sent
              </Text>
              <Text style={styles.instruction}>
                3. Enter OTP: <Text style={styles.highlight}>123456</Text>
              </Text>
              <Text style={styles.instruction}>
                4. You'll be logged in automatically!
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🔑 Password Login Method</Text>
              <Text style={styles.instruction}>
                1. Enter any valid phone number for your selected country
              </Text>
              <Text style={styles.instruction}>
                2. Switch to "Password Login" tab
              </Text>
              <Text style={styles.instruction}>
                3. Enter any password with 6+ characters
              </Text>
              <Text style={styles.instruction}>
                4. Tap "Login" - you'll be authenticated!
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🌍 Supported Countries</Text>
              <Text style={styles.instruction}>• Uganda: +256 (e.g., 777123456)</Text>
              <Text style={styles.instruction}>• Kenya: +254 (e.g., 712345678)</Text>
              <Text style={styles.instruction}>• Tanzania: +255 (e.g., 712345678)</Text>
              <Text style={styles.instruction}>• Rwanda: +250 (e.g., 788123456)</Text>
              <Text style={styles.instruction}>• And 6 more East African countries</Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🎯 What You'll See After Login</Text>
              <Text style={styles.instruction}>✅ Modern wallet card with gradient</Text>
              <Text style={styles.instruction}>✅ 8 quick action buttons</Text>
              <Text style={styles.instruction}>✅ Financial insights dashboard</Text>
              <Text style={styles.instruction}>✅ Recent transactions section</Text>
              <Text style={styles.instruction}>✅ Bottom navigation with 5 tabs</Text>
              <Text style={styles.instruction}>✅ Haptic feedback on interactions</Text>
            </View>

            <View style={styles.warningSection}>
              <Ionicons name="warning" size={20} color={Colors.accent.coral} />
              <Text style={styles.warningText}>
                This is development mode. In production, real SMS OTP will be sent via Supabase + Twilio.
              </Text>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  banner: {
    backgroundColor: Colors.accent.gold + '20',
    borderColor: Colors.accent.gold,
    borderWidth: 1,
    borderRadius: 8,
    margin: 16,
    marginBottom: 8,
  },
  bannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  bannerText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: Colors.accent.coral,
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.neutral.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginBottom: 12,
  },
  instruction: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    marginBottom: 8,
    lineHeight: 22,
  },
  highlight: {
    fontWeight: 'bold',
    color: Colors.primary.main,
    backgroundColor: Colors.primary.light + '20',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  warningSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.accent.coral + '10',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent.coral,
  },
  warningText: {
    marginLeft: 12,
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
    flex: 1,
  },
});

export default DevelopmentBanner;
