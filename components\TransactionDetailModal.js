import React, { useRef } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  Share,
  Dimensions,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { captureRef } from 'react-native-view-shot';
import Colors from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * TransactionDetailModal Component
 * Displays detailed transaction information with print/share functionality
 */
const TransactionDetailModal = ({
  visible,
  onClose,
  transaction,
  onPrint,
  onPrintPDF,
  onShare
}) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const receiptRef = useRef();

  if (!transaction) return null;

  const formatCurrency = (amount, currency = 'UGX') => {
    const currencySymbols = {
      'UGX': 'UGX',
      'KES': 'KSh',
      'TZS': 'TSh',
      'RWF': 'RWF',
      'BIF': 'BIF',
      'ETB': 'ETB'
    };
    const symbol = currencySymbols[currency] || currency;
    return `${symbol} ${parseFloat(amount).toLocaleString()}`;
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-UG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-UG', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    };
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'deposit': return 'add-circle';
      case 'airtime': return 'phone-portrait';
      case 'bill_payment': return 'receipt';
      case 'transfer': return 'send';
      case 'withdrawal': return 'remove-circle';
      default: return 'swap-horizontal';
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'deposit': return Colors.status.success;
      case 'airtime': return Colors.primary.main;
      case 'bill_payment': return Colors.secondary.heritage;
      case 'transfer': return Colors.accent.coral;
      case 'withdrawal': return Colors.status.error;
      default: return Colors.neutral.warmGray;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed': return Colors.status.success;
      case 'pending': return Colors.status.warning;
      case 'failed': return Colors.status.error;
      default: return Colors.neutral.warmGray;
    }
  };

  const generateReceiptHTML = () => {
    const { date, time } = formatDateTime(transaction.created_at || transaction.timestamp);
    const transactionType = transaction.transaction_type || transaction.type;
    const isCredit = transactionType === 'deposit' || transaction.type === 'credit';

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>JiraniPay Transaction Receipt</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f8f9fa;
                color: #333;
                line-height: 1.6;
            }
            .receipt-container {
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #8B4513 0%, #D2691E 100%);
                color: white;
                padding: 24px;
                text-align: center;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 8px;
            }
            .tagline {
                font-size: 14px;
                opacity: 0.9;
            }
            .content {
                padding: 24px;
            }
            .status-badge {
                display: inline-block;
                padding: 6px 12px;
                border-radius: 16px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                margin-bottom: 20px;
                background-color: ${getStatusColor(transaction.status)}20;
                color: ${getStatusColor(transaction.status)};
            }
            .amount-section {
                text-align: center;
                margin: 24px 0;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
            .amount-label {
                font-size: 14px;
                color: #666;
                margin-bottom: 8px;
            }
            .amount-value {
                font-size: 32px;
                font-weight: bold;
                color: ${isCredit ? Colors.status.success : Colors.secondary.heritage};
            }
            .details-section {
                margin-top: 24px;
            }
            .detail-row {
                display: flex;
                justify-content: space-between;
                padding: 12px 0;
                border-bottom: 1px solid #eee;
            }
            .detail-label {
                color: #666;
                font-weight: 500;
            }
            .detail-value {
                font-weight: 600;
                text-align: right;
                max-width: 60%;
                word-break: break-word;
            }
            .footer {
                margin-top: 32px;
                padding-top: 20px;
                border-top: 2px solid #eee;
                text-align: center;
                color: #666;
                font-size: 12px;
            }
            .transaction-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 8px;
                text-align: center;
            }
            @media print {
                body { background-color: white; }
                .receipt-container { box-shadow: none; }
            }
        </style>
    </head>
    <body>
        <div class="receipt-container">
            <div class="header">
                <div class="logo">🏦 JiraniPay</div>
                <div class="tagline">Your Trusted Financial Partner</div>
            </div>

            <div class="content">
                <div class="transaction-title">${transaction.description}</div>
                <div class="status-badge">${(transaction.status || 'completed').charAt(0).toUpperCase() + (transaction.status || 'completed').slice(1)}</div>

                <div class="amount-section">
                    <div class="amount-label">Amount</div>
                    <div class="amount-value">
                        ${isCredit ? '+' : '-'}${formatCurrency(transaction.amount, transaction.currency)}
                    </div>
                </div>

                <div class="details-section">
                    <div class="detail-row">
                        <span class="detail-label">Reference Number</span>
                        <span class="detail-value">${transaction.reference_number || transaction.reference}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Date</span>
                        <span class="detail-value">${date}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Time</span>
                        <span class="detail-value">${time}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Type</span>
                        <span class="detail-value">${transactionType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Transaction'}</span>
                    </div>
                    ${transaction.provider ? `
                    <div class="detail-row">
                        <span class="detail-label">Provider</span>
                        <span class="detail-value">${transaction.provider}</span>
                    </div>
                    ` : ''}
                    ${transaction.category ? `
                    <div class="detail-row">
                        <span class="detail-label">Category</span>
                        <span class="detail-value">${transaction.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                    </div>
                    ` : ''}
                    ${transaction.account_number ? `
                    <div class="detail-row">
                        <span class="detail-label">Account Number</span>
                        <span class="detail-value">${transaction.account_number}</span>
                    </div>
                    ` : ''}
                </div>

                <div class="footer">
                    <p>Thank you for using JiraniPay!</p>
                    <p>Generated on ${new Date().toLocaleString()}</p>
                    <p>For support, contact <NAME_EMAIL></p>
                </div>
            </div>
        </div>
    </body>
    </html>
    `;
  };

  const handlePrint = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      if (onPrint) {
        await onPrint(transaction);
        return;
      }

      // Generate and print receipt
      const htmlContent = generateReceiptHTML();

      if (Platform.OS === 'ios') {
        // iOS printing
        await Print.printAsync({
          html: htmlContent,
          printerUrl: undefined, // Let user select printer
        });
        Alert.alert('Success', 'Receipt sent to printer successfully!');
      } else {
        // Android printing
        await Print.printAsync({
          html: htmlContent,
        });
        Alert.alert('Success', 'Receipt printed successfully!');
      }
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert('Print Error', 'Failed to print receipt. Please check your printer connection and try again.');
    }
  };

  const handlePrintPDF = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      if (onPrintPDF) {
        await onPrintPDF(transaction);
        return;
      }

      // Generate PDF receipt
      const htmlContent = generateReceiptHTML();
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      // Share the PDF file
      if (await Sharing.isAvailableAsync()) {
        const fileName = `JiraniPay_Receipt_${transaction.reference_number || transaction.reference}_${Date.now()}.pdf`;
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Save Transaction Receipt',
          UTI: 'com.adobe.pdf',
        });
        Alert.alert('Success', 'PDF receipt generated and ready to save or share!');
      } else {
        Alert.alert('PDF Generated', `Receipt saved to: ${uri}`);
      }
    } catch (error) {
      console.error('PDF export error:', error);
      Alert.alert('PDF Export Error', 'Failed to generate PDF receipt. Please try again.');
    }
  };

  const handleShareAsImage = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      if (!receiptRef.current) {
        Alert.alert('Error', 'Receipt view not ready. Please try again.');
        return;
      }

      // Capture the receipt as image
      const uri = await captureRef(receiptRef.current, {
        format: 'png',
        quality: 1.0,
        result: 'tmpfile',
      });

      // Share the image
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'image/png',
          dialogTitle: 'Share Transaction Receipt',
        });
      } else {
        await Share.share({
          url: uri,
          title: 'Transaction Receipt'
        });
      }
    } catch (error) {
      console.error('Share as image error:', error);
      Alert.alert('Share Error', 'Failed to share receipt as image. Please try again.');
    }
  };

  const handleShare = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const { date, time } = formatDateTime(transaction.created_at || transaction.timestamp);
      const shareContent = `
🧾 JiraniPay Transaction Receipt

💰 Amount: ${formatCurrency(transaction.amount, transaction.currency)}
📝 Description: ${transaction.description}
📅 Date: ${date}
⏰ Time: ${time}
🔗 Reference: ${transaction.reference_number || transaction.reference}
✅ Status: ${(transaction.status || 'completed').charAt(0).toUpperCase() + (transaction.status || 'completed').slice(1)}
${transaction.provider ? `🏢 Provider: ${transaction.provider}` : ''}

Powered by JiraniPay 🚀
      `.trim();

      if (onShare) {
        await onShare(shareContent, transaction);
      } else {
        await Share.share({
          message: shareContent,
          title: 'Transaction Receipt'
        });
      }
    } catch (error) {
      console.error('Share error:', error);
      Alert.alert('Error', 'Failed to share receipt. Please try again.');
    }
  };

  const handleClose = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClose();
  };

  const { date, time } = formatDateTime(transaction.created_at || transaction.timestamp);
  const transactionType = transaction.transaction_type || transaction.type;
  const isCredit = transactionType === 'deposit' || transaction.type === 'credit';

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Transaction Details</Text>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Receipt Content for Image Capture */}
          <View ref={receiptRef} style={styles.receiptContent}>
            {/* Transaction Icon & Status */}
            <View style={styles.iconSection}>
            <View style={[
              styles.iconContainer,
              { backgroundColor: getTransactionColor(transactionType) + '20' }
            ]}>
              <Ionicons
                name={getTransactionIcon(transactionType)}
                size={32}
                color={getTransactionColor(transactionType)}
              />
            </View>
            <Text style={styles.transactionTitle}>{transaction.description}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(transaction.status) + '20' }]}>
              <Text style={[styles.statusText, { color: getStatusColor(transaction.status) }]}>
                {(transaction.status || 'completed').charAt(0).toUpperCase() + (transaction.status || 'completed').slice(1)}
              </Text>
            </View>
          </View>

          {/* Amount */}
          <View style={styles.amountSection}>
            <Text style={styles.amountLabel}>Amount</Text>
            <Text style={[
              styles.amountValue,
              { color: isCredit ? Colors.status.success : Colors.secondary.heritage }
            ]}>
              {isCredit ? '+' : '-'}{formatCurrency(transaction.amount, transaction.currency)}
            </Text>
          </View>

          {/* Transaction Details */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>Transaction Information</Text>
            
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Reference Number</Text>
              <Text style={styles.detailValue}>{transaction.reference_number || transaction.reference}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Date</Text>
              <Text style={styles.detailValue}>{date}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Time</Text>
              <Text style={styles.detailValue}>{time}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Type</Text>
              <Text style={styles.detailValue}>
                {transactionType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Transaction'}
              </Text>
            </View>

            {transaction.provider && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Provider</Text>
                <Text style={styles.detailValue}>{transaction.provider}</Text>
              </View>
            )}

            {transaction.category && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Category</Text>
                <Text style={styles.detailValue}>
                  {transaction.category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Text>
              </View>
            )}

            {transaction.account_number && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Account Number</Text>
                <Text style={styles.detailValue}>{transaction.account_number}</Text>
              </View>
            )}
          </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionsSection}>
            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>Share Text</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShareAsImage}>
              <Ionicons name="image-outline" size={20} color={Colors.primary.main} />
              <Text style={styles.actionButtonText}>Share as Image</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handlePrintPDF}>
              <Ionicons name="document-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>Save as PDF</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handlePrint}>
              <Ionicons name="print-outline" size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>Print</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  receiptContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginVertical: 8,
    padding: 16,
  },
  iconSection: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  transactionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  amountSection: {
    alignItems: 'center',
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  amountLabel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  amountValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  detailsSection: {
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  detailLabel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'right',
  },
  actionsSection: {
    paddingVertical: 24,
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.primary + '30',
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
});

export default TransactionDetailModal;
