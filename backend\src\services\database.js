/**
 * Database Service
 * Manages database connections and operations using Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const { Pool } = require('pg');
const config = require('../config/config');
const logger = require('../utils/logger');

class DatabaseService {
  constructor() {
    this.supabase = null;
    this.pgPool = null;
    this.isInitialized = false;
  }

  /**
   * Initialize database connections
   */
  async initialize() {
    try {
      // Initialize Supabase client
      this.supabase = createClient(
        config.database.supabase.url,
        config.database.supabase.serviceRoleKey,
        {
          auth: {
            autoRefreshToken: true,
            persistSession: false
          },
          db: {
            schema: 'public'
          }
        }
      );

      // Test Supabase connection
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('count')
        .limit(1);

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is fine
        throw new Error(`Supabase connection failed: ${error.message}`);
      }

      // Initialize PostgreSQL pool for direct queries if needed
      if (config.database.postgres.url) {
        this.pgPool = new Pool({
          connectionString: config.database.postgres.url,
          ssl: config.database.postgres.ssl,
          ...config.database.postgres.pool
        });

        // Test PostgreSQL connection
        const client = await this.pgPool.connect();
        await client.query('SELECT NOW()');
        client.release();
      }

      this.isInitialized = true;
      logger.info('Database service initialized successfully');

    } catch (error) {
      logger.error('Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Get Supabase client
   */
  getSupabase() {
    if (!this.isInitialized) {
      throw new Error('Database service not initialized');
    }
    return this.supabase;
  }

  /**
   * Get PostgreSQL pool
   */
  getPgPool() {
    if (!this.isInitialized) {
      throw new Error('Database service not initialized');
    }
    return this.pgPool;
  }

  /**
   * Execute a raw SQL query
   */
  async query(sql, params = []) {
    if (!this.pgPool) {
      throw new Error('PostgreSQL pool not available');
    }

    const start = Date.now();
    try {
      const result = await this.pgPool.query(sql, params);
      const duration = Date.now() - start;
      
      logger.database('SQL Query executed', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        duration,
        rowCount: result.rowCount
      });

      return result;
    } catch (error) {
      const duration = Date.now() - start;
      logger.error('SQL Query failed', {
        sql: sql.substring(0, 100) + (sql.length > 100 ? '...' : ''),
        duration,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Execute a transaction
   */
  async transaction(callback) {
    if (!this.pgPool) {
      throw new Error('PostgreSQL pool not available');
    }

    const client = await this.pgPool.connect();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Health check for database connections
   */
  async healthCheck() {
    const health = {
      supabase: false,
      postgres: false,
      timestamp: new Date().toISOString()
    };

    try {
      // Check Supabase
      const { error } = await this.supabase
        .from('user_profiles')
        .select('count')
        .limit(1);
      
      health.supabase = !error || error.code === 'PGRST116';
    } catch (error) {
      logger.error('Supabase health check failed:', error);
    }

    try {
      // Check PostgreSQL
      if (this.pgPool) {
        const client = await this.pgPool.connect();
        await client.query('SELECT 1');
        client.release();
        health.postgres = true;
      }
    } catch (error) {
      logger.error('PostgreSQL health check failed:', error);
    }

    return health;
  }

  /**
   * Get database statistics
   */
  async getStats() {
    try {
      const stats = {};

      // Get table row counts
      const tables = [
        'user_profiles',
        'wallets',
        'transactions',
        'bills',
        'mobile_money_accounts',
        'bank_accounts'
      ];

      for (const table of tables) {
        try {
          const { count, error } = await this.supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          if (!error) {
            stats[table] = count;
          }
        } catch (error) {
          logger.warn(`Failed to get count for table ${table}:`, error);
          stats[table] = 'unknown';
        }
      }

      // Get connection pool stats if available
      if (this.pgPool) {
        stats.connectionPool = {
          totalCount: this.pgPool.totalCount,
          idleCount: this.pgPool.idleCount,
          waitingCount: this.pgPool.waitingCount
        };
      }

      return stats;
    } catch (error) {
      logger.error('Failed to get database stats:', error);
      return { error: error.message };
    }
  }

  /**
   * Close database connections
   */
  async close() {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        logger.info('PostgreSQL pool closed');
      }

      // Supabase client doesn't need explicit closing
      this.isInitialized = false;
      logger.info('Database connections closed');
    } catch (error) {
      logger.error('Error closing database connections:', error);
    }
  }

  /**
   * Utility methods for common operations
   */

  /**
   * Create a new user profile
   */
  async createUserProfile(userData) {
    const { data, error } = await this.supabase
      .from('user_profiles')
      .insert(userData)
      .select()
      .single();

    if (error) {
      logger.error('Failed to create user profile:', error);
      throw error;
    }

    logger.audit('User profile created', { userId: data.user_id });
    return data;
  }

  /**
   * Get user profile by user ID
   */
  async getUserProfile(userId) {
    const { data, error } = await this.supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Failed to get user profile:', error);
      throw error;
    }

    return data;
  }

  /**
   * Create a new wallet
   */
  async createWallet(walletData) {
    const { data, error } = await this.supabase
      .from('wallets')
      .insert(walletData)
      .select()
      .single();

    if (error) {
      logger.error('Failed to create wallet:', error);
      throw error;
    }

    logger.audit('Wallet created', { 
      userId: data.user_id, 
      walletId: data.id,
      currency: data.currency 
    });
    return data;
  }

  /**
   * Get wallet by user ID
   */
  async getWallet(userId) {
    const { data, error } = await this.supabase
      .from('wallets')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      logger.error('Failed to get wallet:', error);
      throw error;
    }

    return data;
  }

  /**
   * Update wallet balance
   */
  async updateWalletBalance(walletId, newBalance) {
    const { data, error } = await this.supabase
      .from('wallets')
      .update({ 
        balance: newBalance,
        updated_at: new Date().toISOString()
      })
      .eq('id', walletId)
      .select()
      .single();

    if (error) {
      logger.error('Failed to update wallet balance:', error);
      throw error;
    }

    logger.financial('Wallet balance updated', newBalance, data.currency, data.user_id, {
      walletId,
      previousBalance: 'unknown' // Would need to track this separately
    });

    return data;
  }
}

// Create singleton instance
const databaseService = new DatabaseService();

module.exports = databaseService;
