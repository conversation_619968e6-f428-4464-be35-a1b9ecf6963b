# Integration Tests

This directory contains integration tests for JiraniPay production fixes and features.

## Test Files Moved from Root

The following test files have been moved from the root directory to maintain proper organization:

- `test_email_verification_fix.js` → `email-verification.test.js`
- `test_password_authentication_fix.js` → `password-authentication.test.js`
- `test_profile_update_fix.js` → `profile-update.test.js`
- `test_registration_production_fix.js` → `registration-production.test.js`
- `test_send_money_fixes.js` → `send-money.test.js`
- `test_topup_network_validation.js` → `topup-network-validation.test.js`
- `test_wallet_creation.js` → `wallet-creation.test.js`
- `test_wallet_creation_fix.js` → `wallet-creation-fix.test.js`

## Running Tests

```bash
# Run all integration tests
npm run test:integration

# Run specific test
npm test JiraniPay/tests/integration/registration-production.test.js
```
