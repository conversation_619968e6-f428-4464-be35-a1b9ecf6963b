/**
 * Category Management Service
 * Handles CRUD operations for transaction categories
 * Supports both predefined and custom user-defined categories
 */

import supabase from './supabaseClient';
import transactionCategorizationService from './transactionCategorizationService';

class CategoryManagementService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 15 * 60 * 1000; // 15 minutes
  }

  /**
   * Get all categories for a user (predefined + custom)
   */
  async getUserCategories(userId) {
    try {
      const cacheKey = `user_categories_${userId}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      console.log('📂 Getting categories for user:', userId);

      // Get predefined categories
      const predefinedCategories = transactionCategorizationService.getPredefinedCategories();

      // Get custom categories from database
      const { data: customCategories, error } = await supabase
        .from('custom_categories')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('name');

      if (error) {
        console.error('❌ Error fetching custom categories:', error);
      }

      const allCategories = [
        ...predefinedCategories.map(cat => ({
          ...cat,
          type: 'predefined',
          isCustom: false,
          canEdit: false,
          canDelete: false
        })),
        ...(customCategories || []).map(cat => ({
          id: cat.id,
          name: cat.name,
          icon: cat.icon || 'folder',
          color: cat.color || '#B2BEC3',
          keywords: cat.keywords || [],
          patterns: [],
          type: 'custom',
          isCustom: true,
          canEdit: true,
          canDelete: true,
          description: cat.description,
          parentCategory: cat.parent_category,
          createdAt: cat.created_at
        }))
      ];

      const result = { success: true, data: allCategories };
      this.setCache(cacheKey, result);

      console.log('✅ Found', allCategories.length, 'categories');
      return result;
    } catch (error) {
      console.error('❌ Error getting user categories:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new custom category
   */
  async createCustomCategory(userId, categoryData) {
    try {
      console.log('📂 Creating custom category:', categoryData.name);

      // Validate required fields
      if (!categoryData.name || !categoryData.name.trim()) {
        throw new Error('Category name is required');
      }

      // Check if category name already exists
      const existingCheck = await this.checkCategoryNameExists(userId, categoryData.name);
      if (existingCheck.exists) {
        throw new Error('Category name already exists');
      }

      // Create category in database
      const { data: newCategory, error } = await supabase
        .from('custom_categories')
        .insert({
          user_id: userId,
          name: categoryData.name.trim(),
          description: categoryData.description || '',
          icon: categoryData.icon || 'folder',
          color: categoryData.color || '#B2BEC3',
          keywords: categoryData.keywords || [],
          parent_category: categoryData.parentCategory || null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Clear cache
      this.clearUserCache(userId);

      console.log('✅ Custom category created:', newCategory.id);
      return { 
        success: true, 
        data: {
          id: newCategory.id,
          name: newCategory.name,
          icon: newCategory.icon,
          color: newCategory.color,
          keywords: newCategory.keywords,
          type: 'custom',
          isCustom: true,
          canEdit: true,
          canDelete: true,
          description: newCategory.description,
          parentCategory: newCategory.parent_category,
          createdAt: newCategory.created_at
        }
      };
    } catch (error) {
      console.error('❌ Error creating custom category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update a custom category
   */
  async updateCustomCategory(userId, categoryId, updateData) {
    try {
      console.log('📂 Updating custom category:', categoryId);

      // Validate that this is a custom category owned by the user
      const { data: existingCategory, error: fetchError } = await supabase
        .from('custom_categories')
        .select('*')
        .eq('id', categoryId)
        .eq('user_id', userId)
        .single();

      if (fetchError || !existingCategory) {
        throw new Error('Category not found or not owned by user');
      }

      // Check if new name already exists (if name is being changed)
      if (updateData.name && updateData.name !== existingCategory.name) {
        const existingCheck = await this.checkCategoryNameExists(userId, updateData.name);
        if (existingCheck.exists) {
          throw new Error('Category name already exists');
        }
      }

      // Update category
      const { data: updatedCategory, error } = await supabase
        .from('custom_categories')
        .update({
          name: updateData.name || existingCategory.name,
          description: updateData.description !== undefined ? updateData.description : existingCategory.description,
          icon: updateData.icon || existingCategory.icon,
          color: updateData.color || existingCategory.color,
          keywords: updateData.keywords !== undefined ? updateData.keywords : existingCategory.keywords,
          parent_category: updateData.parentCategory !== undefined ? updateData.parentCategory : existingCategory.parent_category,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      // Clear cache
      this.clearUserCache(userId);

      console.log('✅ Custom category updated:', categoryId);
      return { 
        success: true, 
        data: {
          id: updatedCategory.id,
          name: updatedCategory.name,
          icon: updatedCategory.icon,
          color: updatedCategory.color,
          keywords: updatedCategory.keywords,
          type: 'custom',
          isCustom: true,
          canEdit: true,
          canDelete: true,
          description: updatedCategory.description,
          parentCategory: updatedCategory.parent_category,
          updatedAt: updatedCategory.updated_at
        }
      };
    } catch (error) {
      console.error('❌ Error updating custom category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a custom category
   */
  async deleteCustomCategory(userId, categoryId) {
    try {
      console.log('📂 Deleting custom category:', categoryId);

      // Check if category is being used by any transactions
      const { data: transactionCount, error: countError } = await supabase
        .from('transactions')
        .select('id', { count: 'exact' })
        .eq('user_id', userId)
        .eq('category', categoryId);

      if (countError) throw countError;

      if (transactionCount && transactionCount.length > 0) {
        return { 
          success: false, 
          error: 'Cannot delete category that is being used by transactions',
          canReassign: true,
          transactionCount: transactionCount.length
        };
      }

      // Soft delete the category
      const { error } = await supabase
        .from('custom_categories')
        .update({
          is_active: false,
          deleted_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .eq('user_id', userId);

      if (error) throw error;

      // Clear cache
      this.clearUserCache(userId);

      console.log('✅ Custom category deleted:', categoryId);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting custom category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Reassign transactions from one category to another
   */
  async reassignTransactions(userId, fromCategoryId, toCategoryId) {
    try {
      console.log('📂 Reassigning transactions from', fromCategoryId, 'to', toCategoryId);

      // Validate target category exists
      const targetCategory = await this.getCategoryById(userId, toCategoryId);
      if (!targetCategory.success) {
        throw new Error('Target category not found');
      }

      // Update all transactions
      const { data: updatedTransactions, error } = await supabase
        .from('transactions')
        .update({
          category: toCategoryId,
          category_confidence: 1.0, // Manual reassignment has high confidence
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('category', fromCategoryId)
        .select('id');

      if (error) throw error;

      console.log('✅ Reassigned', updatedTransactions?.length || 0, 'transactions');
      return { 
        success: true, 
        data: { 
          reassignedCount: updatedTransactions?.length || 0 
        }
      };
    } catch (error) {
      console.error('❌ Error reassigning transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(userId, categoryId) {
    try {
      // Check predefined categories first
      const predefinedCategory = transactionCategorizationService.getCategoryById(categoryId);
      if (predefinedCategory) {
        return {
          success: true,
          data: {
            ...predefinedCategory,
            type: 'predefined',
            isCustom: false,
            canEdit: false,
            canDelete: false
          }
        };
      }

      // Check custom categories
      const { data: customCategory, error } = await supabase
        .from('custom_categories')
        .select('*')
        .eq('id', categoryId)
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') { // Not found
          return { success: false, error: 'Category not found' };
        }
        throw error;
      }

      return {
        success: true,
        data: {
          id: customCategory.id,
          name: customCategory.name,
          icon: customCategory.icon,
          color: customCategory.color,
          keywords: customCategory.keywords,
          type: 'custom',
          isCustom: true,
          canEdit: true,
          canDelete: true,
          description: customCategory.description,
          parentCategory: customCategory.parent_category
        }
      };
    } catch (error) {
      console.error('❌ Error getting category by ID:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if category name already exists
   */
  async checkCategoryNameExists(userId, categoryName) {
    try {
      const normalizedName = categoryName.toLowerCase().trim();

      // Check predefined categories
      const predefinedCategories = transactionCategorizationService.getPredefinedCategories();
      const predefinedExists = predefinedCategories.some(cat => 
        cat.name.toLowerCase() === normalizedName
      );

      if (predefinedExists) {
        return { exists: true, type: 'predefined' };
      }

      // Check custom categories
      const { data: customCategories, error } = await supabase
        .from('custom_categories')
        .select('id, name')
        .eq('user_id', userId)
        .eq('is_active', true)
        .ilike('name', normalizedName);

      if (error) throw error;

      if (customCategories && customCategories.length > 0) {
        return { exists: true, type: 'custom' };
      }

      return { exists: false };
    } catch (error) {
      console.error('❌ Error checking category name:', error);
      return { exists: false, error: error.message };
    }
  }

  /**
   * Get category usage statistics
   */
  async getCategoryUsageStats(userId, categoryId) {
    try {
      const { data: stats, error } = await supabase
        .from('transactions')
        .select('amount, created_at')
        .eq('user_id', userId)
        .eq('category', categoryId);

      if (error) throw error;

      const transactions = stats || [];
      const totalTransactions = transactions.length;
      const totalAmount = transactions.reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      const averageAmount = totalTransactions > 0 ? totalAmount / totalTransactions : 0;

      // Calculate monthly usage
      const monthlyUsage = {};
      transactions.forEach(tx => {
        const month = new Date(tx.created_at).toISOString().slice(0, 7);
        monthlyUsage[month] = (monthlyUsage[month] || 0) + 1;
      });

      return {
        success: true,
        data: {
          totalTransactions,
          totalAmount,
          averageAmount,
          monthlyUsage,
          lastUsed: transactions.length > 0 ?
            Math.max(...transactions.map(tx => new Date(tx.created_at).getTime())) : null
        }
      };
    } catch (error) {
      console.error('❌ Error getting category usage stats:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get category suggestions based on transaction data
   */
  async getCategorySuggestions(userId, transactionData) {
    try {
      // Use the categorization service to get suggestions
      const result = await transactionCategorizationService.categorizeTransaction(
        transactionData,
        userId
      );

      if (!result.success) {
        throw new Error(result.error);
      }

      return {
        success: true,
        data: {
          primary: result.data.category,
          confidence: result.data.confidence,
          alternatives: result.data.suggestions
        }
      };
    } catch (error) {
      console.error('❌ Error getting category suggestions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Bulk update transaction categories
   */
  async bulkUpdateCategories(userId, updates) {
    try {
      console.log('📂 Bulk updating', updates.length, 'transaction categories');

      const results = [];
      const batchSize = 50; // Process in batches

      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);

        const batchPromises = batch.map(async (update) => {
          try {
            const { error } = await supabase
              .from('transactions')
              .update({
                category: update.categoryId,
                category_confidence: update.confidence || 1.0,
                updated_at: new Date().toISOString()
              })
              .eq('id', update.transactionId)
              .eq('user_id', userId);

            if (error) throw error;

            return { transactionId: update.transactionId, success: true };
          } catch (error) {
            return {
              transactionId: update.transactionId,
              success: false,
              error: error.message
            };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults.map(result =>
          result.status === 'fulfilled' ? result.value : result.reason
        ));
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      console.log('✅ Bulk update completed:', successCount, 'success,', failureCount, 'failures');
      return {
        success: true,
        data: {
          totalUpdates: updates.length,
          successCount,
          failureCount,
          results
        }
      };
    } catch (error) {
      console.error('❌ Error in bulk category update:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Export categories for backup/sharing
   */
  async exportCategories(userId) {
    try {
      const categoriesResult = await this.getUserCategories(userId);
      if (!categoriesResult.success) {
        throw new Error(categoriesResult.error);
      }

      const customCategories = categoriesResult.data.filter(cat => cat.isCustom);

      return {
        success: true,
        data: {
          exportDate: new Date().toISOString(),
          userId,
          categories: customCategories.map(cat => ({
            name: cat.name,
            description: cat.description,
            icon: cat.icon,
            color: cat.color,
            keywords: cat.keywords,
            parentCategory: cat.parentCategory
          }))
        }
      };
    } catch (error) {
      console.error('❌ Error exporting categories:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Import categories from backup
   */
  async importCategories(userId, importData) {
    try {
      console.log('📂 Importing', importData.categories?.length || 0, 'categories');

      if (!importData.categories || !Array.isArray(importData.categories)) {
        throw new Error('Invalid import data format');
      }

      const results = [];

      for (const categoryData of importData.categories) {
        try {
          const result = await this.createCustomCategory(userId, categoryData);
          results.push({
            name: categoryData.name,
            success: result.success,
            error: result.error
          });
        } catch (error) {
          results.push({
            name: categoryData.name,
            success: false,
            error: error.message
          });
        }
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      console.log('✅ Import completed:', successCount, 'success,', failureCount, 'failures');
      return {
        success: true,
        data: {
          totalCategories: importData.categories.length,
          successCount,
          failureCount,
          results
        }
      };
    } catch (error) {
      console.error('❌ Error importing categories:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.cache.clear();
  }
}

export default new CategoryManagementService();
