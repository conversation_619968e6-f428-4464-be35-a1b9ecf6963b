/**
 * Create Budget Screen
 * Screen for creating new budgets with AI suggestions
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  Switch,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import DateTimePicker from '@react-native-community/datetimepicker';

// Services
import budgetManagementService from '../services/budgetManagementService';
import predictiveAnalyticsService from '../services/predictiveAnalyticsService';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';

// Components
import SkeletonLoader from '../components/SkeletonLoader';
import BudgetCategoryCard from '../components/budget/BudgetCategoryCard';

// Constants
import { Colors } from '../constants/Colors';

const CreateBudgetScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { formatCurrencyValue } = useCurrencyContext();
  const { userId, forecasts } = route.params;

  const [budgetName, setBudgetName] = useState('');
  const [budgetDescription, setBudgetDescription] = useState('');
  const [totalAmount, setTotalAmount] = useState('');
  const [budgetType, setBudgetType] = useState('monthly');
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(null);
  const [useAISuggestions, setUseAISuggestions] = useState(true);
  const [targetSavingsRate, setTargetSavingsRate] = useState(20);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [aiSuggestions, setAISuggestions] = useState(null);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  /**
   * Load AI suggestions when component mounts
   */
  useEffect(() => {
    if (useAISuggestions) {
      loadAISuggestions();
    }
  }, [useAISuggestions, targetSavingsRate]);

  /**
   * Load AI budget suggestions
   */
  const loadAISuggestions = async () => {
    try {
      setLoading(true);
      const result = await predictiveAnalyticsService.generateBudgetSuggestions(
        userId,
        targetSavingsRate / 100
      );

      if (result.success) {
        setAISuggestions(result.data);
        
        // Auto-fill total amount if available
        if (result.data.totalBudget && !totalAmount) {
          setTotalAmount(result.data.totalBudget.toString());
        }
      } else {
        console.warn('⚠️ Error loading AI suggestions:', result.error);
      }
    } catch (error) {
      console.error('❌ Error loading AI suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle budget creation
   */
  const handleCreateBudget = async () => {
    try {
      // Validate inputs
      if (!budgetName.trim()) {
        Alert.alert('Error', 'Please enter a budget name');
        return;
      }

      if (!totalAmount || isNaN(parseFloat(totalAmount))) {
        Alert.alert('Error', 'Please enter a valid total amount');
        return;
      }

      setCreating(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const budgetData = {
        name: budgetName.trim(),
        description: budgetDescription.trim(),
        budgetType,
        totalAmount: parseFloat(totalAmount),
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate ? endDate.toISOString().split('T')[0] : null,
        useAISuggestions,
        targetSavingsRate: targetSavingsRate / 100
      };

      const result = await budgetManagementService.createBudget(userId, budgetData);

      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(
          'Success',
          'Budget created successfully!',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        throw new Error(result.error || 'Failed to create budget');
      }
    } catch (error) {
      console.error('❌ Error creating budget:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', error.message || 'Failed to create budget. Please try again.');
    } finally {
      setCreating(false);
    }
  };

  /**
   * Render header
   */
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
      </TouchableOpacity>
      <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
        Create Budget
      </Text>
      <View style={styles.headerSpacer} />
    </View>
  );

  /**
   * Render basic budget info form
   */
  const renderBasicInfo = () => (
    <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Budget Information
      </Text>

      {/* Budget Name */}
      <View style={styles.inputGroup}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Budget Name *
        </Text>
        <TextInput
          style={[styles.textInput, { 
            backgroundColor: theme.colors.background,
            color: theme.colors.text,
            borderColor: theme.colors.border
          }]}
          value={budgetName}
          onChangeText={setBudgetName}
          placeholder="e.g., Monthly Budget"
          placeholderTextColor={theme.colors.textSecondary}
        />
      </View>

      {/* Budget Description */}
      <View style={styles.inputGroup}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Description
        </Text>
        <TextInput
          style={[styles.textInput, styles.textArea, { 
            backgroundColor: theme.colors.background,
            color: theme.colors.text,
            borderColor: theme.colors.border
          }]}
          value={budgetDescription}
          onChangeText={setBudgetDescription}
          placeholder="Optional description"
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Total Amount */}
      <View style={styles.inputGroup}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Total Amount (UGX) *
        </Text>
        <TextInput
          style={[styles.textInput, { 
            backgroundColor: theme.colors.background,
            color: theme.colors.text,
            borderColor: theme.colors.border
          }]}
          value={totalAmount}
          onChangeText={setTotalAmount}
          placeholder="0"
          placeholderTextColor={theme.colors.textSecondary}
          keyboardType="numeric"
        />
        {aiSuggestions && (
          <Text style={[styles.suggestionText, { color: Colors.primary.main }]}>
            AI Suggested: {formatCurrencyValue(aiSuggestions.totalBudget, 'UGX')}
          </Text>
        )}
      </View>

      {/* Budget Type */}
      <View style={styles.inputGroup}>
        <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
          Budget Period
        </Text>
        <View style={styles.budgetTypeContainer}>
          {['monthly', 'weekly', 'yearly'].map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.budgetTypeButton,
                budgetType === type && { backgroundColor: Colors.primary.main },
                { borderColor: theme.colors.border }
              ]}
              onPress={() => setBudgetType(type)}
            >
              <Text style={[
                styles.budgetTypeText,
                budgetType === type ? { color: 'white' } : { color: theme.colors.text }
              ]}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Date Range */}
      <View style={styles.dateContainer}>
        <View style={styles.dateGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            Start Date *
          </Text>
          <TouchableOpacity
            style={[styles.dateButton, { 
              backgroundColor: theme.colors.background,
              borderColor: theme.colors.border
            }]}
            onPress={() => setShowStartDatePicker(true)}
          >
            <Text style={[styles.dateText, { color: theme.colors.text }]}>
              {startDate.toLocaleDateString()}
            </Text>
            <Ionicons name="calendar" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.dateGroup}>
          <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
            End Date (Optional)
          </Text>
          <TouchableOpacity
            style={[styles.dateButton, { 
              backgroundColor: theme.colors.background,
              borderColor: theme.colors.border
            }]}
            onPress={() => setShowEndDatePicker(true)}
          >
            <Text style={[styles.dateText, { color: theme.colors.text }]}>
              {endDate ? endDate.toLocaleDateString() : 'No end date'}
            </Text>
            <Ionicons name="calendar" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  /**
   * Render AI suggestions section
   */
  const renderAISection = () => (
    <View style={[styles.section, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.aiHeader}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          AI Budget Assistant
        </Text>
        <Switch
          value={useAISuggestions}
          onValueChange={setUseAISuggestions}
          trackColor={{ false: theme.colors.border, true: Colors.primary.main }}
          thumbColor="white"
        />
      </View>

      {useAISuggestions && (
        <>
          {/* Savings Rate */}
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: theme.colors.text }]}>
              Target Savings Rate: {targetSavingsRate}%
            </Text>
            <View style={styles.sliderContainer}>
              <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary }]}>5%</Text>
              <View style={styles.sliderTrack}>
                <View 
                  style={[
                    styles.sliderFill, 
                    { 
                      width: `${(targetSavingsRate - 5) / 45 * 100}%`,
                      backgroundColor: Colors.primary.main 
                    }
                  ]} 
                />
                <TouchableOpacity
                  style={[
                    styles.sliderThumb,
                    { 
                      left: `${(targetSavingsRate - 5) / 45 * 100}%`,
                      backgroundColor: Colors.primary.main 
                    }
                  ]}
                  onPressIn={() => {
                    // Simple slider implementation - could be enhanced
                  }}
                />
              </View>
              <Text style={[styles.sliderLabel, { color: theme.colors.textSecondary }]}>50%</Text>
            </View>
          </View>

          {/* AI Suggestions */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <SkeletonLoader height={80} style={{ borderRadius: 8 }} />
              <Text style={[styles.loadingText, { color: theme.colors.textSecondary }]}>
                Generating AI suggestions...
              </Text>
            </View>
          ) : aiSuggestions ? (
            <View style={styles.suggestionsContainer}>
              <Text style={[styles.suggestionsTitle, { color: theme.colors.text }]}>
                Suggested Category Allocations
              </Text>
              {aiSuggestions.suggestedBudgets?.slice(0, 5).map((category, index) => (
                <BudgetCategoryCard
                  key={index}
                  category={category}
                  theme={theme}
                  formatCurrency={formatCurrencyValue}
                  isPreview={true}
                />
              ))}
              {aiSuggestions.recommendations?.length > 0 && (
                <View style={styles.recommendationsContainer}>
                  <Text style={[styles.recommendationsTitle, { color: theme.colors.text }]}>
                    AI Recommendations
                  </Text>
                  {aiSuggestions.recommendations.slice(0, 2).map((rec, index) => (
                    <View key={index} style={[styles.recommendationItem, { backgroundColor: theme.colors.background }]}>
                      <Ionicons 
                        name={rec.type === 'warning' ? 'warning' : 'bulb'} 
                        size={16} 
                        color={rec.type === 'warning' ? Colors.status.warning : Colors.primary.main} 
                      />
                      <Text style={[styles.recommendationText, { color: theme.colors.text }]}>
                        {rec.message}
                      </Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ) : null}
        </>
      )}
    </View>
  );

  /**
   * Render create button
   */
  const renderCreateButton = () => (
    <View style={styles.buttonContainer}>
      <TouchableOpacity
        style={[
          styles.createButton,
          { backgroundColor: Colors.primary.main },
          creating && styles.createButtonDisabled
        ]}
        onPress={handleCreateBudget}
        disabled={creating}
      >
        {creating ? (
          <ActivityIndicator size="small" color="white" />
        ) : (
          <>
            <Ionicons name="checkmark" size={20} color="white" />
            <Text style={styles.createButtonText}>Create Budget</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderBasicInfo()}
        {renderAISection()}
        {renderCreateButton()}
        
        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={startDate}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowStartDatePicker(false);
            if (selectedDate) {
              setStartDate(selectedDate);
            }
          }}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={endDate || new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowEndDatePicker(false);
            if (selectedDate) {
              setEndDate(selectedDate);
            }
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginLeft: 16,
  },
  headerSpacer: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  suggestionText: {
    fontSize: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  budgetTypeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  budgetTypeButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  budgetTypeText: {
    fontSize: 14,
    fontWeight: '500',
  },
  dateContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  dateGroup: {
    flex: 1,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  dateText: {
    fontSize: 16,
  },
  aiHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  sliderLabel: {
    fontSize: 12,
  },
  sliderTrack: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    position: 'relative',
  },
  sliderFill: {
    height: '100%',
    borderRadius: 2,
  },
  sliderThumb: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    top: -8,
    marginLeft: -10,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 16,
  },
  loadingText: {
    fontSize: 14,
  },
  suggestionsContainer: {
    marginTop: 16,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  recommendationsContainer: {
    marginTop: 16,
  },
  recommendationsTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    marginTop: 20,
  },
  createButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    padding: 16,
    borderRadius: 12,
  },
  createButtonDisabled: {
    opacity: 0.6,
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomSpacing: {
    height: 40,
  },
});

export default CreateBudgetScreen;
