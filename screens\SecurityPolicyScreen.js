import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SecurityPolicyScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [expandedSection, setExpandedSection] = useState(null);

  const policyLastUpdated = 'December 15, 2024';
  const effectiveDate = 'January 1, 2025';

  const callSupport = (phoneNumber) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Call Security Hotline',
      `Do you want to call ${phoneNumber}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => Linking.openURL(`tel:${phoneNumber}`)
        }
      ]
    );
  };

  const emailSupport = (email) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const subject = 'JiraniPay Security Policy Inquiry';
    const body = 'Hello JiraniPay Security Team,\n\nI have a question regarding your security policy:\n\n[Please describe your inquiry here]\n\nThank you.';

    Alert.alert(
      'Email Security Team',
      `Do you want to send an email to ${email}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Email',
          onPress: () => Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`)
        }
      ]
    );
  };

  const policySections = [
    {
      id: '1',
      title: 'Data Protection & Privacy',
      icon: 'shield-checkmark',
      color: Colors.primary.main,
      content: [
        'JiraniPay is committed to protecting your personal and financial data in accordance with Uganda\'s Data Protection and Privacy Act 2019 and East African Community data protection standards.',
        'We collect only necessary information required for financial services and regulatory compliance.',
        'Your data is encrypted using AES-256 encryption both in transit and at rest.',
        'We implement strict access controls ensuring only authorized personnel can access your information.',
        'Data retention periods comply with Bank of Uganda requirements: transaction records (7 years), KYC documents (5 years after account closure).',
        'You have the right to access, correct, or delete your personal data subject to regulatory requirements.',
        'Cross-border data transfers are conducted only with adequate safeguards and regulatory approval.'
      ]
    },
    {
      id: '2',
      title: 'Transaction Security',
      icon: 'card',
      color: Colors.status.success,
      content: [
        'All transactions are protected by multi-layer security including PIN, biometric authentication, and device verification.',
        'We use real-time fraud monitoring systems to detect and prevent suspicious activities.',
        'Transaction limits are enforced: Daily limit UGX 5,000,000, Monthly limit UGX 50,000,000 (subject to KYC level).',
        'All transactions are logged and monitored for compliance with Anti-Money Laundering (AML) regulations.',
        'Disputed transactions are investigated within 48 hours with provisional credit provided where applicable.',
        'We maintain transaction records for audit purposes as required by Bank of Uganda guidelines.',
        'End-to-end encryption ensures transaction data cannot be intercepted or tampered with.'
      ]
    },
    {
      id: '3',
      title: 'User Authentication',
      icon: 'finger-print',
      color: Colors.accent.gold,
      content: [
        'Multi-factor authentication is required for account access and high-value transactions.',
        'Biometric authentication (fingerprint/Face ID) is stored locally on your device using secure hardware.',
        'PIN requirements: minimum 6 digits, cannot be sequential or repetitive patterns.',
        'Account lockout occurs after 5 failed authentication attempts to prevent brute force attacks.',
        'Session timeout is configurable (5-120 minutes) with automatic logout for security.',
        'Device registration and trusted device management prevent unauthorized access.',
        'Password recovery requires multiple verification steps including SMS OTP and security questions.'
      ]
    },
    {
      id: '4',
      title: 'Incident Response',
      icon: 'warning',
      color: Colors.status.error,
      content: [
        'Security incidents are classified and responded to within defined timeframes: Critical (1 hour), High (4 hours), Medium (24 hours).',
        'Customers are notified of security breaches affecting their accounts within 72 hours.',
        'Incident response team includes security specialists, legal counsel, and regulatory liaison officers.',
        'Forensic investigation procedures ensure evidence preservation and root cause analysis.',
        'Business continuity plans ensure service availability during security incidents.',
        'Post-incident reviews identify improvements and prevent recurrence.',
        'Coordination with law enforcement and regulatory authorities as required by Ugandan law.'
      ]
    },
    {
      id: '5',
      title: 'Regulatory Compliance',
      icon: 'document-text',
      color: Colors.secondary.forest,
      content: [
        'JiraniPay operates under license from Bank of Uganda and complies with all applicable financial regulations.',
        'Anti-Money Laundering (AML) and Counter-Terrorism Financing (CTF) controls are implemented per Uganda\'s AML Act 2013.',
        'Know Your Customer (KYC) procedures follow Bank of Uganda guidelines for customer identification and verification.',
        'Suspicious transaction reporting is conducted as required by the Financial Intelligence Authority (FIA).',
        'Regular compliance audits are performed by independent third parties.',
        'Staff undergo mandatory compliance training and background checks.',
        'Cross-border compliance includes adherence to EAC monetary union protocols and international standards.'
      ]
    },
    {
      id: '6',
      title: 'Third-Party Security',
      icon: 'people',
      color: Colors.primary.main,
      content: [
        'All third-party service providers undergo rigorous security assessments before integration.',
        'Vendor contracts include specific security requirements and audit rights.',
        'API security includes rate limiting, authentication tokens, and encryption.',
        'Regular security reviews of third-party integrations ensure ongoing compliance.',
        'Data sharing agreements specify permitted uses and security requirements.',
        'Incident notification requirements ensure prompt communication of security issues.',
        'Termination procedures ensure secure data return or destruction.'
      ]
    }
  ];

  const toggleSection = (sectionId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setExpandedSection(expandedSection === sectionId ? null : sectionId);
  };

  const renderPolicySection = (section) => {
    const isExpanded = expandedSection === section.id;
    
    return (
      <TouchableOpacity
        key={section.id}
        style={[styles.sectionCard, isExpanded && styles.expandedSectionCard]}
        onPress={() => toggleSection(section.id)}
        activeOpacity={0.7}
      >
        <View style={styles.sectionHeader}>
          <View style={styles.sectionLeft}>
            <View style={[styles.sectionIcon, { backgroundColor: section.color + '20' }]}>
              <Ionicons name={section.icon} size={24} color={section.color} />
            </View>
            <Text style={styles.sectionTitle}>{section.title}</Text>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={Colors.neutral.warmGray} 
          />
        </View>
        
        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.content.map((paragraph, index) => (
              <View key={index} style={styles.paragraphItem}>
                <View style={styles.bulletPoint} />
                <Text style={styles.paragraphText}>{paragraph}</Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>Security Policy</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          Comprehensive security and compliance framework
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Policy Overview */}
        <View style={styles.section}>
          <View style={styles.overviewCard}>
            <View style={styles.overviewIcon}>
              <Ionicons name="shield-checkmark" size={32} color={Colors.status.success} />
            </View>
            <View style={styles.overviewText}>
              <Text style={styles.overviewTitle}>JiraniPay Security Policy</Text>
              <Text style={styles.overviewVersion}>Version 2.1</Text>
              <Text style={styles.overviewDate}>
                Last Updated: {policyLastUpdated}
              </Text>
              <Text style={styles.overviewDate}>
                Effective Date: {effectiveDate}
              </Text>
            </View>
          </View>
        </View>

        {/* Introduction */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Introduction</Text>
          <Text style={styles.introText}>
            This Security Policy outlines JiraniPay's commitment to protecting customer data and maintaining 
            the highest standards of financial security. Our framework complies with Uganda's regulatory 
            requirements, East African Community standards, and international best practices for fintech security.
          </Text>
          <Text style={styles.introText}>
            This policy applies to all JiraniPay services, employees, contractors, and third-party partners. 
            Regular reviews ensure our security measures evolve with emerging threats and regulatory changes.
          </Text>
        </View>

        {/* Policy Sections */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Policy Framework</Text>
          <Text style={styles.sectionDescription}>
            Tap any section below to view detailed security measures and compliance requirements
          </Text>

          {policySections.map(renderPolicySection)}
        </View>

        {/* Regulatory Framework */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Regulatory Framework</Text>
          
          <View style={styles.regulatoryCard}>
            <Ionicons name="flag" size={24} color={Colors.primary.main} />
            <View style={styles.regulatoryText}>
              <Text style={styles.regulatoryTitle}>Uganda Compliance</Text>
              <Text style={styles.regulatoryDescription}>
                Bank of Uganda Financial Institutions Act 2004, Data Protection Act 2019, AML Act 2013
              </Text>
            </View>
          </View>

          <View style={styles.regulatoryCard}>
            <Ionicons name="globe" size={24} color={Colors.status.success} />
            <View style={styles.regulatoryText}>
              <Text style={styles.regulatoryTitle}>East African Community</Text>
              <Text style={styles.regulatoryDescription}>
                EAC Monetary Union Protocol, Regional Payment Systems Guidelines
              </Text>
            </View>
          </View>

          <View style={styles.regulatoryCard}>
            <Ionicons name="shield" size={24} color={Colors.accent.gold} />
            <View style={styles.regulatoryText}>
              <Text style={styles.regulatoryTitle}>International Standards</Text>
              <Text style={styles.regulatoryDescription}>
                ISO 27001, PCI DSS, SWIFT Customer Security Programme (CSP)
              </Text>
            </View>
          </View>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Security Contacts</Text>
          
          <TouchableOpacity
            style={styles.contactCard}
            onPress={() => callSupport('+************')}
            activeOpacity={0.7}
          >
            <Ionicons name="call" size={24} color={Colors.status.error} />
            <View style={styles.contactText}>
              <Text style={styles.contactTitle}>Security Hotline</Text>
              <Text style={styles.contactNumber}>+************</Text>
              <Text style={styles.contactDescription}>24/7 security incident reporting</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.contactCard}
            onPress={() => emailSupport('<EMAIL>')}
            activeOpacity={0.7}
          >
            <Ionicons name="mail" size={24} color={Colors.primary.main} />
            <View style={styles.contactText}>
              <Text style={styles.contactTitle}>Security Team</Text>
              <Text style={styles.contactEmail}><EMAIL></Text>
              <Text style={styles.contactDescription}>Non-urgent security matters</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  overviewCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overviewIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.status.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  overviewText: {
    flex: 1,
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  overviewVersion: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  overviewDate: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginBottom: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  introText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 22,
    marginBottom: 15,
    textAlign: 'justify',
  },
  sectionCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expandedSectionCard: {
    borderWidth: 1,
    borderColor: Colors.primary.main + '30',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionContent: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  paragraphItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  bulletPoint: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary.main,
    marginTop: 6,
    marginRight: 12,
  },
  paragraphText: {
    flex: 1,
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
    textAlign: 'justify',
  },
  regulatoryCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  regulatoryText: {
    flex: 1,
    marginLeft: 12,
  },
  regulatoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  regulatoryDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contactText: {
    flex: 1,
    marginLeft: 12,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.status.error,
    marginBottom: 4,
  },
  contactEmail: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  contactDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
});

export default SecurityPolicyScreen;
