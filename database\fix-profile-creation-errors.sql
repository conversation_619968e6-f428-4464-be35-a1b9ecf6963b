-- =====================================================
-- FIX PROFILE CREATION ERRORS - PRODUCTION MIGRATION
-- =====================================================
-- This script fixes the profile creation errors by ensuring
-- proper database schema and constraints
-- Run this in your Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. ENSURE PROFILES TABLE EXISTS WITH CORRECT SCHEMA
-- =====================================================
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    phone TEXT UNIQUE,
    email TEXT,
    avatar_url TEXT,
    country_code TEXT DEFAULT 'UG',
    language_preference TEXT DEFAULT 'en',
    kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected')),
    kyc_level INTEGER DEFAULT 1 CHECK (kyc_level BETWEEN 1 AND 3),
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. ENSURE USER_PREFERENCES TABLE WITH CORRECT COLUMNS
-- =====================================================
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT TRUE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    preferred_currency TEXT DEFAULT 'UGX',
    language_preference TEXT DEFAULT 'en',
    transaction_limit_daily DECIMAL(15,2) DEFAULT 1000000,
    transaction_limit_monthly DECIMAL(15,2) DEFAULT 10000000,
    security_pin_enabled BOOLEAN DEFAULT FALSE,
    auto_logout_minutes INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 3. ADD MISSING COLUMNS IF THEY DON'T EXIST
-- =====================================================

-- Add preferred_currency column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_preferences' 
        AND column_name = 'preferred_currency'
    ) THEN
        ALTER TABLE public.user_preferences 
        ADD COLUMN preferred_currency TEXT DEFAULT 'UGX';
    END IF;
END $$;

-- Add language_preference column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_preferences' 
        AND column_name = 'language_preference'
    ) THEN
        ALTER TABLE public.user_preferences 
        ADD COLUMN language_preference TEXT DEFAULT 'en';
    END IF;
END $$;

-- =====================================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Profiles table indexes
CREATE INDEX IF NOT EXISTS idx_profiles_phone ON public.profiles(phone);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_country_code ON public.profiles(country_code);
CREATE INDEX IF NOT EXISTS idx_profiles_kyc_status ON public.profiles(kyc_status);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_preferences_currency ON public.user_preferences(preferred_currency);
CREATE INDEX IF NOT EXISTS idx_user_preferences_language ON public.user_preferences(language_preference);

-- =====================================================
-- 5. ENABLE ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. CREATE RLS POLICIES
-- =====================================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can manage own preferences" ON public.user_preferences;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User preferences policies
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = id);

-- =====================================================
-- 7. CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to safely create user profile (handles race conditions)
CREATE OR REPLACE FUNCTION public.create_user_profile_safe(
    user_id UUID,
    full_name TEXT DEFAULT NULL,
    phone TEXT DEFAULT NULL,
    email TEXT DEFAULT NULL,
    country_code TEXT DEFAULT 'UG'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    existing_profile public.profiles%ROWTYPE;
BEGIN
    -- Check if profile already exists
    SELECT * INTO existing_profile 
    FROM public.profiles 
    WHERE id = user_id;
    
    IF FOUND THEN
        -- Profile exists, return it
        SELECT json_build_object(
            'success', true,
            'data', row_to_json(existing_profile),
            'created', false,
            'message', 'Profile already exists'
        ) INTO result;
        RETURN result;
    END IF;
    
    -- Profile doesn't exist, create it
    INSERT INTO public.profiles (
        id, 
        full_name, 
        phone, 
        email, 
        country_code,
        created_at,
        updated_at
    ) VALUES (
        user_id,
        full_name,
        phone,
        email,
        country_code,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        updated_at = NOW()
    RETURNING * INTO existing_profile;
    
    -- Create default preferences
    INSERT INTO public.user_preferences (
        id,
        preferred_currency,
        language_preference,
        created_at,
        updated_at
    ) VALUES (
        user_id,
        'UGX',
        'en',
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO NOTHING;
    
    SELECT json_build_object(
        'success', true,
        'data', row_to_json(existing_profile),
        'created', true,
        'message', 'Profile created successfully'
    ) INTO result;
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        SELECT json_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to create profile'
        ) INTO result;
        RETURN result;
END;
$$;

-- =====================================================
-- 8. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.user_preferences TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_profile_safe TO authenticated;

-- =====================================================
-- 9. VERIFICATION QUERIES
-- =====================================================

-- Verify tables exist
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'user_preferences')
ORDER BY table_name;

-- Verify columns exist
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'user_preferences')
ORDER BY table_name, ordinal_position;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Profile creation errors migration completed successfully!';
    RAISE NOTICE 'Tables created: profiles, user_preferences';
    RAISE NOTICE 'RLS policies enabled and configured';
    RAISE NOTICE 'Helper function created: create_user_profile_safe()';
    RAISE NOTICE 'Ready for production profile creation!';
END $$;
