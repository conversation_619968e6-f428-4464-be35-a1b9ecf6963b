/**
 * Audit Service
 * Comprehensive audit trail and compliance logging for financial transactions
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('./database');
const redisService = require('./redis');

class AuditService {
  constructor() {
    this.auditTypes = {
      TRANSACTION: 'transaction',
      USER_ACTION: 'user_action',
      ADMIN_ACTION: 'admin_action',
      SECURITY: 'security',
      COMPLIANCE: 'compliance',
      SYSTEM: 'system'
    };

    this.actionTypes = {
      CREATE: 'create',
      UPDATE: 'update',
      DELETE: 'delete',
      VIEW: 'view',
      APPROVE: 'approve',
      REJECT: 'reject',
      SUSPEND: 'suspend',
      ACTIVATE: 'activate'
    };
  }

  /**
   * Log audit event
   */
  async logAuditEvent(eventData) {
    try {
      const {
        type,
        action,
        userId,
        targetUserId = null,
        resourceType,
        resourceId,
        details = {},
        ipAddress = null,
        userAgent = null,
        sessionId = null
      } = eventData;

      const auditEvent = {
        id: uuidv4(),
        type,
        action,
        user_id: userId,
        target_user_id: targetUserId,
        resource_type: resourceType,
        resource_id: resourceId,
        details: JSON.stringify(details),
        ip_address: ipAddress,
        user_agent: userAgent,
        session_id: sessionId,
        timestamp: new Date().toISOString()
      };

      // Store in database
      const supabase = databaseService.getSupabase();
      const { data: createdEvent, error } = await supabase
        .from('audit_logs')
        .insert(auditEvent)
        .select()
        .single();

      if (error) {
        logger.error('Failed to create audit log:', error);
        // Don't throw error as audit logging shouldn't break main functionality
        return null;
      }

      // Cache recent audit events for quick access
      await this.cacheAuditEvent(userId, createdEvent);

      // Log to application logger for immediate visibility
      logger.audit(`${type}:${action}`, {
        userId,
        resourceType,
        resourceId,
        details: Object.keys(details).length > 0 ? details : undefined
      });

      return createdEvent;
    } catch (error) {
      logger.error('Audit logging failed:', error);
      return null;
    }
  }

  /**
   * Log transaction audit event
   */
  async logTransactionEvent(transactionId, action, userId, details = {}) {
    return await this.logAuditEvent({
      type: this.auditTypes.TRANSACTION,
      action,
      userId,
      resourceType: 'transaction',
      resourceId: transactionId,
      details
    });
  }

  /**
   * Log user action audit event
   */
  async logUserAction(action, userId, resourceType, resourceId, details = {}, context = {}) {
    return await this.logAuditEvent({
      type: this.auditTypes.USER_ACTION,
      action,
      userId,
      resourceType,
      resourceId,
      details,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      sessionId: context.sessionId
    });
  }

  /**
   * Log admin action audit event
   */
  async logAdminAction(action, adminUserId, targetUserId, resourceType, resourceId, details = {}, context = {}) {
    return await this.logAuditEvent({
      type: this.auditTypes.ADMIN_ACTION,
      action,
      userId: adminUserId,
      targetUserId,
      resourceType,
      resourceId,
      details,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      sessionId: context.sessionId
    });
  }

  /**
   * Log security event
   */
  async logSecurityEvent(action, userId, details = {}, context = {}) {
    return await this.logAuditEvent({
      type: this.auditTypes.SECURITY,
      action,
      userId,
      resourceType: 'security',
      resourceId: userId,
      details,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      sessionId: context.sessionId
    });
  }

  /**
   * Log compliance event
   */
  async logComplianceEvent(action, userId, details = {}) {
    return await this.logAuditEvent({
      type: this.auditTypes.COMPLIANCE,
      action,
      userId,
      resourceType: 'compliance',
      resourceId: userId,
      details
    });
  }

  /**
   * Get audit trail for a resource
   */
  async getAuditTrail(resourceType, resourceId, options = {}) {
    try {
      const {
        page = 1,
        limit = 50,
        type = null,
        action = null,
        startDate = null,
        endDate = null
      } = options;

      const offset = (page - 1) * limit;

      const supabase = databaseService.getSupabase();
      let query = supabase
        .from('audit_logs')
        .select(`
          *,
          user:user_id(full_name, email),
          target_user:target_user_id(full_name, email)
        `)
        .eq('resource_type', resourceType)
        .eq('resource_id', resourceId)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (type) {
        query = query.eq('type', type);
      }

      if (action) {
        query = query.eq('action', action);
      }

      if (startDate) {
        query = query.gte('timestamp', startDate);
      }

      if (endDate) {
        query = query.lte('timestamp', endDate);
      }

      const { data: auditLogs, error } = await query;

      if (error) {
        logger.error('Failed to get audit trail:', error);
        throw new Error('Failed to retrieve audit trail');
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('audit_logs')
        .select('*', { count: 'exact', head: true })
        .eq('resource_type', resourceType)
        .eq('resource_id', resourceId);

      if (type) countQuery = countQuery.eq('type', type);
      if (action) countQuery = countQuery.eq('action', action);
      if (startDate) countQuery = countQuery.gte('timestamp', startDate);
      if (endDate) countQuery = countQuery.lte('timestamp', endDate);

      const { count, error: countError } = await countQuery;

      if (countError) {
        logger.error('Failed to get audit count:', countError);
      }

      const totalPages = Math.ceil((count || 0) / limit);

      return {
        auditLogs: auditLogs.map(log => ({
          ...log,
          details: log.details ? JSON.parse(log.details) : null
        })),
        pagination: {
          page,
          limit,
          total: count || 0,
          pages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to get audit trail:', error);
      throw error;
    }
  }

  /**
   * Get user activity summary
   */
  async getUserActivitySummary(userId, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const supabase = databaseService.getSupabase();
      const { data: activities, error } = await supabase
        .from('audit_logs')
        .select('type, action, timestamp')
        .eq('user_id', userId)
        .gte('timestamp', startDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) {
        logger.error('Failed to get user activity summary:', error);
        throw new Error('Failed to retrieve user activity summary');
      }

      // Aggregate activities by type and action
      const summary = {
        totalActivities: activities.length,
        byType: {},
        byAction: {},
        dailyActivity: {},
        recentActivities: activities.slice(0, 10)
      };

      activities.forEach(activity => {
        // Count by type
        summary.byType[activity.type] = (summary.byType[activity.type] || 0) + 1;

        // Count by action
        summary.byAction[activity.action] = (summary.byAction[activity.action] || 0) + 1;

        // Count by day
        const day = activity.timestamp.split('T')[0];
        summary.dailyActivity[day] = (summary.dailyActivity[day] || 0) + 1;
      });

      return summary;
    } catch (error) {
      logger.error('Failed to get user activity summary:', error);
      throw error;
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(startDate, endDate, options = {}) {
    try {
      const {
        includeTransactions = true,
        includeUserActions = true,
        includeSecurityEvents = true,
        format = 'json'
      } = options;

      const supabase = databaseService.getSupabase();
      let query = supabase
        .from('audit_logs')
        .select(`
          *,
          user:user_id(full_name, email, phone_number),
          target_user:target_user_id(full_name, email, phone_number)
        `)
        .gte('timestamp', startDate)
        .lte('timestamp', endDate)
        .order('timestamp', { ascending: false });

      // Apply type filters
      const types = [];
      if (includeTransactions) types.push(this.auditTypes.TRANSACTION);
      if (includeUserActions) types.push(this.auditTypes.USER_ACTION);
      if (includeSecurityEvents) types.push(this.auditTypes.SECURITY);

      if (types.length > 0) {
        query = query.in('type', types);
      }

      const { data: auditLogs, error } = await query;

      if (error) {
        logger.error('Failed to generate compliance report:', error);
        throw new Error('Failed to generate compliance report');
      }

      const report = {
        reportId: uuidv4(),
        generatedAt: new Date().toISOString(),
        period: {
          startDate,
          endDate
        },
        summary: {
          totalEvents: auditLogs.length,
          byType: {},
          byAction: {},
          uniqueUsers: new Set(auditLogs.map(log => log.user_id)).size
        },
        events: auditLogs.map(log => ({
          ...log,
          details: log.details ? JSON.parse(log.details) : null
        }))
      };

      // Generate summary statistics
      auditLogs.forEach(log => {
        report.summary.byType[log.type] = (report.summary.byType[log.type] || 0) + 1;
        report.summary.byAction[log.action] = (report.summary.byAction[log.action] || 0) + 1;
      });

      // Log report generation
      logger.audit('Compliance report generated', {
        reportId: report.reportId,
        period: report.period,
        eventCount: report.summary.totalEvents
      });

      return report;
    } catch (error) {
      logger.error('Failed to generate compliance report:', error);
      throw error;
    }
  }

  /**
   * Cache audit event for quick access
   */
  async cacheAuditEvent(userId, auditEvent) {
    try {
      const cacheKey = `recent_audit:${userId}`;
      const recentEvents = await redisService.get(cacheKey) || [];
      
      // Add new event to the beginning
      recentEvents.unshift(auditEvent);
      
      // Keep only last 50 events
      if (recentEvents.length > 50) {
        recentEvents.splice(50);
      }
      
      // Cache for 24 hours
      await redisService.set(cacheKey, recentEvents, 24 * 60 * 60);
    } catch (error) {
      logger.error('Failed to cache audit event:', error);
    }
  }

  /**
   * Search audit logs
   */
  async searchAuditLogs(searchCriteria, options = {}) {
    try {
      const {
        userId,
        type,
        action,
        resourceType,
        resourceId,
        ipAddress,
        startDate,
        endDate,
        page = 1,
        limit = 50
      } = { ...searchCriteria, ...options };

      const offset = (page - 1) * limit;

      const supabase = databaseService.getSupabase();
      let query = supabase
        .from('audit_logs')
        .select(`
          *,
          user:user_id(full_name, email),
          target_user:target_user_id(full_name, email)
        `)
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply search filters
      if (userId) query = query.eq('user_id', userId);
      if (type) query = query.eq('type', type);
      if (action) query = query.eq('action', action);
      if (resourceType) query = query.eq('resource_type', resourceType);
      if (resourceId) query = query.eq('resource_id', resourceId);
      if (ipAddress) query = query.eq('ip_address', ipAddress);
      if (startDate) query = query.gte('timestamp', startDate);
      if (endDate) query = query.lte('timestamp', endDate);

      const { data: auditLogs, error } = await query;

      if (error) {
        logger.error('Failed to search audit logs:', error);
        throw new Error('Failed to search audit logs');
      }

      return {
        auditLogs: auditLogs.map(log => ({
          ...log,
          details: log.details ? JSON.parse(log.details) : null
        })),
        searchCriteria,
        pagination: {
          page,
          limit,
          total: auditLogs.length // This would need a separate count query for exact total
        }
      };
    } catch (error) {
      logger.error('Failed to search audit logs:', error);
      throw error;
    }
  }
}

// Create singleton instance
const auditService = new AuditService();

module.exports = auditService;
