import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';

const { width } = Dimensions.get('window');

/**
 * PIN Keypad Component
 * Modern numeric keypad for PIN entry with haptic feedback
 */
const PinKeypad = ({ onNumberPress, onBackspace, onClear, disabled = false }) => {
  const handleNumberPress = (number) => {
    if (disabled) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onNumberPress(number);
  };

  const handleBackspace = () => {
    if (disabled) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onBackspace();
  };

  const handleClear = () => {
    if (disabled) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    onClear();
  };

  const renderKey = (value, onPress, style = {}, textStyle = {}) => (
    <TouchableOpacity
      style={[styles.key, disabled && styles.disabledKey, style]}
      onPress={onPress}
      activeOpacity={0.7}
      disabled={disabled}
    >
      {typeof value === 'string' ? (
        <Text style={[styles.keyText, disabled && styles.disabledText, textStyle]}>
          {value}
        </Text>
      ) : (
        value
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Row 1: 1, 2, 3 */}
      <View style={styles.row}>
        {renderKey('1', () => handleNumberPress('1'))}
        {renderKey('2', () => handleNumberPress('2'))}
        {renderKey('3', () => handleNumberPress('3'))}
      </View>

      {/* Row 2: 4, 5, 6 */}
      <View style={styles.row}>
        {renderKey('4', () => handleNumberPress('4'))}
        {renderKey('5', () => handleNumberPress('5'))}
        {renderKey('6', () => handleNumberPress('6'))}
      </View>

      {/* Row 3: 7, 8, 9 */}
      <View style={styles.row}>
        {renderKey('7', () => handleNumberPress('7'))}
        {renderKey('8', () => handleNumberPress('8'))}
        {renderKey('9', () => handleNumberPress('9'))}
      </View>

      {/* Row 4: Clear, 0, Backspace */}
      <View style={styles.row}>
        {renderKey(
          'Clear',
          handleClear,
          styles.actionKey,
          styles.actionKeyText
        )}
        {renderKey('0', () => handleNumberPress('0'))}
        {renderKey(
          <Ionicons 
            name="backspace-outline" 
            size={24} 
            color={disabled ? Colors.neutral.warmGray : Colors.neutral.charcoal} 
          />,
          handleBackspace,
          styles.actionKey
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    paddingHorizontal: 10,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  key: {
    width: (width - 80) / 3,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  disabledKey: {
    backgroundColor: Colors.neutral.lightGray,
    opacity: 0.6,
  },
  keyText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  disabledText: {
    color: Colors.neutral.warmGray,
  },
  actionKey: {
    backgroundColor: Colors.primary.main + '20',
    borderColor: Colors.primary.main + '40',
  },
  actionKeyText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary.main,
  },
});

export default PinKeypad;
