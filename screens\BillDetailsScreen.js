import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import { detectNetworkProvider } from '../utils/countriesConfig';

const BillDetailsScreen = ({ navigation, route }) => {
  const { category, provider } = route.params;
  const [accountNumber, setAccountNumber] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [reference, setReference] = useState('');
  const [selectedServiceType, setSelectedServiceType] = useState(null);
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Phone number validation for mobile providers
  const [phoneValidation, setPhoneValidation] = useState({
    isValid: true,
    errorMessage: '',
    detectedProvider: null,
    showMismatchWarning: false
  });

  const validatePhoneNumber = (phoneNumber, provider) => {
    if (!provider || provider.type !== 'mobile') {
      return {
        isValid: true,
        errorMessage: '',
        detectedProvider: null,
        showMismatchWarning: false
      };
    }

    const cleanedNumber = phoneNumber.replace(/\D/g, '');
    
    if (cleanedNumber.length < 9) {
      return {
        isValid: true,
        errorMessage: '',
        detectedProvider: null,
        showMismatchWarning: false
      };
    }

    let localNumber = cleanedNumber;
    if (cleanedNumber.startsWith('256')) {
      localNumber = cleanedNumber.substring(3);
    } else if (cleanedNumber.startsWith('0')) {
      localNumber = cleanedNumber.substring(1);
    }

    const detectedProvider = detectNetworkProvider(localNumber, 'UG');

    if (!detectedProvider) {
      return {
        isValid: false,
        errorMessage: 'Invalid Uganda phone number format',
        detectedProvider: null,
        showMismatchWarning: false
      };
    }

    const providerNetworkMap = {
      'mtn': 'MTN',
      'airtel': 'Airtel',
      'utl': 'UTL'
    };

    const expectedNetwork = providerNetworkMap[provider.id];
    const detectedNetwork = detectedProvider.key;

    if (expectedNetwork && detectedNetwork !== expectedNetwork) {
      return {
        isValid: false,
        errorMessage: `Phone number doesn't match selected provider. This appears to be a ${detectedProvider.name} number.`,
        detectedProvider: detectedProvider,
        showMismatchWarning: true
      };
    }

    return {
      isValid: true,
      errorMessage: '',
      detectedProvider: detectedProvider,
      showMismatchWarning: false
    };
  };

  const handleAccountNumberChange = (text) => {
    setAccountNumber(text);
    
    if (provider && provider.type === 'mobile') {
      const validation = validatePhoneNumber(text, provider);
      setPhoneValidation(validation);
    } else {
      setPhoneValidation({
        isValid: true,
        errorMessage: '',
        detectedProvider: null,
        showMismatchWarning: false
      });
    }
  };

  const handleServiceTypeSelect = (serviceType) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedServiceType(serviceType);
  };

  const handleNext = () => {
    if (!accountNumber.trim()) {
      Alert.alert('Error', 'Please enter account number');
      return;
    }

    if (provider?.type === 'mobile' && !phoneValidation.isValid) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    // For mobile providers, require service type selection
    if (provider?.type === 'mobile' && !selectedServiceType) {
      Alert.alert('Error', 'Please select a service type (Airtime or Data Bundles)');
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('BillAmount', {
      category,
      provider,
      billDetails: {
        accountNumber: accountNumber.trim(),
        customerName: customerName.trim(),
        reference: reference.trim(),
        serviceType: selectedServiceType, // Pass service type for mobile providers
      }
    });
  };

  const getAccountLabel = () => {
    if (provider.accountLabel) return provider.accountLabel;
    return provider.type === 'mobile' ? 'Phone Number' : 'Account Number';
  };

  const getAccountPlaceholder = () => {
    if (provider.accountPlaceholder) return provider.accountPlaceholder;
    return provider.type === 'mobile' ? 'Enter phone number' : 'Enter account number';
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme.statusBar === 'dark' ? 'dark-content' : 'light-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Header */}
      <LinearGradient
        colors={['#E67E22', '#D35400']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('bills.payBill')}</Text>
          <View style={styles.headerSpacer} />
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Provider Info */}
          <View style={styles.providerInfo}>
            <View style={[styles.providerIcon, { backgroundColor: provider.color + '15' }]}>
              <Ionicons
                name={provider.icon}
                size={32}
                color={provider.color}
              />
            </View>
            <Text style={styles.providerName}>{provider.name}</Text>
            <Text style={styles.categoryName}>{category.title}</Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>{getAccountLabel()}</Text>
              <TextInput
                style={[
                  styles.textInput,
                  !phoneValidation.isValid && provider?.type === 'mobile' && styles.inputError
                ]}
                value={accountNumber}
                onChangeText={handleAccountNumberChange}
                placeholder={getAccountPlaceholder()}
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType={provider.type === 'mobile' ? 'phone-pad' : 'default'}
              />
              
              {/* Validation Error */}
              {!phoneValidation.isValid && provider?.type === 'mobile' && (
                <View style={styles.validationErrorContainer}>
                  <Ionicons name="warning" size={16} color="#E53E3E" />
                  <Text style={styles.validationErrorText}>
                    {phoneValidation.errorMessage}
                  </Text>
                </View>
              )}
            </View>

            {/* Customer Name (optional for some providers) */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Customer Name (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={customerName}
                onChangeText={setCustomerName}
                placeholder="Enter customer name"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            {/* Reference (optional) */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Reference (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={reference}
                onChangeText={setReference}
                placeholder="Enter reference"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            {/* Service Type Selection for Mobile Providers */}
            {provider?.type === 'mobile' && (
              <View style={styles.serviceTypeSection}>
                <Text style={styles.serviceTypeTitle}>Select Service Type</Text>
                <Text style={styles.serviceTypeSubtitle}>Choose the type of service you want to pay for</Text>

                <View style={styles.serviceTypeButtons}>
                  <TouchableOpacity
                    style={[
                      styles.serviceTypeButton,
                      selectedServiceType === 'airtime' && styles.selectedServiceTypeButton
                    ]}
                    onPress={() => handleServiceTypeSelect('airtime')}
                    activeOpacity={0.7}
                  >
                    <View style={[
                      styles.serviceTypeIcon,
                      selectedServiceType === 'airtime' && styles.selectedServiceTypeIcon
                    ]}>
                      <Ionicons
                        name="phone-portrait"
                        size={24}
                        color={selectedServiceType === 'airtime' ? Colors.neutral.white : '#E67E22'}
                      />
                    </View>
                    <Text style={[
                      styles.serviceTypeButtonTitle,
                      selectedServiceType === 'airtime' && styles.selectedServiceTypeButtonTitle
                    ]}>
                      Airtime Top-up
                    </Text>
                    <Text style={[
                      styles.serviceTypeButtonSubtitle,
                      selectedServiceType === 'airtime' && styles.selectedServiceTypeButtonSubtitle
                    ]}>
                      Add credit to your phone
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.serviceTypeButton,
                      selectedServiceType === 'data' && styles.selectedServiceTypeButton
                    ]}
                    onPress={() => handleServiceTypeSelect('data')}
                    activeOpacity={0.7}
                  >
                    <View style={[
                      styles.serviceTypeIcon,
                      selectedServiceType === 'data' && styles.selectedServiceTypeIcon
                    ]}>
                      <Ionicons
                        name="wifi"
                        size={24}
                        color={selectedServiceType === 'data' ? Colors.neutral.white : '#E67E22'}
                      />
                    </View>
                    <Text style={[
                      styles.serviceTypeButtonTitle,
                      selectedServiceType === 'data' && styles.selectedServiceTypeButtonTitle
                    ]}>
                      Data Bundles
                    </Text>
                    <Text style={[
                      styles.serviceTypeButtonSubtitle,
                      selectedServiceType === 'data' && styles.selectedServiceTypeButtonSubtitle
                    ]}>
                      Purchase internet data
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Next Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.nextButton,
              (!accountNumber.trim() ||
               (provider?.type === 'mobile' && !phoneValidation.isValid) ||
               (provider?.type === 'mobile' && !selectedServiceType)) && styles.nextButtonDisabled
            ]}
            onPress={handleNext}
            disabled={!accountNumber.trim() ||
                     (provider?.type === 'mobile' && !phoneValidation.isValid) ||
                     (provider?.type === 'mobile' && !selectedServiceType)}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#E67E22', '#D35400']}
              style={styles.nextButtonGradient}
            >
              <Text style={styles.nextButtonText}>Next</Text>
              <Ionicons name="arrow-forward" size={20} color={Colors.neutral.white} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  providerInfo: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  providerIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  providerName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  categoryName: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  form: {
    paddingHorizontal: 20,
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 1,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  inputError: {
    borderColor: '#E53E3E',
  },
  validationErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    gap: 8,
  },
  validationErrorText: {
    flex: 1,
    fontSize: 14,
    color: '#E53E3E',
    lineHeight: 18,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  nextButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  nextButtonDisabled: {
    opacity: 0.6,
    elevation: 2,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    gap: 12,
  },
  nextButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  // Service Type Selection Styles
  serviceTypeSection: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  serviceTypeTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  serviceTypeSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  serviceTypeButtons: {
    gap: 12,
  },
  serviceTypeButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  selectedServiceTypeButton: {
    borderColor: '#E67E22',
    backgroundColor: '#E67E22' + '10',
    elevation: 4,
  },
  serviceTypeIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E67E22' + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectedServiceTypeIcon: {
    backgroundColor: '#E67E22',
  },
  serviceTypeButtonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  selectedServiceTypeButtonTitle: {
    color: '#E67E22',
  },
  serviceTypeButtonSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
  selectedServiceTypeButtonSubtitle: {
    color: '#E67E22',
    opacity: 0.8,
  },
});

export default BillDetailsScreen;
