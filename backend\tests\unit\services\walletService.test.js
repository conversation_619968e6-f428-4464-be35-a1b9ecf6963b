/**
 * Wallet Service Unit Tests
 * Comprehensive unit tests for wallet management functionality
 */

const walletService = require('../../../src/services/walletService');
const databaseService = require('../../../src/services/database');
const redisService = require('../../../src/services/redis');
const currencyService = require('../../../src/services/currencyService');
const auditService = require('../../../src/services/auditService');

// Mock dependencies
jest.mock('../../../src/services/database');
jest.mock('../../../src/services/redis');
jest.mock('../../../src/services/currencyService');
jest.mock('../../../src/services/auditService');

describe('WalletService', () => {
  let mockSupabase;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn(),
      rpc: jest.fn()
    };
    
    databaseService.getSupabase.mockReturnValue(mockSupabase);
    redisService.get.mockResolvedValue(null);
    redisService.set.mockResolvedValue(true);
    currencyService.validateAmount.mockReturnValue({ valid: true, amount: 10000 });
    auditService.logUserAction.mockResolvedValue(true);
  });

  describe('getWallet', () => {
    const userId = 'test-user-id';
    const mockWallet = {
      id: 'wallet-id',
      user_id: userId,
      balance: 100000,
      available_balance: 100000,
      currency: 'UGX',
      is_active: true
    };

    test('should return cached wallet if available', async () => {
      redisService.get.mockResolvedValue(mockWallet);
      
      const result = await walletService.getWallet(userId);
      
      expect(result).toEqual(mockWallet);
      expect(redisService.get).toHaveBeenCalledWith(`wallet:${userId}`);
      expect(mockSupabase.from).not.toHaveBeenCalled();
    });

    test('should fetch wallet from database if not cached', async () => {
      redisService.get.mockResolvedValue(null);
      mockSupabase.single.mockResolvedValue({ data: mockWallet, error: null });
      
      const result = await walletService.getWallet(userId);
      
      expect(result).toEqual(mockWallet);
      expect(mockSupabase.from).toHaveBeenCalledWith('wallets');
      expect(mockSupabase.select).toHaveBeenCalled();
      expect(mockSupabase.eq).toHaveBeenCalledWith('user_id', userId);
      expect(redisService.set).toHaveBeenCalledWith(`wallet:${userId}`, mockWallet, 1800);
    });

    test('should create wallet if not exists', async () => {
      redisService.get.mockResolvedValue(null);
      mockSupabase.single
        .mockResolvedValueOnce({ data: null, error: { code: 'PGRST116' } })
        .mockResolvedValueOnce({ data: mockWallet, error: null });
      
      const result = await walletService.getWallet(userId);
      
      expect(result).toEqual(mockWallet);
      expect(mockSupabase.insert).toHaveBeenCalled();
    });

    test('should handle database errors', async () => {
      redisService.get.mockResolvedValue(null);
      mockSupabase.single.mockResolvedValue({ 
        data: null, 
        error: { message: 'Database error' } 
      });
      
      await expect(walletService.getWallet(userId)).rejects.toThrow('Failed to retrieve wallet');
    });
  });

  describe('updateBalance', () => {
    const userId = 'test-user-id';
    const amount = 10000;
    const type = 'credit';
    const description = 'Test credit';
    const transactionId = 'txn-id';

    test('should update wallet balance successfully', async () => {
      const mockWallet = {
        id: 'wallet-id',
        balance: 100000,
        available_balance: 100000
      };
      
      mockSupabase.single.mockResolvedValue({ data: mockWallet, error: null });
      mockSupabase.rpc.mockResolvedValue({ data: { success: true }, error: null });
      
      const result = await walletService.updateBalance(userId, amount, type, description, transactionId);
      
      expect(result.success).toBe(true);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('update_wallet_balance', {
        p_user_id: userId,
        p_amount: amount,
        p_type: type,
        p_description: description,
        p_transaction_id: transactionId
      });
    });

    test('should validate amount before update', async () => {
      currencyService.validateAmount.mockReturnValue({ 
        valid: false, 
        error: 'Invalid amount' 
      });
      
      await expect(
        walletService.updateBalance(userId, -100, type, description)
      ).rejects.toThrow('Invalid amount');
    });

    test('should handle insufficient balance for debit', async () => {
      mockSupabase.rpc.mockResolvedValue({ 
        data: null, 
        error: { message: 'Insufficient balance' } 
      });
      
      await expect(
        walletService.updateBalance(userId, amount, 'debit', description)
      ).rejects.toThrow('Failed to update wallet balance');
    });
  });

  describe('transferMoney', () => {
    const fromUserId = 'from-user-id';
    const toUserId = 'to-user-id';
    const amount = 25000;
    const description = 'Test transfer';

    test('should transfer money successfully', async () => {
      const mockTransferResult = {
        transaction_id: 'txn-id',
        transfer_id: 'transfer-id',
        status: 'completed'
      };
      
      mockSupabase.rpc.mockResolvedValue({ 
        data: mockTransferResult, 
        error: null 
      });
      
      const result = await walletService.transferMoney(fromUserId, toUserId, amount, description);
      
      expect(result.success).toBe(true);
      expect(result.transferId).toBe('transfer-id');
      expect(mockSupabase.rpc).toHaveBeenCalledWith('transfer_money', {
        p_transfer_id: expect.any(String),
        p_from_user_id: fromUserId,
        p_to_user_id: toUserId,
        p_amount: amount,
        p_converted_amount: amount,
        p_exchange_rate: 1.0,
        p_description: description,
        p_from_currency: 'UGX',
        p_to_currency: 'UGX'
      });
    });

    test('should handle transfer to same user', async () => {
      await expect(
        walletService.transferMoney(fromUserId, fromUserId, amount, description)
      ).rejects.toThrow('Cannot transfer to self');
    });

    test('should handle invalid amount', async () => {
      currencyService.validateAmount.mockReturnValue({ 
        valid: false, 
        error: 'Amount too small' 
      });
      
      await expect(
        walletService.transferMoney(fromUserId, toUserId, 0, description)
      ).rejects.toThrow('Amount too small');
    });
  });

  describe('getTransactionHistory', () => {
    const userId = 'test-user-id';
    const options = { page: 1, limit: 20 };

    test('should return transaction history', async () => {
      const mockTransactions = [
        {
          id: 'txn-1',
          amount: 10000,
          type: 'transfer',
          status: 'completed',
          created_at: new Date().toISOString()
        }
      ];
      
      mockSupabase.single.mockResolvedValue({ 
        data: mockTransactions, 
        error: null 
      });
      
      const result = await walletService.getTransactionHistory(userId, options);
      
      expect(result.transactions).toEqual(mockTransactions);
      expect(result.pagination).toBeDefined();
    });

    test('should handle pagination correctly', async () => {
      const options = { page: 2, limit: 10 };
      
      mockSupabase.single.mockResolvedValue({ 
        data: [], 
        error: null 
      });
      
      await walletService.getTransactionHistory(userId, options);
      
      expect(mockSupabase.from).toHaveBeenCalledWith('transactions');
    });
  });

  describe('getWalletBalance', () => {
    const userId = 'test-user-id';

    test('should return formatted wallet balance', async () => {
      const mockWallet = {
        balance: 100000,
        available_balance: 95000,
        pending_balance: 5000,
        currency: 'UGX'
      };
      
      redisService.get.mockResolvedValue(mockWallet);
      currencyService.formatCurrency.mockReturnValue('UGX 100,000');
      
      const result = await walletService.getWalletBalance(userId);
      
      expect(result.balance).toBe(100000);
      expect(result.availableBalance).toBe(95000);
      expect(result.pendingBalance).toBe(5000);
      expect(result.currency).toBe('UGX');
      expect(result.formatted).toBe('UGX 100,000');
    });
  });

  describe('freezeWallet', () => {
    const userId = 'test-user-id';
    const reason = 'Suspicious activity';

    test('should freeze wallet successfully', async () => {
      mockSupabase.single.mockResolvedValue({ 
        data: { id: 'wallet-id' }, 
        error: null 
      });
      
      const result = await walletService.freezeWallet(userId, reason);
      
      expect(result.success).toBe(true);
      expect(mockSupabase.update).toHaveBeenCalledWith({
        is_active: false,
        frozen_reason: reason,
        frozen_at: expect.any(String),
        updated_at: expect.any(String)
      });
    });
  });

  describe('unfreezeWallet', () => {
    const userId = 'test-user-id';

    test('should unfreeze wallet successfully', async () => {
      mockSupabase.single.mockResolvedValue({ 
        data: { id: 'wallet-id' }, 
        error: null 
      });
      
      const result = await walletService.unfreezeWallet(userId);
      
      expect(result.success).toBe(true);
      expect(mockSupabase.update).toHaveBeenCalledWith({
        is_active: true,
        frozen_reason: null,
        frozen_at: null,
        updated_at: expect.any(String)
      });
    });
  });

  describe('getWalletLimits', () => {
    const userId = 'test-user-id';

    test('should return wallet limits and spending', async () => {
      const mockWallet = {
        daily_limit: 1000000,
        monthly_limit: 30000000
      };
      
      redisService.get.mockResolvedValue(mockWallet);
      mockSupabase.single.mockResolvedValue({ 
        data: { daily_spent: 50000, monthly_spent: 500000 }, 
        error: null 
      });
      
      const result = await walletService.getWalletLimits(userId);
      
      expect(result.dailyLimit).toBe(1000000);
      expect(result.monthlyLimit).toBe(30000000);
      expect(result.dailySpent).toBe(50000);
      expect(result.monthlySpent).toBe(500000);
      expect(result.dailyRemaining).toBe(950000);
      expect(result.monthlyRemaining).toBe(29500000);
    });
  });

  describe('Error Handling', () => {
    test('should handle Redis connection errors gracefully', async () => {
      redisService.get.mockRejectedValue(new Error('Redis connection failed'));
      mockSupabase.single.mockResolvedValue({ 
        data: { id: 'wallet-id' }, 
        error: null 
      });
      
      const result = await walletService.getWallet('user-id');
      
      expect(result).toBeDefined();
      expect(mockSupabase.from).toHaveBeenCalled();
    });

    test('should handle database connection errors', async () => {
      databaseService.getSupabase.mockImplementation(() => {
        throw new Error('Database connection failed');
      });
      
      await expect(walletService.getWallet('user-id')).rejects.toThrow();
    });
  });

  describe('Performance Tests', () => {
    test('should complete wallet operations within acceptable time', async () => {
      const startTime = Date.now();
      
      mockSupabase.single.mockResolvedValue({ 
        data: { id: 'wallet-id' }, 
        error: null 
      });
      
      await walletService.getWallet('user-id');
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});
