import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
  Animated,
  Alert,
  Image,
} from 'react-native';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import walletService from '../services/walletService';
import profileManagementService from '../services/profileManagementService';
import {
  WalletCardSkeleton,
  QuickActionsSkeleton,
  TransactionsSkeleton
} from '../components/SkeletonLoader';
import TransactionDetailModal from '../components/TransactionDetailModal';
import WalletCard from '../components/WalletCard';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import { useLanguage } from '../contexts/LanguageContext';
import { getDashboardGreeting, useGreetingUpdater } from '../utils/greetingUtils';

const { width, height } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [walletData, setWalletData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [greeting, setGreeting] = useState('Good Morning, Friend!');
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [financialInsights, setFinancialInsights] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [dataLoaded, setDataLoaded] = useState(false);

  // Safe theme, currency, and language access with fallback
  const themeContext = useTheme();
  const { userCurrency } = useCurrencyContext();
  const { t } = useLanguage();
  const theme = themeContext?.theme || {
    colors: {
      primary: '#E67E22',
      background: '#fcf7f0',
      text: '#2C3E50',
      surface: '#fcf7f0',
      border: '#BDC3C7',
      success: '#27AE60',
      warning: '#F39C12',
      error: '#C0392B'
    }
  };
  const styles = createStyles(theme);

  // Transaction Detail Modal State
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [transactionModalVisible, setTransactionModalVisible] = useState(false);

  // Auto-scroll promotional banners state
  const promotionScrollRef = useRef(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const autoScrollTimer = useRef(null);
  const scrollPosition = useRef(0);

  useEffect(() => {
    initializeDashboard();
    startAnimations();
  }, []);

  useEffect(() => {
    if (user) {
      updateGreeting();
    }
  }, [user]);

  // Update greeting when userProfile changes
  useEffect(() => {
    if (user) {
      updateGreeting();
    }
  }, [userProfile]);



  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const initializeDashboard = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadUserData(),
        loadWalletData(),
        loadRecentTransactions(),
        loadFinancialInsights(),
      ]);
      setDataLoaded(true);
    } catch (error) {
      console.error('❌ Error initializing dashboard:', error);
      Alert.alert(t('common.errorTitle'), t('dashboard.alerts.errorLoadingData'));
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await initializeDashboard();
    setRefreshing(false);
  }, []);

  const updateGreeting = async () => {
    try {
      console.log('🎯 Dashboard: Updating greeting with user data:', {
        hasUser: !!user,
        userId: user?.id,
        hasUserProfile: !!userProfile,
        userProfileName: userProfile?.full_name,
        userMetadata: user?.user_metadata,
        userEmail: user?.email
      });

      // Use centralized greeting utility with three-period system
      const dashboardGreeting = await getDashboardGreeting(user, userProfile, false, t);
      setGreeting(dashboardGreeting);

      console.log('✅ Dashboard greeting updated:', dashboardGreeting);
      console.log('🎨 Dashboard greeting state set to:', dashboardGreeting);
    } catch (error) {
      console.error('❌ Error updating dashboard greeting:', error);
      // Fallback to basic greeting
      setGreeting(t('greetings.goodMorning') + ', ' + t('common.friend') + '!');
    }
  };

  const loadUserData = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        console.log('✅ User loaded for dashboard:', {
          id: currentUser.id,
          email: currentUser.email,
          metadata: currentUser.user_metadata
        });

        // Load user profile data for greeting and profile picture
        try {
          console.log('🔍 Dashboard: Loading user profile for greeting...');
          console.log('🔍 Dashboard: Current user details:', {
            id: currentUser.id,
            email: currentUser.email,
            phone: currentUser.phone,
            metadata: currentUser.user_metadata
          });

          const profileResult = await profileManagementService.getProfile(currentUser.id);
          console.log('🔍 Dashboard: Profile result:', {
            success: profileResult.success,
            hasData: !!profileResult.data,
            error: profileResult.error,
            code: profileResult.code
          });

          if (profileResult.success && profileResult.data) {
            setUserProfile(profileResult.data);
            console.log('✅ User profile loaded for dashboard:', {
              fullName: profileResult.data.full_name,
              phone: profileResult.data.phone,
              email: profileResult.data.email,
              avatarUrl: profileResult.data.avatar_url
            });

            // Force update greeting with the loaded profile data
            console.log('🔄 Dashboard: Forcing greeting update with loaded profile...');
            setTimeout(() => {
              updateGreeting();
            }, 100); // Small delay to ensure state is updated
          } else {
            console.log('⚠️ No profile data found, checking user metadata for name...');
            // If no profile data, check if user metadata has name info
            if (currentUser.user_metadata?.full_name || currentUser.user_metadata?.name) {
              console.log('✅ Found name in user metadata:', currentUser.user_metadata);

              // Create a temporary profile object from user metadata for greeting
              const tempProfile = {
                full_name: currentUser.user_metadata.full_name || currentUser.user_metadata.name,
                phone_number: currentUser.phone,
                email: currentUser.email
              };
              setUserProfile(tempProfile);
              console.log('✅ Created temporary profile from metadata for greeting');

              // Force update greeting with metadata
              setTimeout(() => {
                updateGreeting();
              }, 100);
            } else {
              console.log('⚠️ No name found in user metadata either');
            }
          }
        } catch (profileError) {
          console.log('⚠️ Could not load user profile for dashboard:', profileError);
          // Still try to use user metadata for greeting
          if (currentUser.user_metadata?.full_name || currentUser.user_metadata?.name) {
            console.log('✅ Fallback: Using name from user metadata for greeting');

            // Create a temporary profile object from user metadata for greeting
            const tempProfile = {
              full_name: currentUser.user_metadata.full_name || currentUser.user_metadata.name,
              phone_number: currentUser.phone,
              email: currentUser.email
            };
            setUserProfile(tempProfile);
            console.log('✅ Created fallback profile from metadata for greeting');

            // Force update greeting with metadata
            setTimeout(() => {
              updateGreeting();
            }, 100);
          }
        }
      } else {
        console.log('⚠️ No current user found for dashboard');
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error);
    }
  };

  const loadWalletData = async () => {
    try {
      if (!user?.id) {
        console.log('⚠️ No user ID available for wallet data');
        setWalletData({ balance: 0, currency: 'UGX' });
        return;
      }

      const wallet = await walletService.getWalletBalance(user.id);
      if (wallet.success) {
        setWalletData({ balance: wallet.balance, currency: wallet.currency });
        console.log('✅ Wallet loaded:', { balance: wallet.balance, currency: wallet.currency });
      } else {
        console.log('⚠️ Wallet not found, will create default');
        setWalletData({ balance: 0, currency: 'UGX' });
      }
    } catch (error) {
      console.error('❌ Error loading wallet data:', error);
      setWalletData({ balance: 0, currency: 'UGX' });
    }
  };

  const loadRecentTransactions = async () => {
    try {
      if (!user?.id) {
        console.log('⚠️ No user ID available for transactions');
        return;
      }

      // Fix parameter order: (limit, userId) not (userId, limit)
      const transactions = await walletService.getRecentTransactions(5, user.id);
      if (transactions.success && transactions.data) {
        setRecentTransactions(transactions.data);
      } else {
        // Handle case where no transactions are returned
        setRecentTransactions([]);
      }
    } catch (error) {
      console.error('❌ Error loading transactions:', error);
      // Set empty array to prevent map errors
      setRecentTransactions([]);
    }
  };

  const loadFinancialInsights = async () => {
    try {
      if (!user?.id) {
        console.log('⚠️ No user ID available for insights');
        return;
      }

      console.log('📊 Dashboard: Loading financial insights...');
      const insights = await walletService.getFinancialInsights(user.id);
      console.log('📊 Dashboard: Insights result:', insights);

      if (insights.success) {
        console.log('📊 Dashboard: Setting insights data:', insights.insights);
        setFinancialInsights(insights.insights);
      } else {
        console.log('📊 Dashboard: Insights failed, using defaults:', insights.error);
        // Set default insights for new users
        setFinancialInsights({
          totalSpent: 0,
          totalReceived: 0,
          transactionCount: 0,
          topSpendingCategory: null,
          monthlyTrend: { trend: 'stable', percentageChange: 0 }
        });
      }
    } catch (error) {
      console.error('❌ Error loading insights:', error);
      // Set default insights on error
      setFinancialInsights({
        totalSpent: 0,
        totalReceived: 0,
        transactionCount: 0,
        topSpendingCategory: null,
        monthlyTrend: { trend: 'stable', percentageChange: 0 }
      });
    }
  };

  const handleViewAllTransactions = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('TransactionHistory');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('dashboard.alerts.transactionHistory'), t('dashboard.alerts.transactionHistoryDesc'));
    }
  };

  const handleViewAllInsights = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('Analytics');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('dashboard.alerts.financialInsights'), t('dashboard.alerts.financialInsightsDesc'));

      // Fallback navigation attempt
      setTimeout(() => {
        try {
          navigation.navigate('Analytics');
        } catch (fallbackError) {
          console.error('Fallback navigation failed:', fallbackError);
        }
      }, 1000);
    }
  };

  const handleTransactionPress = (transaction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedTransaction(transaction);
    setTransactionModalVisible(true);
  };

  const handleCloseTransactionModal = () => {
    setTransactionModalVisible(false);
    setSelectedTransaction(null);
  };

  const handleViewAllQuickActions = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    try {
      // Navigate to Bills tab in bottom navigation
      if (navigation?.navigateToTab) {
        navigation.navigateToTab('bills');
      } else if (navigation && navigation.navigate) {
        navigation.navigate('Bills');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(t('dashboard.alerts.billPayments'), t('dashboard.alerts.billPaymentsDesc'));
    }
  };

  const handleQuickAction = (action) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Quick action pressed:', action);

    // Handle send money navigation
    if (action === 'send_money') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('SendMoney');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.sendMoney'), t('dashboard.alerts.sendMoneyDesc'));
      }
      return;
    }

    // Handle QR code navigation
    if (action === 'qr_pay') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

        // Navigate to QR tab in bottom navigation
        if (navigation?.navigateToTab) {
          navigation.navigateToTab('qr');
        } else {
          // Fallback: navigate directly to QR scanner
          navigation.navigate('QRScanner', { type: 'payment' });
        }
      } catch (error) {
        console.log('⚠️ QR navigation error:', error);
        Alert.alert(t('dashboard.alerts.qrPay'), t('dashboard.alerts.qrPayDesc'));

        // Fallback navigation attempt
        setTimeout(() => {
          try {
            navigation.navigate('QRScanner', { type: 'payment' });
          } catch (fallbackError) {
            console.error('Fallback QR navigation failed:', fallbackError);
          }
        }, 1000);
      }
      return;
    }

    // Handle bill payment navigation - direct to bill payment screen
    if (action === 'pay_bills') {
      try {
        // Navigate directly to BillPayment screen for immediate access
        if (navigation && navigation.navigate) {
          navigation.navigate('BillPayment');
        } else if (navigation?.navigateToTab) {
          navigation.navigateToTab('bills');
        }
        return;
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.payBills'), t('dashboard.alerts.payBillsDesc'));
      }
      return;
    }

    // Handle savings dashboard navigation
    if (action === 'savings_dashboard') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('SavingsDashboard');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.savings'), t('dashboard.alerts.savingsDesc'));
      }
      return;
    }

    // Handle investments navigation
    if (action === 'investments') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('InvestmentDashboard');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert('Investments', 'Investment features are now available!');
      }
      return;
    }

    // Handle financial planning navigation
    if (action === 'financial_planning') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('FinancialPlanningDashboard');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert('Financial Planning', 'Financial planning tools are now available!');
      }
      return;
    }

    // Handle test screen navigation
    if (action === 'savings_test') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('SavingsTest');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert('Test Features', 'Test screen navigation failed');
      }
      return;
    }

    // Handle legacy savings navigation (keep for backward compatibility)
    if (action === 'savings') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('Savings');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.savings'), t('dashboard.alerts.savingsDesc'));
      }
      return;
    }

    // Handle buy data bundles navigation - direct to mobile providers with data pre-selected
    if (action === 'buy_data') {
      try {
        // Navigate directly to BillPayment with mobile category and data service pre-selected
        if (navigation && navigation.navigate) {
          navigation.navigate('BillPayment', {
            autoSelectCategory: 'mobile',
            autoSelectService: 'data'
          });
        } else if (navigation?.navigateToTab) {
          navigation.navigateToTab('bills');
        }
        return;
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.buyDataBundles'), t('dashboard.alerts.buyDataBundlesDesc'));
      }
      return;
    }

    // Handle buy airtime navigation - direct to mobile providers with airtime pre-selected
    if (action === 'buy_airtime') {
      try {
        // Navigate directly to BillPayment with mobile category and airtime service pre-selected
        if (navigation && navigation.navigate) {
          navigation.navigate('BillPayment', {
            autoSelectCategory: 'mobile',
            autoSelectService: 'airtime'
          });
        } else if (navigation?.navigateToTab) {
          navigation.navigateToTab('bills');
        }
        return;
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.buyAirtime'), t('dashboard.alerts.buyAirtimeDesc'));
      }
      return;
    }

    // Handle request money navigation
    if (action === 'request_money') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('RequestMoney');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.requestMoney'), t('dashboard.alerts.requestMoneyDesc'));
      }
      return;
    }

    // Handle budget insights navigation
    if (action === 'budget_insights') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('BudgetInsights');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.budgetInsights'), t('dashboard.alerts.budgetInsightsDesc'));
      }
      return;
    }

    // Handle transaction history navigation
    if (action === 'history') {
      try {
        if (navigation && navigation.navigate) {
          navigation.navigate('TransactionHistory');
          return;
        } else {
          throw new Error('Navigation not available');
        }
      } catch (error) {
        console.log('⚠️ Navigation error:', error);
        Alert.alert(t('dashboard.alerts.transactionHistory'), t('dashboard.alerts.transactionHistoryAvailable'));
      }
      return;
    }

    // Map action IDs to user-friendly names
    const actionNames = {
      'buy_airtime': 'Buy Airtime',
      'buy_data': 'Buy Data Bundles',
      'pay_bills': 'Pay Bills',
      'send_money': 'Send Money',
      'mobile_money': 'Mobile Money',
      'qr_pay': 'QR Pay',
      'savings': 'Savings & Investment',
      'loans': 'Loans & Credit',
      'history': 'Transaction History'
    };

    const actionName = actionNames[action] || action;

    // For now, show coming soon alerts with specific feature descriptions
    const descriptions = {
      'buy_airtime': 'Purchase airtime for MTN, Airtel, and UTL networks',
      'buy_data': 'Buy internet data bundles for all major carriers',
      'pay_bills': 'Pay electricity, water, and TV subscription bills',
      'send_money': 'Transfer money to friends and family instantly',
      'mobile_money': 'Deposit and withdraw from mobile money accounts',
      'qr_pay': 'Scan QR codes to pay merchants and businesses',
      'savings': 'Start saving and invest in mutual funds',
      'loans': 'Apply for quick loans and credit facilities',
      'history': 'View detailed transaction history with filters and export options'
    };

    Alert.alert(
      actionName,
      descriptions[action] || t('common.thisFeatureWillBeAvailable'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('common.notifyMe'), onPress: () => console.log(`User interested in ${actionName}`) }
      ]
    );
  };

  const handleTopUp = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('TopUp');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error, showing top-up options:', error);
      Alert.alert(
        t('dashboard.alerts.topUpWallet'),
        t('dashboard.alerts.topUpWalletDesc'),
        [
          { text: t('common.cancel'), style: 'cancel' },
          {
            text: t('dashboard.alerts.mobileMoneyOption'),
            onPress: () => {
              Alert.alert(
                t('dashboard.alerts.mobileMoneyTopUp'),
                t('dashboard.alerts.mobileMoneyTopUpDesc')
              );
            }
          },
          {
            text: t('dashboard.alerts.bankTransferOption'),
            onPress: () => {
              Alert.alert(
                t('dashboard.alerts.bankTransfer'),
                t('dashboard.alerts.bankTransferDesc')
              );
            }
          }
        ]
      );
    }
  };

  const toggleBalanceVisibility = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setBalanceVisible(!balanceVisible);
  };

  const refreshBalance = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    await loadWalletData();
  };

  // Handler functions for modernized WalletCard
  const handleWalletSend = () => {
    handleQuickAction('send_money');
  };

  const handleWalletQRPay = () => {
    handleQuickAction('qr_pay');
  };

  const handleWalletHistory = () => {
    handleQuickAction('history');
  };

  // Quick Actions Configuration - Airtel Style (2x4 Grid - 8 Actions Only)
  // Using dynamic translations for professional multilingual experience
  const quickActions = [
    {
      id: 'buy_data',
      title: t('dashboard.quickActions.buyBundles'),
      icon: 'layers-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'send_money',
      title: t('dashboard.quickActions.sendMoney'),
      icon: 'arrow-up-circle-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'pay_bills',
      title: t('dashboard.quickActions.payBills'),
      icon: 'receipt-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'budget_insights',
      title: t('dashboard.quickActions.budgetInsights'),
      icon: 'analytics-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'savings_dashboard',
      title: t('dashboard.quickActions.savingsDashboard'),
      icon: 'wallet-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'request_money',
      title: t('dashboard.quickActions.requestMoney'),
      icon: 'arrow-down-circle-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'qr_pay',
      title: t('dashboard.quickActions.scanPay'),
      icon: 'scan-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'investments',
      title: t('dashboard.quickActions.investments'),
      icon: 'trending-up-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'financial_planning',
      title: t('dashboard.quickActions.planning'),
      icon: 'calculator-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'savings_test',
      title: 'Test New Features',
      icon: 'flask-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'buy_airtime',
      title: t('dashboard.quickActions.buyAirtime'),
      icon: 'phone-portrait-outline',
      iconType: 'Ionicons',
    },
  ];

  // Promotional Banners Configuration
  const promotionalBanners = [
    {
      id: 'school_fees',
      title: t('dashboard.promotions.schoolFees'),
      subtitle: '',
      prize: { amount: 'UGX\n100k', winners: '100\nWINNERS\nEVERY WEEK' },
      colors: ['#E53E3E', '#C53030'],
      emoji: '👩‍🎓',
      type: 'school'
    },
    {
      id: 'double_data',
      title: t('dashboard.promotions.doubleData'),
      subtitle: t('dashboard.promotions.doubleDataSubtitle'),
      colors: [theme.colors.primary, theme.colors.primaryDark],
      emoji: '📱',
      type: 'data'
    },
    {
      id: 'cashback',
      title: t('dashboard.promotions.cashback'),
      subtitle: t('dashboard.promotions.cashbackSubtitle'),
      colors: [Colors.secondary.savanna, Colors.accent.gold],
      emoji: '💰',
      type: 'cashback'
    }
  ];

  // Auto-scroll functionality for promotional banners
  const startAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }

    autoScrollTimer.current = setInterval(() => {
      if (!isUserScrolling && promotionScrollRef.current) {
        const bannerWidth = 280 + 16; // banner width + margin
        const totalWidth = bannerWidth * promotionalBanners.length;

        scrollPosition.current += bannerWidth;

        // Reset to beginning when reaching the end
        if (scrollPosition.current >= totalWidth) {
          scrollPosition.current = 0;
        }

        promotionScrollRef.current.scrollTo({
          x: scrollPosition.current,
          animated: true
        });
      }
    }, 3000); // 3 seconds per banner
  }, [isUserScrolling, promotionalBanners.length]);

  const stopAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
  }, []);

  const handleScrollBegin = () => {
    setIsUserScrolling(true);
    stopAutoScroll();
  };

  const handleScrollEnd = (event) => {
    setIsUserScrolling(false);
    scrollPosition.current = event.nativeEvent.contentOffset.x;

    // Resume auto-scroll after 2 seconds of user inactivity
    setTimeout(() => {
      if (!isUserScrolling) {
        startAutoScroll();
      }
    }, 2000);
  };

  // Start auto-scroll when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startAutoScroll();
    }, 1000); // Start after 1 second

    return () => {
      clearTimeout(timer);
      stopAutoScroll();
    };
  }, [startAutoScroll, stopAutoScroll]);

  // Render Icon Component
  const renderIcon = (iconName, iconType, size = 24, color = theme.colors.primary) => {
    switch (iconType) {
      case 'MaterialIcons':
        return <MaterialIcons name={iconName} size={size} color={color} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={iconName} size={size} color={color} />;
      default:
        return <Ionicons name={iconName} size={size} color={color} />;
    }
  };

  if (loading) {
    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Skeleton */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={[styles.skeletonBox, { width: 200, height: 28, marginBottom: 8 }]} />
            <View style={[styles.skeletonBox, { width: 150, height: 16 }]} />
          </View>
          <View style={styles.headerRight}>
            <View style={[styles.skeletonBox, { width: 40, height: 40, borderRadius: 20 }]} />
          </View>
        </View>

        <View style={styles.dashboard}>
          <WalletCardSkeleton />
          <QuickActionsSkeleton />
          <TransactionsSkeleton />
        </View>
      </ScrollView>
    );
  }

  return (
    <View style={styles.container}>
      {/* Static Header */}
      <View style={styles.staticHeader}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting} numberOfLines={2} adjustsFontSizeToFit={true}>{greeting}</Text>
          <Text style={styles.subtitle}>{t('dashboard.welcome')} JiraniPay</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => {
              try {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                console.log('🔔 Navigating to Notifications screen...');
                navigation.navigate('Notifications');
              } catch (error) {
                console.error('❌ Error navigating to Notifications:', error);
                Alert.alert(
                  t('dashboard.notifications'),
                  t('dashboard.notificationsError'),
                  [{ text: t('common.ok'), style: 'default' }]
                );
              }
            }}
          >
            <Ionicons name="notifications-outline" size={24} color={theme.colors.text} />
            <View style={styles.notificationBadge} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => {
              try {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                console.log('👤 Navigating to Profile tab...');
                navigation.navigateToTab('profile');
              } catch (error) {
                console.error('❌ Error navigating to Profile:', error);
                Alert.alert(
                  t('navigation.profile'),
                  t('profile.profileError'),
                  [{ text: t('common.ok'), style: 'default' }]
                );
              }
            }}
          >
            {userProfile?.avatar_url ? (
              <View style={styles.profileImageContainer}>
                <Image
                  source={{ uri: userProfile.avatar_url }}
                  style={styles.profileImage}
                  onLoad={() => console.log('🖼️ Dashboard profile image loaded successfully')}
                  onError={(error) => {
                    console.log('❌ Dashboard profile image load error:', error);
                    // Fallback will be handled by the conditional rendering below
                  }}
                />
              </View>
            ) : (
              <Ionicons name="person-circle-outline" size={28} color={theme.colors.text} />
            )}
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >

        {/* Dashboard Content */}
        <View style={styles.dashboard}>
          {/* Modernized Wallet Card Component */}
          <WalletCard
            balance={walletData?.balance || 0}
            currency={userCurrency || 'UGX'}
            accountNumber={user?.phone_number || 'JP123456789'}
            onTopUp={handleTopUp}
            onSend={handleWalletSend}
            onQRPay={handleWalletQRPay}
            onHistory={handleWalletHistory}
            onRefresh={refreshBalance}
            hideBalance={!balanceVisible}
            loading={loading || refreshing}
          />

        {/* Airtel-Style Quick Actions Grid */}
        <View style={styles.quickActions}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.quickActionsTitle')}</Text>
            <TouchableOpacity onPress={handleViewAllQuickActions}>
              <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.actionsGrid}>
            {/* First Row - 4 items */}
            <View style={styles.actionRow}>
              {quickActions.slice(0, 4).map((action, index) => (
                <TouchableOpacity
                  key={action.id}
                  style={styles.airtelActionCard}
                  onPress={() => handleQuickAction(action.id)}
                  activeOpacity={0.7}
                >
                  <View style={styles.airtelIconContainer}>
                    {renderIcon(action.icon, action.iconType, 28, Colors.neutral.charcoal)}
                  </View>
                  <Text style={styles.airtelActionText}>{action.title}</Text>
                </TouchableOpacity>
              ))}
            </View>
            {/* Second Row - 4 items */}
            <View style={styles.actionRow}>
              {quickActions.slice(4, 8).map((action, index) => (
                <TouchableOpacity
                  key={action.id}
                  style={styles.airtelActionCard}
                  onPress={() => handleQuickAction(action.id)}
                  activeOpacity={0.7}
                >
                  <View style={styles.airtelIconContainer}>
                    {renderIcon(action.icon, action.iconType, 28, Colors.neutral.charcoal)}
                  </View>
                  <Text style={styles.airtelActionText}>{action.title}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Auto-Scrolling Promotional Banners - Airtel Style */}
        <View style={styles.promotionsSection}>
          <ScrollView
            ref={promotionScrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promotionsContainer}
            onScrollBeginDrag={handleScrollBegin}
            onScrollEndDrag={handleScrollEnd}
            onMomentumScrollEnd={handleScrollEnd}
            decelerationRate="fast"
          >
            {promotionalBanners.map((banner, index) => (
              <View key={banner.id} style={styles.promotionBanner}>
                <LinearGradient
                  colors={banner.colors}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.promotionGradient}
                >
                  <View style={styles.promotionContent}>
                    <View style={styles.promotionLeft}>
                      <Text style={styles.promotionTitle}>{banner.title}</Text>
                      {banner.subtitle && (
                        <Text style={styles.promotionSubtitle}>{banner.subtitle}</Text>
                      )}
                      {banner.prize && (
                        <View style={styles.promotionPrize}>
                          <Text style={styles.prizeAmount}>{banner.prize.amount}</Text>
                          <Text style={styles.prizeWinners}>{banner.prize.winners}</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.promotionRight}>
                      {banner.type === 'school' ? (
                        <View style={styles.studentImageContainer}>
                          <Text style={styles.studentEmoji}>{banner.emoji}</Text>
                        </View>
                      ) : (
                        <Text style={banner.type === 'data' ? styles.dataEmoji : styles.cashbackEmoji}>
                          {banner.emoji}
                        </Text>
                      )}
                    </View>
                  </View>
                </LinearGradient>
              </View>
            ))}
          </ScrollView>
        </View>

        {/* AI Financial Insights */}
        <View style={styles.insightsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.financialInsights')}</Text>
            <View style={styles.sectionHeaderActions}>
              <TouchableOpacity
                style={styles.enhancedAnalyticsButton}
                onPress={() => navigation.navigate('EnhancedDashboard')}
              >
                <Ionicons name="analytics" size={16} color={Colors.primary.main} />
                <Text style={styles.enhancedAnalyticsText}>Enhanced</Text>
              </TouchableOpacity>
              <TouchableOpacity onPress={handleViewAllInsights}>
                <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
              </TouchableOpacity>
            </View>
          </View>

          {financialInsights && !financialInsights.isEmpty ? (
            <View style={styles.insightsGrid}>
              <View style={styles.insightCard}>
                <View style={styles.insightHeader}>
                  <Ionicons name="trending-up" size={20} color={Colors.secondary.savanna} />
                  <Text style={styles.insightTitle}>{t('dashboard.alerts.monthlySpending')}</Text>
                </View>
                <Text style={styles.insightValue}>
                  UGX {(financialInsights.totalSpent || 0).toLocaleString('en-UG')}
                </Text>
                <Text style={styles.insightSubtext}>
                  {financialInsights.monthlyTrend?.trend === 'up' ? '↗️' : '↘️'}
                  {Math.abs(financialInsights.monthlyTrend?.percentageChange || 0).toFixed(1)}% vs last month
                </Text>
              </View>

              <View style={styles.insightCard}>
                <View style={styles.insightHeader}>
                  <Ionicons name="pie-chart" size={20} color={Colors.accent.coral} />
                  <Text style={styles.insightTitle}>{t('dashboard.topCategory')}</Text>
                </View>
                <Text style={styles.insightValue}>
                  {financialInsights.topSpendingCategory?.name || t('common.notAvailable')}
                </Text>
                <Text style={styles.insightSubtext}>
                  UGX {(financialInsights.topSpendingCategory?.amount || 0).toLocaleString('en-UG')}
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.insightsPlaceholder}>
              <Ionicons name="analytics-outline" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.placeholderText}>{t('dashboard.startUsing')}</Text>
              <Text style={styles.placeholderSubtext}>{t('dashboard.makeTransactions')}</Text>
            </View>
          )}
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>{t('dashboard.recentTransactions')}</Text>
            <TouchableOpacity onPress={handleViewAllTransactions}>
              <Text style={styles.viewAllText}>{t('dashboard.viewAll')}</Text>
            </TouchableOpacity>
          </View>

          {recentTransactions.length > 0 ? (
            <View style={styles.transactionsList}>
              {recentTransactions.slice(0, 5).map((transaction, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.transactionItem}
                  onPress={() => handleTransactionPress(transaction)}
                  activeOpacity={0.7}
                >
                  <View style={styles.transactionIcon}>
                    <Ionicons
                      name={getTransactionIcon(transaction.transaction_type)}
                      size={20}
                      color={theme.colors.primary}
                    />
                  </View>
                  <View style={styles.transactionDetails}>
                    <Text style={styles.transactionTitle}>
                      {getTransactionTitle(transaction.transaction_type)}
                    </Text>
                    <Text style={styles.transactionDate}>
                      {new Date(transaction.created_at).toLocaleDateString()}
                    </Text>
                  </View>
                  <Text style={[
                    styles.transactionAmount,
                    { color: transaction.transaction_type === 'deposit'
                      ? Colors.secondary.savanna
                      : Colors.secondary.heritage
                    }
                  ]}>
                    {transaction.transaction_type === 'deposit' ? '+' : '-'}
                    UGX {parseFloat(transaction.amount).toLocaleString('en-UG')}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          ) : (
            <View style={styles.transactionsPlaceholder}>
              <Ionicons name="receipt-outline" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.placeholderText}>{t('dashboard.noTransactions')}</Text>
              <Text style={styles.placeholderSubtext}>{t('dashboard.transactionsWillAppear')}</Text>
            </View>
          )}
        </View>
        </View>
      </ScrollView>

      {/* Transaction Detail Modal */}
      <TransactionDetailModal
        visible={transactionModalVisible}
        onClose={handleCloseTransactionModal}
        transaction={selectedTransaction}
      />
    </View>
  );
};

// Helper functions for transaction display
const getTransactionIcon = (type) => {
  switch (type) {
    case 'airtime': return 'phone-portrait';
    case 'bill_payment': return 'receipt';
    case 'transfer': return 'send';
    case 'deposit': return 'add-circle';
    default: return 'swap-horizontal';
  }
};

const getTransactionTitle = (type) => {
  switch (type) {
    case 'airtime': return 'Airtime Purchase';
    case 'bill_payment': return 'Bill Payment';
    case 'transfer': return 'Money Transfer';
    case 'deposit': return 'Wallet Top-up';
    default: return 'Transaction';
  }
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  // Static Header Styles
  staticHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    zIndex: 1000,
  },
  scrollContainer: {
    flex: 1,
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
    flexWrap: 'wrap',
    flexShrink: 1,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 16,
    padding: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.secondary.heritage,
  },
  dashboard: {
    padding: 20,
    paddingBottom: 120,
  },


  // Quick Actions Styles
  quickActions: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  enhancedAnalyticsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary.main + '15',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  enhancedAnalyticsText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary.main,
  },
  viewAllText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  actionsGrid: {
    gap: 12,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  actionCard: {
    width: (width - 72) / 4, // 4 columns with proper spacing
    backgroundColor: theme.colors.surface,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: 16,
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
    lineHeight: 16,
  },

  // Airtel-Style Action Cards (2x4 Grid)
  airtelActionCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 85,
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  airtelIconContainer: {
    marginBottom: 8,
  },
  airtelActionText: {
    fontSize: 11,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'center',
    lineHeight: 14,
  },

  // Promotional Banners Styles
  promotionsSection: {
    marginBottom: 32,
  },
  promotionsContainer: {
    paddingRight: 20,
  },
  promotionBanner: {
    width: 280,
    height: 120,
    marginRight: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  promotionGradient: {
    flex: 1,
    padding: 16,
  },
  promotionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  promotionLeft: {
    flex: 1,
  },
  promotionTitle: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 20,
    marginBottom: 4,
  },
  promotionSubtitle: {
    color: Colors.neutral.white,
    fontSize: 12,
    opacity: 0.9,
  },
  promotionPrize: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  prizeAmount: {
    color: Colors.neutral.white,
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 12,
    lineHeight: 16,
  },
  prizeWinners: {
    color: Colors.neutral.white,
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 12,
  },
  promotionRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  studentImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  studentEmoji: {
    fontSize: 32,
  },
  dataEmoji: {
    fontSize: 40,
  },
  cashbackEmoji: {
    fontSize: 40,
  },
  // Financial Insights Styles
  insightsSection: {
    marginBottom: 32,
  },
  insightsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  insightCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  insightValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  insightSubtext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  insightsPlaceholder: {
    backgroundColor: theme.colors.card,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  // Transactions Styles
  transactionsSection: {
    marginBottom: 32,
  },
  transactionsList: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary.light + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionsPlaceholder: {
    backgroundColor: theme.colors.card,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  placeholderText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    marginTop: 12,
    marginBottom: 4,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },

  // Skeleton Loading Styles
  skeletonBox: {
    backgroundColor: theme.colors.border,
    borderRadius: 8,
  },

  // Profile Image Styles
  profileImageContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  profileImage: {
    width: '100%',
    height: '100%',
    borderRadius: 14,
  },
});

export default DashboardScreen;
