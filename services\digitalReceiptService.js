/**
 * Digital Receipt Service
 * Comprehensive receipt generation system with QR codes, transaction IDs,
 * and proper formatting for different transaction types
 */

import { supabase } from './supabaseClient';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime, formatDate } from '../utils/dateUtils';

// Receipt types and their configurations
const RECEIPT_TYPES = {
  money_transfer: {
    title: 'Money Transfer Receipt',
    icon: '💸',
    fields: ['sender', 'recipient', 'amount', 'fee', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'amount', 'recipient']
  },
  money_received: {
    title: 'Money Received Receipt',
    icon: '💰',
    fields: ['sender', 'amount', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'amount', 'sender']
  },
  bill_payment: {
    title: 'Bill Payment Receipt',
    icon: '🧾',
    fields: ['biller', 'account_number', 'amount', 'fee', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'biller', 'amount']
  },
  airtime_purchase: {
    title: 'Airtime Purchase Receipt',
    icon: '📱',
    fields: ['network', 'phone_number', 'amount', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'phone_number', 'amount']
  },
  withdrawal: {
    title: 'Cash Withdrawal Receipt',
    icon: '🏧',
    fields: ['amount', 'fee', 'location', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'amount', 'location']
  },
  deposit: {
    title: 'Cash Deposit Receipt',
    icon: '💳',
    fields: ['amount', 'fee', 'location', 'reference', 'timestamp'],
    qrCodeData: ['reference', 'amount', 'location']
  }
};

class DigitalReceiptService {
  constructor() {
    this.receiptTypes = RECEIPT_TYPES;
  }

  /**
   * Generate digital receipt for transaction
   */
  async generateReceipt(transactionId, userId) {
    try {
      console.log('🧾 Generating digital receipt:', { transactionId, userId });

      // Get transaction details
      const transaction = await this.getTransactionDetails(transactionId, userId);
      
      // Get receipt configuration
      const receiptConfig = this.receiptTypes[transaction.type] || 
                           this.receiptTypes.money_transfer;

      // Generate receipt data
      const receiptData = await this.buildReceiptData(transaction, receiptConfig);

      // Generate QR code data
      const qrCodeData = this.generateQRCodeData(transaction, receiptConfig);

      // Create receipt record
      const receipt = await this.createReceiptRecord(
        transactionId, 
        userId, 
        receiptData, 
        qrCodeData
      );

      console.log('✅ Digital receipt generated:', receipt.id);

      return {
        success: true,
        receipt: {
          id: receipt.id,
          type: transaction.type,
          data: receiptData,
          qrCode: qrCodeData,
          downloadUrl: this.generateDownloadUrl(receipt.id),
          shareUrl: this.generateShareUrl(receipt.id)
        }
      };
    } catch (error) {
      console.error('❌ Error generating receipt:', error);
      throw error;
    }
  }

  /**
   * Get transaction details from database
   */
  async getTransactionDetails(transactionId, userId) {
    try {
      const { data: transaction, error } = await supabase
        .from('transactions')
        .select(`
          *,
          sender:sender_id(full_name, phone_number),
          recipient:recipient_id(full_name, phone_number)
        `)
        .eq('id', transactionId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      if (!transaction) throw new Error('Transaction not found');

      return transaction;
    } catch (error) {
      console.error('❌ Error getting transaction details:', error);
      throw error;
    }
  }

  /**
   * Build receipt data structure
   */
  async buildReceiptData(transaction, config) {
    const receiptData = {
      header: {
        title: config.title,
        icon: config.icon,
        receiptNumber: this.generateReceiptNumber(transaction.id),
        timestamp: formatDateTime(transaction.created_at),
        status: transaction.status
      },
      transaction: {
        id: transaction.id,
        reference: transaction.reference,
        type: transaction.type,
        amount: transaction.amount,
        currency: transaction.currency || 'UGX',
        fee: transaction.fee || 0,
        total: (parseFloat(transaction.amount) + parseFloat(transaction.fee || 0))
      },
      details: await this.buildTransactionDetails(transaction),
      footer: {
        generatedAt: new Date().toISOString(),
        supportContact: '+256 700 123 456',
        website: 'www.jiranipay.com'
      }
    };

    return receiptData;
  }

  /**
   * Build transaction-specific details
   */
  async buildTransactionDetails(transaction) {
    const details = {};

    switch (transaction.type) {
      case 'money_transfer':
      case 'send_money':
        details.sender = {
          name: transaction.sender?.full_name || 'You',
          phone: transaction.sender?.phone_number
        };
        details.recipient = {
          name: transaction.recipient?.full_name || transaction.recipient_name,
          phone: transaction.recipient?.phone_number || transaction.recipient_phone
        };
        break;

      case 'money_received':
      case 'receive_money':
        details.sender = {
          name: transaction.sender?.full_name || transaction.sender_name,
          phone: transaction.sender?.phone_number || transaction.sender_phone
        };
        details.recipient = {
          name: 'You',
          phone: transaction.recipient?.phone_number
        };
        break;

      case 'bill_payment':
        details.biller = {
          name: transaction.biller_name,
          code: transaction.biller_code
        };
        details.account = {
          number: transaction.account_number,
          name: transaction.account_name
        };
        break;

      case 'airtime_purchase':
        details.network = {
          name: transaction.network_name,
          code: transaction.network_code
        };
        details.phone = {
          number: transaction.phone_number
        };
        break;

      case 'withdrawal':
      case 'deposit':
        details.location = {
          name: transaction.location_name,
          address: transaction.location_address,
          agent: transaction.agent_name
        };
        break;
    }

    return details;
  }

  /**
   * Generate QR code data
   */
  generateQRCodeData(transaction, config) {
    const qrData = {
      app: 'JiraniPay',
      version: '1.0',
      type: 'receipt',
      transaction: {
        id: transaction.id,
        reference: transaction.reference,
        amount: transaction.amount,
        currency: transaction.currency || 'UGX',
        timestamp: transaction.created_at
      }
    };

    // Add type-specific data
    switch (transaction.type) {
      case 'money_transfer':
      case 'send_money':
        qrData.recipient = transaction.recipient_phone || transaction.recipient?.phone_number;
        break;

      case 'bill_payment':
        qrData.biller = transaction.biller_code;
        qrData.account = transaction.account_number;
        break;

      case 'airtime_purchase':
        qrData.network = transaction.network_code;
        qrData.phone = transaction.phone_number;
        break;
    }

    // Generate QR code string
    const qrString = JSON.stringify(qrData);
    
    return {
      data: qrData,
      string: qrString,
      url: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrString)}`
    };
  }

  /**
   * Create receipt record in database
   */
  async createReceiptRecord(transactionId, userId, receiptData, qrCodeData) {
    try {
      const record = {
        transaction_id: transactionId,
        user_id: userId,
        receipt_number: receiptData.header.receiptNumber,
        receipt_data: receiptData,
        qr_code_data: qrCodeData,
        status: 'generated',
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('digital_receipts')
        .insert(record)
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error('❌ Error creating receipt record:', error);
      throw error;
    }
  }

  /**
   * Generate receipt number
   */
  generateReceiptNumber(transactionId) {
    const timestamp = Date.now().toString().slice(-6);
    const txId = transactionId.toString().slice(-4);
    return `JP${timestamp}${txId}`;
  }

  /**
   * Generate download URL for receipt
   */
  generateDownloadUrl(receiptId) {
    return `jiranipay://receipt/download/${receiptId}`;
  }

  /**
   * Generate share URL for receipt
   */
  generateShareUrl(receiptId) {
    return `https://jiranipay.com/receipt/${receiptId}`;
  }

  /**
   * Get receipt by ID
   */
  async getReceipt(receiptId, userId) {
    try {
      const { data: receipt, error } = await supabase
        .from('digital_receipts')
        .select('*')
        .eq('id', receiptId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      if (!receipt) throw new Error('Receipt not found');

      return {
        success: true,
        receipt: {
          id: receipt.id,
          receiptNumber: receipt.receipt_number,
          data: receipt.receipt_data,
          qrCode: receipt.qr_code_data,
          status: receipt.status,
          createdAt: receipt.created_at,
          downloadUrl: this.generateDownloadUrl(receipt.id),
          shareUrl: this.generateShareUrl(receipt.id)
        }
      };
    } catch (error) {
      console.error('❌ Error getting receipt:', error);
      throw error;
    }
  }

  /**
   * Get user's receipts with pagination
   */
  async getUserReceipts(userId, options = {}) {
    try {
      const {
        limit = 20,
        offset = 0,
        type = null,
        startDate = null,
        endDate = null
      } = options;

      let query = supabase
        .from('digital_receipts')
        .select(`
          id,
          receipt_number,
          receipt_data,
          status,
          created_at,
          transaction:transaction_id(type, amount, currency, status)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (type) {
        query = query.eq('transaction.type', type);
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      const { data: receipts, error } = await query;

      if (error) throw error;

      return {
        success: true,
        receipts: receipts.map(receipt => ({
          id: receipt.id,
          receiptNumber: receipt.receipt_number,
          type: receipt.transaction?.type,
          amount: receipt.transaction?.amount,
          currency: receipt.transaction?.currency,
          status: receipt.status,
          createdAt: receipt.created_at,
          title: receipt.receipt_data?.header?.title,
          icon: receipt.receipt_data?.header?.icon
        })),
        pagination: {
          limit,
          offset,
          hasMore: receipts.length === limit
        }
      };
    } catch (error) {
      console.error('❌ Error getting user receipts:', error);
      throw error;
    }
  }

  /**
   * Search receipts
   */
  async searchReceipts(userId, searchQuery, options = {}) {
    try {
      const { limit = 20, offset = 0 } = options;

      const { data: receipts, error } = await supabase
        .from('digital_receipts')
        .select(`
          id,
          receipt_number,
          receipt_data,
          status,
          created_at,
          transaction:transaction_id(type, amount, currency, reference)
        `)
        .eq('user_id', userId)
        .or(`receipt_number.ilike.%${searchQuery}%,transaction.reference.ilike.%${searchQuery}%`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;

      return {
        success: true,
        receipts: receipts.map(receipt => ({
          id: receipt.id,
          receiptNumber: receipt.receipt_number,
          type: receipt.transaction?.type,
          amount: receipt.transaction?.amount,
          currency: receipt.transaction?.currency,
          reference: receipt.transaction?.reference,
          status: receipt.status,
          createdAt: receipt.created_at,
          title: receipt.receipt_data?.header?.title,
          icon: receipt.receipt_data?.header?.icon
        })),
        query: searchQuery,
        pagination: {
          limit,
          offset,
          hasMore: receipts.length === limit
        }
      };
    } catch (error) {
      console.error('❌ Error searching receipts:', error);
      throw error;
    }
  }

  /**
   * Export receipt as PDF (placeholder for future implementation)
   */
  async exportReceiptAsPDF(receiptId, userId) {
    try {
      const receipt = await this.getReceipt(receiptId, userId);
      
      // TODO: Implement PDF generation
      // For now, return receipt data that can be used by a PDF library
      
      return {
        success: true,
        format: 'pdf',
        data: receipt.receipt.data,
        filename: `JiraniPay_Receipt_${receipt.receipt.receiptNumber}.pdf`
      };
    } catch (error) {
      console.error('❌ Error exporting receipt as PDF:', error);
      throw error;
    }
  }

  /**
   * Share receipt
   */
  async shareReceipt(receiptId, userId, shareMethod = 'link') {
    try {
      const receipt = await this.getReceipt(receiptId, userId);

      switch (shareMethod) {
        case 'link':
          return {
            success: true,
            shareUrl: receipt.receipt.shareUrl,
            message: `View my JiraniPay receipt: ${receipt.receipt.shareUrl}`
          };

        case 'qr':
          return {
            success: true,
            qrCode: receipt.receipt.qrCode,
            message: 'Scan this QR code to view the receipt'
          };

        case 'text':
          const textReceipt = this.formatReceiptAsText(receipt.receipt.data);
          return {
            success: true,
            text: textReceipt,
            message: 'JiraniPay Receipt'
          };

        default:
          throw new Error('Unsupported share method');
      }
    } catch (error) {
      console.error('❌ Error sharing receipt:', error);
      throw error;
    }
  }

  /**
   * Format receipt as plain text
   */
  formatReceiptAsText(receiptData) {
    const { header, transaction, details, footer } = receiptData;

    let text = `${header.icon} ${header.title}\n`;
    text += `Receipt #: ${header.receiptNumber}\n`;
    text += `Date: ${formatDate(header.timestamp)}\n`;
    text += `Status: ${header.status.toUpperCase()}\n\n`;

    text += `Transaction Details:\n`;
    text += `Reference: ${transaction.reference}\n`;
    text += `Amount: ${formatCurrency(transaction.amount, transaction.currency)}\n`;
    
    if (transaction.fee > 0) {
      text += `Fee: ${formatCurrency(transaction.fee, transaction.currency)}\n`;
      text += `Total: ${formatCurrency(transaction.total, transaction.currency)}\n`;
    }

    if (details.sender) {
      text += `\nFrom: ${details.sender.name}\n`;
      if (details.sender.phone) {
        text += `Phone: ${details.sender.phone}\n`;
      }
    }

    if (details.recipient) {
      text += `\nTo: ${details.recipient.name}\n`;
      if (details.recipient.phone) {
        text += `Phone: ${details.recipient.phone}\n`;
      }
    }

    text += `\nGenerated: ${formatDate(footer.generatedAt)}\n`;
    text += `Support: ${footer.supportContact}\n`;
    text += `Website: ${footer.website}`;

    return text;
  }

  /**
   * Validate receipt QR code
   */
  async validateReceiptQR(qrData) {
    try {
      const data = typeof qrData === 'string' ? JSON.parse(qrData) : qrData;

      if (data.app !== 'JiraniPay' || data.type !== 'receipt') {
        throw new Error('Invalid QR code format');
      }

      // Verify transaction exists
      const { data: transaction, error } = await supabase
        .from('transactions')
        .select('id, reference, amount, status')
        .eq('id', data.transaction.id)
        .eq('reference', data.transaction.reference)
        .single();

      if (error || !transaction) {
        throw new Error('Transaction not found or invalid');
      }

      return {
        success: true,
        valid: true,
        transaction: {
          id: transaction.id,
          reference: transaction.reference,
          amount: transaction.amount,
          status: transaction.status
        }
      };
    } catch (error) {
      console.error('❌ Error validating receipt QR:', error);
      return {
        success: false,
        valid: false,
        error: error.message
      };
    }
  }
}

export default new DigitalReceiptService();
