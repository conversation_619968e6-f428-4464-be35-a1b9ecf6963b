/**
 * Notification Preferences Management Service
 * Manages user preferences for notification channels, types, timing, and frequency
 */

import { supabase } from './supabaseClient';

// Default notification preferences
const DEFAULT_PREFERENCES = {
  // Channel preferences
  push_enabled: true,
  sms_enabled: true,
  email_enabled: true,
  
  // Notification type preferences
  transaction_notifications: true,
  security_alerts: true,
  fraud_alerts: true,
  limit_warnings: true,
  promotional_notifications: false,
  
  // Timing preferences
  quiet_hours_enabled: false,
  quiet_hours_start: '22:00:00',
  quiet_hours_end: '07:00:00',
  
  // Frequency preferences
  digest_enabled: false,
  digest_frequency: 'daily',
  
  // Custom settings
  custom_settings: {}
};

// Notification type configurations
const NOTIFICATION_TYPE_CONFIG = {
  transaction_notifications: {
    name: 'Transaction Notifications',
    description: 'Notifications for completed transactions, payments, and money transfers',
    icon: '💳',
    category: 'financial',
    priority: 'high',
    channels: ['push', 'sms', 'email']
  },
  security_alerts: {
    name: 'Security Alerts',
    description: 'Important security notifications and account changes',
    icon: '🔒',
    category: 'security',
    priority: 'critical',
    channels: ['push', 'sms', 'email'],
    force_enabled: true // Cannot be disabled
  },
  fraud_alerts: {
    name: 'Fraud Alerts',
    description: 'Suspicious activity and fraud prevention notifications',
    icon: '🚨',
    category: 'security',
    priority: 'critical',
    channels: ['push', 'sms', 'email'],
    force_enabled: true // Cannot be disabled
  },
  limit_warnings: {
    name: 'Limit Warnings',
    description: 'Notifications when approaching transaction limits',
    icon: '⚠️',
    category: 'financial',
    priority: 'medium',
    channels: ['push', 'sms']
  },
  promotional_notifications: {
    name: 'Promotional Offers',
    description: 'Special offers, promotions, and product updates',
    icon: '🎁',
    category: 'marketing',
    priority: 'low',
    channels: ['push', 'email']
  }
};

class NotificationPreferencesService {
  constructor() {
    this.defaultPreferences = DEFAULT_PREFERENCES;
    this.typeConfig = NOTIFICATION_TYPE_CONFIG;
  }

  /**
   * Get user's notification preferences
   */
  async getUserPreferences(userId) {
    try {
      console.log('📋 Getting notification preferences for user:', userId);

      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // If no preferences found, create default ones
      if (!preferences) {
        return await this.createDefaultPreferences(userId);
      }

      console.log('✅ User preferences retrieved');
      
      return {
        success: true,
        preferences: {
          ...preferences,
          custom_settings: preferences.custom_settings || {}
        }
      };
    } catch (error) {
      console.error('❌ Error getting user preferences:', error);
      throw error;
    }
  }

  /**
   * Create default preferences for new user
   */
  async createDefaultPreferences(userId) {
    try {
      console.log('🆕 Creating default preferences for user:', userId);

      const defaultPrefs = {
        user_id: userId,
        ...this.defaultPreferences,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .insert(defaultPrefs)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Default preferences created');
      
      return {
        success: true,
        preferences,
        isDefault: true
      };
    } catch (error) {
      console.error('❌ Error creating default preferences:', error);
      throw error;
    }
  }

  /**
   * Update user's notification preferences
   */
  async updateUserPreferences(userId, updates) {
    try {
      console.log('📝 Updating notification preferences:', { userId, updates });

      // Validate updates
      const validatedUpdates = this.validatePreferenceUpdates(updates);

      // Ensure critical notifications cannot be disabled
      validatedUpdates.security_alerts = true;
      validatedUpdates.fraud_alerts = true;

      const updateData = {
        ...validatedUpdates,
        updated_at: new Date().toISOString()
      };

      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .update(updateData)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Notification preferences updated');
      
      return {
        success: true,
        preferences
      };
    } catch (error) {
      console.error('❌ Error updating preferences:', error);
      throw error;
    }
  }

  /**
   * Update specific channel preference
   */
  async updateChannelPreference(userId, channel, enabled) {
    try {
      console.log('📱 Updating channel preference:', { userId, channel, enabled });

      const validChannels = ['push', 'sms', 'email'];
      if (!validChannels.includes(channel)) {
        throw new Error(`Invalid channel: ${channel}`);
      }

      const updateKey = `${channel}_enabled`;
      const updates = { [updateKey]: enabled };

      return await this.updateUserPreferences(userId, updates);
    } catch (error) {
      console.error('❌ Error updating channel preference:', error);
      throw error;
    }
  }

  /**
   * Update specific notification type preference
   */
  async updateNotificationTypePreference(userId, notificationType, enabled) {
    try {
      console.log('🔔 Updating notification type preference:', { 
        userId, 
        notificationType, 
        enabled 
      });

      // Check if this notification type can be disabled
      const typeConfig = this.typeConfig[notificationType];
      if (typeConfig?.force_enabled && !enabled) {
        throw new Error(`${notificationType} cannot be disabled for security reasons`);
      }

      const updates = { [notificationType]: enabled };
      return await this.updateUserPreferences(userId, updates);
    } catch (error) {
      console.error('❌ Error updating notification type preference:', error);
      throw error;
    }
  }

  /**
   * Update quiet hours settings
   */
  async updateQuietHours(userId, quietHoursSettings) {
    try {
      console.log('🌙 Updating quiet hours settings:', { userId, quietHoursSettings });

      const { enabled, startTime, endTime } = quietHoursSettings;

      // Validate time format
      if (enabled && (!this.isValidTime(startTime) || !this.isValidTime(endTime))) {
        throw new Error('Invalid time format. Use HH:MM:SS format.');
      }

      const updates = {
        quiet_hours_enabled: enabled,
        quiet_hours_start: startTime,
        quiet_hours_end: endTime
      };

      return await this.updateUserPreferences(userId, updates);
    } catch (error) {
      console.error('❌ Error updating quiet hours:', error);
      throw error;
    }
  }

  /**
   * Update digest settings
   */
  async updateDigestSettings(userId, digestSettings) {
    try {
      console.log('📊 Updating digest settings:', { userId, digestSettings });

      const { enabled, frequency } = digestSettings;
      const validFrequencies = ['daily', 'weekly', 'monthly'];

      if (enabled && !validFrequencies.includes(frequency)) {
        throw new Error(`Invalid digest frequency: ${frequency}`);
      }

      const updates = {
        digest_enabled: enabled,
        digest_frequency: frequency
      };

      return await this.updateUserPreferences(userId, updates);
    } catch (error) {
      console.error('❌ Error updating digest settings:', error);
      throw error;
    }
  }

  /**
   * Get notification type configurations
   */
  getNotificationTypeConfigs() {
    return {
      success: true,
      types: Object.entries(this.typeConfig).map(([key, config]) => ({
        key,
        ...config
      }))
    };
  }

  /**
   * Check if user should receive notification based on preferences
   */
  async shouldReceiveNotification(userId, notificationType, channel) {
    try {
      const { preferences } = await this.getUserPreferences(userId);

      // Check if channel is enabled
      const channelEnabled = preferences[`${channel}_enabled`];
      if (!channelEnabled) {
        return { shouldReceive: false, reason: 'Channel disabled' };
      }

      // Check if notification type is enabled
      const typeEnabled = preferences[notificationType];
      if (typeEnabled === false) {
        return { shouldReceive: false, reason: 'Notification type disabled' };
      }

      // Check quiet hours
      if (preferences.quiet_hours_enabled && this.isInQuietHours(preferences)) {
        // Allow critical notifications during quiet hours
        const typeConfig = this.typeConfig[notificationType];
        if (typeConfig?.priority !== 'critical') {
          return { shouldReceive: false, reason: 'Quiet hours active' };
        }
      }

      return { shouldReceive: true };
    } catch (error) {
      console.error('❌ Error checking notification permission:', error);
      // Default to allowing notification if check fails
      return { shouldReceive: true, reason: 'Check failed, defaulting to allow' };
    }
  }

  /**
   * Get user's notification summary
   */
  async getNotificationSummary(userId) {
    try {
      const { preferences } = await this.getUserPreferences(userId);

      // Count enabled channels
      const enabledChannels = ['push', 'sms', 'email'].filter(
        channel => preferences[`${channel}_enabled`]
      );

      // Count enabled notification types
      const enabledTypes = Object.keys(this.typeConfig).filter(
        type => preferences[type] !== false
      );

      // Get recent notification stats
      const { data: recentStats, error } = await supabase
        .from('notifications')
        .select('status, created_at')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      const summary = {
        preferences: {
          enabledChannels,
          enabledTypes: enabledTypes.length,
          totalTypes: Object.keys(this.typeConfig).length,
          quietHoursEnabled: preferences.quiet_hours_enabled,
          digestEnabled: preferences.digest_enabled
        },
        recentActivity: {
          totalNotifications: recentStats.length,
          sentNotifications: recentStats.filter(n => n.status === 'sent').length,
          failedNotifications: recentStats.filter(n => n.status === 'failed').length
        },
        recommendations: this.generateRecommendations(preferences, recentStats)
      };

      return { success: true, summary };
    } catch (error) {
      console.error('❌ Error getting notification summary:', error);
      throw error;
    }
  }

  /**
   * Generate preference recommendations
   */
  generateRecommendations(preferences, recentStats) {
    const recommendations = [];

    // Check if no channels are enabled
    const enabledChannels = ['push', 'sms', 'email'].filter(
      channel => preferences[`${channel}_enabled`]
    );

    if (enabledChannels.length === 0) {
      recommendations.push({
        type: 'warning',
        title: 'No Notification Channels Enabled',
        message: 'You won\'t receive any notifications. Consider enabling at least one channel.',
        action: 'enable_channels'
      });
    }

    // Check for high failure rate
    const failedCount = recentStats.filter(n => n.status === 'failed').length;
    const failureRate = recentStats.length > 0 ? (failedCount / recentStats.length) * 100 : 0;

    if (failureRate > 20) {
      recommendations.push({
        type: 'warning',
        title: 'High Notification Failure Rate',
        message: `${failureRate.toFixed(1)}% of your notifications failed to deliver. Check your contact information.`,
        action: 'update_contact_info'
      });
    }

    // Suggest enabling digest for high-volume users
    if (recentStats.length > 20 && !preferences.digest_enabled) {
      recommendations.push({
        type: 'suggestion',
        title: 'Enable Notification Digest',
        message: 'You receive many notifications. Consider enabling daily digest to reduce interruptions.',
        action: 'enable_digest'
      });
    }

    return recommendations;
  }

  /**
   * Validate preference updates
   */
  validatePreferenceUpdates(updates) {
    const validKeys = Object.keys(this.defaultPreferences);
    const validated = {};

    Object.entries(updates).forEach(([key, value]) => {
      if (validKeys.includes(key)) {
        // Type validation
        if (typeof this.defaultPreferences[key] === 'boolean') {
          validated[key] = Boolean(value);
        } else if (typeof this.defaultPreferences[key] === 'string') {
          validated[key] = String(value);
        } else if (typeof this.defaultPreferences[key] === 'object') {
          validated[key] = value;
        }
      }
    });

    return validated;
  }

  /**
   * Check if current time is in quiet hours
   */
  isInQuietHours(preferences) {
    if (!preferences.quiet_hours_enabled) {
      return false;
    }

    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS format

    const startTime = preferences.quiet_hours_start;
    const endTime = preferences.quiet_hours_end;

    // Handle overnight quiet hours (e.g., 22:00 to 07:00)
    if (startTime > endTime) {
      return currentTime >= startTime || currentTime <= endTime;
    } else {
      return currentTime >= startTime && currentTime <= endTime;
    }
  }

  /**
   * Validate time format (HH:MM:SS)
   */
  isValidTime(timeString) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
    return timeRegex.test(timeString);
  }

  /**
   * Reset preferences to default
   */
  async resetToDefaults(userId) {
    try {
      console.log('🔄 Resetting preferences to defaults for user:', userId);

      const defaultPrefs = {
        ...this.defaultPreferences,
        updated_at: new Date().toISOString()
      };

      const { data: preferences, error } = await supabase
        .from('notification_preferences')
        .update(defaultPrefs)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Preferences reset to defaults');
      
      return {
        success: true,
        preferences
      };
    } catch (error) {
      console.error('❌ Error resetting preferences:', error);
      throw error;
    }
  }

  /**
   * Export user preferences
   */
  async exportPreferences(userId) {
    try {
      const { preferences } = await this.getUserPreferences(userId);

      const exportData = {
        exportedAt: new Date().toISOString(),
        userId,
        preferences: {
          channels: {
            push: preferences.push_enabled,
            sms: preferences.sms_enabled,
            email: preferences.email_enabled
          },
          types: {
            transactions: preferences.transaction_notifications,
            security: preferences.security_alerts,
            fraud: preferences.fraud_alerts,
            limits: preferences.limit_warnings,
            promotional: preferences.promotional_notifications
          },
          timing: {
            quietHours: {
              enabled: preferences.quiet_hours_enabled,
              start: preferences.quiet_hours_start,
              end: preferences.quiet_hours_end
            }
          },
          digest: {
            enabled: preferences.digest_enabled,
            frequency: preferences.digest_frequency
          },
          custom: preferences.custom_settings
        }
      };

      return {
        success: true,
        exportData,
        filename: `jiranipay_notification_preferences_${userId}_${Date.now()}.json`
      };
    } catch (error) {
      console.error('❌ Error exporting preferences:', error);
      throw error;
    }
  }
}

export default new NotificationPreferencesService();
