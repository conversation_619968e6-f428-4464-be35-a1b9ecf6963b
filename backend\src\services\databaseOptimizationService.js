/**
 * Database Optimization Service
 * Handles database performance monitoring, query optimization, and maintenance
 */

const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('./database');
const redisService = require('./redis');

class DatabaseOptimizationService {
  constructor() {
    this.performanceMetrics = {
      slowQueries: [],
      connectionPool: {},
      queryStats: {},
      indexUsage: {}
    };
    
    this.thresholds = {
      slowQueryTime: 1000, // 1 second
      maxConnections: 100,
      connectionWarningThreshold: 80,
      deadlockRetryAttempts: 3
    };
  }

  /**
   * Initialize database optimization monitoring
   */
  async initialize() {
    try {
      // Set up query performance monitoring
      await this.setupQueryMonitoring();
      
      // Initialize connection pool monitoring
      await this.setupConnectionPoolMonitoring();
      
      // Set up automated maintenance tasks
      await this.setupMaintenanceTasks();
      
      logger.info('Database optimization service initialized');
    } catch (error) {
      logger.error('Failed to initialize database optimization service:', error);
    }
  }

  /**
   * Set up query performance monitoring
   */
  async setupQueryMonitoring() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Enable query logging for slow queries
      await supabase.rpc('enable_query_logging', {
        log_min_duration_statement: this.thresholds.slowQueryTime
      }).catch(() => {
        // Ignore if function doesn't exist (will be created in migration)
        logger.debug('Query logging function not available');
      });
      
      logger.info('Query monitoring enabled');
    } catch (error) {
      logger.error('Failed to setup query monitoring:', error);
    }
  }

  /**
   * Set up connection pool monitoring
   */
  async setupConnectionPoolMonitoring() {
    try {
      // Monitor connection pool every 30 seconds
      setInterval(async () => {
        await this.monitorConnectionPool();
      }, 30000);
      
      logger.info('Connection pool monitoring enabled');
    } catch (error) {
      logger.error('Failed to setup connection pool monitoring:', error);
    }
  }

  /**
   * Set up automated maintenance tasks
   */
  async setupMaintenanceTasks() {
    try {
      // Run maintenance tasks daily at 2 AM
      const maintenanceInterval = 24 * 60 * 60 * 1000; // 24 hours
      
      setInterval(async () => {
        const now = new Date();
        if (now.getHours() === 2) { // 2 AM
          await this.runMaintenanceTasks();
        }
      }, maintenanceInterval);
      
      logger.info('Automated maintenance tasks scheduled');
    } catch (error) {
      logger.error('Failed to setup maintenance tasks:', error);
    }
  }

  /**
   * Monitor connection pool status
   */
  async monitorConnectionPool() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Get connection pool statistics
      const { data: poolStats } = await supabase.rpc('get_connection_pool_stats').catch(() => ({
        data: null
      }));
      
      if (poolStats) {
        this.performanceMetrics.connectionPool = {
          totalConnections: poolStats.total_connections || 0,
          activeConnections: poolStats.active_connections || 0,
          idleConnections: poolStats.idle_connections || 0,
          timestamp: new Date().toISOString()
        };
        
        // Check for connection pool warnings
        const activePercentage = (poolStats.active_connections / this.thresholds.maxConnections) * 100;
        
        if (activePercentage > this.thresholds.connectionWarningThreshold) {
          logger.warn('High database connection usage', {
            activeConnections: poolStats.active_connections,
            maxConnections: this.thresholds.maxConnections,
            percentage: activePercentage
          });
        }
        
        // Cache metrics for monitoring dashboard
        await redisService.set('db_pool_stats', this.performanceMetrics.connectionPool, 300);
      }
    } catch (error) {
      logger.error('Failed to monitor connection pool:', error);
    }
  }

  /**
   * Run automated maintenance tasks
   */
  async runMaintenanceTasks() {
    try {
      logger.info('Starting automated database maintenance');
      
      // Update table statistics
      await this.updateTableStatistics();
      
      // Analyze slow queries
      await this.analyzeSlowQueries();
      
      // Check index usage
      await this.checkIndexUsage();
      
      // Clean up old audit logs (keep 90 days)
      await this.cleanupOldAuditLogs();
      
      // Vacuum and analyze tables
      await this.vacuumTables();
      
      logger.info('Automated database maintenance completed');
    } catch (error) {
      logger.error('Database maintenance failed:', error);
    }
  }

  /**
   * Update table statistics for query optimization
   */
  async updateTableStatistics() {
    try {
      const supabase = databaseService.getSupabase();
      
      const tables = [
        'user_profiles', 'wallets', 'transactions', 'wallet_transactions',
        'payment_methods', 'exchange_rates', 'audit_logs', 'notifications'
      ];
      
      for (const table of tables) {
        await supabase.rpc('analyze_table', { table_name: table }).catch(() => {
          // Ignore if function doesn't exist
        });
      }
      
      logger.info('Table statistics updated');
    } catch (error) {
      logger.error('Failed to update table statistics:', error);
    }
  }

  /**
   * Analyze slow queries and provide optimization recommendations
   */
  async analyzeSlowQueries() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Get slow queries from the last 24 hours
      const { data: slowQueries } = await supabase.rpc('get_slow_queries', {
        hours: 24
      }).catch(() => ({ data: [] }));
      
      if (slowQueries && slowQueries.length > 0) {
        this.performanceMetrics.slowQueries = slowQueries.map(query => ({
          query: query.query,
          avgTime: query.avg_time,
          calls: query.calls,
          totalTime: query.total_time,
          recommendations: this.generateQueryOptimizationRecommendations(query)
        }));
        
        // Log slow queries for investigation
        logger.warn('Slow queries detected', {
          count: slowQueries.length,
          queries: this.performanceMetrics.slowQueries
        });
        
        // Cache for monitoring dashboard
        await redisService.set('slow_queries', this.performanceMetrics.slowQueries, 3600);
      }
    } catch (error) {
      logger.error('Failed to analyze slow queries:', error);
    }
  }

  /**
   * Generate query optimization recommendations
   */
  generateQueryOptimizationRecommendations(query) {
    const recommendations = [];
    
    // Check for missing indexes
    if (query.query.includes('WHERE') && !query.query.includes('INDEX')) {
      recommendations.push('Consider adding indexes on WHERE clause columns');
    }
    
    // Check for SELECT *
    if (query.query.includes('SELECT *')) {
      recommendations.push('Avoid SELECT * - specify only needed columns');
    }
    
    // Check for ORDER BY without LIMIT
    if (query.query.includes('ORDER BY') && !query.query.includes('LIMIT')) {
      recommendations.push('Consider adding LIMIT to ORDER BY queries');
    }
    
    // Check for subqueries
    if (query.query.includes('SELECT') && query.query.split('SELECT').length > 2) {
      recommendations.push('Consider optimizing subqueries or using JOINs');
    }
    
    return recommendations;
  }

  /**
   * Check index usage and identify unused indexes
   */
  async checkIndexUsage() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Get index usage statistics
      const { data: indexStats } = await supabase.rpc('get_index_usage').catch(() => ({
        data: []
      }));
      
      if (indexStats) {
        this.performanceMetrics.indexUsage = indexStats;
        
        // Identify unused indexes
        const unusedIndexes = indexStats.filter(index => 
          index.idx_scan === 0 && !index.indisprimary && !index.indisunique
        );
        
        if (unusedIndexes.length > 0) {
          logger.warn('Unused indexes detected', {
            count: unusedIndexes.length,
            indexes: unusedIndexes.map(idx => idx.indexname)
          });
        }
        
        // Cache for monitoring
        await redisService.set('index_usage', this.performanceMetrics.indexUsage, 3600);
      }
    } catch (error) {
      logger.error('Failed to check index usage:', error);
    }
  }

  /**
   * Clean up old audit logs to maintain performance
   */
  async cleanupOldAuditLogs() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Delete audit logs older than 90 days
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 90);
      
      const { data, error } = await supabase
        .from('audit_logs')
        .delete()
        .lt('timestamp', cutoffDate.toISOString());
      
      if (error) {
        logger.error('Failed to cleanup old audit logs:', error);
      } else {
        logger.info('Old audit logs cleaned up', {
          cutoffDate: cutoffDate.toISOString(),
          deletedCount: data?.length || 0
        });
      }
    } catch (error) {
      logger.error('Failed to cleanup old audit logs:', error);
    }
  }

  /**
   * Vacuum tables for performance optimization
   */
  async vacuumTables() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Run VACUUM ANALYZE on key tables
      const tables = ['transactions', 'audit_logs', 'wallet_transactions'];
      
      for (const table of tables) {
        await supabase.rpc('vacuum_table', { table_name: table }).catch(() => {
          // Ignore if function doesn't exist
        });
      }
      
      logger.info('Table vacuum completed');
    } catch (error) {
      logger.error('Failed to vacuum tables:', error);
    }
  }

  /**
   * Get database performance metrics
   */
  async getPerformanceMetrics() {
    try {
      const supabase = databaseService.getSupabase();
      
      // Get database size and statistics
      const { data: dbStats } = await supabase.rpc('get_database_stats').catch(() => ({
        data: {}
      }));
      
      return {
        connectionPool: this.performanceMetrics.connectionPool,
        slowQueries: this.performanceMetrics.slowQueries,
        indexUsage: this.performanceMetrics.indexUsage,
        databaseStats: dbStats || {},
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to get performance metrics:', error);
      return {
        connectionPool: {},
        slowQueries: [],
        indexUsage: {},
        databaseStats: {},
        error: error.message,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * Optimize specific query
   */
  async optimizeQuery(query, parameters = []) {
    try {
      const supabase = databaseService.getSupabase();
      
      // Get query execution plan
      const { data: executionPlan } = await supabase.rpc('explain_query', {
        query_text: query,
        query_params: parameters
      }).catch(() => ({ data: null }));
      
      if (executionPlan) {
        const recommendations = this.analyzeExecutionPlan(executionPlan);
        
        return {
          query,
          executionPlan,
          recommendations,
          timestamp: new Date().toISOString()
        };
      }
      
      return null;
    } catch (error) {
      logger.error('Failed to optimize query:', error);
      return null;
    }
  }

  /**
   * Analyze execution plan and provide recommendations
   */
  analyzeExecutionPlan(executionPlan) {
    const recommendations = [];
    
    // Check for sequential scans
    if (executionPlan.includes('Seq Scan')) {
      recommendations.push('Sequential scan detected - consider adding indexes');
    }
    
    // Check for nested loops
    if (executionPlan.includes('Nested Loop')) {
      recommendations.push('Nested loop detected - consider optimizing JOIN conditions');
    }
    
    // Check for sorts
    if (executionPlan.includes('Sort')) {
      recommendations.push('Sort operation detected - consider adding indexes for ORDER BY');
    }
    
    return recommendations;
  }

  /**
   * Health check for database optimization service
   */
  async healthCheck() {
    try {
      const metrics = await this.getPerformanceMetrics();
      
      return {
        status: 'healthy',
        metrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const databaseOptimizationService = new DatabaseOptimizationService();

module.exports = databaseOptimizationService;
