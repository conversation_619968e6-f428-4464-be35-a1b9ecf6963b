-- =====================================================
-- STEP-BY-STEP PROFILE CREATION FIX MIGRATION
-- =====================================================
-- Run each section one by one in your Supabase SQL Editor
-- Copy and paste each section separately, then click "Run"

-- =====================================================
-- STEP 1: ADD MISSING COLUMNS TO USER_PREFERENCES
-- =====================================================
-- Copy this section first and run it:

-- Add language_preference column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'user_preferences' 
        AND column_name = 'language_preference'
    ) THEN
        ALTER TABLE public.user_preferences 
        ADD COLUMN language_preference TEXT DEFAULT 'en';
        RAISE NOTICE 'Added language_preference column';
    ELSE
        RAISE NOTICE 'language_preference column already exists';
    END IF;
END $$;

-- Add preferred_currency column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'user_preferences' 
        AND column_name = 'preferred_currency'
    ) THEN
        ALTER TABLE public.user_preferences 
        ADD COLUMN preferred_currency TEXT DEFAULT 'UGX';
        RAISE NOTICE 'Added preferred_currency column';
    ELSE
        RAISE NOTICE 'preferred_currency column already exists';
    END IF;
END $$;

-- =====================================================
-- STEP 2: VERIFY COLUMNS WERE ADDED
-- =====================================================
-- Copy this section and run it to verify:

SELECT 
    table_name,
    column_name,
    data_type,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_preferences'
AND column_name IN ('preferred_currency', 'language_preference')
ORDER BY column_name;

-- =====================================================
-- STEP 3: CREATE SAFE PROFILE CREATION FUNCTION
-- =====================================================
-- Copy this section and run it:

CREATE OR REPLACE FUNCTION public.create_user_profile_safe(
    user_id UUID,
    full_name TEXT DEFAULT NULL,
    phone TEXT DEFAULT NULL,
    email TEXT DEFAULT NULL,
    country_code TEXT DEFAULT 'UG'
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    existing_profile public.profiles%ROWTYPE;
BEGIN
    -- Check if profile already exists
    SELECT * INTO existing_profile 
    FROM public.profiles 
    WHERE id = user_id;
    
    IF FOUND THEN
        -- Profile exists, return it
        SELECT json_build_object(
            'success', true,
            'data', row_to_json(existing_profile),
            'created', false,
            'message', 'Profile already exists'
        ) INTO result;
        RETURN result;
    END IF;
    
    -- Profile doesn't exist, create it
    INSERT INTO public.profiles (
        id, 
        full_name, 
        phone, 
        email, 
        country_code,
        created_at,
        updated_at
    ) VALUES (
        user_id,
        full_name,
        phone,
        email,
        country_code,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        updated_at = NOW()
    RETURNING * INTO existing_profile;
    
    -- Create default preferences
    INSERT INTO public.user_preferences (
        id,
        preferred_currency,
        language_preference,
        created_at,
        updated_at
    ) VALUES (
        user_id,
        'UGX',
        'en',
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO NOTHING;
    
    SELECT json_build_object(
        'success', true,
        'data', row_to_json(existing_profile),
        'created', true,
        'message', 'Profile created successfully'
    ) INTO result;
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        SELECT json_build_object(
            'success', false,
            'error', SQLERRM,
            'message', 'Failed to create profile'
        ) INTO result;
        RETURN result;
END;
$$;

-- =====================================================
-- STEP 4: GRANT PERMISSIONS
-- =====================================================
-- Copy this section and run it:

GRANT EXECUTE ON FUNCTION public.create_user_profile_safe TO authenticated;

-- =====================================================
-- STEP 5: TEST THE FUNCTION
-- =====================================================
-- Copy this section and run it to test:

SELECT public.create_user_profile_safe(
    '00000000-0000-0000-0000-000000000001'::UUID,
    'Test User',
    '+************',
    '<EMAIL>',
    'UG'
);

-- =====================================================
-- STEP 6: FINAL VERIFICATION
-- =====================================================
-- Copy this section and run it to verify everything works:

-- Check if all required columns exist
SELECT 
    'user_preferences' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_preferences'
AND column_name IN ('preferred_currency', 'language_preference', 'biometric_enabled', 'notifications_enabled')
ORDER BY column_name;

-- Check if function exists
SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name = 'create_user_profile_safe';

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 MIGRATION COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '✅ Missing columns added to user_preferences table';
    RAISE NOTICE '✅ Safe profile creation function created';
    RAISE NOTICE '✅ Permissions granted';
    RAISE NOTICE '🚀 Ready to test profile creation fixes!';
END $$;
