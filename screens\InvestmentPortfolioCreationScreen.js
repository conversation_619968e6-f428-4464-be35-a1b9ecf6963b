/**
 * Investment Portfolio Creation Screen
 * Screen for creating new investment portfolios with risk assessment
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import { formatCurrency } from '../utils/currencyUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentPortfolioCreationScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [portfolioName, setPortfolioName] = useState('');
  const [portfolioType, setPortfolioType] = useState('general');
  const [description, setDescription] = useState('');
  const [initialCash, setInitialCash] = useState('');
  const [riskLevel, setRiskLevel] = useState('moderate');
  const [riskScore, setRiskScore] = useState(5);
  const [investmentStrategy, setInvestmentStrategy] = useState('balanced');
  const [showTypeModal, setShowTypeModal] = useState(false);
  const [showRiskModal, setShowRiskModal] = useState(false);
  const [showStrategyModal, setShowStrategyModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const portfolioTypes = [
    { key: 'general', label: 'General Portfolio', icon: 'briefcase', color: '#4ECDC4', description: 'Flexible investment portfolio for any purpose' },
    { key: 'retirement', label: 'Retirement Portfolio', icon: 'time', color: '#6C5CE7', description: 'Long-term retirement savings and investments' },
    { key: 'education', label: 'Education Portfolio', icon: 'school', color: '#FECA57', description: 'Save and invest for education expenses' },
    { key: 'aggressive', label: 'Aggressive Growth', icon: 'trending-up', color: '#FF6B35', description: 'High-risk, high-reward investment strategy' },
    { key: 'conservative', label: 'Conservative Portfolio', icon: 'shield-checkmark', color: '#96CEB4', description: 'Low-risk, stable investment approach' },
    { key: 'balanced', label: 'Balanced Portfolio', icon: 'scale', color: '#45B7D1', description: 'Balanced mix of growth and stability' }
  ];

  const riskLevels = [
    { key: 'conservative', label: 'Conservative', score: 2, color: '#96CEB4', description: 'Low risk, stable returns' },
    { key: 'moderate', label: 'Moderate', score: 5, color: '#45B7D1', description: 'Balanced risk and return' },
    { key: 'aggressive', label: 'Aggressive', score: 8, color: '#FF6B35', description: 'High risk, high potential returns' }
  ];

  const investmentStrategies = [
    { key: 'conservative', label: 'Conservative', description: 'Focus on bonds and stable assets' },
    { key: 'balanced', label: 'Balanced', description: 'Mix of stocks, bonds, and other assets' },
    { key: 'growth', label: 'Growth', description: 'Focus on growth stocks and emerging markets' },
    { key: 'income', label: 'Income', description: 'Focus on dividend-paying stocks and bonds' },
    { key: 'value', label: 'Value', description: 'Focus on undervalued stocks and assets' }
  ];

  const handleCreatePortfolio = async () => {
    try {
      // Validation
      if (!portfolioName.trim()) {
        Alert.alert('Validation Error', 'Please enter a portfolio name');
        return;
      }

      if (initialCash && parseFloat(initialCash) < 0) {
        Alert.alert('Validation Error', 'Initial cash cannot be negative');
        return;
      }

      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to create portfolios');
        return;
      }

      const portfolioData = {
        portfolioName: portfolioName.trim(),
        portfolioType,
        description: description.trim(),
        initialCash: parseFloat(initialCash) || 0,
        riskLevel,
        riskScore,
        investmentStrategy,
        currency: 'UGX', // Default to UGX for East African investments
        isManaged: false // User-managed portfolio
      };

      const result = await investmentPortfolioService.createPortfolio(userId, portfolioData);

      if (result.success) {
        Alert.alert(
          'Portfolio Created!',
          `Your ${portfolioName} portfolio has been created successfully!`,
          [
            {
              text: 'View Portfolio',
              onPress: () => {
                navigation.replace('InvestmentPortfolioDetails', { 
                  portfolioId: result.portfolio.id 
                });
              }
            },
            {
              text: 'Back to Dashboard',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to create portfolio');
      }

    } catch (error) {
      console.error('❌ Error creating portfolio:', error);
      Alert.alert('Error', 'Failed to create portfolio');
    } finally {
      setLoading(false);
    }
  };

  const getSelectedType = () => {
    return portfolioTypes.find(type => type.key === portfolioType) || portfolioTypes[0];
  };

  const getSelectedRisk = () => {
    return riskLevels.find(risk => risk.key === riskLevel) || riskLevels[1];
  };

  const getSelectedStrategy = () => {
    return investmentStrategies.find(strategy => strategy.key === investmentStrategy) || investmentStrategies[1];
  };

  const renderSelector = (title, selectedItem, options, showModal, setShowModal, onSelect, renderOption) => (
    <View style={styles.inputGroup}>
      <Text style={styles.inputLabel}>{title}</Text>
      <TouchableOpacity 
        style={styles.selectorButton}
        onPress={() => setShowModal(true)}
      >
        <View style={styles.selectedContent}>
          {selectedItem.icon && (
            <View style={[styles.selectorIcon, { backgroundColor: selectedItem.color }]}>
              <Ionicons name={selectedItem.icon} size={16} color={theme.colors.white} />
            </View>
          )}
          <View style={styles.selectorInfo}>
            <Text style={styles.selectedText}>{selectedItem.label}</Text>
            {selectedItem.description && (
              <Text style={styles.selectedDescription}>{selectedItem.description}</Text>
            )}
          </View>
        </View>
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={showModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select {title}</Text>
            <TouchableOpacity onPress={() => setShowModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {options.map((option) => renderOption(option, onSelect, setShowModal))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  const renderTypeOption = (type, onSelect, setShowModal) => (
    <TouchableOpacity
      key={type.key}
      style={[
        styles.optionCard,
        portfolioType === type.key && styles.optionCardSelected
      ]}
      onPress={() => {
        onSelect(type.key);
        setShowModal(false);
      }}
    >
      <View style={[styles.optionIcon, { backgroundColor: type.color }]}>
        <Ionicons name={type.icon} size={24} color={theme.colors.white} />
      </View>
      <View style={styles.optionContent}>
        <Text style={styles.optionTitle}>{type.label}</Text>
        <Text style={styles.optionDescription}>{type.description}</Text>
      </View>
      {portfolioType === type.key && (
        <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderRiskOption = (risk, onSelect, setShowModal) => (
    <TouchableOpacity
      key={risk.key}
      style={[
        styles.optionCard,
        riskLevel === risk.key && styles.optionCardSelected
      ]}
      onPress={() => {
        onSelect(risk.key);
        setRiskScore(risk.score);
        setShowModal(false);
      }}
    >
      <View style={[styles.riskIndicator, { backgroundColor: risk.color }]}>
        <Text style={styles.riskScore}>{risk.score}</Text>
      </View>
      <View style={styles.optionContent}>
        <Text style={styles.optionTitle}>{risk.label}</Text>
        <Text style={styles.optionDescription}>{risk.description}</Text>
      </View>
      {riskLevel === risk.key && (
        <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderStrategyOption = (strategy, onSelect, setShowModal) => (
    <TouchableOpacity
      key={strategy.key}
      style={[
        styles.optionCard,
        investmentStrategy === strategy.key && styles.optionCardSelected
      ]}
      onPress={() => {
        onSelect(strategy.key);
        setShowModal(false);
      }}
    >
      <View style={styles.optionContent}>
        <Text style={styles.optionTitle}>{strategy.label}</Text>
        <Text style={styles.optionDescription}>{strategy.description}</Text>
      </View>
      {investmentStrategy === strategy.key && (
        <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Create Portfolio</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          {/* Portfolio Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Portfolio Name *</Text>
            <TextInput
              style={styles.textInput}
              value={portfolioName}
              onChangeText={setPortfolioName}
              placeholder="e.g., My Growth Portfolio"
              placeholderTextColor={theme.colors.textSecondary}
              maxLength={50}
            />
          </View>

          {/* Portfolio Type */}
          {renderSelector(
            'Portfolio Type',
            getSelectedType(),
            portfolioTypes,
            showTypeModal,
            setShowTypeModal,
            setPortfolioType,
            renderTypeOption
          )}

          {/* Risk Level */}
          {renderSelector(
            'Risk Level',
            getSelectedRisk(),
            riskLevels,
            showRiskModal,
            setShowRiskModal,
            setRiskLevel,
            renderRiskOption
          )}

          {/* Investment Strategy */}
          {renderSelector(
            'Investment Strategy',
            getSelectedStrategy(),
            investmentStrategies,
            showStrategyModal,
            setShowStrategyModal,
            setInvestmentStrategy,
            renderStrategyOption
          )}

          {/* Description */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Description (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Describe your investment goals..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
              maxLength={200}
            />
          </View>

          {/* Initial Cash */}
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Initial Cash (UGX)</Text>
            <TextInput
              style={styles.amountInput}
              value={initialCash}
              onChangeText={setInitialCash}
              placeholder="0"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
            />
            <Text style={styles.helpText}>
              You can add cash to your portfolio later if you prefer to start with UGX 0
            </Text>
          </View>

          {/* Portfolio Summary */}
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Portfolio Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Name:</Text>
              <Text style={styles.summaryValue}>{portfolioName || 'Not set'}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Type:</Text>
              <Text style={styles.summaryValue}>{getSelectedType().label}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Risk Level:</Text>
              <Text style={styles.summaryValue}>{getSelectedRisk().label} ({riskScore}/10)</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Strategy:</Text>
              <Text style={styles.summaryValue}>{getSelectedStrategy().label}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Initial Cash:</Text>
              <Text style={styles.summaryValue}>
                {initialCash ? formatCurrency(parseFloat(initialCash), 'UGX') : formatCurrency(0, 'UGX')}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Create Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[
            styles.createButton,
            (!portfolioName.trim() || loading) && styles.createButtonDisabled
          ]}
          onPress={handleCreatePortfolio}
          disabled={!portfolioName.trim() || loading}
        >
          <Text style={styles.createButtonText}>
            {loading ? 'Creating Portfolio...' : 'Create Portfolio'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  amountInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
    textAlign: 'right',
  },
  helpText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  selectorButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectorIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectorInfo: {
    flex: 1,
  },
  selectedText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '500',
  },
  selectedDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  summaryCard: {
    backgroundColor: theme.colors.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginTop: 10,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  createButtonDisabled: {
    opacity: 0.5,
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: theme.colors.surface,
  },
  optionCardSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  riskIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  riskScore: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.white,
  },
});

export default InvestmentPortfolioCreationScreen;
