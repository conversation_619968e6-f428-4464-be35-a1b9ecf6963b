/**
 * Production Mode Toggle Component
 * Allows developers to easily switch between development and production modes
 * This component should be removed or hidden in actual production builds
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { isProductionMode, getEnvironmentName } from '../config/environment';

const ProductionModeToggle = ({ visible, onClose }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [currentMode, setCurrentMode] = useState(getEnvironmentName());

  useEffect(() => {
    setCurrentMode(getEnvironmentName());
  }, []);

  const handleModeSwitch = () => {
    Alert.alert(
      'Switch Environment Mode',
      `Are you sure you want to switch to ${currentMode === 'development' ? 'PRODUCTION' : 'DEVELOPMENT'} mode?\n\nThis will require an app restart.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Switch',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Manual Configuration Required',
              `To switch to ${currentMode === 'development' ? 'production' : 'development'} mode:\n\n1. Open config/environment.js\n2. Set PRODUCTION_MODE to ${currentMode === 'development' ? 'true' : 'false'}\n3. Restart the app\n\nNote: Production mode requires all API credentials to be configured.`,
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  const getStatusColor = () => {
    return currentMode === 'production' ? '#E53E3E' : '#38A169';
  };

  const getStatusIcon = () => {
    return currentMode === 'production' ? 'shield-checkmark' : 'code-slash';
  };

  if (!__DEV__) {
    // Don't show in production builds
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Environment Configuration</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            <View style={[styles.statusCard, { borderColor: getStatusColor() }]}>
              <View style={styles.statusHeader}>
                <Ionicons 
                  name={getStatusIcon()} 
                  size={32} 
                  color={getStatusColor()} 
                />
                <Text style={[styles.statusTitle, { color: getStatusColor() }]}>
                  {currentMode.toUpperCase()} MODE
                </Text>
              </View>
              
              <Text style={styles.statusDescription}>
                {currentMode === 'production' 
                  ? 'Using real APIs, live transactions, and production security measures.'
                  : 'Using mock data, simulated transactions, and development shortcuts.'
                }
              </Text>
            </View>

            <View style={styles.featuresSection}>
              <Text style={styles.featuresTitle}>Current Configuration:</Text>
              
              <View style={styles.featuresList}>
                <FeatureItem
                  icon="server"
                  title="API Endpoints"
                  value={currentMode === 'production' ? 'Production APIs' : 'Mock/Development'}
                  isProduction={currentMode === 'production'}
                  theme={theme}
                />
                
                <FeatureItem
                  icon="card"
                  title="Transactions"
                  value={currentMode === 'production' ? 'Real Payments' : 'Simulated'}
                  isProduction={currentMode === 'production'}
                  theme={theme}
                />
                
                <FeatureItem
                  icon="shield"
                  title="Security"
                  value={currentMode === 'production' ? 'Full Security' : 'Development Mode'}
                  isProduction={currentMode === 'production'}
                  theme={theme}
                />
                
                <FeatureItem
                  icon="people"
                  title="Authentication"
                  value={currentMode === 'production' ? 'Real Users' : 'Test Accounts'}
                  isProduction={currentMode === 'production'}
                  theme={theme}
                />
                
                <FeatureItem
                  icon="analytics"
                  title="Data Storage"
                  value={currentMode === 'production' ? 'Production DB' : 'Dev/Mock Data'}
                  isProduction={currentMode === 'production'}
                  theme={theme}
                />
              </View>
            </View>

            <TouchableOpacity 
              style={styles.switchButton} 
              onPress={handleModeSwitch}
              activeOpacity={0.7}
            >
              <Ionicons name="swap-horizontal" size={20} color={Colors.neutral.white} />
              <Text style={styles.switchButtonText}>
                Switch to {currentMode === 'development' ? 'Production' : 'Development'} Mode
              </Text>
            </TouchableOpacity>

            <Text style={styles.warning}>
              ⚠️ This toggle is for development only and will be removed in production builds.
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const FeatureItem = ({ icon, title, value, isProduction, theme }) => {
  return (
    <View style={[styles.featureItem, { backgroundColor: theme.colors.surface }]}>
      <Ionicons 
        name={icon} 
        size={20} 
        color={isProduction ? '#E53E3E' : '#38A169'} 
      />
      <View style={styles.featureText}>
        <Text style={[styles.featureTitle, { color: theme.colors.text }]}>{title}</Text>
        <Text style={[styles.featureValue, { color: theme.colors.textSecondary }]}>{value}</Text>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 20,
  },
  statusCard: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    backgroundColor: theme.colors.surface,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 12,
  },
  statusDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  featuresSection: {
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  featuresList: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  featureText: {
    marginLeft: 12,
    flex: 1,
  },
  featureTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  featureValue: {
    fontSize: 12,
    marginTop: 2,
  },
  switchButton: {
    backgroundColor: '#E67E22',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  switchButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  warning: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default ProductionModeToggle;
