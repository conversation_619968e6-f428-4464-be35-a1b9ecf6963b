import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import QRCode from 'react-native-qrcode-svg';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import qrCodeService from '../services/qrCodeService';

/**
 * QRGeneratorScreen - Generate QR codes for receiving payments
 * Features amount entry, purpose selection, and QR code sharing
 */
const QRGeneratorScreen = ({ navigation, route }) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [amount, setAmount] = useState('');
  const [purpose, setPurpose] = useState('Payment');
  const [qrData, setQrData] = useState(null);
  const [loading, setLoading] = useState(false);


  const qrType = route.params?.type || 'receive'; // 'receive' or 'request'

  const quickAmounts = [5000, 10000, 25000, 50000, 100000, 200000];
  const purposeOptions = [
    'Payment',
    'Service Fee',
    'Product Purchase',
    'Bill Payment',
    'Donation',
    'Other',
  ];

  const handleAmountChange = (value) => {
    // Remove any non-numeric characters except decimal point
    const cleanValue = value.replace(/[^\d.]/g, '');
    setAmount(cleanValue);
  };

  const handleQuickAmount = (quickAmount) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setAmount(quickAmount.toString());
  };

  const generateQRCode = async () => {
    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const amountValue = amount ? parseFloat(amount) : null;
      
      // Validate amount if provided
      if (amountValue !== null && (isNaN(amountValue) || amountValue <= 0)) {
        Alert.alert('Invalid Amount', 'Please enter a valid amount');
        return;
      }

      const params = {
        amount: amountValue,
        purpose: purpose || 'Payment',
      };

      let result;
      if (qrType === 'request') {
        if (!amountValue) {
          Alert.alert('Amount Required', 'Payment requests must include an amount');
          return;
        }
        result = await qrCodeService.generatePaymentRequestQR(params);
      } else {
        result = await qrCodeService.generateReceivePaymentQR(params);
      }

      if (result.success) {
        setQrData(result);
        console.log('✅ QR Code generated successfully');
      } else {
        Alert.alert('Generation Failed', result.error);
      }
    } catch (error) {
      console.error('Generate QR code error:', error);
      Alert.alert('Error', 'Failed to generate QR code');
    } finally {
      setLoading(false);
    }
  };

  const shareQRCode = async () => {
    try {
      if (!qrData) return;

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Share using native share with text only for now
      const shareText = qrCodeService.formatShareText(qrData.qrData);

      await Share.share({
        message: shareText,
        title: 'JiraniPay QR Code',
      });

    } catch (error) {
      console.error('Share QR code error:', error);
      Alert.alert('Share Failed', 'Failed to share QR code');
    }
  };

  const saveQRCode = async () => {
    try {
      if (!qrData) return;

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // For now, just show the QR data that can be copied
      Alert.alert(
        'QR Code Data',
        `Copy this QR code data:\n\n${qrData.qrString}`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Copy', onPress: () => {
            // Copy to clipboard functionality would go here
            Alert.alert('Copied', 'QR code data copied to clipboard');
          }}
        ]
      );
    } catch (error) {
      console.error('Save QR code error:', error);
      Alert.alert('Save Failed', 'Failed to save QR code');
    }
  };

  const formatCurrency = (value) => {
    return `UGX ${parseFloat(value).toLocaleString()}`;
  };

  const renderAmountInput = () => (
    <View style={styles.inputSection}>
      <Text style={styles.sectionTitle}>
        {qrType === 'request' ? 'Request Amount' : 'Amount (Optional)'}
      </Text>
      <Text style={styles.sectionSubtitle}>
        {qrType === 'request' 
          ? 'Enter the amount you want to request'
          : 'Leave empty for open amount QR code'
        }
      </Text>
      
      <View style={styles.amountInputContainer}>
        <Text style={styles.currencyLabel}>UGX</Text>
        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={handleAmountChange}
          placeholder="0"
          placeholderTextColor={theme.colors.placeholder}
          keyboardType="numeric"
          maxLength={10}
        />
      </View>

      <View style={styles.quickAmounts}>
        {quickAmounts.map((quickAmount, index) => (
          <TouchableOpacity
            key={index}
            style={styles.quickAmountButton}
            onPress={() => handleQuickAmount(quickAmount)}
            activeOpacity={0.7}
          >
            <Text style={styles.quickAmountText}>
              {quickAmount >= 1000 ? `${quickAmount / 1000}K` : quickAmount}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderPurposeInput = () => (
    <View style={styles.inputSection}>
      <Text style={styles.sectionTitle}>Purpose</Text>
      <Text style={styles.sectionSubtitle}>
        What is this payment for?
      </Text>
      
      <View style={styles.purposeContainer}>
        <TextInput
          style={styles.purposeInput}
          value={purpose}
          onChangeText={setPurpose}
          placeholder="Enter payment purpose"
          placeholderTextColor={theme.colors.placeholder}
          maxLength={50}
        />
      </View>

      <View style={styles.purposeOptions}>
        {purposeOptions.map((option, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.purposeOption,
              purpose === option && styles.purposeOptionSelected
            ]}
            onPress={() => setPurpose(option)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.purposeOptionText,
              purpose === option && styles.purposeOptionTextSelected
            ]}>
              {option}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderQRCode = () => {
    if (!qrData) return null;

    return (
      <View style={styles.qrSection}>
        <Text style={styles.sectionTitle}>Your QR Code</Text>
        <Text style={styles.sectionSubtitle}>
          Share this QR code to receive payments
        </Text>
        
        <View style={styles.qrContainer}>
          <View style={styles.qrCodeWrapper}>
            <QRCode
              value={qrData.qrString}
              size={200}
              color={theme.colors.text}
              backgroundColor={theme.colors.surface}
            />
          </View>
          
          <View style={styles.qrInfo}>
            <Text style={styles.qrInfoTitle}>
              {qrType === 'request' ? 'Payment Request' : 'Receive Payment'}
            </Text>
            {amount && (
              <Text style={styles.qrAmount}>{formatCurrency(amount)}</Text>
            )}
            <Text style={styles.qrPurpose}>{purpose}</Text>
            <Text style={styles.qrInstructions}>
              Scan with JiraniPay to pay instantly
            </Text>
          </View>
        </View>

        <View style={styles.qrActions}>
          <TouchableOpacity
            style={styles.qrActionButton}
            onPress={shareQRCode}
            activeOpacity={0.8}
          >
            <Ionicons name="share-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.qrActionText}>Share</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.qrActionButton}
            onPress={saveQRCode}
            activeOpacity={0.8}
          >
            <Ionicons name="download-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.qrActionText}>Save</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.qrActionButton}
            onPress={() => setQrData(null)}
            activeOpacity={0.8}
          >
            <Ionicons name="refresh-outline" size={20} color={theme.colors.primary} />
            <Text style={styles.qrActionText}>New QR</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {qrType === 'request' ? 'Request Payment' : 'Receive Payment'}
        </Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {!qrData ? (
          <>
            {renderAmountInput()}
            {renderPurposeInput()}
            
            <View style={styles.generateSection}>
              <TouchableOpacity
                style={[
                  styles.generateButton,
                  loading && styles.generateButtonDisabled
                ]}
                onPress={generateQRCode}
                disabled={loading}
                activeOpacity={0.8}
              >
                <Text style={styles.generateButtonText}>
                  {loading ? 'Generating...' : 'Generate QR Code'}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          renderQRCode()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  inputSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 2,
    borderColor: theme.colors.primary + '30',
    marginBottom: 20,
  },
  currencyLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: 12,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  quickAmounts: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickAmountButton: {
    backgroundColor: theme.colors.primary + '10',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.primary + '30',
  },
  quickAmountText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  purposeContainer: {
    marginBottom: 16,
  },
  purposeInput: {
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 2,
    borderColor: theme.colors.primary + '30',
  },
  purposeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  purposeOption: {
    backgroundColor: theme.colors.inputBackground,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  purposeOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderColor: theme.colors.primary,
  },
  purposeOptionText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  purposeOptionTextSelected: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  generateSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  generateButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  generateButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  generateButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  qrSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
  },
  qrContainer: {
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 20,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  qrCodeWrapper: {
    padding: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginBottom: 20,
  },
  qrInfo: {
    alignItems: 'center',
  },
  qrInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  qrAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  qrPurpose: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  qrInstructions: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  qrActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  qrActionButton: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: theme.colors.primary + '10',
    minWidth: 80,
  },
  qrActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
    marginTop: 4,
  },
});

export default QRGeneratorScreen;
