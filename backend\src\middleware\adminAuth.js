/**
 * Admin Authentication Middleware
 * Handles authentication and authorization for admin routes
 */

const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('../services/database');
const redisService = require('../services/redis');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');

/**
 * Admin authentication middleware
 */
const adminAuthMiddleware = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Admin access token required');
    }

    const token = authHeader.substring(7);

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, config.jwt.secret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new AuthenticationError('Admin token expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new AuthenticationError('Invalid admin token');
      } else {
        throw new AuthenticationError('Token verification failed');
      }
    }

    // Check if token is blacklisted
    const isBlacklisted = await redisService.get(`blacklisted_token:${token}`);
    if (isBlacklisted) {
      throw new AuthenticationError('Token has been revoked');
    }

    // Get admin user details
    const supabase = databaseService.getSupabase();
    const { data: adminUser, error } = await supabase
      .from('admin_users')
      .select(`
        id,
        user_id,
        email,
        role,
        permissions,
        is_active,
        last_login,
        created_at,
        user_profiles!inner(
          full_name,
          phone_number
        )
      `)
      .eq('user_id', decoded.user_id)
      .eq('is_active', true)
      .single();

    if (error || !adminUser) {
      throw new AuthorizationError('Admin user not found or inactive');
    }

    // Check admin permissions
    if (!adminUser.permissions || !Array.isArray(adminUser.permissions)) {
      throw new AuthorizationError('Admin permissions not configured');
    }

    // Update last activity
    await updateAdminActivity(adminUser.id, req);

    // Attach admin user to request
    req.admin = {
      id: adminUser.id,
      user_id: adminUser.user_id,
      email: adminUser.email,
      role: adminUser.role,
      permissions: adminUser.permissions,
      fullName: adminUser.user_profiles.full_name,
      phoneNumber: adminUser.user_profiles.phone_number
    };

    // Log admin access
    logger.audit('Admin access', {
      adminId: adminUser.id,
      email: adminUser.email,
      role: adminUser.role,
      endpoint: req.path,
      method: req.method,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    logger.error('Admin authentication failed:', error);
    
    if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
      return res.status(error.statusCode).json({
        success: false,
        error: error.message,
        code: error.code
      });
    }

    return res.status(401).json({
      success: false,
      error: 'Admin authentication failed',
      code: 'ADMIN_AUTH_FAILED'
    });
  }
};

/**
 * Permission-based authorization middleware
 */
const requirePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.admin) {
        throw new AuthorizationError('Admin authentication required');
      }

      if (!req.admin.permissions.includes(permission) && !req.admin.permissions.includes('*')) {
        throw new AuthorizationError(`Permission required: ${permission}`);
      }

      next();
    } catch (error) {
      logger.error('Permission check failed:', error);
      
      return res.status(403).json({
        success: false,
        error: error.message,
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permission
      });
    }
  };
};

/**
 * Role-based authorization middleware
 */
const requireRole = (role) => {
  return (req, res, next) => {
    try {
      if (!req.admin) {
        throw new AuthorizationError('Admin authentication required');
      }

      if (req.admin.role !== role && req.admin.role !== 'super_admin') {
        throw new AuthorizationError(`Role required: ${role}`);
      }

      next();
    } catch (error) {
      logger.error('Role check failed:', error);
      
      return res.status(403).json({
        success: false,
        error: error.message,
        code: 'INSUFFICIENT_ROLE',
        required: role
      });
    }
  };
};

/**
 * Multiple permissions authorization (requires ANY of the permissions)
 */
const requireAnyPermission = (permissions) => {
  return (req, res, next) => {
    try {
      if (!req.admin) {
        throw new AuthorizationError('Admin authentication required');
      }

      const hasPermission = permissions.some(permission => 
        req.admin.permissions.includes(permission) || req.admin.permissions.includes('*')
      );

      if (!hasPermission) {
        throw new AuthorizationError(`One of these permissions required: ${permissions.join(', ')}`);
      }

      next();
    } catch (error) {
      logger.error('Permission check failed:', error);
      
      return res.status(403).json({
        success: false,
        error: error.message,
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions
      });
    }
  };
};

/**
 * Multiple permissions authorization (requires ALL of the permissions)
 */
const requireAllPermissions = (permissions) => {
  return (req, res, next) => {
    try {
      if (!req.admin) {
        throw new AuthorizationError('Admin authentication required');
      }

      const hasAllPermissions = permissions.every(permission => 
        req.admin.permissions.includes(permission) || req.admin.permissions.includes('*')
      );

      if (!hasAllPermissions) {
        throw new AuthorizationError(`All of these permissions required: ${permissions.join(', ')}`);
      }

      next();
    } catch (error) {
      logger.error('Permission check failed:', error);
      
      return res.status(403).json({
        success: false,
        error: error.message,
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions
      });
    }
  };
};

/**
 * Update admin activity tracking
 */
async function updateAdminActivity(adminId, req) {
  try {
    const supabase = databaseService.getSupabase();
    
    // Update last activity timestamp
    await supabase
      .from('admin_users')
      .update({
        last_activity: new Date().toISOString(),
        last_ip: req.ip
      })
      .eq('id', adminId);

    // Track admin session activity
    const activityData = {
      adminId,
      endpoint: req.path,
      method: req.method,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    };

    // Store recent activity in Redis (keep last 100 activities)
    await redisService.lpush(`admin_activity:${adminId}`, activityData);
    await redisService.ltrim(`admin_activity:${adminId}`, 0, 99);

  } catch (error) {
    logger.error('Failed to update admin activity:', error);
    // Don't throw error as this is not critical
  }
}

/**
 * Admin session validation middleware
 */
const validateAdminSession = async (req, res, next) => {
  try {
    if (!req.admin) {
      throw new AuthorizationError('Admin authentication required');
    }

    // Check if admin session is still valid
    const sessionKey = `admin_session:${req.admin.user_id}`;
    const sessionData = await redisService.get(sessionKey);

    if (!sessionData) {
      throw new AuthenticationError('Admin session expired');
    }

    // Check for concurrent sessions (if enabled)
    if (config.admin.preventConcurrentSessions) {
      const currentSessionId = req.headers['x-session-id'];
      if (sessionData.sessionId !== currentSessionId) {
        throw new AuthenticationError('Session invalidated by another login');
      }
    }

    next();
  } catch (error) {
    logger.error('Admin session validation failed:', error);
    
    return res.status(401).json({
      success: false,
      error: error.message,
      code: 'SESSION_INVALID'
    });
  }
};

/**
 * Rate limiting for admin endpoints
 */
const adminRateLimit = (maxRequests = 100, windowMinutes = 15) => {
  return async (req, res, next) => {
    try {
      if (!req.admin) {
        return next();
      }

      const identifier = `admin_rate_limit:${req.admin.id}`;
      const windowSeconds = windowMinutes * 60;

      const { allowed, remaining } = await redisService.checkRateLimit(
        identifier,
        maxRequests,
        windowSeconds
      );

      if (!allowed) {
        logger.warn('Admin rate limit exceeded', {
          adminId: req.admin.id,
          email: req.admin.email,
          endpoint: req.path,
          ipAddress: req.ip
        });

        return res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: windowSeconds
        });
      }

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests,
        'X-RateLimit-Remaining': remaining,
        'X-RateLimit-Reset': new Date(Date.now() + windowSeconds * 1000).toISOString()
      });

      next();
    } catch (error) {
      logger.error('Admin rate limiting failed:', error);
      next(); // Continue on rate limiting errors
    }
  };
};

module.exports = {
  adminAuthMiddleware,
  requirePermission,
  requireRole,
  requireAnyPermission,
  requireAllPermissions,
  validateAdminSession,
  adminRateLimit
};
