/**
 * Payment History & Tracking Service
 * Comprehensive tracking of bill payments with status updates,
 * receipt generation, and notification integration
 */

import { supabase } from './supabaseClient';
import digitalReceiptService from './digitalReceiptService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate, formatDateTime } from '../utils/dateUtils';

// Payment status tracking
const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
};

// History filter types
const FILTER_TYPES = {
  ALL: 'all',
  COMPLETED: 'completed',
  PENDING: 'pending',
  FAILED: 'failed',
  THIS_MONTH: 'this_month',
  LAST_MONTH: 'last_month',
  THIS_YEAR: 'this_year'
};

class PaymentHistoryService {
  constructor() {
    this.paymentStatus = PAYMENT_STATUS;
    this.filterTypes = FILTER_TYPES;
    this.statusUpdateListeners = new Map();
  }

  /**
   * Get comprehensive payment history for user
   */
  async getPaymentHistory(userId, options = {}) {
    try {
      const {
        limit = 20,
        offset = 0,
        status = null,
        billerId = null,
        startDate = null,
        endDate = null,
        searchQuery = null,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = options;

      console.log('📊 Fetching payment history:', { userId, options });

      let query = supabase
        .from('bill_payments')
        .select(`
          *,
          biller:billers(
            id, 
            display_name, 
            logo_url, 
            category:bill_categories(name, display_name, icon, color)
          ),
          history:bill_payment_history(
            old_status,
            new_status,
            status_reason,
            created_at,
            processing_time_ms
          )
        `)
        .eq('user_id', userId)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (status && status !== 'all') {
        query = query.eq('status', status);
      }

      if (billerId) {
        query = query.eq('biller_id', billerId);
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      if (searchQuery) {
        query = query.or(`reference.ilike.%${searchQuery}%,account_number.ilike.%${searchQuery}%,account_name.ilike.%${searchQuery}%`);
      }

      const { data: payments, error } = await query;

      if (error) throw error;

      // Format payment data
      const formattedPayments = payments.map(payment => this.formatPaymentData(payment));

      // Get summary statistics
      const summary = await this.getPaymentSummary(userId, options);

      return {
        success: true,
        payments: formattedPayments,
        summary,
        pagination: {
          limit,
          offset,
          hasMore: payments.length === limit,
          total: summary.totalCount
        }
      };
    } catch (error) {
      console.error('❌ Error fetching payment history:', error);
      throw error;
    }
  }

  /**
   * Get payment summary statistics
   */
  async getPaymentSummary(userId, options = {}) {
    try {
      const { startDate, endDate, status } = options;

      let query = supabase
        .from('bill_payments')
        .select('status, amount, fee, created_at')
        .eq('user_id', userId);

      if (startDate) query = query.gte('created_at', startDate);
      if (endDate) query = query.lte('created_at', endDate);
      if (status && status !== 'all') query = query.eq('status', status);

      const { data: payments, error } = await query;

      if (error) throw error;

      const summary = {
        totalCount: payments.length,
        totalAmount: 0,
        totalFees: 0,
        completedCount: 0,
        pendingCount: 0,
        failedCount: 0,
        completedAmount: 0,
        averageAmount: 0
      };

      payments.forEach(payment => {
        summary.totalAmount += payment.amount;
        summary.totalFees += payment.fee || 0;

        switch (payment.status) {
          case 'completed':
            summary.completedCount++;
            summary.completedAmount += payment.amount;
            break;
          case 'pending':
          case 'processing':
            summary.pendingCount++;
            break;
          case 'failed':
          case 'cancelled':
            summary.failedCount++;
            break;
        }
      });

      summary.averageAmount = summary.completedCount > 0 ? 
        summary.completedAmount / summary.completedCount : 0;

      return summary;
    } catch (error) {
      console.error('❌ Error getting payment summary:', error);
      return {
        totalCount: 0,
        totalAmount: 0,
        totalFees: 0,
        completedCount: 0,
        pendingCount: 0,
        failedCount: 0,
        completedAmount: 0,
        averageAmount: 0
      };
    }
  }

  /**
   * Get detailed payment information
   */
  async getPaymentDetails(paymentId, userId) {
    try {
      console.log('🔍 Fetching payment details:', { paymentId, userId });

      const { data: payment, error } = await supabase
        .from('bill_payments')
        .select(`
          *,
          biller:billers(
            id, 
            display_name, 
            logo_url, 
            category:bill_categories(name, display_name, icon, color),
            fee_type,
            processing_time
          ),
          history:bill_payment_history(
            old_status,
            new_status,
            status_reason,
            processed_by,
            processing_time_ms,
            external_transaction_id,
            external_response,
            created_at
          ),
          recurring:recurring_bill_payments(
            id,
            name,
            frequency,
            next_payment_date
          )
        `)
        .eq('id', paymentId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      // Get receipt information if available
      let receipt = null;
      try {
        const receiptResult = await digitalReceiptService.getReceiptByTransaction(paymentId, userId);
        if (receiptResult.success) {
          receipt = receiptResult.receipt;
        }
      } catch (receiptError) {
        console.log('No receipt found for payment:', paymentId);
      }

      return {
        success: true,
        payment: {
          ...this.formatPaymentData(payment),
          receipt,
          statusHistory: payment.history.map(h => ({
            oldStatus: h.old_status,
            newStatus: h.new_status,
            reason: h.status_reason,
            processedBy: h.processed_by,
            processingTime: h.processing_time_ms,
            externalTransactionId: h.external_transaction_id,
            externalResponse: h.external_response,
            timestamp: h.created_at
          })),
          recurringPayment: payment.recurring
        }
      };
    } catch (error) {
      console.error('❌ Error fetching payment details:', error);
      throw error;
    }
  }

  /**
   * Track payment status update
   */
  async trackStatusUpdate(paymentId, oldStatus, newStatus, metadata = {}) {
    try {
      console.log('📊 Tracking status update:', { paymentId, oldStatus, newStatus });

      // Log status change in history
      const historyEntry = {
        bill_payment_id: paymentId,
        old_status: oldStatus,
        new_status: newStatus,
        status_reason: metadata.reason,
        processed_by: metadata.processedBy || 'system',
        processing_time_ms: metadata.processingTime,
        external_transaction_id: metadata.externalTransactionId,
        external_response: metadata.externalResponse || {},
        created_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('bill_payment_history')
        .insert(historyEntry);

      if (error) throw error;

      // Notify status update listeners
      this.notifyStatusUpdateListeners(paymentId, newStatus, metadata);

      // Handle status-specific actions
      await this.handleStatusSpecificActions(paymentId, newStatus, metadata);

      return { success: true };
    } catch (error) {
      console.error('❌ Error tracking status update:', error);
      throw error;
    }
  }

  /**
   * Handle status-specific actions
   */
  async handleStatusSpecificActions(paymentId, status, metadata) {
    try {
      switch (status) {
        case this.paymentStatus.COMPLETED:
          await this.handleCompletedPayment(paymentId, metadata);
          break;

        case this.paymentStatus.FAILED:
          await this.handleFailedPayment(paymentId, metadata);
          break;

        case this.paymentStatus.REFUNDED:
          await this.handleRefundedPayment(paymentId, metadata);
          break;
      }
    } catch (error) {
      console.error('❌ Error handling status-specific actions:', error);
    }
  }

  /**
   * Handle completed payment
   */
  async handleCompletedPayment(paymentId, metadata) {
    try {
      // Get payment details
      const { data: payment, error } = await supabase
        .from('bill_payments')
        .select(`
          *,
          biller:billers(display_name)
        `)
        .eq('id', paymentId)
        .single();

      if (error) throw error;

      // Generate receipt
      await digitalReceiptService.generateReceipt(paymentId, payment.user_id);

      // Send success notification
      await enhancedNotificationService.sendNotification(payment.user_id, {
        type: 'bill_payment_success',
        title: 'Bill Payment Successful',
        content: `Your payment of ${formatCurrency(payment.amount)} to ${payment.biller.display_name} was successful`,
        data: {
          paymentId,
          reference: payment.reference,
          amount: payment.amount,
          biller: payment.biller.display_name
        }
      });

    } catch (error) {
      console.error('❌ Error handling completed payment:', error);
    }
  }

  /**
   * Handle failed payment
   */
  async handleFailedPayment(paymentId, metadata) {
    try {
      // Get payment details
      const { data: payment, error } = await supabase
        .from('bill_payments')
        .select(`
          *,
          biller:billers(display_name)
        `)
        .eq('id', paymentId)
        .single();

      if (error) throw error;

      // Send failure notification
      await enhancedNotificationService.sendNotification(payment.user_id, {
        type: 'transaction_failed',
        title: 'Bill Payment Failed',
        content: `Your payment of ${formatCurrency(payment.amount)} to ${payment.biller.display_name} failed. ${metadata.reason || ''}`,
        data: {
          paymentId,
          reference: payment.reference,
          amount: payment.amount,
          biller: payment.biller.display_name,
          error: metadata.reason
        }
      });

    } catch (error) {
      console.error('❌ Error handling failed payment:', error);
    }
  }

  /**
   * Handle refunded payment
   */
  async handleRefundedPayment(paymentId, metadata) {
    try {
      // Get payment details
      const { data: payment, error } = await supabase
        .from('bill_payments')
        .select(`
          *,
          biller:billers(display_name)
        `)
        .eq('id', paymentId)
        .single();

      if (error) throw error;

      // Send refund notification
      await enhancedNotificationService.sendNotification(payment.user_id, {
        type: 'payment_refunded',
        title: 'Payment Refunded',
        content: `Your payment of ${formatCurrency(payment.amount)} to ${payment.biller.display_name} has been refunded`,
        data: {
          paymentId,
          reference: payment.reference,
          amount: payment.amount,
          biller: payment.biller.display_name,
          refundReason: metadata.reason
        }
      });

    } catch (error) {
      console.error('❌ Error handling refunded payment:', error);
    }
  }

  /**
   * Get payment analytics
   */
  async getPaymentAnalytics(userId, period = 'month') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      const { data: payments, error } = await supabase
        .from('bill_payments')
        .select(`
          amount,
          fee,
          status,
          created_at,
          biller:billers(category:bill_categories(name, display_name))
        `)
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .eq('status', 'completed');

      if (error) throw error;

      // Analyze by category
      const categoryAnalytics = {};
      let totalSpent = 0;
      let totalFees = 0;

      payments.forEach(payment => {
        const categoryName = payment.biller.category.name;
        const amount = payment.amount;
        const fee = payment.fee || 0;

        totalSpent += amount;
        totalFees += fee;

        if (!categoryAnalytics[categoryName]) {
          categoryAnalytics[categoryName] = {
            name: payment.biller.category.display_name,
            amount: 0,
            count: 0,
            percentage: 0
          };
        }

        categoryAnalytics[categoryName].amount += amount;
        categoryAnalytics[categoryName].count++;
      });

      // Calculate percentages
      Object.values(categoryAnalytics).forEach(category => {
        category.percentage = totalSpent > 0 ? (category.amount / totalSpent) * 100 : 0;
      });

      return {
        success: true,
        analytics: {
          period,
          totalSpent,
          totalFees,
          totalPayments: payments.length,
          averagePayment: payments.length > 0 ? totalSpent / payments.length : 0,
          categoryBreakdown: Object.values(categoryAnalytics)
            .sort((a, b) => b.amount - a.amount)
        }
      };
    } catch (error) {
      console.error('❌ Error getting payment analytics:', error);
      throw error;
    }
  }

  /**
   * Export payment history
   */
  async exportPaymentHistory(userId, options = {}) {
    try {
      const { format = 'csv', startDate, endDate } = options;

      // Get all payments for the period
      const historyResult = await this.getPaymentHistory(userId, {
        limit: 1000, // Large limit for export
        startDate,
        endDate,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });

      if (!historyResult.success) {
        throw new Error('Failed to fetch payment history');
      }

      const payments = historyResult.payments;

      if (format === 'csv') {
        return this.exportAsCSV(payments);
      } else if (format === 'json') {
        return this.exportAsJSON(payments);
      } else {
        throw new Error('Unsupported export format');
      }
    } catch (error) {
      console.error('❌ Error exporting payment history:', error);
      throw error;
    }
  }

  /**
   * Export as CSV
   */
  exportAsCSV(payments) {
    const headers = [
      'Date',
      'Reference',
      'Biller',
      'Account Number',
      'Amount',
      'Fee',
      'Total',
      'Status',
      'Payment Method'
    ];

    const rows = payments.map(payment => [
      formatDate(payment.createdAt),
      payment.reference,
      payment.biller.name,
      payment.accountNumber,
      payment.amount,
      payment.fee,
      payment.totalAmount,
      payment.status,
      payment.paymentMethod
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return {
      success: true,
      format: 'csv',
      content: csvContent,
      filename: `jiranipay_payment_history_${Date.now()}.csv`
    };
  }

  /**
   * Export as JSON
   */
  exportAsJSON(payments) {
    const exportData = {
      exportedAt: new Date().toISOString(),
      totalPayments: payments.length,
      payments: payments.map(payment => ({
        date: payment.createdAt,
        reference: payment.reference,
        biller: payment.biller.name,
        category: payment.biller.category,
        accountNumber: payment.accountNumber,
        accountName: payment.accountName,
        amount: payment.amount,
        fee: payment.fee,
        totalAmount: payment.totalAmount,
        currency: payment.currency,
        status: payment.status,
        paymentMethod: payment.paymentMethod
      }))
    };

    return {
      success: true,
      format: 'json',
      content: JSON.stringify(exportData, null, 2),
      filename: `jiranipay_payment_history_${Date.now()}.json`
    };
  }

  /**
   * Format payment data for display
   */
  formatPaymentData(payment) {
    return {
      id: payment.id,
      reference: payment.reference,
      externalReference: payment.external_reference,
      biller: {
        id: payment.biller.id,
        name: payment.biller.display_name,
        logo: payment.biller.logo_url,
        category: payment.biller.category?.display_name || 'Other'
      },
      accountNumber: payment.account_number,
      accountName: payment.account_name,
      amount: payment.amount,
      fee: payment.fee || 0,
      totalAmount: payment.total_amount,
      currency: payment.currency,
      status: payment.status,
      paymentMethod: payment.payment_method,
      accountVerified: payment.account_verified,
      verificationData: payment.verification_data,
      scheduledAt: payment.scheduled_at,
      processedAt: payment.processed_at,
      completedAt: payment.completed_at,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at,
      errorCode: payment.error_code,
      errorMessage: payment.error_message,
      retryCount: payment.retry_count,
      metadata: payment.metadata
    };
  }

  /**
   * Subscribe to status updates
   */
  subscribeToStatusUpdates(paymentId, callback) {
    if (!this.statusUpdateListeners.has(paymentId)) {
      this.statusUpdateListeners.set(paymentId, new Set());
    }
    this.statusUpdateListeners.get(paymentId).add(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.statusUpdateListeners.get(paymentId);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.statusUpdateListeners.delete(paymentId);
        }
      }
    };
  }

  /**
   * Notify status update listeners
   */
  notifyStatusUpdateListeners(paymentId, newStatus, metadata) {
    const listeners = this.statusUpdateListeners.get(paymentId);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(paymentId, newStatus, metadata);
        } catch (error) {
          console.error('❌ Error in status update listener:', error);
        }
      });
    }
  }
}

export default new PaymentHistoryService();
