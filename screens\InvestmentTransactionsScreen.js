/**
 * Investment Transactions Screen
 * Screen for viewing and managing investment transactions
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentTransactionsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');

  const filters = [
    { key: 'all', label: 'All' },
    { key: 'buy', label: 'Buys' },
    { key: 'sell', label: 'Sells' },
    { key: 'dividend', label: 'Dividends' },
    { key: 'deposit', label: 'Deposits' },
    { key: 'withdrawal', label: 'Withdrawals' }
  ];

  // East African investment transaction types
  const transactionTypes = [
    { key: 'all', label: 'All Transactions' },
    { key: 'buy', label: 'Buy Orders' },
    { key: 'sell', label: 'Sell Orders' },
    { key: 'dividend', label: 'Dividends' },
    { key: 'transfer', label: 'Transfers' }
  ];

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view transactions');
        navigation.goBack();
        return;
      }

      // Load real investment transactions from service
      const result = await investmentPortfolioService.getUserTransactions(userId, {
        limit: 50,
        orderBy: 'created_at',
        ascending: false
      });

      if (result.success) {
        setTransactions(result.transactions || []);
      } else {
        console.error('❌ Error loading transactions:', result.error);
        Alert.alert('Error', 'Failed to load investment transactions');
        setTransactions([]);
      }

    } catch (error) {
      console.error('❌ Error loading transactions:', error);
      Alert.alert('Error', 'Failed to load transactions');
      setTransactions([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadTransactions(true);
  };

  const handleExportTransactions = () => {
    Alert.alert(
      'Export Transactions',
      'Choose export format:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'PDF', onPress: () => exportTransactionsPDF() },
        { text: 'Excel/CSV', onPress: () => exportTransactionsCSV() }
      ]
    );
  };

  const exportTransactionsPDF = async () => {
    try {
      setLoading(true);

      if (!transactions || transactions.length === 0) {
        Alert.alert('No Data', 'No transactions available to export');
        return;
      }

      // Generate HTML content for PDF
      const htmlContent = generateTransactionsHTML();

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });

      // Share the PDF
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Investment Transactions Report'
        });

        Alert.alert('Export Successful', 'Investment transactions exported as PDF successfully!');
      } else {
        Alert.alert('Export Successful', 'PDF report has been generated successfully!');
      }

    } catch (error) {
      console.error('❌ Error exporting PDF:', error);
      Alert.alert('Export Failed', 'Failed to export transactions as PDF');
    } finally {
      setLoading(false);
    }
  };

  const exportTransactionsCSV = async () => {
    try {
      setLoading(true);

      if (!transactions || transactions.length === 0) {
        Alert.alert('No Data', 'No transactions available to export');
        return;
      }

      // Generate CSV content
      const csvContent = generateTransactionsCSV();

      // Save to device storage
      const fileName = `investment_transactions_${new Date().toISOString().split('T')[0]}.csv`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8
      });

      // Share the CSV file
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'Share Investment Transactions (Excel)'
        });

        Alert.alert('Export Successful', 'Investment transactions exported as CSV successfully!');
      } else {
        Alert.alert('Export Successful', 'CSV file has been generated successfully!');
      }

    } catch (error) {
      console.error('❌ Error exporting CSV:', error);
      Alert.alert('Export Failed', 'Failed to export transactions as CSV');
    } finally {
      setLoading(false);
    }
  };

  const generateTransactionsHTML = () => {
    const currentDate = new Date().toLocaleDateString();

    let html = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { color: #2E7D32; font-size: 24px; font-weight: bold; }
            .subtitle { color: #666; font-size: 14px; margin-top: 5px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .amount { text-align: right; }
            .buy { color: #2E7D32; }
            .sell { color: #D32F2F; }
            .dividend { color: #1976D2; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">JiraniPay Investment Transactions</div>
            <div class="subtitle">Generated: ${currentDate}</div>
          </div>

          <table>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Asset</th>
              <th>Quantity</th>
              <th>Price</th>
              <th>Total Amount</th>
              <th>Fees</th>
              <th>Status</th>
            </tr>
    `;

    transactions.forEach(transaction => {
      const typeClass = transaction.type === 'buy' ? 'buy' : transaction.type === 'sell' ? 'sell' : 'dividend';
      html += `
        <tr>
          <td>${formatDate(transaction.createdAt)}</td>
          <td class="${typeClass}">${transaction.type.toUpperCase()}</td>
          <td>${transaction.asset?.symbol || 'N/A'}</td>
          <td class="amount">${transaction.quantity || 0}</td>
          <td class="amount">${formatCurrency(transaction.price || 0, transaction.currency || 'UGX')}</td>
          <td class="amount">${formatCurrency(transaction.totalAmount || 0, transaction.currency || 'UGX')}</td>
          <td class="amount">${formatCurrency(transaction.fees || 0, transaction.currency || 'UGX')}</td>
          <td>${transaction.status || 'Completed'}</td>
        </tr>
      `;
    });

    html += `
          </table>
        </body>
      </html>
    `;

    return html;
  };

  const generateTransactionsCSV = () => {
    let csv = 'Date,Type,Asset,Quantity,Price,Total Amount,Fees,Status,Currency\n';

    transactions.forEach(transaction => {
      csv += `${formatDate(transaction.createdAt)},`;
      csv += `${transaction.type.toUpperCase()},`;
      csv += `${transaction.asset?.symbol || 'N/A'},`;
      csv += `${transaction.quantity || 0},`;
      csv += `${transaction.price || 0},`;
      csv += `${transaction.totalAmount || 0},`;
      csv += `${transaction.fees || 0},`;
      csv += `${transaction.status || 'Completed'},`;
      csv += `${transaction.currency || 'UGX'}\n`;
    });

    return csv;
  };

  const getTransactionIcon = (type) => {
    const icons = {
      buy: 'arrow-up-circle',
      sell: 'arrow-down-circle',
      dividend: 'cash',
      deposit: 'add-circle',
      withdrawal: 'remove-circle'
    };
    return icons[type] || 'help-circle';
  };

  const getTransactionColor = (type) => {
    const colors = {
      buy: theme.colors.error,
      sell: theme.colors.success,
      dividend: theme.colors.primary,
      deposit: theme.colors.success,
      withdrawal: theme.colors.error
    };
    return colors[type] || theme.colors.textSecondary;
  };

  const getTransactionSign = (type) => {
    return ['sell', 'dividend', 'deposit'].includes(type) ? '+' : '-';
  };

  const renderFilterBar = () => (
    <View style={styles.filterContainer}>
      <FlatList
        data={filters}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === item.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(item.key)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedFilter === item.key && styles.filterButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderTransactionCard = ({ item: transaction }) => (
    <TouchableOpacity 
      style={styles.transactionCard}
      onPress={() => Alert.alert('Transaction Details', `Transaction ID: ${transaction.id}`)}
    >
      <View style={styles.transactionHeader}>
        <View style={styles.transactionInfo}>
          <View style={styles.transactionTitleRow}>
            <View style={[styles.transactionIcon, { backgroundColor: getTransactionColor(transaction.type) + '20' }]}>
              <Ionicons 
                name={getTransactionIcon(transaction.type)} 
                size={16} 
                color={getTransactionColor(transaction.type)} 
              />
            </View>
            <View style={styles.transactionDetails}>
              <Text style={styles.transactionType}>
                {transaction.type.toUpperCase()}
                {transaction.asset && ` • ${transaction.asset.symbol}`}
              </Text>
              <Text style={styles.transactionDate}>
                {formatDate(transaction.createdAt)} • {transaction.portfolioName}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.transactionAmount}>
          <Text style={[
            styles.transactionValue,
            { color: getTransactionColor(transaction.type) }
          ]}>
            {getTransactionSign(transaction.type)}{formatCurrency(transaction.totalAmount, transaction.currency || 'UGX')}
          </Text>
          <View style={styles.transactionStatus}>
            <View style={[styles.statusDot, { backgroundColor: theme.colors.success }]} />
            <Text style={styles.statusText}>{transaction.status}</Text>
          </View>
        </View>
      </View>
      
      {transaction.asset && (
        <View style={styles.transactionBody}>
          <Text style={styles.assetName}>{transaction.asset.name}</Text>
          <View style={styles.transactionMetrics}>
            <View style={styles.metric}>
              <Text style={styles.metricLabel}>Quantity</Text>
              <Text style={styles.metricValue}>{transaction.quantity.toFixed(4)}</Text>
            </View>
            <View style={styles.metric}>
              <Text style={styles.metricLabel}>Price</Text>
              <Text style={styles.metricValue}>{formatCurrency(transaction.price, transaction.currency || 'UGX')}</Text>
            </View>
            {transaction.fees > 0 && (
              <View style={styles.metric}>
                <Text style={styles.metricLabel}>Fees</Text>
                <Text style={styles.metricValue}>{formatCurrency(transaction.fees, transaction.currency || 'UGX')}</Text>
              </View>
            )}
          </View>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderSummaryCard = () => {
    const filteredTransactions = selectedFilter === 'all' 
      ? transactions 
      : transactions.filter(t => t.type === selectedFilter);

    const totalBuys = filteredTransactions
      .filter(t => t.type === 'buy')
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const totalSells = filteredTransactions
      .filter(t => t.type === 'sell')
      .reduce((sum, t) => sum + t.totalAmount, 0);

    const totalFees = filteredTransactions
      .reduce((sum, t) => sum + t.fees, 0);

    return (
      <View style={styles.summaryCard}>
        <Text style={styles.summaryTitle}>
          {selectedFilter === 'all' ? 'All Transactions' : `${filters.find(f => f.key === selectedFilter)?.label} Summary`}
        </Text>
        
        <View style={styles.summaryMetrics}>
          <View style={styles.summaryMetric}>
            <Text style={styles.summaryMetricValue}>{filteredTransactions.length}</Text>
            <Text style={styles.summaryMetricLabel}>Total</Text>
          </View>
          
          {selectedFilter === 'all' && (
            <>
              <View style={styles.summaryMetric}>
                <Text style={[styles.summaryMetricValue, { color: theme.colors.error }]}>
                  {formatCurrency(totalBuys, 'UGX')}
                </Text>
                <Text style={styles.summaryMetricLabel}>Invested</Text>
              </View>
              
              <View style={styles.summaryMetric}>
                <Text style={[styles.summaryMetricValue, { color: theme.colors.success }]}>
                  {formatCurrency(totalSells, 'UGX')}
                </Text>
                <Text style={styles.summaryMetricLabel}>Realized</Text>
              </View>
              
              <View style={styles.summaryMetric}>
                <Text style={styles.summaryMetricValue}>
                  {formatCurrency(totalFees, 'UGX')}
                </Text>
                <Text style={styles.summaryMetricLabel}>Fees</Text>
              </View>
            </>
          )}
        </View>
      </View>
    );
  };

  const filteredTransactions = selectedFilter === 'all' 
    ? transactions 
    : transactions.filter(t => t.type === selectedFilter);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading transactions...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Investment Transactions</Text>
        <TouchableOpacity onPress={handleExportTransactions}>
          <Ionicons name="download-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filter Bar */}
      {renderFilterBar()}

      {/* Content */}
      <FlatList
        data={filteredTransactions}
        renderItem={renderTransactionCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderSummaryCard}
        contentContainerStyle={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyTitle}>No transactions found</Text>
            <Text style={styles.emptyDescription}>
              {selectedFilter === 'all' 
                ? 'Your investment transactions will appear here'
                : `No ${selectedFilter} transactions found`
              }
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryMetric: {
    alignItems: 'center',
    flex: 1,
  },
  summaryMetricValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  summaryMetricLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  transactionCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  transactionInfo: {
    flex: 1,
    marginRight: 12,
  },
  transactionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionType: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  transactionValue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  transactionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  statusText: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    textTransform: 'capitalize',
  },
  transactionBody: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  assetName: {
    fontSize: 12,
    color: theme.colors.text,
    marginBottom: 8,
  },
  transactionMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
});

export default InvestmentTransactionsScreen;
