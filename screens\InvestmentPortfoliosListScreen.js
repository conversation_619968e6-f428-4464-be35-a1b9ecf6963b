/**
 * Investment Portfolios List Screen
 * Screen for viewing all investment portfolios with filtering and sorting
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentPortfoliosListScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [portfolios, setPortfolios] = useState([]);
  const [filteredPortfolios, setFilteredPortfolios] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('value');

  const filters = [
    { key: 'all', label: 'All Portfolios' },
    { key: 'general', label: 'General' },
    { key: 'retirement', label: 'Retirement' },
    { key: 'education', label: 'Education' },
    { key: 'aggressive', label: 'Aggressive' },
    { key: 'conservative', label: 'Conservative' },
    { key: 'balanced', label: 'Balanced' }
  ];

  const sortOptions = [
    { key: 'value', label: 'Total Value' },
    { key: 'return', label: 'Total Return' },
    { key: 'name', label: 'Name' },
    { key: 'created', label: 'Date Created' }
  ];

  // Mock portfolio data
  const mockPortfolios = [
    {
      id: '1',
      portfolioName: 'Growth Portfolio',
      portfolioType: 'aggressive',
      totalValue: 25000.00,
      totalInvested: 20000.00,
      totalGainsLosses: 5000.00,
      totalReturn: 25.0,
      dailyReturn: 1.2,
      cashBalance: 500.00,
      riskLevel: 'aggressive',
      riskScore: 8,
      createdAt: new Date(Date.now() - 86400000 * 30).toISOString(),
      isManaged: false,
      currency: 'USD'
    },
    {
      id: '2',
      portfolioName: 'Retirement Fund',
      portfolioType: 'retirement',
      totalValue: 45000.00,
      totalInvested: 40000.00,
      totalGainsLosses: 5000.00,
      totalReturn: 12.5,
      dailyReturn: 0.3,
      cashBalance: 1200.00,
      riskLevel: 'moderate',
      riskScore: 5,
      createdAt: new Date(Date.now() - 86400000 * 90).toISOString(),
      isManaged: true,
      currency: 'USD'
    },
    {
      id: '3',
      portfolioName: 'Conservative Savings',
      portfolioType: 'conservative',
      totalValue: 15000.00,
      totalInvested: 14500.00,
      totalGainsLosses: 500.00,
      totalReturn: 3.4,
      dailyReturn: 0.1,
      cashBalance: 800.00,
      riskLevel: 'conservative',
      riskScore: 2,
      createdAt: new Date(Date.now() - 86400000 * 60).toISOString(),
      isManaged: false,
      currency: 'USD'
    }
  ];

  useEffect(() => {
    loadPortfolios();
  }, []);

  useEffect(() => {
    filterAndSortPortfolios();
  }, [portfolios, selectedFilter, sortBy]);

  const loadPortfolios = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view portfolios');
        navigation.goBack();
        return;
      }

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPortfolios(mockPortfolios);

    } catch (error) {
      console.error('❌ Error loading portfolios:', error);
      Alert.alert('Error', 'Failed to load portfolios');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterAndSortPortfolios = () => {
    let filtered = [...portfolios];

    // Apply filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(portfolio => portfolio.portfolioType === selectedFilter);
    }

    // Apply sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'value':
          return b.totalValue - a.totalValue;
        case 'return':
          return b.totalReturn - a.totalReturn;
        case 'name':
          return a.portfolioName.localeCompare(b.portfolioName);
        case 'created':
          return new Date(b.createdAt) - new Date(a.createdAt);
        default:
          return 0;
      }
    });

    setFilteredPortfolios(filtered);
  };

  const onRefresh = () => {
    loadPortfolios(true);
  };

  const handlePortfolioPress = (portfolio) => {
    navigation.navigate('InvestmentPortfolioDetails', { portfolioId: portfolio.id });
  };

  const handleCreatePortfolio = () => {
    navigation.navigate('InvestmentPortfolioCreation');
  };

  const getPortfolioTypeIcon = (portfolioType) => {
    const icons = {
      general: 'briefcase',
      retirement: 'time',
      education: 'school',
      aggressive: 'trending-up',
      conservative: 'shield-checkmark',
      balanced: 'scale'
    };
    return icons[portfolioType] || 'briefcase';
  };

  const getPortfolioTypeColor = (portfolioType) => {
    const colors = {
      general: '#4ECDC4',
      retirement: '#6C5CE7',
      education: '#FECA57',
      aggressive: '#FF6B35',
      conservative: '#96CEB4',
      balanced: '#45B7D1'
    };
    return colors[portfolioType] || '#4ECDC4';
  };

  const getPerformanceColor = (value) => {
    if (value > 0) return theme.colors.success;
    if (value < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderFilterBar = () => (
    <View style={styles.filterContainer}>
      <FlatList
        data={filters}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === item.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(item.key)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedFilter === item.key && styles.filterButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderSortBar = () => (
    <View style={styles.sortContainer}>
      <Text style={styles.sortLabel}>Sort by:</Text>
      <FlatList
        data={sortOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.sortButton,
              sortBy === item.key && styles.sortButtonActive
            ]}
            onPress={() => setSortBy(item.key)}
          >
            <Text style={[
              styles.sortButtonText,
              sortBy === item.key && styles.sortButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderPortfolioCard = ({ item: portfolio }) => (
    <TouchableOpacity 
      style={styles.portfolioCard}
      onPress={() => handlePortfolioPress(portfolio)}
    >
      <View style={styles.portfolioHeader}>
        <View style={[styles.portfolioIcon, { backgroundColor: getPortfolioTypeColor(portfolio.portfolioType) }]}>
          <Ionicons name={getPortfolioTypeIcon(portfolio.portfolioType)} size={20} color={theme.colors.white} />
        </View>
        <View style={styles.portfolioInfo}>
          <Text style={styles.portfolioName}>{portfolio.portfolioName}</Text>
          <Text style={styles.portfolioType}>
            {portfolio.portfolioType.charAt(0).toUpperCase() + portfolio.portfolioType.slice(1)} Portfolio
          </Text>
          <Text style={styles.portfolioCreated}>Created {formatDate(portfolio.createdAt)}</Text>
        </View>
        <View style={styles.portfolioActions}>
          <Text style={styles.portfolioValue}>
            {formatCurrency(portfolio.totalValue, portfolio.currency)}
          </Text>
          <View style={styles.returnContainer}>
            <Text style={[styles.returnText, { color: getPerformanceColor(portfolio.totalGainsLosses) }]}>
              {portfolio.totalGainsLosses >= 0 ? '+' : ''}{formatCurrency(portfolio.totalGainsLosses, portfolio.currency)}
            </Text>
            <Text style={[styles.returnPercent, { color: getPerformanceColor(portfolio.totalReturn) }]}>
              ({portfolio.totalReturn >= 0 ? '+' : ''}{portfolio.totalReturn.toFixed(1)}%)
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.portfolioMetrics}>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Invested</Text>
          <Text style={styles.metricValue}>{formatCurrency(portfolio.totalInvested, portfolio.currency)}</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Cash</Text>
          <Text style={styles.metricValue}>{formatCurrency(portfolio.cashBalance, portfolio.currency)}</Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Today</Text>
          <Text style={[styles.metricValue, { color: getPerformanceColor(portfolio.dailyReturn) }]}>
            {portfolio.dailyReturn >= 0 ? '+' : ''}{portfolio.dailyReturn.toFixed(2)}%
          </Text>
        </View>
        <View style={styles.metric}>
          <Text style={styles.metricLabel}>Risk</Text>
          <Text style={styles.metricValue}>{portfolio.riskScore}/10</Text>
        </View>
      </View>
      
      <View style={styles.portfolioFooter}>
        {portfolio.isManaged && (
          <View style={styles.managedBadge}>
            <Ionicons name="shield-checkmark" size={12} color={theme.colors.primary} />
            <Text style={styles.managedText}>Managed</Text>
          </View>
        )}
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="briefcase-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>
        {selectedFilter === 'all' ? 'No Investment Portfolios' : `No ${selectedFilter} Portfolios`}
      </Text>
      <Text style={styles.emptyDescription}>
        {selectedFilter === 'all' 
          ? 'Create your first investment portfolio to start building wealth'
          : `You don't have any ${selectedFilter} portfolios yet`
        }
      </Text>
      <TouchableOpacity style={styles.createButton} onPress={handleCreatePortfolio}>
        <Text style={styles.createButtonText}>Create Portfolio</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading portfolios...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>All Portfolios</Text>
        <TouchableOpacity onPress={handleCreatePortfolio}>
          <Ionicons name="add" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filters and Sort */}
      {renderFilterBar()}
      {renderSortBar()}

      {/* Portfolios List */}
      {filteredPortfolios.length > 0 ? (
        <FlatList
          data={filteredPortfolios}
          renderItem={renderPortfolioCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderEmptyState()
      )}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  sortLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 12,
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: theme.colors.background,
    marginRight: 8,
  },
  sortButtonActive: {
    backgroundColor: theme.colors.primary + '20',
  },
  sortButtonText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  sortButtonTextActive: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  portfolioCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  portfolioHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  portfolioIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  portfolioInfo: {
    flex: 1,
  },
  portfolioName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  portfolioType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  portfolioCreated: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  portfolioActions: {
    alignItems: 'flex-end',
  },
  portfolioValue: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  returnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  returnText: {
    fontSize: 12,
    fontWeight: '500',
    marginRight: 4,
  },
  returnPercent: {
    fontSize: 12,
    fontWeight: '500',
  },
  portfolioMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  metricValue: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
  },
  portfolioFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  managedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  managedText: {
    fontSize: 10,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  moreButton: {
    padding: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default InvestmentPortfoliosListScreen;
