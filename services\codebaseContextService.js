/**
 * Codebase Context Service for JiraniPay
 * Provides deep understanding of app architecture, components, and functionality
 */

class CodebaseContextService {
  constructor() {
    this.appArchitecture = this.initializeAppArchitecture();
    this.componentMap = this.initializeComponentMap();
    this.serviceMap = this.initializeServiceMap();
    this.featureImplementations = this.initializeFeatureImplementations();
    this.userFlowMappings = this.initializeUserFlowMappings();
  }

  // =====================================================
  // APP ARCHITECTURE UNDERSTANDING
  // =====================================================

  initializeAppArchitecture() {
    return {
      navigation: {
        structure: 'Stack Navigator with Tab Navigator',
        mainNavigator: 'MainNavigator.js',
        tabNavigation: 'BottomNavigation.js',
        authFlow: ['Onboarding', 'Login', 'Register', 'CompleteProfile'],
        mainFlow: ['Dashboard', 'Bills', 'QR', 'Wallet', 'Profile'],
        modalScreens: ['TransferAmount', 'TransferConfirmation', 'QRScanner', 'ContactSupport']
      },

      dataFlow: {
        authentication: 'authService.js',
        wallet: 'walletService.js',
        transactions: 'transactionService.js',
        bills: 'billPaymentService.js',
        qr: 'qrCodeService.js',
        ai: 'aiChatService.js',
        storage: 'AsyncStorage + Supabase'
      },

      stateManagement: {
        type: 'React Hooks + Context',
        globalState: 'LanguageProvider',
        localState: 'useState, useEffect',
        persistence: 'AsyncStorage'
      },

      security: {
        authentication: 'Biometric + PIN + Password',
        dataEncryption: 'Bank-level encryption',
        storage: 'Secure AsyncStorage',
        api: 'Supabase with RLS'
      }
    };
  }

  // =====================================================
  // COMPONENT MAPPING
  // =====================================================

  initializeComponentMap() {
    return {
      screens: {
        'DashboardScreen': {
          purpose: 'Main app dashboard with wallet overview and quick actions',
          keyFeatures: ['wallet_card', 'quick_actions', 'recent_transactions', 'ai_insights'],
          navigation: 'MainNavigator home tab',
          components: ['WalletCard', 'QuickActions', 'TransactionList', 'AIInsights'],
          dataServices: ['walletService', 'transactionService', 'aiChatService']
        },

        'WalletScreen': {
          purpose: 'Detailed wallet management and transaction history',
          keyFeatures: ['balance_display', 'transaction_history', 'top_up', 'settings'],
          navigation: 'MainNavigator wallet tab',
          components: ['WalletCard', 'TransactionHistory', 'TopUpButton'],
          dataServices: ['walletService', 'transactionService', 'currencyService']
        },

        'BillPaymentScreen': {
          purpose: 'Comprehensive bill payment system for utilities and services',
          keyFeatures: ['provider_selection', 'account_validation', 'payment_processing', 'receipts'],
          navigation: 'MainNavigator bills tab',
          components: ['ProviderGrid', 'AccountInput', 'PaymentForm'],
          dataServices: ['billPaymentService', 'walletService']
        },

        'SendMoneyScreen': {
          purpose: 'Send money to contacts or phone numbers',
          keyFeatures: ['recipient_selection', 'amount_input', 'confirmation', 'success'],
          navigation: 'Stack navigation from dashboard',
          components: ['ContactPicker', 'AmountInput', 'ConfirmationModal'],
          dataServices: ['transactionService', 'contactService', 'walletService']
        },

        'QRScannerScreen': {
          purpose: 'Scan QR codes for payments and contact sharing',
          keyFeatures: ['camera_scanner', 'qr_detection', 'payment_processing'],
          navigation: 'MainNavigator qr tab or modal',
          components: ['CameraView', 'QRDetector', 'PaymentModal'],
          dataServices: ['qrCodeService', 'transactionService']
        },

        'ProfileScreen': {
          purpose: 'User profile management and app settings',
          keyFeatures: ['profile_info', 'security_settings', 'support_access', 'app_settings'],
          navigation: 'MainNavigator profile tab',
          components: ['ProfileHeader', 'MenuItems', 'SettingsToggles'],
          dataServices: ['authService', 'profileService']
        },

        'ContactSupportScreen': {
          purpose: 'Multiple support channels and help options',
          keyFeatures: ['support_channels', 'ai_chat', 'ticket_creation', 'faq_access'],
          navigation: 'Stack navigation from profile',
          components: ['SupportOptions', 'QuickActions', 'ContactMethods'],
          dataServices: ['supportService', 'aiChatService']
        },

        'AIChatScreen': {
          purpose: 'AI assistant chat interface for user support',
          keyFeatures: ['chat_interface', 'message_history', 'quick_replies', 'typing_indicators'],
          navigation: 'Stack navigation from support',
          components: ['MessageList', 'InputField', 'QuickReplies', 'TypingIndicator'],
          dataServices: ['aiChatService', 'enhancedAIKnowledgeBase']
        }
      },

      reusableComponents: {
        'UnifiedBackButton': 'Consistent back navigation across screens',
        'BottomNavigation': 'Main app tab navigation',
        'WalletCard': 'Wallet balance display with show/hide toggle',
        'TransactionItem': 'Individual transaction display component',
        'LoadingSpinner': 'Loading state indicator',
        'ErrorBoundary': 'Error handling wrapper',
        'SkeletonLoader': 'Loading placeholder animations'
      }
    };
  }

  // =====================================================
  // SERVICE MAPPING
  // =====================================================

  initializeServiceMap() {
    return {
      'authService': {
        purpose: 'User authentication and session management',
        methods: ['login', 'register', 'logout', 'biometricAuth', 'resetPassword'],
        dataHandling: 'User credentials, session tokens, biometric data'
      },

      'walletService': {
        purpose: 'Wallet balance and transaction management',
        methods: ['getBalance', 'topUp', 'withdraw', 'getTransactions', 'updateLimits'],
        dataHandling: 'Wallet balance, transaction history, spending limits'
      },

      'transactionService': {
        purpose: 'Money transfer and payment processing',
        methods: ['sendMoney', 'processPayment', 'getHistory', 'validateRecipient'],
        dataHandling: 'Transaction records, recipient validation, payment processing'
      },

      'billPaymentService': {
        purpose: 'Utility bill payments and service management',
        methods: ['getProviders', 'validateAccount', 'processBillPayment', 'getHistory'],
        dataHandling: 'Bill providers, account validation, payment processing'
      },

      'qrCodeService': {
        purpose: 'QR code generation and scanning functionality',
        methods: ['generateQR', 'scanQR', 'processPayment', 'validateQRData'],
        dataHandling: 'QR code data, payment information, merchant details'
      },

      'aiChatService': {
        purpose: 'AI assistant chat functionality',
        methods: ['initializeChat', 'sendMessage', 'generateResponse', 'saveChatHistory'],
        dataHandling: 'Chat history, AI responses, user queries'
      },

      'enhancedAIKnowledgeBase': {
        purpose: 'Comprehensive app knowledge for AI responses',
        methods: ['findRelevantFAQ', 'getFeatureGuide', 'analyzeUserIntent', 'getContextualResponse'],
        dataHandling: 'FAQ data, feature guides, troubleshooting, navigation flows'
      }
    };
  }

  // =====================================================
  // FEATURE IMPLEMENTATIONS
  // =====================================================

  initializeFeatureImplementations() {
    return {
      wallet_management: {
        screens: ['DashboardScreen', 'WalletScreen', 'TopUpScreen'],
        services: ['walletService', 'transactionService', 'currencyService'],
        features: [
          'Balance display with show/hide toggle',
          'Multiple top-up methods (bank, mobile money, cards)',
          'Transaction history with filtering',
          'Spending limits and analytics',
          'Multi-currency support (UGX primary)'
        ],
        userFlow: 'Dashboard → Wallet → View balance/transactions → Top up/Settings'
      },

      money_transfer: {
        screens: ['SendMoneyScreen', 'TransferAmountScreen', 'TransferConfirmationScreen', 'TransferSuccessScreen'],
        services: ['transactionService', 'contactService', 'walletService'],
        features: [
          'Contact selection from phone book',
          'Manual phone number entry',
          'QR code recipient scanning',
          'Amount validation against limits',
          'PIN confirmation for security',
          'Instant transfer to JiraniPay users'
        ],
        userFlow: 'Dashboard → Send Money → Select recipient → Enter amount → Confirm → Success'
      },

      bill_payments: {
        screens: ['BillPaymentScreen'],
        services: ['billPaymentService', 'walletService'],
        features: [
          'Multiple bill categories (electricity, water, airtime, data)',
          'Provider selection by country/region',
          'Account number validation',
          'Bill inquiry for outstanding amounts',
          'Recurring payment setup',
          'Payment receipts and history'
        ],
        userFlow: 'Bills tab → Select category → Choose provider → Enter details → Pay'
      },

      qr_functionality: {
        screens: ['QRScannerScreen', 'QRGeneratorScreen'],
        services: ['qrCodeService', 'transactionService'],
        features: [
          'Camera-based QR scanning',
          'QR code generation for receiving payments',
          'Merchant payment processing',
          'Contact sharing via QR',
          'Payment amount pre-filling',
          'Uganda mobile money QR compatibility'
        ],
        userFlow: 'QR tab → Scan/Generate → Process payment/share contact'
      },

      security_features: {
        screens: ['SecuritySettingsScreen', 'TwoFactorAuthScreen', 'PrivacyControlsScreen'],
        services: ['authService', 'securityService'],
        features: [
          'Biometric authentication (fingerprint/face)',
          'Two-factor authentication (SMS/Email)',
          'Transaction PINs',
          'Account freeze functionality',
          'Security activity monitoring',
          'Privacy controls and data management'
        ],
        userFlow: 'Profile → Security Settings → Configure options'
      },

      support_system: {
        screens: ['ContactSupportScreen', 'AIChatScreen', 'CreateTicketScreen', 'FAQScreen'],
        services: ['aiChatService', 'enhancedAIKnowledgeBase', 'supportService'],
        features: [
          'AI chat assistant with app knowledge',
          'Multiple contact channels (phone, email, WhatsApp)',
          'Support ticket creation with attachments',
          'Comprehensive FAQ system',
          'Multi-language support',
          'Real-time chat with typing indicators'
        ],
        userFlow: 'Profile → Contact Support → Choose channel → Get assistance'
      }
    };
  }

  // =====================================================
  // USER FLOW MAPPINGS
  // =====================================================

  initializeUserFlowMappings() {
    return {
      onboarding: {
        flow: ['Onboarding', 'Register', 'CompleteProfile', 'Dashboard'],
        description: 'New user registration and profile setup',
        keySteps: ['App introduction', 'Account creation', 'Profile completion', 'First login']
      },

      authentication: {
        flow: ['Login', 'BiometricAuth/PIN', 'Dashboard'],
        description: 'User login with biometric or PIN authentication',
        keySteps: ['Credential entry', 'Biometric verification', 'Session establishment']
      },

      send_money: {
        flow: ['Dashboard', 'SendMoney', 'SelectRecipient', 'EnterAmount', 'Confirm', 'Success'],
        description: 'Complete money transfer process',
        keySteps: ['Initiate transfer', 'Choose recipient', 'Set amount', 'Confirm details', 'Complete transfer']
      },

      bill_payment: {
        flow: ['Bills', 'SelectCategory', 'ChooseProvider', 'EnterDetails', 'Pay', 'Receipt'],
        description: 'Bill payment process for utilities and services',
        keySteps: ['Select bill type', 'Choose provider', 'Enter account info', 'Process payment', 'Get receipt']
      },

      qr_payment: {
        flow: ['QR', 'ScanCode', 'VerifyDetails', 'ConfirmPayment', 'Success'],
        description: 'QR code-based payment process',
        keySteps: ['Open scanner', 'Scan QR code', 'Verify payment details', 'Confirm payment', 'Complete transaction']
      },

      wallet_topup: {
        flow: ['Wallet', 'TopUp', 'SelectMethod', 'EnterAmount', 'ProcessPayment', 'Confirmation'],
        description: 'Adding money to wallet from external sources',
        keySteps: ['Access wallet', 'Choose top-up method', 'Enter amount', 'Complete payment', 'Confirm addition']
      },

      support_request: {
        flow: ['Profile', 'ContactSupport', 'SelectChannel', 'DescribeIssue', 'GetAssistance'],
        description: 'Getting help through various support channels',
        keySteps: ['Access support', 'Choose contact method', 'Explain issue', 'Receive assistance']
      },

      ai_chat: {
        flow: ['ContactSupport', 'AIChat', 'SendMessage', 'ReceiveResponse', 'ContinueConversation'],
        description: 'AI assistant interaction for support and guidance',
        keySteps: ['Start AI chat', 'Send query', 'Get AI response', 'Continue conversation as needed']
      }
    };
  }

  // =====================================================
  // CONTEXT ANALYSIS METHODS
  // =====================================================

  analyzeUserContext(query, currentScreen = null, userAction = null) {
    const context = {
      query: query.toLowerCase(),
      screen: currentScreen,
      action: userAction,
      intent: this.determineIntent(query),
      relevantFeatures: this.findRelevantFeatures(query),
      suggestedFlow: this.suggestUserFlow(query),
      troubleshootingContext: this.getTroubleshootingContext(query, currentScreen)
    };

    return context;
  }

  determineIntent(query) {
    const queryLower = query.toLowerCase();
    
    const intentPatterns = {
      navigation: ['how to get to', 'where is', 'find', 'access', 'go to', 'navigate'],
      feature_usage: ['how to use', 'how do i', 'steps to', 'guide', 'tutorial'],
      troubleshooting: ['not working', 'error', 'failed', 'problem', 'issue', 'broken'],
      information: ['what is', 'explain', 'tell me about', 'info', 'details'],
      limits_fees: ['limit', 'fee', 'cost', 'charge', 'maximum', 'minimum'],
      security: ['safe', 'secure', 'protect', 'fraud', 'hack', 'privacy']
    };

    for (const [intent, patterns] of Object.entries(intentPatterns)) {
      if (patterns.some(pattern => queryLower.includes(pattern))) {
        return intent;
      }
    }

    return 'general_inquiry';
  }

  findRelevantFeatures(query) {
    const queryLower = query.toLowerCase();
    const relevantFeatures = [];

    Object.entries(this.featureImplementations).forEach(([feature, implementation]) => {
      // Defensive programming - ensure arrays exist
      const screens = implementation.screens || [];
      const features = implementation.features || [];

      const featureKeywords = [
        feature.replace('_', ' '),
        ...screens.map(screen => screen.toLowerCase()),
        ...features.map(f => f.toLowerCase())
      ];

      if (featureKeywords.some(keyword => queryLower.includes(keyword))) {
        relevantFeatures.push({
          feature,
          implementation,
          relevanceScore: this.calculateFeatureRelevance(queryLower, implementation)
        });
      }
    });

    return relevantFeatures.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  calculateFeatureRelevance(query, implementation) {
    let score = 0;
    const queryWords = query.split(' ');

    // Defensive programming - ensure arrays and strings exist
    const features = implementation.features || [];
    const screens = implementation.screens || [];
    const userFlow = implementation.userFlow || '';

    queryWords.forEach(word => {
      if (features.some(feature => feature.toLowerCase().includes(word))) {
        score += 0.4;
      }
      if (screens.some(screen => screen.toLowerCase().includes(word))) {
        score += 0.3;
      }
      if (userFlow.toLowerCase().includes(word)) {
        score += 0.2;
      }
    });

    return Math.min(score, 1.0);
  }

  suggestUserFlow(query) {
    const queryLower = query.toLowerCase();
    
    const flowKeywords = {
      send_money: ['send money', 'transfer', 'payment to person'],
      bill_payment: ['pay bill', 'utility', 'electricity', 'water', 'airtime'],
      qr_payment: ['qr', 'scan', 'merchant payment'],
      wallet_topup: ['add money', 'top up', 'deposit'],
      support_request: ['help', 'support', 'contact', 'assistance'],
      ai_chat: ['ai', 'chat', 'assistant', 'bot']
    };

    for (const [flow, keywords] of Object.entries(flowKeywords)) {
      if (keywords.some(keyword => queryLower.includes(keyword))) {
        return this.userFlowMappings[flow];
      }
    }

    return null;
  }

  getTroubleshootingContext(query, currentScreen) {
    const queryLower = query.toLowerCase();
    const troubleshootingKeywords = ['not working', 'error', 'failed', 'problem', 'issue'];
    
    if (!troubleshootingKeywords.some(keyword => queryLower.includes(keyword))) {
      return null;
    }

    const context = {
      currentScreen,
      likelyIssues: [],
      suggestedSolutions: []
    };

    // Screen-specific troubleshooting
    if (currentScreen) {
      const screenInfo = this.componentMap.screens[currentScreen];
      if (screenInfo) {
        context.likelyIssues = [
          `${screenInfo.purpose} functionality issues`,
          'Data loading problems',
          'Navigation issues',
          'Service connectivity problems'
        ];

        context.suggestedSolutions = [
          'Check internet connection',
          'Refresh the screen',
          'Restart the app',
          'Contact support if issue persists'
        ];
      }
    }

    return context;
  }

  getScreenContext(screenName) {
    return this.componentMap.screens[screenName] || null;
  }

  getServiceContext(serviceName) {
    return this.serviceMap[serviceName] || null;
  }

  getFeatureImplementation(featureName) {
    return this.featureImplementations[featureName] || null;
  }
}

export default new CodebaseContextService();
