/**
 * Legal Compliance Service for JiraniPay
 * 
 * Manages legal compliance requirements including privacy policy acceptance,
 * data subject requests, and regulatory compliance logging.
 */

import { supabase } from './supabaseClient';
import { isProductionMode } from '../config/environment';

class LegalComplianceService {
  constructor() {
    this.currentPolicyVersion = '2.0';
    this.supportedJurisdictions = ['uganda', 'kenya', 'tanzania', 'eac'];
    this.complianceStandards = ['uganda_dpa_2019', 'kenya_dpa_2019', 'tanzania_dpa_2022', 'gdpr'];
  }

  /**
   * Check if user has accepted the latest privacy policy
   */
  async checkPolicyAcceptance(userId, policyType = 'privacy_policy') {
    try {
      const { data, error } = await supabase
        .rpc('check_user_policy_acceptance', {
          p_user_id: userId,
          p_policy_type: policyType
        });

      if (error) {
        console.error('❌ Error checking policy acceptance:', error);
        return { success: false, accepted: false, error: error.message };
      }

      return { success: true, accepted: data };
    } catch (error) {
      console.error('❌ Error checking policy acceptance:', error);
      return { success: false, accepted: false, error: error.message };
    }
  }

  /**
   * Record user's policy acceptance
   */
  async recordPolicyAcceptance(userId, policyType = 'privacy_policy', acceptanceMethod = 'app_interaction') {
    try {
      // Get the current active policy version
      const { data: policyVersion, error: policyError } = await supabase
        .from('privacy_policy_versions')
        .select('id')
        .eq('is_active', true)
        .order('effective_date', { ascending: false })
        .limit(1)
        .single();

      if (policyError) {
        console.error('❌ Error getting policy version:', policyError);
        return { success: false, error: policyError.message };
      }

      // Record the acceptance
      const { data, error } = await supabase
        .from('user_policy_acceptances')
        .insert({
          user_id: userId,
          policy_version_id: policyVersion.id,
          policy_type: policyType,
          acceptance_method: acceptanceMethod
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error recording policy acceptance:', error);
        return { success: false, error: error.message };
      }

      // Log compliance event
      await this.logComplianceEvent(
        'policy_acceptance',
        'user',
        userId,
        'accepted_policy',
        'policy',
        policyVersion.id,
        null,
        { policy_type: policyType, acceptance_method: acceptanceMethod },
        'consent'
      );

      console.log('✅ Policy acceptance recorded successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error recording policy acceptance:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Submit a data subject request
   */
  async submitDataSubjectRequest(userId, requestType, requestDetails = {}) {
    try {
      const validRequestTypes = ['access', 'rectification', 'erasure', 'portability', 'restriction', 'objection'];
      
      if (!validRequestTypes.includes(requestType)) {
        return { success: false, error: 'Invalid request type' };
      }

      const { data, error } = await supabase
        .from('data_subject_requests')
        .insert({
          user_id: userId,
          request_type: requestType,
          request_details: requestDetails,
          legal_basis: this.getLegalBasisForRequest(requestType)
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error submitting data subject request:', error);
        return { success: false, error: error.message };
      }

      // Log compliance event
      await this.logComplianceEvent(
        'data_subject_request',
        'user',
        userId,
        'submitted_request',
        'data_request',
        data.id,
        null,
        { request_type: requestType, request_details: requestDetails },
        'user_rights'
      );

      console.log('✅ Data subject request submitted successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error submitting data subject request:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's data subject requests
   */
  async getUserDataSubjectRequests(userId) {
    try {
      const { data, error } = await supabase
        .from('data_subject_requests')
        .select('*')
        .eq('user_id', userId)
        .order('submitted_at', { ascending: false });

      if (error) {
        console.error('❌ Error getting data subject requests:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting data subject requests:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Log compliance event
   */
  async logComplianceEvent(auditType, entityType, entityId, action, resourceType = null, resourceId = null, oldValues = null, newValues = null, legalBasis = null) {
    try {
      const { data, error } = await supabase
        .rpc('log_compliance_event', {
          p_audit_type: auditType,
          p_entity_type: entityType,
          p_entity_id: entityId,
          p_action: action,
          p_resource_type: resourceType,
          p_resource_id: resourceId,
          p_old_values: oldValues,
          p_new_values: newValues,
          p_legal_basis: legalBasis
        });

      if (error) {
        console.error('❌ Error logging compliance event:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error logging compliance event:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Report data breach incident
   */
  async reportDataBreach(incidentData) {
    try {
      const incidentReference = `BREACH-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      
      const { data, error } = await supabase
        .from('data_breach_incidents')
        .insert({
          incident_reference: incidentReference,
          severity: incidentData.severity,
          breach_type: incidentData.breachType,
          affected_data_types: incidentData.affectedDataTypes,
          affected_users_count: incidentData.affectedUsersCount || 0,
          discovered_at: incidentData.discoveredAt || new Date().toISOString(),
          breach_description: incidentData.description,
          impact_assessment: incidentData.impactAssessment || {},
          mitigation_actions: incidentData.mitigationActions || {},
          created_by: incidentData.reportedBy
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error reporting data breach:', error);
        return { success: false, error: error.message };
      }

      // Log regulatory compliance event
      await this.logRegulatoryCompliance('data_breach', 'data_breach', {
        incident_id: data.id,
        incident_reference: incidentReference,
        severity: incidentData.severity,
        affected_users: incidentData.affectedUsersCount
      }, 'critical');

      console.log('✅ Data breach incident reported successfully');
      return { success: true, data, incidentReference };
    } catch (error) {
      console.error('❌ Error reporting data breach:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Log regulatory compliance event
   */
  async logRegulatoryCompliance(complianceType, eventType, eventData, severity = 'info') {
    try {
      const { data, error } = await supabase
        .from('regulatory_compliance_logs')
        .insert({
          compliance_type: complianceType,
          event_type: eventType,
          event_data: eventData,
          severity: severity
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error logging regulatory compliance:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error logging regulatory compliance:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current privacy policy version
   */
  async getCurrentPrivacyPolicy() {
    try {
      const { data, error } = await supabase
        .from('privacy_policy_versions')
        .select('*')
        .eq('is_active', true)
        .order('effective_date', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('❌ Error getting current privacy policy:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting current privacy policy:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user needs to accept updated policy
   */
  async checkPolicyUpdateRequired(userId) {
    try {
      const policyCheck = await this.checkPolicyAcceptance(userId, 'privacy_policy');
      
      if (!policyCheck.success) {
        return policyCheck;
      }

      return {
        success: true,
        updateRequired: !policyCheck.accepted,
        currentVersion: this.currentPolicyVersion
      };
    } catch (error) {
      console.error('❌ Error checking policy update requirement:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(startDate, endDate, complianceType = null) {
    try {
      let query = supabase
        .from('regulatory_compliance_logs')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (complianceType) {
        query = query.eq('compliance_type', complianceType);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error generating compliance report:', error);
        return { success: false, error: error.message };
      }

      // Generate summary statistics
      const summary = {
        total_events: data.length,
        by_severity: data.reduce((acc, event) => {
          acc[event.severity] = (acc[event.severity] || 0) + 1;
          return acc;
        }, {}),
        by_type: data.reduce((acc, event) => {
          acc[event.event_type] = (acc[event.event_type] || 0) + 1;
          return acc;
        }, {}),
        period: { start: startDate, end: endDate }
      };

      return { success: true, data, summary };
    } catch (error) {
      console.error('❌ Error generating compliance report:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get legal basis for data subject request
   */
  getLegalBasisForRequest(requestType) {
    const legalBasisMap = {
      'access': 'Article 15 GDPR / Uganda DPA Section 35',
      'rectification': 'Article 16 GDPR / Uganda DPA Section 36',
      'erasure': 'Article 17 GDPR / Uganda DPA Section 37',
      'portability': 'Article 20 GDPR / Uganda DPA Section 39',
      'restriction': 'Article 18 GDPR / Uganda DPA Section 38',
      'objection': 'Article 21 GDPR / Uganda DPA Section 40'
    };

    return legalBasisMap[requestType] || 'General data protection rights';
  }

  /**
   * Validate jurisdiction compliance
   */
  validateJurisdictionCompliance(jurisdiction) {
    return this.supportedJurisdictions.includes(jurisdiction);
  }

  /**
   * Get compliance requirements for jurisdiction
   */
  getComplianceRequirements(jurisdiction) {
    const requirements = {
      'uganda': {
        authority: 'National Information Technology Authority Uganda (NITA-U)',
        law: 'Data Protection and Privacy Act 2019',
        notification_period: '72 hours',
        user_notification_period: '72 hours',
        data_retention_max: '7 years (financial records)',
        contact: '<EMAIL>'
      },
      'kenya': {
        authority: 'Office of the Data Protection Commissioner (ODPC)',
        law: 'Data Protection Act 2019',
        notification_period: '72 hours',
        user_notification_period: '72 hours',
        data_retention_max: '7 years (financial records)',
        contact: '<EMAIL>'
      },
      'tanzania': {
        authority: 'Tanzania Communications Regulatory Authority (TCRA)',
        law: 'Data Protection Act 2022',
        notification_period: '72 hours',
        user_notification_period: '72 hours',
        data_retention_max: '7 years (financial records)',
        contact: '<EMAIL>'
      }
    };

    return requirements[jurisdiction] || null;
  }
}

export default new LegalComplianceService();
