import { Alert } from 'react-native';
import profileManagementService from './profileManagementService';
import authService from './authService';

/**
 * Verification Service
 * Handles verification level checks for transactions and features
 */
class VerificationService {
  constructor() {
    // High-value transaction threshold (500,000 UGX as per requirements)
    this.HIGH_VALUE_THRESHOLD = 500000;
    
    // Verification levels and their limits
    this.verificationLevels = {
      basic: {
        daily_limit: 100000,
        monthly_limit: 3000000,
        annual_limit: 36000000,
        features: ['basic_transfers', 'bill_payments', 'airtime_purchase']
      },
      standard: {
        daily_limit: 500000,
        monthly_limit: 15000000,
        annual_limit: 180000000,
        features: ['basic_transfers', 'bill_payments', 'airtime_purchase', 'international_transfers', 'loans']
      },
      premium: {
        daily_limit: 2000000,
        monthly_limit: 60000000,
        annual_limit: 720000000,
        features: ['all_features', 'priority_support', 'investment_products']
      }
    };
  }

  /**
   * Check if user can perform a transaction based on verification level
   * @param {number} amount - Transaction amount
   * @param {string} transactionType - Type of transaction
   * @param {Function} navigation - Navigation function for redirecting to verification
   * @returns {Promise<Object>} - Verification result
   */
  async checkTransactionVerification(amount, transactionType = 'transfer', navigation = null) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get user's current verification level
      const levelResult = await profileManagementService.getKYCVerificationLevel(user.id);
      if (!levelResult.success) {
        return { success: false, error: 'Unable to verify account level' };
      }

      const userLevel = levelResult.data.verification_level || 'basic';
      const limits = this.verificationLevels[userLevel];

      // Check if transaction amount exceeds daily limit
      if (amount > limits.daily_limit) {
        return this.handleVerificationRequired(
          amount,
          userLevel,
          'daily_limit',
          navigation
        );
      }

      // Check if it's a high-value transaction requiring verification
      if (amount > this.HIGH_VALUE_THRESHOLD && userLevel === 'basic') {
        return this.handleVerificationRequired(
          amount,
          userLevel,
          'high_value',
          navigation
        );
      }

      // Check if transaction type is allowed for current level
      if (!this.isFeatureAllowed(transactionType, userLevel)) {
        return this.handleVerificationRequired(
          amount,
          userLevel,
          'feature_restricted',
          navigation
        );
      }

      return { 
        success: true, 
        userLevel,
        limits,
        message: 'Transaction approved'
      };

    } catch (error) {
      console.error('❌ Error checking transaction verification:', error);
      return { success: false, error: 'Verification check failed' };
    }
  }

  /**
   * Handle verification required scenarios
   * @param {number} amount - Transaction amount
   * @param {string} currentLevel - Current verification level
   * @param {string} reason - Reason for verification requirement
   * @param {Function} navigation - Navigation function
   * @returns {Object} - Verification result with action
   */
  handleVerificationRequired(amount, currentLevel, reason, navigation) {
    const nextLevel = this.getNextVerificationLevel(currentLevel);
    const nextLimits = this.verificationLevels[nextLevel];

    let title, message, actionText;

    switch (reason) {
      case 'daily_limit':
        title = 'Daily Limit Exceeded';
        message = `Your current daily limit is UGX ${this.verificationLevels[currentLevel].daily_limit.toLocaleString()}. Upgrade to ${nextLevel.toUpperCase()} for UGX ${nextLimits.daily_limit.toLocaleString()} daily limit.`;
        actionText = `Upgrade to ${nextLevel.toUpperCase()}`;
        break;
      
      case 'high_value':
        title = 'Verification Required';
        message = `Transactions above UGX ${this.HIGH_VALUE_THRESHOLD.toLocaleString()} require identity verification for security purposes.`;
        actionText = 'Verify Identity';
        break;
      
      case 'feature_restricted':
        title = 'Feature Not Available';
        message = `This feature requires ${nextLevel.toUpperCase()} verification level.`;
        actionText = `Upgrade to ${nextLevel.toUpperCase()}`;
        break;
      
      default:
        title = 'Verification Required';
        message = 'Please complete account verification to proceed.';
        actionText = 'Verify Account';
    }

    return {
      success: false,
      requiresVerification: true,
      reason,
      currentLevel,
      nextLevel,
      title,
      message,
      actionText,
      amount,
      onVerify: navigation ? () => navigation.navigate('AccountVerification') : null
    };
  }

  /**
   * Get the next verification level
   * @param {string} currentLevel - Current verification level
   * @returns {string} - Next verification level
   */
  getNextVerificationLevel(currentLevel) {
    const levelOrder = ['basic', 'standard', 'premium'];
    const currentIndex = levelOrder.indexOf(currentLevel);
    return levelOrder[Math.min(currentIndex + 1, levelOrder.length - 1)];
  }

  /**
   * Check if a feature is allowed for the current verification level
   * @param {string} feature - Feature to check
   * @param {string} level - Verification level
   * @returns {boolean} - Whether feature is allowed
   */
  isFeatureAllowed(feature, level) {
    const levelFeatures = this.verificationLevels[level]?.features || [];
    return levelFeatures.includes(feature) || levelFeatures.includes('all_features');
  }

  /**
   * Show verification alert with navigation option
   * @param {Object} verificationResult - Result from checkTransactionVerification
   * @returns {Promise<boolean>} - Whether user chose to verify
   */
  async showVerificationAlert(verificationResult) {
    return new Promise((resolve) => {
      if (!verificationResult.requiresVerification) {
        resolve(true);
        return;
      }

      const buttons = [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => resolve(false)
        }
      ];

      if (verificationResult.onVerify) {
        buttons.push({
          text: verificationResult.actionText,
          onPress: () => {
            verificationResult.onVerify();
            resolve(false); // Don't proceed with transaction
          }
        });
      }

      Alert.alert(
        verificationResult.title,
        verificationResult.message,
        buttons
      );
    });
  }

  /**
   * Quick check for high-value transactions
   * @param {number} amount - Transaction amount
   * @returns {boolean} - Whether verification is required
   */
  isHighValueTransaction(amount) {
    return amount > this.HIGH_VALUE_THRESHOLD;
  }

  /**
   * Get verification level info
   * @param {string} level - Verification level
   * @returns {Object} - Level information
   */
  getVerificationLevelInfo(level) {
    const levelInfo = {
      basic: {
        title: 'Basic Account',
        color: '#F59E0B', // warning color
        icon: 'person-outline'
      },
      standard: {
        title: 'Standard Account',
        color: '#5B37B7', // primary color
        icon: 'shield-outline'
      },
      premium: {
        title: 'Premium Account',
        color: '#10B981', // success color
        icon: 'diamond-outline'
      }
    };

    return levelInfo[level] || levelInfo.basic;
  }

  /**
   * Format currency for display
   * @param {number} amount - Amount to format
   * @returns {string} - Formatted currency string
   */
  formatCurrency(amount) {
    return `UGX ${amount?.toLocaleString() || '0'}`;
  }

  /**
   * Get user's current verification status
   * @returns {Promise<Object>} - Current verification status
   */
  async getCurrentVerificationStatus() {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const levelResult = await profileManagementService.getKYCVerificationLevel(user.id);
      if (!levelResult.success) {
        return { success: false, error: 'Unable to get verification level' };
      }

      const level = levelResult.data.verification_level || 'basic';
      const limits = this.verificationLevels[level];
      const levelInfo = this.getVerificationLevelInfo(level);

      return {
        success: true,
        level,
        limits,
        levelInfo,
        isHighValueAllowed: level !== 'basic' || limits.daily_limit > this.HIGH_VALUE_THRESHOLD
      };
    } catch (error) {
      console.error('❌ Error getting verification status:', error);
      return { success: false, error: 'Failed to get verification status' };
    }
  }
}

// Create and export singleton instance
const verificationService = new VerificationService();
export default verificationService;
