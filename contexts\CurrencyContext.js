import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import currencyService from '../services/currencyService';
import authService from '../services/authService';

/**
 * Currency Context for Global Currency State Management
 * 
 * Provides real-time currency conversion state across the entire app
 * Automatically updates all components when user changes preferred currency
 */
const CurrencyContext = createContext({});

export const CurrencyProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userCurrency, setUserCurrency] = useState('UGX');
  const [exchangeRates, setExchangeRates] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [error, setError] = useState(null);

  // Initialize currency data
  const initializeCurrency = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('💱 CurrencyContext: Initializing currency system...');

      // Initialize currency service
      if (!currencyService.isInitialized) {
        await currencyService.initialize();
      }

      // Load user's preferred currency
      const preferredCurrency = await currencyService.getUserPreferredCurrency();
      setUserCurrency(preferredCurrency);

      // Load exchange rates
      const rates = currencyService.getAllExchangeRates();
      setExchangeRates(rates);
      setLastUpdate(new Date());

      console.log('✅ CurrencyContext: Currency system initialized', {
        userCurrency: preferredCurrency,
        ratesCount: Object.keys(rates).length,
        rates: rates
      });
    } catch (err) {
      console.error('❌ CurrencyContext: Error initializing currency:', err);
      setError(err.message);
      // Set fallback values
      setUserCurrency('UGX');
      setExchangeRates({ UGX: 1 });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user currency preference
  const updateUserCurrency = useCallback(async (newCurrency) => {
    try {
      console.log('💱 CurrencyContext: Updating user currency to:', newCurrency);
      
      // Update in currency service
      await currencyService.setUserPreferredCurrency(newCurrency);
      
      // Update local state
      setUserCurrency(newCurrency);
      
      // Notify all listeners
      currencyService.notifyCurrencyChangeListeners(newCurrency);
      
      console.log('✅ CurrencyContext: User currency updated successfully');
    } catch (err) {
      console.error('❌ CurrencyContext: Error updating user currency:', err);
      setError(err.message);
    }
  }, []);

  // Convert amount from UGX to user's preferred currency
  const convertFromUGX = useCallback((ugxAmount, targetCurrency = null) => {
    try {
      const target = targetCurrency || userCurrency;
      const amount = parseFloat(ugxAmount) || 0;
      
      if (target === 'UGX') {
        return amount;
      }

      // If still loading, return UGX amount without warning
      if (isLoading) {
        return amount;
      }

      const rate = exchangeRates[target];
      if (!rate) {
        // Only warn if currency system is fully initialized
        if (!isLoading && Object.keys(exchangeRates).length > 0) {
          console.warn(`⚠️ CurrencyContext: No exchange rate for ${target}, using UGX`);
        }
        return amount;
      }

      const converted = amount * rate;
      return converted;
    } catch (error) {
      console.error('❌ CurrencyContext: Error converting from UGX:', error);
      return parseFloat(ugxAmount) || 0;
    }
  }, [userCurrency, exchangeRates]);

  // Convert between any two currencies
  const convert = useCallback((amount, fromCurrency, toCurrency = null) => {
    try {
      const target = toCurrency || userCurrency;
      const numAmount = parseFloat(amount) || 0;

      if (fromCurrency === target) {
        return numAmount;
      }

      const fromRate = exchangeRates[fromCurrency] || 1;
      const toRate = exchangeRates[target] || 1;
      
      // Convert through UGX as base
      let ugxAmount;
      if (fromCurrency === 'UGX') {
        ugxAmount = numAmount;
      } else {
        ugxAmount = numAmount / fromRate;
      }
      
      let convertedAmount;
      if (target === 'UGX') {
        convertedAmount = ugxAmount;
      } else {
        convertedAmount = ugxAmount * toRate;
      }

      return convertedAmount;
    } catch (error) {
      console.error('❌ CurrencyContext: Error converting currencies:', error);
      return parseFloat(amount) || 0;
    }
  }, [userCurrency, exchangeRates]);

  // Format amount with currency
  const formatAmount = useCallback((amount, currency = null, options = {}) => {
    try {
      const targetCurrency = currency || userCurrency;
      const numAmount = parseFloat(amount) || 0;
      
      return currencyService.formatAmountWithCurrency(numAmount, targetCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false,
        ...options
      });
    } catch (error) {
      console.error('❌ CurrencyContext: Error formatting amount:', error);
      const fallbackSymbol = currency || userCurrency || 'UGX';
      return `${fallbackSymbol} ${parseFloat(amount).toLocaleString()}`;
    }
  }, [userCurrency]);

  // Convert from UGX and format in one step
  const convertAndFormat = useCallback((ugxAmount, targetCurrency = null, options = {}) => {
    const convertedAmount = convertFromUGX(ugxAmount, targetCurrency);
    return formatAmount(convertedAmount, targetCurrency || userCurrency, options);
  }, [convertFromUGX, formatAmount, userCurrency]);

  // Get currency symbol
  const getCurrencySymbol = useCallback((currency = null) => {
    try {
      const targetCurrency = currency || userCurrency;
      const currencyInfo = currencyService.getCurrencyInfo(targetCurrency);
      return currencyInfo?.symbol || targetCurrency;
    } catch (error) {
      console.error('❌ CurrencyContext: Error getting currency symbol:', error);
      return currency || userCurrency || 'UGX';
    }
  }, [userCurrency]);

  // Refresh exchange rates
  const refreshRates = useCallback(async () => {
    try {
      console.log('💱 CurrencyContext: Refreshing exchange rates...');
      await currencyService.updateExchangeRates();
      
      const newRates = currencyService.getAllExchangeRates();
      setExchangeRates(newRates);
      setLastUpdate(new Date());
      
      console.log('✅ CurrencyContext: Exchange rates refreshed');
    } catch (err) {
      console.error('❌ CurrencyContext: Error refreshing rates:', err);
      setError(err.message);
    }
  }, []);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('❌ CurrencyContext: Error getting current user:', error);
      }
    };
    getCurrentUser();
  }, []);

  // Initialize on mount and when user changes
  useEffect(() => {
    if (user) {
      // Add a small delay to ensure all services are ready
      const timer = setTimeout(() => {
        initializeCurrency();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [initializeCurrency, user]);

  // Listen for currency changes from currency service
  useEffect(() => {
    const unsubscribe = currencyService.addCurrencyChangeListener((newCurrency) => {
      console.log('💱 CurrencyContext: Received currency change notification:', newCurrency);
      setUserCurrency(newCurrency);
    });

    return unsubscribe;
  }, []);

  const contextValue = {
    // State
    userCurrency,
    exchangeRates,
    isLoading,
    lastUpdate,
    error,

    // Actions
    updateUserCurrency,
    refreshRates,

    // Conversion functions
    convertFromUGX,
    convert,
    formatAmount,
    convertAndFormat,

    // Utility functions
    getCurrencySymbol,
    getCurrencyInfo: (currency = null) => {
      const targetCurrency = currency || userCurrency;
      return currencyService.getCurrencyInfo(targetCurrency);
    },
    getExchangeRate: (fromCurrency, toCurrency = null) => {
      const target = toCurrency || userCurrency;
      return currencyService.getExchangeRate(fromCurrency, target);
    },

    // Helper functions
    isCurrencySupported: (currency) => !!exchangeRates[currency],
    getSupportedCurrencies: () => Object.keys(exchangeRates)
  };

  return (
    <CurrencyContext.Provider value={contextValue}>
      {children}
    </CurrencyContext.Provider>
  );
};

// Custom hook to use currency context
export const useCurrencyContext = () => {
  const context = useContext(CurrencyContext);
  if (!context) {
    throw new Error('useCurrencyContext must be used within a CurrencyProvider');
  }
  return context;
};

export { CurrencyContext };
export default CurrencyProvider;
