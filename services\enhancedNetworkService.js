/**
 * Enhanced Network Service for JiraniPay
 * 
 * Provides robust network request handling with authentication,
 * retry logic, and comprehensive error handling for production mode.
 */

import supabase from './supabaseClient';
import authService from './authService';
import networkService from './networkService';
import { isProductionMode } from '../config/environment';

class EnhancedNetworkService {
  constructor() {
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.authToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Initialize the enhanced network service
   */
  async initialize() {
    try {
      console.log('🌐 Initializing enhanced network service...');
      
      // Get initial auth token
      await this.refreshAuthToken();
      
      // Listen for network changes
      networkService.addNetworkListener(this.handleNetworkChange.bind(this));
      
      console.log('✅ Enhanced network service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Enhanced network service initialization failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle network connectivity changes
   */
  handleNetworkChange(networkStatus) {
    if (networkStatus.isConnected && this.requestQueue.length > 0) {
      console.log('🌐 Network restored, processing queued requests');
      this.processRequestQueue();
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshAuthToken() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) throw error;
      
      if (session?.access_token) {
        this.authToken = session.access_token;
        this.tokenExpiry = new Date(session.expires_at * 1000);
        console.log('🔐 Auth token refreshed successfully');
        return true;
      } else {
        console.log('⚠️ No valid session found');
        this.authToken = null;
        this.tokenExpiry = null;
        return false;
      }
    } catch (error) {
      console.error('❌ Error refreshing auth token:', error);
      this.authToken = null;
      this.tokenExpiry = null;
      return false;
    }
  }

  /**
   * Check if auth token is valid and refresh if needed
   */
  async ensureValidToken() {
    if (!this.authToken || !this.tokenExpiry) {
      return await this.refreshAuthToken();
    }

    // Check if token expires in the next 5 minutes
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    if (this.tokenExpiry <= fiveMinutesFromNow) {
      console.log('🔐 Token expiring soon, refreshing...');
      return await this.refreshAuthToken();
    }

    return true;
  }

  /**
   * Make authenticated Supabase request with enhanced error handling
   */
  async makeSupabaseRequest(operation, options = {}) {
    const {
      retries = 3,
      timeout = 15000,
      priority = 'normal',
      requireAuth = true
    } = options;

    try {
      // Check network connectivity
      const networkStatus = networkService.getNetworkStatus();
      if (!networkStatus.isConnected) {
        throw new Error('No network connection available');
      }

      // Ensure valid authentication if required
      if (requireAuth) {
        const hasValidToken = await this.ensureValidToken();
        if (!hasValidToken) {
          throw new Error('Authentication required but no valid token available');
        }
      }

      // Execute the operation with retry logic
      let lastError;
      for (let attempt = 0; attempt < retries; attempt++) {
        try {
          console.log(`🌐 Executing Supabase request (attempt ${attempt + 1}/${retries})`);
          
          // Set timeout for the operation
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), timeout);
          });

          const result = await Promise.race([operation(), timeoutPromise]);
          
          console.log('✅ Supabase request successful');
          return { success: true, data: result.data, error: null };

        } catch (error) {
          lastError = error;
          console.log(`⚠️ Supabase request failed (attempt ${attempt + 1}):`, error.message);

          // Check if it's an auth error
          if (error.message.includes('JWT') || error.message.includes('auth') || error.message.includes('token')) {
            console.log('🔐 Auth error detected, refreshing token...');
            await this.refreshAuthToken();
          }

          // Don't retry on certain errors
          if (this.isNonRetryableError(error) || attempt === retries - 1) {
            break;
          }

          // Exponential backoff
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await this.delay(delay);
        }
      }

      // If all retries failed, check if we should queue the request
      if (this.shouldQueueRequest(lastError, priority)) {
        return await this.queueRequest(operation, options);
      }

      throw lastError;

    } catch (error) {
      console.error('❌ Enhanced network request failed:', error);
      return { success: false, data: null, error: error.message };
    }
  }

  /**
   * Make HTTP request with enhanced error handling
   */
  async makeHttpRequest(url, requestOptions = {}, options = {}) {
    const {
      retries = 3,
      timeout = 15000,
      priority = 'normal'
    } = options;

    const operation = async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      try {
        // Add authentication headers if available
        const headers = { ...requestOptions.headers };
        if (this.authToken) {
          headers['Authorization'] = `Bearer ${this.authToken}`;
        }

        const response = await fetch(url, {
          ...requestOptions,
          headers,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return { data };

      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    };

    return await this.makeSupabaseRequest(operation, { retries, timeout, priority, requireAuth: false });
  }

  /**
   * Get user preferences with enhanced error handling
   */
  async getUserPreferences(userId) {
    const operation = async () => {
      return await supabase
        .from('user_preferences')
        .select('*')
        .eq('id', userId)
        .single();
    };

    const result = await this.makeSupabaseRequest(operation, {
      retries: 3,
      timeout: 10000,
      priority: 'high',
      requireAuth: true
    });

    // Handle "no rows found" error gracefully
    if (!result.success && result.error?.includes('PGRST116')) {
      console.log('⚠️ User preferences not found, will create defaults');
      return { success: true, data: null, needsCreation: true };
    }

    return result;
  }

  /**
   * Update user preferences with enhanced error handling
   */
  async updateUserPreferences(userId, preferences) {
    const operation = async () => {
      return await supabase
        .from('user_preferences')
        .upsert({
          id: userId,
          ...preferences,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
    };

    return await this.makeSupabaseRequest(operation, {
      retries: 3,
      timeout: 15000,
      priority: 'high',
      requireAuth: true
    });
  }

  /**
   * Upload file to Supabase storage with enhanced error handling
   */
  async uploadFile(bucket, fileName, fileData, options = {}) {
    const operation = async () => {
      return await supabase.storage
        .from(bucket)
        .upload(fileName, fileData, {
          contentType: options.contentType || 'application/octet-stream',
          upsert: options.upsert || false,
          ...options
        });
    };

    return await this.makeSupabaseRequest(operation, {
      retries: 2, // Fewer retries for uploads
      timeout: 30000, // Longer timeout for uploads
      priority: 'high',
      requireAuth: true
    });
  }

  /**
   * Check if error should not be retried
   */
  isNonRetryableError(error) {
    const nonRetryablePatterns = [
      'Invalid input',
      'Validation failed',
      'Permission denied',
      'Not found',
      'Conflict',
      'Bad request'
    ];

    return nonRetryablePatterns.some(pattern => 
      error.message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * Check if request should be queued for later
   */
  shouldQueueRequest(error, priority) {
    const queueableErrors = [
      'network',
      'timeout',
      'connection',
      'fetch',
      'abort'
    ];

    return priority === 'high' || 
           queueableErrors.some(pattern => 
             error.message.toLowerCase().includes(pattern)
           );
  }

  /**
   * Queue request for later execution
   */
  async queueRequest(operation, options) {
    const queueItem = {
      id: Date.now() + Math.random(),
      operation,
      options,
      timestamp: Date.now(),
      attempts: 0
    };

    this.requestQueue.push(queueItem);
    console.log(`📥 Request queued (${this.requestQueue.length} items in queue)`);

    return {
      success: false,
      queued: true,
      queueId: queueItem.id,
      error: 'Request queued for when connection improves'
    };
  }

  /**
   * Process queued requests
   */
  async processRequestQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) return;

    this.isProcessingQueue = true;
    console.log(`🔄 Processing request queue (${this.requestQueue.length} items)`);

    const processedItems = [];

    for (const item of this.requestQueue) {
      try {
        const networkStatus = networkService.getNetworkStatus();
        if (!networkStatus.isConnected) break;

        console.log(`🔄 Processing queued request`);
        const result = await this.makeSupabaseRequest(item.operation, item.options);
        
        if (result.success) {
          processedItems.push(item.id);
        } else {
          item.attempts++;
          if (item.attempts >= 3) {
            processedItems.push(item.id);
            console.log(`❌ Removing failed queued request after 3 attempts`);
          }
        }

      } catch (error) {
        console.log(`⚠️ Queued request failed:`, error.message);
        item.attempts++;
        if (item.attempts >= 3) {
          processedItems.push(item.id);
        }
      }
    }

    // Remove processed items
    this.requestQueue = this.requestQueue.filter(item => !processedItems.includes(item.id));
    this.isProcessingQueue = false;

    console.log(`✅ Queue processing complete (${this.requestQueue.length} items remaining)`);
  }

  /**
   * Utility delay function
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get network status
   */
  getNetworkStatus() {
    return networkService.getNetworkStatus();
  }

  /**
   * Clear request queue
   */
  clearQueue() {
    this.requestQueue = [];
    console.log('🧹 Request queue cleared');
  }
}

// Create and export singleton instance
const enhancedNetworkService = new EnhancedNetworkService();
export default enhancedNetworkService;
