/**
 * Redis Service
 * Manages Redis connections for caching, sessions, and real-time features
 */

const Redis = require('redis');
const config = require('../config/config');
const logger = require('../utils/logger');

class RedisService {
  constructor() {
    this.client = null;
    this.isInitialized = false;
    this.isConnected = false;
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      // Create Redis client
      this.client = Redis.createClient({
        url: config.redis.url,
        password: config.redis.password,
        retry_delay_on_failover: config.redis.retryDelayOnFailover,
        enable_ready_check: config.redis.enableReadyCheck,
        max_attempts: config.redis.maxRetriesPerRequest
      });

      // Set up event handlers
      this.client.on('connect', () => {
        logger.info('Redis client connected');
      });

      this.client.on('ready', () => {
        this.isConnected = true;
        logger.info('Redis client ready');
      });

      this.client.on('error', (error) => {
        this.isConnected = false;
        logger.error('Redis client error:', error);
      });

      this.client.on('end', () => {
        this.isConnected = false;
        logger.info('Redis client disconnected');
      });

      this.client.on('reconnecting', () => {
        logger.info('Redis client reconnecting...');
      });

      // Connect to Redis
      await this.client.connect();

      // Test connection
      await this.client.ping();

      this.isInitialized = true;
      logger.info('Redis service initialized successfully');

    } catch (error) {
      logger.error('Redis initialization failed:', error);
      
      // In development, continue without Redis
      if (config.server.nodeEnv === 'development') {
        logger.warn('Continuing without Redis in development mode');
        this.isInitialized = false;
        return;
      }
      
      throw error;
    }
  }

  /**
   * Get Redis client
   */
  getClient() {
    if (!this.isInitialized || !this.isConnected) {
      throw new Error('Redis service not available');
    }
    return this.client;
  }

  /**
   * Check if Redis is available
   */
  isAvailable() {
    return this.isInitialized && this.isConnected;
  }

  /**
   * Set a key-value pair with optional expiration
   */
  async set(key, value, expirationSeconds = null) {
    if (!this.isAvailable()) {
      logger.warn('Redis not available, skipping set operation');
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      
      if (expirationSeconds) {
        await this.client.setEx(key, expirationSeconds, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }

      logger.debug(`Redis SET: ${key}`, { expiration: expirationSeconds });
      return true;
    } catch (error) {
      logger.error('Redis SET failed:', error);
      return false;
    }
  }

  /**
   * Get a value by key
   */
  async get(key) {
    if (!this.isAvailable()) {
      logger.warn('Redis not available, skipping get operation');
      return null;
    }

    try {
      const value = await this.client.get(key);
      
      if (value === null) {
        return null;
      }

      logger.debug(`Redis GET: ${key}`);
      return JSON.parse(value);
    } catch (error) {
      logger.error('Redis GET failed:', error);
      return null;
    }
  }

  /**
   * Delete a key
   */
  async del(key) {
    if (!this.isAvailable()) {
      logger.warn('Redis not available, skipping delete operation');
      return false;
    }

    try {
      const result = await this.client.del(key);
      logger.debug(`Redis DEL: ${key}`, { deleted: result > 0 });
      return result > 0;
    } catch (error) {
      logger.error('Redis DEL failed:', error);
      return false;
    }
  }

  /**
   * Check if a key exists
   */
  async exists(key) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS failed:', error);
      return false;
    }
  }

  /**
   * Set expiration for a key
   */
  async expire(key, seconds) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.client.expire(key, seconds);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXPIRE failed:', error);
      return false;
    }
  }

  /**
   * Increment a numeric value
   */
  async incr(key) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const result = await this.client.incr(key);
      logger.debug(`Redis INCR: ${key}`, { value: result });
      return result;
    } catch (error) {
      logger.error('Redis INCR failed:', error);
      return null;
    }
  }

  /**
   * Session management methods
   */

  /**
   * Store user session
   */
  async setSession(sessionId, sessionData, expirationSeconds = 86400) { // 24 hours default
    const key = `session:${sessionId}`;
    return await this.set(key, sessionData, expirationSeconds);
  }

  /**
   * Get user session
   */
  async getSession(sessionId) {
    const key = `session:${sessionId}`;
    return await this.get(key);
  }

  /**
   * Delete user session
   */
  async deleteSession(sessionId) {
    const key = `session:${sessionId}`;
    return await this.del(key);
  }

  /**
   * Cache management methods
   */

  /**
   * Cache user data
   */
  async cacheUser(userId, userData, expirationSeconds = 3600) { // 1 hour default
    const key = `user:${userId}`;
    return await this.set(key, userData, expirationSeconds);
  }

  /**
   * Get cached user data
   */
  async getCachedUser(userId) {
    const key = `user:${userId}`;
    return await this.get(key);
  }

  /**
   * Cache wallet data
   */
  async cacheWallet(userId, walletData, expirationSeconds = 1800) { // 30 minutes default
    const key = `wallet:${userId}`;
    return await this.set(key, walletData, expirationSeconds);
  }

  /**
   * Get cached wallet data
   */
  async getCachedWallet(userId) {
    const key = `wallet:${userId}`;
    return await this.get(key);
  }

  /**
   * Rate limiting methods
   */

  /**
   * Check and increment rate limit counter
   */
  async checkRateLimit(identifier, limit, windowSeconds) {
    if (!this.isAvailable()) {
      return { allowed: true, remaining: limit };
    }

    try {
      const key = `rate_limit:${identifier}`;
      const current = await this.incr(key);
      
      if (current === 1) {
        await this.expire(key, windowSeconds);
      }

      const allowed = current <= limit;
      const remaining = Math.max(0, limit - current);

      logger.debug(`Rate limit check: ${identifier}`, {
        current,
        limit,
        allowed,
        remaining
      });

      return { allowed, remaining, current };
    } catch (error) {
      logger.error('Rate limit check failed:', error);
      return { allowed: true, remaining: limit };
    }
  }

  /**
   * OTP management methods
   */

  /**
   * Store OTP
   */
  async storeOTP(phoneNumber, otp, expirationSeconds = 300) { // 5 minutes default
    const key = `otp:${phoneNumber}`;
    return await this.set(key, { otp, timestamp: Date.now() }, expirationSeconds);
  }

  /**
   * Verify and consume OTP
   */
  async verifyOTP(phoneNumber, providedOtp) {
    const key = `otp:${phoneNumber}`;
    const otpData = await this.get(key);

    if (!otpData) {
      return { valid: false, reason: 'OTP not found or expired' };
    }

    if (otpData.otp !== providedOtp) {
      return { valid: false, reason: 'Invalid OTP' };
    }

    // Delete OTP after successful verification
    await this.del(key);

    return { valid: true };
  }

  /**
   * Get cache statistics
   */
  async getStats() {
    try {
      if (!this.isAvailable()) {
        return {
          memoryUsage: 0,
          keyCount: 0,
          hitRate: 0,
          missRate: 0,
          connected: false
        };
      }

      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');

      // Parse memory info
      const memoryMatch = info.match(/used_memory:(\d+)/);
      const memoryUsage = memoryMatch ? parseInt(memoryMatch[1]) : 0;

      // Parse keyspace info
      const keyspaceMatch = keyspace.match(/keys=(\d+)/);
      const keyCount = keyspaceMatch ? parseInt(keyspaceMatch[1]) : 0;

      return {
        memoryUsage,
        keyCount,
        hitRate: 95, // Placeholder - in production, track actual hits/misses
        missRate: 5,
        connected: this.client.status === 'ready'
      };
    } catch (error) {
      logger.error('Failed to get Redis stats:', error);
      return {
        memoryUsage: 0,
        keyCount: 0,
        hitRate: 0,
        missRate: 0,
        connected: false,
        error: error.message
      };
    }
  }

  /**
   * List operations for monitoring and queues
   */
  async lpush(key, value) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const serializedValue = JSON.stringify(value);
      return await this.client.lpush(key, serializedValue);
    } catch (error) {
      logger.error(`Redis LPUSH failed for key ${key}:`, error);
      return null;
    }
  }

  async lrange(key, start, stop) {
    if (!this.isAvailable()) {
      return [];
    }

    try {
      const values = await this.client.lrange(key, start, stop);
      return values.map(value => {
        try {
          return JSON.parse(value);
        } catch {
          return value;
        }
      });
    } catch (error) {
      logger.error(`Redis LRANGE failed for key ${key}:`, error);
      return [];
    }
  }

  async ltrim(key, start, stop) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      await this.client.ltrim(key, start, stop);
      return true;
    } catch (error) {
      logger.error(`Redis LTRIM failed for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Hash operations for complex data structures
   */
  async hset(key, field, value) {
    if (!this.isAvailable()) {
      return false;
    }

    try {
      const serializedValue = JSON.stringify(value);
      await this.client.hset(key, field, serializedValue);
      return true;
    } catch (error) {
      logger.error(`Redis HSET failed for key ${key}:`, error);
      return false;
    }
  }

  async hget(key, field) {
    if (!this.isAvailable()) {
      return null;
    }

    try {
      const value = await this.client.hget(key, field);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error(`Redis HGET failed for key ${key}:`, error);
      return null;
    }
  }

  async hgetall(key) {
    if (!this.isAvailable()) {
      return {};
    }

    try {
      const hash = await this.client.hgetall(key);
      const result = {};
      for (const [field, value] of Object.entries(hash)) {
        try {
          result[field] = JSON.parse(value);
        } catch {
          result[field] = value;
        }
      }
      return result;
    } catch (error) {
      logger.error(`Redis HGETALL failed for key ${key}:`, error);
      return {};
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.isAvailable()) {
        return { status: 'disconnected', timestamp: new Date().toISOString() };
      }

      const start = Date.now();
      await this.client.ping();
      const responseTime = Date.now() - start;

      return {
        status: 'connected',
        responseTime,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Close Redis connection
   */
  async close() {
    try {
      if (this.client && this.isConnected) {
        await this.client.quit();
        logger.info('Redis connection closed');
      }
      this.isInitialized = false;
      this.isConnected = false;
    } catch (error) {
      logger.error('Error closing Redis connection:', error);
    }
  }
}

// Create singleton instance
const redisService = new RedisService();

module.exports = redisService;
