/**
 * Fraud Detection Service
 * Monitors transactions for suspicious patterns and potential fraud
 */

const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');
const databaseService = require('./database');

class FraudDetectionService {
  constructor() {
    this.riskLevels = {
      LOW: 'low',
      MEDIUM: 'medium',
      HIGH: 'high',
      CRITICAL: 'critical'
    };

    this.fraudIndicators = {
      VELOCITY: 'velocity_check',
      AMOUNT: 'amount_anomaly',
      LOCATION: 'location_anomaly',
      DEVICE: 'device_anomaly',
      PATTERN: 'pattern_anomaly',
      BLACKLIST: 'blacklist_match'
    };

    // Risk thresholds
    this.thresholds = {
      maxTransactionsPerHour: 10,
      maxTransactionsPerDay: 50,
      maxAmountPerHour: 1000000, // 1M UGX
      maxAmountPerDay: 5000000,  // 5M UGX
      unusualAmountMultiplier: 10, // 10x average transaction
      maxFailedAttempts: 5
    };
  }

  /**
   * Analyze transaction for fraud risk
   */
  async analyzeTransaction(transactionData, userContext = {}) {
    try {
      const {
        userId,
        amount,
        currency,
        type,
        toUserId,
        ipAddress,
        userAgent,
        deviceId
      } = { ...transactionData, ...userContext };

      const riskFactors = [];
      let riskScore = 0;

      // Velocity checks
      const velocityRisk = await this.checkVelocity(userId, amount, type);
      if (velocityRisk.risk > 0) {
        riskFactors.push({
          indicator: this.fraudIndicators.VELOCITY,
          risk: velocityRisk.risk,
          details: velocityRisk.details
        });
        riskScore += velocityRisk.risk;
      }

      // Amount anomaly detection
      const amountRisk = await this.checkAmountAnomaly(userId, amount, currency);
      if (amountRisk.risk > 0) {
        riskFactors.push({
          indicator: this.fraudIndicators.AMOUNT,
          risk: amountRisk.risk,
          details: amountRisk.details
        });
        riskScore += amountRisk.risk;
      }

      // Device and location checks
      if (ipAddress || deviceId) {
        const deviceRisk = await this.checkDeviceAnomaly(userId, ipAddress, deviceId);
        if (deviceRisk.risk > 0) {
          riskFactors.push({
            indicator: this.fraudIndicators.DEVICE,
            risk: deviceRisk.risk,
            details: deviceRisk.details
          });
          riskScore += deviceRisk.risk;
        }
      }

      // Pattern analysis
      const patternRisk = await this.checkTransactionPattern(userId, type, toUserId);
      if (patternRisk.risk > 0) {
        riskFactors.push({
          indicator: this.fraudIndicators.PATTERN,
          risk: patternRisk.risk,
          details: patternRisk.details
        });
        riskScore += patternRisk.risk;
      }

      // Blacklist checks
      const blacklistRisk = await this.checkBlacklist(userId, toUserId, ipAddress);
      if (blacklistRisk.risk > 0) {
        riskFactors.push({
          indicator: this.fraudIndicators.BLACKLIST,
          risk: blacklistRisk.risk,
          details: blacklistRisk.details
        });
        riskScore += blacklistRisk.risk;
      }

      // Determine overall risk level
      const riskLevel = this.calculateRiskLevel(riskScore);
      const shouldBlock = riskLevel === this.riskLevels.CRITICAL;
      const requiresReview = riskLevel === this.riskLevels.HIGH;

      const analysis = {
        riskScore,
        riskLevel,
        shouldBlock,
        requiresReview,
        riskFactors,
        timestamp: new Date().toISOString()
      };

      // Log high-risk transactions
      if (riskLevel === this.riskLevels.HIGH || riskLevel === this.riskLevels.CRITICAL) {
        logger.security('High-risk transaction detected', {
          userId,
          amount,
          currency,
          type,
          riskLevel,
          riskScore,
          riskFactors: riskFactors.map(f => f.indicator)
        });
      }

      // Store analysis for future reference
      await this.storeRiskAnalysis(userId, analysis);

      return analysis;
    } catch (error) {
      logger.error('Fraud analysis failed:', error);
      // Return low risk if analysis fails to avoid blocking legitimate transactions
      return {
        riskScore: 0,
        riskLevel: this.riskLevels.LOW,
        shouldBlock: false,
        requiresReview: false,
        riskFactors: [],
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Check transaction velocity (frequency and amount)
   */
  async checkVelocity(userId, amount, type) {
    try {
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;
      const oneDay = 24 * oneHour;

      // Get recent transactions
      const hourlyKey = `velocity:${userId}:${Math.floor(now / oneHour)}`;
      const dailyKey = `velocity:${userId}:${Math.floor(now / oneDay)}`;

      const hourlyData = await redisService.get(hourlyKey) || { count: 0, amount: 0 };
      const dailyData = await redisService.get(dailyKey) || { count: 0, amount: 0 };

      let risk = 0;
      const details = [];

      // Check transaction count limits
      if (hourlyData.count >= this.thresholds.maxTransactionsPerHour) {
        risk += 30;
        details.push(`Exceeded hourly transaction limit: ${hourlyData.count}`);
      }

      if (dailyData.count >= this.thresholds.maxTransactionsPerDay) {
        risk += 50;
        details.push(`Exceeded daily transaction limit: ${dailyData.count}`);
      }

      // Check amount limits
      if (hourlyData.amount + amount > this.thresholds.maxAmountPerHour) {
        risk += 25;
        details.push(`Exceeded hourly amount limit`);
      }

      if (dailyData.amount + amount > this.thresholds.maxAmountPerDay) {
        risk += 40;
        details.push(`Exceeded daily amount limit`);
      }

      // Update velocity counters
      await this.updateVelocityCounters(userId, amount, now);

      return { risk: Math.min(risk, 100), details };
    } catch (error) {
      logger.error('Velocity check failed:', error);
      return { risk: 0, details: [] };
    }
  }

  /**
   * Check for amount anomalies
   */
  async checkAmountAnomaly(userId, amount, currency) {
    try {
      // Get user's transaction history to calculate average
      const supabase = databaseService.getSupabase();
      const { data: recentTransactions } = await supabase
        .from('transactions')
        .select('amount')
        .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
        .eq('currency', currency)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(20);

      if (!recentTransactions || recentTransactions.length < 5) {
        return { risk: 0, details: [] };
      }

      const amounts = recentTransactions.map(tx => tx.amount);
      const avgAmount = amounts.reduce((sum, amt) => sum + amt, 0) / amounts.length;
      const maxAmount = Math.max(...amounts);

      let risk = 0;
      const details = [];

      // Check if amount is unusually high compared to average
      if (amount > avgAmount * this.thresholds.unusualAmountMultiplier) {
        risk += 35;
        details.push(`Amount ${amount} is ${Math.round(amount / avgAmount)}x higher than average`);
      }

      // Check if amount is much higher than previous maximum
      if (amount > maxAmount * 5) {
        risk += 25;
        details.push(`Amount significantly higher than previous maximum`);
      }

      return { risk: Math.min(risk, 100), details };
    } catch (error) {
      logger.error('Amount anomaly check failed:', error);
      return { risk: 0, details: [] };
    }
  }

  /**
   * Check for device and location anomalies
   */
  async checkDeviceAnomaly(userId, ipAddress, deviceId) {
    try {
      let risk = 0;
      const details = [];

      // Check for new device
      if (deviceId) {
        const deviceKey = `user_devices:${userId}`;
        const knownDevices = await redisService.get(deviceKey) || [];
        
        if (!knownDevices.includes(deviceId)) {
          risk += 20;
          details.push('Transaction from new device');
          
          // Add device to known devices
          knownDevices.push(deviceId);
          await redisService.set(deviceKey, knownDevices, 30 * 24 * 60 * 60); // 30 days
        }
      }

      // Check for new IP address
      if (ipAddress) {
        const ipKey = `user_ips:${userId}`;
        const knownIPs = await redisService.get(ipKey) || [];
        
        if (!knownIPs.includes(ipAddress)) {
          risk += 15;
          details.push('Transaction from new IP address');
          
          // Add IP to known IPs (keep last 10)
          knownIPs.unshift(ipAddress);
          if (knownIPs.length > 10) {
            knownIPs.splice(10);
          }
          await redisService.set(ipKey, knownIPs, 7 * 24 * 60 * 60); // 7 days
        }
      }

      return { risk: Math.min(risk, 100), details };
    } catch (error) {
      logger.error('Device anomaly check failed:', error);
      return { risk: 0, details: [] };
    }
  }

  /**
   * Check transaction patterns
   */
  async checkTransactionPattern(userId, type, toUserId) {
    try {
      let risk = 0;
      const details = [];

      // Check for rapid repeated transactions to same recipient
      if (toUserId && type === 'transfer') {
        const patternKey = `pattern:${userId}:${toUserId}`;
        const recentTransfers = await redisService.get(patternKey) || [];
        
        // Count transfers in last hour
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        const recentCount = recentTransfers.filter(timestamp => timestamp > oneHourAgo).length;
        
        if (recentCount >= 5) {
          risk += 40;
          details.push(`${recentCount} transfers to same recipient in last hour`);
        }
        
        // Update pattern tracking
        recentTransfers.push(Date.now());
        // Keep only last 24 hours
        const filtered = recentTransfers.filter(timestamp => timestamp > Date.now() - (24 * 60 * 60 * 1000));
        await redisService.set(patternKey, filtered, 24 * 60 * 60);
      }

      return { risk: Math.min(risk, 100), details };
    } catch (error) {
      logger.error('Pattern check failed:', error);
      return { risk: 0, details: [] };
    }
  }

  /**
   * Check against blacklists
   */
  async checkBlacklist(userId, toUserId, ipAddress) {
    try {
      let risk = 0;
      const details = [];

      // Check user blacklist
      const userBlacklist = await redisService.get('blacklist:users') || [];
      if (userBlacklist.includes(userId)) {
        risk += 100;
        details.push('User is blacklisted');
      }

      if (toUserId && userBlacklist.includes(toUserId)) {
        risk += 80;
        details.push('Recipient is blacklisted');
      }

      // Check IP blacklist
      if (ipAddress) {
        const ipBlacklist = await redisService.get('blacklist:ips') || [];
        if (ipBlacklist.includes(ipAddress)) {
          risk += 90;
          details.push('IP address is blacklisted');
        }
      }

      return { risk: Math.min(risk, 100), details };
    } catch (error) {
      logger.error('Blacklist check failed:', error);
      return { risk: 0, details: [] };
    }
  }

  /**
   * Calculate overall risk level
   */
  calculateRiskLevel(riskScore) {
    if (riskScore >= 80) return this.riskLevels.CRITICAL;
    if (riskScore >= 60) return this.riskLevels.HIGH;
    if (riskScore >= 30) return this.riskLevels.MEDIUM;
    return this.riskLevels.LOW;
  }

  /**
   * Update velocity counters
   */
  async updateVelocityCounters(userId, amount, timestamp) {
    try {
      const oneHour = 60 * 60 * 1000;
      const oneDay = 24 * oneHour;

      const hourlyKey = `velocity:${userId}:${Math.floor(timestamp / oneHour)}`;
      const dailyKey = `velocity:${userId}:${Math.floor(timestamp / oneDay)}`;

      // Update hourly counter
      const hourlyData = await redisService.get(hourlyKey) || { count: 0, amount: 0 };
      hourlyData.count += 1;
      hourlyData.amount += amount;
      await redisService.set(hourlyKey, hourlyData, 3600); // 1 hour

      // Update daily counter
      const dailyData = await redisService.get(dailyKey) || { count: 0, amount: 0 };
      dailyData.count += 1;
      dailyData.amount += amount;
      await redisService.set(dailyKey, dailyData, 86400); // 24 hours
    } catch (error) {
      logger.error('Failed to update velocity counters:', error);
    }
  }

  /**
   * Store risk analysis
   */
  async storeRiskAnalysis(userId, analysis) {
    try {
      const analysisKey = `risk_analysis:${userId}:${Date.now()}`;
      await redisService.set(analysisKey, analysis, 7 * 24 * 60 * 60); // 7 days
    } catch (error) {
      logger.error('Failed to store risk analysis:', error);
    }
  }

  /**
   * Add user to blacklist
   */
  async addToBlacklist(type, value, reason) {
    try {
      const blacklistKey = `blacklist:${type}`;
      const blacklist = await redisService.get(blacklistKey) || [];
      
      if (!blacklist.includes(value)) {
        blacklist.push(value);
        await redisService.set(blacklistKey, blacklist, 365 * 24 * 60 * 60); // 1 year
        
        logger.security('Added to blacklist', {
          type,
          value: type === 'users' ? value.substring(0, 8) + '...' : value,
          reason
        });
      }
    } catch (error) {
      logger.error('Failed to add to blacklist:', error);
    }
  }

  /**
   * Remove from blacklist
   */
  async removeFromBlacklist(type, value) {
    try {
      const blacklistKey = `blacklist:${type}`;
      const blacklist = await redisService.get(blacklistKey) || [];
      const filtered = blacklist.filter(item => item !== value);
      
      await redisService.set(blacklistKey, filtered, 365 * 24 * 60 * 60);
      
      logger.security('Removed from blacklist', {
        type,
        value: type === 'users' ? value.substring(0, 8) + '...' : value
      });
    } catch (error) {
      logger.error('Failed to remove from blacklist:', error);
    }
  }
}

// Create singleton instance
const fraudDetectionService = new FraudDetectionService();

module.exports = fraudDetectionService;
