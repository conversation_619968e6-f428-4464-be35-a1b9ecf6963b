# JiraniPay Environment Configuration Template
# Copy this file to .env and fill in your actual values
#
# 🔒 SECURITY NOTICE:
# - NEVER commit actual credentials to version control
# - Use separate Supabase projects for each environment
# - Keep this file as a template only

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
# Set to 'development', 'staging', or 'production'
NODE_ENV=development
EXPO_PUBLIC_ENVIRONMENT=development

# =============================================================================
# SUPABASE CONFIGURATION - DEVELOPMENT
# =============================================================================
# Create a separate Supabase project for development
# Get these values from your DEVELOPMENT Supabase project dashboard
EXPO_PUBLIC_SUPABASE_URL=https://your-dev-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-dev-anon-key-here

# =============================================================================
# SUPABASE CONFIGURATION - STAGING (Optional)
# =============================================================================
# Create a separate Supabase project for staging
# EXPO_PUBLIC_STAGING_SUPABASE_URL=https://your-staging-project.supabase.co
# EXPO_PUBLIC_STAGING_SUPABASE_ANON_KEY=your-staging-anon-key-here

# =============================================================================
# SUPABASE CONFIGURATION - PRODUCTION
# =============================================================================
# Create a separate Supabase project for production
# EXPO_PUBLIC_PROD_SUPABASE_URL=https://your-prod-project.supabase.co
# EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY=your-prod-anon-key-here

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base URL for your backend API
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
# JWT secret for token signing (backend only) - Generate with: openssl rand -base64 32
JWT_SECRET=your-jwt-secret-here

# Encryption key for sensitive data - Generate with: openssl rand -base64 32
ENCRYPTION_KEY=your-encryption-key-here

# =============================================================================
# PAYMENT CONFIGURATION
# =============================================================================
# Payment provider settings (if applicable)
PAYMENT_PROVIDER_API_KEY=your-payment-api-key
PAYMENT_PROVIDER_SECRET=your-payment-secret

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================
# Sentry DSN for error tracking (optional)
SENTRY_DSN=your-sentry-dsn-here

# Log level (debug, info, warn, error)
LOG_LEVEL=info

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable debug mode in development
DEBUG=true

# Enable hot reload
EXPO_PUBLIC_HOT_RELOAD=true

# =============================================================================
# BACKEND SERVICE CONFIGURATION
# =============================================================================
# Backend service role key (NEVER expose in frontend)
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
# Mobile Money APIs (Production keys)
# MTN_API_KEY=your-mtn-production-key
# MTN_SUBSCRIPTION_KEY=your-mtn-subscription-key
# AIRTEL_CLIENT_ID=your-airtel-client-id
# AIRTEL_CLIENT_SECRET=your-airtel-client-secret

# SMS/Communication APIs
# TWILIO_ACCOUNT_SID=your-twilio-account-sid
# TWILIO_AUTH_TOKEN=your-twilio-auth-token
# AFRICASTALKING_USERNAME=your-africastalking-username
# AFRICASTALKING_API_KEY=your-africastalking-api-key

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Production API URL
EXPO_PUBLIC_PROD_API_URL=https://api.jiranipay.com

# Staging API URL (if using staging environment)
# EXPO_PUBLIC_STAGING_API_URL=https://staging-api.jiranipay.com

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Never commit the actual .env file to version control
# 2. Use different values for development, staging, and production
# 3. Rotate secrets regularly
# 4. Use strong, unique passwords and keys
# 5. Limit access to environment variables in production
