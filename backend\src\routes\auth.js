/**
 * Authentication Routes
 * Handles user registration, login, OTP verification, and password management
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { body, validationResult } = require('express-validator');

const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('../services/database');
const redisService = require('../services/redis');
const smsService = require('../services/sms');
const sessionService = require('../services/sessionService');
const passwordService = require('../services/passwordService');
const { 
  generateToken, 
  generateRefreshToken, 
  verifyToken, 
  blacklistToken,
  authenticate 
} = require('../middleware/auth');
const {
  asyncHand<PERSON>,
  ValidationError,
  AuthenticationError,
  ConflictError,
  NotFoundError
} = require('../middleware/errorHandler');

const router = express.Router();

/**
 * @route   POST /api/v1/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', [
  body('phoneNumber')
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),
  body('fullName')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*\d)/)
    .withMessage('Password must contain at least one letter and one number'),
  body('countryCode')
    .isIn(config.business.supportedCountries)
    .withMessage('Unsupported country')
], asyncHandler(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, fullName, email, password, countryCode } = req.body;

  // Normalize phone number
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  // Check if user already exists
  const supabase = databaseService.getSupabase();
  const { data: existingUser } = await supabase
    .from('user_profiles')
    .select('id')
    .eq('phone_number', normalizedPhone)
    .single();

  if (existingUser) {
    throw new ConflictError('User with this phone number already exists');
  }

  // Check email if provided
  if (email) {
    const { data: existingEmail } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('email', email)
      .single();

    if (existingEmail) {
      throw new ConflictError('User with this email already exists');
    }
  }

  // Validate password strength
  const passwordValidation = passwordService.validatePasswordStrength(password);
  if (!passwordValidation.isValid) {
    throw new ValidationError('Password validation failed', passwordValidation.errors.map(error => ({
      field: 'password',
      message: error
    })));
  }

  // Hash password
  const hashedPassword = await passwordService.hashPassword(password);

  // Create user in Supabase Auth
  const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
    phone: normalizedPhone,
    password: hashedPassword,
    email_confirm: false,
    phone_confirm: false,
    user_metadata: {
      full_name: fullName,
      country_code: countryCode
    }
  });

  if (authError) {
    logger.error('Failed to create auth user:', authError);
    throw new Error('Failed to create user account');
  }

  // Create user profile
  const userProfile = {
    user_id: authUser.user.id,
    phone_number: normalizedPhone,
    full_name: fullName,
    email: email || null,
    country_code: countryCode,
    is_verified: false,
    kyc_status: 'pending',
    created_at: new Date().toISOString()
  };

  const { data: profile, error: profileError } = await supabase
    .from('user_profiles')
    .insert(userProfile)
    .select()
    .single();

  if (profileError) {
    logger.error('Failed to create user profile:', profileError);
    // Clean up auth user
    await supabase.auth.admin.deleteUser(authUser.user.id);
    throw new Error('Failed to create user profile');
  }

  // Update password history
  await passwordService.updatePasswordHistory(authUser.user.id, hashedPassword);

  // Generate OTP for phone verification
  const otp = generateOTP();
  await redisService.storeOTP(normalizedPhone, otp, 300); // 5 minutes

  // Send OTP via SMS
  try {
    await smsService.sendOTP(normalizedPhone, otp);
    logger.audit('Registration OTP sent', { 
      userId: authUser.user.id, 
      phoneNumber: normalizedPhone 
    });
  } catch (error) {
    logger.error('Failed to send registration OTP:', error);
    // Don't fail registration if SMS fails
  }

  logger.audit('User registered', {
    userId: authUser.user.id,
    phoneNumber: normalizedPhone,
    email: email || 'none',
    countryCode
  });

  res.status(201).json({
    success: true,
    message: 'User registered successfully. Please verify your phone number.',
    data: {
      userId: authUser.user.id,
      phoneNumber: normalizedPhone,
      fullName: fullName,
      email: email || null,
      isVerified: false,
      otpSent: true
    }
  });
}));

/**
 * @route   POST /api/v1/auth/verify-phone
 * @desc    Verify phone number with OTP
 * @access  Public
 */
router.post('/verify-phone', [
  body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  body('otp').isLength({ min: 4, max: 6 }).withMessage('Valid OTP is required'),
  body('countryCode').isIn(config.business.supportedCountries).withMessage('Unsupported country')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, otp, countryCode } = req.body;
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  // Verify OTP
  const otpResult = await redisService.verifyOTP(normalizedPhone, otp);
  if (!otpResult.valid) {
    throw new AuthenticationError(otpResult.reason);
  }

  // Update user verification status
  const supabase = databaseService.getSupabase();
  const { data: user, error } = await supabase
    .from('user_profiles')
    .update({ 
      is_verified: true,
      phone_verified_at: new Date().toISOString()
    })
    .eq('phone_number', normalizedPhone)
    .select()
    .single();

  if (error) {
    logger.error('Failed to update user verification:', error);
    throw new Error('Failed to verify phone number');
  }

  // Update auth user
  await supabase.auth.admin.updateUserById(user.user_id, {
    phone_confirm: true
  });

  logger.audit('Phone number verified', {
    userId: user.user_id,
    phoneNumber: normalizedPhone
  });

  res.json({
    success: true,
    message: 'Phone number verified successfully',
    data: {
      userId: user.user_id,
      isVerified: true
    }
  });
}));

/**
 * @route   POST /api/v1/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', [
  body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  body('password').notEmpty().withMessage('Password is required'),
  body('countryCode').isIn(config.business.supportedCountries).withMessage('Unsupported country')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, password, countryCode } = req.body;
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  // Check account lockout
  const lockoutStatus = await passwordService.checkAccountLockout(normalizedPhone);
  if (lockoutStatus.isLocked) {
    throw new AuthenticationError(`Account is locked until ${lockoutStatus.lockedUntil.toISOString()}`);
  }

  // Get user
  const supabase = databaseService.getSupabase();
  const { data: user, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('phone_number', normalizedPhone)
    .single();

  if (error || !user) {
    throw new AuthenticationError('Invalid credentials');
  }

  // Check if user is active
  if (!user.is_active) {
    throw new AuthenticationError('Account is deactivated');
  }

  // Authenticate with Supabase
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    phone: normalizedPhone,
    password: password
  });

  if (authError) {
    // Record failed login attempt
    await passwordService.recordFailedLogin(normalizedPhone);

    logger.security('Login failed', {
      phoneNumber: normalizedPhone,
      error: authError.message,
      ip: req.ip
    });
    throw new AuthenticationError('Invalid credentials');
  }

  // Clear failed login attempts on successful login
  await passwordService.clearFailedLogins(normalizedPhone);

  // Create session with device tracking
  const deviceInfo = {
    userAgent: req.get('User-Agent'),
    platform: req.body.platform || 'unknown',
    appVersion: req.body.appVersion || 'unknown',
    deviceModel: req.body.deviceModel || 'unknown'
  };

  const { sessionId } = await sessionService.createSession(
    user.user_id,
    deviceInfo,
    req.ip
  );

  // Check for suspicious activity
  const suspiciousActivity = await sessionService.checkSuspiciousActivity(
    user.user_id,
    deviceInfo,
    req.ip
  );

  if (suspiciousActivity.isSuspicious) {
    logger.security('Suspicious login detected', {
      userId: user.user_id,
      indicators: suspiciousActivity.indicators,
      ip: req.ip
    });
    // Could trigger additional verification here
  }

  const tokenPayload = {
    userId: user.user_id,
    sessionId: sessionId,
    role: user.role || 'user'
  };

  const accessToken = generateToken(tokenPayload);
  const refreshToken = generateRefreshToken({ userId: user.user_id, sessionId });

  logger.audit('User logged in', {
    userId: user.user_id,
    phoneNumber: normalizedPhone,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.user_id,
        phoneNumber: normalizedPhone,
        fullName: user.full_name,
        email: user.email,
        isVerified: user.is_verified,
        kycStatus: user.kyc_status
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: config.jwt.expiresIn
      }
    }
  });
}));

/**
 * Helper function to normalize phone number
 */
function normalizePhoneNumber(phoneNumber, countryCode) {
  // Remove all non-digit characters
  let normalized = phoneNumber.replace(/\D/g, '');
  
  // Add country code if not present
  const countryCodes = {
    'UG': '256',
    'KE': '254',
    'TZ': '255',
    'RW': '250',
    'BI': '257',
    'ET': '251'
  };
  
  const code = countryCodes[countryCode];
  if (code && !normalized.startsWith(code)) {
    // Remove leading zero if present
    if (normalized.startsWith('0')) {
      normalized = normalized.substring(1);
    }
    normalized = code + normalized;
  }
  
  return '+' + normalized;
}

/**
 * Helper function to generate OTP
 */
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * @route   POST /api/v1/auth/resend-otp
 * @desc    Resend OTP for phone verification
 * @access  Public
 */
router.post('/resend-otp', [
  body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  body('countryCode').isIn(config.business.supportedCountries).withMessage('Unsupported country')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, countryCode } = req.body;
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  // Check if user exists
  const supabase = databaseService.getSupabase();
  const { data: user } = await supabase
    .from('user_profiles')
    .select('user_id, is_verified')
    .eq('phone_number', normalizedPhone)
    .single();

  if (!user) {
    throw new AuthenticationError('User not found');
  }

  if (user.is_verified) {
    throw new ConflictError('Phone number already verified');
  }

  // Generate new OTP
  const otp = generateOTP();
  await redisService.storeOTP(normalizedPhone, otp, 300); // 5 minutes

  // Send OTP via SMS
  try {
    await smsService.sendOTP(normalizedPhone, otp);
    logger.audit('OTP resent', {
      userId: user.user_id,
      phoneNumber: normalizedPhone
    });
  } catch (error) {
    logger.error('Failed to resend OTP:', error);
    throw new Error('Failed to send OTP');
  }

  res.json({
    success: true,
    message: 'OTP sent successfully',
    data: {
      phoneNumber: normalizedPhone,
      otpSent: true
    }
  });
}));

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', [
  body('refreshToken').notEmpty().withMessage('Refresh token is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { refreshToken } = req.body;

  try {
    // Verify refresh token
    const decoded = verifyToken(refreshToken);

    // Check if refresh token is blacklisted
    const isBlacklisted = await redisService.exists(`blacklist:${refreshToken}`);
    if (isBlacklisted) {
      throw new AuthenticationError('Refresh token has been revoked');
    }

    // Get user
    const supabase = databaseService.getSupabase();
    const { data: user } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', decoded.userId)
      .single();

    if (!user || !user.is_active) {
      throw new AuthenticationError('User not found or inactive');
    }

    // Generate new tokens
    const newSessionId = uuidv4();
    const tokenPayload = {
      userId: user.user_id,
      sessionId: newSessionId,
      role: user.role || 'user'
    };

    const newAccessToken = generateToken(tokenPayload);
    const newRefreshToken = generateRefreshToken({ userId: user.user_id, sessionId: newSessionId });

    // Update session in Redis
    await redisService.setSession(newSessionId, {
      userId: user.user_id,
      phoneNumber: user.phone_number,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString()
    }, 24 * 60 * 60); // 24 hours

    // Blacklist old refresh token
    await blacklistToken(refreshToken);

    logger.audit('Token refreshed', {
      userId: user.user_id,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: config.jwt.expiresIn
        }
      }
    });

  } catch (error) {
    logger.security('Token refresh failed', {
      error: error.message,
      ip: req.ip
    });
    throw new AuthenticationError('Invalid refresh token');
  }
}));

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout', authenticate, asyncHandler(async (req, res) => {
  try {
    // Blacklist current token
    await blacklistToken(req.token);

    // Delete session from Redis
    if (req.tokenPayload?.sessionId) {
      await redisService.deleteSession(req.tokenPayload.sessionId);
    }

    logger.audit('User logged out', {
      userId: req.user.user_id,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    logger.error('Logout failed:', error);
    // Still return success even if cleanup fails
    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  }
}));

/**
 * @route   POST /api/v1/auth/forgot-password
 * @desc    Initiate password reset
 * @access  Public
 */
router.post('/forgot-password', [
  body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  body('countryCode').isIn(config.business.supportedCountries).withMessage('Unsupported country')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, countryCode } = req.body;
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  // Check if user exists
  const supabase = databaseService.getSupabase();
  const { data: user } = await supabase
    .from('user_profiles')
    .select('user_id, full_name')
    .eq('phone_number', normalizedPhone)
    .single();

  if (!user) {
    // Don't reveal if user exists or not for security
    res.json({
      success: true,
      message: 'If the phone number is registered, you will receive a password reset code.'
    });
    return;
  }

  // Initiate password reset
  try {
    await passwordService.initiatePasswordReset(normalizedPhone, user.user_id);
  } catch (error) {
    logger.error('Failed to initiate password reset:', error);
    // Don't reveal the error to prevent enumeration attacks
  }

  res.json({
    success: true,
    message: 'If the phone number is registered, you will receive a password reset code.'
  });
}));

/**
 * @route   POST /api/v1/auth/reset-password
 * @desc    Reset password with OTP
 * @access  Public
 */
router.post('/reset-password', [
  body('phoneNumber').isMobilePhone('any').withMessage('Valid phone number is required'),
  body('countryCode').isIn(config.business.supportedCountries).withMessage('Unsupported country'),
  body('otp').isLength({ min: 4, max: 6 }).withMessage('Valid OTP is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*\d)/)
    .withMessage('Password must contain at least one letter and one number')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { phoneNumber, countryCode, otp, newPassword, resetToken } = req.body;
  const normalizedPhone = normalizePhoneNumber(phoneNumber, countryCode);

  try {
    // Complete password reset using password service
    const result = await passwordService.completePasswordReset(
      normalizedPhone,
      resetToken,
      newPassword
    );

    // Update password in Supabase Auth
    const supabase = databaseService.getSupabase();
    const { error } = await supabase.auth.admin.updateUserById(result.userId, {
      password: result.passwordHash
    });

    if (error) {
      logger.error('Failed to update password in auth:', error);
      throw new Error('Failed to reset password');
    }

  } catch (error) {
    logger.error('Password reset failed:', error);
    throw new AuthenticationError(error.message);
  }

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
}));

/**
 * @route   POST /api/v1/auth/change-password
 * @desc    Change password for authenticated user
 * @access  Private
 */
router.post('/change-password', authenticate, [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*\d)/)
    .withMessage('Password must contain at least one letter and one number'),
  body('confirmPassword').custom((value, { req }) => {
    if (value !== req.body.newPassword) {
      throw new Error('Password confirmation does not match');
    }
    return true;
  })
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { currentPassword, newPassword } = req.body;

  try {
    // Get current password hash from Supabase
    const supabase = databaseService.getSupabase();
    const { data: authUser } = await supabase.auth.admin.getUserById(req.user.user_id);

    if (!authUser) {
      throw new AuthenticationError('User not found');
    }

    // Change password using password service
    const result = await passwordService.changePassword(
      req.user.user_id,
      currentPassword,
      newPassword,
      authUser.user.encrypted_password
    );

    // Update password in Supabase Auth
    const { error } = await supabase.auth.admin.updateUserById(req.user.user_id, {
      password: result.passwordHash
    });

    if (error) {
      logger.error('Failed to update password in auth:', error);
      throw new Error('Failed to change password');
    }

    // Terminate all other sessions for security
    await sessionService.terminateAllUserSessions(req.user.user_id, req.tokenPayload.sessionId);

    res.json({
      success: true,
      message: 'Password changed successfully',
      data: {
        passwordStrength: result.strength,
        otherSessionsTerminated: true
      }
    });

  } catch (error) {
    logger.error('Password change failed:', error);
    throw new AuthenticationError(error.message);
  }
}));

/**
 * @route   GET /api/v1/auth/sessions
 * @desc    Get user's active sessions
 * @access  Private
 */
router.get('/sessions', authenticate, asyncHandler(async (req, res) => {
  const sessions = await sessionService.getUserSessions(req.user.user_id);

  res.json({
    success: true,
    data: {
      sessions: sessions.map(session => ({
        sessionId: session.sessionId,
        deviceInfo: session.deviceInfo,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity,
        isCurrent: session.sessionId === req.tokenPayload.sessionId
      }))
    }
  });
}));

/**
 * @route   DELETE /api/v1/auth/sessions/:sessionId
 * @desc    Terminate a specific session
 * @access  Private
 */
router.delete('/sessions/:sessionId', authenticate, asyncHandler(async (req, res) => {
  const { sessionId } = req.params;

  // Don't allow terminating current session
  if (sessionId === req.tokenPayload.sessionId) {
    throw new ValidationError('Cannot terminate current session. Use logout instead.');
  }

  const success = await sessionService.terminateSession(sessionId, 'user_terminated');

  if (!success) {
    throw new NotFoundError('Session not found');
  }

  res.json({
    success: true,
    message: 'Session terminated successfully'
  });
}));

/**
 * @route   DELETE /api/v1/auth/sessions
 * @desc    Terminate all sessions except current
 * @access  Private
 */
router.delete('/sessions', authenticate, asyncHandler(async (req, res) => {
  const terminatedCount = await sessionService.terminateAllUserSessions(
    req.user.user_id,
    req.tokenPayload.sessionId
  );

  res.json({
    success: true,
    message: `${terminatedCount} sessions terminated successfully`,
    data: {
      terminatedCount
    }
  });
}));

module.exports = router;
