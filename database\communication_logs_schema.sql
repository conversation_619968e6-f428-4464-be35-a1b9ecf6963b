-- Communication Logs Schema for JiraniPay
-- Tracks SMS and Email delivery for monitoring and compliance

-- SMS Delivery Logs
CREATE TABLE IF NOT EXISTS public.sms_delivery_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    phone_number TEXT,
    purpose TEXT NOT NULL, -- '2FA', 'login', 'transaction', 'password_reset'
    status TEXT NOT NULL, -- 'sent', 'delivered', 'failed', 'verified'
    error_message TEXT,
    provider TEXT, -- 'twilio', 'messagebird', 'supabase'
    message_id TEXT, -- Provider's message ID for tracking
    cost_cents INTEGER, -- Cost in cents for monitoring
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    environment TEXT DEFAULT 'production', -- 'production', 'development'
    metadata JSONB DEFAULT '{}', -- Additional tracking data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Delivery Logs
CREATE TABLE IF NOT EXISTS public.email_delivery_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email_address TEXT,
    purpose TEXT NOT NULL, -- '2FA', 'welcome', 'password_reset', 'transaction_alert'
    status TEXT NOT NULL, -- 'sent', 'delivered', 'opened', 'failed', 'verified'
    error_message TEXT,
    provider TEXT, -- 'sendgrid', 'aws_ses', 'mailgun', 'supabase'
    message_id TEXT, -- Provider's message ID for tracking
    subject TEXT,
    template_used TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    environment TEXT DEFAULT 'production',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Communication Rate Limiting
CREATE TABLE IF NOT EXISTS public.communication_rate_limits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    identifier TEXT NOT NULL, -- phone number or email
    communication_type TEXT NOT NULL, -- 'sms' or 'email'
    purpose TEXT NOT NULL, -- '2FA', 'login', etc.
    attempt_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(identifier, communication_type, purpose)
);

-- 2FA Code Storage (for verification tracking)
CREATE TABLE IF NOT EXISTS public.verification_codes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    code_hash TEXT NOT NULL, -- Hashed version of the code
    purpose TEXT NOT NULL, -- '2FA', 'login', 'password_reset'
    method TEXT NOT NULL, -- 'sms' or 'email'
    contact_info TEXT, -- phone or email (masked for security)
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    attempts INTEGER DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_sms_logs_user_id ON public.sms_delivery_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_sms_logs_timestamp ON public.sms_delivery_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_sms_logs_status ON public.sms_delivery_logs(status);
CREATE INDEX IF NOT EXISTS idx_sms_logs_purpose ON public.sms_delivery_logs(purpose);

CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON public.email_delivery_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_timestamp ON public.email_delivery_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON public.email_delivery_logs(status);
CREATE INDEX IF NOT EXISTS idx_email_logs_purpose ON public.email_delivery_logs(purpose);

CREATE INDEX IF NOT EXISTS idx_rate_limits_identifier ON public.communication_rate_limits(identifier);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window ON public.communication_rate_limits(window_start);

CREATE INDEX IF NOT EXISTS idx_verification_codes_user_id ON public.verification_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_codes_expires ON public.verification_codes(expires_at);
CREATE INDEX IF NOT EXISTS idx_verification_codes_purpose ON public.verification_codes(purpose);

-- Row Level Security (RLS) Policies
ALTER TABLE public.sms_delivery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_delivery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.communication_rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.verification_codes ENABLE ROW LEVEL SECURITY;

-- SMS Logs Policies
CREATE POLICY "Users can view their own SMS logs" ON public.sms_delivery_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can insert SMS logs" ON public.sms_delivery_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Service can update SMS logs" ON public.sms_delivery_logs
    FOR UPDATE USING (true);

-- Email Logs Policies
CREATE POLICY "Users can view their own email logs" ON public.email_delivery_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can insert email logs" ON public.email_delivery_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Service can update email logs" ON public.email_delivery_logs
    FOR UPDATE USING (true);

-- Rate Limits Policies
CREATE POLICY "Service can manage rate limits" ON public.communication_rate_limits
    FOR ALL USING (true);

-- Verification Codes Policies
CREATE POLICY "Users can view their own verification codes" ON public.verification_codes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage verification codes" ON public.verification_codes
    FOR ALL USING (true);

-- Functions for cleanup and maintenance

-- Function to clean up expired verification codes
CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.verification_codes 
    WHERE expires_at < NOW() - INTERVAL '1 hour';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up old communication logs (keep 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_communication_logs()
RETURNS INTEGER AS $$
DECLARE
    sms_deleted INTEGER;
    email_deleted INTEGER;
    total_deleted INTEGER;
BEGIN
    DELETE FROM public.sms_delivery_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS sms_deleted = ROW_COUNT;
    
    DELETE FROM public.email_delivery_logs 
    WHERE created_at < NOW() - INTERVAL '90 days';
    GET DIAGNOSTICS email_deleted = ROW_COUNT;
    
    total_deleted := sms_deleted + email_deleted;
    RETURN total_deleted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset rate limits for expired windows
CREATE OR REPLACE FUNCTION reset_expired_rate_limits()
RETURNS INTEGER AS $$
DECLARE
    reset_count INTEGER;
BEGIN
    DELETE FROM public.communication_rate_limits 
    WHERE window_start < NOW() - INTERVAL '1 hour'
    AND blocked_until IS NULL;
    
    UPDATE public.communication_rate_limits 
    SET blocked_until = NULL 
    WHERE blocked_until < NOW();
    
    GET DIAGNOSTICS reset_count = ROW_COUNT;
    RETURN reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create scheduled cleanup jobs (requires pg_cron extension)
-- These would typically be set up by a database administrator

-- Clean up expired codes every hour
-- SELECT cron.schedule('cleanup-verification-codes', '0 * * * *', 'SELECT cleanup_expired_verification_codes();');

-- Clean up old logs daily at 2 AM
-- SELECT cron.schedule('cleanup-communication-logs', '0 2 * * *', 'SELECT cleanup_old_communication_logs();');

-- Reset rate limits every hour
-- SELECT cron.schedule('reset-rate-limits', '0 * * * *', 'SELECT reset_expired_rate_limits();');

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.sms_delivery_logs TO authenticated;
GRANT ALL ON public.email_delivery_logs TO authenticated;
GRANT ALL ON public.communication_rate_limits TO authenticated;
GRANT ALL ON public.verification_codes TO authenticated;

-- Grant execute permissions on cleanup functions
GRANT EXECUTE ON FUNCTION cleanup_expired_verification_codes() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_communication_logs() TO authenticated;
GRANT EXECUTE ON FUNCTION reset_expired_rate_limits() TO authenticated;

-- Comments for documentation
COMMENT ON TABLE public.sms_delivery_logs IS 'Tracks SMS delivery for 2FA, notifications, and compliance';
COMMENT ON TABLE public.email_delivery_logs IS 'Tracks email delivery for 2FA, notifications, and compliance';
COMMENT ON TABLE public.communication_rate_limits IS 'Prevents spam and abuse of communication services';
COMMENT ON TABLE public.verification_codes IS 'Stores hashed verification codes for secure validation';

COMMENT ON FUNCTION cleanup_expired_verification_codes() IS 'Removes expired verification codes older than 1 hour';
COMMENT ON FUNCTION cleanup_old_communication_logs() IS 'Archives communication logs older than 90 days';
COMMENT ON FUNCTION reset_expired_rate_limits() IS 'Resets expired rate limiting windows';
