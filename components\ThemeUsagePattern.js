import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

const ComponentName = () => {
  // 1. Get theme with fallback FIRST
  const themeContext = useTheme();
  const theme = themeContext?.theme || {
    colors: {
      primary: '#E67E22',
      background: '#fcf7f0',
      // other fallback colors...
    }
  };
  
  // 2. Create styles with theme
  const styles = createStyles(theme);
  
  // 3. Only use theme after it's safely initialized
  console.log('Theme color:', theme.colors.primary);
  
  return (
    <View style={styles.container}>
      <Text style={styles.text}>Safe theme usage</Text>
    </View>
  );
};

// 4. Theme-aware styles function
const createStyles = (theme) => StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background,
  },
  text: {
    color: theme.colors.text,
  },
});

export default ComponentName;