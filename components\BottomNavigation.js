import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const { width } = Dimensions.get('window');

const BottomNavigation = ({ activeTab, onTabPress }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const tabs = [
    {
      id: 'home',
      title: t('navigation.home'),
      icon: 'home',
      iconType: 'Ionicons',
    },
    {
      id: 'bills',
      title: t('navigation.bills'),
      icon: 'receipt',
      iconType: 'Ionicons',
    },
    {
      id: 'qr',
      title: t('navigation.qrPay'),
      icon: 'qr-code-scanner',
      iconType: 'MaterialIcons',
      isCenter: true,
    },
    {
      id: 'wallet',
      title: t('navigation.wallet'),
      icon: 'wallet',
      iconType: 'Ionicons',
    },
    {
      id: 'profile',
      title: t('navigation.profile'),
      icon: 'person',
      iconType: 'Ionicons',
    },
  ];

  const handleTabPress = (tabId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onTabPress(tabId);
  };

  const renderIcon = (tab) => {
    const isActive = activeTab === tab.id;
    const iconColor = isActive ? theme.colors.tabBarActive : theme.colors.tabBarInactive;
    const iconSize = tab.isCenter ? 28 : 24;

    if (tab.iconType === 'MaterialIcons') {
      return <MaterialIcons name={tab.icon} size={iconSize} color={iconColor} />;
    }
    return <Ionicons name={tab.icon} size={iconSize} color={iconColor} />;
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tab,
              tab.isCenter && styles.centerTab,
              activeTab === tab.id && styles.activeTab,
            ]}
            onPress={() => handleTabPress(tab.id)}
            activeOpacity={0.7}
          >
            {tab.isCenter ? (
              <View style={styles.centerIconContainer}>
                {renderIcon(tab)}
              </View>
            ) : (
              <>
                {renderIcon(tab)}
                <Text
                  style={[
                    styles.tabText,
                    activeTab === tab.id && styles.activeTabText,
                  ]}
                >
                  {tab.title}
                </Text>
              </>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.tabBarBackground,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingBottom: 20,
    paddingTop: 10,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 12,
    minWidth: 60,
  },
  centerTab: {
    backgroundColor: theme.colors.primary,
    borderRadius: 30,
    width: 60,
    height: 60,
    marginTop: -20,
    shadowColor: theme.colors.primaryDark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  activeTab: {
    backgroundColor: theme.colors.tabBarActive + '20',
  },
  centerIconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.tabBarInactive,
    marginTop: 4,
  },
  activeTabText: {
    color: theme.colors.tabBarActive,
    fontWeight: '600',
  },
});

export default BottomNavigation;
