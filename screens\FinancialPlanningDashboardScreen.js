/**
 * Financial Planning Dashboard Screen
 * Main dashboard for financial planning with budgets, goals,
 * recommendations, and comprehensive financial insights
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import financialPlanningService from '../services/financialPlanningService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const FinancialPlanningDashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [budgets, setBudgets] = useState([]);
  const [goals, setGoals] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [financialProfile, setFinancialProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadFinancialPlanningData();
  }, []);

  const loadFinancialPlanningData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view financial planning');
        navigation.goBack();
        return;
      }

      // Load financial planning data in parallel
      const [budgetsResult, goalsResult, recommendationsResult] = await Promise.all([
        financialPlanningService.getUserBudgets(userId, { isActive: true }),
        financialPlanningService.getUserFinancialGoals(userId, { isActive: true }),
        financialPlanningService.generateFinancialRecommendations(userId)
      ]);

      if (budgetsResult.success) {
        setBudgets(budgetsResult.budgets);
      }

      if (goalsResult.success) {
        setGoals(goalsResult.goals);
      }

      if (recommendationsResult.success) {
        setRecommendations(recommendationsResult.recommendations);
        setFinancialProfile(recommendationsResult.financialProfile);
      }

    } catch (error) {
      console.error('❌ Error loading financial planning data:', error);
      Alert.alert('Error', 'Failed to load financial planning data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadFinancialPlanningData(true);
  };

  const handleCreateBudget = () => {
    navigation.navigate('BudgetCreation');
  };

  const handleCreateGoal = () => {
    navigation.navigate('FinancialGoalCreation');
  };

  const handleBudgetPress = (budget) => {
    navigation.navigate('BudgetDetails', { budgetId: budget.id });
  };

  const handleGoalPress = (goal) => {
    navigation.navigate('FinancialGoalDetails', { goalId: goal.id });
  };

  const getGoalTypeIcon = (goalType) => {
    const icons = {
      savings: 'wallet',
      investment: 'trending-up',
      debt_payoff: 'card',
      retirement: 'time',
      emergency_fund: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      home_purchase: 'home'
    };
    return icons[goalType] || 'flag';
  };

  const getGoalTypeColor = (goalType) => {
    const colors = {
      savings: '#4ECDC4',
      investment: '#45B7D1',
      debt_payoff: '#FF6B35',
      retirement: '#6C5CE7',
      emergency_fund: '#96CEB4',
      vacation: '#FECA57',
      education: '#A8E6CF',
      home_purchase: '#FFB6C1'
    };
    return colors[goalType] || '#4ECDC4';
  };

  const getRecommendationIcon = (type) => {
    const icons = {
      emergency_fund: 'shield-checkmark',
      first_savings: 'wallet',
      start_investing: 'trending-up',
      create_budget: 'calculator',
      set_goals: 'flag'
    };
    return icons[type] || 'bulb';
  };

  const renderFinancialOverview = () => {
    if (!financialProfile) return null;

    const totalSavings = financialProfile.savings.totalSavings || 0;
    const totalInvestments = financialProfile.investments.totalPortfolioValue || 0;
    const totalWealth = totalSavings + totalInvestments;

    return (
      <View style={styles.overviewCard}>
        <View style={styles.overviewHeader}>
          <Text style={styles.overviewTitle}>Financial Overview</Text>
          <TouchableOpacity onPress={() => navigation.navigate('FinancialAnalytics')}>
            <Ionicons name="analytics-outline" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.totalWealth}>
          {formatCurrency(totalWealth, 'UGX')}
        </Text>
        <Text style={styles.wealthLabel}>Total Net Worth</Text>
        
        <View style={styles.wealthBreakdown}>
          <View style={styles.wealthItem}>
            <Text style={styles.wealthValue}>{formatCurrency(totalSavings, 'UGX')}</Text>
            <Text style={styles.wealthItemLabel}>Savings</Text>
          </View>
          <View style={styles.wealthItem}>
            <Text style={styles.wealthValue}>{formatCurrency(totalInvestments, 'UGX')}</Text>
            <Text style={styles.wealthItemLabel}>Investments</Text>
          </View>
          <View style={styles.wealthItem}>
            <Text style={styles.wealthValue}>{goals.length}</Text>
            <Text style={styles.wealthItemLabel}>Goals</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity style={styles.actionItem} onPress={handleCreateBudget}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.primary + '20' }]}>
            <Ionicons name="calculator" size={24} color={theme.colors.primary} />
          </View>
          <Text style={styles.actionLabel}>Create Budget</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={handleCreateGoal}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.success + '20' }]}>
            <Ionicons name="flag" size={24} color={theme.colors.success} />
          </View>
          <Text style={styles.actionLabel}>Set Goal</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('RetirementPlanning')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.warning + '20' }]}>
            <Ionicons name="time" size={24} color={theme.colors.warning} />
          </View>
          <Text style={styles.actionLabel}>Retirement</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('FinancialReports')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.info + '20' }]}>
            <Ionicons name="bar-chart" size={24} color={theme.colors.info} />
          </View>
          <Text style={styles.actionLabel}>Reports</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderRecommendations = () => {
    if (recommendations.length === 0) return null;

    return (
      <View style={styles.recommendationsCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Recommendations</Text>
          <TouchableOpacity onPress={() => navigation.navigate('FinancialRecommendations')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {recommendations.slice(0, 3).map((recommendation, index) => (
          <View key={index} style={styles.recommendationItem}>
            <View style={[styles.recommendationIcon, { backgroundColor: theme.colors.primary + '20' }]}>
              <Ionicons name={getRecommendationIcon(recommendation.type)} size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.recommendationContent}>
              <Text style={styles.recommendationTitle}>{recommendation.title}</Text>
              <Text style={styles.recommendationDescription}>{recommendation.description}</Text>
            </View>
            <TouchableOpacity style={styles.recommendationAction}>
              <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    );
  };

  const renderActiveGoals = () => {
    if (goals.length === 0) return null;

    return (
      <View style={styles.goalsCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Active Goals</Text>
          <TouchableOpacity onPress={() => navigation.navigate('FinancialGoalsList')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {goals.slice(0, 3).map((goal) => (
          <TouchableOpacity 
            key={goal.id} 
            style={styles.goalItem}
            onPress={() => handleGoalPress(goal)}
          >
            <View style={[styles.goalIcon, { backgroundColor: getGoalTypeColor(goal.goalType) }]}>
              <Ionicons name={getGoalTypeIcon(goal.goalType)} size={16} color={theme.colors.white} />
            </View>
            <View style={styles.goalContent}>
              <Text style={styles.goalName}>{goal.goalName}</Text>
              <Text style={styles.goalProgress}>
                {formatCurrency(goal.currentAmount, 'UGX')} of {formatCurrency(goal.targetAmount, 'UGX')}
              </Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill, 
                    { 
                      width: `${Math.min(goal.progressPercentage, 100)}%`,
                      backgroundColor: getGoalTypeColor(goal.goalType)
                    }
                  ]} 
                />
              </View>
            </View>
            <Text style={styles.goalPercentage}>{goal.progressPercentage.toFixed(0)}%</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderRecentBudgets = () => {
    if (budgets.length === 0) return null;

    return (
      <View style={styles.budgetsCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Active Budgets</Text>
          <TouchableOpacity onPress={() => navigation.navigate('BudgetsList')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {budgets.slice(0, 2).map((budget) => (
          <TouchableOpacity 
            key={budget.id} 
            style={styles.budgetItem}
            onPress={() => handleBudgetPress(budget)}
          >
            <View style={styles.budgetHeader}>
              <Text style={styles.budgetName}>{budget.budgetName}</Text>
              <Text style={styles.budgetPeriod}>{budget.budgetPeriod}</Text>
            </View>
            <View style={styles.budgetAmounts}>
              <View style={styles.budgetAmount}>
                <Text style={styles.budgetLabel}>Income</Text>
                <Text style={[styles.budgetValue, { color: theme.colors.success }]}>
                  {formatCurrency(budget.totalIncome, budget.currency)}
                </Text>
              </View>
              <View style={styles.budgetAmount}>
                <Text style={styles.budgetLabel}>Expenses</Text>
                <Text style={[styles.budgetValue, { color: theme.colors.error }]}>
                  {formatCurrency(budget.totalExpenses, budget.currency)}
                </Text>
              </View>
              <View style={styles.budgetAmount}>
                <Text style={styles.budgetLabel}>Savings</Text>
                <Text style={[styles.budgetValue, { color: theme.colors.primary }]}>
                  {formatCurrency(budget.totalSavings, budget.currency)}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="calculator-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Start Your Financial Planning</Text>
      <Text style={styles.emptyDescription}>
        Create budgets, set goals, and get personalized recommendations to achieve financial success
      </Text>
      <View style={styles.emptyActions}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleCreateBudget}>
          <Text style={styles.primaryButtonText}>Create Budget</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.secondaryButton} onPress={handleCreateGoal}>
          <Text style={styles.secondaryButtonText}>Set Goal</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading financial planning...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const hasData = budgets.length > 0 || goals.length > 0 || recommendations.length > 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Financial Planning</Text>
        <TouchableOpacity onPress={() => navigation.navigate('FinancialSettings')}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {hasData ? (
          <>
            {renderFinancialOverview()}
            {renderQuickActions()}
            {renderRecommendations()}
            {renderActiveGoals()}
            {renderRecentBudgets()}
          </>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  overviewCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  overviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  overviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalWealth: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  wealthLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
  },
  wealthBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  wealthItem: {
    alignItems: 'center',
  },
  wealthValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  wealthItemLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  quickActionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionItem: {
    alignItems: 'center',
    flex: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'center',
  },
  recommendationsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  recommendationIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recommendationContent: {
    flex: 1,
  },
  recommendationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  recommendationDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  recommendationAction: {
    padding: 4,
  },
  goalsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  goalIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  goalContent: {
    flex: 1,
  },
  goalName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  goalProgress: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 6,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  goalPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  budgetsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  budgetItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  budgetPeriod: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textTransform: 'capitalize',
  },
  budgetAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  budgetAmount: {
    alignItems: 'center',
  },
  budgetLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  budgetValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  emptyActions: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  primaryButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  secondaryButtonText: {
    color: theme.colors.text,
    fontSize: 14,
    fontWeight: '600',
  },
});

export default FinancialPlanningDashboardScreen;
