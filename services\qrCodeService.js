import AsyncStorage from '@react-native-async-storage/async-storage';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import authService from './authService';

/**
 * QR Code Service for JiraniPay
 * Handles QR code generation, parsing, and validation
 */
class QRCodeService {
  constructor() {
    this.qrCodePrefix = 'JIRANIPAY';
    this.version = '1.0';
    
    // QR Code types
    this.qrTypes = {
      PAYMENT_REQUEST: 'payment_request',
      RECEIVE_PAYMENT: 'receive_payment',
      USER_PROFILE: 'user_profile',
    };
  }

  /**
   * Generate QR code data for receiving payments
   * @param {Object} params - QR code parameters
   * @returns {Object} - QR code data and formatted string
   */
  async generateReceivePaymentQR(params = {}) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const qrData = {
        prefix: this.qrCodePrefix,
        version: this.version,
        type: this.qrTypes.RECEIVE_PAYMENT,
        timestamp: new Date().toISOString(),
        user: {
          id: user.id,
          phone: user.phone,
          name: user.user_metadata?.full_name || 'JiraniPay User',
        },
        amount: params.amount || null, // null for open amount
        purpose: params.purpose || 'Payment',
        currency: 'UGX',
        expires: params.expires || null, // Optional expiration
      };

      const qrString = this.encodeQRData(qrData);
      
      console.log('✅ Generated receive payment QR:', qrData);
      
      return {
        success: true,
        qrData: qrData,
        qrString: qrString,
        displayText: this.formatDisplayText(qrData),
      };
    } catch (error) {
      console.error('Generate receive QR error:', error);
      return { success: false, error: 'Failed to generate QR code' };
    }
  }

  /**
   * Generate QR code data for payment requests
   * @param {Object} params - Payment request parameters
   * @returns {Object} - QR code data and formatted string
   */
  async generatePaymentRequestQR(params) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      if (!params.amount || params.amount <= 0) {
        return { success: false, error: 'Valid amount is required for payment requests' };
      }

      const qrData = {
        prefix: this.qrCodePrefix,
        version: this.version,
        type: this.qrTypes.PAYMENT_REQUEST,
        timestamp: new Date().toISOString(),
        user: {
          id: user.id,
          phone: user.phone,
          name: user.user_metadata?.full_name || 'JiraniPay User',
        },
        amount: params.amount,
        purpose: params.purpose || 'Payment Request',
        currency: 'UGX',
        expires: params.expires || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours default
        reference: this.generateReference(),
      };

      const qrString = this.encodeQRData(qrData);
      
      console.log('✅ Generated payment request QR:', qrData);
      
      return {
        success: true,
        qrData: qrData,
        qrString: qrString,
        displayText: this.formatDisplayText(qrData),
      };
    } catch (error) {
      console.error('Generate payment request QR error:', error);
      return { success: false, error: 'Failed to generate payment request QR' };
    }
  }

  /**
   * Parse scanned QR code data
   * @param {string} qrString - Scanned QR code string
   * @returns {Object} - Parsed QR data or error
   */
  parseQRCode(qrString) {
    try {
      if (!qrString || typeof qrString !== 'string') {
        return { success: false, error: 'Invalid QR code data' };
      }

      // Check if it's a JiraniPay QR code
      if (!qrString.startsWith(this.qrCodePrefix)) {
        return { success: false, error: 'Not a JiraniPay QR code' };
      }

      const qrData = this.decodeQRData(qrString);
      
      if (!qrData) {
        return { success: false, error: 'Failed to decode QR code' };
      }

      // Validate QR code structure
      const validation = this.validateQRData(qrData);
      if (!validation.success) {
        return validation;
      }

      // Check expiration
      if (qrData.expires) {
        const expirationDate = new Date(qrData.expires);
        if (expirationDate < new Date()) {
          return { success: false, error: 'QR code has expired' };
        }
      }

      console.log('✅ Parsed QR code:', qrData);
      
      return {
        success: true,
        qrData: qrData,
        displayText: this.formatDisplayText(qrData),
      };
    } catch (error) {
      console.error('Parse QR code error:', error);
      return { success: false, error: 'Invalid QR code format' };
    }
  }

  /**
   * Encode QR data to string
   * @param {Object} qrData - QR data object
   * @returns {string} - Encoded QR string
   */
  encodeQRData(qrData) {
    try {
      const jsonString = JSON.stringify(qrData);
      const base64String = btoa(jsonString);
      return `${this.qrCodePrefix}:${base64String}`;
    } catch (error) {
      console.error('Encode QR data error:', error);
      throw new Error('Failed to encode QR data');
    }
  }

  /**
   * Decode QR string to data
   * @param {string} qrString - Encoded QR string
   * @returns {Object} - Decoded QR data
   */
  decodeQRData(qrString) {
    try {
      const base64Part = qrString.replace(`${this.qrCodePrefix}:`, '');
      const jsonString = atob(base64Part);
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Decode QR data error:', error);
      return null;
    }
  }

  /**
   * Validate QR data structure
   * @param {Object} qrData - QR data to validate
   * @returns {Object} - Validation result
   */
  validateQRData(qrData) {
    try {
      // Check required fields
      if (!qrData.prefix || qrData.prefix !== this.qrCodePrefix) {
        return { success: false, error: 'Invalid QR code prefix' };
      }

      if (!qrData.version) {
        return { success: false, error: 'Missing QR code version' };
      }

      if (!qrData.type || !Object.values(this.qrTypes).includes(qrData.type)) {
        return { success: false, error: 'Invalid QR code type' };
      }

      if (!qrData.user || !qrData.user.id || !qrData.user.phone) {
        return { success: false, error: 'Invalid user data in QR code' };
      }

      // Validate amount if present
      if (qrData.amount !== null && (typeof qrData.amount !== 'number' || qrData.amount <= 0)) {
        return { success: false, error: 'Invalid amount in QR code' };
      }

      return { success: true };
    } catch (error) {
      console.error('Validate QR data error:', error);
      return { success: false, error: 'QR code validation failed' };
    }
  }

  /**
   * Format display text for QR code
   * @param {Object} qrData - QR data
   * @returns {string} - Formatted display text
   */
  formatDisplayText(qrData) {
    try {
      const { user, amount, purpose, type } = qrData;
      
      if (type === this.qrTypes.PAYMENT_REQUEST) {
        return `Payment Request\n${user.name}\n${user.phone}\nAmount: UGX ${amount?.toLocaleString()}\nPurpose: ${purpose}`;
      } else if (type === this.qrTypes.RECEIVE_PAYMENT) {
        if (amount) {
          return `Pay ${user.name}\n${user.phone}\nAmount: UGX ${amount.toLocaleString()}\nPurpose: ${purpose}`;
        } else {
          return `Pay ${user.name}\n${user.phone}\nEnter amount\nPurpose: ${purpose}`;
        }
      }
      
      return `JiraniPay QR Code\n${user.name}\n${user.phone}`;
    } catch (error) {
      console.error('Format display text error:', error);
      return 'JiraniPay QR Code';
    }
  }

  /**
   * Generate unique reference for payment requests
   * @returns {string} - Unique reference
   */
  generateReference() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `QR${timestamp}${random}`.toUpperCase();
  }

  /**
   * Save QR code to device gallery
   * @param {string} qrCodeUri - QR code image URI
   * @returns {Object} - Save result
   */
  async saveQRToGallery(qrCodeUri) {
    try {
      const permission = await MediaLibrary.requestPermissionsAsync();
      if (!permission.granted) {
        return { success: false, error: 'Gallery permission denied' };
      }

      const asset = await MediaLibrary.createAssetAsync(qrCodeUri);
      await MediaLibrary.createAlbumAsync('JiraniPay QR Codes', asset, false);
      
      return { success: true, message: 'QR code saved to gallery' };
    } catch (error) {
      console.error('Save QR to gallery error:', error);
      return { success: false, error: 'Failed to save QR code' };
    }
  }

  /**
   * Share QR code
   * @param {string} qrCodeUri - QR code image URI
   * @param {Object} qrData - QR data for sharing text
   * @returns {Object} - Share result
   */
  async shareQRCode(qrCodeUri, qrData) {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        return { success: false, error: 'Sharing not available on this device' };
      }

      const shareText = this.formatShareText(qrData);
      
      await Sharing.shareAsync(qrCodeUri, {
        mimeType: 'image/png',
        dialogTitle: 'Share JiraniPay QR Code',
        UTI: 'public.png',
      });
      
      return { success: true, message: 'QR code shared successfully' };
    } catch (error) {
      console.error('Share QR code error:', error);
      return { success: false, error: 'Failed to share QR code' };
    }
  }

  /**
   * Format text for sharing QR code
   * @param {Object} qrData - QR data
   * @returns {string} - Formatted share text
   */
  formatShareText(qrData) {
    const { user, amount, purpose, type } = qrData;
    
    if (type === this.qrTypes.PAYMENT_REQUEST) {
      return `💰 Payment Request via JiraniPay\n\nFrom: ${user.name}\nPhone: ${user.phone}\nAmount: UGX ${amount?.toLocaleString()}\nPurpose: ${purpose}\n\nScan this QR code with JiraniPay to pay instantly!`;
    } else {
      return `💳 Pay ${user.name} via JiraniPay\n\nPhone: ${user.phone}\n${amount ? `Amount: UGX ${amount.toLocaleString()}` : 'Enter amount when paying'}\nPurpose: ${purpose}\n\nScan this QR code with JiraniPay to pay instantly!`;
    }
  }

  /**
   * Get recent QR codes from storage
   * @returns {Array} - Recent QR codes
   */
  async getRecentQRCodes() {
    try {
      const stored = await AsyncStorage.getItem('jiranipay_recent_qr_codes');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Get recent QR codes error:', error);
      return [];
    }
  }

  /**
   * Save QR code to recent list
   * @param {Object} qrData - QR data to save
   */
  async saveToRecentQRCodes(qrData) {
    try {
      const recent = await this.getRecentQRCodes();
      const newItem = {
        ...qrData,
        savedAt: new Date().toISOString(),
      };
      
      // Add to beginning and limit to 10 items
      const updated = [newItem, ...recent.filter(item => item.user?.id !== qrData.user?.id)].slice(0, 10);
      
      await AsyncStorage.setItem('jiranipay_recent_qr_codes', JSON.stringify(updated));
    } catch (error) {
      console.error('Save to recent QR codes error:', error);
    }
  }
}

export default new QRCodeService();
