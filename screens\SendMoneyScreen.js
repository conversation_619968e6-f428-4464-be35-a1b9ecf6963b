import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  RefreshControl,
  FlatList,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import { useLanguage } from '../contexts/LanguageContext';
import sendMoneyService from '../services/sendMoneyService';
import authService from '../services/authService';
import ContactPicker from '../components/ContactPicker';
import contactService from '../services/contactService';
import { getDisplayFormat } from '../utils/phoneValidation';

/**
 * SendMoneyScreen - Main screen for P2P transfers
 * Features contact selection, search, recent transfers, and favorites
 */
const SendMoneyScreen = ({ navigation }) => {
  // Use theme, currency, and language contexts
  const { theme } = useTheme();
  const { convertAndFormat, getCurrencySymbol } = useCurrencyContext();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [contacts, setContacts] = useState([]);
  const [recentContacts, setRecentContacts] = useState([]);
  const [favoriteContacts, setFavoriteContacts] = useState([]);
  const [filteredContacts, setFilteredContacts] = useState([]);
  const [selectedTab, setSelectedTab] = useState('all'); // all, recent, favorites
  const [user, setUser] = useState(null);
  const [showContactPicker, setShowContactPicker] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    filterContacts();
  }, [searchQuery, contacts, selectedTab]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = await authService.getCurrentUser();
      if (currentUser.success) {
        setUser(currentUser.user);
        
        // Load contacts and data
        await Promise.all([
          loadContacts(),
          loadRecentContacts(currentUser.user.id),
          loadFavoriteContacts(currentUser.user.id),
        ]);
      }
    } catch (error) {
      console.error('Load initial data error:', error);
      Alert.alert('Error', 'Failed to load contacts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadContacts = async () => {
    try {
      console.log('📱 SendMoneyScreen: Loading contacts via contactService...');
      const result = await contactService.initialize();

      if (result.success) {
        const allContacts = contactService.getAllContacts();
        setContacts(allContacts);
        console.log(`✅ Loaded ${allContacts.length} contacts`);
      } else {
        console.log('❌ Failed to load contacts:', result.error);
        setContacts([]);

        if (result.error?.includes('permission')) {
          Alert.alert(
            'Contacts Permission Required',
            'To send money to your contacts, please allow access to your contacts in Settings. You can still enter phone numbers manually.',
            [
              { text: 'OK', style: 'default' },
              { text: 'Manual Entry', onPress: handleManualEntry }
            ]
          );
        }
      }
    } catch (error) {
      console.error('❌ Load contacts error:', error);
      setContacts([]); // Set empty array for graceful handling
    }
  };

  const loadRecentContacts = async (userId) => {
    try {
      const result = await sendMoneyService.getRecentContacts(userId);
      if (result.success) {
        setRecentContacts(result.contacts);
      }
    } catch (error) {
      console.error('Load recent contacts error:', error);
    }
  };

  const loadFavoriteContacts = async (userId) => {
    try {
      const result = await sendMoneyService.getFavoriteContacts(userId);
      if (result.success) {
        setFavoriteContacts(result.contacts);
      }
    } catch (error) {
      console.error('Load favorite contacts error:', error);
    }
  };

  const filterContacts = () => {
    let contactsToFilter = [];
    
    switch (selectedTab) {
      case 'recent':
        contactsToFilter = recentContacts;
        break;
      case 'favorites':
        contactsToFilter = favoriteContacts;
        break;
      default:
        contactsToFilter = contacts;
    }

    if (!searchQuery.trim()) {
      setFilteredContacts(contactsToFilter);
      return;
    }

    const filtered = contactsToFilter.filter(contact => {
      const nameMatch = contact.name.toLowerCase().includes(searchQuery.toLowerCase());
      const phoneMatch = contact.phoneNumbers?.some(phone => 
        phone.number.includes(searchQuery)
      );
      return nameMatch || phoneMatch;
    });

    setFilteredContacts(filtered);
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  }, []);

  const handleContactSelect = (contact, phoneNumber = null) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Handle both old and new contact formats
    let selectedPhoneNumber, selectedProvider;

    if (phoneNumber) {
      // Specific phone number selected
      selectedPhoneNumber = phoneNumber.number || phoneNumber;
      selectedProvider = phoneNumber.provider;
    } else if (contact.phoneNumber) {
      // New format (like Request Money)
      selectedPhoneNumber = contact.phoneNumber;
      selectedProvider = contact.provider;
    } else if (contact.phoneNumbers && contact.phoneNumbers.length > 0) {
      // Old format fallback
      const primaryPhone = contact.phoneNumbers[0];
      selectedPhoneNumber = primaryPhone.number;
      selectedProvider = primaryPhone.provider;
    } else {
      Alert.alert('Error', 'No phone number found for this contact');
      return;
    }

    const recipient = {
      name: contact.name,
      phoneNumber: selectedPhoneNumber,
      provider: selectedProvider,
      image: contact.image,
    };

    navigation.navigate('TransferAmount', { recipient });
  };

  const handleManualEntry = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('ManualRecipient');
  };

  const handleQRScanner = () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      navigation.navigate('QRScanner', { type: 'payment' });
    } catch (error) {
      console.error('QR scanner navigation error:', error);
      Alert.alert('QR Scanner', 'Failed to open QR scanner. Please try again.');
    }
  };

  const handleContactPicker = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowContactPicker(true);
  };

  const handleContactPickerSelect = (contact) => {
    console.log('📱 SendMoneyScreen: Contact selected from picker:', contact.name);

    // Convert contact format for navigation
    const recipient = {
      id: contact.id,
      name: contact.name,
      phoneNumber: contact.phoneNumber,
      networkProvider: contact.networkProvider,
      isFavorite: contact.isFavorite
    };

    navigation.navigate('TransferAmount', { recipient });
  };

  const handleContactPickerClose = () => {
    setShowContactPicker(false);
  };

  const handleQRScan = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('QRScanner', { type: 'send_money' });
  };

  const toggleFavorite = async (contact) => {
    if (!user) return;
    
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const isFavorite = favoriteContacts.some(fav => 
        fav.phoneNumbers?.[0]?.number === contact.phoneNumbers?.[0]?.number
      );
      
      const result = await sendMoneyService.toggleFavoriteContact(
        user.id, 
        contact, 
        !isFavorite
      );
      
      if (result.success) {
        await loadFavoriteContacts(user.id);
      }
    } catch (error) {
      console.error('Toggle favorite error:', error);
    }
  };

  const renderContactItem = ({ item: contact }) => {
    const isFavorite = favoriteContacts.some(fav => 
      fav.phoneNumbers?.[0]?.number === contact.phoneNumbers?.[0]?.number
    );

    return (
      <TouchableOpacity
        style={styles.contactItem}
        onPress={() => handleContactSelect(contact)}
        activeOpacity={0.7}
      >
        <View style={styles.contactLeft}>
          {contact.image ? (
            <Image source={{ uri: contact.image.uri }} style={styles.contactAvatar} />
          ) : (
            <View style={[styles.contactAvatar, styles.contactAvatarPlaceholder]}>
              <Text style={styles.contactAvatarText}>{contact.firstLetter}</Text>
            </View>
          )}
          
          <View style={styles.contactInfo}>
            <Text style={styles.contactName}>{contact.name}</Text>
            {contact.phoneNumbers && contact.phoneNumbers.length > 0 && (
              <View style={styles.phoneContainer}>
                <Text style={styles.contactPhone}>
                  {contact.phoneNumbers[0].number}
                </Text>
                {contact.phoneNumbers[0].provider && (
                  <View style={[
                    styles.providerBadge,
                    { backgroundColor: contact.phoneNumbers[0].provider.color + '20' }
                  ]}>
                    <Text style={[
                      styles.providerText,
                      { color: contact.phoneNumbers[0].provider.color }
                    ]}>
                      {contact.phoneNumbers[0].provider.name}
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>

        <View style={styles.contactActions}>
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => toggleFavorite(contact)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isFavorite ? 'heart' : 'heart-outline'}
              size={20}
              color={isFavorite ? theme.colors.error : theme.colors.textSecondary}
            />
          </TouchableOpacity>
          
          <Ionicons
            name="chevron-forward"
            size={20}
            color={theme.colors.textSecondary}
          />
        </View>
      </TouchableOpacity>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={handleManualEntry}
        activeOpacity={0.7}
      >
        <View style={styles.quickActionIcon}>
          <Ionicons name="keypad" size={24} color={theme.colors.primary} />
        </View>
        <Text style={styles.quickActionText}>Enter Number</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={handleQRScan}
        activeOpacity={0.7}
      >
        <View style={styles.quickActionIcon}>
          <Ionicons name="qr-code" size={24} color={theme.colors.primary} />
        </View>
        <Text style={styles.quickActionText}>Scan QR</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('RequestMoney')}
        activeOpacity={0.7}
      >
        <View style={styles.quickActionIcon}>
          <Ionicons name="arrow-down" size={24} color={theme.colors.primary} />
        </View>
        <Text style={styles.quickActionText}>Request</Text>
      </TouchableOpacity>
    </View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { id: 'all', label: 'All Contacts', icon: 'people' },
        { id: 'recent', label: 'Recent', icon: 'time' },
        { id: 'favorites', label: 'Favorites', icon: 'heart' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.id}
          style={[styles.tabButton, selectedTab === tab.id && styles.tabButtonActive]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setSelectedTab(tab.id);
          }}
          activeOpacity={0.7}
        >
          <Ionicons
            name={tab.icon}
            size={20}
            color={selectedTab === tab.id ? theme.colors.primary : theme.colors.textSecondary}
          />
          <Text style={[
            styles.tabButtonText,
            selectedTab === tab.id && styles.tabButtonTextActive
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading contacts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('wallet.sendMoney')}</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerActionButton}
            onPress={handleQRScanner}
            activeOpacity={0.7}
            disabled={loading}
          >
            <Ionicons name="qr-code" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerActionButton}
            onPress={handleManualEntry}
            activeOpacity={0.7}
          >
            <Ionicons name="keypad" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search contacts or enter phone number"
            placeholderTextColor={theme.colors.placeholder}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => setSearchQuery('')}
              activeOpacity={0.7}
            >
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Quick Actions */}
      {renderQuickActions()}

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Contacts List */}
      <FlatList
        data={filteredContacts}
        renderItem={renderContactItem}
        keyExtractor={(item, index) => `${item.id || index}`}
        style={styles.contactsList}
        contentContainerStyle={styles.contactsListContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyTitle}>
              {selectedTab === 'recent' ? 'No Recent Transfers' :
               selectedTab === 'favorites' ? 'No Favorite Contacts' :
               searchQuery ? 'No Contacts Found' : 'No Contacts Available'}
            </Text>
            <Text style={styles.emptySubtitle}>
              {selectedTab === 'recent' ? 'Your recent transfers will appear here' :
               selectedTab === 'favorites' ? 'Add contacts to favorites for quick access' :
               searchQuery ? 'Try a different search term' : 'Allow contacts access to see your contacts'}
            </Text>
          </View>
        )}
      />

      {/* Contact Picker Modal */}
      <ContactPicker
        visible={showContactPicker}
        onClose={handleContactPickerClose}
        onSelectContact={handleContactPickerSelect}
        title="Select Contact"
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerActionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: theme.colors.primary + '10',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  quickActionButton: {
    alignItems: 'center',
    gap: 8,
  },
  quickActionIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary + '10',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  tabButtonActive: {
    backgroundColor: theme.colors.primary + '10',
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  tabButtonTextActive: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  contactsList: {
    flex: 1,
  },
  contactsListContent: {
    paddingVertical: 8,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  contactLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 12,
  },
  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  contactAvatarPlaceholder: {
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  contactPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  providerBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  providerText: {
    fontSize: 10,
    fontWeight: '600',
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  favoriteButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default SendMoneyScreen;
