import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';

/**
 * Offline Storage Service for JiraniPay
 * Optimized for East African network conditions with comprehensive local caching
 */
class OfflineStorageService {
  constructor() {
    this.STORAGE_KEYS = {
      USER_PROFILE: 'user_profile',
      USER_SETTINGS: 'user_settings',
      PROFILE_PICTURE: 'profile_picture',
      BILL_PROVIDERS: 'bill_providers',
      TRANSACTION_HISTORY: 'transaction_history',
      PENDING_TRANSACTIONS: 'pending_transactions',
      APP_SETTINGS: 'app_settings',
      CACHED_DATA: 'cached_data',
      LAST_SYNC: 'last_sync',
      OFFLINE_QUEUE: 'offline_queue'
    };
    
    this.syncListeners = [];
    this.init();
  }

  async init() {
    try {
      await this.createDirectories();
      await this.loadPendingTransactions();
      console.log('💾 OfflineStorage: Initialized successfully');
    } catch (error) {
      console.error('❌ OfflineStorage: Initialization failed:', error);
    }
  }

  async createDirectories() {
    const directories = [
      `${FileSystem.documentDirectory}profile_pictures/`,
      `${FileSystem.documentDirectory}cached_images/`,
      `${FileSystem.documentDirectory}offline_data/`
    ];

    for (const dir of directories) {
      const dirInfo = await FileSystem.getInfoAsync(dir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dir, { intermediates: true });
        console.log(`📁 OfflineStorage: Created directory ${dir}`);
      }
    }
  }

  // ==================== USER PROFILE MANAGEMENT ====================

  async saveUserProfile(profileData) {
    try {
      const profileWithTimestamp = {
        ...profileData,
        lastUpdated: Date.now(),
        offlineAvailable: true
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.USER_PROFILE, 
        JSON.stringify(profileWithTimestamp)
      );

      console.log('💾 OfflineStorage: User profile saved for offline access');
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to save user profile:', error);
      return false;
    }
  }

  async getUserProfile() {
    try {
      const profileData = await AsyncStorage.getItem(this.STORAGE_KEYS.USER_PROFILE);
      if (profileData) {
        const profile = JSON.parse(profileData);
        console.log('💾 OfflineStorage: User profile loaded from offline storage');
        return profile;
      }
      return null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load user profile:', error);
      return null;
    }
  }

  async saveUserSettings(settings) {
    try {
      const settingsWithTimestamp = {
        ...settings,
        lastUpdated: Date.now()
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.USER_SETTINGS,
        JSON.stringify(settingsWithTimestamp)
      );

      console.log('💾 OfflineStorage: User settings saved');
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to save user settings:', error);
      return false;
    }
  }

  async getUserSettings() {
    try {
      const settingsData = await AsyncStorage.getItem(this.STORAGE_KEYS.USER_SETTINGS);
      return settingsData ? JSON.parse(settingsData) : null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load user settings:', error);
      return null;
    }
  }

  // ==================== PROFILE PICTURE MANAGEMENT ====================

  async saveProfilePicture(imageUri, userId) {
    try {
      const fileName = `profile_${userId}_${Date.now()}.jpg`;
      const localPath = `${FileSystem.documentDirectory}profile_pictures/${fileName}`;

      // Copy image to local storage
      await FileSystem.copyAsync({
        from: imageUri,
        to: localPath
      });

      // Save reference in AsyncStorage
      await AsyncStorage.setItem(this.STORAGE_KEYS.PROFILE_PICTURE, JSON.stringify({
        localPath,
        originalUri: imageUri,
        savedAt: Date.now(),
        userId
      }));

      console.log('💾 OfflineStorage: Profile picture saved locally');
      return localPath;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to save profile picture:', error);
      return null;
    }
  }

  async getProfilePicture() {
    try {
      const pictureData = await AsyncStorage.getItem(this.STORAGE_KEYS.PROFILE_PICTURE);
      if (pictureData) {
        const data = JSON.parse(pictureData);
        
        // Check if file still exists
        const fileInfo = await FileSystem.getInfoAsync(data.localPath);
        if (fileInfo.exists) {
          return data.localPath;
        } else {
          // Clean up broken reference
          await AsyncStorage.removeItem(this.STORAGE_KEYS.PROFILE_PICTURE);
        }
      }
      return null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load profile picture:', error);
      return null;
    }
  }

  // ==================== BILL PAYMENT DATA CACHING ====================

  async saveBillProviders(providers) {
    try {
      const providersWithTimestamp = {
        data: providers,
        lastUpdated: Date.now(),
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.BILL_PROVIDERS,
        JSON.stringify(providersWithTimestamp)
      );

      console.log('💾 OfflineStorage: Bill providers cached for offline access');
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to cache bill providers:', error);
      return false;
    }
  }

  async getBillProviders() {
    try {
      const providersData = await AsyncStorage.getItem(this.STORAGE_KEYS.BILL_PROVIDERS);
      if (providersData) {
        const cached = JSON.parse(providersData);
        
        // Check if cache is still valid
        if (Date.now() < cached.expiresAt) {
          console.log('💾 OfflineStorage: Bill providers loaded from cache');
          return {
            data: cached.data,
            fromCache: true,
            lastUpdated: cached.lastUpdated
          };
        } else {
          console.log('⚠️ OfflineStorage: Bill providers cache expired');
          // Return expired data with warning for offline scenarios
          return {
            data: cached.data,
            fromCache: true,
            expired: true,
            lastUpdated: cached.lastUpdated
          };
        }
      }
      return null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load bill providers:', error);
      return null;
    }
  }

  // ==================== TRANSACTION MANAGEMENT ====================

  async saveTransactionHistory(transactions) {
    try {
      const historyWithTimestamp = {
        data: transactions,
        lastUpdated: Date.now(),
        expiresAt: Date.now() + (6 * 60 * 60 * 1000) // 6 hours
      };

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.TRANSACTION_HISTORY,
        JSON.stringify(historyWithTimestamp)
      );

      console.log('💾 OfflineStorage: Transaction history cached');
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to cache transaction history:', error);
      return false;
    }
  }

  async getTransactionHistory() {
    try {
      const historyData = await AsyncStorage.getItem(this.STORAGE_KEYS.TRANSACTION_HISTORY);
      if (historyData) {
        const cached = JSON.parse(historyData);
        return {
          data: cached.data,
          fromCache: true,
          expired: Date.now() >= cached.expiresAt,
          lastUpdated: cached.lastUpdated
        };
      }
      return null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load transaction history:', error);
      return null;
    }
  }

  // ==================== PENDING TRANSACTIONS QUEUE ====================

  async queuePendingTransaction(transaction) {
    try {
      const pendingTransactions = await this.getPendingTransactions();
      const newTransaction = {
        ...transaction,
        id: `pending_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        queuedAt: Date.now(),
        status: 'pending',
        retryCount: 0
      };

      pendingTransactions.push(newTransaction);
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.PENDING_TRANSACTIONS,
        JSON.stringify(pendingTransactions)
      );

      console.log('📥 OfflineStorage: Transaction queued for when online');
      return newTransaction.id;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to queue transaction:', error);
      return null;
    }
  }

  async getPendingTransactions() {
    try {
      const pendingData = await AsyncStorage.getItem(this.STORAGE_KEYS.PENDING_TRANSACTIONS);
      return pendingData ? JSON.parse(pendingData) : [];
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load pending transactions:', error);
      return [];
    }
  }

  async removePendingTransaction(transactionId) {
    try {
      const pendingTransactions = await this.getPendingTransactions();
      const updatedTransactions = pendingTransactions.filter(t => t.id !== transactionId);
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.PENDING_TRANSACTIONS,
        JSON.stringify(updatedTransactions)
      );

      console.log('✅ OfflineStorage: Pending transaction removed');
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to remove pending transaction:', error);
      return false;
    }
  }

  async loadPendingTransactions() {
    const pending = await this.getPendingTransactions();
    console.log(`📥 OfflineStorage: Loaded ${pending.length} pending transactions`);
    return pending;
  }

  // ==================== GENERAL CACHING ====================

  async cacheData(key, data, expirationHours = 24) {
    try {
      const cachedItem = {
        data,
        cachedAt: Date.now(),
        expiresAt: Date.now() + (expirationHours * 60 * 60 * 1000)
      };

      const allCachedData = await this.getAllCachedData();
      allCachedData[key] = cachedItem;

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.CACHED_DATA,
        JSON.stringify(allCachedData)
      );

      console.log(`💾 OfflineStorage: Data cached with key '${key}'`);
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to cache data:', error);
      return false;
    }
  }

  async getCachedData(key) {
    try {
      const allCachedData = await this.getAllCachedData();
      const cachedItem = allCachedData[key];

      if (cachedItem) {
        const isExpired = Date.now() >= cachedItem.expiresAt;
        return {
          data: cachedItem.data,
          fromCache: true,
          expired: isExpired,
          cachedAt: cachedItem.cachedAt
        };
      }
      return null;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to get cached data:', error);
      return null;
    }
  }

  async getAllCachedData() {
    try {
      const cachedData = await AsyncStorage.getItem(this.STORAGE_KEYS.CACHED_DATA);
      return cachedData ? JSON.parse(cachedData) : {};
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to load cached data:', error);
      return {};
    }
  }

  // ==================== SYNC MANAGEMENT ====================

  async updateLastSyncTime(syncType = 'general') {
    try {
      const syncData = await this.getLastSyncTimes();
      syncData[syncType] = Date.now();

      await AsyncStorage.setItem(
        this.STORAGE_KEYS.LAST_SYNC,
        JSON.stringify(syncData)
      );

      console.log(`🔄 OfflineStorage: Last sync time updated for ${syncType}`);
      return true;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to update sync time:', error);
      return false;
    }
  }

  async getLastSyncTimes() {
    try {
      const syncData = await AsyncStorage.getItem(this.STORAGE_KEYS.LAST_SYNC);
      return syncData ? JSON.parse(syncData) : {};
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to get sync times:', error);
      return {};
    }
  }

  // ==================== CLEANUP AND MAINTENANCE ====================

  async clearExpiredCache() {
    try {
      const allCachedData = await this.getAllCachedData();
      const now = Date.now();
      let cleanedCount = 0;

      Object.keys(allCachedData).forEach(key => {
        if (now >= allCachedData[key].expiresAt) {
          delete allCachedData[key];
          cleanedCount++;
        }
      });

      if (cleanedCount > 0) {
        await AsyncStorage.setItem(
          this.STORAGE_KEYS.CACHED_DATA,
          JSON.stringify(allCachedData)
        );
        console.log(`🧹 OfflineStorage: Cleaned ${cleanedCount} expired cache items`);
      }

      return cleanedCount;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to clear expired cache:', error);
      return 0;
    }
  }

  async getStorageInfo() {
    try {
      const keys = Object.values(this.STORAGE_KEYS);
      const storageInfo = {};

      for (const key of keys) {
        const data = await AsyncStorage.getItem(key);
        storageInfo[key] = {
          exists: !!data,
          size: data ? data.length : 0,
          lastModified: data ? 'unknown' : null
        };
      }

      return storageInfo;
    } catch (error) {
      console.error('❌ OfflineStorage: Failed to get storage info:', error);
      return {};
    }
  }

  // ==================== UTILITY METHODS ====================

  async isDataAvailableOffline(dataType) {
    switch (dataType) {
      case 'profile':
        return !!(await this.getUserProfile());
      case 'settings':
        return !!(await this.getUserSettings());
      case 'billProviders':
        return !!(await this.getBillProviders());
      case 'transactions':
        return !!(await this.getTransactionHistory());
      default:
        return false;
    }
  }

  addSyncListener(listener) {
    this.syncListeners.push(listener);
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }

  notifySyncListeners(syncEvent) {
    this.syncListeners.forEach(listener => {
      try {
        listener(syncEvent);
      } catch (error) {
        console.error('❌ OfflineStorage: Sync listener error:', error);
      }
    });
  }
}

export default new OfflineStorageService();
