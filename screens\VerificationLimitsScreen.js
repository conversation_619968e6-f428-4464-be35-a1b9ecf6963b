import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';

const VerificationLimitsScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [currentLevel, setCurrentLevel] = useState(null);

  const { theme } = useTheme();
  const styles = createStyles(theme);

  useEffect(() => {
    loadVerificationData();
  }, []);

  const loadVerificationData = async () => {
    try {
      setLoading(true);
      
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        Alert.alert('Error', 'Please log in to continue');
        navigation.goBack();
        return;
      }
      
      setUser(currentUser);

      // Get current verification level
      const levelResult = await profileManagementService.getKYCVerificationLevel(currentUser.id);
      if (levelResult.success) {
        setCurrentLevel(levelResult.data.verification_level);
      }

    } catch (error) {
      console.error('❌ Error loading verification data:', error);
      Alert.alert('Error', 'Failed to load verification data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return `UGX ${amount?.toLocaleString() || '0'}`;
  };

  const getVerificationLevels = () => {
    return [
      {
        level: 'basic',
        title: 'Basic Account',
        subtitle: 'Get started with essential features',
        icon: 'person-outline',
        color: Colors.status.warning,
        daily_limit: 100000,
        monthly_limit: 3000000,
        annual_limit: ********,
        features: [
          'Basic transfers',
          'Bill payments',
          'Airtime purchase',
          'Mobile money deposits'
        ],
        requirements: [
          'Phone number verification',
          'Basic profile information'
        ]
      },
      {
        level: 'standard',
        title: 'Standard Account',
        subtitle: 'Enhanced features and higher limits',
        icon: 'shield-outline',
        color: Colors.primary.main,
        daily_limit: 500000,
        monthly_limit: ********,
        annual_limit: *********,
        features: [
          'All Basic features',
          'International transfers',
          'Loan applications',
          'Investment products',
          'Higher transaction limits'
        ],
        requirements: [
          'National ID verification',
          'Complete profile information',
          'Phone number verification'
        ]
      },
      {
        level: 'premium',
        title: 'Premium Account',
        subtitle: 'Full access to all features',
        icon: 'diamond-outline',
        color: Colors.status.success,
        daily_limit: 2000000,
        monthly_limit: ********,
        annual_limit: *********,
        features: [
          'All Standard features',
          'Priority customer support',
          'Advanced investment products',
          'Business account features',
          'Maximum transaction limits'
        ],
        requirements: [
          'National ID verification',
          'Address verification (utility bill)',
          'Complete profile information',
          'Phone number verification'
        ]
      }
    ];
  };

  const renderLevelCard = (levelData) => {
    const isCurrentLevel = currentLevel === levelData.level;
    const isUpgrade = !isCurrentLevel && shouldShowUpgrade(levelData.level);

    return (
      <View 
        key={levelData.level}
        style={[
          styles.levelCard,
          isCurrentLevel && styles.currentLevelCard
        ]}
      >
        {/* Header */}
        <View style={styles.levelHeader}>
          <View style={[styles.levelIcon, { backgroundColor: levelData.color }]}>
            <Ionicons name={levelData.icon} size={24} color={Colors.neutral.white} />
          </View>
          <View style={styles.levelInfo}>
            <View style={styles.levelTitleRow}>
              <Text style={styles.levelTitle}>{levelData.title}</Text>
              {isCurrentLevel && (
                <View style={styles.currentBadge}>
                  <Text style={styles.currentBadgeText}>Current</Text>
                </View>
              )}
            </View>
            <Text style={styles.levelSubtitle}>{levelData.subtitle}</Text>
          </View>
        </View>

        {/* Limits */}
        <View style={styles.limitsSection}>
          <Text style={styles.sectionTitle}>Transaction Limits</Text>
          <View style={styles.limitRow}>
            <Text style={styles.limitLabel}>Daily Limit:</Text>
            <Text style={styles.limitValue}>{formatCurrency(levelData.daily_limit)}</Text>
          </View>
          <View style={styles.limitRow}>
            <Text style={styles.limitLabel}>Monthly Limit:</Text>
            <Text style={styles.limitValue}>{formatCurrency(levelData.monthly_limit)}</Text>
          </View>
          <View style={styles.limitRow}>
            <Text style={styles.limitLabel}>Annual Limit:</Text>
            <Text style={styles.limitValue}>{formatCurrency(levelData.annual_limit)}</Text>
          </View>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Features</Text>
          {levelData.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={levelData.color} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        {/* Requirements */}
        <View style={styles.requirementsSection}>
          <Text style={styles.sectionTitle}>Requirements</Text>
          {levelData.requirements.map((requirement, index) => (
            <View key={index} style={styles.requirementItem}>
              <Ionicons name="document-outline" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.requirementText}>{requirement}</Text>
            </View>
          ))}
        </View>

        {/* Action Button */}
        {isUpgrade && (
          <TouchableOpacity
            style={[styles.upgradeButton, { backgroundColor: levelData.color }]}
            onPress={() => navigation.navigate('AccountVerification')}
            activeOpacity={0.7}
          >
            <Text style={styles.upgradeButtonText}>Upgrade to {levelData.title}</Text>
            <Ionicons name="arrow-forward" size={16} color={Colors.neutral.white} />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const shouldShowUpgrade = (level) => {
    const levelOrder = ['basic', 'standard', 'premium'];
    const currentIndex = levelOrder.indexOf(currentLevel || 'basic');
    const targetIndex = levelOrder.indexOf(level);
    return targetIndex > currentIndex;
  };

  const renderComparisonTable = () => {
    const levels = getVerificationLevels();
    
    return (
      <View style={styles.comparisonSection}>
        <Text style={styles.sectionTitle}>Quick Comparison</Text>
        <View style={styles.comparisonTable}>
          {/* Header */}
          <View style={styles.comparisonHeader}>
            <Text style={styles.comparisonHeaderText}>Feature</Text>
            <Text style={styles.comparisonHeaderText}>Basic</Text>
            <Text style={styles.comparisonHeaderText}>Standard</Text>
            <Text style={styles.comparisonHeaderText}>Premium</Text>
          </View>

          {/* Daily Limit Row */}
          <View style={styles.comparisonRow}>
            <Text style={styles.comparisonLabel}>Daily Limit</Text>
            <Text style={styles.comparisonValue}>100K</Text>
            <Text style={styles.comparisonValue}>500K</Text>
            <Text style={styles.comparisonValue}>2M</Text>
          </View>

          {/* Monthly Limit Row */}
          <View style={styles.comparisonRow}>
            <Text style={styles.comparisonLabel}>Monthly Limit</Text>
            <Text style={styles.comparisonValue}>3M</Text>
            <Text style={styles.comparisonValue}>15M</Text>
            <Text style={styles.comparisonValue}>60M</Text>
          </View>

          {/* International Transfers */}
          <View style={styles.comparisonRow}>
            <Text style={styles.comparisonLabel}>International</Text>
            <Ionicons name="close" size={16} color={Colors.status.error} />
            <Ionicons name="checkmark" size={16} color={Colors.status.success} />
            <Ionicons name="checkmark" size={16} color={Colors.status.success} />
          </View>

          {/* Loans */}
          <View style={styles.comparisonRow}>
            <Text style={styles.comparisonLabel}>Loans</Text>
            <Ionicons name="close" size={16} color={Colors.status.error} />
            <Ionicons name="checkmark" size={16} color={Colors.status.success} />
            <Ionicons name="checkmark" size={16} color={Colors.status.success} />
          </View>

          {/* Priority Support */}
          <View style={styles.comparisonRow}>
            <Text style={styles.comparisonLabel}>Priority Support</Text>
            <Ionicons name="close" size={16} color={Colors.status.error} />
            <Ionicons name="close" size={16} color={Colors.status.error} />
            <Ionicons name="checkmark" size={16} color={Colors.status.success} />
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>Loading verification limits...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>Verification Limits</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Card */}
        <View style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <Ionicons name="information-circle" size={24} color={Colors.primary.main} />
            <Text style={styles.infoTitle}>About Verification Levels</Text>
          </View>
          <Text style={styles.infoDescription}>
            Higher verification levels unlock increased transaction limits and additional features. 
            Complete the verification process to upgrade your account.
          </Text>
        </View>

        {/* Comparison Table */}
        {renderComparisonTable()}

        {/* Level Cards */}
        <View style={styles.levelsSection}>
          <Text style={styles.sectionTitle}>Detailed Information</Text>
          {getVerificationLevels().map(renderLevelCard)}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  infoCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginLeft: 12,
  },
  infoDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  comparisonSection: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  comparisonTable: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  comparisonHeader: {
    flexDirection: 'row',
    backgroundColor: Colors.primary.main + '10',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  comparisonHeaderText: {
    flex: 1,
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  comparisonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  comparisonLabel: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
  },
  comparisonValue: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  levelsSection: {
    marginVertical: 16,
  },
  levelCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  currentLevelCard: {
    borderColor: Colors.primary.main,
    borderWidth: 2,
  },
  levelHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  levelIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  levelInfo: {
    flex: 1,
  },
  levelTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  levelTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: 12,
  },
  currentBadge: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  currentBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  levelSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  limitsSection: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  limitRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  limitLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  limitValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  featuresSection: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
  requirementsSection: {
    marginBottom: 20,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 8,
  },
  upgradeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginRight: 8,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default VerificationLimitsScreen;
