#!/usr/bin/env node

/**
 * Production Mode Validation Script for JiraniPay
 * 
 * This script validates that the app is completely configured for production mode
 * with no development mode indicators or debug information visible to users.
 */

console.log('🔒 JiraniPay Production Mode Validation');
console.log('======================================\n');

const fs = require('fs');
const path = require('path');

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: []
};

/**
 * Add validation result
 */
function addResult(type, category, message, details = null) {
  results[type]++;
  results.issues.push({
    type,
    category,
    message,
    details,
    timestamp: new Date().toISOString()
  });
}

/**
 * Check environment configuration
 */
function validateEnvironmentConfig() {
  console.log('🔧 Validating Environment Configuration...');
  
  try {
    // Check main environment file
    const envPath = '.env.production.local';
    if (!fs.existsSync(envPath)) {
      addResult('failed', 'environment', 'Production environment file not found', envPath);
      return;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    // Check for production environment setting
    if (envContent.includes('EXPO_PUBLIC_ENVIRONMENT=production')) {
      addResult('passed', 'environment', 'Environment correctly set to production');
    } else {
      addResult('failed', 'environment', 'EXPO_PUBLIC_ENVIRONMENT not set to production');
    }
    
    // Check for development mode indicators
    if (envContent.includes('development') && !envContent.includes('# development')) {
      addResult('warnings', 'environment', 'Development references found in production config');
    }
    
  } catch (error) {
    addResult('failed', 'environment', 'Error validating environment config', error.message);
  }
}

/**
 * Check for development mode UI components
 */
function validateUIComponents() {
  console.log('🎨 Validating UI Components...');
  
  const componentsToCheck = [
    'screens/LoginScreen.js',
    'screens/NotificationScreen.js',
    'screens/TrustedDevicesScreen.js'
  ];
  
  componentsToCheck.forEach(componentPath => {
    try {
      if (!fs.existsSync(componentPath)) {
        addResult('warnings', 'ui', `Component not found: ${componentPath}`);
        return;
      }
      
      const content = fs.readFileSync(componentPath, 'utf8');
      
      // Check for development mode imports
      if (content.includes('DevelopmentBanner')) {
        addResult('failed', 'ui', `Development banner found in ${componentPath}`);
      }
      
      if (content.includes('ProductionModeToggle')) {
        addResult('failed', 'ui', `Production mode toggle found in ${componentPath}`);
      }
      
      // Check for development mode messages
      if (content.includes('Development Mode') || content.includes('development mode')) {
        addResult('failed', 'ui', `Development mode text found in ${componentPath}`);
      }
      
      // Check for mock data indicators
      if (content.includes('mock') && content.includes('development')) {
        addResult('warnings', 'ui', `Mock data references found in ${componentPath}`);
      }
      
    } catch (error) {
      addResult('failed', 'ui', `Error checking ${componentPath}`, error.message);
    }
  });
  
  addResult('passed', 'ui', 'UI components validation completed');
}

/**
 * Check services for development mode code
 */
function validateServices() {
  console.log('⚙️ Validating Services...');
  
  const servicesToCheck = [
    'services/authService.js',
    'services/productionDataService.js',
    'services/analyticsService.js',
    'services/sendMoneyService.js'
  ];
  
  servicesToCheck.forEach(servicePath => {
    try {
      if (!fs.existsSync(servicePath)) {
        addResult('warnings', 'services', `Service not found: ${servicePath}`);
        return;
      }
      
      const content = fs.readFileSync(servicePath, 'utf8');
      
      // Check for development mode conditions
      const devModePatterns = [
        /Development Mode:/g,
        /development mode:/g,
        /__DEV__/g,
        /process\.env\.NODE_ENV === 'development'/g
      ];
      
      devModePatterns.forEach(pattern => {
        const matches = content.match(pattern);
        if (matches && matches.length > 0) {
          addResult('warnings', 'services', `Development mode code found in ${servicePath}`, `${matches.length} occurrences`);
        }
      });
      
      // Check for mock data generation
      if (content.includes('generateMockTransactions') || content.includes('createMockTransactions')) {
        addResult('warnings', 'services', `Mock data generation found in ${servicePath}`);
      }
      
    } catch (error) {
      addResult('failed', 'services', `Error checking ${servicePath}`, error.message);
    }
  });
  
  addResult('passed', 'services', 'Services validation completed');
}

/**
 * Check configuration files
 */
function validateConfigFiles() {
  console.log('📋 Validating Configuration Files...');
  
  const configFiles = [
    'config/environment.js',
    'config/secureEnvironment.js'
  ];
  
  configFiles.forEach(configPath => {
    try {
      if (!fs.existsSync(configPath)) {
        addResult('warnings', 'config', `Config file not found: ${configPath}`);
        return;
      }
      
      const content = fs.readFileSync(configPath, 'utf8');
      
      // Check for production mode detection
      if (content.includes('isProductionMode') || content.includes('PRODUCTION_MODE')) {
        addResult('passed', 'config', `Production mode detection found in ${configPath}`);
      }
      
      // Check for development logging
      if (content.includes('console.log') && content.includes('development')) {
        addResult('warnings', 'config', `Development logging found in ${configPath}`);
      }
      
    } catch (error) {
      addResult('failed', 'config', `Error checking ${configPath}`, error.message);
    }
  });
}

/**
 * Check backend configuration
 */
function validateBackendConfig() {
  console.log('🖥️ Validating Backend Configuration...');
  
  try {
    const backendEnvPath = 'backend/.env.production.local';
    if (!fs.existsSync(backendEnvPath)) {
      addResult('failed', 'backend', 'Backend production environment file not found');
      return;
    }
    
    const content = fs.readFileSync(backendEnvPath, 'utf8');
    
    // Check for production settings
    if (content.includes('NODE_ENV=production')) {
      addResult('passed', 'backend', 'Backend NODE_ENV set to production');
    } else {
      addResult('failed', 'backend', 'Backend NODE_ENV not set to production');
    }
    
    // Check for CORS wildcard (should be removed)
    if (content.includes('CORS_ORIGIN=*')) {
      addResult('failed', 'backend', 'Dangerous CORS wildcard found in backend config');
    } else {
      addResult('passed', 'backend', 'No CORS wildcard found');
    }
    
  } catch (error) {
    addResult('failed', 'backend', 'Error validating backend config', error.message);
  }
}

/**
 * Generate validation report
 */
function generateReport() {
  console.log('\n📊 PRODUCTION MODE VALIDATION REPORT');
  console.log('=====================================');
  
  console.log(`\n✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️ Warnings: ${results.warnings}`);
  
  if (results.issues.length > 0) {
    console.log('\n📋 DETAILED RESULTS:');
    console.log('--------------------');
    
    results.issues.forEach((issue, index) => {
      const icon = issue.type === 'passed' ? '✅' : issue.type === 'failed' ? '❌' : '⚠️';
      console.log(`${index + 1}. ${icon} [${issue.category.toUpperCase()}] ${issue.message}`);
      if (issue.details) {
        console.log(`   Details: ${issue.details}`);
      }
    });
  }
  
  // Overall assessment
  console.log('\n🎯 OVERALL ASSESSMENT:');
  console.log('----------------------');
  
  if (results.failed === 0) {
    console.log('🎉 PRODUCTION READY! No critical issues found.');
    if (results.warnings > 0) {
      console.log(`⚠️ ${results.warnings} warnings found - review recommended but not blocking.`);
    }
  } else {
    console.log(`❌ NOT PRODUCTION READY! ${results.failed} critical issues must be fixed.`);
  }
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('-------------------');
  console.log('1. Remove all development mode UI components');
  console.log('2. Eliminate development mode console logs');
  console.log('3. Ensure no mock data is visible to users');
  console.log('4. Verify environment variables are set correctly');
  console.log('5. Test app thoroughly in production mode');
  
  return results.failed === 0;
}

// Run all validations
async function runValidation() {
  try {
    validateEnvironmentConfig();
    validateUIComponents();
    validateServices();
    validateConfigFiles();
    validateBackendConfig();
    
    const isProductionReady = generateReport();
    
    // Exit with appropriate code
    process.exit(isProductionReady ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Validation script error:', error);
    process.exit(1);
  }
}

// Run the validation
runValidation();
