#!/usr/bin/env node

/**
 * Production Mode Startup Script for JiraniPay
 * 
 * This script properly configures the environment for production mode
 * and starts the Expo app with real Supabase configuration and database operations.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting JiraniPay in PRODUCTION mode...\n');

// Simple environment file parser
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ Environment file not found: ${filePath}`);
    return 0;
  }
  
  console.log(`📁 Loading environment file: ${filePath}`);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let loadedVars = 0;
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
        loadedVars++;
      }
    }
  }
  return loadedVars;
}

// Set production environment variables
process.env.EXPO_PUBLIC_ENVIRONMENT = 'production';
process.env.NODE_ENV = 'production';

// Load production environment file
const prodEnvFile = path.join(__dirname, '..', '.env.production.local');
const loadedVars = loadEnvFile(prodEnvFile);

console.log(`✅ Loaded ${loadedVars} environment variables\n`);

// Display environment configuration
console.log('🔧 Environment Configuration:');
console.log(`- EXPO_PUBLIC_ENVIRONMENT: ${process.env.EXPO_PUBLIC_ENVIRONMENT}`);
console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`- EXPO_PUBLIC_SUPABASE_URL: ${process.env.EXPO_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET'}`);
console.log(`- EXPO_PUBLIC_SUPABASE_ANON_KEY: ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET'}`);

console.log('\n✅ Environment configured for PRODUCTION!\n');

console.log('🚀 STARTING EXPO IN PRODUCTION MODE:');
console.log('====================================');

// Start Expo
const expoProcess = spawn('npx', ['expo', 'start'], {
  stdio: 'inherit',
  shell: true,
  env: process.env
});

expoProcess.on('close', (code) => {
  console.log(`\n📋 Expo process exited with code ${code}`);
  process.exit(code);
});

expoProcess.on('error', (error) => {
  console.error('❌ Error starting Expo:', error);
  process.exit(1);
});
