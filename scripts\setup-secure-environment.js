#!/usr/bin/env node

/**
 * Secure Environment Setup Script for JiraniPay
 * 
 * This script helps set up secure environment configuration by:
 * 1. Validating current configuration
 * 2. Checking for security issues
 * 3. Guiding through proper setup
 * 4. Creating necessary environment files
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function log(message, color = 'white') {
  console.log(colorize(message, color));
}

function logHeader(message) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(message, 'cyan'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

function logSuccess(message) {
  console.log(colorize('✅ ' + message, 'green'));
}

function logWarning(message) {
  console.log(colorize('⚠️  ' + message, 'yellow'));
}

function logError(message) {
  console.log(colorize('❌ ' + message, 'red'));
}

function logInfo(message) {
  console.log(colorize('ℹ️  ' + message, 'blue'));
}

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Read file content
function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    return null;
  }
}

// Check for hardcoded credentials
function checkForHardcodedCredentials() {
  logHeader('🔍 CHECKING FOR HARDCODED CREDENTIALS');
  
  const filesToCheck = [
    'config/environment.js',
    'services/supabaseClient.js',
    'backend/src/routes/config.js',
    '.env.development',
    'backend/.env.development'
  ];
  
  let foundIssues = false;
  
  filesToCheck.forEach(file => {
    const content = readFile(file);
    if (content) {
      // Skip security scripts themselves (they contain patterns for detection)
      if (file.includes('setup-secure-environment.js') || file.includes('validate-security.js')) {
        return;
      }

      // Check for hardcoded Supabase URLs (pattern-based detection)
      const supabaseUrlPattern = /https:\/\/[a-z0-9]{20}\.supabase\.co/g;
      if (supabaseUrlPattern.test(content)) {
        logError(`Found hardcoded Supabase URL in ${file}`);
        foundIssues = true;
      }

      // Check for hardcoded JWT tokens (pattern-based detection)
      const jwtPattern = /eyJ[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}\.[A-Za-z0-9_-]{10,}/g;
      if (jwtPattern.test(content)) {
        logError(`Found hardcoded JWT token in ${file}`);
        foundIssues = true;
      }
    }
  });
  
  if (!foundIssues) {
    logSuccess('No hardcoded credentials found');
  }
  
  return !foundIssues;
}

// Check environment file security
function checkEnvironmentFiles() {
  logHeader('📁 CHECKING ENVIRONMENT FILES');
  
  const issues = [];
  
  // Check if actual credential files exist and are gitignored
  const credentialFiles = [
    '.env',
    '.env.local',
    '.env.development.local',
    '.env.staging.local',
    '.env.production.local',
    'backend/.env',
    'backend/.env.local'
  ];
  
  credentialFiles.forEach(file => {
    if (fileExists(file)) {
      logInfo(`Found credential file: ${file}`);
    }
  });
  
  // Check if template files exist
  const templateFiles = [
    '.env.example',
    '.env.production.template',
    '.env.staging.template',
    'backend/.env.example'
  ];
  
  templateFiles.forEach(file => {
    if (fileExists(file)) {
      logSuccess(`Template file exists: ${file}`);
    } else {
      logWarning(`Template file missing: ${file}`);
      issues.push(`Missing template: ${file}`);
    }
  });
  
  return issues;
}

// Validate .gitignore
function validateGitignore() {
  logHeader('🚫 VALIDATING .GITIGNORE');
  
  const gitignoreContent = readFile('.gitignore');
  if (!gitignoreContent) {
    logError('.gitignore file not found');
    return false;
  }
  
  const requiredEntries = [
    '.env',
    '.env.local',
    '.env.development.local',
    '.env.staging.local',
    '.env.production.local',
    'backend/.env'
  ];
  
  let allEntriesFound = true;
  
  requiredEntries.forEach(entry => {
    if (gitignoreContent.includes(entry)) {
      logSuccess(`Gitignore includes: ${entry}`);
    } else {
      logError(`Gitignore missing: ${entry}`);
      allEntriesFound = false;
    }
  });
  
  return allEntriesFound;
}

// Create environment file from template
function createEnvironmentFile(templatePath, targetPath) {
  if (fileExists(templatePath)) {
    const templateContent = readFile(templatePath);
    fs.writeFileSync(targetPath, templateContent);
    logSuccess(`Created ${targetPath} from template`);
    return true;
  } else {
    logError(`Template not found: ${templatePath}`);
    return false;
  }
}

// Interactive setup
async function interactiveSetup() {
  logHeader('🛠️  INTERACTIVE ENVIRONMENT SETUP');
  
  const question = (prompt) => {
    return new Promise((resolve) => {
      rl.question(prompt, resolve);
    });
  };
  
  try {
    log('\nThis will help you set up secure environment configuration.');
    log('You will need Supabase project URLs and keys for each environment.\n');
    
    const setupDev = await question('Set up development environment? (y/n): ');
    if (setupDev.toLowerCase() === 'y') {
      const devUrl = await question('Enter development Supabase URL: ');
      const devKey = await question('Enter development Supabase anon key: ');
      
      // Create development environment file
      let envContent = `# JiraniPay Development Environment\n`;
      envContent += `NODE_ENV=development\n`;
      envContent += `EXPO_PUBLIC_ENVIRONMENT=development\n`;
      envContent += `EXPO_PUBLIC_SUPABASE_URL=${devUrl}\n`;
      envContent += `EXPO_PUBLIC_SUPABASE_ANON_KEY=${devKey}\n`;
      envContent += `EXPO_PUBLIC_API_BASE_URL=http://localhost:3001\n`;
      
      fs.writeFileSync('.env.development.local', envContent);
      logSuccess('Created .env.development.local');
    }
    
    const setupProd = await question('Set up production environment? (y/n): ');
    if (setupProd.toLowerCase() === 'y') {
      const prodUrl = await question('Enter production Supabase URL: ');
      const prodKey = await question('Enter production Supabase anon key: ');
      
      // Create production environment file
      let envContent = `# JiraniPay Production Environment\n`;
      envContent += `NODE_ENV=production\n`;
      envContent += `EXPO_PUBLIC_ENVIRONMENT=production\n`;
      envContent += `EXPO_PUBLIC_PROD_SUPABASE_URL=${prodUrl}\n`;
      envContent += `EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY=${prodKey}\n`;
      envContent += `EXPO_PUBLIC_PROD_API_URL=https://api.jiranipay.com\n`;
      
      fs.writeFileSync('.env.production.local', envContent);
      logSuccess('Created .env.production.local');
    }
    
  } catch (error) {
    logError('Setup interrupted');
  }
}

// Main function
async function main() {
  console.clear();
  logHeader('🔒 JIRANIPAY SECURE ENVIRONMENT SETUP');
  
  log('This script will help you set up secure environment configuration.');
  log('It will check for security issues and guide you through proper setup.\n');
  
  // Run security checks
  const credentialsSecure = checkForHardcodedCredentials();
  const envIssues = checkEnvironmentFiles();
  const gitignoreValid = validateGitignore();
  
  // Summary
  logHeader('📊 SECURITY SUMMARY');
  
  if (credentialsSecure && envIssues.length === 0 && gitignoreValid) {
    logSuccess('All security checks passed! ✨');
  } else {
    logWarning('Security issues found that need attention:');
    
    if (!credentialsSecure) {
      logError('- Hardcoded credentials detected');
    }
    
    if (envIssues.length > 0) {
      logError('- Environment file issues');
    }
    
    if (!gitignoreValid) {
      logError('- .gitignore configuration issues');
    }
  }
  
  // Offer interactive setup
  const runSetup = await new Promise((resolve) => {
    rl.question('\nWould you like to run interactive setup? (y/n): ', resolve);
  });
  
  if (runSetup.toLowerCase() === 'y') {
    await interactiveSetup();
  }
  
  logHeader('🎉 SETUP COMPLETE');
  log('Next steps:');
  log('1. Review the created environment files');
  log('2. Add your actual Supabase credentials');
  log('3. Test each environment');
  log('4. Never commit .env.*.local files to git');
  log('\nFor detailed instructions, see: docs/SECURE_ENVIRONMENT_SETUP.md');
  
  rl.close();
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  checkForHardcodedCredentials,
  checkEnvironmentFiles,
  validateGitignore
};
