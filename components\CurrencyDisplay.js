import React from 'react';
import { View, Text } from 'react-native';
import { useCurrencyContext } from '../contexts/CurrencyContext';

/**
 * Enhanced Currency Display Component
 * 
 * Provides flexible currency display with conversion, formatting, and styling options
 */
const CurrencyDisplay = ({
  amount,
  fromCurrency = 'UGX',
  targetCurrency = null,
  showOriginal = false,
  showConversionRate = false,
  containerStyle = {},
  amountStyle = {},
  originalStyle = {},
  rateStyle = {},
  options = {},
  ...props
}) => {
  const { 
    convert, 
    formatAmount, 
    userCurrency, 
    getExchangeRate,
    isLoading 
  } = useCurrencyContext();

  if (isLoading) {
    return (
      <View style={containerStyle} {...props}>
        <Text style={amountStyle}>Loading...</Text>
      </View>
    );
  }

  try {
    const target = targetCurrency || userCurrency;
    const convertedAmount = convert(amount, fromCurrency, target);
    const formattedAmount = formatAmount(convertedAmount, target, {
      showSymbol: true,
      showFlag: false,
      compact: false,
      ...options
    });

    const originalFormatted = formatAmount(amount, fromCurrency, options);
    const exchangeRate = getExchangeRate(fromCurrency, target);

    return (
      <View style={containerStyle} {...props}>
        <Text style={amountStyle}>
          {formattedAmount}
        </Text>
        
        {showOriginal && fromCurrency !== target && (
          <Text style={originalStyle}>
            Originally: {originalFormatted}
          </Text>
        )}
        
        {showConversionRate && fromCurrency !== target && exchangeRate && (
          <Text style={rateStyle}>
            Rate: 1 {fromCurrency} = {exchangeRate.toFixed(4)} {target}
          </Text>
        )}
      </View>
    );
  } catch (error) {
    console.error('❌ CurrencyDisplay: Error displaying currency:', error);
    return (
      <View style={containerStyle} {...props}>
        <Text style={amountStyle}>
          {fromCurrency} {parseFloat(amount).toLocaleString()}
        </Text>
      </View>
    );
  }
};

export default CurrencyDisplay;
