/**
 * User Utilities
 * Helper functions for user authentication and ID management
 */

import authService from '../services/authService';
import { supabase } from '../services/supabaseClient';

/**
 * Get current authenticated user ID
 * @returns {string|null} User ID or null if not authenticated
 */
export const getCurrentUserId = async () => {
  try {
    // First try to get from auth service
    const currentUser = authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      return currentUser.id;
    }

    // Fallback to Supabase auth
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('❌ Error getting user from Supabase:', error);
      return null;
    }

    return user?.id || null;
  } catch (error) {
    console.error('❌ Error getting current user ID:', error);
    return null;
  }
};

/**
 * Get current authenticated user
 * @returns {object|null} User object or null if not authenticated
 */
export const getCurrentUser = async () => {
  try {
    // First try to get from auth service
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      return currentUser;
    }

    // Fallback to Supabase auth
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error('❌ Error getting user from Supabase:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('❌ Error getting current user:', error);
    return null;
  }
};

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isUserAuthenticated = async () => {
  const userId = await getCurrentUserId();
  return userId !== null;
};

/**
 * Validate UUID format
 * @param {string} uuid - UUID string to validate
 * @returns {boolean} True if valid UUID format
 */
export const isValidUUID = (uuid) => {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Ensure user is authenticated before proceeding
 * @param {string} operation - Name of the operation for logging
 * @returns {string|null} User ID if authenticated, null otherwise
 */
export const requireAuthentication = async (operation = 'operation') => {
  const userId = await getCurrentUserId();
  
  if (!userId) {
    console.warn(`⚠️ ${operation} requires authentication but user is not logged in`);
    return null;
  }

  if (!isValidUUID(userId)) {
    console.error(`❌ Invalid user ID format for ${operation}:`, userId);
    return null;
  }

  return userId;
};

/**
 * Get user session info
 * @returns {object|null} Session info or null if not authenticated
 */
export const getUserSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.error('❌ Error getting session:', error);
      return null;
    }
    return session;
  } catch (error) {
    console.error('❌ Error getting user session:', error);
    return null;
  }
};

/**
 * Check if user has specific permissions
 * @param {string} permission - Permission to check
 * @returns {boolean} True if user has permission
 */
export const hasPermission = async (permission) => {
  const user = await getCurrentUser();
  if (!user) return false;

  // Add permission checking logic here based on your user roles/permissions system
  // For now, return true for authenticated users
  return true;
};

/**
 * Get user profile data
 * @returns {object|null} User profile or null if not found
 */
export const getUserProfile = async () => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) return null;

    const profile = await authService.getUserProfile(userId);
    return profile.success ? profile.data : null;
  } catch (error) {
    console.error('❌ Error getting user profile:', error);
    return null;
  }
};

/**
 * Format user display name
 * @param {object} user - User object
 * @returns {string} Formatted display name
 */
export const formatUserDisplayName = (user) => {
  if (!user) return 'Unknown User';
  
  if (user.user_metadata?.full_name) {
    return user.user_metadata.full_name;
  }
  
  if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
    return `${user.user_metadata.first_name} ${user.user_metadata.last_name}`;
  }
  
  if (user.email) {
    return user.email.split('@')[0];
  }
  
  if (user.phone) {
    return user.phone;
  }
  
  return 'User';
};

/**
 * Check if user needs to complete profile
 * @returns {boolean} True if profile is incomplete
 */
export const needsProfileCompletion = async () => {
  const profile = await getUserProfile();
  if (!profile) return true;

  // Check for required profile fields
  const requiredFields = ['first_name', 'last_name', 'phone_number'];
  return requiredFields.some(field => !profile[field]);
};

export default {
  getCurrentUserId,
  getCurrentUser,
  isUserAuthenticated,
  isValidUUID,
  requireAuthentication,
  getUserSession,
  hasPermission,
  getUserProfile,
  formatUserDisplayName,
  needsProfileCompletion
};
