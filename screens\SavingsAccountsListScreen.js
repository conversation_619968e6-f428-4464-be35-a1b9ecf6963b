/**
 * Savings Accounts List Screen
 * Screen for viewing all savings accounts with filtering and sorting
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsAccountsListScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [accounts, setAccounts] = useState([]);
  const [filteredAccounts, setFilteredAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('balance');

  const filters = [
    { key: 'all', label: 'All Accounts' },
    { key: 'general', label: 'General' },
    { key: 'goal', label: 'Goal-Based' },
    { key: 'emergency', label: 'Emergency' },
    { key: 'vacation', label: 'Vacation' },
    { key: 'education', label: 'Education' },
    { key: 'retirement', label: 'Retirement' }
  ];

  const sortOptions = [
    { key: 'balance', label: 'Balance' },
    { key: 'name', label: 'Name' },
    { key: 'created', label: 'Date Created' },
    { key: 'progress', label: 'Goal Progress' }
  ];

  useEffect(() => {
    loadAccounts();
  }, []);

  useEffect(() => {
    filterAndSortAccounts();
  }, [accounts, selectedFilter, sortBy]);

  const loadAccounts = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view accounts');
        navigation.goBack();
        return;
      }

      const result = await savingsAccountService.getUserSavingsAccounts(userId);

      if (result.success) {
        setAccounts(result.accounts);
      } else {
        Alert.alert('Error', result.error || 'Failed to load accounts');
      }

    } catch (error) {
      console.error('❌ Error loading accounts:', error);
      Alert.alert('Error', 'Failed to load accounts');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterAndSortAccounts = () => {
    let filtered = [...accounts];

    // Apply filter
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(account => account.accountType === selectedFilter);
    }

    // Apply sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'balance':
          return b.currentBalance - a.currentBalance;
        case 'name':
          return a.accountName.localeCompare(b.accountName);
        case 'created':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'progress':
          return (b.progressPercentage || 0) - (a.progressPercentage || 0);
        default:
          return 0;
      }
    });

    setFilteredAccounts(filtered);
  };

  const onRefresh = () => {
    loadAccounts(true);
  };

  const handleAccountPress = (account) => {
    navigation.navigate('SavingsAccountDetails', { accountId: account.id });
  };

  const handleCreateAccount = () => {
    navigation.navigate('SavingsAccountCreation');
  };

  const getAccountTypeIcon = (accountType) => {
    const icons = {
      general: 'wallet',
      goal: 'flag',
      emergency: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      retirement: 'time'
    };
    return icons[accountType] || 'wallet';
  };

  const getAccountTypeColor = (accountType) => {
    const colors = {
      general: '#4ECDC4',
      goal: '#45B7D1',
      emergency: '#FF6B35',
      vacation: '#96CEB4',
      education: '#FECA57',
      retirement: '#6C5CE7'
    };
    return colors[accountType] || '#4ECDC4';
  };

  const renderFilterBar = () => (
    <View style={styles.filterContainer}>
      <FlatList
        data={filters}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedFilter === item.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(item.key)}
          >
            <Text style={[
              styles.filterButtonText,
              selectedFilter === item.key && styles.filterButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderSortBar = () => (
    <View style={styles.sortContainer}>
      <Text style={styles.sortLabel}>Sort by:</Text>
      <FlatList
        data={sortOptions}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.key}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.sortButton,
              sortBy === item.key && styles.sortButtonActive
            ]}
            onPress={() => setSortBy(item.key)}
          >
            <Text style={[
              styles.sortButtonText,
              sortBy === item.key && styles.sortButtonTextActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
      />
    </View>
  );

  const renderAccountCard = ({ item: account }) => (
    <TouchableOpacity 
      style={styles.accountCard}
      onPress={() => handleAccountPress(account)}
    >
      <View style={styles.accountHeader}>
        <View style={[styles.accountIcon, { backgroundColor: getAccountTypeColor(account.accountType) }]}>
          <Ionicons name={getAccountTypeIcon(account.accountType)} size={20} color={theme.colors.white} />
        </View>
        <View style={styles.accountInfo}>
          <Text style={styles.accountName}>{account.accountName}</Text>
          <Text style={styles.accountType}>
            {account.accountType.charAt(0).toUpperCase() + account.accountType.slice(1)} Savings
          </Text>
          <Text style={styles.accountNumber}>Account: {account.accountNumber}</Text>
        </View>
        <View style={styles.accountActions}>
          <Text style={styles.accountBalance}>
            {formatCurrency(account.currentBalance, account.currency)}
          </Text>
          {account.totalInterestEarned > 0 && (
            <Text style={styles.interestEarned}>
              +{formatCurrency(account.totalInterestEarned, account.currency)} interest
            </Text>
          )}
        </View>
      </View>
      
      {account.targetAmount && (
        <View style={styles.progressContainer}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              Goal Progress: {account.progressPercentage.toFixed(1)}% of {formatCurrency(account.targetAmount, account.currency)}
            </Text>
            {account.targetDate && (
              <Text style={styles.targetDate}>Target: {formatDate(account.targetDate)}</Text>
            )}
          </View>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  width: `${Math.min(account.progressPercentage, 100)}%`,
                  backgroundColor: getAccountTypeColor(account.accountType)
                }
              ]} 
            />
          </View>
        </View>
      )}
      
      <View style={styles.accountFooter}>
        <View style={styles.accountStat}>
          <Text style={styles.statLabel}>Created</Text>
          <Text style={styles.statValue}>{formatDate(account.createdAt)}</Text>
        </View>
        {account.autoTransferEnabled && (
          <View style={styles.autoTransferBadge}>
            <Ionicons name="repeat" size={12} color={theme.colors.primary} />
            <Text style={styles.autoTransferText}>Auto Transfer</Text>
          </View>
        )}
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-horizontal" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="wallet-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>
        {selectedFilter === 'all' ? 'No Savings Accounts' : `No ${selectedFilter} Accounts`}
      </Text>
      <Text style={styles.emptyDescription}>
        {selectedFilter === 'all' 
          ? 'Create your first savings account to start building wealth'
          : `You don't have any ${selectedFilter} savings accounts yet`
        }
      </Text>
      <TouchableOpacity style={styles.createButton} onPress={handleCreateAccount}>
        <Text style={styles.createButtonText}>Create Account</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading accounts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>All Savings Accounts</Text>
        <TouchableOpacity onPress={handleCreateAccount}>
          <Ionicons name="add" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filters and Sort */}
      {renderFilterBar()}
      {renderSortBar()}

      {/* Accounts List */}
      {filteredAccounts.length > 0 ? (
        <FlatList
          data={filteredAccounts}
          renderItem={renderAccountCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.content}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderEmptyState()
      )}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  filterContainer: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  sortContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  sortLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 12,
  },
  sortButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: theme.colors.background,
    marginRight: 8,
  },
  sortButtonActive: {
    backgroundColor: theme.colors.primary + '20',
  },
  sortButtonText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  sortButtonTextActive: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  content: {
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  accountCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  accountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  accountIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  accountNumber: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  accountActions: {
    alignItems: 'flex-end',
  },
  accountBalance: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  interestEarned: {
    fontSize: 10,
    color: theme.colors.success,
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressInfo: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    color: theme.colors.text,
    marginBottom: 2,
  },
  targetDate: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  accountFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  accountStat: {
    flex: 1,
  },
  statLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  statValue: {
    fontSize: 12,
    color: theme.colors.text,
    fontWeight: '500',
  },
  autoTransferBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  autoTransferText: {
    fontSize: 10,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  moreButton: {
    padding: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SavingsAccountsListScreen;
