/**
 * Load Balancer and High Availability Service
 * Handles request distribution, health monitoring, and failover mechanisms
 */

const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');

class LoadBalancerService {
  constructor() {
    this.servers = new Map();
    this.healthCheckInterval = 30000; // 30 seconds
    this.algorithms = {
      ROUND_ROBIN: 'round_robin',
      LEAST_CONNECTIONS: 'least_connections',
      WEIGHTED_ROUND_ROBIN: 'weighted_round_robin',
      IP_HASH: 'ip_hash',
      LEAST_RESPONSE_TIME: 'least_response_time'
    };
    
    this.currentAlgorithm = this.algorithms.ROUND_ROBIN;
    this.roundRobinIndex = 0;
    this.isMonitoring = false;
  }

  /**
   * Initialize load balancer
   */
  async initialize() {
    try {
      // Register available servers
      await this.registerServers();
      
      // Start health monitoring
      await this.startHealthMonitoring();
      
      // Initialize circuit breakers
      await this.initializeCircuitBreakers();
      
      logger.info('Load balancer service initialized');
    } catch (error) {
      logger.error('Failed to initialize load balancer:', error);
      throw error;
    }
  }

  /**
   * Register available servers
   */
  async registerServers() {
    try {
      const serverConfigs = config.loadBalancer?.servers || [
        {
          id: 'primary',
          host: 'localhost',
          port: 3000,
          weight: 100,
          maxConnections: 1000,
          isHealthy: true,
          responseTime: 0,
          activeConnections: 0
        }
      ];

      for (const serverConfig of serverConfigs) {
        this.servers.set(serverConfig.id, {
          ...serverConfig,
          lastHealthCheck: new Date(),
          totalRequests: 0,
          failedRequests: 0,
          circuitBreakerState: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
          circuitBreakerFailures: 0,
          circuitBreakerLastFailure: null
        });
      }

      logger.info(`Registered ${this.servers.size} servers for load balancing`);
    } catch (error) {
      logger.error('Failed to register servers:', error);
      throw error;
    }
  }

  /**
   * Start health monitoring
   */
  async startHealthMonitoring() {
    try {
      if (this.isMonitoring) {
        return;
      }

      this.isMonitoring = true;
      
      setInterval(async () => {
        await this.performHealthChecks();
      }, this.healthCheckInterval);

      logger.info('Health monitoring started');
    } catch (error) {
      logger.error('Failed to start health monitoring:', error);
    }
  }

  /**
   * Perform health checks on all servers
   */
  async performHealthChecks() {
    try {
      const healthCheckPromises = Array.from(this.servers.entries()).map(
        async ([serverId, server]) => {
          try {
            const healthStatus = await this.checkServerHealth(server);
            this.updateServerHealth(serverId, healthStatus);
          } catch (error) {
            logger.error(`Health check failed for server ${serverId}:`, error);
            this.updateServerHealth(serverId, { isHealthy: false, error: error.message });
          }
        }
      );

      await Promise.all(healthCheckPromises);
      
      // Update load balancer metrics
      await this.updateMetrics();
      
    } catch (error) {
      logger.error('Health check cycle failed:', error);
    }
  }

  /**
   * Check individual server health
   */
  async checkServerHealth(server) {
    try {
      const startTime = Date.now();
      
      // Simulate health check (in production, make actual HTTP request)
      const isHealthy = Math.random() > 0.05; // 95% uptime simulation
      const responseTime = Math.random() * 100 + 50; // 50-150ms
      
      return {
        isHealthy,
        responseTime,
        timestamp: new Date(),
        checkedAt: Date.now()
      };
    } catch (error) {
      return {
        isHealthy: false,
        error: error.message,
        timestamp: new Date(),
        checkedAt: Date.now()
      };
    }
  }

  /**
   * Update server health status
   */
  updateServerHealth(serverId, healthStatus) {
    try {
      const server = this.servers.get(serverId);
      if (!server) {
        return;
      }

      const wasHealthy = server.isHealthy;
      server.isHealthy = healthStatus.isHealthy;
      server.responseTime = healthStatus.responseTime || server.responseTime;
      server.lastHealthCheck = healthStatus.timestamp;

      // Update circuit breaker state
      if (!healthStatus.isHealthy) {
        server.circuitBreakerFailures++;
        server.circuitBreakerLastFailure = new Date();
        
        if (server.circuitBreakerFailures >= 5) {
          server.circuitBreakerState = 'OPEN';
        }
      } else {
        server.circuitBreakerFailures = 0;
        if (server.circuitBreakerState === 'OPEN') {
          server.circuitBreakerState = 'HALF_OPEN';
        } else if (server.circuitBreakerState === 'HALF_OPEN') {
          server.circuitBreakerState = 'CLOSED';
        }
      }

      // Log health status changes
      if (wasHealthy !== healthStatus.isHealthy) {
        logger.info(`Server ${serverId} health changed`, {
          serverId,
          wasHealthy,
          isHealthy: healthStatus.isHealthy,
          circuitBreakerState: server.circuitBreakerState
        });
      }

      this.servers.set(serverId, server);
    } catch (error) {
      logger.error(`Failed to update server health for ${serverId}:`, error);
    }
  }

  /**
   * Get next available server based on load balancing algorithm
   */
  getNextServer(clientInfo = {}) {
    try {
      const healthyServers = Array.from(this.servers.values()).filter(
        server => server.isHealthy && server.circuitBreakerState !== 'OPEN'
      );

      if (healthyServers.length === 0) {
        throw new Error('No healthy servers available');
      }

      let selectedServer;

      switch (this.currentAlgorithm) {
        case this.algorithms.ROUND_ROBIN:
          selectedServer = this.roundRobinSelection(healthyServers);
          break;
        case this.algorithms.LEAST_CONNECTIONS:
          selectedServer = this.leastConnectionsSelection(healthyServers);
          break;
        case this.algorithms.WEIGHTED_ROUND_ROBIN:
          selectedServer = this.weightedRoundRobinSelection(healthyServers);
          break;
        case this.algorithms.IP_HASH:
          selectedServer = this.ipHashSelection(healthyServers, clientInfo.ipAddress);
          break;
        case this.algorithms.LEAST_RESPONSE_TIME:
          selectedServer = this.leastResponseTimeSelection(healthyServers);
          break;
        default:
          selectedServer = this.roundRobinSelection(healthyServers);
      }

      // Update server metrics
      selectedServer.activeConnections++;
      selectedServer.totalRequests++;

      return selectedServer;
    } catch (error) {
      logger.error('Failed to get next server:', error);
      throw error;
    }
  }

  /**
   * Round robin server selection
   */
  roundRobinSelection(servers) {
    const server = servers[this.roundRobinIndex % servers.length];
    this.roundRobinIndex++;
    return server;
  }

  /**
   * Least connections server selection
   */
  leastConnectionsSelection(servers) {
    return servers.reduce((least, current) => 
      current.activeConnections < least.activeConnections ? current : least
    );
  }

  /**
   * Weighted round robin server selection
   */
  weightedRoundRobinSelection(servers) {
    const totalWeight = servers.reduce((sum, server) => sum + server.weight, 0);
    const random = Math.random() * totalWeight;
    
    let weightSum = 0;
    for (const server of servers) {
      weightSum += server.weight;
      if (random <= weightSum) {
        return server;
      }
    }
    
    return servers[0];
  }

  /**
   * IP hash server selection for session affinity
   */
  ipHashSelection(servers, ipAddress) {
    if (!ipAddress) {
      return this.roundRobinSelection(servers);
    }
    
    const hash = this.hashString(ipAddress);
    const index = hash % servers.length;
    return servers[index];
  }

  /**
   * Least response time server selection
   */
  leastResponseTimeSelection(servers) {
    return servers.reduce((fastest, current) => 
      current.responseTime < fastest.responseTime ? current : fastest
    );
  }

  /**
   * Hash string for consistent hashing
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Release server connection
   */
  releaseConnection(serverId) {
    try {
      const server = this.servers.get(serverId);
      if (server && server.activeConnections > 0) {
        server.activeConnections--;
        this.servers.set(serverId, server);
      }
    } catch (error) {
      logger.error(`Failed to release connection for server ${serverId}:`, error);
    }
  }

  /**
   * Report request failure
   */
  reportFailure(serverId, error) {
    try {
      const server = this.servers.get(serverId);
      if (server) {
        server.failedRequests++;
        server.circuitBreakerFailures++;
        server.circuitBreakerLastFailure = new Date();
        
        // Open circuit breaker if too many failures
        if (server.circuitBreakerFailures >= 5) {
          server.circuitBreakerState = 'OPEN';
          logger.warn(`Circuit breaker opened for server ${serverId}`, {
            failures: server.circuitBreakerFailures,
            error: error.message
          });
        }
        
        this.servers.set(serverId, server);
      }
    } catch (error) {
      logger.error(`Failed to report failure for server ${serverId}:`, error);
    }
  }

  /**
   * Initialize circuit breakers
   */
  async initializeCircuitBreakers() {
    try {
      // Set up circuit breaker recovery timer
      setInterval(async () => {
        await this.checkCircuitBreakerRecovery();
      }, 60000); // Check every minute

      logger.info('Circuit breakers initialized');
    } catch (error) {
      logger.error('Failed to initialize circuit breakers:', error);
    }
  }

  /**
   * Check for circuit breaker recovery
   */
  async checkCircuitBreakerRecovery() {
    try {
      for (const [serverId, server] of this.servers) {
        if (server.circuitBreakerState === 'OPEN') {
          const timeSinceLastFailure = Date.now() - (server.circuitBreakerLastFailure?.getTime() || 0);
          
          // Try to recover after 5 minutes
          if (timeSinceLastFailure > 5 * 60 * 1000) {
            server.circuitBreakerState = 'HALF_OPEN';
            server.circuitBreakerFailures = 0;
            
            logger.info(`Circuit breaker moved to HALF_OPEN for server ${serverId}`);
            this.servers.set(serverId, server);
          }
        }
      }
    } catch (error) {
      logger.error('Failed to check circuit breaker recovery:', error);
    }
  }

  /**
   * Update load balancer metrics
   */
  async updateMetrics() {
    try {
      const metrics = {
        totalServers: this.servers.size,
        healthyServers: Array.from(this.servers.values()).filter(s => s.isHealthy).length,
        algorithm: this.currentAlgorithm,
        servers: Array.from(this.servers.entries()).map(([id, server]) => ({
          id,
          isHealthy: server.isHealthy,
          activeConnections: server.activeConnections,
          totalRequests: server.totalRequests,
          failedRequests: server.failedRequests,
          responseTime: server.responseTime,
          circuitBreakerState: server.circuitBreakerState,
          lastHealthCheck: server.lastHealthCheck
        })),
        timestamp: new Date().toISOString()
      };

      // Cache metrics for monitoring
      await redisService.set('load_balancer_metrics', metrics, 300);
      
    } catch (error) {
      logger.error('Failed to update load balancer metrics:', error);
    }
  }

  /**
   * Get load balancer status
   */
  async getStatus() {
    try {
      const healthyServers = Array.from(this.servers.values()).filter(s => s.isHealthy);
      const totalConnections = Array.from(this.servers.values())
        .reduce((sum, server) => sum + server.activeConnections, 0);
      
      return {
        status: healthyServers.length > 0 ? 'healthy' : 'unhealthy',
        algorithm: this.currentAlgorithm,
        totalServers: this.servers.size,
        healthyServers: healthyServers.length,
        totalConnections,
        isMonitoring: this.isMonitoring,
        servers: Array.from(this.servers.entries()).map(([id, server]) => ({
          id,
          host: server.host,
          port: server.port,
          isHealthy: server.isHealthy,
          activeConnections: server.activeConnections,
          responseTime: server.responseTime,
          circuitBreakerState: server.circuitBreakerState,
          lastHealthCheck: server.lastHealthCheck
        })),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Change load balancing algorithm
   */
  setAlgorithm(algorithm) {
    if (Object.values(this.algorithms).includes(algorithm)) {
      this.currentAlgorithm = algorithm;
      logger.info(`Load balancing algorithm changed to ${algorithm}`);
      return true;
    }
    return false;
  }

  /**
   * Health check for load balancer service
   */
  async healthCheck() {
    try {
      const status = await this.getStatus();
      return {
        status: status.status,
        details: status,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const loadBalancerService = new LoadBalancerService();

module.exports = loadBalancerService;
