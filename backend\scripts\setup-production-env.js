#!/usr/bin/env node

/**
 * Production Environment Setup Script
 * Validates and configures production environment for JiraniPay
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function generateSecureKey(length = 64) {
  return crypto.randomBytes(length).toString('base64');
}

function generateEncryptionKey() {
  return crypto.randomBytes(32).toString('base64');
}

async function validateSupabaseConnection(url, key) {
  try {
    const supabase = createClient(url, key);
    const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
    return !error;
  } catch (error) {
    return false;
  }
}

async function setupProductionEnvironment() {
  log('🏭 JiraniPay Production Environment Setup', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  const envPath = path.join(__dirname, '../.env.production.local');
  const templatePath = path.join(__dirname, '../.env.production');
  
  // Read template
  if (!fs.existsSync(templatePath)) {
    log('❌ Production template file not found!', 'red');
    process.exit(1);
  }
  
  let envContent = fs.readFileSync(templatePath, 'utf8');
  
  log('\n🔐 SECURITY CONFIGURATION', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // Generate JWT Secret
  const jwtSecret = generateSecureKey(64);
  log('✅ Generated secure JWT secret (64 bytes)', 'green');
  envContent = envContent.replace('CHANGE_THIS_TO_STRONG_JWT_SECRET_IN_PRODUCTION_MINIMUM_64_CHARACTERS_REQUIRED', jwtSecret);
  
  // Generate Encryption Key
  const encryptionKey = generateEncryptionKey();
  log('✅ Generated secure encryption key (32 bytes)', 'green');
  envContent = envContent.replace('CHANGE_THIS_TO_STRONG_ENCRYPTION_KEY_32_BYTES_BASE64_ENCODED', encryptionKey);
  
  // Generate Session Secret
  const sessionSecret = generateSecureKey(64);
  log('✅ Generated secure session secret (64 bytes)', 'green');
  envContent = envContent.replace('CHANGE_THIS_TO_STRONG_SESSION_SECRET_64_CHARACTERS_MINIMUM', sessionSecret);
  
  log('\n🗄️ DATABASE CONFIGURATION', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // Supabase Configuration
  const supabaseUrl = await question('Enter your production Supabase URL: ');
  const supabaseAnonKey = await question('Enter your production Supabase anon key: ');
  const supabaseServiceKey = await question('Enter your production Supabase service role key: ');
  
  // Validate Supabase connection
  log('🔍 Validating Supabase connection...', 'blue');
  const isValidConnection = await validateSupabaseConnection(supabaseUrl, supabaseAnonKey);
  
  if (isValidConnection) {
    log('✅ Supabase connection validated successfully', 'green');
    envContent = envContent.replace('https://tooamucmigdsoxqvjxve.supabase.co', supabaseUrl);
    envContent = envContent.replace('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRvb2FtdWNtaWdkc294cXZqeHZlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU5MzI2NzQsImV4cCI6MjA1MTUwODY3NH0.Ej3_Qs8-Ej3_Qs8-Ej3_Qs8-Ej3_Qs8-Ej3_Qs8', supabaseAnonKey);
    envContent = envContent.replace('REPLACE_WITH_YOUR_ACTUAL_SERVICE_ROLE_KEY_FROM_SUPABASE_DASHBOARD', supabaseServiceKey);
  } else {
    log('❌ Supabase connection validation failed', 'red');
    const proceed = await question('Continue anyway? (y/N): ');
    if (proceed.toLowerCase() !== 'y') {
      log('Setup cancelled', 'yellow');
      process.exit(1);
    }
  }
  
  log('\n💳 PAYMENT GATEWAY CONFIGURATION', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // MTN Mobile Money
  const mtnApiKey = await question('Enter MTN Mobile Money API key (or press Enter to skip): ');
  if (mtnApiKey) {
    const mtnApiSecret = await question('Enter MTN Mobile Money API secret: ');
    envContent = envContent.replace('your-production-mtn-api-key', mtnApiKey);
    envContent = envContent.replace('your-production-mtn-api-secret', mtnApiSecret);
    log('✅ MTN Mobile Money configured', 'green');
  }
  
  // Airtel Money
  const airtelApiKey = await question('Enter Airtel Money API key (or press Enter to skip): ');
  if (airtelApiKey) {
    const airtelApiSecret = await question('Enter Airtel Money API secret: ');
    envContent = envContent.replace('your-production-airtel-api-key', airtelApiKey);
    envContent = envContent.replace('your-production-airtel-api-secret', airtelApiSecret);
    log('✅ Airtel Money configured', 'green');
  }
  
  log('\n📧 COMMUNICATION SERVICES', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // SMS Configuration
  const smsProvider = await question('SMS Provider (twilio/aws-sns) [twilio]: ') || 'twilio';
  const smsApiKey = await question('Enter SMS API key (or press Enter to skip): ');
  if (smsApiKey) {
    const smsApiSecret = await question('Enter SMS API secret: ');
    const smsFromNumber = await question('Enter SMS from number (e.g., +256700000000): ');
    envContent = envContent.replace('your-production-sms-api-key', smsApiKey);
    envContent = envContent.replace('your-production-sms-api-secret', smsApiSecret);
    envContent = envContent.replace('+256700000000', smsFromNumber);
    log('✅ SMS service configured', 'green');
  }
  
  // Email Configuration
  const emailProvider = await question('Email Provider (sendgrid/aws-ses) [sendgrid]: ') || 'sendgrid';
  const emailApiKey = await question('Enter Email API key (or press Enter to skip): ');
  if (emailApiKey) {
    const emailFromAddress = await question('Enter from email address [<EMAIL>]: ') || '<EMAIL>';
    envContent = envContent.replace('your-production-email-api-key', emailApiKey);
    envContent = envContent.replace('<EMAIL>', emailFromAddress);
    log('✅ Email service configured', 'green');
  }
  
  log('\n🔍 MONITORING & OBSERVABILITY', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // Sentry Configuration
  const sentryDsn = await question('Enter Sentry DSN (or press Enter to skip): ');
  if (sentryDsn) {
    envContent = envContent.replace('https://<EMAIL>/project-id', sentryDsn);
    log('✅ Sentry error tracking configured', 'green');
  }
  
  log('\n🌐 DOMAIN CONFIGURATION', 'yellow');
  log('-'.repeat(40), 'yellow');
  
  // CORS Origins
  const corsOrigins = await question('Enter production domains (comma-separated) [https://jiranipay.com]: ') || 'https://jiranipay.com';
  envContent = envContent.replace('https://jiranipay.com,https://www.jiranipay.com,https://app.jiranipay.com', corsOrigins);
  log('✅ CORS origins configured', 'green');
  
  // Write production environment file
  fs.writeFileSync(envPath, envContent);
  log(`\n✅ Production environment file created: ${envPath}`, 'green');
  
  // Set file permissions (Unix-like systems)
  try {
    fs.chmodSync(envPath, 0o600); // Read/write for owner only
    log('✅ Secure file permissions set (600)', 'green');
  } catch (error) {
    log('⚠️ Could not set file permissions (Windows system)', 'yellow');
  }
  
  log('\n🚀 PRODUCTION READINESS CHECKLIST', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  const checklist = [
    '✅ Secure JWT secret generated',
    '✅ Encryption key generated',
    '✅ Session secret generated',
    `${supabaseUrl ? '✅' : '❌'} Supabase configuration`,
    `${mtnApiKey ? '✅' : '⚠️'} MTN Mobile Money integration`,
    `${airtelApiKey ? '✅' : '⚠️'} Airtel Money integration`,
    `${smsApiKey ? '✅' : '⚠️'} SMS service configuration`,
    `${emailApiKey ? '✅' : '⚠️'} Email service configuration`,
    `${sentryDsn ? '✅' : '⚠️'} Error monitoring (Sentry)`,
    '✅ CORS origins configured',
    '✅ Production environment file created'
  ];
  
  checklist.forEach(item => {
    if (item.includes('✅')) {
      log(item, 'green');
    } else if (item.includes('⚠️')) {
      log(item, 'yellow');
    } else {
      log(item, 'red');
    }
  });
  
  log('\n📋 NEXT STEPS', 'cyan');
  log('-'.repeat(40), 'cyan');
  log('1. Review and update any remaining placeholder values', 'white');
  log('2. Set up production Redis instance', 'white');
  log('3. Configure SSL/TLS certificates', 'white');
  log('4. Set up load balancer and CDN', 'white');
  log('5. Configure backup and disaster recovery', 'white');
  log('6. Set up monitoring and alerting', 'white');
  log('7. Run security audit: npm run security-audit', 'white');
  log('8. Deploy to production: npm run deploy:production', 'white');
  
  log('\n🔒 SECURITY REMINDERS', 'red');
  log('-'.repeat(40), 'red');
  log('• Never commit .env.production.local to version control', 'red');
  log('• Regularly rotate secrets and API keys', 'red');
  log('• Monitor for security vulnerabilities', 'red');
  log('• Keep dependencies updated', 'red');
  log('• Enable audit logging in production', 'red');
  
  log('\n🎉 Production environment setup completed!', 'green');
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🏭 JiraniPay Production Environment Setup

Usage: node setup-production-env.js [options]

Options:
  --help, -h     Show this help message

This script will:
1. Generate secure secrets and keys
2. Configure database connections
3. Set up payment gateways
4. Configure monitoring services
5. Create production-ready environment file

The script will prompt you for necessary credentials and configuration.
`);
  process.exit(0);
}

// Run the setup
setupProductionEnvironment()
  .then(() => {
    rl.close();
    process.exit(0);
  })
  .catch((error) => {
    log(`❌ Setup failed: ${error.message}`, 'red');
    rl.close();
    process.exit(1);
  });
