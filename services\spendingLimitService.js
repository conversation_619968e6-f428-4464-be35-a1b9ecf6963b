import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from './notificationService';

class SpendingLimitService {
  constructor() {
    this.isInitialized = false;
    this.userLimits = {
      dailyLimit: 500000,
      monthlyLimit: 2000000,
      alertThreshold: 80 // Alert when 80% of limit is reached
    };
    this.spendingData = {
      daily: { amount: 0, date: null },
      monthly: { amount: 0, month: null }
    };
  }

  async initialize() {
    try {
      console.log('💰 Initializing spending limit service...');
      
      // Load user limits
      await this.loadUserLimits();
      
      // Load spending data
      await this.loadSpendingData();
      
      // Reset data if needed (new day/month)
      this.resetIfNeeded();
      
      this.isInitialized = true;
      console.log('✅ Spending limit service initialized');
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing spending limit service:', error);
      return { success: false, error: error.message };
    }
  }

  async loadUserLimits() {
    try {
      const stored = await AsyncStorage.getItem('userSpendingLimits');
      if (stored) {
        this.userLimits = { ...this.userLimits, ...JSON.parse(stored) };
      }
      console.log('📊 User spending limits loaded:', this.userLimits);
    } catch (error) {
      console.error('❌ Error loading user limits:', error);
    }
  }

  async saveUserLimits() {
    try {
      await AsyncStorage.setItem('userSpendingLimits', JSON.stringify(this.userLimits));
      console.log('💾 User spending limits saved');
    } catch (error) {
      console.error('❌ Error saving user limits:', error);
    }
  }

  async loadSpendingData() {
    try {
      const stored = await AsyncStorage.getItem('spendingData');
      if (stored) {
        this.spendingData = { ...this.spendingData, ...JSON.parse(stored) };
      }
      console.log('📊 Spending data loaded:', this.spendingData);
    } catch (error) {
      console.error('❌ Error loading spending data:', error);
    }
  }

  async saveSpendingData() {
    try {
      await AsyncStorage.setItem('spendingData', JSON.stringify(this.spendingData));
      console.log('💾 Spending data saved');
    } catch (error) {
      console.error('❌ Error saving spending data:', error);
    }
  }

  resetIfNeeded() {
    const today = new Date().toDateString();
    const currentMonth = new Date().getMonth() + '-' + new Date().getFullYear();

    // Reset daily spending if new day
    if (this.spendingData.daily.date !== today) {
      this.spendingData.daily = { amount: 0, date: today };
      console.log('🔄 Daily spending reset for new day');
    }

    // Reset monthly spending if new month
    if (this.spendingData.monthly.month !== currentMonth) {
      this.spendingData.monthly = { amount: 0, month: currentMonth };
      console.log('🔄 Monthly spending reset for new month');
    }
  }

  async updateLimits(newLimits) {
    try {
      this.userLimits = { ...this.userLimits, ...newLimits };
      await this.saveUserLimits();
      console.log('✅ Spending limits updated:', newLimits);
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating limits:', error);
      return { success: false, error: error.message };
    }
  }

  async checkTransactionLimit(amount) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      this.resetIfNeeded();

      const newDailyTotal = this.spendingData.daily.amount + amount;
      const newMonthlyTotal = this.spendingData.monthly.amount + amount;

      // Check daily limit
      if (newDailyTotal > this.userLimits.dailyLimit) {
        const result = {
          allowed: false,
          reason: 'daily_limit_exceeded',
          message: `Transaction would exceed daily limit of UGX ${this.userLimits.dailyLimit.toLocaleString()}`,
          currentSpending: this.spendingData.daily.amount,
          limit: this.userLimits.dailyLimit,
          attemptedAmount: amount
        };
        
        // Send notification
        await notificationService.scheduleSpendingLimitAlertWithSettings(
          newDailyTotal, 
          this.userLimits.dailyLimit, 
          'daily'
        );
        
        return result;
      }

      // Check monthly limit
      if (newMonthlyTotal > this.userLimits.monthlyLimit) {
        const result = {
          allowed: false,
          reason: 'monthly_limit_exceeded',
          message: `Transaction would exceed monthly limit of UGX ${this.userLimits.monthlyLimit.toLocaleString()}`,
          currentSpending: this.spendingData.monthly.amount,
          limit: this.userLimits.monthlyLimit,
          attemptedAmount: amount
        };
        
        // Send notification
        await notificationService.scheduleSpendingLimitAlertWithSettings(
          newMonthlyTotal, 
          this.userLimits.monthlyLimit, 
          'monthly'
        );
        
        return result;
      }

      // Check if approaching limits (80% threshold)
      const dailyPercentage = (newDailyTotal / this.userLimits.dailyLimit) * 100;
      const monthlyPercentage = (newMonthlyTotal / this.userLimits.monthlyLimit) * 100;

      if (dailyPercentage >= this.userLimits.alertThreshold) {
        await notificationService.scheduleSpendingLimitAlertWithSettings(
          newDailyTotal, 
          this.userLimits.dailyLimit, 
          'daily'
        );
      }

      if (monthlyPercentage >= this.userLimits.alertThreshold) {
        await notificationService.scheduleSpendingLimitAlertWithSettings(
          newMonthlyTotal, 
          this.userLimits.monthlyLimit, 
          'monthly'
        );
      }

      return {
        allowed: true,
        dailyPercentage: dailyPercentage.toFixed(1),
        monthlyPercentage: monthlyPercentage.toFixed(1),
        remainingDaily: this.userLimits.dailyLimit - newDailyTotal,
        remainingMonthly: this.userLimits.monthlyLimit - newMonthlyTotal
      };

    } catch (error) {
      console.error('❌ Error checking transaction limit:', error);
      return { 
        allowed: true, // Allow transaction if check fails
        warning: 'Could not verify spending limits'
      };
    }
  }

  async recordTransaction(amount) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      this.resetIfNeeded();

      // Update spending data
      this.spendingData.daily.amount += amount;
      this.spendingData.monthly.amount += amount;

      // Save updated data
      await this.saveSpendingData();

      console.log(`💸 Transaction recorded: UGX ${amount.toLocaleString()}`);
      console.log(`📊 Daily total: UGX ${this.spendingData.daily.amount.toLocaleString()}`);
      console.log(`📊 Monthly total: UGX ${this.spendingData.monthly.amount.toLocaleString()}`);

      return { success: true };
    } catch (error) {
      console.error('❌ Error recording transaction:', error);
      return { success: false, error: error.message };
    }
  }

  getSpendingSummary() {
    return {
      daily: {
        spent: this.spendingData.daily.amount,
        limit: this.userLimits.dailyLimit,
        remaining: this.userLimits.dailyLimit - this.spendingData.daily.amount,
        percentage: (this.spendingData.daily.amount / this.userLimits.dailyLimit) * 100
      },
      monthly: {
        spent: this.spendingData.monthly.amount,
        limit: this.userLimits.monthlyLimit,
        remaining: this.userLimits.monthlyLimit - this.spendingData.monthly.amount,
        percentage: (this.spendingData.monthly.amount / this.userLimits.monthlyLimit) * 100
      }
    };
  }

  getUserLimits() {
    return this.userLimits;
  }
}

// Create and export singleton instance
const spendingLimitService = new SpendingLimitService();
export default spendingLimitService;
