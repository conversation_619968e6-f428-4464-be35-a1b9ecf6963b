/**
 * <PERSON><PERSON>t to fix remaining supabase references in authService.js
 */

const fs = require('fs');
const path = require('path');

const authServicePath = path.join(__dirname, 'services', 'authService.js');

console.log('🔧 Fixing remaining supabase references in authService.js...');

// Read the file
let content = fs.readFileSync(authServicePath, 'utf8');

// Count original supabase references
const originalCount = (content.match(/await supabase\./g) || []).length;
console.log(`📊 Found ${originalCount} direct supabase references to fix`);

// Replace patterns that need the secure client
const replacements = [
  // Pattern: await supabase.auth.method(
  {
    pattern: /(\s+)(const { data, error } = )?await supabase\.auth\./g,
    replacement: '$1const supabase = await this.getSupabaseClient();\n$1$2await supabase.auth.'
  },
  // Pattern: await supabase.from(
  {
    pattern: /(\s+)(const { data[^}]*} = )?await supabase\.from\(/g,
    replacement: '$1const supabase = await this.getSupabaseClient();\n$1$2await supabase.from('
  },
  // Pattern: supabase.auth.setSession
  {
    pattern: /(\s+)await supabase\.auth\.setSession\(/g,
    replacement: '$1const supabase = await this.getSupabaseClient();\n$1await supabase.auth.setSession('
  },
  // Pattern: supabase.auth.signOut
  {
    pattern: /(\s+)await supabase\.auth\.signOut\(/g,
    replacement: '$1const supabase = await this.getSupabaseClient();\n$1await supabase.auth.signOut('
  }
];

// Apply replacements
let modifiedContent = content;
let totalReplacements = 0;

replacements.forEach((replacement, index) => {
  const matches = modifiedContent.match(replacement.pattern);
  if (matches) {
    console.log(`🔄 Applying replacement ${index + 1}: ${matches.length} matches`);
    modifiedContent = modifiedContent.replace(replacement.pattern, replacement.replacement);
    totalReplacements += matches.length;
  }
});

// Remove duplicate getSupabaseClient calls that might be created
modifiedContent = modifiedContent.replace(
  /(const supabase = await this\.getSupabaseClient\(\);\s*\n\s*const supabase = await this\.getSupabaseClient\(\);)/g,
  'const supabase = await this.getSupabaseClient();'
);

// Count remaining direct references
const remainingCount = (modifiedContent.match(/await supabase\./g) || []).length;

console.log(`📊 Applied ${totalReplacements} replacements`);
console.log(`📊 Remaining direct supabase references: ${remainingCount}`);

// Write the modified content back
fs.writeFileSync(authServicePath, modifiedContent, 'utf8');

console.log('✅ AuthService.js updated successfully');
console.log('🔧 All supabase calls now use the secure client');

// Verify the changes
const verifyContent = fs.readFileSync(authServicePath, 'utf8');
const finalCount = (verifyContent.match(/await supabase\./g) || []).length;
const secureClientCalls = (verifyContent.match(/await this\.getSupabaseClient\(\)/g) || []).length;

console.log('\n📋 VERIFICATION:');
console.log(`   - Original direct supabase calls: ${originalCount}`);
console.log(`   - Remaining direct supabase calls: ${finalCount}`);
console.log(`   - Secure client calls added: ${secureClientCalls}`);

if (finalCount === 0) {
  console.log('✅ SUCCESS: All supabase calls now use secure client');
} else {
  console.log('⚠️ WARNING: Some direct supabase calls remain');
}

console.log('\n🚀 AuthService is now ready to use secure configuration!');
