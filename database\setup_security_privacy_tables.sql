-- =====================================================
-- SECURITY & PRIVACY TABLES SETUP SCRIPT
-- Run this in your Supabase SQL Editor
-- =====================================================

-- Create audit_logs table first (required by services)
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_settings table
CREATE TABLE IF NOT EXISTS public.security_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pin_enabled BOOLEAN DEFAULT FALSE,
    pin_hash TEXT,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_method TEXT DEFAULT 'sms',
    biometric_enabled BOOLEAN DEFAULT FALSE,
    session_timeout_minutes INTEGER DEFAULT 30,
    login_attempts_limit INTEGER DEFAULT 5,
    failed_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    last_password_change TIMESTAMP WITH TIME ZONE,
    security_questions JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create privacy_settings table
CREATE TABLE IF NOT EXISTS public.privacy_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    data_sharing_enabled BOOLEAN DEFAULT FALSE,
    marketing_emails BOOLEAN DEFAULT TRUE,
    marketing_sms BOOLEAN DEFAULT TRUE,
    analytics_tracking BOOLEAN DEFAULT TRUE,
    profile_visibility TEXT DEFAULT 'private',
    transaction_history_retention_days INTEGER DEFAULT 2555,
    data_export_requested BOOLEAN DEFAULT FALSE,
    data_export_requested_at TIMESTAMP WITH TIME ZONE,
    account_deletion_requested BOOLEAN DEFAULT FALSE,
    account_deletion_requested_at TIMESTAMP WITH TIME ZONE,
    deletion_reason TEXT,
    consent_given JSONB DEFAULT '{"essential": true, "analytics": true, "marketing": true, "dataSharing": false, "locationTracking": false}',
    consent_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_security_settings_user_id ON public.security_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON public.privacy_settings(user_id);

-- Enable Row Level Security
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.privacy_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users can insert own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users can view own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can insert own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can update own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can view own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Users can insert own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Users can update own privacy_settings" ON public.privacy_settings;

-- Create RLS Policies with proper permissions
-- Audit logs policies
CREATE POLICY "Enable all operations for authenticated users on audit_logs" ON public.audit_logs
    FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Security settings policies
CREATE POLICY "Enable all operations for authenticated users on security_settings" ON public.security_settings
    FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Privacy settings policies
CREATE POLICY "Enable all operations for authenticated users on privacy_settings" ON public.privacy_settings
    FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Insert default settings for existing users (optional)
-- Uncomment the lines below if you want to create default settings for existing users

-- INSERT INTO public.security_settings (user_id, pin_enabled, biometric_enabled, two_factor_enabled)
-- SELECT id, false, false, false FROM auth.users 
-- WHERE id NOT IN (SELECT user_id FROM public.security_settings);

-- INSERT INTO public.privacy_settings (user_id, data_sharing_enabled, marketing_emails, marketing_sms, analytics_tracking)
-- SELECT id, false, true, true, true FROM auth.users 
-- WHERE id NOT IN (SELECT user_id FROM public.privacy_settings);

-- Success message
SELECT 'Security and Privacy tables created successfully!' as message;
