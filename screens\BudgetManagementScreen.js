/**
 * Budget Management Screen
 * Main screen for budget overview, creation, and management
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  Alert,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

// Services and Hooks
import budgetManagementService from '../services/budgetManagementService';
import predictiveAnalyticsService from '../services/predictiveAnalyticsService';
import authService from '../services/authService';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';

// Components
import SkeletonLoader from '../components/SkeletonLoader';
import BudgetCard from '../components/budget/BudgetCard';
import BudgetSummaryCard from '../components/budget/BudgetSummaryCard';
import RecommendationCard from '../components/budget/RecommendationCard';

// Constants
import { Colors } from '../constants/Colors';

const BudgetManagementScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { formatCurrencyValue } = useCurrencyContext();
  
  const [user, setUser] = useState(null);
  const [budgets, setBudgets] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [forecasts, setForecasts] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Initialize screen
   */
  useEffect(() => {
    initializeScreen();
  }, []);

  const initializeScreen = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      if (!currentUser) {
        navigation.replace('Login');
        return;
      }

      setUser(currentUser);
      await loadBudgetData(currentUser.id);
    } catch (err) {
      console.error('❌ Error initializing budget screen:', err);
      setError(err.message);
    }
  };

  /**
   * Load all budget-related data
   */
  const loadBudgetData = useCallback(async (userId, showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      // Initialize services
      await Promise.all([
        budgetManagementService.initialize(userId),
        predictiveAnalyticsService.initialize(userId)
      ]);

      // Load data in parallel
      const [budgetsResult, analyticsResult, recommendationsResult, forecastsResult] = await Promise.all([
        budgetManagementService.getUserBudgets(userId),
        budgetManagementService.getBudgetAnalytics(userId),
        budgetManagementService.getBudgetRecommendations(userId),
        predictiveAnalyticsService.generateSpendingForecast(userId, 'month', 3)
      ]);

      // Handle results
      if (budgetsResult.success) {
        setBudgets(budgetsResult.data || []);
      } else {
        console.warn('⚠️ Error loading budgets:', budgetsResult.error);
      }

      if (analyticsResult.success) {
        setAnalytics(analyticsResult.data);
      } else {
        console.warn('⚠️ Error loading analytics:', analyticsResult.error);
      }

      if (recommendationsResult.success) {
        setRecommendations(recommendationsResult.data || []);
      } else {
        console.warn('⚠️ Error loading recommendations:', recommendationsResult.error);
      }

      if (forecastsResult.success) {
        setForecasts(forecastsResult.data);
      } else {
        console.warn('⚠️ Error loading forecasts:', forecastsResult.error);
      }

    } catch (err) {
      console.error('❌ Error loading budget data:', err);
      setError(err.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(() => {
    if (user?.id) {
      setRefreshing(true);
      loadBudgetData(user.id, false);
    }
  }, [user?.id, loadBudgetData]);

  /**
   * Navigate to create budget
   */
  const handleCreateBudget = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate('CreateBudget', { 
      userId: user?.id,
      forecasts: forecasts
    });
  }, [navigation, user?.id, forecasts]);

  /**
   * Navigate to budget details
   */
  const handleBudgetPress = useCallback((budget) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('BudgetDetails', { 
      budgetId: budget.id,
      budget: budget
    });
  }, [navigation]);

  /**
   * Handle recommendation action
   */
  const handleRecommendationAction = useCallback(async (recommendation) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      switch (recommendation.action) {
        case 'review_spending':
          navigation.navigate('SpendingAnalysis', { userId: user?.id });
          break;
        case 'adjust_categories':
          navigation.navigate('BudgetCategories', { userId: user?.id });
          break;
        case 'reallocate_budget':
          navigation.navigate('BudgetReallocation', { userId: user?.id });
          break;
        case 'increase_savings':
          navigation.navigate('SavingsGoals', { userId: user?.id });
          break;
        default:
          Alert.alert('Info', recommendation.message);
      }
    } catch (error) {
      console.error('❌ Error handling recommendation action:', error);
      Alert.alert('Error', 'Failed to perform action. Please try again.');
    }
  }, [navigation, user?.id]);

  /**
   * Render header
   */
  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.headerContent}>
        <View>
          <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
            Budget Management
          </Text>
          <Text style={[styles.headerSubtitle, { color: theme.colors.textSecondary }]}>
            Track and optimize your spending
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.createButton, { backgroundColor: Colors.primary.main }]}
          onPress={handleCreateBudget}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );

  /**
   * Render budget summary
   */
  const renderBudgetSummary = () => {
    if (!analytics) return null;

    return (
      <BudgetSummaryCard
        analytics={analytics}
        forecasts={forecasts}
        theme={theme}
        formatCurrency={formatCurrencyValue}
      />
    );
  };

  /**
   * Render recommendations
   */
  const renderRecommendations = () => {
    if (!recommendations.length) return null;

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Recommendations
        </Text>
        {recommendations.slice(0, 3).map((recommendation, index) => (
          <RecommendationCard
            key={index}
            recommendation={recommendation}
            onPress={() => handleRecommendationAction(recommendation)}
            theme={theme}
          />
        ))}
      </View>
    );
  };

  /**
   * Render budgets list
   */
  const renderBudgetsList = () => {
    if (!budgets.length) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name="wallet-outline" 
            size={64} 
            color={theme.colors.textSecondary} 
          />
          <Text style={[styles.emptyStateTitle, { color: theme.colors.text }]}>
            No Budgets Yet
          </Text>
          <Text style={[styles.emptyStateText, { color: theme.colors.textSecondary }]}>
            Create your first budget to start tracking your spending
          </Text>
          <TouchableOpacity
            style={[styles.createBudgetButton, { backgroundColor: Colors.primary.main }]}
            onPress={handleCreateBudget}
          >
            <Text style={styles.createBudgetButtonText}>Create Budget</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Your Budgets
        </Text>
        {budgets.map((budget) => (
          <BudgetCard
            key={budget.id}
            budget={budget}
            onPress={() => handleBudgetPress(budget)}
            theme={theme}
            formatCurrency={formatCurrencyValue}
          />
        ))}
      </View>
    );
  };

  /**
   * Render error state
   */
  if (error) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.colors.background }]}>
        <Ionicons name="alert-circle" size={64} color={Colors.status.error} />
        <Text style={[styles.errorTitle, { color: theme.colors.text }]}>
          Error Loading Budgets
        </Text>
        <Text style={[styles.errorText, { color: theme.colors.textSecondary }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: Colors.primary.main }]}
          onPress={() => user?.id && loadBudgetData(user.id)}
        >
          <Text style={styles.retryButtonText}>Try Again</Text>
        </TouchableOpacity>
      </View>
    );
  }

  /**
   * Render loading state
   */
  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <SkeletonLoader
          height={200}
          style={{ margin: 16, borderRadius: 12 }}
        />
        <SkeletonLoader
          height={150}
          style={{ margin: 16, borderRadius: 12 }}
        />
        <SkeletonLoader
          height={100}
          style={{ margin: 16, borderRadius: 12 }}
        />
        <Text style={[styles.loadingText, { color: theme.colors.textSecondary, textAlign: 'center', marginTop: 20 }]}>
          Loading your budgets...
        </Text>
      </View>
    );
  }

  /**
   * Main render
   */
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary.main]}
            tintColor={Colors.primary.main}
          />
        }
      >
        {renderBudgetSummary()}
        {renderRecommendations()}
        {renderBudgetsList()}
        
        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
    marginTop: 4,
  },
  createButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  createBudgetButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  createBudgetButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  retryButton: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default BudgetManagementScreen;
