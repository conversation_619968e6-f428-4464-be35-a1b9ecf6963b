import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons, FontAwesome5 } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { Colors } from '../constants/Colors';

const { width, height } = Dimensions.get('window');

const EnhancedSplashScreen = ({ onAnimationComplete }) => {
  const [currentMessage, setCurrentMessage] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const { theme, isDarkMode, isInitialized } = useTheme() || { 
    theme: { 
      colors: { 
        background: '#FFFFFF',
        text: '#000000',
        primary: '#E67E22',
        secondary: '#2C3E50',
        accent: '#F39C12',
        success: '#27AE60',
        warning: '#F39C12',
        error: '#C0392B',
        info: '#3498DB',
      },
      statusBar: 'dark'
    },
    isDarkMode: false,
    isInitialized: false 
  };

  // Return a simple loading screen while theme initializes
  if (!isInitialized) {
    return (
      <View style={{ flex: 1, backgroundColor: '#FFFFFF' }}>
        <StatusBar barStyle="dark-content" />
      </View>
    );
  }

  useEffect(() => {
    if (isInitialized) {
      setIsReady(true);
    }
  }, [isInitialized]);

  // Return a loading fallback while theme initializes
  if (!isReady) {
    return (
      <View style={{ flex: 1, backgroundColor: '#FFFFFF' }}>
        <StatusBar barStyle="dark-content" />
      </View>
    );
  }

  // Trust messages that rotate during loading
  const trustMessages = [
    'Securing your financial future...',
    'Connecting communities across East Africa...',
    'Building trust, one transaction at a time...',
    'Empowering financial inclusion...',
  ];

  // Animation values
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const logoRotation = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const connectionsOpacity = useRef(new Animated.Value(0)).current;
  const loadingOpacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;
  const messageOpacity = useRef(new Animated.Value(0)).current;
  
  // Cultural elements
  const culturalElement1 = useRef(new Animated.Value(0)).current;
  const culturalElement2 = useRef(new Animated.Value(0)).current;
  const culturalElement3 = useRef(new Animated.Value(0)).current;
  
  // Connection network animation
  const networkPulse = useRef(new Animated.Value(0)).current;
  const connectionFlow = useRef(new Animated.Value(0)).current;
  
  // Trust indicators
  const trustIndicator1 = useRef(new Animated.Value(0)).current;
  const trustIndicator2 = useRef(new Animated.Value(0)).current;
  const trustIndicator3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Message rotation
    const messageInterval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % trustMessages.length);
    }, 1000);

    const animationSequence = Animated.sequence([
      // Phase 1: Logo entrance with cultural flair (0-1000ms)
      Animated.parallel([
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(logoRotation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      
      // Phase 2: Cultural elements and network (1000-2000ms)
      Animated.parallel([
        Animated.stagger(200, [
          Animated.spring(culturalElement1, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(culturalElement2, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(culturalElement3, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
        Animated.timing(connectionsOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
      
      // Phase 3: Title and network pulse (2000-2800ms)
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.loop(
          Animated.sequence([
            Animated.timing(networkPulse, {
              toValue: 1,
              duration: 1000,
              useNativeDriver: true,
            }),
            Animated.timing(networkPulse, {
              toValue: 0,
              duration: 1000,
              useNativeDriver: true,
            }),
          ])
        ),
      ]),
      
      // Phase 4: Tagline and trust indicators (2800-3600ms)
      Animated.parallel([
        Animated.timing(taglineOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.stagger(150, [
          Animated.spring(trustIndicator1, {
            toValue: 1,
            tension: 80,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(trustIndicator2, {
            toValue: 1,
            tension: 80,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(trustIndicator3, {
            toValue: 1,
            tension: 80,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]),
      
      // Phase 5: Loading and connection flow (3600-4800ms)
      Animated.parallel([
        Animated.timing(loadingOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(messageOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(progressWidth, {
          toValue: 1,
          duration: 1200,
          useNativeDriver: false,
        }),
        Animated.loop(
          Animated.timing(connectionFlow, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          })
        ),
      ]),
      
      // Phase 6: Hold and complete (4800-5300ms)
      Animated.delay(500),
    ]);

    animationSequence.start(({ finished }) => {
      if (finished) {
        setTimeout(() => {
          clearInterval(messageInterval);
          onAnimationComplete && onAnimationComplete();
        }, 500);
      }
    });

    return () => clearInterval(messageInterval);
  }, []);

  // Dynamic colors based on theme
  const gradientColors = isDarkMode
    ? ['#1a1a1a', '#2d2d2d', '#1a1a1a']
    : ['#fcf7f0', '#ffffff', '#fcf7f0'];

  const textColor = isDarkMode ? '#FFFFFF' : '#2C3E50';
  const subtitleColor = isDarkMode ? '#B3B3B3' : '#95A5A6';
  const accentColor = '#E67E22';

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      
      <LinearGradient
        colors={gradientColors}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Cultural Background Pattern */}
        <View style={styles.backgroundPattern}>
          {/* Traditional East African geometric patterns */}
          <View style={[styles.geometricPattern, styles.pattern1]} />
          <View style={[styles.geometricPattern, styles.pattern2]} />
          <View style={[styles.geometricPattern, styles.pattern3]} />
          <View style={[styles.geometricPattern, styles.pattern4]} />
        </View>

        {/* Network Pulse Effect */}
        <Animated.View
          style={[
            styles.networkPulse,
            {
              opacity: networkPulse.interpolate({
                inputRange: [0, 1],
                outputRange: [0.1, 0.3],
              }),
              transform: [
                {
                  scale: networkPulse.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.5],
                  }),
                },
              ],
            },
          ]}
        />

        {/* Main Content */}
        <View style={styles.content}>
          {/* Cultural Elements */}
          <Animated.View
            style={[
              styles.culturalElement,
              styles.culturalElement1,
              {
                opacity: culturalElement1,
                transform: [
                  {
                    scale: culturalElement1,
                  },
                  {
                    rotate: culturalElement1.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg'],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.culturalSymbol}>🌍</Text>
          </Animated.View>

          <Animated.View
            style={[
              styles.culturalElement,
              styles.culturalElement2,
              {
                opacity: culturalElement2,
                transform: [
                  {
                    scale: culturalElement2,
                  },
                ],
              },
            ]}
          >
            <Text style={styles.culturalSymbol}>🤝</Text>
          </Animated.View>

          <Animated.View
            style={[
              styles.culturalElement,
              styles.culturalElement3,
              {
                opacity: culturalElement3,
                transform: [
                  {
                    scale: culturalElement3,
                  },
                ],
              },
            ]}
          >
            <Text style={styles.culturalSymbol}>💫</Text>
          </Animated.View>

          {/* Logo Section with Enhanced Animation */}
          <Animated.View
            style={[
              styles.logoContainer,
              {
                transform: [
                  { scale: logoScale },
                  {
                    rotate: logoRotation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg'],
                    }),
                  },
                ],
                opacity: logoOpacity,
              },
            ]}
          >
            <LinearGradient
              colors={[Colors.primary.main, Colors.accent.gold]}
              style={styles.logoBackground}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons
                name="wallet"
                size={60}
                color="#FFFFFF"
              />
            </LinearGradient>
          </Animated.View>

          {/* Connection Network Visualization */}
          <Animated.View
            style={[
              styles.connectionsContainer,
              { opacity: connectionsOpacity },
            ]}
          >
            {/* Animated connection flow */}
            <Animated.View
              style={[
                styles.connectionFlow,
                {
                  opacity: connectionFlow.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0, 1, 0],
                  }),
                  transform: [
                    {
                      translateX: connectionFlow.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-50, 50],
                      }),
                    },
                  ],
                },
              ]}
            />

            {/* Country nodes with enhanced styling */}
            <View style={[styles.countryNode, styles.ugandaNode]}>
              <Text style={styles.countryFlag}>🇺🇬</Text>
              <Text style={styles.countryLabel}>UG</Text>
            </View>
            <View style={[styles.countryNode, styles.kenyaNode]}>
              <Text style={styles.countryFlag}>🇰🇪</Text>
              <Text style={styles.countryLabel}>KE</Text>
            </View>
            <View style={[styles.countryNode, styles.tanzaniaNode]}>
              <Text style={styles.countryFlag}>🇹🇿</Text>
              <Text style={styles.countryLabel}>TZ</Text>
            </View>
            <View style={[styles.countryNode, styles.rwandaNode]}>
              <Text style={styles.countryFlag}>🇷🇼</Text>
              <Text style={styles.countryLabel}>RW</Text>
            </View>
          </Animated.View>

          {/* Enhanced Title */}
          <Animated.View
            style={[
              styles.titleContainer,
              { opacity: titleOpacity },
            ]}
          >
            <Text style={[styles.title, { color: textColor }]}>
              JiraniPay
            </Text>
            <Text style={[styles.titleSubtext, { color: accentColor }]}>
              جيراني • Jirani • Voisin
            </Text>
          </Animated.View>

          {/* Enhanced Tagline */}
          <Animated.View
            style={[
              styles.taglineContainer,
              { opacity: taglineOpacity },
            ]}
          >
            <Text style={[styles.tagline, { color: subtitleColor }]}>
              Connecting East Africa
            </Text>
            <Text style={[styles.subtitle, { color: subtitleColor }]}>
              One Transaction at a Time
            </Text>
          </Animated.View>

          {/* Trust Indicators */}
          <View style={styles.trustIndicators}>
            <Animated.View
              style={[
                styles.trustIndicator,
                {
                  opacity: trustIndicator1,
                  transform: [{ scale: trustIndicator1 }],
                },
              ]}
            >
              <MaterialCommunityIcons
                name="shield-check"
                size={20}
                color={Colors.status.success}
              />
              <Text style={[styles.trustText, { color: subtitleColor }]}>Secure</Text>
            </Animated.View>

            <Animated.View
              style={[
                styles.trustIndicator,
                {
                  opacity: trustIndicator2,
                  transform: [{ scale: trustIndicator2 }],
                },
              ]}
            >
              <MaterialCommunityIcons
                name="lightning-bolt"
                size={20}
                color={Colors.accent.gold}
              />
              <Text style={[styles.trustText, { color: subtitleColor }]}>Fast</Text>
            </Animated.View>

            <Animated.View
              style={[
                styles.trustIndicator,
                {
                  opacity: trustIndicator3,
                  transform: [{ scale: trustIndicator3 }],
                },
              ]}
            >
              <FontAwesome5
                name="handshake"
                size={18}
                color={Colors.primary.main}
              />
              <Text style={[styles.trustText, { color: subtitleColor }]}>Trusted</Text>
            </Animated.View>
          </View>
        </View>

        {/* Enhanced Loading Section */}
        <Animated.View
          style={[
            styles.loadingContainer,
            { opacity: loadingOpacity },
          ]}
        >
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressWidth.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Animated.View style={{ opacity: messageOpacity }}>
            <Text style={[styles.loadingText, { color: subtitleColor }]}>
              {trustMessages[currentMessage]}
            </Text>
          </Animated.View>
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const defaultStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Default white background
    alignItems: 'center',
    justifyContent: 'center',
  },
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  geometricPattern: {
    position: 'absolute',
    borderWidth: 1,
    borderColor: Colors.primary.main,
    opacity: 0.1,
  },
  pattern1: {
    top: '15%',
    left: '10%',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  pattern2: {
    top: '25%',
    right: '15%',
    width: 30,
    height: 30,
    transform: [{ rotate: '45deg' }],
  },
  pattern3: {
    bottom: '30%',
    left: '20%',
    width: 35,
    height: 35,
    borderRadius: 8,
  },
  pattern4: {
    bottom: '20%',
    right: '10%',
    width: 25,
    height: 25,
    borderRadius: 12,
  },
  networkPulse: {
    position: 'absolute',
    width: 300,
    height: 300,
    borderRadius: 150,
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  culturalElement: {
    position: 'absolute',
    padding: 12,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  culturalElement1: {
    top: '20%',
    left: '15%',
  },
  culturalElement2: {
    top: '30%',
    right: '20%',
  },
  culturalElement3: {
    bottom: '35%',
    left: '25%',
  },
  culturalSymbol: {
    fontSize: 24,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logoBackground: {
    width: 130,
    height: 130,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.primary.main,
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 15,
  },
  connectionsContainer: {
    width: 240,
    height: 140,
    marginBottom: 30,
    position: 'relative',
  },
  connectionFlow: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: Colors.accent.gold,
    borderRadius: 1.5,
  },
  countryNode: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  ugandaNode: {
    top: 10,
    left: 10,
  },
  kenyaNode: {
    top: 20,
    right: 10,
  },
  tanzaniaNode: {
    bottom: 20,
    left: 40,
  },
  rwandaNode: {
    bottom: 10,
    right: 40,
  },
  countryFlag: {
    fontSize: 20,
  },
  countryLabel: {
    fontSize: 8,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginTop: 2,
  },
  titleContainer: {
    marginBottom: 8,
    alignItems: 'center',
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 2,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
  },
  titleSubtext: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.8,
  },
  taglineContainer: {
    marginBottom: 40,
    alignItems: 'center',
  },
  tagline: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
  },
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background || '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  geometricPattern: {
    position: 'absolute',
    borderWidth: 1,
    borderColor: Colors.primary.main,
    opacity: 0.1,
  },
  pattern1: {
    top: '15%',
    left: '10%',
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  pattern2: {
    top: '25%',
    right: '15%',
    width: 30,
    height: 30,
    transform: [{ rotate: '45deg' }],
  },
  pattern3: {
    bottom: '30%',
    left: '20%',
    width: 35,
    height: 35,
    borderRadius: 8,
  },
  pattern4: {
    bottom: '20%',
    right: '10%',
    width: 25,
    height: 25,
    borderRadius: 12,
  },
  networkPulse: {
    position: 'absolute',
    width: 300,
    height: 300,
    borderRadius: 150,
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  culturalElement: {
    position: 'absolute',
    padding: 12,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  culturalElement1: {
    top: '20%',
    left: '15%',
  },
  culturalElement2: {
    top: '30%',
    right: '20%',
  },
  culturalElement3: {
    bottom: '35%',
    left: '25%',
  },
  culturalSymbol: {
    fontSize: 24,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logoBackground: {
    width: 130,
    height: 130,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.primary.main,
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 15,
  },
  connectionsContainer: {
    width: 240,
    height: 140,
    marginBottom: 30,
    position: 'relative',
  },
  connectionFlow: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    height: 3,
    backgroundColor: Colors.accent.gold,
    borderRadius: 1.5,
  },
  countryNode: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 2,
    borderColor: Colors.primary.main,
  },
  ugandaNode: {
    top: 10,
    left: 10,
  },
  kenyaNode: {
    top: 20,
    right: 10,
  },
  tanzaniaNode: {
    bottom: 20,
    left: 40,
  },
  rwandaNode: {
    bottom: 10,
    right: 40,
  },
  countryFlag: {
    fontSize: 20,
  },
  countryLabel: {
    fontSize: 8,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginTop: 2,
  },
  titleContainer: {
    marginBottom: 8,
    alignItems: 'center',
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 2,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
  },
  titleSubtext: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.8,
  },
  taglineContainer: {
    marginBottom: 40,
    alignItems: 'center',
  },
  tagline: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 6,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
    opacity: 0.9,
  },
  trustIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 40,
  },
  trustIndicator: {
    alignItems: 'center',
    padding: 12,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    minWidth: 70,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  trustText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  progressContainer: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 3,
    marginBottom: 20,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
    backgroundColor: Colors.primary.main,
    shadowColor: Colors.primary.main,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default EnhancedSplashScreen;
