/**
 * Security Alert Service
 * Manages real-time security alerts and notifications for fraud detection,
 * suspicious activities, and security events
 */

import { supabase } from './supabaseClient';
import notificationService from './notificationService';
import { formatCurrency } from '../utils/currencyUtils';

// Alert types and their configurations
const ALERT_TYPES = {
  fraud_detection: {
    title: 'Fraud Alert',
    icon: '🚨',
    priority: 'high',
    channels: ['push', 'email', 'sms']
  },
  suspicious_login: {
    title: 'Suspicious Login',
    icon: '⚠️',
    priority: 'medium',
    channels: ['push', 'email']
  },
  limit_violation: {
    title: 'Transaction Limit',
    icon: '🚫',
    priority: 'medium',
    channels: ['push']
  },
  new_device: {
    title: 'New Device',
    icon: '📱',
    priority: 'low',
    channels: ['push', 'email']
  },
  location_change: {
    title: 'Location Change',
    icon: '📍',
    priority: 'low',
    channels: ['push']
  },
  account_lockout: {
    title: 'Account Locked',
    icon: '🔒',
    priority: 'high',
    channels: ['push', 'email', 'sms']
  },
  velocity_alert: {
    title: 'High Transaction Frequency',
    icon: '⚡',
    priority: 'medium',
    channels: ['push']
  }
};

class SecurityAlertService {
  constructor() {
    this.alertTypes = ALERT_TYPES;
  }

  /**
   * Send security alert to user
   */
  async sendSecurityAlert(userId, alertData) {
    try {
      console.log('🚨 Sending security alert:', { userId, type: alertData.type });

      const alertConfig = this.alertTypes[alertData.type] || this.alertTypes.fraud_detection;
      
      // Create alert record
      const alert = await this.createAlertRecord(userId, alertData, alertConfig);

      // Send notifications through configured channels
      await this.sendNotifications(userId, alert, alertConfig);

      // Log security event
      await this.logSecurityEvent(userId, alertData);

      console.log('✅ Security alert sent successfully');
      return alert;
    } catch (error) {
      console.error('❌ Error sending security alert:', error);
      throw error;
    }
  }

  /**
   * Create alert record in database
   */
  async createAlertRecord(userId, alertData, config) {
    try {
      const alert = {
        id: crypto.randomUUID(),
        userId,
        type: alertData.type,
        title: config.title,
        message: alertData.message,
        severity: alertData.severity || config.priority,
        details: alertData.details || {},
        status: 'sent',
        channels: config.channels,
        createdAt: new Date().toISOString(),
        readAt: null,
        actionTaken: null
      };

      // Store in database (you might want to create a security_alerts table)
      const { error } = await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: 'security_alert',
          severity: alert.severity,
          details: {
            alert_type: alertData.type,
            message: alertData.message,
            alert_details: alertData.details,
            channels_used: config.channels
          },
          created_at: alert.createdAt
        });

      if (error) throw error;

      return alert;
    } catch (error) {
      console.error('❌ Error creating alert record:', error);
      throw error;
    }
  }

  /**
   * Send notifications through multiple channels
   */
  async sendNotifications(userId, alert, config) {
    try {
      const notifications = [];

      // Send push notification
      if (config.channels.includes('push')) {
        try {
          await notificationService.sendPushNotification(userId, {
            title: `${config.icon} ${alert.title}`,
            body: alert.message,
            data: {
              type: 'security_alert',
              alertType: alert.type,
              severity: alert.severity,
              alertId: alert.id
            }
          });
          notifications.push('push');
        } catch (error) {
          console.error('❌ Push notification failed:', error);
        }
      }

      // Send email notification
      if (config.channels.includes('email')) {
        try {
          await this.sendEmailAlert(userId, alert, config);
          notifications.push('email');
        } catch (error) {
          console.error('❌ Email notification failed:', error);
        }
      }

      // Send SMS notification for critical alerts
      if (config.channels.includes('sms') && alert.severity === 'high') {
        try {
          await this.sendSMSAlert(userId, alert);
          notifications.push('sms');
        } catch (error) {
          console.error('❌ SMS notification failed:', error);
        }
      }

      console.log('✅ Notifications sent via:', notifications);
      return notifications;
    } catch (error) {
      console.error('❌ Error sending notifications:', error);
      throw error;
    }
  }

  /**
   * Send email security alert
   */
  async sendEmailAlert(userId, alert, config) {
    try {
      // Get user email
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('email, full_name')
        .eq('user_id', userId)
        .single();

      if (error || !profile.email) {
        console.log('⚠️ No email found for user:', userId);
        return;
      }

      const emailContent = this.generateEmailContent(alert, config, profile.full_name);

      // Send via notification service
      await notificationService.sendEmail({
        to: profile.email,
        subject: `${config.icon} JiraniPay Security Alert: ${alert.title}`,
        html: emailContent,
        priority: alert.severity === 'high' ? 'high' : 'normal'
      });

      console.log('✅ Email alert sent to:', profile.email);
    } catch (error) {
      console.error('❌ Error sending email alert:', error);
      throw error;
    }
  }

  /**
   * Send SMS security alert
   */
  async sendSMSAlert(userId, alert) {
    try {
      // Get user phone
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('phone_number, full_name')
        .eq('user_id', userId)
        .single();

      if (error || !profile.phone_number) {
        console.log('⚠️ No phone found for user:', userId);
        return;
      }

      const smsMessage = `JiraniPay Security Alert: ${alert.message}. If this wasn't you, contact support immediately.`;

      // Send via notification service
      await notificationService.sendSMS({
        to: profile.phone_number,
        message: smsMessage
      });

      console.log('✅ SMS alert sent to:', profile.phone_number);
    } catch (error) {
      console.error('❌ Error sending SMS alert:', error);
      throw error;
    }
  }

  /**
   * Generate email content for security alerts
   */
  generateEmailContent(alert, config, userName) {
    const timestamp = new Date(alert.createdAt).toLocaleString('en-UG', {
      timeZone: 'Africa/Kampala',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>JiraniPay Security Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc3545; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .alert-box { background: white; border-left: 4px solid #dc3545; padding: 15px; margin: 15px 0; }
          .details { background: white; padding: 15px; margin: 15px 0; border-radius: 4px; }
          .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
          .button { display: inline-block; background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${config.icon} Security Alert</h1>
            <p>JiraniPay Account Security Notification</p>
          </div>
          
          <div class="content">
            <p>Hello ${userName || 'Valued Customer'},</p>
            
            <div class="alert-box">
              <h3>${alert.title}</h3>
              <p><strong>Alert:</strong> ${alert.message}</p>
              <p><strong>Time:</strong> ${timestamp}</p>
              <p><strong>Severity:</strong> ${alert.severity.toUpperCase()}</p>
            </div>
            
            ${alert.details && Object.keys(alert.details).length > 0 ? `
            <div class="details">
              <h4>Additional Details:</h4>
              ${Object.entries(alert.details).map(([key, value]) => 
                `<p><strong>${key.replace(/_/g, ' ').toUpperCase()}:</strong> ${value}</p>`
              ).join('')}
            </div>
            ` : ''}
            
            <h4>What should you do?</h4>
            <ul>
              <li>If this was you, no action is needed</li>
              <li>If this wasn't you, change your password immediately</li>
              <li>Review your recent account activity</li>
              <li>Contact our support team if you need assistance</li>
            </ul>
            
            <div style="text-align: center;">
              <a href="jiranipay://security" class="button">Review Account Security</a>
            </div>
          </div>
          
          <div class="footer">
            <p>This is an automated security notification from JiraniPay.</p>
            <p>If you have questions, contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Log security event
   */
  async logSecurityEvent(userId, alertData) {
    try {
      const { error } = await supabase
        .from('security_events')
        .insert({
          user_id: userId,
          event_type: alertData.type,
          severity: alertData.severity || 'medium',
          details: {
            message: alertData.message,
            alert_details: alertData.details,
            timestamp: new Date().toISOString()
          },
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('❌ Error logging security event:', error);
      // Don't throw - logging failure shouldn't stop alert
    }
  }

  /**
   * Send fraud detection alert
   */
  async sendFraudAlert(userId, fraudAnalysis) {
    return this.sendSecurityAlert(userId, {
      type: 'fraud_detection',
      severity: fraudAnalysis.riskLevel,
      message: `Suspicious transaction detected with ${fraudAnalysis.riskLevel} risk level`,
      details: {
        risk_score: fraudAnalysis.riskScore,
        risk_factors: fraudAnalysis.riskFactors.map(f => f.message).join(', '),
        recommendation: fraudAnalysis.recommendation.message
      }
    });
  }

  /**
   * Send login failure alert
   */
  async sendLoginFailureAlert(userId, failureData) {
    return this.sendSecurityAlert(userId, {
      type: 'suspicious_login',
      severity: failureData.attemptCount > 3 ? 'high' : 'medium',
      message: `${failureData.attemptCount} failed login attempts detected`,
      details: {
        ip_address: failureData.ipAddress,
        user_agent: failureData.userAgent,
        location: failureData.location,
        last_attempt: failureData.lastAttempt
      }
    });
  }

  /**
   * Send transaction limit violation alert
   */
  async sendLimitViolationAlert(userId, limitData) {
    return this.sendSecurityAlert(userId, {
      type: 'limit_violation',
      severity: 'medium',
      message: `Transaction limit exceeded: ${limitData.reason}`,
      details: {
        attempted_amount: formatCurrency(limitData.amount),
        limit_type: limitData.reason,
        current_limit: formatCurrency(limitData.limit),
        verification_level: limitData.verificationLevel
      }
    });
  }

  /**
   * Send new device alert
   */
  async sendNewDeviceAlert(userId, deviceData) {
    return this.sendSecurityAlert(userId, {
      type: 'new_device',
      severity: 'low',
      message: 'New device detected on your account',
      details: {
        device_type: deviceData.deviceType,
        device_name: deviceData.deviceName,
        location: deviceData.location,
        ip_address: deviceData.ipAddress
      }
    });
  }

  /**
   * Send velocity alert
   */
  async sendVelocityAlert(userId, velocityData) {
    return this.sendSecurityAlert(userId, {
      type: 'velocity_alert',
      severity: 'medium',
      message: `${velocityData.transactionCount} transactions in ${velocityData.timeWindow} minutes`,
      details: {
        transaction_count: velocityData.transactionCount,
        time_window: velocityData.timeWindow,
        threshold: velocityData.threshold
      }
    });
  }

  /**
   * Get user's security alerts
   */
  async getUserSecurityAlerts(userId, limit = 50) {
    try {
      const { data: alerts, error } = await supabase
        .from('security_events')
        .select('*')
        .eq('user_id', userId)
        .in('event_type', ['security_alert', 'fraud_alert', 'suspicious_login'])
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return alerts.map(alert => ({
        id: alert.id,
        type: alert.event_type,
        severity: alert.severity,
        message: alert.details?.message || 'Security event detected',
        details: alert.details,
        resolved: alert.resolved,
        createdAt: alert.created_at,
        resolvedAt: alert.resolved_at
      }));
    } catch (error) {
      console.error('❌ Error getting security alerts:', error);
      throw error;
    }
  }

  /**
   * Mark alert as resolved
   */
  async resolveAlert(alertId, userId) {
    try {
      const { error } = await supabase
        .from('security_events')
        .update({
          resolved: true,
          resolved_at: new Date().toISOString(),
          resolved_by: userId
        })
        .eq('id', alertId)
        .eq('user_id', userId);

      if (error) throw error;

      console.log('✅ Alert resolved:', alertId);
    } catch (error) {
      console.error('❌ Error resolving alert:', error);
      throw error;
    }
  }
}

export default new SecurityAlertService();
