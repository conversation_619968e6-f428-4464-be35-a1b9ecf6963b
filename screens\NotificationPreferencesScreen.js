/**
 * Notification Preferences Screen
 * Allows users to customize notification channels, types, timing, and frequency
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Switch,
  Alert,
  RefreshControl
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import notificationPreferencesService from '../services/notificationPreferencesService';
import UnifiedBackButton from '../components/UnifiedBackButton';

const NotificationPreferencesScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [preferences, setPreferences] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      setLoading(true);
      // Get current user ID from auth context
      const userId = 'current-user-id'; // Replace with actual user ID from auth
      const result = await notificationPreferencesService.getUserPreferences(userId);
      setPreferences(result.preferences);
    } catch (error) {
      console.error('❌ Error loading preferences:', error);
      Alert.alert('Error', 'Failed to load notification preferences. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPreferences();
    setRefreshing(false);
  };

  const updatePreference = async (key, value) => {
    try {
      setSaving(true);
      const userId = 'current-user-id'; // Replace with actual user ID
      
      const updates = { [key]: value };
      const result = await notificationPreferencesService.updateUserPreferences(userId, updates);
      
      setPreferences(result.preferences);
    } catch (error) {
      console.error('❌ Error updating preference:', error);
      Alert.alert('Error', 'Failed to update preference. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const updateQuietHours = async (quietHoursData) => {
    try {
      setSaving(true);
      const userId = 'current-user-id'; // Replace with actual user ID
      
      const result = await notificationPreferencesService.updateQuietHours(userId, quietHoursData);
      setPreferences(result.preferences);
    } catch (error) {
      console.error('❌ Error updating quiet hours:', error);
      Alert.alert('Error', 'Failed to update quiet hours. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const renderChannelPreferences = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Notification Channels</Text>
      <Text style={styles.sectionDescription}>
        Choose how you want to receive notifications
      </Text>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="phone-portrait" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Push Notifications</Text>
            <Text style={styles.preferenceDescription}>
              Instant notifications on your device
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.push_enabled || false}
          onValueChange={(value) => updatePreference('push_enabled', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="chatbubble" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>SMS Notifications</Text>
            <Text style={styles.preferenceDescription}>
              Text messages to your phone number
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.sms_enabled || false}
          onValueChange={(value) => updatePreference('sms_enabled', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="mail" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Email Notifications</Text>
            <Text style={styles.preferenceDescription}>
              Detailed notifications to your email
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.email_enabled || false}
          onValueChange={(value) => updatePreference('email_enabled', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>
    </View>
  );

  const renderNotificationTypes = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Notification Types</Text>
      <Text style={styles.sectionDescription}>
        Choose which types of notifications you want to receive
      </Text>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="card" size={24} color={theme.colors.success} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Transaction Notifications</Text>
            <Text style={styles.preferenceDescription}>
              Payment confirmations and money transfers
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.transaction_notifications !== false}
          onValueChange={(value) => updatePreference('transaction_notifications', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="shield-checkmark" size={24} color={theme.colors.warning} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Security Alerts</Text>
            <Text style={styles.preferenceDescription}>
              Important security notifications (Required)
            </Text>
          </View>
        </View>
        <Switch
          value={true}
          onValueChange={() => {}}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={true}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="alert-circle" size={24} color={theme.colors.error} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Fraud Alerts</Text>
            <Text style={styles.preferenceDescription}>
              Suspicious activity notifications (Required)
            </Text>
          </View>
        </View>
        <Switch
          value={true}
          onValueChange={() => {}}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={true}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="warning" size={24} color={theme.colors.warning} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Limit Warnings</Text>
            <Text style={styles.preferenceDescription}>
              Transaction limit notifications
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.limit_warnings !== false}
          onValueChange={(value) => updatePreference('limit_warnings', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="gift" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Promotional Offers</Text>
            <Text style={styles.preferenceDescription}>
              Special offers and product updates
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.promotional_notifications || false}
          onValueChange={(value) => updatePreference('promotional_notifications', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>
    </View>
  );

  const renderQuietHours = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Quiet Hours</Text>
      <Text style={styles.sectionDescription}>
        Reduce notifications during specific hours (except critical alerts)
      </Text>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="moon" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Enable Quiet Hours</Text>
            <Text style={styles.preferenceDescription}>
              Currently: {preferences?.quiet_hours_start} - {preferences?.quiet_hours_end}
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.quiet_hours_enabled || false}
          onValueChange={(value) => updateQuietHours({
            enabled: value,
            startTime: preferences?.quiet_hours_start || '22:00:00',
            endTime: preferences?.quiet_hours_end || '07:00:00'
          })}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      {preferences?.quiet_hours_enabled && (
        <TouchableOpacity 
          style={styles.timeSettingButton}
          onPress={() => navigation.navigate('QuietHoursSettings', { preferences })}
        >
          <Text style={styles.timeSettingText}>Adjust Quiet Hours</Text>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderDigestSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Notification Digest</Text>
      <Text style={styles.sectionDescription}>
        Receive a summary of notifications instead of individual alerts
      </Text>

      <View style={styles.preferenceItem}>
        <View style={styles.preferenceInfo}>
          <Ionicons name="newspaper" size={24} color={theme.colors.primary} />
          <View style={styles.preferenceText}>
            <Text style={styles.preferenceTitle}>Enable Digest</Text>
            <Text style={styles.preferenceDescription}>
              {preferences?.digest_frequency || 'Daily'} summary of notifications
            </Text>
          </View>
        </View>
        <Switch
          value={preferences?.digest_enabled || false}
          onValueChange={(value) => updatePreference('digest_enabled', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary }}
          thumbColor={theme.colors.white}
          disabled={saving}
        />
      </View>

      {preferences?.digest_enabled && (
        <TouchableOpacity 
          style={styles.timeSettingButton}
          onPress={() => navigation.navigate('DigestSettings', { preferences })}
        >
          <Text style={styles.timeSettingText}>Digest Frequency</Text>
          <Text style={styles.timeSettingValue}>
            {preferences?.digest_frequency?.charAt(0).toUpperCase() + 
             preferences?.digest_frequency?.slice(1) || 'Daily'}
          </Text>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );

  const renderActionButtons = () => (
    <View style={styles.actionSection}>
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => navigation.navigate('NotificationHistory')}
      >
        <Ionicons name="time" size={20} color={theme.colors.primary} />
        <Text style={styles.actionButtonText}>Notification History</Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => navigation.navigate('NotificationTest')}
      >
        <Ionicons name="send" size={20} color={theme.colors.primary} />
        <Text style={styles.actionButtonText}>Test Notifications</Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.actionButton, styles.resetButton]}
        onPress={() => {
          Alert.alert(
            'Reset Preferences',
            'Are you sure you want to reset all notification preferences to default?',
            [
              { text: 'Cancel', style: 'cancel' },
              { 
                text: 'Reset', 
                style: 'destructive',
                onPress: async () => {
                  try {
                    const userId = 'current-user-id';
                    const result = await notificationPreferencesService.resetToDefaults(userId);
                    setPreferences(result.preferences);
                    Alert.alert('Success', 'Preferences reset to defaults');
                  } catch (error) {
                    Alert.alert('Error', 'Failed to reset preferences');
                  }
                }
              }
            ]
          );
        }}
      >
        <Ionicons name="refresh" size={20} color={theme.colors.error} />
        <Text style={[styles.actionButtonText, { color: theme.colors.error }]}>
          Reset to Defaults
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading preferences...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Notification Preferences</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderChannelPreferences()}
        {renderNotificationTypes()}
        {renderQuietHours()}
        {renderDigestSettings()}
        {renderActionButtons()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    lineHeight: 20,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  preferenceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  preferenceText: {
    marginLeft: 12,
    flex: 1,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  preferenceDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  timeSettingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  timeSettingText: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  timeSettingValue: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  actionSection: {
    marginTop: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionButtonText: {
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
    flex: 1,
  },
  resetButton: {
    borderWidth: 1,
    borderColor: theme.colors.error,
    backgroundColor: 'transparent',
  },
});

export default NotificationPreferencesScreen;
