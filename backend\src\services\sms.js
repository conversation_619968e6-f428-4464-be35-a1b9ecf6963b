/**
 * SMS Service
 * Handles SMS sending via Africa's Talking API
 */

const AfricasTalking = require('africastalking');
const config = require('../config/config');
const logger = require('../utils/logger');

class SMSService {
  constructor() {
    this.isInitialized = false;
    this.client = null;
  }

  /**
   * Initialize SMS service
   */
  initialize() {
    try {
      if (!config.sms.africastalking.username || !config.sms.africastalking.apiKey) {
        logger.warn('SMS service not configured - SMS functionality will be disabled');
        return;
      }

      this.client = AfricasTalking({
        apiKey: config.sms.africastalking.apiKey,
        username: config.sms.africastalking.username
      });

      this.sms = this.client.SMS;
      this.isInitialized = true;
      
      logger.info('SMS service initialized successfully');
    } catch (error) {
      logger.error('SMS service initialization failed:', error);
    }
  }

  /**
   * Send OTP SMS
   */
  async sendOTP(phoneNumber, otp, type = 'verification') {
    if (!this.isInitialized) {
      logger.warn('SMS service not initialized - OTP not sent');
      return { success: false, reason: 'SMS service not available' };
    }

    try {
      let message;

      switch (type) {
        case 'password_reset':
          message = `Your JiraniPay password reset code is: ${otp}. This code expires in 10 minutes. Do not share this code with anyone.`;
          break;
        case 'verification':
        default:
          message = `Your JiraniPay verification code is: ${otp}. This code expires in 5 minutes. Do not share this code with anyone.`;
          break;
      }
      
      const result = await this.sms.send({
        to: phoneNumber,
        message: message,
        from: config.sms.africastalking.senderId
      });

      if (result.SMSMessageData.Recipients[0].status === 'Success') {
        logger.info('OTP SMS sent successfully', { phoneNumber, messageId: result.SMSMessageData.Recipients[0].messageId });
        return { success: true, messageId: result.SMSMessageData.Recipients[0].messageId };
      } else {
        logger.error('Failed to send OTP SMS', { phoneNumber, status: result.SMSMessageData.Recipients[0].status });
        return { success: false, reason: result.SMSMessageData.Recipients[0].status };
      }
    } catch (error) {
      logger.error('SMS sending failed:', error);
      return { success: false, reason: error.message };
    }
  }

  /**
   * Send transaction notification SMS
   */
  async sendTransactionNotification(phoneNumber, transactionData) {
    if (!this.isInitialized) {
      return { success: false, reason: 'SMS service not available' };
    }

    try {
      const { type, amount, currency, reference } = transactionData;
      const message = `JiraniPay: Your ${type} of ${currency} ${amount.toLocaleString()} has been processed. Ref: ${reference}`;
      
      const result = await this.sms.send({
        to: phoneNumber,
        message: message,
        from: config.sms.africastalking.senderId
      });

      if (result.SMSMessageData.Recipients[0].status === 'Success') {
        logger.info('Transaction SMS sent successfully', { phoneNumber, type, amount });
        return { success: true, messageId: result.SMSMessageData.Recipients[0].messageId };
      } else {
        logger.error('Failed to send transaction SMS', { phoneNumber, status: result.SMSMessageData.Recipients[0].status });
        return { success: false, reason: result.SMSMessageData.Recipients[0].status };
      }
    } catch (error) {
      logger.error('Transaction SMS sending failed:', error);
      return { success: false, reason: error.message };
    }
  }
}

// Create singleton instance
const smsService = new SMSService();
smsService.initialize();

module.exports = smsService;
