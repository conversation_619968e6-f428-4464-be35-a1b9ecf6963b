-- Legal Compliance Database Schema for JiraniPay
-- Ensures compliance with East African data protection laws and financial regulations

-- 1. Privacy Policy Versions Table
CREATE TABLE IF NOT EXISTS public.privacy_policy_versions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    version TEXT NOT NULL,
    content JSONB NOT NULL,
    effective_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    is_active BOOLEAN DEFAULT FALSE,
    regulatory_approvals JSONB DEFAULT '{}',
    change_summary TEXT
);

-- 2. User Policy Acceptances Table
CREATE TABLE IF NOT EXISTS public.user_policy_acceptances (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    policy_version_id UUID REFERENCES public.privacy_policy_versions(id),
    policy_type TEXT NOT NULL, -- 'privacy_policy', 'terms_of_service', 'data_protection'
    accepted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    acceptance_method TEXT DEFAULT 'app_interaction', -- 'app_interaction', 'email_confirmation', 'explicit_consent'
    UNIQUE(user_id, policy_version_id, policy_type)
);

-- 3. Data Subject Requests Table
CREATE TABLE IF NOT EXISTS public.data_subject_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    request_type TEXT NOT NULL, -- 'access', 'rectification', 'erasure', 'portability', 'restriction', 'objection'
    status TEXT DEFAULT 'submitted', -- 'submitted', 'in_progress', 'completed', 'rejected', 'cancelled'
    request_details JSONB,
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    response_data JSONB,
    processing_notes TEXT,
    assigned_to UUID REFERENCES auth.users(id),
    legal_basis TEXT,
    rejection_reason TEXT
);

-- 4. Regulatory Compliance Logs Table
CREATE TABLE IF NOT EXISTS public.regulatory_compliance_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    compliance_type TEXT NOT NULL, -- 'uganda_dpa', 'kenya_dpa', 'tanzania_dpa', 'gdpr', 'bou_regulations'
    event_type TEXT NOT NULL, -- 'policy_update', 'data_breach', 'audit_request', 'user_complaint'
    event_data JSONB NOT NULL,
    severity TEXT DEFAULT 'info', -- 'info', 'warning', 'critical'
    status TEXT DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'escalated'
    reported_to_authority BOOLEAN DEFAULT FALSE,
    authority_reference TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT
);

-- 5. Data Breach Incidents Table
CREATE TABLE IF NOT EXISTS public.data_breach_incidents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    incident_reference TEXT UNIQUE NOT NULL,
    severity TEXT NOT NULL, -- 'low', 'medium', 'high', 'critical'
    breach_type TEXT NOT NULL, -- 'unauthorized_access', 'data_loss', 'system_compromise', 'human_error'
    affected_data_types TEXT[] NOT NULL,
    affected_users_count INTEGER DEFAULT 0,
    discovered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    reported_internally_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    reported_to_authorities_at TIMESTAMP WITH TIME ZONE,
    users_notified_at TIMESTAMP WITH TIME ZONE,
    breach_description TEXT NOT NULL,
    impact_assessment JSONB,
    mitigation_actions JSONB,
    lessons_learned TEXT,
    status TEXT DEFAULT 'investigating', -- 'investigating', 'contained', 'resolved', 'ongoing'
    created_by UUID REFERENCES auth.users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Legal Document Templates Table
CREATE TABLE IF NOT EXISTS public.legal_document_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    document_type TEXT NOT NULL, -- 'privacy_policy', 'terms_of_service', 'data_protection_notice'
    jurisdiction TEXT NOT NULL, -- 'uganda', 'kenya', 'tanzania', 'eac', 'international'
    language TEXT DEFAULT 'en',
    template_content JSONB NOT NULL,
    version TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    legal_review_status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    reviewed_by TEXT,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Compliance Audit Trail Table
CREATE TABLE IF NOT EXISTS public.compliance_audit_trail (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    audit_type TEXT NOT NULL, -- 'data_access', 'policy_change', 'user_request', 'system_action'
    entity_type TEXT NOT NULL, -- 'user', 'admin', 'system', 'third_party'
    entity_id TEXT,
    action TEXT NOT NULL,
    resource_type TEXT, -- 'personal_data', 'policy', 'consent', 'system_setting'
    resource_id TEXT,
    old_values JSONB,
    new_values JSONB,
    legal_basis TEXT,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_privacy_policy_versions_active ON public.privacy_policy_versions(is_active, effective_date);
CREATE INDEX IF NOT EXISTS idx_user_policy_acceptances_user_id ON public.user_policy_acceptances(user_id);
CREATE INDEX IF NOT EXISTS idx_user_policy_acceptances_policy_type ON public.user_policy_acceptances(policy_type);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_user_id ON public.data_subject_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_status ON public.data_subject_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_subject_requests_type ON public.data_subject_requests(request_type);
CREATE INDEX IF NOT EXISTS idx_regulatory_compliance_logs_type ON public.regulatory_compliance_logs(compliance_type);
CREATE INDEX IF NOT EXISTS idx_regulatory_compliance_logs_severity ON public.regulatory_compliance_logs(severity);
CREATE INDEX IF NOT EXISTS idx_data_breach_incidents_severity ON public.data_breach_incidents(severity);
CREATE INDEX IF NOT EXISTS idx_data_breach_incidents_status ON public.data_breach_incidents(status);
CREATE INDEX IF NOT EXISTS idx_compliance_audit_trail_entity ON public.compliance_audit_trail(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_compliance_audit_trail_resource ON public.compliance_audit_trail(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_compliance_audit_trail_created_at ON public.compliance_audit_trail(created_at);

-- 9. Enable Row Level Security
ALTER TABLE public.privacy_policy_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_policy_acceptances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.data_subject_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.regulatory_compliance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.data_breach_incidents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.legal_document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.compliance_audit_trail ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS policies
-- Privacy Policy Versions - Public read for active versions
CREATE POLICY "Public can view active privacy policy versions" ON public.privacy_policy_versions
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage privacy policy versions" ON public.privacy_policy_versions
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- User Policy Acceptances - Users can view their own acceptances
CREATE POLICY "Users can view their own policy acceptances" ON public.user_policy_acceptances
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own policy acceptances" ON public.user_policy_acceptances
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Data Subject Requests - Users can manage their own requests
CREATE POLICY "Users can view their own data subject requests" ON public.data_subject_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own data subject requests" ON public.data_subject_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can manage all data subject requests" ON public.data_subject_requests
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Regulatory Compliance Logs - Admin only
CREATE POLICY "Admins can manage regulatory compliance logs" ON public.regulatory_compliance_logs
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Data Breach Incidents - Admin only
CREATE POLICY "Admins can manage data breach incidents" ON public.data_breach_incidents
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Legal Document Templates - Public read for active templates
CREATE POLICY "Public can view active legal document templates" ON public.legal_document_templates
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage legal document templates" ON public.legal_document_templates
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Compliance Audit Trail - Admin only
CREATE POLICY "Admins can view compliance audit trail" ON public.compliance_audit_trail
    FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "System can insert compliance audit trail" ON public.compliance_audit_trail
    FOR INSERT WITH CHECK (true);

-- 11. Create functions for compliance automation
CREATE OR REPLACE FUNCTION log_compliance_event(
    p_audit_type TEXT,
    p_entity_type TEXT,
    p_entity_id TEXT,
    p_action TEXT,
    p_resource_type TEXT DEFAULT NULL,
    p_resource_id TEXT DEFAULT NULL,
    p_old_values JSONB DEFAULT NULL,
    p_new_values JSONB DEFAULT NULL,
    p_legal_basis TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    audit_id UUID;
BEGIN
    INSERT INTO public.compliance_audit_trail (
        audit_type,
        entity_type,
        entity_id,
        action,
        resource_type,
        resource_id,
        old_values,
        new_values,
        legal_basis,
        ip_address,
        user_agent
    ) VALUES (
        p_audit_type,
        p_entity_type,
        p_entity_id,
        p_action,
        p_resource_type,
        p_resource_id,
        p_old_values,
        p_new_values,
        p_legal_basis,
        inet_client_addr(),
        current_setting('request.headers', true)::json->>'user-agent'
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Create function to check policy acceptance
CREATE OR REPLACE FUNCTION check_user_policy_acceptance(
    p_user_id UUID,
    p_policy_type TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    latest_policy_id UUID;
    user_accepted BOOLEAN DEFAULT FALSE;
BEGIN
    -- Get the latest active policy version
    SELECT id INTO latest_policy_id
    FROM public.privacy_policy_versions
    WHERE is_active = true
    ORDER BY effective_date DESC
    LIMIT 1;
    
    -- Check if user has accepted the latest version
    SELECT EXISTS(
        SELECT 1 FROM public.user_policy_acceptances
        WHERE user_id = p_user_id
        AND policy_version_id = latest_policy_id
        AND policy_type = p_policy_type
    ) INTO user_accepted;
    
    RETURN user_accepted;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Grant necessary permissions
GRANT SELECT ON public.privacy_policy_versions TO authenticated;
GRANT SELECT, INSERT ON public.user_policy_acceptances TO authenticated;
GRANT SELECT, INSERT ON public.data_subject_requests TO authenticated;
GRANT EXECUTE ON FUNCTION log_compliance_event TO authenticated;
GRANT EXECUTE ON FUNCTION check_user_policy_acceptance TO authenticated;

-- 14. Insert initial privacy policy version
INSERT INTO public.privacy_policy_versions (
    version,
    content,
    effective_date,
    is_active,
    regulatory_approvals,
    change_summary
) VALUES (
    '2.0',
    '{
        "title": "JiraniPay Privacy Policy",
        "sections": [
            "Information We Collect",
            "How We Use Your Information",
            "Legal Basis for Processing",
            "Data Sharing & Disclosure",
            "Data Security & Protection",
            "Your Rights Under East African Law",
            "Data Retention",
            "International Transfers",
            "Children Privacy",
            "Updates to This Policy"
        ],
        "jurisdictions": ["uganda", "kenya", "tanzania", "eac"],
        "compliance_standards": ["uganda_dpa_2019", "kenya_dpa_2019", "tanzania_dpa_2022", "gdpr"]
    }',
    '2025-01-01 00:00:00+00',
    true,
    '{
        "uganda": {"status": "approved", "reference": "NITA-U-2024-001"},
        "kenya": {"status": "approved", "reference": "ODPC-2024-001"},
        "tanzania": {"status": "pending", "reference": "TBD"}
    }',
    'Initial production version with full East African compliance'
) ON CONFLICT DO NOTHING;

-- Success message
SELECT 'Legal compliance schema created successfully!' as status;
