import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { Colors } from '../constants/Colors';

const { width, height } = Dimensions.get('window');

const SplashScreen = ({ onAnimationComplete }) => {
  const { theme, isDarkMode, isInitialized } = useTheme();

  // Animation values
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const taglineOpacity = useRef(new Animated.Value(0)).current;
  const connectionsOpacity = useRef(new Animated.Value(0)).current;
  const loadingOpacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;
  
  // Connection lines animation
  const connectionLine1 = useRef(new Animated.Value(0)).current;
  const connectionLine2 = useRef(new Animated.Value(0)).current;
  const connectionLine3 = useRef(new Animated.Value(0)).current;
  
  // Floating elements
  const floatingElement1 = useRef(new Animated.Value(0)).current;
  const floatingElement2 = useRef(new Animated.Value(0)).current;
  const floatingElement3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animationSequence = Animated.sequence([
      // Phase 1: Logo entrance (0-800ms)
      Animated.parallel([
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
      ]),
      
      // Phase 2: Title and connections (800-1600ms)
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.stagger(200, [
          Animated.timing(connectionLine1, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(connectionLine2, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(connectionLine3, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
        Animated.timing(connectionsOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      
      // Phase 3: Tagline and floating elements (1600-2400ms)
      Animated.parallel([
        Animated.timing(taglineOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.stagger(150, [
          Animated.timing(floatingElement1, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(floatingElement2, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(floatingElement3, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]),
      ]),
      
      // Phase 4: Loading indicator (2400-3200ms)
      Animated.parallel([
        Animated.timing(loadingOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(progressWidth, {
          toValue: 1,
          duration: 800,
          useNativeDriver: false,
        }),
      ]),
      
      // Phase 5: Hold for a moment (3200-3700ms)
      Animated.delay(500),
    ]);

    // Start continuous floating animations
    const startFloatingAnimations = () => {
      const createFloatingAnimation = (animatedValue, duration, delay = 0) => {
        return Animated.loop(
          Animated.sequence([
            Animated.delay(delay),
            Animated.timing(animatedValue, {
              toValue: 1,
              duration: duration,
              useNativeDriver: true,
            }),
            Animated.timing(animatedValue, {
              toValue: 0,
              duration: duration,
              useNativeDriver: true,
            }),
          ])
        );
      };

      Animated.parallel([
        createFloatingAnimation(floatingElement1, 2000, 0),
        createFloatingAnimation(floatingElement2, 2500, 500),
        createFloatingAnimation(floatingElement3, 2200, 1000),
      ]).start();
    };

    animationSequence.start(({ finished }) => {
      if (finished) {
        startFloatingAnimations();
        // Complete the splash screen after total duration
        setTimeout(() => {
          onAnimationComplete && onAnimationComplete();
        }, 1000);
      }
    });
  }, []);

  // Dynamic colors based on theme
  const gradientColors = isDarkMode
    ? ['#1a1a1a', '#2d2d2d', '#1a1a1a']
    : [Colors.neutral.appBackground, '#ffffff', Colors.neutral.appBackground];

  const textColor = isDarkMode ? '#FFFFFF' : Colors.neutral.charcoal;
  const subtitleColor = isDarkMode ? '#B3B3B3' : Colors.neutral.warmGray;
  const accentColor = Colors.primary?.main || '#E67E22';

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      
      <LinearGradient
        colors={gradientColors}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Pattern */}
        <View style={styles.backgroundPattern}>
          {/* Subtle geometric pattern representing connectivity */}
          <View style={[styles.patternDot, { top: '20%', left: '15%' }]} />
          <View style={[styles.patternDot, { top: '30%', right: '20%' }]} />
          <View style={[styles.patternDot, { bottom: '25%', left: '25%' }]} />
          <View style={[styles.patternDot, { bottom: '35%', right: '15%' }]} />
        </View>

        {/* Main Content */}
        <View style={styles.content}>
          {/* Logo Section */}
          <Animated.View
            style={[
              styles.logoContainer,
              {
                transform: [{ scale: logoScale }],
                opacity: logoOpacity,
              },
            ]}
          >
            <View style={styles.logoBackground}>
              <Ionicons
                name="wallet"
                size={60}
                color="#FFFFFF"
              />
            </View>
          </Animated.View>

          {/* Connection Visualization */}
          <Animated.View
            style={[
              styles.connectionsContainer,
              { opacity: connectionsOpacity },
            ]}
          >
            {/* Connection Lines */}
            <Animated.View
              style={[
                styles.connectionLine,
                styles.connectionLine1,
                {
                  opacity: connectionLine1,
                  transform: [
                    {
                      scaleX: connectionLine1,
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.connectionLine,
                styles.connectionLine2,
                {
                  opacity: connectionLine2,
                  transform: [
                    {
                      scaleX: connectionLine2,
                    },
                  ],
                },
              ]}
            />
            <Animated.View
              style={[
                styles.connectionLine,
                styles.connectionLine3,
                {
                  opacity: connectionLine3,
                  transform: [
                    {
                      scaleX: connectionLine3,
                    },
                  ],
                },
              ]}
            />

            {/* Connection Nodes */}
            <View style={[styles.connectionNode, styles.node1]}>
              <Text style={styles.nodeText}>🇺🇬</Text>
            </View>
            <View style={[styles.connectionNode, styles.node2]}>
              <Text style={styles.nodeText}>🇰🇪</Text>
            </View>
            <View style={[styles.connectionNode, styles.node3]}>
              <Text style={styles.nodeText}>🇹🇿</Text>
            </View>
            <View style={[styles.connectionNode, styles.node4]}>
              <Text style={styles.nodeText}>🇷🇼</Text>
            </View>
          </Animated.View>

          {/* Title */}
          <Animated.View
            style={[
              styles.titleContainer,
              { opacity: titleOpacity },
            ]}
          >
            <Text style={[styles.title, { color: textColor }]}>
              JiraniPay
            </Text>
          </Animated.View>

          {/* Tagline */}
          <Animated.View
            style={[
              styles.taglineContainer,
              { opacity: taglineOpacity },
            ]}
          >
            <Text style={[styles.tagline, { color: subtitleColor }]}>
              Connecting East Africa
            </Text>
            <Text style={[styles.subtitle, { color: subtitleColor }]}>
              One Transaction at a Time
            </Text>
          </Animated.View>

          {/* Floating Trust Elements */}
          <Animated.View
            style={[
              styles.floatingElement,
              styles.floatingElement1,
              {
                opacity: floatingElement1,
                transform: [
                  {
                    translateY: floatingElement1.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -10],
                    }),
                  },
                ],
              },
            ]}
          >
            <MaterialCommunityIcons
              name="shield-check"
              size={24}
              color={accentColor}
            />
          </Animated.View>

          <Animated.View
            style={[
              styles.floatingElement,
              styles.floatingElement2,
              {
                opacity: floatingElement2,
                transform: [
                  {
                    translateY: floatingElement2.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -8],
                    }),
                  },
                ],
              },
            ]}
          >
            <MaterialCommunityIcons
              name="lightning-bolt"
              size={20}
              color={Colors.accent.gold}
            />
          </Animated.View>

          <Animated.View
            style={[
              styles.floatingElement,
              styles.floatingElement3,
              {
                opacity: floatingElement3,
                transform: [
                  {
                    translateY: floatingElement3.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -12],
                    }),
                  },
                ],
              },
            ]}
          >
            <MaterialCommunityIcons
              name="account-group"
              size={22}
              color={Colors.secondary.main}
            />
          </Animated.View>
        </View>

        {/* Loading Section */}
        <Animated.View
          style={[
            styles.loadingContainer,
            { opacity: loadingOpacity },
          ]}
        >
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressWidth.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={[styles.loadingText, { color: subtitleColor }]}>
            Securing your financial future...
          </Text>
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  patternDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.primary?.main || '#E67E22',
    opacity: 0.3,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 40,
  },
  logoBackground: {
    width: 120,
    height: 120,
    borderRadius: 30,
    backgroundColor: Colors.primary?.main || '#E67E22',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.primary?.main || '#E67E22',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  connectionsContainer: {
    width: 200,
    height: 120,
    marginBottom: 30,
    position: 'relative',
  },
  connectionLine: {
    position: 'absolute',
    height: 2,
    backgroundColor: Colors.primary.main,
    opacity: 0.6,
  },
  connectionLine1: {
    top: 30,
    left: 20,
    width: 60,
    transform: [{ rotate: '15deg' }],
  },
  connectionLine2: {
    top: 60,
    left: 70,
    width: 80,
    transform: [{ rotate: '-10deg' }],
  },
  connectionLine3: {
    top: 45,
    left: 40,
    width: 70,
    transform: [{ rotate: '25deg' }],
  },
  connectionNode: {
    position: 'absolute',
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  node1: {
    top: 10,
    left: 10,
  },
  node2: {
    top: 20,
    right: 10,
  },
  node3: {
    bottom: 20,
    left: 30,
  },
  node4: {
    bottom: 10,
    right: 30,
  },
  nodeText: {
    fontSize: 16,
  },
  titleContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 1,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
  },
  taglineContainer: {
    marginBottom: 60,
    alignItems: 'center',
  },
  tagline: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  floatingElement: {
    position: 'absolute',
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  floatingElement1: {
    top: '25%',
    right: '15%',
  },
  floatingElement2: {
    top: '35%',
    left: '10%',
  },
  floatingElement3: {
    top: '45%',
    right: '20%',
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 80,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.primary.main,
    borderRadius: 2,
  },
  loadingText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default SplashScreen;
