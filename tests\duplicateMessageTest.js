/**
 * Test for Duplicate Message Prevention
 * Tests the fix for the "Encountered two children with the same key" error
 */

import aiChatService from '../services/aiChatService';

const testDuplicateMessagePrevention = async () => {
  console.log('🧪 Testing Duplicate Message Prevention...');
  
  try {
    const testUserId = 'test_user_123';
    
    // Initialize chat
    console.log('1. Initializing chat...');
    const initResult = await aiChatService.initializeChat(testUserId);
    console.log('✅ Chat initialized:', initResult.success);
    
    // Send first message
    console.log('2. Sending first message...');
    const firstResult = await aiChatService.sendMessage(testUserId, 'Hello, how are you?');
    console.log('✅ First message sent:', firstResult.success);
    console.log('   User message ID:', firstResult.userMessage?.id);
    console.log('   AI message ID:', firstResult.aiMessage?.id);
    
    // Try to send the same message again (should be prevented)
    console.log('3. Attempting to send duplicate message...');
    const duplicateResult = await aiChatService.sendMessage(testUserId, 'Hello, how are you?');
    console.log('✅ Duplicate prevention:', !duplicateResult.success);
    
    // Send a different message
    console.log('4. Sending different message...');
    const secondResult = await aiChatService.sendMessage(testUserId, 'What can you help me with?');
    console.log('✅ Second message sent:', secondResult.success);
    console.log('   User message ID:', secondResult.userMessage?.id);
    console.log('   AI message ID:', secondResult.aiMessage?.id);
    
    // Check chat history for unique IDs
    console.log('5. Checking chat history for unique IDs...');
    const chatHistory = aiChatService.getChatHistory();
    const messageIds = chatHistory.map(msg => msg.id);
    const uniqueIds = [...new Set(messageIds)];
    
    console.log('   Total messages:', chatHistory.length);
    console.log('   Unique IDs:', uniqueIds.length);
    console.log('✅ All IDs are unique:', messageIds.length === uniqueIds.length);
    
    // Verify message structure
    console.log('6. Verifying message structure...');
    chatHistory.forEach((msg, index) => {
      console.log(`   Message ${index + 1}:`, {
        id: msg.id,
        sender: msg.sender,
        text: msg.text.substring(0, 30) + '...',
        hasTimestamp: !!msg.timestamp
      });
    });
    
    // Clear chat for cleanup
    await aiChatService.clearChatHistory(testUserId);
    console.log('✅ Chat history cleared');
    
    console.log('\n🎉 Duplicate Message Prevention Test PASSED!');
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
};

// Test unique ID generation
const testUniqueIdGeneration = () => {
  console.log('\n🧪 Testing Unique ID Generation...');
  
  const ids = [];
  const baseTimestamp = Date.now();
  
  // Generate 100 IDs rapidly
  for (let i = 0; i < 100; i++) {
    const userMessageId = `msg_user_${baseTimestamp + i}_${Math.random().toString(36).substr(2, 9)}`;
    const aiMessageId = `msg_ai_${baseTimestamp + i + 1}_${Math.random().toString(36).substr(2, 9)}`;
    ids.push(userMessageId, aiMessageId);
  }
  
  const uniqueIds = [...new Set(ids)];
  console.log('   Generated IDs:', ids.length);
  console.log('   Unique IDs:', uniqueIds.length);
  console.log('✅ All generated IDs are unique:', ids.length === uniqueIds.length);
  
  // Show sample IDs
  console.log('   Sample IDs:');
  ids.slice(0, 6).forEach((id, index) => {
    console.log(`     ${index + 1}. ${id}`);
  });
  
  return ids.length === uniqueIds.length;
};

// Run tests
const runDuplicateMessageTests = async () => {
  console.log('🚀 Starting Duplicate Message Prevention Tests\n');
  
  const test1 = await testDuplicateMessagePrevention();
  const test2 = testUniqueIdGeneration();
  
  console.log('\n📊 Test Results:');
  console.log('   Duplicate Prevention:', test1 ? '✅ PASSED' : '❌ FAILED');
  console.log('   Unique ID Generation:', test2 ? '✅ PASSED' : '❌ FAILED');
  
  if (test1 && test2) {
    console.log('\n🎉 All tests PASSED! The duplicate message issue should be resolved.');
  } else {
    console.log('\n❌ Some tests FAILED. Please check the implementation.');
  }
  
  return test1 && test2;
};

export default runDuplicateMessageTests;

// For direct execution
if (require.main === module) {
  runDuplicateMessageTests();
}
