#!/usr/bin/env node

/**
 * Environment Debug Script for JiraniPay
 * 
 * This script helps debug environment configuration issues
 * and provides detailed information about the current setup.
 */

console.log('🔍 JiraniPay Environment Debug Tool');
console.log('=====================================\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log('- NODE_ENV:', process.env.NODE_ENV || 'NOT SET');
console.log('- EXPO_PUBLIC_ENVIRONMENT:', process.env.EXPO_PUBLIC_ENVIRONMENT || 'NOT SET');
console.log('- EXPO_PUBLIC_SUPABASE_URL:', process.env.EXPO_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_SUPABASE_ANON_KEY:', process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_PROD_SUPABASE_URL:', process.env.EXPO_PUBLIC_PROD_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY:', process.env.EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');

console.log('\n📁 Environment Files Check:');
const fs = require('fs');

const envFiles = [
  '.env',
  '.env.local',
  '.env.development',
  '.env.development.local',
  '.env.production.local',
  '.env.staging.local'
];

envFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`- ${file}: ${exists ? '✅ EXISTS' : '❌ NOT FOUND'}`);
});

console.log('\n🔧 Secure Configuration Test:');
try {
  // Import the secure configuration
  const secureConfig = require('./config/secureEnvironment.js').default;
  
  console.log('✅ Secure configuration loaded successfully');
  console.log('- Environment:', secureConfig.environment);
  console.log('- Is Production:', secureConfig.isProduction());
  console.log('- Is Development:', secureConfig.isDevelopment());
  console.log('- Supabase URL:', secureConfig.supabase.url ? 'SET' : 'NOT SET');
  console.log('- Supabase Key:', secureConfig.supabase.anonKey ? 'SET' : 'NOT SET');
  console.log('- API Base URL:', secureConfig.api.baseUrl);
  
  // Test validation
  const errors = secureConfig.validate();
  if (errors.length > 0) {
    console.log('\n⚠️ Configuration Validation Errors:');
    errors.forEach(error => console.log(`  - ${error}`));
  } else {
    console.log('\n✅ Configuration validation passed');
  }
  
} catch (error) {
  console.log('❌ Failed to load secure configuration:', error.message);
}

console.log('\n🗄️ Supabase Connection Test:');
try {
  // Test Supabase client
  const supabaseClient = require('./services/supabaseClient.js').default;
  console.log('✅ Supabase client loaded successfully');
  
  // Test a simple query (non-blocking)
  supabaseClient.from('user_profiles').select('count').limit(1)
    .then(({ data, error }) => {
      if (error && error.code !== 'PGRST116') {
        console.log('⚠️ Supabase connection test failed:', error.message);
      } else {
        console.log('✅ Supabase connection test successful');
      }
    })
    .catch(error => {
      console.log('⚠️ Supabase connection test error:', error.message);
    });
    
} catch (error) {
  console.log('❌ Failed to load Supabase client:', error.message);
}

console.log('\n🎯 Recommendations:');
console.log('1. Ensure .env.production.local exists with correct credentials');
console.log('2. Set EXPO_PUBLIC_ENVIRONMENT=production to force production mode');
console.log('3. Verify Supabase credentials are correct');
console.log('4. Check that user profile data exists in the database');
console.log('5. Run: npm run validate-security to check for security issues');

console.log('\n🚀 Quick Fixes:');
console.log('- To force production mode: EXPO_PUBLIC_ENVIRONMENT=production npm start');
console.log('- To use development mode: EXPO_PUBLIC_ENVIRONMENT=development npm start');
console.log('- To validate config: npm run validate-config');
console.log('- To setup environment: npm run setup-env');

console.log('\n✨ Debug complete!');
