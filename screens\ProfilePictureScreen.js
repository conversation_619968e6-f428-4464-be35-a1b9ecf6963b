import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Modal,
} from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';

const ProfilePictureScreen = ({ navigation, route }) => {

  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [showCamera, setShowCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [user, setUser] = useState(null);

  const cameraRef = useRef(null);
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [permission, requestPermission] = useCameraPermissions();

  useEffect(() => {
    loadUserData();
    requestPermissions();
  }, []);

  useEffect(() => {
    if (permission) {
      setHasPermission(permission.granted);
    }
  }, [permission]);

  const loadUserData = async () => {
    const currentUser = authService.getCurrentUser();
    if (!currentUser) {
      Alert.alert('Error', 'Please log in to continue');
      navigation.goBack();
      return;
    }
    
    // Handle both direct user object and wrapped response
    const userData = currentUser.user || currentUser;
    setUser(userData);
  };

  const requestPermissions = async () => {
    try {
      let cameraPermission = permission;
      if (!cameraPermission?.granted) {
        cameraPermission = await requestPermission();
      }

      const galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      setHasPermission(cameraPermission?.granted || false);

      if (!cameraPermission?.granted || galleryPermission.status !== 'granted') {
        Alert.alert(
          'Permissions Required',
          'Camera and gallery permissions are needed to update your profile picture.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      setHasPermission(false);
    }
  };

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });
        setCapturedImage(photo.uri);
        setShowCamera(false);
      } catch (error) {
        console.error('❌ Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      }
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'], // Updated to use new MediaType array format
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio for profile pictures
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setCapturedImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('❌ Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image from gallery');
    }
  };

  const uploadProfilePicture = async () => {
    if (!capturedImage) {
      Alert.alert('Error', 'Please capture or select an image');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    setUploading(true);
    try {
      const result = await profileManagementService.uploadProfilePicture(
        user.id,
        capturedImage
      );

      if (result.success) {
        Alert.alert(
          'Success',
          'Profile picture updated successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to upload profile picture');
      }
    } catch (error) {
      console.error('❌ Error uploading profile picture:', error);
      Alert.alert('Error', 'Failed to upload profile picture. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const renderCameraView = () => {
    if (hasPermission === null) {
      return (
        <View style={styles.permissionContainer}>
          <ActivityIndicator size="large" color={Colors.primary.main} />
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }

    if (hasPermission === false) {
      return (
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-outline" size={64} color={Colors.neutral.warmGray} />
          <Text style={styles.permissionText}>Camera permission denied</Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={requestPermissions}
          >
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <Modal visible={showCamera} animationType="slide">
        <View style={styles.cameraContainer}>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing="front"
          >
            <View style={styles.cameraOverlay}>
              <View style={styles.cameraHeader}>
                <TouchableOpacity
                  style={styles.cameraCloseButton}
                  onPress={() => setShowCamera(false)}
                >
                  <Ionicons name="close" size={24} color={Colors.neutral.white} />
                </TouchableOpacity>
                <Text style={styles.cameraTitle}>Take Profile Picture</Text>
                <View style={styles.cameraCloseButton} />
              </View>

              <View style={styles.cameraGuide}>
                <View style={styles.profileFrame} />
                <Text style={styles.cameraInstructions}>
                  Position your face within the circle
                </Text>
              </View>

              <View style={styles.cameraControls}>
                <TouchableOpacity
                  style={styles.galleryButton}
                  onPress={() => {
                    setShowCamera(false);
                    pickImageFromGallery();
                  }}
                >
                  <Ionicons name="images-outline" size={24} color={Colors.neutral.white} />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.captureButton}
                  onPress={takePicture}
                >
                  <View style={styles.captureButtonInner} />
                </TouchableOpacity>

                <View style={styles.galleryButton} />
              </View>
            </View>
          </CameraView>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>Update Profile Picture</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        {/* Info */}
        <View style={styles.infoCard}>
          <View style={styles.infoIcon}>
            <Ionicons name="camera-outline" size={32} color={Colors.primary.main} />
          </View>
          <Text style={styles.infoTitle}>Update Your Profile Picture</Text>
          <Text style={styles.infoDescription}>
            Choose a clear photo of yourself for your profile picture
          </Text>
        </View>

        {/* Image Preview */}
        {capturedImage && (
          <View style={styles.previewCard}>
            <Text style={styles.previewTitle}>Preview</Text>
            <Image source={{ uri: capturedImage }} style={styles.previewImage} />
            <TouchableOpacity
              style={styles.retakeButton}
              onPress={() => setCapturedImage(null)}
            >
              <Ionicons name="refresh-outline" size={16} color={Colors.primary.main} />
              <Text style={styles.retakeButtonText}>Choose Different Photo</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {!capturedImage ? (
            <>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setShowCamera(true)}
                activeOpacity={0.7}
              >
                <Ionicons name="camera-outline" size={20} color={Colors.neutral.white} />
                <Text style={styles.actionButtonText}>Take Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={pickImageFromGallery}
                activeOpacity={0.7}
              >
                <Ionicons name="images-outline" size={20} color={Colors.primary.main} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                  Choose from Gallery
                </Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity
              style={[styles.actionButton, uploading && styles.actionButtonDisabled]}
              onPress={uploadProfilePicture}
              disabled={uploading}
              activeOpacity={0.7}
            >
              {uploading ? (
                <ActivityIndicator size="small" color={Colors.neutral.white} />
              ) : (
                <Ionicons name="checkmark-circle-outline" size={20} color={Colors.neutral.white} />
              )}
              <Text style={styles.actionButtonText}>
                {uploading ? 'Uploading...' : 'Update Profile Picture'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {renderCameraView()}
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
  },
  infoCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  infoIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  infoDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  previewCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  previewImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 16,
  },
  retakeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  retakeButtonText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
    marginLeft: 4,
  },
  actionsContainer: {
    marginBottom: 40,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionButtonDisabled: {
    opacity: 0.6,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary.main,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: Colors.primary.main,
  },
  // Camera styles
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  cameraHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  cameraCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    textAlign: 'center',
  },
  cameraGuide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  profileFrame: {
    width: 200,
    height: 200,
    borderWidth: 3,
    borderColor: Colors.neutral.white,
    borderRadius: 100,
    backgroundColor: 'transparent',
  },
  cameraInstructions: {
    fontSize: 16,
    color: Colors.neutral.white,
    textAlign: 'center',
    marginTop: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  cameraControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
    paddingBottom: 40,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary.main,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: 40,
  },
  permissionText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginVertical: 20,
  },
  permissionButton: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  permissionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
});

export default ProfilePictureScreen;
