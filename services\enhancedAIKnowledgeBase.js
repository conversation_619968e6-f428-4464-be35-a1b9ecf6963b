/**
 * Enhanced AI Knowledge Base Service for JiraniPay
 * Provides comprehensive understanding of app features, navigation, and user flows
 */

class EnhancedAIKnowledgeBase {
  constructor() {
    this.appStructure = this.initializeAppStructure();
    this.faqData = this.initializeFAQData();
    this.featureGuides = this.initializeFeatureGuides();
    this.troubleshooting = this.initializeTroubleshooting();
    this.navigationFlows = this.initializeNavigationFlows();
  }

  // =====================================================
  // APP STRUCTURE & NAVIGATION
  // =====================================================

  initializeAppStructure() {
    return {
      mainTabs: {
        home: {
          name: 'Dashboard',
          description: 'Main screen with wallet balance, quick actions, recent transactions, and financial insights',
          features: ['wallet_card', 'quick_actions', 'recent_transactions', 'ai_insights', 'spending_analytics']
        },
        bills: {
          name: 'Bill Payment',
          description: 'Pay utilities, buy airtime/data, and manage recurring payments',
          features: ['electricity_bills', 'water_bills', 'airtime_purchase', 'data_bundles', 'recurring_payments']
        },
        qr: {
          name: 'QR Pay',
          description: 'Scan QR codes for payments or generate QR codes to receive money',
          features: ['qr_scanner', 'qr_generator', 'merchant_payments', 'contact_sharing']
        },
        wallet: {
          name: 'Wallet',
          description: 'Manage wallet balance, view transaction history, and access wallet settings',
          features: ['balance_management', 'transaction_history', 'top_up', 'withdrawal', 'spending_limits']
        },
        profile: {
          name: 'Profile',
          description: 'Account settings, security, KYC verification, and support',
          features: ['account_settings', 'security_settings', 'kyc_verification', 'support_access']
        }
      },

      screens: {
        // Authentication Flow
        'Onboarding': 'App introduction and feature overview',
        'Login': 'User authentication with biometric support',
        'Register': 'New user account creation',
        'CompleteProfile': 'Profile completion after registration',
        'ForgotPassword': 'Password recovery with phone verification',

        // Main App Screens
        'DashboardScreen': 'Main dashboard with wallet and quick actions',
        'WalletScreen': 'Detailed wallet management and transaction history',
        'BillPaymentScreen': 'Comprehensive bill payment system',
        'QRScannerScreen': 'QR code scanning for payments',
        'QRGeneratorScreen': 'Generate QR codes for receiving payments',
        'ProfileScreen': 'User profile and account management',

        // Money Transfer Flow
        'SendMoneyScreen': 'Send money to contacts or phone numbers',
        'TransferAmountScreen': 'Enter transfer amount and details',
        'TransferConfirmationScreen': 'Confirm transfer details',
        'TransferSuccessScreen': 'Transfer completion confirmation',
        'ManualRecipientScreen': 'Add recipient manually',

        // Security & Settings
        'SecuritySettingsScreen': 'Security preferences and 2FA',
        'PrivacyControlsScreen': 'Privacy settings and data control',
        'TwoFactorAuthScreen': 'Two-factor authentication setup',
        'SecurityFAQScreen': 'Security-related frequently asked questions',

        // Support System
        'ContactSupportScreen': 'Multiple support channels and options',
        'AIChatScreen': 'AI assistant chat interface',
        'CreateTicketScreen': 'Create support tickets with attachments',
        'FAQScreen': 'General frequently asked questions',

        // Financial Features
        'AnalyticsScreen': 'Spending analytics and insights',
        'SavingsScreen': 'Savings goals and automatic savings',
        'TopUpScreen': 'Add money to wallet from various sources',
        'TransactionHistoryScreen': 'Detailed transaction records'
      }
    };
  }

  // =====================================================
  // FAQ DATA INTEGRATION
  // =====================================================

  initializeFAQData() {
    return {
      'Money Transfer': [
        {
          question: 'How do I send money to another JiraniPay user?',
          answer: 'To send money: 1) Go to the Send Money section, 2) Enter the recipient\'s phone number or scan their QR code, 3) Enter the amount, 4) Add a note (optional), 5) Confirm with your PIN. The money will be transferred instantly.',
          keywords: ['send money', 'transfer money', 'payment', 'recipient', 'phone number', 'qr code', 'how to send', 'money transfer', 'send cash', 'transfer funds', 'jiranipay user', 'another user']
        },
        {
          question: 'What are the transfer limits?',
          answer: 'Daily limits depend on your verification level: Basic (UGX 500,000), Verified (UGX 2,000,000), Premium (UGX 10,000,000). You can increase limits by completing account verification.',
          keywords: ['limits', 'daily limit', 'transfer limits', 'verification', 'basic', 'verified', 'premium', 'kyc', 'maximum amount', 'minimum amount', 'transaction limits', 'daily maximum']
        },
        {
          question: 'How long do transfers take?',
          answer: 'JiraniPay to JiraniPay transfers are instant. Bank transfers take 1-3 business days. Mobile money transfers are usually instant but may take up to 30 minutes during peak times.',
          keywords: ['transfer time', 'instant', 'bank transfer', 'mobile money', 'duration', 'how long', 'transfer speed', 'processing time', 'instant transfer', 'transfer duration']
        }
      ],

      'Bill Payments': [
        {
          question: 'Which bills can I pay through JiraniPay?',
          answer: 'You can pay: Electricity (UMEME, KPLC, TANESCO), Water bills, Internet/Cable TV, School fees, Insurance premiums, Government services, and Mobile airtime/data for all major networks across East Africa.',
          keywords: ['bills', 'electricity', 'umeme', 'water', 'airtime', 'data', 'school fees', 'insurance', 'what bills', 'which bills', 'bills can i pay', 'pay bills', 'bill types', 'available bills']
        },
        {
          question: 'How do I pay utility bills?',
          answer: 'Go to Bill Payments → Select utility type → Enter your account/meter number → Confirm amount → Pay with wallet balance or linked account. You\'ll receive instant confirmation and receipt.',
          keywords: ['utility bills', 'meter number', 'account number', 'payment process']
        },
        {
          question: 'Can I schedule recurring bill payments?',
          answer: 'Yes! When paying a bill, select "Set as Recurring" and choose frequency (weekly, monthly, quarterly). JiraniPay will automatically pay your bills on the scheduled dates.',
          keywords: ['recurring payments', 'automatic payments', 'schedule', 'weekly', 'monthly']
        }
      ],

      'Wallet Management': [
        {
          question: 'How do I add money to my wallet?',
          answer: 'You can top up via: 1) Bank transfer (instant), 2) Mobile money (MTN, Airtel, etc.), 3) Agent locations, 4) Debit/Credit cards. Go to Wallet → Add Money and select your preferred method.',
          keywords: ['top up', 'add money', 'bank transfer', 'mobile money', 'mtn', 'airtel', 'agent']
        },
        {
          question: 'Is my money safe in JiraniPay wallet?',
          answer: 'Yes! Your funds are secured with bank-level encryption, stored in regulated financial institutions, and protected by insurance. We use multi-factor authentication and real-time fraud monitoring.',
          keywords: ['security', 'safe', 'encryption', 'insurance', 'fraud monitoring', 'protection']
        },
        {
          question: 'Can I withdraw money from my wallet?',
          answer: 'Yes, you can withdraw to: Your linked bank account (free), Mobile money account (small fee), or visit any JiraniPay agent location for cash withdrawal.',
          keywords: ['withdraw', 'cash out', 'bank account', 'mobile money', 'agent', 'fees', 'withdraw money', 'cash withdrawal', 'get money out', 'withdraw funds', 'money out', 'cash from wallet']
        }
      ],

      'QR Code Scanner': [
        {
          question: 'How do I use the QR code scanner?',
          answer: 'Tap the QR scanner icon → Point your camera at any JiraniPay QR code → The app will automatically detect and process the payment or contact information. Perfect for quick payments to merchants!',
          keywords: ['qr scanner', 'camera', 'scan', 'merchants', 'payments', 'automatic detection']
        },
        {
          question: 'What can I do with QR codes?',
          answer: 'QR codes allow you to: Pay merchants instantly, Add contacts quickly, Receive payments (generate your own QR), Access special offers, and Join group payments or savings circles.',
          keywords: ['qr codes', 'merchants', 'contacts', 'receive payments', 'offers', 'group payments']
        },
        {
          question: 'How do I generate my QR code?',
          answer: 'Go to Receive Money → Tap "Generate QR Code" → Set amount (optional) → Share the QR code. Others can scan it to send you money instantly without typing your phone number.',
          keywords: ['generate qr', 'receive money', 'qr code', 'share', 'amount']
        }
      ],

      'Account Security': [
        {
          question: 'How do I verify my account?',
          answer: 'Go to Profile → Account Verification → Upload: Valid ID (National ID, Passport, or Driver\'s License), Proof of address (utility bill or bank statement), and take a selfie. Verification usually takes 24-48 hours.',
          keywords: ['verification', 'kyc', 'id upload', 'national id', 'passport', 'proof of address', 'selfie']
        },
        {
          question: 'What security features does JiraniPay offer?',
          answer: 'We provide: Biometric login (fingerprint/face), Two-factor authentication, Transaction PINs, Real-time fraud monitoring, Account alerts, and the ability to freeze your account instantly if needed.',
          keywords: ['security features', 'biometric', 'fingerprint', 'face id', '2fa', 'pin', 'fraud monitoring', 'freeze account']
        },
        {
          question: 'What should I do if I suspect fraud?',
          answer: 'Immediately: 1) Freeze your account in the app, 2) Contact support via the app or hotline, 3) Change your PIN and password, 4) Report the incident. We have 24/7 fraud monitoring and will investigate promptly.',
          keywords: ['fraud', 'suspicious activity', 'freeze account', 'contact support', 'change pin', 'report incident', 'suspect fraud', 'fraudulent', 'suspicious transaction', 'unauthorized', 'hacked', 'security breach', 'stolen money']
        }
      ]
    };
  }

  // =====================================================
  // FEATURE GUIDES & HOW-TOS
  // =====================================================

  initializeFeatureGuides() {
    return {
      dashboard: {
        title: 'Using the Dashboard',
        description: 'Your dashboard is the central hub of JiraniPay',
        steps: [
          'View your wallet balance (tap the eye icon to show/hide)',
          'Use quick actions for common tasks (Send Money, Pay Bills, QR Pay)',
          'Check recent transactions and spending analytics',
          'Access AI financial insights and recommendations'
        ],
        tips: [
          'Swipe down to refresh your balance and transactions',
          'Tap on any transaction to view detailed information',
          'Use the search function to find specific transactions'
        ]
      },

      send_money: {
        title: 'Sending Money',
        description: 'Transfer money to friends, family, or businesses',
        steps: [
          'Tap "Send Money" from the dashboard or navigation',
          'Enter recipient\'s phone number or scan their QR code',
          'Enter the amount you want to send',
          'Add an optional note or reference',
          'Review details and confirm with your PIN',
          'Money is transferred instantly to JiraniPay users'
        ],
        tips: [
          'Save frequent recipients for faster transfers',
          'Use QR codes for error-free recipient selection',
          'Check your daily limits before large transfers'
        ]
      },

      bill_payments: {
        title: 'Paying Bills',
        description: 'Pay utilities, buy airtime, and manage recurring payments',
        steps: [
          'Go to the Bills tab or tap "Pay Bills" on dashboard',
          'Select the type of bill (Electricity, Water, Airtime, etc.)',
          'Choose your service provider',
          'Enter your account/meter number',
          'Enter the amount or select from available options',
          'Confirm payment with your PIN'
        ],
        tips: [
          'Save bill accounts for quick future payments',
          'Set up recurring payments for regular bills',
          'Use bill inquiry to check outstanding amounts'
        ]
      }
    };
  }

  // =====================================================
  // TROUBLESHOOTING GUIDE
  // =====================================================

  initializeTroubleshooting() {
    return {
      login_issues: {
        problem: 'Cannot log in to the app',
        solutions: [
          'Check your internet connection',
          'Verify your phone number and password',
          'Try using biometric login if enabled',
          'Reset your password using "Forgot Password"',
          'Clear app cache and restart the app',
          'Contact support if issues persist'
        ]
      },

      transaction_failed: {
        problem: 'Transaction failed or pending',
        solutions: [
          'Check your internet connection',
          'Verify recipient details are correct',
          'Ensure sufficient wallet balance',
          'Check if you\'ve reached daily limits',
          'Wait a few minutes and try again',
          'Contact support with transaction reference'
        ]
      },

      app_crashes: {
        problem: 'App keeps crashing or freezing',
        solutions: [
          'Force close and restart the app',
          'Restart your device',
          'Update the app to the latest version',
          'Clear app cache and data',
          'Free up device storage space',
          'Reinstall the app if necessary'
        ]
      },

      biometric_not_working: {
        problem: 'Biometric login not working',
        solutions: [
          'Ensure biometric is enabled in device settings',
          'Re-register your fingerprint/face in device settings',
          'Enable biometric login in JiraniPay security settings',
          'Use PIN login as alternative',
          'Update your device software',
          'Contact support for technical assistance'
        ]
      }
    };
  }

  // =====================================================
  // NAVIGATION FLOWS
  // =====================================================

  initializeNavigationFlows() {
    return {
      send_money_flow: [
        'Dashboard → Send Money',
        'Enter recipient (phone/QR/contacts)',
        'Enter amount and note',
        'Confirm details',
        'Enter PIN',
        'Success confirmation'
      ],

      bill_payment_flow: [
        'Dashboard → Pay Bills (or Bills tab)',
        'Select bill category',
        'Choose service provider',
        'Enter account details',
        'Enter amount',
        'Confirm and pay with PIN'
      ],

      qr_payment_flow: [
        'Dashboard → QR Pay (or QR tab)',
        'Scan merchant QR code',
        'Verify payment details',
        'Confirm amount',
        'Complete payment with PIN'
      ],

      wallet_topup_flow: [
        'Wallet tab → Add Money',
        'Select top-up method',
        'Enter amount',
        'Complete payment via chosen method',
        'Funds added to wallet'
      ],

      support_flow: [
        'Profile → Contact Support',
        'Choose support channel (AI Chat, Phone, Email)',
        'Describe your issue',
        'Get assistance or create ticket'
      ]
    };
  }

  // =====================================================
  // QUERY PROCESSING METHODS
  // =====================================================

  findRelevantFAQ(query) {
    const queryLower = query.toLowerCase();
    const results = [];

    Object.entries(this.faqData).forEach(([category, faqs]) => {
      faqs.forEach(faq => {
        const relevanceScore = this.calculateRelevance(queryLower, faq);
        // Increased threshold for better accuracy
        if (relevanceScore > 0.5) {
          results.push({
            category,
            question: faq.question,
            answer: faq.answer,
            relevance: relevanceScore,
            keywords: faq.keywords
          });
        }
      });
    });

    // Sort by relevance and return top matches
    const sortedResults = results.sort((a, b) => b.relevance - a.relevance);

    // Additional validation: ensure top result is significantly better than others
    if (sortedResults.length > 1) {
      const topScore = sortedResults[0].relevance;
      const secondScore = sortedResults[1].relevance;

      // If top result isn't significantly better, require higher threshold
      if (topScore - secondScore < 0.2 && topScore < 0.7) {
        return sortedResults.filter(result => result.relevance > 0.7);
      }
    }

    return sortedResults;
  }

  calculateRelevance(query, faq) {
    let score = 0;
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(' ').filter(word => word.length > 2);
    const questionLower = faq.question.toLowerCase();
    const answerLower = faq.answer.toLowerCase();

    // Exact phrase matching gets highest score (but not perfect)
    if (questionLower.includes(queryLower)) {
      score += 0.7;
    }

    // Count word matches for more precise scoring
    let wordMatches = 0;
    let keywordMatches = 0;
    let actionWordMatches = 0;

    queryWords.forEach(word => {
      const wordLower = word.toLowerCase();

      // Question word matching
      if (questionLower.includes(wordLower)) {
        wordMatches++;
        // Higher score for key action words
        if (['send', 'transfer', 'pay', 'withdraw', 'deposit', 'scan', 'verify', 'fraud', 'suspect'].includes(wordLower)) {
          actionWordMatches++;
          score += 0.4;
        } else {
          score += 0.2;
        }
      }

      // Answer content matching (lower weight)
      if (answerLower.includes(wordLower)) {
        score += 0.1;
      }

      // Keyword matching with higher weight
      if (faq.keywords && faq.keywords.some(keyword => keyword.toLowerCase().includes(wordLower))) {
        keywordMatches++;
        score += 0.3;
      }
    });

    // Bonus for multiple word coverage
    const wordCoverage = wordMatches / Math.max(queryWords.length, 1);
    if (wordCoverage > 0.5) {
      score += 0.2;
    }

    // Penalty for very short or very generic queries
    if (query.length < 8) {
      score *= 0.6;
    }

    // Penalty for single word queries unless they're very specific
    if (queryWords.length === 1 && !['fraud', 'withdraw', 'limits', 'verification'].includes(queryWords[0])) {
      score *= 0.4;
    }

    // Ensure we don't exceed 1.0 and apply minimum threshold
    return Math.min(score, 1.0);
  }

  getFeatureGuide(feature) {
    return this.featureGuides[feature] || null;
  }

  getTroubleshootingGuide(issue) {
    const issueKey = Object.keys(this.troubleshooting).find(key => 
      issue.toLowerCase().includes(key.replace('_', ' '))
    );
    return this.troubleshooting[issueKey] || null;
  }

  getNavigationFlow(flow) {
    return this.navigationFlows[flow] || null;
  }

  // =====================================================
  // CONTEXT UNDERSTANDING
  // =====================================================

  analyzeUserIntent(query) {
    const queryLower = query.toLowerCase();
    
    // Intent patterns
    const intents = {
      how_to: ['how to', 'how do i', 'how can i', 'steps to', 'guide'],
      problem: ['not working', 'failed', 'error', 'issue', 'problem', 'trouble'],
      information: ['what is', 'what are', 'tell me about', 'explain', 'info'],
      navigation: ['where is', 'how to find', 'locate', 'access', 'go to'],
      limits: ['limit', 'maximum', 'minimum', 'restriction'],
      security: ['safe', 'secure', 'protection', 'fraud', 'hack'],
      fees: ['cost', 'fee', 'charge', 'price', 'free']
    };

    for (const [intent, patterns] of Object.entries(intents)) {
      if (patterns.some(pattern => queryLower.includes(pattern))) {
        return intent;
      }
    }

    return 'general';
  }

  getContextualResponse(query, intent) {
    const relevantFAQs = this.findRelevantFAQ(query);
    
    if (relevantFAQs.length > 0) {
      return {
        type: 'faq_match',
        content: relevantFAQs[0],
        additionalFAQs: relevantFAQs.slice(1, 3)
      };
    }

    // Check for feature guides
    const featureKeywords = {
      'dashboard': ['dashboard', 'home screen', 'main screen'],
      'send_money': ['send money', 'transfer', 'payment'],
      'bill_payments': ['pay bills', 'bills', 'utilities', 'airtime']
    };

    for (const [feature, keywords] of Object.entries(featureKeywords)) {
      if (keywords.some(keyword => query.toLowerCase().includes(keyword))) {
        const guide = this.getFeatureGuide(feature);
        if (guide) {
          return {
            type: 'feature_guide',
            content: guide
          };
        }
      }
    }

    // Check for troubleshooting
    if (intent === 'problem') {
      const troubleshootingGuide = this.getTroubleshootingGuide(query);
      if (troubleshootingGuide) {
        return {
          type: 'troubleshooting',
          content: troubleshootingGuide
        };
      }
    }

    return {
      type: 'general',
      content: null
    };
  }
}

export default new EnhancedAIKnowledgeBase();
