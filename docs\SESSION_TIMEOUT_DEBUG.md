# 🔧 Session Timeout Debug Guide

## **Issue Description**
Session timeout setting only shows 30 minutes as selected, and users cannot change to other timeout values (5, 15, 60, 120 minutes).

## **🔍 Debugging Steps**

### **Step 1: Check Console Logs**
When you tap on different timeout options, check the console for these logs:

```
🔄 Updating session timeout to: [X] minutes
👤 User ID: [user-id]
📊 Update result: [result object]
✅ Session timeout updated successfully
```

### **Step 2: Test Each Timeout Option**
1. Navigate to: Profile → Security Settings → Session Timeout
2. Try tapping each option:
   - **5 minutes** (High security)
   - **15 minutes** (Balanced)
   - **30 minutes** (Standard) ← Currently working
   - **60 minutes** (Extended)
   - **120 minutes** (Low security)

### **Step 3: Visual Feedback Check**
When tapping an option, you should see:
- ✅ **Haptic feedback** (vibration)
- ✅ **"⏳ Updating..." text** appears next to current value
- ✅ **Radio button** changes to selected option
- ✅ **Success alert** appears
- ✅ **Current Setting** updates to new value

## **🔧 Enhanced Features Added**

### **1. Optimistic Updates**
- UI updates immediately when you tap an option
- If database update fails, UI reverts to original value

### **2. Loading States**
- "⏳ Updating..." indicator during database update
- Options are disabled during update to prevent multiple requests
- Visual feedback with opacity changes

### **3. Better Error Handling**
- Detailed console logging for debugging
- User-friendly error messages
- Automatic reversion on failure

### **4. Improved UX**
- Immediate visual feedback
- Prevents multiple simultaneous updates
- Clear success confirmation

## **🐛 Potential Issues & Solutions**

### **Issue 1: Database Connection**
**Symptoms**: Console shows "Update failed" or network errors
**Solution**: Check Supabase connection and RLS policies

### **Issue 2: User Authentication**
**Symptoms**: "No user ID available" error
**Solution**: Ensure user is properly logged in

### **Issue 3: State Management**
**Symptoms**: UI doesn't update or reverts unexpectedly
**Solution**: Check React state updates and component re-renders

### **Issue 4: Database Schema**
**Symptoms**: "Column doesn't exist" errors
**Solution**: Verify `session_timeout_minutes` column exists in `security_settings` table

## **🧪 Testing Checklist**

### **Basic Functionality**
- [ ] Can navigate to Session Timeout screen
- [ ] Current setting displays correctly (30 minutes by default)
- [ ] All 5 timeout options are visible
- [ ] Tapping options provides haptic feedback

### **Update Process**
- [ ] Tapping option shows "⏳ Updating..." indicator
- [ ] Radio button selection changes immediately
- [ ] Success alert appears after update
- [ ] Current setting value updates to new selection

### **Error Handling**
- [ ] Network errors show user-friendly messages
- [ ] Failed updates revert UI to original state
- [ ] Multiple rapid taps don't cause issues

### **Persistence**
- [ ] Selected timeout persists when leaving and returning to screen
- [ ] Settings survive app restart
- [ ] Database stores the correct value

## **📱 Expected Console Output**

### **Successful Update:**
```
👤 Current user: {id: "user-123", ...}
⚙️ Security settings result: {success: true, data: {...}}
⏱️ Setting timeout to: 30
🔄 Updating session timeout to: 60 minutes
👤 User ID: user-123
📊 Update result: {success: true, data: {...}}
✅ Session timeout updated successfully
```

### **Failed Update:**
```
🔄 Updating session timeout to: 60 minutes
👤 User ID: user-123
📊 Update result: {success: false, error: "RLS policy violation"}
❌ Update failed: RLS policy violation
```

## **🔧 Quick Fixes**

### **If Updates Aren't Working:**
1. **Check RLS Policies**: Run the RLS fix script from `fix_rls_policies.sql`
2. **Verify User Login**: Ensure user is authenticated
3. **Check Database**: Verify `security_settings` table exists
4. **Test Network**: Check Supabase connection

### **If UI Isn't Updating:**
1. **Clear App Cache**: Restart the app completely
2. **Check State**: Look for React state update issues in console
3. **Verify Props**: Ensure navigation and user props are passed correctly

## **🎯 Success Criteria**

The session timeout feature is working correctly when:
- ✅ All 5 timeout options are selectable
- ✅ UI updates immediately when tapping options
- ✅ Database stores the selected value
- ✅ Settings persist across app sessions
- ✅ Success/error messages appear appropriately
- ✅ Loading states provide clear feedback

---

**🔍 If issues persist, check the console logs and compare with the expected output above.**
