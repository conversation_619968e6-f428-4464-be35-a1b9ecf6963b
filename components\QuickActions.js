import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';

/**
 * QuickActions Component
 * Displays quick action buttons for common operations
 */
const QuickActions = ({ onActionPress }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const actions = [
    {
      id: 'pay_bills',
      title: 'Pay Bills',
      subtitle: 'Utilities & Services',
      icon: 'receipt-outline',
      color: Colors.accent.coral,
      gradient: ['#E67E22', '#D35400'],
    },
    {
      id: 'buy_airtime',
      title: 'Buy Airtime',
      subtitle: 'Mobile Top-up',
      icon: 'phone-portrait-outline',
      color: Colors.secondary.savanna,
      gradient: ['#27AE60', '#229954'],
    },
    {
      id: 'send_money',
      title: 'Send Money',
      subtitle: 'To Friends & Family',
      icon: 'send-outline',
      color: Colors.primary.main,
      gradient: ['#8E44AD', '#7D3C98'],
    },
    {
      id: 'buy_data',
      title: 'Buy Data',
      subtitle: 'Internet Bundles',
      icon: 'wifi-outline',
      color: '#3498DB',
      gradient: ['#3498DB', '#2980B9'],
    },
    {
      id: 'scan_pay',
      title: 'Scan & Pay',
      subtitle: 'QR Code Payment',
      icon: 'qr-code-outline',
      color: '#E74C3C',
      gradient: ['#E74C3C', '#C0392B'],
    },
    {
      id: 'request_money',
      title: 'Request Money',
      subtitle: 'From Contacts',
      icon: 'hand-left-outline',
      color: '#F39C12',
      gradient: ['#F39C12', '#E67E22'],
    },
  ];

  const handleActionPress = (actionId) => {
    onActionPress?.(actionId);
  };

  const renderAction = (action) => (
    <TouchableOpacity
      key={action.id}
      style={[styles.actionCard, { backgroundColor: action.color }]}
      onPress={() => handleActionPress(action.id)}
      activeOpacity={0.8}
    >
      <View style={styles.actionContent}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={action.icon}
            size={24}
            color={Colors.neutral.white}
          />
        </View>
        <View style={styles.actionTextContainer}>
          <Text style={styles.actionTitle}>{action.title}</Text>
          <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
        </View>
        <View style={styles.arrowContainer}>
          <Ionicons
            name="chevron-forward"
            size={16}
            color={Colors.neutral.white}
          />
        </View>
      </View>
      
      {/* Decorative gradient overlay */}
      <View style={[styles.gradientOverlay, { backgroundColor: action.color }]} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <Text style={styles.sectionSubtitle}>What would you like to do?</Text>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.actionsContainer}
        style={styles.actionsScroll}
      >
        {actions.map(renderAction)}
      </ScrollView>

      {/* Grid Layout Alternative (commented out) */}
      {/* 
      <View style={styles.gridContainer}>
        {actions.slice(0, 4).map((action, index) => (
          <TouchableOpacity
            key={action.id}
            style={[styles.gridAction, { backgroundColor: action.color }]}
            onPress={() => handleActionPress(action.id)}
            activeOpacity={0.8}
          >
            <Ionicons
              name={action.icon}
              size={28}
              color={Colors.neutral.white}
            />
            <Text style={styles.gridActionText}>{action.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
      */}
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  actionsScroll: {
    paddingLeft: 20,
  },
  actionsContainer: {
    paddingRight: 20,
    gap: 12,
  },
  actionCard: {
    width: 280,
    borderRadius: 16,
    padding: 20,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  actionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: Colors.neutral.white,
    opacity: 0.8,
  },
  arrowContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
    zIndex: 1,
  },
  
  // Grid Layout Styles (Alternative)
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
  },
  gridAction: {
    width: '48%',
    aspectRatio: 1.2,
    borderRadius: 16,
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  gridActionText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default QuickActions;
