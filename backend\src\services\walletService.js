/**
 * Wallet Management Service
 * Handles wallet operations, balance management, and transaction processing
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('./database');
const redisService = require('./redis');
const currencyService = require('./currencyService');

class WalletService {
  constructor() {
    this.defaultCurrency = config.business.defaultCurrency;
    this.supportedCurrencies = config.business.supportedCurrencies;
  }

  /**
   * Create a new wallet for user
   */
  async createWallet(userId, currency = this.defaultCurrency) {
    try {
      // Validate currency
      if (!this.supportedCurrencies.includes(currency)) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      // Check if user already has a wallet
      const existingWallet = await this.getWallet(userId);
      if (existingWallet) {
        throw new Error('User already has a wallet');
      }

      const walletData = {
        id: uuidv4(),
        user_id: userId,
        currency: currency,
        balance: 0,
        available_balance: 0,
        pending_balance: 0,
        is_active: true,
        daily_limit: config.transactionLimits.dailyTransactionLimit,
        monthly_limit: config.transactionLimits.dailyTransactionLimit * 30,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const supabase = databaseService.getSupabase();
      const { data: wallet, error } = await supabase
        .from('wallets')
        .insert(walletData)
        .select()
        .single();

      if (error) {
        logger.error('Failed to create wallet:', error);
        throw new Error('Failed to create wallet');
      }

      // Cache wallet data
      await redisService.cacheWallet(userId, wallet, 1800); // 30 minutes

      logger.audit('Wallet created', {
        userId,
        walletId: wallet.id,
        currency
      });

      return wallet;
    } catch (error) {
      logger.error('Wallet creation failed:', error);
      throw error;
    }
  }

  /**
   * Get user's wallet
   */
  async getWallet(userId) {
    try {
      // Try cache first
      const cachedWallet = await redisService.getCachedWallet(userId);
      if (cachedWallet) {
        return cachedWallet;
      }

      // Get from database
      const supabase = databaseService.getSupabase();
      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Failed to get wallet:', error);
        throw new Error('Failed to retrieve wallet');
      }

      if (wallet) {
        // Cache wallet data
        await redisService.cacheWallet(userId, wallet, 1800);
      }

      return wallet;
    } catch (error) {
      logger.error('Failed to get wallet:', error);
      throw error;
    }
  }

  /**
   * Get wallet balance
   */
  async getBalance(userId) {
    try {
      const wallet = await this.getWallet(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      return {
        balance: wallet.balance,
        availableBalance: wallet.available_balance,
        pendingBalance: wallet.pending_balance,
        currency: wallet.currency,
        lastUpdated: wallet.updated_at
      };
    } catch (error) {
      logger.error('Failed to get balance:', error);
      throw error;
    }
  }

  /**
   * Update wallet balance
   */
  async updateBalance(userId, amount, type = 'credit', description = '', transactionId = null) {
    try {
      const wallet = await this.getWallet(userId);
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error('Invalid amount');
      }

      let newBalance = wallet.balance;
      let newAvailableBalance = wallet.available_balance;

      if (type === 'credit') {
        newBalance += numericAmount;
        newAvailableBalance += numericAmount;
      } else if (type === 'debit') {
        if (newAvailableBalance < numericAmount) {
          throw new Error('Insufficient balance');
        }
        newBalance -= numericAmount;
        newAvailableBalance -= numericAmount;
      } else {
        throw new Error('Invalid transaction type');
      }

      // Update wallet in database
      const supabase = databaseService.getSupabase();
      const { data: updatedWallet, error } = await supabase
        .from('wallets')
        .update({
          balance: newBalance,
          available_balance: newAvailableBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update wallet balance:', error);
        throw new Error('Failed to update balance');
      }

      // Clear cache
      await redisService.del(`wallet:${userId}`);

      // Log transaction
      logger.financial(
        `Wallet ${type}`,
        numericAmount,
        wallet.currency,
        userId,
        {
          walletId: wallet.id,
          transactionId,
          description,
          previousBalance: wallet.balance,
          newBalance: newBalance
        }
      );

      return updatedWallet;
    } catch (error) {
      logger.error('Failed to update balance:', error);
      throw error;
    }
  }

  /**
   * Transfer money between wallets
   */
  async transferMoney(fromUserId, toUserId, amount, description = '') {
    try {
      const transferId = uuidv4();
      
      // Validate amount
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error('Invalid transfer amount');
      }

      // Check transaction limits
      if (numericAmount > config.transactionLimits.maxTransactionAmount) {
        throw new Error('Amount exceeds maximum transaction limit');
      }

      // Get both wallets
      const fromWallet = await this.getWallet(fromUserId);
      const toWallet = await this.getWallet(toUserId);

      if (!fromWallet) {
        throw new Error('Sender wallet not found');
      }

      if (!toWallet) {
        throw new Error('Recipient wallet not found');
      }

      // Check sufficient balance
      if (fromWallet.available_balance < numericAmount) {
        throw new Error('Insufficient balance');
      }

      // Check daily limits
      const dailySpent = await this.getDailySpent(fromUserId);
      if (dailySpent + numericAmount > fromWallet.daily_limit) {
        throw new Error('Daily transaction limit exceeded');
      }

      // Handle currency conversion if needed
      let convertedAmount = numericAmount;
      let exchangeRate = 1;

      if (fromWallet.currency !== toWallet.currency) {
        const conversion = await currencyService.convertCurrency(
          numericAmount,
          fromWallet.currency,
          toWallet.currency
        );
        convertedAmount = conversion.convertedAmount;
        exchangeRate = conversion.exchangeRate;
      }

      // Perform transfer using database transaction
      const supabase = databaseService.getSupabase();
      
      // Start transaction
      const { data: transaction, error: transactionError } = await supabase.rpc(
        'transfer_money',
        {
          p_transfer_id: transferId,
          p_from_user_id: fromUserId,
          p_to_user_id: toUserId,
          p_amount: numericAmount,
          p_converted_amount: convertedAmount,
          p_exchange_rate: exchangeRate,
          p_description: description,
          p_from_currency: fromWallet.currency,
          p_to_currency: toWallet.currency
        }
      );

      if (transactionError) {
        logger.error('Transfer transaction failed:', transactionError);
        throw new Error('Transfer failed');
      }

      // Clear wallet caches
      await redisService.del(`wallet:${fromUserId}`);
      await redisService.del(`wallet:${toUserId}`);

      logger.audit('Money transfer completed', {
        transferId,
        fromUserId,
        toUserId,
        amount: numericAmount,
        convertedAmount,
        fromCurrency: fromWallet.currency,
        toCurrency: toWallet.currency,
        exchangeRate
      });

      return {
        transferId,
        amount: numericAmount,
        convertedAmount,
        fromCurrency: fromWallet.currency,
        toCurrency: toWallet.currency,
        exchangeRate,
        status: 'completed',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Money transfer failed:', error);
      throw error;
    }
  }

  /**
   * Get daily spent amount
   */
  async getDailySpent(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const supabase = databaseService.getSupabase();
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('amount')
        .eq('from_user_id', userId)
        .eq('type', 'transfer')
        .eq('status', 'completed')
        .gte('created_at', today.toISOString());

      if (error) {
        logger.error('Failed to get daily spent:', error);
        return 0;
      }

      return transactions.reduce((total, tx) => total + tx.amount, 0);
    } catch (error) {
      logger.error('Failed to calculate daily spent:', error);
      return 0;
    }
  }

  /**
   * Get transaction history
   */
  async getTransactionHistory(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        type = null,
        status = null,
        startDate = null,
        endDate = null
      } = options;

      const offset = (page - 1) * limit;

      const supabase = databaseService.getSupabase();
      let query = supabase
        .from('transactions')
        .select(`
          *,
          from_user:from_user_id(full_name, phone_number),
          to_user:to_user_id(full_name, phone_number)
        `)
        .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (type) {
        query = query.eq('type', type);
      }

      if (status) {
        query = query.eq('status', status);
      }

      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      const { data: transactions, error } = await query;

      if (error) {
        logger.error('Failed to get transaction history:', error);
        throw new Error('Failed to retrieve transaction history');
      }

      // Get total count for pagination
      let countQuery = supabase
        .from('transactions')
        .select('*', { count: 'exact', head: true })
        .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`);

      if (type) countQuery = countQuery.eq('type', type);
      if (status) countQuery = countQuery.eq('status', status);
      if (startDate) countQuery = countQuery.gte('created_at', startDate);
      if (endDate) countQuery = countQuery.lte('created_at', endDate);

      const { count, error: countError } = await countQuery;

      if (countError) {
        logger.error('Failed to get transaction count:', countError);
      }

      const totalPages = Math.ceil((count || 0) / limit);

      return {
        transactions: transactions.map(tx => ({
          ...tx,
          direction: tx.from_user_id === userId ? 'outgoing' : 'incoming'
        })),
        pagination: {
          page,
          limit,
          total: count || 0,
          pages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };

    } catch (error) {
      logger.error('Failed to get transaction history:', error);
      throw error;
    }
  }

  /**
   * Freeze/unfreeze wallet
   */
  async freezeWallet(userId, reason = 'security') {
    try {
      const supabase = databaseService.getSupabase();
      const { data: wallet, error } = await supabase
        .from('wallets')
        .update({
          is_active: false,
          frozen_reason: reason,
          frozen_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to freeze wallet:', error);
        throw new Error('Failed to freeze wallet');
      }

      // Clear cache
      await redisService.del(`wallet:${userId}`);

      logger.audit('Wallet frozen', {
        userId,
        walletId: wallet.id,
        reason
      });

      return wallet;
    } catch (error) {
      logger.error('Failed to freeze wallet:', error);
      throw error;
    }
  }

  /**
   * Unfreeze wallet
   */
  async unfreezeWallet(userId) {
    try {
      const supabase = databaseService.getSupabase();
      const { data: wallet, error } = await supabase
        .from('wallets')
        .update({
          is_active: true,
          frozen_reason: null,
          frozen_at: null,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to unfreeze wallet:', error);
        throw new Error('Failed to unfreeze wallet');
      }

      // Clear cache
      await redisService.del(`wallet:${userId}`);

      logger.audit('Wallet unfrozen', {
        userId,
        walletId: wallet.id
      });

      return wallet;
    } catch (error) {
      logger.error('Failed to unfreeze wallet:', error);
      throw error;
    }
  }
}

// Create singleton instance
const walletService = new WalletService();

module.exports = walletService;
