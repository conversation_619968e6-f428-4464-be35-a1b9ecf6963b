#!/usr/bin/env node

/**
 * Profile Names Fix Script
 * 
 * This script identifies and fixes user profiles that have auto-generated names
 * like "User 9916" by replacing them with real names from user registration data.
 */

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.development.local') });

console.log('🔧 Profile Names Fix Script');
console.log('===========================\n');

/**
 * Check if a name is auto-generated
 */
function isAutoGeneratedName(name) {
  if (!name || typeof name !== 'string') {
    return true;
  }

  const trimmedName = name.trim();
  const autoGeneratedPatterns = [
    /^User \d+$/,           // "User 9916"
    /^JiraniPay User$/,     // "JiraniPay User"
    /^User$/,               // Just "User"
    /^\d+$/,                // Just numbers
    /^[a-zA-Z0-9._-]+@/,    // Email-like patterns
  ];

  return autoGeneratedPatterns.some(pattern => pattern.test(trimmedName));
}

/**
 * Extract real name from user metadata
 */
function extractRealNameFromMetadata(userMetadata) {
  if (!userMetadata) return null;

  // Priority 1: full_name
  if (userMetadata.full_name && userMetadata.full_name.trim()) {
    const fullName = userMetadata.full_name.trim();
    if (!isAutoGeneratedName(fullName)) {
      return fullName;
    }
  }

  // Priority 2: name
  if (userMetadata.name && userMetadata.name.trim()) {
    const name = userMetadata.name.trim();
    if (!isAutoGeneratedName(name)) {
      return name;
    }
  }

  // Priority 3: first_name + last_name
  if (userMetadata.first_name && userMetadata.last_name) {
    const fullName = `${userMetadata.first_name} ${userMetadata.last_name}`.trim();
    if (!isAutoGeneratedName(fullName)) {
      return fullName;
    }
  }

  // Priority 4: just first_name
  if (userMetadata.first_name && userMetadata.first_name.trim()) {
    const firstName = userMetadata.first_name.trim();
    if (!isAutoGeneratedName(firstName)) {
      return firstName;
    }
  }

  return null;
}

/**
 * Main fix function
 */
async function main() {
  try {
    console.log('🚀 Starting profile names fix process...\n');

    // Import Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.EXPO_PUBLIC_SUPABASE_URL,
      process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
    );
    
    console.log('✅ Supabase client initialized');

    // Step 1: Get all profiles with auto-generated names
    console.log('\n🔍 Step 1: Finding profiles with auto-generated names...');
    
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name');

    if (profilesError) {
      throw profilesError;
    }

    const autoGeneratedProfiles = profiles.filter(profile => 
      isAutoGeneratedName(profile.full_name)
    );

    console.log(`📊 Found ${autoGeneratedProfiles.length} profiles with auto-generated names:`);
    autoGeneratedProfiles.forEach(profile => {
      console.log(`   - ${profile.id}: "${profile.full_name}"`);
    });

    if (autoGeneratedProfiles.length === 0) {
      console.log('\n🎉 No profiles with auto-generated names found!');
      console.log('All profiles already have real names.');
      return;
    }

    // Step 2: Process each profile
    console.log('\n🔧 Step 2: Processing profiles...');
    
    const results = {
      total: autoGeneratedProfiles.length,
      fixed: 0,
      unchanged: 0,
      errors: 0,
      details: []
    };

    for (const profile of autoGeneratedProfiles) {
      try {
        console.log(`\n📋 Processing user: ${profile.id}`);
        console.log(`   Current name: "${profile.full_name}"`);

        // Get user from auth to access metadata
        const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(profile.id);
        
        if (userError || !user) {
          console.log(`   ❌ Could not get user from auth: ${userError?.message || 'User not found'}`);
          results.errors++;
          results.details.push({
            userId: profile.id,
            status: 'error',
            error: 'Could not access user auth data'
          });
          continue;
        }

        console.log(`   📋 User metadata:`, {
          fullName: user.user_metadata?.full_name,
          name: user.user_metadata?.name,
          firstName: user.user_metadata?.first_name,
          lastName: user.user_metadata?.last_name
        });

        // Try to extract real name
        const realName = extractRealNameFromMetadata(user.user_metadata);
        
        if (!realName) {
          console.log(`   ⚠️ No real name found in metadata, keeping current name`);
          results.unchanged++;
          results.details.push({
            userId: profile.id,
            status: 'unchanged',
            reason: 'No real name available in user metadata'
          });
          continue;
        }

        // Update profile with real name
        console.log(`   🔄 Updating profile with real name: "${realName}"`);
        
        const { data: updatedProfile, error: updateError } = await supabase
          .from('profiles')
          .update({
            full_name: realName,
            updated_at: new Date().toISOString()
          })
          .eq('id', profile.id)
          .select()
          .single();

        if (updateError) {
          console.log(`   ❌ Error updating profile: ${updateError.message}`);
          results.errors++;
          results.details.push({
            userId: profile.id,
            status: 'error',
            error: updateError.message
          });
          continue;
        }

        console.log(`   ✅ Profile updated successfully!`);
        console.log(`   📝 Name changed: "${profile.full_name}" → "${realName}"`);
        
        results.fixed++;
        results.details.push({
          userId: profile.id,
          status: 'fixed',
          oldName: profile.full_name,
          newName: realName
        });

      } catch (error) {
        console.log(`   ❌ Error processing user: ${error.message}`);
        results.errors++;
        results.details.push({
          userId: profile.id,
          status: 'error',
          error: error.message
        });
      }
    }

    // Step 3: Show results
    console.log('\n📊 FINAL RESULTS');
    console.log('=================');
    console.log(`Total profiles processed: ${results.total}`);
    console.log(`✅ Fixed: ${results.fixed}`);
    console.log(`⚠️ Unchanged: ${results.unchanged}`);
    console.log(`❌ Errors: ${results.errors}`);

    if (results.fixed > 0) {
      console.log('\n🎉 SUCCESS! Fixed profiles:');
      results.details
        .filter(detail => detail.status === 'fixed')
        .forEach(detail => {
          console.log(`   ✅ ${detail.userId}: "${detail.oldName}" → "${detail.newName}"`);
        });
    }

    if (results.unchanged > 0) {
      console.log('\n⚠️ Unchanged profiles:');
      results.details
        .filter(detail => detail.status === 'unchanged')
        .forEach(detail => {
          console.log(`   ⚠️ ${detail.userId}: ${detail.reason}`);
        });
    }

    if (results.errors > 0) {
      console.log('\n❌ Errors:');
      results.details
        .filter(detail => detail.status === 'error')
        .forEach(detail => {
          console.log(`   ❌ ${detail.userId}: ${detail.error}`);
        });
    }

    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    if (results.fixed > 0) {
      console.log('✅ Profile names have been fixed!');
      console.log('✅ Users will now see their real names in greetings');
      console.log('✅ Both LoginScreen and DashboardScreen will show correct names');
      console.log('\n🔄 Restart the app to see the changes:');
      console.log('   npm run start-dev');
    } else {
      console.log('ℹ️ No profile names were changed');
      console.log('ℹ️ This could mean:');
      console.log('   - All profiles already have real names');
      console.log('   - User metadata doesn\'t contain real names');
      console.log('   - Users registered without providing names');
    }

    process.exit(results.errors > 0 ? 1 : 0);

  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
