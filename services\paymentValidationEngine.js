/**
 * Payment Validation Engine
 * Comprehensive validation logic for bill account numbers, amount limits,
 * payment schedules, and duplicate payment prevention with real-time verification
 */

import { supabase } from './supabaseClient';
import billerManagementService from './billerManagementService';
import { formatCurrency } from '../utils/currencyUtils';

// Validation error types
const VALIDATION_ERRORS = {
  INVALID_ACCOUNT: 'invalid_account',
  INVALID_AMOUNT: 'invalid_amount',
  DUPLICATE_PAYMENT: 'duplicate_payment',
  BILLER_UNAVAILABLE: 'biller_unavailable',
  LIMIT_EXCEEDED: 'limit_exceeded',
  SCHEDULE_CONFLICT: 'schedule_conflict',
  INSUFFICIENT_BALANCE: 'insufficient_balance'
};

// Validation severity levels
const VALIDATION_SEVERITY = {
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

class PaymentValidationEngine {
  constructor() {
    this.validationErrors = VALIDATION_ERRORS;
    this.validationSeverity = VALIDATION_SEVERITY;
    this.validationCache = new Map();
    this.cacheExpiry = 2 * 60 * 1000; // 2 minutes for validation cache
  }

  /**
   * Comprehensive payment validation
   */
  async validatePayment(userId, paymentData) {
    try {
      console.log('🔍 Starting comprehensive payment validation');

      const validationResults = {
        isValid: true,
        errors: [],
        warnings: [],
        info: [],
        validatedData: {}
      };

      // 1. Validate biller availability
      const billerValidation = await this.validateBillerAvailability(paymentData.billerId);
      this.mergeValidationResults(validationResults, billerValidation);

      // 2. Validate account number
      const accountValidation = await this.validateAccountNumber(
        paymentData.billerId, 
        paymentData.accountNumber
      );
      this.mergeValidationResults(validationResults, accountValidation);

      // 3. Validate payment amount
      const amountValidation = await this.validatePaymentAmount(
        paymentData.billerId, 
        paymentData.amount
      );
      this.mergeValidationResults(validationResults, amountValidation);

      // 4. Check for duplicate payments
      const duplicateValidation = await this.checkDuplicatePayment(
        userId, 
        paymentData
      );
      this.mergeValidationResults(validationResults, duplicateValidation);

      // 5. Validate user payment limits
      const limitValidation = await this.validatePaymentLimits(
        userId, 
        paymentData.amount
      );
      this.mergeValidationResults(validationResults, limitValidation);

      // 6. Check user balance (if applicable)
      const balanceValidation = await this.validateUserBalance(
        userId, 
        paymentData.amount
      );
      this.mergeValidationResults(validationResults, balanceValidation);

      // 7. Validate payment schedule (for recurring payments)
      if (paymentData.isRecurring) {
        const scheduleValidation = await this.validatePaymentSchedule(
          userId, 
          paymentData
        );
        this.mergeValidationResults(validationResults, scheduleValidation);
      }

      console.log('✅ Payment validation completed:', {
        isValid: validationResults.isValid,
        errorCount: validationResults.errors.length,
        warningCount: validationResults.warnings.length
      });

      return validationResults;
    } catch (error) {
      console.error('❌ Error in payment validation:', error);
      return {
        isValid: false,
        errors: [{
          type: 'validation_error',
          message: 'Payment validation failed',
          severity: this.validationSeverity.ERROR
        }],
        warnings: [],
        info: []
      };
    }
  }

  /**
   * Validate biller availability
   */
  async validateBillerAvailability(billerId) {
    try {
      const availability = await billerManagementService.checkBillerAvailability(billerId);
      
      if (!availability.success) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.BILLER_UNAVAILABLE,
            message: 'Unable to verify biller availability',
            severity: this.validationSeverity.ERROR
          }]
        };
      }

      if (!availability.availability.isAvailable) {
        const message = availability.availability.maintenanceMode 
          ? availability.availability.maintenanceMessage || 'Biller is under maintenance'
          : availability.availability.currentOutage?.public_message || 'Biller is currently unavailable';

        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.BILLER_UNAVAILABLE,
            message,
            severity: this.validationSeverity.ERROR
          }]
        };
      }

      return { isValid: true, errors: [], warnings: [], info: [] };
    } catch (error) {
      console.error('❌ Error validating biller availability:', error);
      return {
        isValid: false,
        errors: [{
          type: this.validationErrors.BILLER_UNAVAILABLE,
          message: 'Biller availability check failed',
          severity: this.validationSeverity.ERROR
        }]
      };
    }
  }

  /**
   * Validate account number format and existence
   */
  async validateAccountNumber(billerId, accountNumber) {
    try {
      // Check cache first
      const cacheKey = `account_${billerId}_${accountNumber}`;
      const cached = this.validationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.result;
      }

      const validationRules = await billerManagementService.getBillerValidationRules(billerId);
      
      if (!validationRules.success) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.INVALID_ACCOUNT,
            message: 'Unable to retrieve account validation rules',
            severity: this.validationSeverity.ERROR
          }]
        };
      }

      const rules = validationRules.validationRules;
      const errors = [];
      const warnings = [];

      // Format validation
      if (rules.accountNumberFormat) {
        const regex = new RegExp(rules.accountNumberFormat);
        if (!regex.test(accountNumber)) {
          errors.push({
            type: this.validationErrors.INVALID_ACCOUNT,
            message: 'Account number format is invalid',
            severity: this.validationSeverity.ERROR
          });
        }
      }

      // Length validation
      if (rules.accountNumberLength) {
        if (accountNumber.length !== rules.accountNumberLength) {
          errors.push({
            type: this.validationErrors.INVALID_ACCOUNT,
            message: `Account number must be exactly ${rules.accountNumberLength} characters`,
            severity: this.validationSeverity.ERROR
          });
        }
      }

      // Prefix validation
      if (rules.accountNumberPrefix) {
        if (!accountNumber.startsWith(rules.accountNumberPrefix)) {
          errors.push({
            type: this.validationErrors.INVALID_ACCOUNT,
            message: `Account number must start with ${rules.accountNumberPrefix}`,
            severity: this.validationSeverity.ERROR
          });
        }
      }

      // Real-time account verification (if supported)
      if (errors.length === 0) {
        try {
          const verificationResult = await this.performRealTimeAccountVerification(
            billerId, 
            accountNumber
          );
          
          if (!verificationResult.verified) {
            errors.push({
              type: this.validationErrors.INVALID_ACCOUNT,
              message: verificationResult.message || 'Account number could not be verified',
              severity: this.validationSeverity.ERROR
            });
          } else if (verificationResult.warnings) {
            warnings.push(...verificationResult.warnings);
          }
        } catch (verificationError) {
          warnings.push({
            type: 'verification_warning',
            message: 'Real-time verification unavailable, proceeding with format validation',
            severity: this.validationSeverity.WARNING
          });
        }
      }

      const result = {
        isValid: errors.length === 0,
        errors,
        warnings,
        info: []
      };

      // Cache the result
      this.validationCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error('❌ Error validating account number:', error);
      return {
        isValid: false,
        errors: [{
          type: this.validationErrors.INVALID_ACCOUNT,
          message: 'Account number validation failed',
          severity: this.validationSeverity.ERROR
        }]
      };
    }
  }

  /**
   * Validate payment amount
   */
  async validatePaymentAmount(billerId, amount) {
    try {
      const feeStructure = await billerManagementService.getBillerFeeStructure(billerId);
      
      if (!feeStructure.success) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.INVALID_AMOUNT,
            message: 'Unable to retrieve payment limits',
            severity: this.validationSeverity.ERROR
          }]
        };
      }

      const { minAmount, maxAmount } = feeStructure.feeStructure;
      const errors = [];
      const warnings = [];

      // Amount validation
      if (amount <= 0) {
        errors.push({
          type: this.validationErrors.INVALID_AMOUNT,
          message: 'Payment amount must be greater than zero',
          severity: this.validationSeverity.ERROR
        });
      }

      if (minAmount && amount < minAmount) {
        errors.push({
          type: this.validationErrors.INVALID_AMOUNT,
          message: `Minimum payment amount is ${formatCurrency(minAmount)}`,
          severity: this.validationSeverity.ERROR
        });
      }

      if (maxAmount && amount > maxAmount) {
        errors.push({
          type: this.validationErrors.INVALID_AMOUNT,
          message: `Maximum payment amount is ${formatCurrency(maxAmount)}`,
          severity: this.validationSeverity.ERROR
        });
      }

      // Warning for large amounts
      if (maxAmount && amount > maxAmount * 0.8) {
        warnings.push({
          type: 'large_amount_warning',
          message: 'This is a large payment amount. Please verify the details.',
          severity: this.validationSeverity.WARNING
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        info: []
      };
    } catch (error) {
      console.error('❌ Error validating payment amount:', error);
      return {
        isValid: false,
        errors: [{
          type: this.validationErrors.INVALID_AMOUNT,
          message: 'Payment amount validation failed',
          severity: this.validationSeverity.ERROR
        }]
      };
    }
  }

  /**
   * Check for duplicate payments
   */
  async checkDuplicatePayment(userId, paymentData) {
    try {
      const timeWindow = 5 * 60 * 1000; // 5 minutes
      const cutoffTime = new Date(Date.now() - timeWindow).toISOString();

      const { data: recentPayments, error } = await supabase
        .from('bill_payments')
        .select('id, amount, account_number, created_at')
        .eq('user_id', userId)
        .eq('biller_id', paymentData.billerId)
        .eq('account_number', paymentData.accountNumber)
        .eq('amount', paymentData.amount)
        .gte('created_at', cutoffTime)
        .in('status', ['pending', 'processing', 'completed']);

      if (error) throw error;

      if (recentPayments && recentPayments.length > 0) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.DUPLICATE_PAYMENT,
            message: 'A similar payment was made recently. Please wait before trying again.',
            severity: this.validationSeverity.ERROR,
            data: { recentPayment: recentPayments[0] }
          }]
        };
      }

      return { isValid: true, errors: [], warnings: [], info: [] };
    } catch (error) {
      console.error('❌ Error checking duplicate payment:', error);
      // Don't block payment if duplicate check fails
      return {
        isValid: true,
        errors: [],
        warnings: [{
          type: 'duplicate_check_warning',
          message: 'Unable to verify duplicate payments',
          severity: this.validationSeverity.WARNING
        }],
        info: []
      };
    }
  }

  /**
   * Validate user payment limits
   */
  async validatePaymentLimits(userId, amount) {
    try {
      // Get user's daily payment total
      const today = new Date().toISOString().split('T')[0];
      
      const { data: todayPayments, error } = await supabase
        .from('bill_payments')
        .select('amount')
        .eq('user_id', userId)
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`)
        .eq('status', 'completed');

      if (error) throw error;

      const todayTotal = todayPayments.reduce((sum, payment) => sum + payment.amount, 0);
      const newTotal = todayTotal + amount;

      // Default daily limit (should be configurable per user)
      const dailyLimit = 5000000; // UGX 5M

      const warnings = [];

      if (newTotal > dailyLimit) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.LIMIT_EXCEEDED,
            message: `Daily payment limit of ${formatCurrency(dailyLimit)} would be exceeded`,
            severity: this.validationSeverity.ERROR,
            data: { currentTotal: todayTotal, dailyLimit }
          }]
        };
      }

      // Warning when approaching limit
      if (newTotal > dailyLimit * 0.8) {
        warnings.push({
          type: 'limit_warning',
          message: `You're approaching your daily payment limit (${formatCurrency(newTotal)}/${formatCurrency(dailyLimit)})`,
          severity: this.validationSeverity.WARNING
        });
      }

      return {
        isValid: true,
        errors: [],
        warnings,
        info: []
      };
    } catch (error) {
      console.error('❌ Error validating payment limits:', error);
      // Don't block payment if limit check fails
      return {
        isValid: true,
        errors: [],
        warnings: [{
          type: 'limit_check_warning',
          message: 'Unable to verify payment limits',
          severity: this.validationSeverity.WARNING
        }],
        info: []
      };
    }
  }

  /**
   * Validate user balance
   */
  async validateUserBalance(userId, amount) {
    try {
      // This would integrate with wallet service
      // For now, return success with warning
      return {
        isValid: true,
        errors: [],
        warnings: [{
          type: 'balance_check_info',
          message: 'Please ensure you have sufficient balance for this payment',
          severity: this.validationSeverity.INFO
        }],
        info: []
      };
    } catch (error) {
      console.error('❌ Error validating user balance:', error);
      return {
        isValid: true,
        errors: [],
        warnings: [{
          type: 'balance_check_warning',
          message: 'Unable to verify account balance',
          severity: this.validationSeverity.WARNING
        }],
        info: []
      };
    }
  }

  /**
   * Validate payment schedule for recurring payments
   */
  async validatePaymentSchedule(userId, paymentData) {
    try {
      // Check for conflicting recurring payments
      const { data: existingSchedules, error } = await supabase
        .from('recurring_bill_payments')
        .select('*')
        .eq('user_id', userId)
        .eq('biller_id', paymentData.billerId)
        .eq('account_number', paymentData.accountNumber)
        .eq('is_active', true);

      if (error) throw error;

      if (existingSchedules && existingSchedules.length > 0) {
        return {
          isValid: false,
          errors: [{
            type: this.validationErrors.SCHEDULE_CONFLICT,
            message: 'A recurring payment already exists for this account',
            severity: this.validationSeverity.ERROR,
            data: { existingSchedule: existingSchedules[0] }
          }]
        };
      }

      return { isValid: true, errors: [], warnings: [], info: [] };
    } catch (error) {
      console.error('❌ Error validating payment schedule:', error);
      return {
        isValid: false,
        errors: [{
          type: this.validationErrors.SCHEDULE_CONFLICT,
          message: 'Unable to validate payment schedule',
          severity: this.validationSeverity.ERROR
        }]
      };
    }
  }

  /**
   * Perform real-time account verification
   */
  async performRealTimeAccountVerification(billerId, accountNumber) {
    try {
      // Mock real-time verification - in production, integrate with biller APIs
      await new Promise(resolve => setTimeout(resolve, 500));

      // Simulate verification result
      const isValid = Math.random() > 0.1; // 90% success rate

      if (isValid) {
        return {
          verified: true,
          accountName: `Account Holder ${accountNumber.slice(-4)}`,
          accountStatus: 'active'
        };
      } else {
        return {
          verified: false,
          message: 'Account number not found or inactive'
        };
      }
    } catch (error) {
      console.error('❌ Real-time verification error:', error);
      throw error;
    }
  }

  /**
   * Merge validation results
   */
  mergeValidationResults(target, source) {
    if (!source.isValid) {
      target.isValid = false;
    }
    
    target.errors.push(...(source.errors || []));
    target.warnings.push(...(source.warnings || []));
    target.info.push(...(source.info || []));
    
    if (source.validatedData) {
      target.validatedData = { ...target.validatedData, ...source.validatedData };
    }
  }

  /**
   * Clear validation cache
   */
  clearCache() {
    this.validationCache.clear();
    console.log('🧹 Payment validation cache cleared');
  }
}

export default new PaymentValidationEngine();
