/**
 * Profile Name Verification Service
 * 
 * This service ensures that user profiles contain real names from registration
 * instead of auto-generated names like "User 9916"
 */

import { supabase } from '../config/supabaseClient';

class ProfileNameVerificationService {
  constructor() {
    this.tableName = 'profiles'; // Using schema-essential.sql structure
  }

  /**
   * Verify and fix profile names for a specific user
   * @param {string} userId - User ID to verify
   * @returns {Promise<Object>} - Result object
   */
  async verifyAndFixUserProfileName(userId) {
    try {
      console.log('🔍 Verifying profile name for user:', userId);

      // Get current user from auth to access user_metadata
      const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(userId);
      
      if (userError || !user) {
        console.error('❌ Could not get user from auth:', userError);
        return { success: false, error: 'User not found in auth system' };
      }

      // Get current profile from database
      const { data: profile, error: profileError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('❌ Could not get profile from database:', profileError);
        return { success: false, error: 'Profile not found in database' };
      }

      console.log('📋 Current profile data:', {
        id: profile.id,
        fullName: profile.full_name,
        phone: profile.phone,
        email: profile.email
      });

      console.log('📋 User metadata:', {
        fullName: user.user_metadata?.full_name,
        name: user.user_metadata?.name,
        firstName: user.user_metadata?.first_name,
        lastName: user.user_metadata?.last_name
      });

      // Check if current profile name is auto-generated
      const isAutoGenerated = this.isAutoGeneratedName(profile.full_name);
      
      if (!isAutoGenerated) {
        console.log('✅ Profile already has a real name:', profile.full_name);
        return { 
          success: true, 
          data: profile, 
          changed: false, 
          message: 'Profile already has a real name' 
        };
      }

      // Try to get real name from user_metadata
      const realName = this.extractRealNameFromMetadata(user.user_metadata);
      
      if (!realName) {
        console.log('⚠️ No real name found in user metadata, keeping auto-generated name');
        return { 
          success: true, 
          data: profile, 
          changed: false, 
          message: 'No real name available in user metadata' 
        };
      }

      // Update profile with real name
      console.log('🔄 Updating profile with real name:', realName);
      
      const { data: updatedProfile, error: updateError } = await supabase
        .from(this.tableName)
        .update({
          full_name: realName,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Error updating profile:', updateError);
        return { success: false, error: updateError.message };
      }

      console.log('✅ Profile name updated successfully:', {
        oldName: profile.full_name,
        newName: updatedProfile.full_name
      });

      return { 
        success: true, 
        data: updatedProfile, 
        changed: true, 
        oldName: profile.full_name,
        newName: updatedProfile.full_name,
        message: 'Profile name updated with real name from registration' 
      };

    } catch (error) {
      console.error('❌ Error verifying profile name:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if a name is auto-generated
   * @param {string} name - Name to check
   * @returns {boolean} - True if auto-generated
   */
  isAutoGeneratedName(name) {
    if (!name || typeof name !== 'string') {
      return true;
    }

    const trimmedName = name.trim();

    // Check for various auto-generated patterns
    const autoGeneratedPatterns = [
      /^User \d+$/,           // "User 9916"
      /^JiraniPay User$/,     // "JiraniPay User"
      /^User$/,               // Just "User"
      /^\d+$/,                // Just numbers
      /^[a-zA-Z0-9._-]+@/,    // Email-like patterns
    ];

    return autoGeneratedPatterns.some(pattern => pattern.test(trimmedName));
  }

  /**
   * Extract real name from user metadata
   * @param {Object} userMetadata - User metadata object
   * @returns {string|null} - Real name or null
   */
  extractRealNameFromMetadata(userMetadata) {
    if (!userMetadata) {
      return null;
    }

    // Priority 1: full_name
    if (userMetadata.full_name && userMetadata.full_name.trim()) {
      const fullName = userMetadata.full_name.trim();
      if (!this.isAutoGeneratedName(fullName)) {
        return fullName;
      }
    }

    // Priority 2: name
    if (userMetadata.name && userMetadata.name.trim()) {
      const name = userMetadata.name.trim();
      if (!this.isAutoGeneratedName(name)) {
        return name;
      }
    }

    // Priority 3: first_name + last_name
    if (userMetadata.first_name && userMetadata.last_name) {
      const fullName = `${userMetadata.first_name} ${userMetadata.last_name}`.trim();
      if (!this.isAutoGeneratedName(fullName)) {
        return fullName;
      }
    }

    // Priority 4: just first_name
    if (userMetadata.first_name && userMetadata.first_name.trim()) {
      const firstName = userMetadata.first_name.trim();
      if (!this.isAutoGeneratedName(firstName)) {
        return firstName;
      }
    }

    return null;
  }

  /**
   * Verify and fix profile names for all users with auto-generated names
   * @returns {Promise<Object>} - Results summary
   */
  async verifyAndFixAllAutoGeneratedNames() {
    try {
      console.log('🔍 Finding all profiles with auto-generated names...');

      // Get all profiles
      const { data: profiles, error: profilesError } = await supabase
        .from(this.tableName)
        .select('id, full_name');

      if (profilesError) {
        throw profilesError;
      }

      // Filter profiles with auto-generated names
      const autoGeneratedProfiles = profiles.filter(profile => 
        this.isAutoGeneratedName(profile.full_name)
      );

      console.log(`📊 Found ${autoGeneratedProfiles.length} profiles with auto-generated names`);

      const results = {
        total: autoGeneratedProfiles.length,
        fixed: 0,
        unchanged: 0,
        errors: 0,
        details: []
      };

      // Process each profile
      for (const profile of autoGeneratedProfiles) {
        try {
          const result = await this.verifyAndFixUserProfileName(profile.id);
          
          if (result.success) {
            if (result.changed) {
              results.fixed++;
              results.details.push({
                userId: profile.id,
                status: 'fixed',
                oldName: result.oldName,
                newName: result.newName
              });
            } else {
              results.unchanged++;
              results.details.push({
                userId: profile.id,
                status: 'unchanged',
                reason: result.message
              });
            }
          } else {
            results.errors++;
            results.details.push({
              userId: profile.id,
              status: 'error',
              error: result.error
            });
          }
        } catch (error) {
          results.errors++;
          results.details.push({
            userId: profile.id,
            status: 'error',
            error: error.message
          });
        }
      }

      console.log('📊 Profile name verification results:', {
        total: results.total,
        fixed: results.fixed,
        unchanged: results.unchanged,
        errors: results.errors
      });

      return { success: true, data: results };

    } catch (error) {
      console.error('❌ Error verifying all profile names:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current user's profile name status
   * @returns {Promise<Object>} - Profile name status
   */
  async getCurrentUserProfileNameStatus() {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'No authenticated user found' };
      }

      return await this.verifyAndFixUserProfileName(user.id);

    } catch (error) {
      console.error('❌ Error getting current user profile name status:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create and export singleton instance
const profileNameVerificationService = new ProfileNameVerificationService();
export default profileNameVerificationService;
