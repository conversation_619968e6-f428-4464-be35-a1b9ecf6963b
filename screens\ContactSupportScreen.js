import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const ContactSupportScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);

  const supportOptions = [
    {
      id: 'live_chat',
      title: 'Live AI Chat',
      subtitle: 'Instant AI Assistant',
      description: 'Get immediate help from our AI assistant',
      contact: 'AI Assistant',
      icon: 'chatbubble-ellipses',
      color: Colors.primary.main,
      action: () => navigation.navigate('AIChat'),
      available: '24/7',
      responseTime: 'Instant',
      priority: 1
    },
    {
      id: 'phone',
      title: 'Call Support',
      subtitle: '24/7 Customer Service',
      description: 'Speak directly with our support team',
      contact: '+************',
      icon: 'call',
      color: Colors.status.error,
      action: () => callSupport('+************'),
      available: '24/7',
      responseTime: 'Immediate',
      priority: 2
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp Support',
      subtitle: 'Chat on WhatsApp',
      description: 'Message us on WhatsApp for quick help',
      contact: '+************',
      icon: 'logo-whatsapp',
      color: '#25D366',
      action: () => openWhatsApp('+************'),
      available: 'Mon-Fri 8AM-8PM',
      responseTime: '5-15 minutes',
      priority: 3
    },
    {
      id: 'email',
      title: 'Email Support',
      subtitle: 'General Inquiries',
      description: 'Send us your questions and concerns',
      contact: '<EMAIL>',
      icon: 'mail',
      color: Colors.secondary.forest,
      action: () => emailSupport('<EMAIL>'),
      available: 'Mon-Fri 8AM-6PM',
      responseTime: '2-4 hours',
      priority: 4
    },
    {
      id: 'security',
      title: 'Security Hotline',
      subtitle: 'Emergency Security Issues',
      description: 'Report security incidents immediately',
      contact: '+************',
      icon: 'shield-checkmark',
      color: Colors.accent.gold,
      action: () => callSupport('+************'),
      available: '24/7',
      responseTime: 'Immediate',
      priority: 5
    },
    {
      id: 'technical',
      title: 'Technical Support',
      subtitle: 'App & Technical Issues',
      description: 'Get help with app functionality',
      contact: '<EMAIL>',
      icon: 'settings',
      color: Colors.neutral.warmGray,
      action: () => emailSupport('<EMAIL>'),
      available: 'Mon-Fri 8AM-8PM',
      responseTime: '1-2 hours',
      priority: 6
    }
  ];

  const supportCategories = [
    {
      id: 'account',
      title: 'Account Issues',
      icon: 'person',
      color: Colors.primary.main,
      issues: [
        'Login problems',
        'Account verification',
        'Profile updates',
        'Account recovery',
        'KYC verification',
        'Phone number changes',
        'Password reset',
        'Account suspension'
      ]
    },
    {
      id: 'transactions',
      title: 'Transaction Problems',
      icon: 'card',
      color: Colors.status.success,
      issues: [
        'Failed transactions',
        'Missing money',
        'Transaction disputes',
        'Payment issues',
        'Send money problems',
        'Bill payment failures',
        'Mobile money integration',
        'Transaction limits'
      ]
    },
    {
      id: 'wallet',
      title: 'Wallet & Balance',
      icon: 'wallet',
      color: Colors.secondary.lake,
      issues: [
        'Balance discrepancies',
        'Top-up issues',
        'Wallet not loading',
        'Currency conversion',
        'Savings problems',
        'Automatic savings',
        'Withdrawal issues',
        'Balance visibility'
      ]
    },
    {
      id: 'security',
      title: 'Security Concerns',
      icon: 'shield',
      color: Colors.status.error,
      issues: [
        'Suspicious activity',
        'Unauthorized access',
        'Fraud reports',
        'Security questions',
        'Biometric issues',
        'PIN problems',
        'Two-factor authentication',
        'Device security'
      ]
    },
    {
      id: 'bills',
      title: 'Bill Payments',
      icon: 'receipt',
      color: Colors.accent.gold,
      issues: [
        'UMEME bill payment',
        'Water bill issues',
        'Airtime purchase',
        'Data bundle problems',
        'School fees payment',
        'Utility connections',
        'Bill reminders',
        'Payment confirmations'
      ]
    },
    {
      id: 'technical',
      title: 'Technical Support',
      icon: 'settings',
      color: Colors.neutral.warmGray,
      issues: [
        'App crashes',
        'Feature not working',
        'Update problems',
        'Device compatibility',
        'Network issues',
        'Performance problems',
        'UI/UX issues',
        'Notification problems'
      ]
    }
  ];

  const callSupport = (phoneNumber) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(t('callSupport'), t('doYouWantToCallPhonenumber'),
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => Linking.openURL(`tel:${phoneNumber}`)
        }
      ]
    );
  };

  const emailSupport = (email) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const subject = 'JiraniPay Support Request';
    const body = 'Hello JiraniPay Support Team,\n\nI need assistance with:\n\n[Please describe your issue here]\n\nAccount Details:\n- Phone: [Your registered phone number]\n- Issue Category: [Transaction/Account/Technical/Security]\n- Urgency: [Low/Medium/High]\n\nThank you.';

    Alert.alert(t('support.emailSupport'), t('support.doYouWantToSendAnEmailToEmail'),
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Email',
          onPress: () => Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`)
        }
      ]
    );
  };

  const openWhatsApp = (phoneNumber) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const message = 'Hello JiraniPay Support Team,\n\nI need assistance with my JiraniPay account.\n\nIssue: [Please describe your issue]\n\nThank you.';
    const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;

    Alert.alert(t('whatsappSupport'), t('doYouWantToMessageUsOnWhatsapp'),
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open WhatsApp',
          onPress: () => {
            Linking.canOpenURL(whatsappUrl).then(supported => {
              if (supported) {
                Linking.openURL(whatsappUrl);
              } else {
                Alert.alert(t('whatsappNotAvailable'), t('whatsappIsNotInstalledOnYourDevicePleaseInstallWha'),
                  [
                    { text: 'OK', style: 'default' },
                    { text: 'Call Instead', onPress: () => callSupport(phoneNumber) }
                  ]
                );
              }
            });
          }
        }
      ]
    );
  };

  const toggleCategory = (categoryId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedCategory(selectedCategory === categoryId ? null : categoryId);
  };

  // Filter support categories based on search query
  const filteredCategories = supportCategories.filter(category => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      category.title.toLowerCase().includes(query) ||
      category.issues.some(issue => issue.toLowerCase().includes(query))
    );
  });

  // Filter support options based on search query
  const filteredSupportOptions = supportOptions.filter(option => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      option.title.toLowerCase().includes(query) ||
      option.subtitle.toLowerCase().includes(query) ||
      option.description.toLowerCase().includes(query)
    );
  });

  const renderSupportOption = (option) => (
    <TouchableOpacity
      key={option.id}
      style={styles.supportCard}
      onPress={option.action}
      activeOpacity={0.7}
    >
      <View style={styles.supportLeft}>
        <View style={[styles.supportIcon, { backgroundColor: option.color + '20' }]}>
          <Ionicons name={option.icon} size={24} color={option.color} />
        </View>
        <View style={styles.supportInfo}>
          <Text style={styles.supportTitle}>{option.title}</Text>
          <Text style={styles.supportSubtitle}>{option.subtitle}</Text>
          <Text style={styles.supportContact}>{option.contact}</Text>
          <Text style={styles.supportDescription}>{option.description}</Text>
        </View>
      </View>
      <View style={styles.supportRight}>
        <View style={styles.supportMeta}>
          <Text style={styles.supportAvailable}>{option.available}</Text>
          <Text style={styles.supportResponse}>Response: {option.responseTime}</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
      </View>
    </TouchableOpacity>
  );

  const renderCategory = (category) => {
    const isExpanded = selectedCategory === category.id;
    
    return (
      <TouchableOpacity
        key={category.id}
        style={[styles.categoryCard, isExpanded && styles.expandedCategoryCard]}
        onPress={() => toggleCategory(category.id)}
        activeOpacity={0.7}
      >
        <View style={styles.categoryHeader}>
          <View style={styles.categoryLeft}>
            <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
              <Ionicons name={category.icon} size={24} color={category.color} />
            </View>
            <Text style={styles.categoryTitle}>{category.title}</Text>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={Colors.neutral.warmGray} 
          />
        </View>
        
        {isExpanded && (
          <View style={styles.categoryContent}>
            <Text style={styles.categoryDescription}>{t('commonIssuesInThisCategory')}</Text>
            {category.issues.map((issue, index) => (
              <View key={index} style={styles.issueItem}>
                <View style={styles.issueBullet} />
                <Text style={styles.issueText}>{issue}</Text>
              </View>
            ))}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('support.contactSupport')}</Text>
          <TouchableOpacity
            style={styles.searchButton}
            onPress={() => setShowSearch(!showSearch)}
            activeOpacity={0.7}
          >
            <Ionicons name={showSearch ? 'close' : 'search'} size={20} color={Colors.neutral.white} />
          </TouchableOpacity>
        </View>
        <Text style={styles.headerSubtitle}>
          {t('support.wereHereToHelpYou247')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Section */}
        {showSearch && (
          <View style={styles.searchSection}>
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color={Colors.neutral.warmGray} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search support topics..."
                placeholderTextColor={Colors.neutral.warmGray}
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoFocus={showSearch}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={20} color={Colors.neutral.warmGray} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Support Overview */}
        <View style={styles.section}>
          <View style={styles.overviewCard}>
            <View style={styles.overviewIcon}>
              <Ionicons name="headset" size={32} color={Colors.primary.main} />
            </View>
            <View style={styles.overviewText}>
              <Text style={styles.overviewTitle}>{t('support.customerSupport')}</Text>
              <Text style={styles.overviewDescription}>
                {t('support.ourDedicatedSupportTeamIsAvailable247ToAssistYouWi')}
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('quickActions')}</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('CreateTicket')}
              activeOpacity={0.7}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: Colors.primary.main + '20' }]}>
                <Ionicons name="create" size={24} color={Colors.primary.main} />
              </View>
              <Text style={styles.quickActionTitle}>{t('createTicket')}</Text>
              <Text style={styles.quickActionDescription}>{t('submitADetailedSupportRequest')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => navigation.navigate('AIChat')}
              activeOpacity={0.7}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: Colors.accent.gold + '20' }]}>
                <Ionicons name="chatbubble-ellipses" size={24} color={Colors.accent.gold} />
              </View>
              <Text style={styles.quickActionTitle}>{t('support.aiAssistant')}</Text>
              <Text style={styles.quickActionDescription}>{t('support.getInstantHelpFromAi')}</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Support Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('support.contactMethods')}</Text>
          <Text style={styles.sectionDescription}>
            {t('support.chooseTheBestWayToReachUsBasedOnYourNeeds')}
          </Text>

          {filteredSupportOptions.map(renderSupportOption)}
        </View>

        {/* Support Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('whatCanWeHelpYouWith')}</Text>
          <Text style={styles.sectionDescription}>
            {t('browseCommonSupportCategoriesToFindTheRightAssista')}
          </Text>

          {filteredCategories.map(renderCategory)}
        </View>

        {/* Emergency Contact */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('emergencySupport')}</Text>
          
          <View style={styles.emergencyCard}>
            <View style={styles.emergencyIcon}>
              <Ionicons name="warning" size={32} color={Colors.status.error} />
            </View>
            <View style={styles.emergencyText}>
              <Text style={styles.emergencyTitle}>{t('securityEmergency')}</Text>
              <Text style={styles.emergencyDescription}>
                {t('ifYouSuspectFraudOrUnauthorizedAccessToYourAccount')}
              </Text>
              <TouchableOpacity
                style={styles.emergencyButton}
                onPress={() => callSupport('+************')}
                activeOpacity={0.7}
              >
                <Ionicons name="call" size={20} color={Colors.neutral.white} />
                <Text style={styles.emergencyButtonText}>+************</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Support Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('supportHours')}</Text>
          
          <View style={styles.hoursCard}>
            <View style={styles.hoursItem}>
              <Ionicons name="call" size={20} color={Colors.status.error} />
              <View style={styles.hoursText}>
                <Text style={styles.hoursTitle}>{t('phoneSupport')}</Text>
                <Text style={styles.hoursTime}>{t('alwaysAvailable247')}</Text>
              </View>
            </View>

            <View style={styles.hoursItem}>
              <Ionicons name="mail" size={20} color={Colors.primary.main} />
              <View style={styles.hoursText}>
                <Text style={styles.hoursTitle}>{t('emailSupport')}</Text>
                <Text style={styles.hoursTime}>{t('mondayFriday800Am600Pm')}</Text>
              </View>
            </View>

            <View style={styles.hoursItem}>
              <Ionicons name="settings" size={20} color={Colors.secondary.forest} />
              <View style={styles.hoursText}>
                <Text style={styles.hoursTitle}>{t('technicalSupport')}</Text>
                <Text style={styles.hoursTime}>{t('mondayFriday800Am800Pm')}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Additional Resources */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('additionalResources')}</Text>
          
          <TouchableOpacity 
            style={styles.resourceCard} 
            activeOpacity={0.7}
            onPress={() => navigation.navigate('SecurityFAQ')}
          >
            <Ionicons name="help-circle" size={24} color={Colors.accent.gold} />
            <View style={styles.resourceText}>
              <Text style={styles.resourceTitle}>{t('securityFaq')}</Text>
              <Text style={styles.resourceDescription}>
                {t('findAnswersToCommonSecurityQuestions')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.resourceCard} 
            activeOpacity={0.7}
            onPress={() => navigation.navigate('SecurityTips')}
          >
            <Ionicons name="shield-checkmark" size={24} color={Colors.status.success} />
            <View style={styles.resourceText}>
              <Text style={styles.resourceTitle}>{t('securityTips')}</Text>
              <Text style={styles.resourceDescription}>
                {t('learnHowToKeepYourAccountSecure')}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  overviewCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overviewIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  overviewText: {
    flex: 1,
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  overviewDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
    textAlign: 'center',
  },
  quickActionDescription: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  supportCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  supportLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  supportIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  supportInfo: {
    flex: 1,
  },
  supportTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  supportSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  supportContact: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  supportDescription: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  supportRight: {
    alignItems: 'flex-end',
  },
  supportMeta: {
    alignItems: 'flex-end',
    marginBottom: 8,
  },
  supportAvailable: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.status.success,
    marginBottom: 2,
  },
  supportResponse: {
    fontSize: 11,
    color: Colors.neutral.warmGray,
  },
  categoryCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expandedCategoryCard: {
    borderWidth: 1,
    borderColor: Colors.primary.main + '30',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  categoryContent: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  categoryDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 12,
  },
  issueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  issueBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary.main,
    marginRight: 12,
  },
  issueText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
  },
  emergencyCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.status.error + '10',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.status.error + '30',
  },
  emergencyIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.status.error + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  emergencyText: {
    flex: 1,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.status.error,
    marginBottom: 8,
  },
  emergencyDescription: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
    marginBottom: 16,
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.status.error,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  emergencyButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
  hoursCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  hoursItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.lightGray,
  },
  hoursText: {
    marginLeft: 12,
    flex: 1,
  },
  hoursTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  hoursTime: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  resourceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resourceText: {
    flex: 1,
    marginLeft: 12,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  resourceDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
});

export default ContactSupportScreen;
