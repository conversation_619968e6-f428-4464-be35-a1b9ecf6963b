import 'react-native-gesture-handler';
import React, { useEffect, useState, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, ActivityIndicator, StyleSheet, StatusBar } from 'react-native';
import { ThemeProvider } from './contexts/ThemeContext';
import { useUserActivity } from './hooks/useUserActivity';
import EnhancedSplashScreen from './components/EnhancedSplashScreen';
import NavigationUtils from './utils/navigationUtils';
import OnboardingScreen from './screens/OnboardingScreen';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import CompleteProfileScreen from './screens/CompleteProfileScreen';
import ForgotPasswordScreen from './screens/ForgotPasswordScreen';
import FAQScreen from './screens/FAQScreen';

import AIChatScreen from './screens/AIChatScreen';
import WalletScreen from './screens/WalletScreen';
import TopUpScreen from './screens/TopUpScreen';
import TransactionHistoryScreen from './screens/TransactionHistoryScreen';
import AnalyticsScreen from './screens/AnalyticsScreen';
import BudgetInsightsScreen from './screens/BudgetInsightsScreen';
// Enhanced Analytics
import EnhancedDashboardScreen from './screens/EnhancedDashboardScreen';
import AnalyticsExportScreen from './screens/AnalyticsExportScreen';
// Smart Categorization
import CategoryManagementScreen from './screens/CategoryManagementScreen';
import BulkCategorizationScreen from './screens/BulkCategorizationScreen';
import SavingsScreen from './screens/SavingsScreen';
import AutomaticSavingsScreen from './screens/AutomaticSavingsScreen';
// New Savings & Investment Screens
import SavingsDashboardScreen from './screens/SavingsDashboardScreen';
import SavingsAccountCreationScreen from './screens/SavingsAccountCreationScreen';
import SavingsAccountDetailsScreen from './screens/SavingsAccountDetailsScreen';
import InvestmentDashboardScreen from './screens/InvestmentDashboardScreen';
import FinancialPlanningDashboardScreen from './screens/FinancialPlanningDashboardScreen';
import SavingsTestScreen from './screens/SavingsTestScreen';
// Additional Savings Screens
import SavingsGoalsScreen from './screens/SavingsGoalsScreen';
import SavingsTransfersScreen from './screens/SavingsTransfersScreen';
import SavingsReportsScreen from './screens/SavingsReportsScreen';
import SavingsAccountsListScreen from './screens/SavingsAccountsListScreen';
import SavingsAnalyticsScreen from './screens/SavingsAnalyticsScreen';
import SavingsReportSchedulingScreen from './screens/SavingsReportSchedulingScreen';
import FinancialGoalCreationScreen from './screens/FinancialGoalCreationScreen';
import FinancialGoalDetailsScreen from './screens/FinancialGoalDetailsScreen';
// Investment Screens
import InvestmentPortfolioCreationScreen from './screens/InvestmentPortfolioCreationScreen';
import InvestmentPortfolioDetailsScreen from './screens/InvestmentPortfolioDetailsScreen';
import MarketOverviewScreen from './screens/MarketOverviewScreen';
import AssetSearchScreen from './screens/AssetSearchScreen';
import AssetDetailsScreen from './screens/AssetDetailsScreen';
import InvestmentAnalyticsScreen from './screens/InvestmentAnalyticsScreen';
import InvestmentTransactionsScreen from './screens/InvestmentTransactionsScreen';
import InvestmentReportsScreen from './screens/InvestmentReportsScreen';
import InvestmentPortfoliosListScreen from './screens/InvestmentPortfoliosListScreen';
import BillsOptimizationScreen from './screens/BillsOptimizationScreen';
import BillPaymentScreen from './screens/BillPaymentScreen';
import BillProvidersScreen from './screens/BillProvidersScreen';
import BillDetailsScreen from './screens/BillDetailsScreen';
import BillAmountScreen from './screens/BillAmountScreen';
import BillConfirmationScreen from './screens/BillConfirmationScreen';
import BillSuccessScreen from './screens/BillSuccessScreen';
import WalletSettingsScreen from './screens/WalletSettingsScreen';
import SendMoneyScreen from './screens/SendMoneyScreen';
import RequestMoneyScreen from './screens/RequestMoneyScreen';
import RequestAmountScreen from './screens/RequestAmountScreen';
import RequestHistoryScreen from './screens/RequestHistoryScreen';
import ManualRequestRecipientScreen from './screens/ManualRequestRecipientScreen';
import TransferAmountScreen from './screens/TransferAmountScreen';
import TransferConfirmationScreen from './screens/TransferConfirmationScreen';
import TransferSuccessScreen from './screens/TransferSuccessScreen';
import ManualRecipientScreen from './screens/ManualRecipientScreen';
import QRScannerScreen from './screens/QRScannerScreen';
import QRGeneratorScreen from './screens/QRGeneratorScreen';
import SecuritySettingsScreen from './screens/SecuritySettingsScreen';
import PrivacyControlsScreen from './screens/PrivacyControlsScreen';
import TwoFactorAuthScreen from './screens/TwoFactorAuthScreen';
import SessionTimeoutScreen from './screens/SessionTimeoutScreen';
import TrustedDevicesScreen from './screens/TrustedDevicesScreen';
import SecurityActivityScreen from './screens/SecurityActivityScreen';
import SecurityTipsScreen from './screens/SecurityTipsScreen';
import SecurityPolicyScreen from './screens/SecurityPolicyScreen';
import SecurityFAQScreen from './screens/SecurityFAQScreen';
import ContactSupportScreen from './screens/ContactSupportScreen';
import CreateTicketScreen from './screens/CreateTicketScreen';
import NotificationScreen from './screens/NotificationScreen';
import AccountVerificationScreen from './screens/AccountVerificationScreen';
import DocumentUploadScreen from './screens/DocumentUploadScreen';
import VerificationStatusScreen from './screens/VerificationStatusScreen';
import VerificationLimitsScreen from './screens/VerificationLimitsScreen';
import EmailVerificationScreen from './screens/EmailVerificationScreen';
import ProfilePictureScreen from './screens/ProfilePictureScreen';
import EditProfileScreen from './screens/EditProfileScreen';
import PrivacyPolicyScreen from './screens/PrivacyPolicyScreen';
import DataProtectionScreen from './screens/DataProtectionScreen';
// Predictive Analytics & Budgeting Screens
import BudgetManagementScreen from './screens/BudgetManagementScreen';
import CreateBudgetScreen from './screens/CreateBudgetScreen';
import MainNavigator from './navigation/MainNavigator';

const Stack = createStackNavigator();

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isFirstLaunch, setIsFirstLaunch] = useState(false);

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      setIsLoading(false);
      setIsFirstLaunch(true);
    }, 2000);
  }, []);

  // Return a simple loading screen while theme initializes
  if (isLoading) {
    return (
      <View style={{ flex: 1, backgroundColor: '#FFFFFF' }}>
        <StatusBar barStyle="dark-content" />
      </View>
    );
  }

  return (
    <ThemeProvider>
      <NavigationContainer>
        <MainNavigator />
      </NavigationContainer>
    </ThemeProvider>
  );
}
