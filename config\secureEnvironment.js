/**
 * Secure Environment Configuration for JiraniPay
 * 
 * This file implements a secure multi-environment configuration system that:
 * 1. Uses environment variables exclusively (no hardcoded credentials)
 * 2. Supports separate Supabase projects for dev/staging/production
 * 3. Validates configuration at runtime
 * 4. Provides fallback handling for missing configurations
 * 
 * SECURITY PRINCIPLES:
 * - Never hardcode credentials in source code
 * - Use separate projects/databases for each environment
 * - Validate all configurations before use
 * - Fail securely if configuration is invalid
 */

import Constants from 'expo-constants';

// Environment detection
const getEnvironment = () => {
  // Check explicit environment setting first
  const explicitEnv = process.env.EXPO_PUBLIC_ENVIRONMENT || Constants.expoConfig?.extra?.environment;

  if (explicitEnv) {
    return explicitEnv.toLowerCase();
  }

  // Default to production for better security
  // Only use development if explicitly set
  const nodeEnv = process.env.NODE_ENV;
  if (nodeEnv === 'development') {
    return 'development';
  }

  // Default to production
  return 'production';
};

const CURRENT_ENVIRONMENT = getEnvironment();

/**
 * Environment-specific Supabase configuration
 * Each environment should have its own Supabase project
 */
const getSupabaseConfig = () => {
  let url, anonKey;
  
  switch (CURRENT_ENVIRONMENT) {
    case 'production':
      url = process.env.EXPO_PUBLIC_PROD_SUPABASE_URL;
      anonKey = process.env.EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY;
      break;
      
    case 'staging':
      url = process.env.EXPO_PUBLIC_STAGING_SUPABASE_URL;
      anonKey = process.env.EXPO_PUBLIC_STAGING_SUPABASE_ANON_KEY;
      break;
      
    case 'development':
    default:
      url = process.env.EXPO_PUBLIC_SUPABASE_URL;
      anonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;
      break;
  }
  
  return { url, anonKey };
};

/**
 * API endpoint configuration based on environment
 */
const getApiConfig = () => {
  switch (CURRENT_ENVIRONMENT) {
    case 'production':
      return {
        baseUrl: process.env.EXPO_PUBLIC_PROD_API_URL || 'https://api.jiranipay.com',
        timeout: 30000,
        retries: 3,
      };
      
    case 'staging':
      return {
        baseUrl: process.env.EXPO_PUBLIC_STAGING_API_URL || 'https://staging-api.jiranipay.com',
        timeout: 30000,
        retries: 2,
      };
      
    case 'development':
    default:
      return {
        baseUrl: process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:3001',
        timeout: 10000,
        retries: 1,
      };
  }
};

/**
 * Security configuration based on environment
 */
const getSecurityConfig = () => {
  const isProduction = CURRENT_ENVIRONMENT === 'production';
  
  return {
    enableBiometrics: isProduction,
    enablePinFallback: true,
    sessionTimeout: isProduction ? 15 * 60 * 1000 : 60 * 60 * 1000, // 15 min prod, 1 hour dev
    maxLoginAttempts: isProduction ? 3 : 10,
    enableDeviceBinding: isProduction,
    enableCertificatePinning: isProduction,
    enableJailbreakDetection: isProduction,
  };
};

/**
 * Feature flags based on environment
 */
const getFeatureFlags = () => {
  const isProduction = CURRENT_ENVIRONMENT === 'production';
  const isDevelopment = CURRENT_ENVIRONMENT === 'development';
  
  return {
    enableDebugMode: isDevelopment,
    enableMockServices: isDevelopment,
    enableTestData: isDevelopment,
    enableAnalytics: isProduction,
    enableCrashReporting: isProduction,
    enablePerformanceMonitoring: isProduction,
    enableRealTimeFeatures: true,
    enableOfflineMode: true,
  };
};

/**
 * Logging configuration based on environment
 */
const getLoggingConfig = () => {
  switch (CURRENT_ENVIRONMENT) {
    case 'production':
      return {
        level: 'error',
        enableConsoleLogging: false,
        enableRemoteLogging: true,
        enableSensitiveDataLogging: false,
      };
      
    case 'staging':
      return {
        level: 'warn',
        enableConsoleLogging: true,
        enableRemoteLogging: true,
        enableSensitiveDataLogging: false,
      };
      
    case 'development':
    default:
      return {
        level: 'debug',
        enableConsoleLogging: true,
        enableRemoteLogging: false,
        enableSensitiveDataLogging: true,
      };
  }
};

/**
 * Configuration validation
 */
const validateConfiguration = () => {
  const errors = [];
  const supabaseConfig = getSupabaseConfig();
  
  // Validate Supabase configuration
  if (!supabaseConfig.url) {
    errors.push(`Missing Supabase URL for ${CURRENT_ENVIRONMENT} environment`);
  } else if (!supabaseConfig.url.startsWith('https://')) {
    errors.push(`Invalid Supabase URL format for ${CURRENT_ENVIRONMENT} environment`);
  }
  
  if (!supabaseConfig.anonKey) {
    errors.push(`Missing Supabase anon key for ${CURRENT_ENVIRONMENT} environment`);
  } else if (!supabaseConfig.anonKey.startsWith('eyJ')) {
    errors.push(`Invalid Supabase anon key format for ${CURRENT_ENVIRONMENT} environment`);
  }
  
  // Validate API configuration
  const apiConfig = getApiConfig();
  if (!apiConfig.baseUrl) {
    errors.push(`Missing API base URL for ${CURRENT_ENVIRONMENT} environment`);
  }
  
  return errors;
};

/**
 * Main configuration object
 */
const secureConfig = {
  environment: CURRENT_ENVIRONMENT,
  supabase: getSupabaseConfig(),
  api: getApiConfig(),
  security: getSecurityConfig(),
  features: getFeatureFlags(),
  logging: getLoggingConfig(),
  
  // Validation method
  validate: validateConfiguration,
  
  // Environment checks
  isProduction: () => CURRENT_ENVIRONMENT === 'production',
  isStaging: () => CURRENT_ENVIRONMENT === 'staging',
  isDevelopment: () => CURRENT_ENVIRONMENT === 'development',
};

// Validate configuration on import
const configErrors = validateConfiguration();
if (configErrors.length > 0) {
  console.error('❌ Configuration validation failed:');
  configErrors.forEach(error => console.error(`  - ${error}`));

  if (CURRENT_ENVIRONMENT === 'production') {
    console.warn('⚠️ Production configuration issues detected. Using fallback configuration.');
    // Don't throw error in production, use fallback
  } else {
    console.warn('⚠️ Configuration issues detected. Some features may not work correctly.');
  }
} else {
  console.log(`✅ Configuration validated successfully for ${CURRENT_ENVIRONMENT} environment`);
}

// Log current configuration for debugging
console.log('🔧 Current Environment Configuration:', {
  environment: CURRENT_ENVIRONMENT,
  supabaseUrl: secureConfig.supabase.url ? secureConfig.supabase.url.substring(0, 30) + '...' : 'NOT SET',
  supabaseKey: secureConfig.supabase.anonKey ? 'SET (' + secureConfig.supabase.anonKey.length + ' chars)' : 'NOT SET',
  apiBaseUrl: secureConfig.api.baseUrl,
  isProduction: secureConfig.isProduction(),
  isDevelopment: secureConfig.isDevelopment()
});

export default secureConfig;
export { 
  getSupabaseConfig, 
  getApiConfig, 
  getSecurityConfig, 
  getFeatureFlags, 
  getLoggingConfig,
  validateConfiguration,
  CURRENT_ENVIRONMENT 
};
