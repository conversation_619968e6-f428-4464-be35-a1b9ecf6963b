/**
 * Recurring Payment Setup Screen
 * Comprehensive setup flow for creating recurring bill payments
 * with frequency selection, date configuration, and validation
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  TextInput,
  Switch,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import recurringPaymentsService from '../services/recurringPaymentsService';
import billerManagementService from '../services/billerManagementService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const RecurringPaymentSetupScreen = ({ navigation, route }) => {
  const { billerId, billerName, accountNumber, accountName } = route.params || {};
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    amount: '',
    frequency: 'monthly',
    startDate: new Date(),
    endDate: null,
    hasEndDate: false,
    paymentMethod: 'wallet',
    isActive: true,
    reminderEnabled: true,
    reminderDays: 1
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [biller, setBiller] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('start');
  const [showFrequencyModal, setShowFrequencyModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  // Frequency options
  const frequencyOptions = [
    { key: 'daily', label: 'Daily', icon: 'calendar', description: 'Every day' },
    { key: 'weekly', label: 'Weekly', icon: 'calendar-outline', description: 'Every week' },
    { key: 'monthly', label: 'Monthly', icon: 'calendar', description: 'Every month' },
    { key: 'quarterly', label: 'Quarterly', icon: 'calendar-outline', description: 'Every 3 months' },
    { key: 'yearly', label: 'Yearly', icon: 'calendar', description: 'Every year' }
  ];

  useEffect(() => {
    loadBillerDetails();
    generateDefaultName();
  }, [billerId]);

  const loadBillerDetails = async () => {
    if (!billerId) return;

    try {
      const result = await billerManagementService.getBillerDetails(billerId);
      if (result.success) {
        setBiller(result.biller);
      }
    } catch (error) {
      console.error('❌ Error loading biller details:', error);
    }
  };

  const generateDefaultName = () => {
    if (billerName) {
      const frequency = formData.frequency.charAt(0).toUpperCase() + formData.frequency.slice(1);
      setFormData(prev => ({
        ...prev,
        name: `${frequency} ${billerName} Payment`
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = 'Payment name is required';
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      errors.amount = 'Valid amount is required';
    }

    if (biller) {
      const amount = parseFloat(formData.amount) || 0;
      if (amount < biller.minAmount) {
        errors.amount = `Minimum amount is ${formatCurrency(biller.minAmount)}`;
      }
      if (amount > biller.maxAmount) {
        errors.amount = `Maximum amount is ${formatCurrency(biller.maxAmount)}`;
      }
    }

    if (formData.startDate < new Date()) {
      errors.startDate = 'Start date cannot be in the past';
    }

    if (formData.hasEndDate && formData.endDate && formData.endDate <= formData.startDate) {
      errors.endDate = 'End date must be after start date';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSetupRecurringPayment = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before proceeding');
      return;
    }

    try {
      setLoading(true);
      const userId = await requireAuthentication('setup recurring payment');
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to set up recurring payments');
        return;
      }

      const recurringPaymentData = {
        userId,
        billerId,
        accountNumber,
        accountName,
        name: formData.name,
        amount: parseFloat(formData.amount),
        currency: 'UGX',
        frequency: formData.frequency,
        startDate: formData.startDate.toISOString(),
        endDate: formData.hasEndDate ? formData.endDate?.toISOString() : null,
        paymentMethod: formData.paymentMethod,
        isActive: formData.isActive,
        reminderEnabled: formData.reminderEnabled,
        reminderDays: formData.reminderDays
      };

      const result = await recurringPaymentsService.createRecurringPayment(recurringPaymentData);

      if (result.success) {
        Alert.alert(
          'Success',
          'Recurring payment has been set up successfully!',
          [
            {
              text: 'View Recurring Payments',
              onPress: () => navigation.navigate('RecurringPayments')
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Setup Failed', result.error || 'Failed to set up recurring payment');
      }
    } catch (error) {
      console.error('❌ Error setting up recurring payment:', error);
      Alert.alert('Error', 'Failed to set up recurring payment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (event, selectedDate) => {
    setShowDatePicker(false);
    if (selectedDate) {
      if (datePickerMode === 'start') {
        setFormData(prev => ({ ...prev, startDate: selectedDate }));
      } else {
        setFormData(prev => ({ ...prev, endDate: selectedDate }));
      }
    }
  };

  const showDatePickerModal = (mode) => {
    setDatePickerMode(mode);
    setShowDatePicker(true);
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Regenerate name when frequency changes
    if (field === 'frequency') {
      generateDefaultName();
    }
  };

  const renderBillerInfo = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Payment Details</Text>
      
      <View style={styles.billerCard}>
        <View style={styles.billerHeader}>
          <View style={[styles.billerIcon, { backgroundColor: biller?.category?.color || theme.colors.primary }]}>
            <Ionicons 
              name={getBillerIcon(biller?.category?.name)} 
              size={24} 
              color={theme.colors.white} 
            />
          </View>
          <View style={styles.billerDetails}>
            <Text style={styles.billerName}>{billerName}</Text>
            <Text style={styles.billerCategory}>{biller?.category?.displayName}</Text>
          </View>
        </View>
        
        <View style={styles.accountInfo}>
          <Text style={styles.accountLabel}>Account Number</Text>
          <Text style={styles.accountValue}>{accountNumber}</Text>
          {accountName && (
            <>
              <Text style={styles.accountLabel}>Account Name</Text>
              <Text style={styles.accountValue}>{accountName}</Text>
            </>
          )}
        </View>
      </View>
    </View>
  );

  const renderBasicSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Basic Settings</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Payment Name</Text>
        <TextInput
          style={[styles.textInput, validationErrors.name && styles.inputError]}
          value={formData.name}
          onChangeText={(value) => updateFormData('name', value)}
          placeholder="Enter payment name"
          placeholderTextColor={theme.colors.textSecondary}
        />
        {validationErrors.name && (
          <Text style={styles.errorText}>{validationErrors.name}</Text>
        )}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Amount</Text>
        <TextInput
          style={[styles.textInput, validationErrors.amount && styles.inputError]}
          value={formData.amount}
          onChangeText={(value) => updateFormData('amount', value)}
          placeholder="Enter amount"
          placeholderTextColor={theme.colors.textSecondary}
          keyboardType="numeric"
        />
        {validationErrors.amount && (
          <Text style={styles.errorText}>{validationErrors.amount}</Text>
        )}
        {biller && (
          <Text style={styles.helperText}>
            Range: {formatCurrency(biller.minAmount)} - {formatCurrency(biller.maxAmount)}
          </Text>
        )}
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Frequency</Text>
        <TouchableOpacity 
          style={styles.selectButton}
          onPress={() => setShowFrequencyModal(true)}
        >
          <Text style={styles.selectButtonText}>
            {frequencyOptions.find(f => f.key === formData.frequency)?.label}
          </Text>
          <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderScheduleSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Schedule Settings</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Start Date</Text>
        <TouchableOpacity 
          style={[styles.selectButton, validationErrors.startDate && styles.inputError]}
          onPress={() => showDatePickerModal('start')}
        >
          <Text style={styles.selectButtonText}>{formatDate(formData.startDate)}</Text>
          <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>
        {validationErrors.startDate && (
          <Text style={styles.errorText}>{validationErrors.startDate}</Text>
        )}
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Set End Date</Text>
        <Switch
          value={formData.hasEndDate}
          onValueChange={(value) => updateFormData('hasEndDate', value)}
          trackColor={{ false: theme.colors.border, true: theme.colors.primary + '40' }}
          thumbColor={formData.hasEndDate ? theme.colors.primary : theme.colors.textSecondary}
        />
      </View>

      {formData.hasEndDate && (
        <View style={styles.inputGroup}>
          <Text style={styles.inputLabel}>End Date</Text>
          <TouchableOpacity 
            style={[styles.selectButton, validationErrors.endDate && styles.inputError]}
            onPress={() => showDatePickerModal('end')}
          >
            <Text style={styles.selectButtonText}>
              {formData.endDate ? formatDate(formData.endDate) : 'Select end date'}
            </Text>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
          {validationErrors.endDate && (
            <Text style={styles.errorText}>{validationErrors.endDate}</Text>
          )}
        </View>
      )}
    </View>
  );

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Set Up Recurring Payment</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderBillerInfo()}
        {renderBasicSettings()}
        {renderScheduleSettings()}
      </ScrollView>

      {/* Setup Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.setupButton, loading && styles.setupButtonDisabled]}
          onPress={handleSetupRecurringPayment}
          disabled={loading}
        >
          <Text style={styles.setupButtonText}>
            {loading ? 'Setting Up...' : 'Set Up Recurring Payment'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Date Picker Modal */}
      {showDatePicker && (
        <DateTimePicker
          value={datePickerMode === 'start' ? formData.startDate : (formData.endDate || new Date())}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={datePickerMode === 'start' ? new Date() : formData.startDate}
        />
      )}

      {/* Frequency Selection Modal */}
      <Modal
        visible={showFrequencyModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFrequencyModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFrequencyModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Frequency</Text>
            <TouchableOpacity onPress={() => setShowFrequencyModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {frequencyOptions.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.frequencyOption,
                  formData.frequency === option.key && styles.frequencyOptionSelected
                ]}
                onPress={() => updateFormData('frequency', option.key)}
              >
                <View style={styles.frequencyOptionContent}>
                  <Ionicons 
                    name={option.icon} 
                    size={24} 
                    color={formData.frequency === option.key ? theme.colors.primary : theme.colors.textSecondary} 
                  />
                  <View style={styles.frequencyOptionText}>
                    <Text style={[
                      styles.frequencyOptionLabel,
                      formData.frequency === option.key && styles.frequencyOptionLabelSelected
                    ]}>
                      {option.label}
                    </Text>
                    <Text style={styles.frequencyOptionDescription}>{option.description}</Text>
                  </View>
                </View>
                {formData.frequency === option.key && (
                  <Ionicons name="checkmark" size={24} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  billerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
  },
  billerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  billerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  billerDetails: {
    flex: 1,
  },
  billerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerCategory: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  accountInfo: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
  },
  accountLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  accountValue: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 12,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: 12,
    color: theme.colors.error,
    marginTop: 4,
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  selectButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectButtonText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  setupButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  setupButtonDisabled: {
    opacity: 0.7,
  },
  setupButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  frequencyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  frequencyOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  frequencyOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  frequencyOptionText: {
    marginLeft: 12,
    flex: 1,
  },
  frequencyOptionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  frequencyOptionLabelSelected: {
    color: theme.colors.primary,
  },
  frequencyOptionDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default RecurringPaymentSetupScreen;
