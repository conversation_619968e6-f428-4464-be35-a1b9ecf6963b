import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const FAQScreen = ({ navigation }) => {
  // Use theme and language contexts
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [expandedItems, setExpandedItems] = useState({});

  const faqData = [
    {
      id: 1,
      category: t('support.moneyTransfer'),
      icon: 'send-outline',
      color: Colors.primary.main,
      questions: [
        {
          question: t('support.howDoISendMoney'),
          answer: t('support.sendMoneyAnswer')
        },
        {
          question: t('support.whatAreTransferLimits'),
          answer: t('support.transferLimitsAnswer')
        },
        {
          question: t('support.howLongDoTransfersTake'),
          answer: t('support.transferTimeAnswer')
        }
      ]
    },
    {
      id: 2,
      category: t('support.billPayments'),
      icon: 'receipt-outline',
      color: Colors.secondary.savanna,
      questions: [
        {
          question: t('support.whichBillsCanIPay'),
          answer: t('support.billPaymentsAnswer')
        },
        {
          question: t('support.howDoIPayUtilityBills'),
          answer: t('support.payUtilityBillsAnswer')
        },
        {
          question: t('support.canIScheduleRecurringPayments'),
          answer: t('support.recurringPaymentsAnswer')
        }
      ]
    },
    {
      id: 3,
      category: t('support.walletManagement'),
      icon: 'wallet-outline',
      color: Colors.accent.coral,
      questions: [
        {
          question: t('support.howDoIAddMoney'),
          answer: t('support.addMoneyAnswer')
        },
        {
          question: t('support.isMyMoneySafe'),
          answer: t('support.moneySafetyAnswer')
        },
        {
          question: t('support.canIWithdrawMoney'),
          answer: t('support.withdrawMoneyAnswer')
        }
      ]
    },
    {
      id: 4,
      category: t('support.qrCodeScanner'),
      icon: 'qr-code-outline',
      color: Colors.secondary.lake,
      questions: [
        {
          question: t('support.howDoIUseQRScanner'),
          answer: t('support.qrScannerAnswer')
        },
        {
          question: t('support.whatCanIDoWithQRCodes'),
          answer: t('support.qrCodeUsesAnswer')
        },
        {
          question: t('support.howDoIGenerateQRCode'),
          answer: t('support.generateQRAnswer')
        }
      ]
    },
    {
      id: 5,
      category: t('support.accountSecurity'),
      icon: 'shield-checkmark-outline',
      color: Colors.secondary.earth,
      questions: [
        {
          question: t('support.howDoIVerifyAccount'),
          answer: t('support.verifyAccountAnswer')
        },
        {
          question: t('support.whatSecurityFeatures'),
          answer: t('support.securityFeaturesAnswer')
        },
        {
          question: t('support.whatIfISuspectFraud'),
          answer: t('support.fraudAnswer')
        }
      ]
    }
  ];

  const toggleExpanded = (categoryId, questionIndex) => {
    const key = `${categoryId}-${questionIndex}`;
    setExpandedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const renderFAQCategory = (category) => (
    <View key={category.id} style={styles.categoryContainer}>
      <View style={styles.categoryHeader}>
        <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
          <Ionicons name={category.icon} size={24} color={theme.colors.white} />
        </View>
        <Text style={styles.categoryTitle}>{category.category}</Text>
      </View>

      {category.questions.map((item, index) => {
        const key = `${category.id}-${index}`;
        const isExpanded = expandedItems[key];

        return (
          <TouchableOpacity
            key={index}
            style={styles.questionContainer}
            onPress={() => toggleExpanded(category.id, index)}
          >
            <View style={styles.questionHeader}>
              <Text style={styles.questionText}>{item.question}</Text>
              <Ionicons
                name={isExpanded ? 'chevron-up' : 'chevron-down'}
                size={20}
                color={theme.colors.textSecondary}
              />
            </View>
            {isExpanded && (
              <View style={styles.answerContainer}>
                <Text style={styles.answerText}>{item.answer}</Text>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('support.frequentlyAskedQuestions')}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.introContainer}>
          <Text style={styles.introTitle}>{t('support.howCanWeHelpYou')}</Text>
          <Text style={styles.introText}>
            {t('support.findAnswersToCommonQuestionsAboutUsingJiranipayIfY')}
          </Text>
        </View>

        {faqData.map(renderFAQCategory)}

        <View style={styles.contactContainer}>
          <Text style={styles.contactTitle}>{t('support.stillNeedHelp')}</Text>
          <Text style={styles.contactText}>
            {t('support.ourCustomerSupportTeamIsAvailable247ToAssistYou')}
          </Text>
          <TouchableOpacity
            style={styles.contactButton}
            onPress={() => navigation.navigate('ContactSupport')}
            activeOpacity={0.7}
          >
            <Ionicons name="call" size={20} color={theme.colors.white} />
            <Text style={styles.contactButtonText}>{t('support.contactSupport')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  introContainer: {
    padding: 20,
    backgroundColor: theme.colors.surface,
    marginBottom: 16,
  },
  introTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  introText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    lineHeight: 24,
  },
  categoryContainer: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme.colors.inputBackground,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  questionContainer: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  questionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginRight: 12,
  },
  answerContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  answerText: {
    fontSize: 15,
    color: theme.colors.textSecondary,
    lineHeight: 22,
  },
  contactContainer: {
    backgroundColor: theme.colors.surface,
    margin: 16,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  contactText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  contactButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default FAQScreen;
