# One-by-One Table Creation

If the emergency fix still fails, create tables one at a time:

## Step 1: Create Wallets Table Only

```sql
CREATE TABLE public.wallets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    balance NUMERIC DEFAULT 0,
    currency TEXT DEFAULT 'UGX'
);
```

**Run this first. If it works, continue to Step 2.**

## Step 2: Test Wallets Table

```sql
INSERT INTO public.wallets (user_id, balance) VALUES ('test-123', 1000);
SELECT * FROM public.wallets;
```

**If this works, continue to Step 3.**

## Step 3: Create Transactions Table

```sql
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    description TEXT
);
```

## Step 4: Create User Profiles Table

```sql
CREATE TABLE public.user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    phone_number TEXT,
    full_name TEXT
);
```

## Step 5: Final Test

```sql
-- Test all tables
SELECT 'wallets' as table_name, COUNT(*) as records FROM public.wallets
UNION ALL
SELECT 'transactions' as table_name, COUNT(*) as records FROM public.transactions
UNION ALL
SELECT 'user_profiles' as table_name, COUNT(*) as records FROM public.user_profiles;
```

## If ANY Step Fails

**Just skip the database setup entirely!** 

Your JiraniPay app is designed to work perfectly without any database tables. It will automatically use mock data that provides the exact same user experience.

## Alternative: Use Supabase Table Editor

1. Go to **Table Editor** in Supabase Dashboard
2. Click **"New Table"**
3. Create table named: `wallets`
4. Add columns:
   - `id` (uuid, primary key, default: uuid_generate_v4())
   - `user_id` (text, required)
   - `balance` (numeric, default: 0)
   - `currency` (text, default: 'UGX')
5. Save table
6. Repeat for `transactions` and `user_profiles`
