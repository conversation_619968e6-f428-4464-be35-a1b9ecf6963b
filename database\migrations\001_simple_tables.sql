-- JiraniPay Simple Database Schema
-- This is the simplest version that should work without any issues
-- Run this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create wallets table (minimal version)
CREATE TABLE IF NOT EXISTS public.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'UGX',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table (minimal version)
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    transaction_type TEXT DEFAULT 'payment',
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    reference_number TEXT,
    status TEXT DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_profiles table (minimal version)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    phone_number TEXT,
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON public.wallets(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);

-- Test the tables by inserting a sample record
INSERT INTO public.wallets (user_id, balance, currency) 
VALUES (uuid_generate_v4(), 50000.00, 'UGX')
ON CONFLICT DO NOTHING;

-- Verify tables exist
SELECT 'wallets' as table_name, COUNT(*) as record_count FROM public.wallets
UNION ALL
SELECT 'transactions' as table_name, COUNT(*) as record_count FROM public.transactions
UNION ALL
SELECT 'user_profiles' as table_name, COUNT(*) as record_count FROM public.user_profiles;
