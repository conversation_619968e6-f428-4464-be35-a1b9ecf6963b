import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { isProductionMode } from '../config/environment';
import UnifiedBackButton from '../components/UnifiedBackButton';
import notificationService from '../services/notificationService';

const NotificationScreen = ({ navigation }) => {
  // Safe theme access with fallback
  const themeContext = useTheme();
  const theme = themeContext?.theme || {
    colors: {
      primary: '#E67E22',
      background: '#fcf7f0',
      surface: '#fcf7f0',
      text: '#2C3E50',
      textSecondary: '#95A5A6',
      border: '#BDC3C7',
      success: '#27AE60',
      warning: '#F39C12',
      error: '#C0392B',
      shadow: '#000000'
    }
  };
  
  // Debug Colors import (moved after theme initialization)
  console.log('🎨 Colors.primary:', Colors.primary);
  console.log('🎨 theme.colors.primary:', theme.colors.primary);

  // Create styles with safe theme access
  const styles = createStyles(theme);

  const [notifications, setNotifications] = useState([]);
  const [preferences, setPreferences] = useState({
    transactions: true,
    security: true,
    marketing: false,
    system: true,
  });

  useEffect(() => {
    loadNotifications();
    loadPreferences();
  }, []);

  const loadNotifications = async () => {
    try {
      console.log('📱 Loading notifications from enhanced service...');
      console.log(`🔒 Production mode: ${isProductionMode()}`);

      // Load real notifications from the enhanced notification service
      const realNotifications = await notificationService.loadInAppNotifications();

      if (realNotifications && realNotifications.length > 0) {
        console.log(`📱 Loaded ${realNotifications.length} real notifications`);
        // Convert service notifications to UI format
        const formattedNotifications = realNotifications.map(notification => ({
          id: notification.id,
          title: notification.title,
          message: notification.message,
          time: new Date(notification.timestamp).toLocaleString(),
          type: notification.type || 'system',
          read: notification.read || false,
          icon: getNotificationIcon(notification.type),
          color: getNotificationColor(notification.type),
        }));
        setNotifications(formattedNotifications);
      } else {
        console.log('📱 No real notifications found');

        // Load notifications from service
        console.log('📱 Loading user notifications');

        // Try to load real notifications first
        try {
          const realNotifications = await notificationService.getUserNotifications(currentUser.id);
          if (realNotifications && realNotifications.length > 0) {
            setNotifications(realNotifications);
            return;
          }
        } catch (error) {
          console.log('⚠️ Could not load notifications, showing empty state');
        }

        // Show empty state if no notifications
        setNotifications([]);
      }
    } catch (error) {
      console.error('❌ Error loading notifications:', error);
      setNotifications([]);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'transaction': return 'card';
      case 'security': return 'shield-checkmark';
      case 'system': return 'information-circle';
      default: return 'notifications';
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case 'transaction': return Colors.status.success;
      case 'security': return Colors.status.warning;
      case 'system': return theme.colors.primary;
      default: return Colors.neutral.warmGray;
    }
  };

  const loadPreferences = async () => {
    try {
      console.log('📱 Loading notification preferences from service...');
      const userSettings = await notificationService.getUserSettings();
      if (userSettings) {
        setPreferences({
          transactions: userSettings.transactionNotifications,
          security: userSettings.lowBalanceAlerts,
          marketing: false, // Keep marketing disabled by default
          system: userSettings.spendingLimitAlerts,
        });
        console.log('✅ Notification preferences loaded:', userSettings);
      }
    } catch (error) {
      console.error('❌ Error loading notification preferences:', error);
    }
  };

  const markAsRead = (notificationId) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const clearAllNotifications = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Clear All', 
          style: 'destructive',
          onPress: () => setNotifications([])
        },
      ]
    );
  };

  const togglePreference = async (type, value) => {
    try {
      setPreferences(prev => ({
        ...prev,
        [type]: value
      }));

      // Save to notification service
      const settingsUpdate = {};
      if (type === 'transactions') settingsUpdate.transactionNotifications = value;
      if (type === 'security') settingsUpdate.lowBalanceAlerts = value;
      if (type === 'system') settingsUpdate.spendingLimitAlerts = value;

      await notificationService.updateUserSettings(settingsUpdate);
      console.log(`✅ ${type} notifications ${value ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('❌ Error updating notification preference:', error);
      // Revert the change if save failed
      setPreferences(prev => ({
        ...prev,
        [type]: !value
      }));
    }
  };

  const renderNotificationItem = (notification) => (
    <TouchableOpacity
      key={notification.id}
      style={[
        styles.notificationItem,
        { backgroundColor: theme.colors.surface },
        !notification.read && styles.unreadNotification
      ]}
      onPress={() => markAsRead(notification.id)}
    >
      <View style={styles.notificationLeft}>
        <View style={[styles.notificationIcon, { backgroundColor: notification.color }]}>
          <Ionicons name={notification.icon} size={20} color={Colors.neutral.white} />
        </View>
        <View style={styles.notificationContent}>
          <Text style={[styles.notificationTitle, { color: theme.colors.text }]}>
            {notification.title}
          </Text>
          <Text style={[styles.notificationMessage, { color: theme.colors.textSecondary }]}>
            {notification.message}
          </Text>
          <Text style={[styles.notificationTime, { color: theme.colors.textSecondary }]}>
            {notification.time}
          </Text>
        </View>
      </View>
      {!notification.read && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );

  const renderPreferenceItem = (title, subtitle, type, icon, color) => (
    <View style={[styles.preferenceItem, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.preferenceLeft}>
        <View style={[styles.preferenceIcon, { backgroundColor: color }]}>
          <Ionicons name={icon} size={20} color={Colors.neutral.white} />
        </View>
        <View>
          <Text style={[styles.preferenceTitle, { color: theme.colors.text }]}>
            {title}
          </Text>
          <Text style={[styles.preferenceSubtitle, { color: theme.colors.textSecondary }]}>
            {subtitle}
          </Text>
        </View>
      </View>
      <Switch
        value={preferences[type]}
        onValueChange={(value) => togglePreference(type, value)}
        trackColor={{ false: theme.colors.border, true: theme.colors.primary + '40' }}
        thumbColor={preferences[type] ? theme.colors.primary : Colors.neutral.white}
      />
    </View>
  );

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <UnifiedBackButton navigation={navigation} />
            <Text style={styles.headerTitle}>Notifications</Text>
            {unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
              </View>
            )}
          </View>
          <View style={styles.headerActions}>
            {notifications.length > 0 && (
              <>
                <TouchableOpacity style={styles.actionButton} onPress={markAllAsRead}>
                  <Text style={styles.actionText}>Mark All Read</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton} onPress={clearAllNotifications}>
                  <Text style={styles.actionText}>Clear All</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Recent Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent</Text>
          {notifications.length > 0 ? (
            notifications.map(renderNotificationItem)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons
                name="notifications-off-outline"
                size={64}
                color={theme.colors.textSecondary}
                style={styles.emptyIcon}
              />
              <Text style={[styles.emptyTitle, { color: theme.colors.text }]}>No Notifications</Text>
              <Text style={[styles.emptyMessage, { color: theme.colors.textSecondary }]}>
                {isProductionMode()
                  ? "You're all caught up! New notifications will appear here when you receive payments, security alerts, or system updates."
                  : "You're all caught up! New notifications will appear here."
                }
              </Text>
              {isProductionMode() && (
                <View style={styles.emptyHint}>
                  <Text style={[styles.emptyHintText, { color: theme.colors.textMuted }]}>
                    💡 Enable notification preferences below to receive alerts
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>

        {/* Notification Preferences */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notification Preferences</Text>
          {renderPreferenceItem(
            'Transaction Alerts',
            'Get notified about payments and transfers',
            'transactions',
            'card-outline',
            Colors.status.success
          )}
          {renderPreferenceItem(
            'Security Alerts',
            'Important security and login notifications',
            'security',
            'shield-checkmark-outline',
            Colors.status.warning
          )}
          {renderPreferenceItem(
            'System Updates',
            'App updates and maintenance notifications',
            'system',
            'settings-outline',
            theme.colors.primary
          )}
          {renderPreferenceItem(
            'Marketing & Promotions',
            'Special offers and feature announcements',
            'marketing',
            'megaphone-outline',
            Colors.secondary.heritage
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginLeft: 16,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    marginLeft: 16,
    padding: 8,
  },
  actionText: {
    color: theme.colors.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  unreadBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  unreadBadgeText: {
    color: Colors.neutral.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
    marginHorizontal: 20,
    marginTop: 20,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  unreadNotification: {
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  notificationLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 12,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  preferenceLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  preferenceIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  preferenceSubtitle: {
    fontSize: 14,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyMessage: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyHint: {
    marginTop: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  emptyHintText: {
    fontSize: 12,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default NotificationScreen;

