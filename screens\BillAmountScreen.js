import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  StatusBar,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const { width } = Dimensions.get('window');

const BillAmountScreen = ({ navigation, route }) => {
  const { category, provider, billDetails } = route.params;
  const [selectedAmount, setSelectedAmount] = useState(null);
  const [customAmount, setCustomAmount] = useState('');
  const { theme } = useTheme();
  const { convertAndFormat, getCurrencySymbol } = useCurrencyContext();
  const styles = createStyles(theme);

  // Get service type for mobile providers
  const serviceType = billDetails?.serviceType;

  // Predefined amounts in UGX - optimized for Uganda market
  const getPredefinedAmounts = () => {
    // Different amounts for different service types
    if (provider?.type === 'mobile') {
      if (serviceType === 'airtime') {
        return [
          { id: '5k', label: '5K', value: 5000, description: 'Basic airtime' },
          { id: '10k', label: '10K', value: 10000, description: 'Standard top-up' },
          { id: '20k', label: '20K', value: 20000, description: 'Popular choice' },
          { id: '50k', label: '50K', value: 50000, description: 'Heavy usage' },
        ];
      } else if (serviceType === 'data') {
        return [
          { id: '10k', label: '10K', value: 10000, description: 'Daily bundle' },
          { id: '25k', label: '25K', value: 25000, description: 'Weekly bundle' },
          { id: '50k', label: '50K', value: 50000, description: 'Monthly bundle' },
          { id: '100k', label: '100K', value: 100000, description: 'Heavy data' },
        ];
      }
    }

    // Default amounts for other bill types
    return [
      { id: '20k', label: '20K', value: 20000, description: 'Small bill' },
      { id: '50k', label: '50K', value: 50000, description: 'Medium bill' },
      { id: '100k', label: '100K', value: 100000, description: 'Large bill' },
      { id: '200k', label: '200K', value: 200000, description: 'Extra large' },
    ];
  };

  const predefinedAmounts = getPredefinedAmounts();

  const handleAmountSelect = (amount) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (text) => {
    setCustomAmount(text);
    setSelectedAmount(null);
  };

  const getSelectedAmountValue = () => {
    if (selectedAmount) return selectedAmount.value;
    if (customAmount) return parseFloat(customAmount.replace(/,/g, ''));
    return 0;
  };

  const handleNext = () => {
    const amount = getSelectedAmountValue();
    
    if (!amount || amount <= 0) {
      Alert.alert('Error', 'Please select or enter an amount');
      return;
    }

    if (amount < 1000) {
      Alert.alert('Error', `Minimum amount is ${convertAndFormat(1000)}`);
      return;
    }

    if (amount > 10000000) {
      Alert.alert('Error', `Maximum amount is ${convertAndFormat(10000000)}`);
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('BillConfirmation', {
      category,
      provider,
      billDetails,
      amount: amount
    });
  };

  const formatAmount = (amount) => {
    // Use consistent UGX formatting pattern from the app
    const numAmount = parseFloat(amount) || 0;
    return numAmount.toLocaleString('en-UG', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
  };

  // Convert UGX amounts to user's preferred currency and format
  const formatCurrencyFromUGX = (ugxAmount) => {
    try {
      const currencyService = require('../services/currencyService').default;
      // Get user's preferred currency from user preferences or default to UGX
      const targetCurrency = 'UGX'; // For now, bills are in UGX only
      return currencyService.convertFromUGXAndFormat(parseFloat(ugxAmount), targetCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false
      });
    } catch (error) {
      console.error('❌ Error converting currency from UGX:', error);
      // Fallback to UGX formatting
      return `UGX ${formatAmount(ugxAmount)}`;
    }
  };

  const renderAmountButton = (amount) => (
    <TouchableOpacity
      key={amount.id}
      style={[
        styles.amountButton,
        selectedAmount?.id === amount.id && styles.selectedAmountButton
      ]}
      onPress={() => handleAmountSelect(amount)}
      activeOpacity={0.7}
    >
      <View style={styles.amountButtonContent}>
        <Text style={[
          styles.amountLabel,
          selectedAmount?.id === amount.id && styles.selectedAmountLabel
        ]}>
          {amount.label}
        </Text>
        <Text style={[
          styles.amountValue,
          selectedAmount?.id === amount.id && styles.selectedAmountValue
        ]}>
          {convertAndFormat(amount.value)}
        </Text>
        <Text style={[
          styles.amountDescription,
          selectedAmount?.id === amount.id && styles.selectedAmountDescription
        ]}>
          {amount.description}
        </Text>
      </View>
      {selectedAmount?.id === amount.id && (
        <View style={styles.selectedIndicator}>
          <Ionicons name="checkmark-circle" size={20} color={Colors.neutral.white} />
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme.statusBar === 'dark' ? 'dark-content' : 'light-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Header */}
      <LinearGradient
        colors={['#E67E22', '#D35400']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>Pay Bill</Text>
          <View style={styles.headerSpacer} />
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Provider Info */}
          <View style={styles.providerInfo}>
            <Text style={styles.providerName}>{provider.name}</Text>
            {provider?.type === 'mobile' && serviceType && (
              <Text style={styles.serviceTypeInfo}>
                {serviceType === 'airtime' ? 'Airtime Top-up' : 'Data Bundles'}
              </Text>
            )}
            <Text style={styles.accountInfo}>
              {billDetails.accountNumber}
              {billDetails.customerName && ` • ${billDetails.customerName}`}
            </Text>
          </View>

          {/* Amount Selection */}
          <View style={styles.amountSection}>
            <Text style={styles.sectionTitle}>Select Amount</Text>
            
            {/* Predefined Amounts Grid */}
            <View style={styles.amountsGrid}>
              {predefinedAmounts.map(renderAmountButton)}
            </View>

            {/* Custom Amount */}
            <View style={styles.customAmountSection}>
              <Text style={styles.customAmountLabel}>Or enter custom amount</Text>
              <View style={styles.customAmountContainer}>
                <Text style={styles.currencyPrefix}>{getCurrencySymbol()}</Text>
                <TextInput
                  style={styles.customAmountInput}
                  value={customAmount}
                  onChangeText={handleCustomAmountChange}
                  placeholder="0"
                  placeholderTextColor={theme.colors.textSecondary}
                  keyboardType="numeric"
                />
              </View>
            </View>

            {/* Amount Info */}
            <View style={styles.amountInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Service Charge</Text>
                <Text style={styles.infoValue}>{convertAndFormat(0)}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>VAT</Text>
                <Text style={styles.infoValue}>{convertAndFormat(0)}</Text>
              </View>
              <View style={[styles.infoRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalValue}>
                  {convertAndFormat(getSelectedAmountValue())}
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Next Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.nextButton,
              getSelectedAmountValue() <= 0 && styles.nextButtonDisabled
            ]}
            onPress={handleNext}
            disabled={getSelectedAmountValue() <= 0}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#E67E22', '#D35400']}
              style={styles.nextButtonGradient}
            >
              <Text style={styles.nextButtonText}>Next</Text>
              <Ionicons name="arrow-forward" size={20} color={Colors.neutral.white} />
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  providerInfo: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  providerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  accountInfo: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  amountSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 20,
  },
  amountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
    marginBottom: 30,
  },
  amountButton: {
    width: (width - 72) / 2, // Better spacing calculation
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 3,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    position: 'relative',
    overflow: 'hidden',
  },
  selectedAmountButton: {
    borderColor: '#E67E22',
    backgroundColor: '#E67E22',
    elevation: 6,
    shadowColor: '#E67E22',
    shadowOpacity: 0.3,
  },
  amountButtonContent: {
    alignItems: 'center',
    flex: 1,
  },
  amountLabel: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 6,
  },
  selectedAmountLabel: {
    color: Colors.neutral.white,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  selectedAmountValue: {
    color: Colors.neutral.white,
  },
  amountDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  selectedAmountDescription: {
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    padding: 2,
  },
  serviceTypeInfo: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E67E22',
    marginBottom: 4,
  },
  customAmountSection: {
    marginBottom: 30,
  },
  customAmountLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  customAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  currencyPrefix: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: 12,
  },
  customAmountInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    paddingVertical: 16,
  },
  amountInfo: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    elevation: 1,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginBottom: 0,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E67E22',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  nextButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  nextButtonDisabled: {
    opacity: 0.6,
    elevation: 2,
  },
  nextButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    gap: 12,
  },
  nextButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
});

export default BillAmountScreen;
