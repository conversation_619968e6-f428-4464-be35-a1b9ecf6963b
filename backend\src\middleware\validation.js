/**
 * Validation Middleware
 * Common validation rules and middleware for request validation
 */

const { body, param, query, validationResult } = require('express-validator');
const { ValidationError } = require('./errorHandler');
const config = require('../config/config');

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }
  next();
};

/**
 * Common validation rules
 */
const validationRules = {
  // Phone number validation
  phoneNumber: body('phoneNumber')
    .isMobilePhone('any')
    .withMessage('Valid phone number is required'),

  // Email validation
  email: body('email')
    .isEmail()
    .withMessage('Valid email address is required'),

  // Password validation
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*\d)/)
    .withMessage('Password must contain at least one letter and one number'),

  // Amount validation
  amount: body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be a positive number'),

  // Currency validation
  currency: body('currency')
    .isIn(config.business.supportedCurrencies)
    .withMessage('Unsupported currency'),

  // Country code validation
  countryCode: body('countryCode')
    .isIn(config.business.supportedCountries)
    .withMessage('Unsupported country'),

  // UUID validation
  uuid: param('id')
    .isUUID()
    .withMessage('Invalid ID format'),

  // Pagination validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],

  // OTP validation
  otp: body('otp')
    .isLength({ min: 4, max: 6 })
    .isNumeric()
    .withMessage('Valid OTP is required'),

  // Full name validation
  fullName: body('fullName')
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),

  // Account number validation
  accountNumber: body('accountNumber')
    .isLength({ min: 4, max: 20 })
    .isAlphanumeric()
    .withMessage('Valid account number is required'),

  // Transaction type validation
  transactionType: body('type')
    .isIn(['bill_payment', 'transfer', 'topup', 'withdrawal'])
    .withMessage('Invalid transaction type'),

  // Payment method validation
  paymentMethod: body('paymentMethod')
    .isIn(['wallet', 'mobile_money', 'bank'])
    .withMessage('Invalid payment method')
};

/**
 * Validation middleware combinations
 */
const validationMiddleware = {
  // User registration validation
  register: [
    validationRules.phoneNumber,
    validationRules.fullName,
    body('email').optional().isEmail().withMessage('Valid email is required'),
    validationRules.password,
    validationRules.countryCode,
    handleValidationErrors
  ],

  // User login validation
  login: [
    validationRules.phoneNumber,
    body('password').notEmpty().withMessage('Password is required'),
    validationRules.countryCode,
    handleValidationErrors
  ],

  // Phone verification validation
  verifyPhone: [
    validationRules.phoneNumber,
    validationRules.otp,
    validationRules.countryCode,
    handleValidationErrors
  ],

  // Wallet top-up validation
  walletTopup: [
    validationRules.amount,
    validationRules.paymentMethod,
    body('provider').optional().isString().withMessage('Provider must be a string'),
    handleValidationErrors
  ],

  // Money transfer validation
  moneyTransfer: [
    validationRules.amount,
    validationRules.phoneNumber.withMessage('Recipient phone number is required'),
    body('description').optional().isLength({ max: 200 }).withMessage('Description too long'),
    handleValidationErrors
  ],

  // Bill payment validation
  billPayment: [
    body('providerId').notEmpty().withMessage('Provider ID is required'),
    validationRules.accountNumber,
    validationRules.amount,
    body('customerName').optional().isLength({ max: 100 }).withMessage('Customer name too long'),
    handleValidationErrors
  ],

  // Profile update validation
  updateProfile: [
    body('fullName').optional().isLength({ min: 2, max: 100 }).withMessage('Full name must be between 2 and 100 characters'),
    body('email').optional().isEmail().withMessage('Valid email is required'),
    body('dateOfBirth').optional().isISO8601().withMessage('Valid date of birth is required'),
    handleValidationErrors
  ],

  // Password change validation
  changePassword: [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    validationRules.password.withMessage('New password must meet requirements'),
    body('confirmPassword').custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match');
      }
      return true;
    }),
    handleValidationErrors
  ],

  // Transaction history validation
  transactionHistory: [
    ...validationRules.pagination,
    query('type').optional().isIn(['bill_payment', 'transfer', 'topup', 'withdrawal']).withMessage('Invalid transaction type'),
    query('status').optional().isIn(['pending', 'completed', 'failed', 'cancelled']).withMessage('Invalid status'),
    query('startDate').optional().isISO8601().withMessage('Valid start date is required'),
    query('endDate').optional().isISO8601().withMessage('Valid end date is required'),
    handleValidationErrors
  ],

  // Admin user management validation
  adminUserUpdate: [
    validationRules.uuid,
    body('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
    body('kycStatus').optional().isIn(['pending', 'verified', 'rejected']).withMessage('Invalid KYC status'),
    body('role').optional().isIn(['user', 'admin']).withMessage('Invalid role'),
    handleValidationErrors
  ],

  // KYC document upload validation
  kycUpload: [
    body('documentType').isIn(['national_id', 'passport', 'driving_license']).withMessage('Invalid document type'),
    body('documentNumber').isLength({ min: 5, max: 20 }).withMessage('Invalid document number'),
    handleValidationErrors
  ]
};

/**
 * Custom validation functions
 */
const customValidations = {
  // Validate transaction amount against limits
  validateTransactionLimits: (req, res, next) => {
    const { amount } = req.body;
    const numericAmount = parseFloat(amount);

    if (numericAmount > config.transactionLimits.maxTransactionAmount) {
      throw new ValidationError(`Amount exceeds maximum limit of ${config.transactionLimits.maxTransactionAmount}`);
    }

    next();
  },

  // Validate business hours (if needed)
  validateBusinessHours: (req, res, next) => {
    const now = new Date();
    const hour = now.getHours();

    // Example: Only allow transactions between 6 AM and 10 PM
    if (hour < 6 || hour > 22) {
      throw new ValidationError('Transactions are only allowed between 6 AM and 10 PM');
    }

    next();
  },

  // Validate file upload
  validateFileUpload: (req, res, next) => {
    if (!req.file) {
      throw new ValidationError('File is required');
    }

    const allowedTypes = config.upload.allowedFileTypes;
    const fileExtension = req.file.originalname.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      throw new ValidationError(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }

    if (req.file.size > config.upload.maxFileSize) {
      throw new ValidationError(`File size exceeds maximum limit of ${config.upload.maxFileSize} bytes`);
    }

    next();
  }
};

module.exports = {
  validationRules,
  validationMiddleware,
  customValidations,
  handleValidationErrors
};
