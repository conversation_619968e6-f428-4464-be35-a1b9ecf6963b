/**
 * Wallet Integration Tests
 * End-to-end integration tests for wallet functionality
 */

const request = require('supertest');
const app = require('../../src/server');
const databaseService = require('../../src/services/database');
const redisService = require('../../src/services/redis');

describe('Wallet Integration Tests', () => {
  let authToken;
  let userId;
  let testUser2Token;
  let testUser2Id;
  let supabase;

  beforeAll(async () => {
    // Initialize database connection
    await databaseService.initialize();
    supabase = databaseService.getSupabase();
    
    // Create test users
    const testUser1 = global.testFactories.user();
    const testUser2 = global.testFactories.user();

    // Register first test user
    const registerResponse1 = await request(app)
      .post('/api/v1/auth/register')
      .send(testUser1);

    expect(registerResponse1.status).toBe(201);
    
    // Mock OTP verification for first user
    const { data: user1 } = await supabase
      .from('user_profiles')
      .update({ is_verified: true })
      .eq('phone_number', testUser1.phoneNumber)
      .select()
      .single();

    userId = user1.user_id;

    // Login first user
    const loginResponse1 = await request(app)
      .post('/api/v1/auth/login')
      .send({
        phoneNumber: testUser1.phoneNumber,
        password: testUser1.password,
        countryCode: testUser1.countryCode
      });

    authToken = loginResponse1.body.data.tokens.accessToken;

    // Register and setup second test user
    const registerResponse2 = await request(app)
      .post('/api/v1/auth/register')
      .send(testUser2);

    const { data: user2 } = await supabase
      .from('user_profiles')
      .update({ is_verified: true })
      .eq('phone_number', testUser2.phoneNumber)
      .select()
      .single();

    testUser2Id = user2.user_id;

    const loginResponse2 = await request(app)
      .post('/api/v1/auth/login')
      .send({
        phoneNumber: testUser2.phoneNumber,
        password: testUser2.password,
        countryCode: testUser2.countryCode
      });

    testUser2Token = loginResponse2.body.data.tokens.accessToken;
  });

  afterAll(async () => {
    // Cleanup test data
    await global.testUtils.cleanupTestData();
    await databaseService.close();
  });

  beforeEach(async () => {
    // Clear Redis cache before each test
    await redisService.del(`wallet:${userId}`);
    await redisService.del(`wallet:${testUser2Id}`);
  });

  describe('GET /api/v1/wallets', () => {
    test('should create and return wallet on first access', async () => {
      const response = await request(app)
        .get('/api/v1/wallets')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveValidApiResponse();
      expect(response.body.data.wallet).toBeDefined();
      expect(response.body.data.wallet.id).toBeValidUUID();
      expect(response.body.data.wallet.balance).toBe(0);
      expect(response.body.data.wallet.currency).toBeValidCurrency();
      expect(response.body.data.wallet.isActive).toBe(true);
    });

    test('should return existing wallet on subsequent calls', async () => {
      // First call creates wallet
      const response1 = await request(app)
        .get('/api/v1/wallets')
        .set('Authorization', `Bearer ${authToken}`);

      const walletId = response1.body.data.wallet.id;

      // Second call should return same wallet
      const response2 = await request(app)
        .get('/api/v1/wallets')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response2.status).toBe(200);
      expect(response2.body.data.wallet.id).toBe(walletId);
    });

    test('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/wallets');

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/v1/wallets/balance', () => {
    test('should return wallet balance information', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('balance');
      expect(response.body.data).toHaveProperty('availableBalance');
      expect(response.body.data).toHaveProperty('pendingBalance');
      expect(response.body.data).toHaveProperty('currency');
      expect(response.body.data).toHaveProperty('formatted');
      expect(typeof response.body.data.balance).toBe('number');
      expect(response.body.data.currency).toBeValidCurrency();
    });
  });

  describe('POST /api/v1/wallets/topup', () => {
    test('should initiate wallet top-up successfully', async () => {
      const topupData = {
        amount: 50000,
        paymentMethod: 'mobile_money',
        provider: 'mtn'
      };

      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send(topupData);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('transactionId');
      expect(response.body.data.amount).toBe(topupData.amount);
      expect(response.body.data.status).toBe('pending');
      expect(response.body.data).toHaveProperty('formatted');
    });

    test('should reject invalid top-up amount', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: -1000,
          paymentMethod: 'mobile_money'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject invalid payment method', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          paymentMethod: 'invalid_method'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/wallets/transfer', () => {
    beforeEach(async () => {
      // Add balance to first user's wallet for testing
      await supabase.rpc('update_wallet_balance', {
        p_user_id: userId,
        p_amount: 100000,
        p_type: 'credit',
        p_description: 'Test balance for integration tests',
        p_transaction_id: null
      });
    });

    test('should transfer money between users successfully', async () => {
      // Get second user's phone number
      const { data: user2Profile } = await supabase
        .from('user_profiles')
        .select('phone_number')
        .eq('user_id', testUser2Id)
        .single();

      const transferData = {
        amount: 25000,
        phoneNumber: user2Profile.phone_number,
        description: 'Integration test transfer'
      };

      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send(transferData);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('transferId');
      expect(response.body.data.amount).toBe(transferData.amount);
      expect(response.body.data.status).toBe('completed');

      // Verify balances updated
      const senderBalance = await request(app)
        .get('/api/v1/wallets/balance')
        .set('Authorization', `Bearer ${authToken}`);

      const recipientBalance = await request(app)
        .get('/api/v1/wallets/balance')
        .set('Authorization', `Bearer ${testUser2Token}`);

      expect(senderBalance.body.data.balance).toBe(75000); // 100000 - 25000
      expect(recipientBalance.body.data.balance).toBe(25000);
    });

    test('should reject transfer to non-existent user', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          phoneNumber: '+256700000999',
          description: 'Test transfer'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should reject transfer to self', async () => {
      const { data: userProfile } = await supabase
        .from('user_profiles')
        .select('phone_number')
        .eq('user_id', userId)
        .single();

      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          phoneNumber: userProfile.phone_number,
          description: 'Self transfer'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject transfer with insufficient balance', async () => {
      const { data: user2Profile } = await supabase
        .from('user_profiles')
        .select('phone_number')
        .eq('user_id', testUser2Id)
        .single();

      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 1000000, // More than available balance
          phoneNumber: user2Profile.phone_number,
          description: 'Large transfer'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/wallets/transactions', () => {
    test('should return transaction history', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('transactions');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.transactions)).toBe(true);
      expect(response.body.data.pagination).toHaveProperty('page');
      expect(response.body.data.pagination).toHaveProperty('limit');
    });

    test('should filter transactions by type', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions?type=transfer')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      
      // All transactions should be of type 'transfer'
      response.body.data.transactions.forEach(tx => {
        expect(tx.type).toBe('transfer');
      });
    });

    test('should paginate transaction history', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions?page=1&limit=5')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(5);
      expect(response.body.data.transactions.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/v1/wallets/limits', () => {
    test('should return wallet transaction limits', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/limits')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('dailyLimit');
      expect(response.body.data).toHaveProperty('monthlyLimit');
      expect(response.body.data).toHaveProperty('dailySpent');
      expect(response.body.data).toHaveProperty('dailyRemaining');
      expect(typeof response.body.data.dailyLimit).toBe('number');
      expect(typeof response.body.data.monthlyLimit).toBe('number');
    });
  });

  describe('Currency Operations', () => {
    test('should get supported currencies', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/currencies')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('currencies');
      expect(response.body.data).toHaveProperty('exchangeRates');
      expect(Array.isArray(response.body.data.currencies)).toBe(true);
      expect(response.body.data.currencies.length).toBeGreaterThan(0);
    });

    test('should convert currency amounts', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/convert')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 100000,
          fromCurrency: 'UGX',
          toCurrency: 'KES'
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('originalAmount');
      expect(response.body.data).toHaveProperty('convertedAmount');
      expect(response.body.data).toHaveProperty('exchangeRate');
      expect(response.body.data.originalAmount).toBe(100000);
      expect(typeof response.body.data.convertedAmount).toBe('number');
      expect(typeof response.body.data.exchangeRate).toBe('number');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('should handle concurrent transfer requests', async () => {
      // Add balance for concurrent tests
      await supabase.rpc('update_wallet_balance', {
        p_user_id: userId,
        p_amount: 100000,
        p_type: 'credit',
        p_description: 'Test balance for concurrent tests',
        p_transaction_id: null
      });

      const { data: user2Profile } = await supabase
        .from('user_profiles')
        .select('phone_number')
        .eq('user_id', testUser2Id)
        .single();

      // Make concurrent transfer requests
      const transferPromises = Array(3).fill().map(() =>
        request(app)
          .post('/api/v1/wallets/transfer')
          .set('Authorization', `Bearer ${authToken}`)
          .send({
            amount: 10000,
            phoneNumber: user2Profile.phone_number,
            description: 'Concurrent transfer test'
          })
      );

      const responses = await Promise.all(transferPromises);
      
      // At least one should succeed, others might fail due to insufficient balance
      const successfulTransfers = responses.filter(r => r.status === 200);
      const failedTransfers = responses.filter(r => r.status !== 200);
      
      expect(successfulTransfers.length).toBeGreaterThan(0);
      expect(successfulTransfers.length + failedTransfers.length).toBe(3);
    });

    test('should handle database connection issues gracefully', async () => {
      // This test would require mocking database failures
      // For now, we'll test that the API returns appropriate error responses
      const response = await request(app)
        .get('/api/v1/wallets')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Performance Tests', () => {
    test('wallet operations should complete within acceptable time', async () => {
      const startTime = Date.now();
      
      await request(app)
        .get('/api/v1/wallets/balance')
        .set('Authorization', `Bearer ${authToken}`);
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
    });

    test('should handle multiple concurrent wallet requests', async () => {
      const requests = Array(10).fill().map(() =>
        request(app)
          .get('/api/v1/wallets/balance')
          .set('Authorization', `Bearer ${authToken}`)
      );

      const responses = await Promise.all(requests);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });
  });
});
