-- JiraniPay Database Schema Migration - FIXED VERSION
-- Run this in your Supabase SQL Editor to create the required tables
-- This version avoids foreign key constraint issues

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_profiles table (simplified)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    full_name TEXT,
    phone_number TEXT UNIQUE,
    email TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    city TEXT,
    country TEXT DEFAULT 'UG',
    profile_picture_url TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create wallets table (simplified)
CREATE TABLE IF NOT EXISTS public.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    account_number TEXT UNIQUE,
    account_type TEXT DEFAULT 'wallet' CHECK (account_type IN ('wallet', 'savings', 'business')),
    balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    pending_balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'UGX',
    daily_limit DECIMAL(15,2) DEFAULT 500000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 2000000.00,
    spent_today DECIMAL(15,2) DEFAULT 0.00,
    spent_this_month DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    provider_name TEXT DEFAULT 'JiraniPay',
    last_balance_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table (simplified)
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    wallet_id UUID,
    transaction_type TEXT CHECK (transaction_type IN ('deposit', 'withdrawal', 'transfer', 'payment', 'refund')),
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'UGX',
    description TEXT,
    reference_number TEXT UNIQUE,
    external_reference TEXT,
    recipient_phone TEXT,
    recipient_name TEXT,
    sender_phone TEXT,
    sender_name TEXT,
    category TEXT DEFAULT 'general',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    provider TEXT DEFAULT 'JiraniPay',
    provider_transaction_id TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_phone ON public.user_profiles(phone_number);
CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON public.wallets(user_id);
CREATE INDEX IF NOT EXISTS idx_wallets_account_number ON public.wallets(account_number);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_wallet_id ON public.transactions(wallet_id);
CREATE INDEX IF NOT EXISTS idx_transactions_reference ON public.transactions(reference_number);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON public.transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON public.transactions(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_wallets_updated_at ON public.wallets;
CREATE TRIGGER update_wallets_updated_at 
    BEFORE UPDATE ON public.wallets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_transactions_updated_at ON public.transactions;
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON public.transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert a test record to verify everything works
-- (This will help verify the tables are created correctly)
DO $$
BEGIN
    -- Only insert if no records exist
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles LIMIT 1) THEN
        INSERT INTO public.user_profiles (
            user_id,
            full_name,
            phone_number,
            email,
            country,
            is_active,
            phone_verified
        ) VALUES (
            uuid_generate_v4(),
            'Test User',
            '+************',
            '<EMAIL>',
            'UG',
            true,
            true
        );
        
        RAISE NOTICE 'Test user profile created successfully';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Note: Could not create test user profile: %', SQLERRM;
END $$;

-- Verify tables were created
DO $$
BEGIN
    RAISE NOTICE '✅ Tables created successfully:';
    RAISE NOTICE '   - user_profiles: %', (SELECT COUNT(*) FROM public.user_profiles);
    RAISE NOTICE '   - wallets: %', (SELECT COUNT(*) FROM public.wallets);
    RAISE NOTICE '   - transactions: %', (SELECT COUNT(*) FROM public.transactions);
    RAISE NOTICE '🎉 Database schema setup complete!';
END $$;
