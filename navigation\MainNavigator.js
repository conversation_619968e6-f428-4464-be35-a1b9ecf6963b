import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, StatusBar, TouchableOpacity, BackHandler } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DashboardScreen from '../screens/DashboardScreen';
import ProfileScreen from '../screens/ProfileScreen';
import WalletScreen from '../screens/WalletScreen';
import BillPaymentScreen from '../screens/BillPaymentScreen';
import BottomNavigation from '../components/BottomNavigation';
import { Colors } from '../constants/Colors';
import NavigationUtils from '../utils/navigationUtils';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const QRPayScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const handleQRScan = () => {
    navigation.navigate('QRScanner', { type: 'payment' });
  };

  const handleQRGenerate = () => {
    navigation.navigate('QRGenerator', { type: 'receive' });
  };

  return (
    <View style={styles.placeholderScreen}>
      <Ionicons name="qr-code" size={64} color={theme.colors.primary} />
      <Text style={styles.placeholderText}>{t('qr.title')}</Text>
      <Text style={styles.placeholderSubtext}>{t('qr.subtitle')}</Text>

      <View style={styles.qrActions}>
        <TouchableOpacity style={styles.qrActionButton} onPress={handleQRScan}>
          <Ionicons name="scan" size={24} color={theme.colors.white} />
          <Text style={styles.qrActionText}>{t('qr.scanQRCode')}</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.qrActionButton} onPress={handleQRGenerate}>
          <Ionicons name="qr-code-outline" size={24} color={theme.colors.white} />
          <Text style={styles.qrActionText}>{t('qr.generateQR')}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// ProfileScreen and WalletScreen are now imported from ../screens/

const MainNavigator = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('home');
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const handleTabNavigation = (tab) => {
    setActiveTab(tab);
  };

  useEffect(() => {
    // Handle back button for main navigator (bottom tab navigation)
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Check if we can go back in the navigation stack first
      if (navigation?.canGoBack && navigation.canGoBack()) {
        navigation.goBack();
        return true;
      }

      // If we're not on the home tab, navigate back to home tab
      if (activeTab !== 'home') {
        setActiveTab('home');
        return true;
      }

      // If we're on the home tab and can't go back, show exit confirmation
      NavigationUtils.showExitConfirmation();
      return true;
    });

    return () => backHandler.remove();
  }, [activeTab, navigation]);

  const renderScreen = () => {
    const screenProps = {
      navigation: {
        ...navigation,
        navigateToTab: handleTabNavigation,
      }
    };

    switch (activeTab) {
      case 'home':
        return <DashboardScreen {...screenProps} />;
      case 'bills':
        return <BillPaymentScreen {...screenProps} />;
      case 'qr':
        return <QRPayScreen {...screenProps} />;
      case 'wallet':
        return <WalletScreen {...screenProps} />;
      case 'profile':
        return <ProfileScreen {...screenProps} />;
      default:
        return <DashboardScreen {...screenProps} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme.statusBar === 'dark' ? 'dark-content' : 'light-content'}
        backgroundColor={theme.colors.background}
        translucent={false}
      />
      {renderScreen()}
      <BottomNavigation
        activeTab={activeTab}
        onTabPress={setActiveTab}
      />
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  placeholderScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingBottom: 100, // Account for bottom navigation
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginHorizontal: 40,
    lineHeight: 22,
    marginBottom: 30,
  },
  qrActions: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 20,
  },
  qrActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8,
  },
  qrActionText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MainNavigator;
