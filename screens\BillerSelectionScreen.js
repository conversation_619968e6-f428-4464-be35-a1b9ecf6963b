/**
 * Biller Selection Screen
 * Dedicated screen for browsing and selecting billers by category
 * with advanced filtering, search, and availability status
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  TextInput,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import billerManagementService from '../services/billerManagementService';
import { formatCurrency } from '../utils/currencyUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const BillerSelectionScreen = ({ navigation, route }) => {
  const { categoryId, categoryName, autoSelectService } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [billers, setBillers] = useState([]);
  const [filteredBillers, setFilteredBillers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  const filterOptions = [
    { key: 'all', label: 'All Billers', icon: 'grid-outline' },
    { key: 'available', label: 'Available', icon: 'checkmark-circle-outline' },
    { key: 'popular', label: 'Popular', icon: 'trending-up-outline' },
    { key: 'recent', label: 'Recent', icon: 'time-outline' }
  ];

  const sortOptions = [
    { key: 'name', label: 'Name' },
    { key: 'popularity', label: 'Popularity' },
    { key: 'fees', label: 'Fees' }
  ];

  useEffect(() => {
    loadBillers();
  }, [categoryId]);

  useEffect(() => {
    filterAndSortBillers();
  }, [billers, searchQuery, selectedFilter, sortBy]);

  const loadBillers = async () => {
    try {
      setLoading(true);
      const result = await billerManagementService.getBillersByCategory(categoryId, {
        includeInactive: false,
        includeMaintenanceMode: false
      });

      if (result.success) {
        setBillers(result.billers);
      } else {
        Alert.alert('Error', 'Failed to load billers. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error loading billers:', error);
      Alert.alert('Error', 'Failed to load billers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBillers();
    setRefreshing(false);
  };

  const filterAndSortBillers = () => {
    let filtered = [...billers];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(biller =>
        biller.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        biller.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    switch (selectedFilter) {
      case 'available':
        filtered = filtered.filter(biller => biller.isAvailable && !biller.maintenanceMode);
        break;
      case 'popular':
        // This would typically use actual popularity data
        filtered = filtered.slice(0, 10);
        break;
      case 'recent':
        // This would typically use user's recent usage data
        filtered = filtered.slice(0, 5);
        break;
    }

    // Apply sorting
    switch (sortBy) {
      case 'name':
        filtered.sort((a, b) => a.displayName.localeCompare(b.displayName));
        break;
      case 'popularity':
        // Sort by popularity (mock implementation)
        filtered.sort((a, b) => Math.random() - 0.5);
        break;
      case 'fees':
        filtered.sort((a, b) => (a.minAmount || 0) - (b.minAmount || 0));
        break;
    }

    setFilteredBillers(filtered);
  };

  const handleBillerPress = (biller) => {
    if (!biller.isAvailable || biller.maintenanceMode) {
      Alert.alert(
        'Biller Unavailable',
        biller.maintenanceMessage || 'This biller is currently unavailable. Please try again later.',
        [{ text: 'OK' }]
      );
      return;
    }

    navigation.navigate('BillPaymentForm', {
      billerId: biller.id,
      billerName: biller.displayName,
      autoSelectService
    });
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  const renderFilterBar = () => (
    <View style={styles.filterBar}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {filterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              selectedFilter === filter.key && styles.filterButtonActive
            ]}
            onPress={() => setSelectedFilter(filter.key)}
          >
            <Ionicons 
              name={filter.icon} 
              size={16} 
              color={selectedFilter === filter.key ? theme.colors.white : theme.colors.textSecondary} 
            />
            <Text style={[
              styles.filterButtonText,
              selectedFilter === filter.key && styles.filterButtonTextActive
            ]}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderSortButton = () => (
    <TouchableOpacity 
      style={styles.sortButton}
      onPress={() => {
        Alert.alert(
          'Sort By',
          'Choose sorting option',
          sortOptions.map(option => ({
            text: option.label,
            onPress: () => setSortBy(option.key)
          })).concat([{ text: 'Cancel', style: 'cancel' }])
        );
      }}
    >
      <Ionicons name="funnel-outline" size={20} color={theme.colors.primary} />
      <Text style={styles.sortButtonText}>Sort</Text>
    </TouchableOpacity>
  );

  const renderBillerItem = ({ item: biller }) => (
    <TouchableOpacity 
      style={[
        styles.billerCard,
        !biller.isAvailable && styles.billerCardDisabled
      ]}
      onPress={() => handleBillerPress(biller)}
      disabled={!biller.isAvailable}
    >
      <View style={styles.billerHeader}>
        <View style={[styles.billerIcon, { backgroundColor: biller.category?.color || theme.colors.primary }]}>
          <Ionicons 
            name={getBillerIcon(biller.category?.name)} 
            size={24} 
            color={theme.colors.white} 
          />
        </View>
        
        <View style={styles.billerInfo}>
          <Text style={[styles.billerName, !biller.isAvailable && styles.disabledText]}>
            {biller.displayName}
          </Text>
          <Text style={[styles.billerDescription, !biller.isAvailable && styles.disabledText]}>
            {biller.description || biller.category?.displayName}
          </Text>
          
          <View style={styles.billerDetails}>
            <Text style={[styles.billerLimits, !biller.isAvailable && styles.disabledText]}>
              {formatCurrency(biller.minAmount)} - {formatCurrency(biller.maxAmount)}
            </Text>
            <Text style={[styles.processingTime, !biller.isAvailable && styles.disabledText]}>
              {biller.processingTime || 'Instant'}
            </Text>
          </View>
        </View>

        <View style={styles.billerStatus}>
          {biller.isAvailable ? (
            <View style={styles.statusIndicator}>
              <View style={[styles.statusDot, { backgroundColor: theme.colors.success }]} />
              <Text style={styles.statusText}>Available</Text>
            </View>
          ) : (
            <View style={styles.statusIndicator}>
              <View style={[styles.statusDot, { backgroundColor: theme.colors.error }]} />
              <Text style={styles.statusText}>
                {biller.maintenanceMode ? 'Maintenance' : 'Unavailable'}
              </Text>
            </View>
          )}
          <Ionicons 
            name="chevron-forward" 
            size={20} 
            color={biller.isAvailable ? theme.colors.textSecondary : theme.colors.border} 
          />
        </View>
      </View>

      {biller.maintenanceMode && biller.maintenanceMessage && (
        <View style={styles.maintenanceNotice}>
          <Ionicons name="warning" size={16} color={theme.colors.warning} />
          <Text style={styles.maintenanceText}>{biller.maintenanceMessage}</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="business-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Billers Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? `No billers match "${searchQuery}"`
          : selectedFilter === 'all'
          ? 'No billers available in this category'
          : `No ${selectedFilter} billers found`
        }
      </Text>
      {searchQuery && (
        <TouchableOpacity 
          style={styles.clearSearchButton}
          onPress={() => setSearchQuery('')}
        >
          <Text style={styles.clearSearchText}>Clear Search</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.header}>
          <UnifiedBackButton navigation={navigation} />
          <Text style={styles.headerTitle}>{categoryName}</Text>
          <View style={styles.headerButton} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading billers...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>{categoryName}</Text>
        {renderSortButton()}
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search billers..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Bar */}
      {renderFilterBar()}

      {/* Results Count */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {filteredBillers.length} biller{filteredBillers.length !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Billers List */}
      <FlatList
        data={filteredBillers}
        renderItem={renderBillerItem}
        keyExtractor={(item) => item.id}
        style={styles.billersList}
        contentContainerStyle={filteredBillers.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerButton: {
    width: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 10,
  },
  filterBar: {
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
    marginLeft: 6,
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  sortButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  resultsHeader: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  resultsCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  billersList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  billerCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  billerCardDisabled: {
    opacity: 0.6,
  },
  billerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  billerInfo: {
    flex: 1,
  },
  billerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  billerDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  billerLimits: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  processingTime: {
    fontSize: 12,
    color: theme.colors.success,
    fontWeight: '500',
  },
  billerStatus: {
    alignItems: 'flex-end',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  disabledText: {
    color: theme.colors.border,
  },
  maintenanceNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.warning + '20',
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  maintenanceText: {
    fontSize: 12,
    color: theme.colors.warning,
    marginLeft: 6,
    flex: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
  clearSearchButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 16,
  },
  clearSearchText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BillerSelectionScreen;
