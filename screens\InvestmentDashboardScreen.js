/**
 * Investment Dashboard Screen
 * Main dashboard for investment portfolios with overview,
 * performance tracking, and portfolio management
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import marketDataService from '../services/marketDataService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentDashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [portfolios, setPortfolios] = useState([]);
  const [summary, setSummary] = useState(null);
  const [marketStatus, setMarketStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadUserPreferences();
    loadInvestmentData();
    loadMarketStatus();
  }, []);

  const loadUserPreferences = async () => {
    try {
      const userId = await getCurrentUserId();
      if (userId) {
        // Load saved preferences
        const marketPref = await AsyncStorage.getItem(`investment_market_preference_${userId}`);
        const currencyPref = await AsyncStorage.getItem(`investment_currency_preference_${userId}`);
        const riskPref = await AsyncStorage.getItem(`investment_risk_profile_${userId}`);

        // Apply preferences to data loading
        if (marketPref) {
          console.log('📊 User market preference:', marketPref);
        }
        if (currencyPref) {
          console.log('💰 User currency preference:', currencyPref);
        }
        if (riskPref) {
          console.log('⚖️ User risk profile:', riskPref);
        }
      }
    } catch (error) {
      console.error('❌ Error loading user preferences:', error);
    }
  };

  const loadInvestmentData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view investments');
        navigation.goBack();
        return;
      }

      // Load portfolios and summary in parallel
      const [portfoliosResult, summaryResult] = await Promise.all([
        investmentPortfolioService.getUserPortfolios(userId, { isActive: true }),
        investmentPortfolioService.getPortfolioSummary(userId)
      ]);

      if (portfoliosResult.success) {
        setPortfolios(portfoliosResult.portfolios);
      }

      if (summaryResult.success) {
        setSummary(summaryResult.summary);
      }

    } catch (error) {
      console.error('❌ Error loading investment data:', error);
      Alert.alert('Error', 'Failed to load investment data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadMarketStatus = async () => {
    try {
      const result = await marketDataService.getMarketStatus();
      if (result.success) {
        setMarketStatus(result);
      }
    } catch (error) {
      console.error('❌ Error loading market status:', error);
    }
  };

  const onRefresh = () => {
    loadInvestmentData(true);
    loadMarketStatus();
  };

  const handleCreatePortfolio = () => {
    navigation.navigate('InvestmentPortfolioCreation');
  };

  const handlePortfolioPress = (portfolio) => {
    navigation.navigate('InvestmentPortfolioDetails', { portfolioId: portfolio.id });
  };

  const handleInvestmentSettings = () => {
    Alert.alert(
      'Investment Settings',
      'Choose an option:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Market Preferences',
          onPress: () => showMarketPreferences()
        },
        {
          text: 'Currency Settings',
          onPress: () => showCurrencySettings()
        },
        {
          text: 'Risk Profile',
          onPress: () => showRiskProfile()
        },
        {
          text: 'Notifications',
          onPress: () => showNotificationSettings()
        },
        {
          text: 'Trading Hours',
          onPress: () => showTradingHours()
        }
      ]
    );
  };

  const showMarketPreferences = () => {
    Alert.alert(
      'Market Preferences',
      'Select your preferred East African markets:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Uganda (USE)', onPress: () => saveMarketPreference('USE') },
        { text: 'Kenya (NSE)', onPress: () => saveMarketPreference('NSE') },
        { text: 'Tanzania (DSE)', onPress: () => saveMarketPreference('DSE') },
        { text: 'Rwanda (RSE)', onPress: () => saveMarketPreference('RSE') },
        { text: 'All Markets', onPress: () => saveMarketPreference('ALL') }
      ]
    );
  };

  const showCurrencySettings = () => {
    Alert.alert(
      'Default Currency',
      'Choose your preferred currency for investments:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'UGX (Ugandan Shilling)', onPress: () => saveCurrencyPreference('UGX') },
        { text: 'KES (Kenyan Shilling)', onPress: () => saveCurrencyPreference('KES') },
        { text: 'TZS (Tanzanian Shilling)', onPress: () => saveCurrencyPreference('TZS') },
        { text: 'RWF (Rwandan Franc)', onPress: () => saveCurrencyPreference('RWF') },
        { text: 'USD (US Dollar)', onPress: () => saveCurrencyPreference('USD') }
      ]
    );
  };

  const showRiskProfile = () => {
    Alert.alert(
      'Risk Profile',
      'Select your investment risk tolerance:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Conservative', onPress: () => saveRiskProfile('conservative') },
        { text: 'Moderate', onPress: () => saveRiskProfile('moderate') },
        { text: 'Aggressive', onPress: () => saveRiskProfile('aggressive') }
      ]
    );
  };

  const showNotificationSettings = () => {
    Alert.alert(
      'Investment Notifications',
      'Configure your notification preferences:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Price Alerts', onPress: () => toggleNotification('price_alerts') },
        { text: 'Market News', onPress: () => toggleNotification('market_news') },
        { text: 'Portfolio Updates', onPress: () => toggleNotification('portfolio_updates') },
        { text: 'Dividend Notifications', onPress: () => toggleNotification('dividend_notifications') }
      ]
    );
  };

  const showTradingHours = () => {
    Alert.alert(
      'East African Trading Hours',
      'Uganda Securities Exchange (USE): 10:00 - 15:00 EAT\n' +
      'Nairobi Securities Exchange (NSE): 09:00 - 15:00 EAT\n' +
      'Dar es Salaam Stock Exchange (DSE): 10:00 - 15:00 EAT\n' +
      'Rwanda Stock Exchange (RSE): 09:00 - 15:00 EAT\n\n' +
      'All times are in East Africa Time (EAT)',
      [{ text: 'OK' }]
    );
  };

  const saveMarketPreference = async (market) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`investment_market_preference_${userId}`, market);

      // Refresh data to reflect market preference
      await loadInvestmentData();
      await loadMarketStatus();

      Alert.alert(
        'Market Preference Updated',
        `Your preferred market is now set to ${market}. The dashboard will prioritize ${market} assets and market data.`,
        [{ text: 'OK', onPress: () => loadInvestmentData() }]
      );
    } catch (error) {
      console.error('❌ Error saving market preference:', error);
      Alert.alert('Error', 'Failed to save market preference');
    }
  };

  const saveCurrencyPreference = async (currency) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`investment_currency_preference_${userId}`, currency);

      // Refresh data to reflect currency preference
      await loadInvestmentData();

      Alert.alert(
        'Currency Preference Updated',
        `Your default currency is now set to ${currency}. All investment values will be displayed in ${currency} when possible.`,
        [{ text: 'OK', onPress: () => loadInvestmentData() }]
      );
    } catch (error) {
      console.error('❌ Error saving currency preference:', error);
      Alert.alert('Error', 'Failed to save currency preference');
    }
  };

  const saveRiskProfile = async (profile) => {
    try {
      const userId = await getCurrentUserId();
      await AsyncStorage.setItem(`investment_risk_profile_${userId}`, profile);
      Alert.alert('Saved', `Risk profile set to ${profile}`);
    } catch (error) {
      console.error('❌ Error saving risk profile:', error);
    }
  };

  const toggleNotification = async (type) => {
    try {
      const userId = await getCurrentUserId();
      const currentSetting = await AsyncStorage.getItem(`investment_notification_${type}_${userId}`);
      const newSetting = currentSetting === 'true' ? 'false' : 'true';
      await AsyncStorage.setItem(`investment_notification_${type}_${userId}`, newSetting);
      Alert.alert('Updated', `${type.replace('_', ' ')} notifications ${newSetting === 'true' ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('❌ Error toggling notification:', error);
    }
  };

  const getPortfolioTypeIcon = (portfolioType) => {
    const icons = {
      general: 'briefcase',
      retirement: 'time',
      education: 'school',
      aggressive: 'trending-up',
      conservative: 'shield-checkmark',
      balanced: 'scale'
    };
    return icons[portfolioType] || 'briefcase';
  };

  const getPortfolioTypeColor = (portfolioType) => {
    const colors = {
      general: '#4ECDC4',
      retirement: '#6C5CE7',
      education: '#FECA57',
      aggressive: '#FF6B35',
      conservative: '#96CEB4',
      balanced: '#45B7D1'
    };
    return colors[portfolioType] || '#4ECDC4';
  };

  const getPerformanceColor = (value) => {
    if (value > 0) return theme.colors.success;
    if (value < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderMarketStatus = () => {
    if (!marketStatus) return null;

    return (
      <View style={styles.marketStatusCard}>
        <View style={styles.marketStatusHeader}>
          <View style={styles.marketStatusIndicator}>
            <View style={[
              styles.marketStatusDot,
              { backgroundColor: marketStatus.isOpen ? theme.colors.success : theme.colors.error }
            ]} />
            <Text style={styles.marketStatusText}>
              Market {marketStatus.isOpen ? 'Open' : 'Closed'}
            </Text>
          </View>
          <TouchableOpacity onPress={() => navigation.navigate('MarketOverview')}>
            <Ionicons name="trending-up" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
        
        {!marketStatus.isOpen && (
          <Text style={styles.marketNextOpen}>
            Next open: {formatDate(marketStatus.nextOpen)}
          </Text>
        )}
      </View>
    );
  };

  const renderSummaryCard = () => {
    if (!summary) return null;

    const totalReturn = summary.totalPortfolioValue - summary.totalInvested;
    const returnPercentage = summary.totalInvested > 0 ? 
      (totalReturn / summary.totalInvested) * 100 : 0;

    return (
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Total Portfolio Value</Text>
          <TouchableOpacity onPress={() => navigation.navigate('InvestmentAnalytics')}>
            <Ionicons name="analytics-outline" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.totalValue}>
          {formatCurrency(summary.totalPortfolioValue || 0, 'UGX')}
        </Text>
        
        <View style={styles.performanceRow}>
          <Text style={[styles.performanceText, { color: getPerformanceColor(totalReturn) }]}>
            {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn, 'UGX')}
          </Text>
          <Text style={[styles.performancePercent, { color: getPerformanceColor(totalReturn) }]}>
            ({returnPercentage >= 0 ? '+' : ''}{returnPercentage.toFixed(2)}%)
          </Text>
        </View>
        
        <View style={styles.summaryStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{summary.totalPortfolios || 0}</Text>
            <Text style={styles.statLabel}>Portfolios</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{formatCurrency(summary.totalInvested || 0, 'UGX')}</Text>
            <Text style={styles.statLabel}>Invested</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{summary.uniqueAssets || 0}</Text>
            <Text style={styles.statLabel}>Assets</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity style={styles.actionItem} onPress={handleCreatePortfolio}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.primary + '20' }]}>
            <Ionicons name="add" size={24} color={theme.colors.primary} />
          </View>
          <Text style={styles.actionLabel}>Create Portfolio</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('AssetSearch')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.success + '20' }]}>
            <Ionicons name="search" size={24} color={theme.colors.success} />
          </View>
          <Text style={styles.actionLabel}>Find Assets</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('InvestmentTransactions')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.warning + '20' }]}>
            <Ionicons name="swap-horizontal" size={24} color={theme.colors.warning} />
          </View>
          <Text style={styles.actionLabel}>Trade</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('InvestmentReports')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.info + '20' }]}>
            <Ionicons name="bar-chart" size={24} color={theme.colors.info} />
          </View>
          <Text style={styles.actionLabel}>Reports</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPortfolioCard = ({ item: portfolio }) => {
    const totalReturn = portfolio.totalValue - portfolio.totalInvested;
    const returnPercentage = portfolio.totalInvested > 0 ? 
      (totalReturn / portfolio.totalInvested) * 100 : 0;

    return (
      <TouchableOpacity 
        style={styles.portfolioCard}
        onPress={() => handlePortfolioPress(portfolio)}
      >
        <View style={styles.portfolioHeader}>
          <View style={[styles.portfolioIcon, { backgroundColor: getPortfolioTypeColor(portfolio.portfolioType) }]}>
            <Ionicons name={getPortfolioTypeIcon(portfolio.portfolioType)} size={20} color={theme.colors.white} />
          </View>
          <View style={styles.portfolioInfo}>
            <Text style={styles.portfolioName}>{portfolio.portfolioName}</Text>
            <Text style={styles.portfolioType}>
              {portfolio.portfolioType.charAt(0).toUpperCase() + portfolio.portfolioType.slice(1)} Portfolio
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </View>
        
        <View style={styles.portfolioValue}>
          <Text style={styles.valueAmount}>{formatCurrency(portfolio.totalValue, portfolio.currency)}</Text>
          <View style={styles.performanceRow}>
            <Text style={[styles.returnText, { color: getPerformanceColor(totalReturn) }]}>
              {totalReturn >= 0 ? '+' : ''}{formatCurrency(totalReturn, portfolio.currency)}
            </Text>
            <Text style={[styles.returnPercent, { color: getPerformanceColor(totalReturn) }]}>
              ({returnPercentage >= 0 ? '+' : ''}{returnPercentage.toFixed(2)}%)
            </Text>
          </View>
        </View>
        
        <View style={styles.portfolioDetails}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Holdings</Text>
            <Text style={styles.detailValue}>{portfolio.holdingsCount || 0}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Cash</Text>
            <Text style={styles.detailValue}>{formatCurrency(portfolio.cashBalance, portfolio.currency)}</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Risk</Text>
            <Text style={styles.detailValue}>{portfolio.riskLevel}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="trending-up-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Start Your Investment Journey</Text>
      <Text style={styles.emptyDescription}>
        Create your first investment portfolio and start building wealth for your future
      </Text>
      <TouchableOpacity style={styles.createButton} onPress={handleCreatePortfolio}>
        <Text style={styles.createButtonText}>Create Portfolio</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading investments...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Investments</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={handleInvestmentSettings} style={styles.headerButton}>
            <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <TouchableOpacity onPress={handleCreatePortfolio} style={styles.headerButton}>
            <Ionicons name="add" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderMarketStatus()}
        {renderSummaryCard()}
        {renderQuickActions()}
        
        {portfolios.length > 0 ? (
          <View style={styles.portfoliosSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Your Portfolios</Text>
              <TouchableOpacity onPress={() => navigation.navigate('InvestmentPortfoliosList')}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={portfolios}
              renderItem={renderPortfolioCard}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          </View>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 12,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  marketStatusCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  marketStatusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  marketStatusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  marketStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  marketStatusText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  marketNextOpen: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  totalValue: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 8,
  },
  performanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  performanceText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  performancePercent: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  quickActionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionItem: {
    alignItems: 'center',
    flex: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'center',
  },
  portfoliosSection: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  portfolioCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  portfolioHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  portfolioIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  portfolioInfo: {
    flex: 1,
  },
  portfolioName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  portfolioType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  portfolioValue: {
    marginBottom: 12,
  },
  valueAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  returnText: {
    fontSize: 14,
    fontWeight: '600',
    marginRight: 8,
  },
  returnPercent: {
    fontSize: 12,
    fontWeight: '500',
  },
  portfolioDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default InvestmentDashboardScreen;
