#!/usr/bin/env node

/**
 * Fix Hardcoded Text Script
 * 
 * This script automatically finds and suggests fixes for hardcoded text
 * in React Native components that should use translation keys.
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 JiraniPay Hardcoded Text Fixer');
console.log('=================================\n');

/**
 * Find and suggest fixes for hardcoded text
 */
function findAndFixHardcodedText(filePath, content) {
  const fixes = [];
  
  // Pattern 1: Alert.alert with hardcoded strings
  const alertPattern = /Alert\.alert\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]/g;
  let match;
  
  while ((match = alertPattern.exec(content)) !== null) {
    const title = match[1];
    const message = match[2];
    
    if (title.length > 3 && !title.includes('t(')) {
      const titleKey = generateTranslationKey(title);
      const messageKey = generateTranslationKey(message);
      
      fixes.push({
        type: 'Alert.alert',
        original: match[0],
        suggested: `Alert.alert(t('${titleKey}'), t('${messageKey}')`,
        line: getLineNumber(content, match.index),
        translations: {
          [titleKey]: title,
          [messageKey]: message
        }
      });
    }
  }
  
  // Pattern 2: Text components with hardcoded strings
  const textPattern = /<Text[^>]*>\s*([^<{]+)\s*<\/Text>/g;
  
  while ((match = textPattern.exec(content)) !== null) {
    const text = match[1].trim();
    
    if (text.length > 3 && !text.includes('{') && !text.includes('t(')) {
      const key = generateTranslationKey(text);
      
      fixes.push({
        type: 'Text Component',
        original: match[0],
        suggested: match[0].replace(text, `{t('${key}')}`),
        line: getLineNumber(content, match.index),
        translations: {
          [key]: text
        }
      });
    }
  }
  
  // Pattern 3: Button titles
  const buttonPattern = /title\s*=\s*['"`]([^'"`]+)['"`]/g;
  
  while ((match = buttonPattern.exec(content)) !== null) {
    const title = match[1];
    
    if (title.length > 2 && !title.includes('t(')) {
      const key = generateTranslationKey(title);
      
      fixes.push({
        type: 'Button Title',
        original: match[0],
        suggested: `title={t('${key}')}`,
        line: getLineNumber(content, match.index),
        translations: {
          [key]: title
        }
      });
    }
  }
  
  return fixes;
}

/**
 * Generate translation key from text
 */
function generateTranslationKey(text) {
  // Clean and convert to camelCase
  return text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 0)
    .map((word, index) => index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
    .slice(0, 50); // Limit length
}

/**
 * Get line number for a position in content
 */
function getLineNumber(content, index) {
  return content.substring(0, index).split('\n').length;
}

/**
 * Apply fixes to a file
 */
function applyFixes(filePath, fixes) {
  if (fixes.length === 0) return;
  
  console.log(`\n🔧 Applying ${fixes.length} fixes to ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Apply fixes in reverse order to maintain positions
  fixes.reverse().forEach(fix => {
    content = content.replace(fix.original, fix.suggested);
    console.log(`   ✅ Fixed ${fix.type} at line ${fix.line}`);
  });
  
  // Write back to file
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ Applied all fixes to ${filePath}`);
}

/**
 * Generate translation entries
 */
function generateTranslationEntries(fixes) {
  const entries = {};
  
  fixes.forEach(fix => {
    Object.assign(entries, fix.translations);
  });
  
  return entries;
}

/**
 * Scan and fix files
 */
function scanAndFixFiles() {
  const projectRoot = path.join(__dirname, '..');
  const filesToScan = [
    'screens/DashboardScreen.js',
    'screens/ProfileScreen.js',
    'screens/LoginScreen.js',
    'screens/WalletScreen.js',
    'screens/BillPaymentScreen.js',
    'components/BottomNavigation.js',
    // Additional sub-screens identified by user
    'screens/EditProfileScreen.js',
    'screens/AccountVerificationScreen.js',
    'screens/EmailVerificationScreen.js',
    'screens/IdentityVerificationScreen.js',
    'screens/AddressVerificationScreen.js',
    'screens/VerificationStatusScreen.js',
    'screens/AccountLimitsScreen.js',
    'screens/SecuritySettingsScreen.js',
    'screens/PinSetupScreen.js',
    'screens/TwoFactorAuthScreen.js',
    'screens/SessionTimeoutScreen.js',
    'screens/TrustedDevicesScreen.js',
    'screens/SecurityActivityScreen.js',
    'screens/SecurityTipsScreen.js',
    'screens/SecurityFAQScreen.js',
    'screens/PrivacyDataScreen.js',
    'screens/ExportDataScreen.js',
    'screens/PrivacyPolicyScreen.js',
    'screens/DataProtectionScreen.js',
    'screens/DeleteAccountScreen.js',
    'screens/FAQScreen.js',
    'screens/ContactSupportScreen.js',
    'screens/AIChatScreen.js'
  ];
  
  const allFixes = [];
  const allTranslations = {};
  
  console.log(`📁 Scanning ${filesToScan.length} files for hardcoded text...\n`);
  
  filesToScan.forEach(relativePath => {
    const filePath = path.join(projectRoot, relativePath);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ File not found: ${relativePath}`);
      return;
    }
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const fixes = findAndFixHardcodedText(filePath, content);
      
      if (fixes.length > 0) {
        console.log(`📄 ${relativePath}: ${fixes.length} issues found`);
        
        fixes.forEach(fix => {
          console.log(`   Line ${fix.line}: ${fix.type} - "${Object.values(fix.translations)[0]}"`);
        });
        
        allFixes.push({ filePath, fixes });
        Object.assign(allTranslations, generateTranslationEntries(fixes));
      } else {
        console.log(`✅ ${relativePath}: No hardcoded text found`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${relativePath}:`, error.message);
    }
  });
  
  return { allFixes, allTranslations };
}

/**
 * Generate translation file updates
 */
function generateTranslationFileUpdates(translations) {
  console.log('\n📝 SUGGESTED TRANSLATION ADDITIONS:');
  console.log('===================================');
  
  console.log('\n// Add these to locales/en.js:');
  Object.entries(translations).forEach(([key, value]) => {
    console.log(`${key}: '${value}',`);
  });
  
  console.log('\n// Add these to locales/sw.js (Swahili translations needed):');
  Object.entries(translations).forEach(([key, value]) => {
    console.log(`${key}: '[TRANSLATE: ${value}]',`);
  });
  
  console.log('\n// Add these to locales/fr.js (French translations needed):');
  Object.entries(translations).forEach(([key, value]) => {
    console.log(`${key}: '[TRANSLATE: ${value}]',`);
  });
  
  console.log('\n// Add these to locales/ar.js (Arabic translations needed):');
  Object.entries(translations).forEach(([key, value]) => {
    console.log(`${key}: '[TRANSLATE: ${value}]',`);
  });
  
  console.log('\n// Add these to locales/am.js (Amharic translations needed):');
  Object.entries(translations).forEach(([key, value]) => {
    console.log(`${key}: '[TRANSLATE: ${value}]',`);
  });
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting hardcoded text detection and fixing...\n');
  
  const { allFixes, allTranslations } = scanAndFixFiles();
  
  console.log('\n📊 SUMMARY');
  console.log('==========');
  console.log(`Files with issues: ${allFixes.length}`);
  console.log(`Total fixes needed: ${allFixes.reduce((sum, { fixes }) => sum + fixes.length, 0)}`);
  console.log(`Translation keys to add: ${Object.keys(allTranslations).length}`);
  
  if (allFixes.length > 0) {
    console.log('\n🎯 NEXT STEPS:');
    console.log('==============');
    console.log('1. Review the suggested fixes above');
    console.log('2. Add the translation keys to all language files');
    console.log('3. Apply the code fixes to replace hardcoded text');
    console.log('4. Test language switching to ensure everything works');
    
    // Ask user if they want to apply fixes automatically
    console.log('\n⚠️ AUTOMATIC FIXES:');
    console.log('This script can automatically apply the fixes, but please review them first.');
    console.log('To apply fixes automatically, run: node scripts/fix-hardcoded-text.js --apply');
    
    // Check if --apply flag is present
    if (process.argv.includes('--apply')) {
      console.log('\n🔧 Applying fixes automatically...');
      
      allFixes.forEach(({ filePath, fixes }) => {
        applyFixes(filePath, fixes);
      });
      
      console.log('\n✅ All fixes applied! Please review the changes and test the app.');
    }
    
    generateTranslationFileUpdates(allTranslations);
  } else {
    console.log('\n🎉 No hardcoded text found! Your app is ready for multilingual support.');
  }
  
  process.exit(allFixes.length > 0 ? 1 : 0);
}

// Run the script
main();
