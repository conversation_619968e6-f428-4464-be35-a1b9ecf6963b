# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Environment files with actual credentials - NEVER COMMIT THESE
.env
.env.local
.env.development.local
.env.staging.local
.env.production.local
.env.test.local

# Legacy environment files (if they contain real credentials)
.env.production
.env.staging
.env.development

# Backend environment files with actual credentials
backend/.env
backend/.env.local
backend/.env.development.local
backend/.env.staging.local
backend/.env.production.local

# Security - Never commit these files
*.pem
*.key
secrets/
config/secrets.json
supabase-credentials.json
api-keys.json

# typescript
*.tsbuildinfo

# Production Security
config/production.json
database-credentials.json
# Development artifacts
test-*.js
*-test.js
mock-*.js
*-mock.js
debug.log
*.debug