-- Privacy Compliance Production Schema Updates
-- Ensures full compliance with East African data protection regulations

-- 1. Add consent audit trail table
CREATE TABLE IF NOT EXISTS public.consent_audit_trail (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    consent_type TEXT NOT NULL,
    previous_value BOOLEAN,
    new_value BOOLEAN NOT NULL,
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    location_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Add marketing campaign tracking
CREATE TABLE IF NOT EXISTS public.marketing_campaigns (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    campaign_name TEXT NOT NULL,
    campaign_type TEXT NOT NULL, -- 'email', 'sms', 'push'
    target_audience JSONB,
    content_template JSONB,
    consent_required TEXT[] DEFAULT ARRAY['marketing'],
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Add marketing delivery logs
CREATE TABLE IF NOT EXISTS public.marketing_delivery_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES public.marketing_campaigns(id),
    delivery_type TEXT NOT NULL, -- 'email', 'sms', 'push'
    status TEXT NOT NULL, -- 'sent', 'delivered', 'failed', 'blocked_no_consent'
    consent_checked BOOLEAN DEFAULT TRUE,
    consent_status JSONB,
    error_message TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Add data sharing agreements
CREATE TABLE IF NOT EXISTS public.data_sharing_agreements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    partner_id TEXT NOT NULL,
    partner_name TEXT NOT NULL,
    data_types TEXT[] NOT NULL,
    purpose TEXT NOT NULL,
    consent_given BOOLEAN DEFAULT FALSE,
    consent_timestamp TIMESTAMP WITH TIME ZONE,
    agreement_expires_at TIMESTAMP WITH TIME ZONE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Add location tracking logs
CREATE TABLE IF NOT EXISTS public.location_tracking_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    location_data JSONB NOT NULL,
    accuracy_meters DECIMAL,
    purpose TEXT NOT NULL, -- 'security', 'fraud_detection', 'service_enhancement'
    consent_verified BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Update privacy_settings table with additional compliance fields
ALTER TABLE public.privacy_settings 
ADD COLUMN IF NOT EXISTS consent_version TEXT DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS last_consent_review TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS gdpr_lawful_basis JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS data_retention_preferences JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS third_party_sharing_log JSONB DEFAULT '[]';

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_consent_audit_user_id ON public.consent_audit_trail(user_id);
CREATE INDEX IF NOT EXISTS idx_consent_audit_type ON public.consent_audit_trail(consent_type);
CREATE INDEX IF NOT EXISTS idx_consent_audit_created_at ON public.consent_audit_trail(created_at);

CREATE INDEX IF NOT EXISTS idx_marketing_delivery_user_id ON public.marketing_delivery_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_marketing_delivery_campaign_id ON public.marketing_delivery_logs(campaign_id);
CREATE INDEX IF NOT EXISTS idx_marketing_delivery_status ON public.marketing_delivery_logs(status);

CREATE INDEX IF NOT EXISTS idx_data_sharing_user_id ON public.data_sharing_agreements(user_id);
CREATE INDEX IF NOT EXISTS idx_data_sharing_partner ON public.data_sharing_agreements(partner_id);

CREATE INDEX IF NOT EXISTS idx_location_tracking_user_id ON public.location_tracking_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_location_tracking_created_at ON public.location_tracking_logs(created_at);

-- 8. Enable Row Level Security
ALTER TABLE public.consent_audit_trail ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.marketing_delivery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.data_sharing_agreements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_tracking_logs ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for consent audit trail
CREATE POLICY "Users can view their own consent audit trail" ON public.consent_audit_trail
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert consent audit records" ON public.consent_audit_trail
    FOR INSERT WITH CHECK (true);

-- 10. Create RLS policies for marketing delivery logs
CREATE POLICY "Users can view their own marketing delivery logs" ON public.marketing_delivery_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert marketing delivery logs" ON public.marketing_delivery_logs
    FOR INSERT WITH CHECK (true);

-- 11. Create RLS policies for data sharing agreements
CREATE POLICY "Users can view their own data sharing agreements" ON public.data_sharing_agreements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own data sharing agreements" ON public.data_sharing_agreements
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert data sharing agreements" ON public.data_sharing_agreements
    FOR INSERT WITH CHECK (true);

-- 12. Create RLS policies for location tracking logs
CREATE POLICY "Users can view their own location tracking logs" ON public.location_tracking_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert location tracking logs" ON public.location_tracking_logs
    FOR INSERT WITH CHECK (true);

-- 13. Create RLS policies for marketing campaigns (admin only)
CREATE POLICY "Authenticated users can view active marketing campaigns" ON public.marketing_campaigns
    FOR SELECT USING (active = true);

-- 14. Create function to log consent changes
CREATE OR REPLACE FUNCTION log_consent_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Log consent changes to audit trail
    IF OLD.consent_given IS DISTINCT FROM NEW.consent_given THEN
        INSERT INTO public.consent_audit_trail (
            user_id,
            consent_type,
            previous_value,
            new_value,
            change_reason,
            ip_address
        )
        SELECT 
            NEW.user_id,
            key,
            (OLD.consent_given->>key)::boolean,
            (NEW.consent_given->>key)::boolean,
            'user_update',
            inet_client_addr()
        FROM jsonb_each_text(NEW.consent_given)
        WHERE (OLD.consent_given->>key)::boolean IS DISTINCT FROM (NEW.consent_given->>key)::boolean;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 15. Create trigger for consent change logging
DROP TRIGGER IF EXISTS trigger_log_consent_changes ON public.privacy_settings;
CREATE TRIGGER trigger_log_consent_changes
    AFTER UPDATE ON public.privacy_settings
    FOR EACH ROW
    EXECUTE FUNCTION log_consent_change();

-- 16. Create function for data retention cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- Clean up old marketing delivery logs (keep for 2 years)
    DELETE FROM public.marketing_delivery_logs 
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    -- Clean up old location tracking logs based on user preferences
    DELETE FROM public.location_tracking_logs 
    WHERE created_at < NOW() - INTERVAL '1 year'
    AND user_id NOT IN (
        SELECT user_id FROM public.privacy_settings 
        WHERE (data_retention_preferences->>'location_extended')::boolean = true
    );
    
    -- Clean up revoked data sharing agreements (keep audit trail)
    UPDATE public.data_sharing_agreements 
    SET partner_name = 'REDACTED', data_types = ARRAY['REDACTED']
    WHERE revoked_at < NOW() - INTERVAL '1 year';
    
    RAISE NOTICE 'Data retention cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- 17. Grant necessary permissions
GRANT SELECT, INSERT ON public.consent_audit_trail TO authenticated;
GRANT SELECT ON public.marketing_campaigns TO authenticated;
GRANT SELECT, INSERT ON public.marketing_delivery_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.data_sharing_agreements TO authenticated;
GRANT SELECT, INSERT ON public.location_tracking_logs TO authenticated;

-- 18. Insert default marketing campaigns for production
INSERT INTO public.marketing_campaigns (campaign_name, campaign_type, content_template, consent_required) VALUES
('Welcome Series', 'email', '{"subject": "Welcome to JiraniPay", "template": "welcome_email"}', ARRAY['marketing']),
('Financial Tips Weekly', 'email', '{"subject": "Your Weekly Financial Tip", "template": "financial_tip"}', ARRAY['marketing']),
('Security Alerts', 'sms', '{"template": "security_alert_sms"}', ARRAY['essential']),
('Transaction Notifications', 'push', '{"template": "transaction_push"}', ARRAY['essential'])
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'Privacy compliance production schema updated successfully!' as status;
