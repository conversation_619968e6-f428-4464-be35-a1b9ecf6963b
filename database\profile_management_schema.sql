-- =====================================================
-- PROFILE MANAGEMENT SYSTEM DATABASE SCHEMA
-- =====================================================
-- Enhanced schema for comprehensive profile management

-- =====================================================
-- KYC VERIFICATION TABLES
-- =====================================================

-- KYC Documents Table
CREATE TABLE IF NOT EXISTS public.kyc_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL, -- 'national_id', 'passport', 'driving_license', 'utility_bill'
    document_number TEXT,
    document_url TEXT NOT NULL, -- Supabase Storage URL
    verification_status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'expired'
    verification_notes TEXT,
    verified_by UUID REFERENCES auth.users(id),
    verified_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    country_code TEXT NOT NULL DEFAULT 'UG',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- KYC Verification Levels
CREATE TABLE IF NOT EXISTS public.kyc_verification_levels (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    verification_level TEXT NOT NULL DEFAULT 'basic', -- 'basic', 'standard', 'premium'
    daily_limit DECIMAL(15,2) DEFAULT 100000, -- UGX
    monthly_limit DECIMAL(15,2) DEFAULT 3000000, -- UGX
    annual_limit DECIMAL(15,2) DEFAULT 36000000, -- UGX
    features_enabled JSONB DEFAULT '[]', -- Array of enabled features
    achieved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- AUDIT LOGS TABLE (Required for security/privacy logging)
-- =====================================================

-- Audit Logs for Security and Privacy Events
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- 'biometric_enabled', 'pin_setup', 'data_exported', etc.
    resource_type TEXT NOT NULL, -- 'security', 'privacy', 'profile'
    old_values JSONB, -- Previous values (for updates)
    new_values JSONB, -- New values or metadata
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- SECURITY & PRIVACY TABLES
-- =====================================================

-- Security Settings
CREATE TABLE IF NOT EXISTS public.security_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    pin_enabled BOOLEAN DEFAULT FALSE,
    pin_hash TEXT, -- Hashed PIN
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_method TEXT, -- 'sms', 'email', 'app'
    biometric_enabled BOOLEAN DEFAULT FALSE,
    session_timeout_minutes INTEGER DEFAULT 30,
    login_attempts_limit INTEGER DEFAULT 5,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    last_password_change TIMESTAMP WITH TIME ZONE,
    security_questions JSONB, -- Array of security questions and hashed answers
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Privacy Settings
CREATE TABLE IF NOT EXISTS public.privacy_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    data_sharing_enabled BOOLEAN DEFAULT FALSE,
    marketing_emails BOOLEAN DEFAULT TRUE,
    marketing_sms BOOLEAN DEFAULT TRUE,
    analytics_tracking BOOLEAN DEFAULT TRUE,
    profile_visibility TEXT DEFAULT 'private', -- 'public', 'friends', 'private'
    transaction_history_retention_days INTEGER DEFAULT 2555, -- 7 years
    data_export_requested BOOLEAN DEFAULT FALSE,
    data_export_requested_at TIMESTAMP WITH TIME ZONE,
    account_deletion_requested BOOLEAN DEFAULT FALSE,
    account_deletion_requested_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PROFILE COMPLETION TRACKING
-- =====================================================

-- Profile Completion Steps
CREATE TABLE IF NOT EXISTS public.profile_completion_steps (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    step_name TEXT NOT NULL, -- 'basic_info', 'profile_picture', 'kyc_basic', 'kyc_standard', etc.
    completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP WITH TIME ZONE,
    weight INTEGER DEFAULT 1, -- Weight for completion percentage calculation
    required_for_level TEXT DEFAULT 'basic', -- 'basic', 'standard', 'premium'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, step_name)
);

-- =====================================================
-- NOTIFICATION PREFERENCES
-- =====================================================

-- Enhanced Notification Settings
CREATE TABLE IF NOT EXISTS public.notification_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT NOT NULL, -- 'transactions', 'security', 'marketing', 'system'
    channel TEXT NOT NULL, -- 'push', 'sms', 'email', 'in_app'
    enabled BOOLEAN DEFAULT TRUE,
    frequency TEXT DEFAULT 'immediate', -- 'immediate', 'daily', 'weekly', 'monthly'
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone TEXT DEFAULT 'Africa/Kampala',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, category, channel)
);

-- =====================================================
-- SUPPORT & HELP SYSTEM
-- =====================================================

-- Support Tickets
CREATE TABLE IF NOT EXISTS public.support_tickets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    ticket_number TEXT UNIQUE NOT NULL,
    subject TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL, -- 'technical', 'billing', 'account', 'general'
    priority TEXT DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    status TEXT DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
    assigned_to UUID REFERENCES auth.users(id),
    resolution_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support Ticket Messages
CREATE TABLE IF NOT EXISTS public.support_ticket_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ticket_id UUID REFERENCES public.support_tickets(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE, -- Internal staff notes
    attachments JSONB, -- Array of attachment URLs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- DEVICE MANAGEMENT
-- =====================================================

-- User Devices
CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL, -- Unique device identifier
    device_name TEXT NOT NULL,
    device_type TEXT NOT NULL, -- 'mobile', 'tablet', 'web'
    platform TEXT NOT NULL, -- 'ios', 'android', 'web'
    app_version TEXT,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_trusted BOOLEAN DEFAULT FALSE,
    push_token TEXT, -- For push notifications
    location_data JSONB, -- Last known location (if permitted)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- KYC Documents indexes
CREATE INDEX IF NOT EXISTS idx_kyc_documents_user_id ON public.kyc_documents(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_documents_status ON public.kyc_documents(verification_status);
CREATE INDEX IF NOT EXISTS idx_kyc_documents_type ON public.kyc_documents(document_type);

-- Security settings indexes
CREATE INDEX IF NOT EXISTS idx_security_settings_user_id ON public.security_settings(user_id);

-- Privacy settings indexes
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON public.privacy_settings(user_id);

-- Profile completion indexes
CREATE INDEX IF NOT EXISTS idx_profile_completion_user_id ON public.profile_completion_steps(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_completion_step ON public.profile_completion_steps(step_name);

-- Support tickets indexes
CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON public.support_tickets(user_id);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON public.support_tickets(status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_number ON public.support_tickets(ticket_number);

-- Device management indexes
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);

-- Audit logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON public.audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.kyc_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kyc_verification_levels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profile_completion_steps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own kyc_documents" ON public.kyc_documents FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own kyc_documents" ON public.kyc_documents FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own kyc_documents" ON public.kyc_documents FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own verification_levels" ON public.kyc_verification_levels FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own security_settings" ON public.security_settings FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own privacy_settings" ON public.privacy_settings FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own completion_steps" ON public.profile_completion_steps FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own notifications" ON public.notification_preferences FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own tickets" ON public.support_tickets FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own devices" ON public.user_devices FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own audit_logs" ON public.audit_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own audit_logs" ON public.audit_logs FOR INSERT WITH CHECK (auth.uid() = user_id);
