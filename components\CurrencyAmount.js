import React from 'react';
import { Text } from 'react-native';
import { useCurrencyContext } from '../contexts/CurrencyContext';

/**
 * Universal Currency Amount Component
 * 
 * Automatically converts and displays amounts in user's preferred currency
 * Supports real-time updates when currency preference changes
 */
const CurrencyAmount = ({
  amount,
  fromCurrency = 'UGX',
  targetCurrency = null,
  style = {},
  options = {},
  ...textProps
}) => {
  const { convert, formatAmount, userCurrency, isLoading } = useCurrencyContext();

  if (isLoading) {
    return <Text style={style} {...textProps}>Loading...</Text>;
  }

  try {
    const target = targetCurrency || userCurrency;
    const convertedAmount = convert(amount, fromCurrency, target);
    const formattedAmount = formatAmount(convertedAmount, target, {
      showSymbol: true,
      showFlag: false,
      compact: false,
      ...options
    });

    return (
      <Text style={style} {...textProps}>
        {formattedAmount}
      </Text>
    );
  } catch (error) {
    console.error('❌ CurrencyAmount: Error displaying amount:', error);
    return (
      <Text style={style} {...textProps}>
        {fromCurrency} {parseFloat(amount).toLocaleString()}
      </Text>
    );
  }
};

export default CurrencyAmount;
