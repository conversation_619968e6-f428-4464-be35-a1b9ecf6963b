-- Notifications & Receipts Database Schema
-- Creates tables for enhanced notifications, digital receipts, preferences, and delivery tracking

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- NOTIFICATIONS TABLE (Enhanced)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN (
        'transaction_completed', 'transaction_failed', 'money_received',
        'bill_payment_success', 'security_alert', 'fraud_alert',
        'limit_warning', 'account_verification', 'promotional'
    )),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    channels TEXT[] NOT NULL DEFAULT '{}',
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired', 'retrying')),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add columns to existing notifications table if they don't exist
DO $$
BEGIN
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'status') THEN
        ALTER TABLE public.notifications ADD COLUMN status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'expired', 'retrying'));
    END IF;

    -- Add channels column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'channels') THEN
        ALTER TABLE public.notifications ADD COLUMN channels TEXT[] DEFAULT '{}';
    END IF;

    -- Add priority column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'priority') THEN
        ALTER TABLE public.notifications ADD COLUMN priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical'));
    END IF;

    -- Add retry_count column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'retry_count') THEN
        ALTER TABLE public.notifications ADD COLUMN retry_count INTEGER DEFAULT 0;
    END IF;

    -- Add max_retries column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'max_retries') THEN
        ALTER TABLE public.notifications ADD COLUMN max_retries INTEGER DEFAULT 3;
    END IF;

    -- Add expires_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'expires_at') THEN
        ALTER TABLE public.notifications ADD COLUMN expires_at TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add data column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'notifications' AND column_name = 'data') THEN
        ALTER TABLE public.notifications ADD COLUMN data JSONB DEFAULT '{}';
    END IF;
END $$;

-- =====================================================
-- NOTIFICATION PREFERENCES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notification_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Channel preferences
    push_enabled BOOLEAN DEFAULT true,
    sms_enabled BOOLEAN DEFAULT true,
    email_enabled BOOLEAN DEFAULT true,
    
    -- Notification type preferences
    transaction_notifications BOOLEAN DEFAULT true,
    security_alerts BOOLEAN DEFAULT true,
    fraud_alerts BOOLEAN DEFAULT true,
    limit_warnings BOOLEAN DEFAULT true,
    promotional_notifications BOOLEAN DEFAULT false,
    
    -- Timing preferences
    quiet_hours_enabled BOOLEAN DEFAULT false,
    quiet_hours_start TIME DEFAULT '22:00:00',
    quiet_hours_end TIME DEFAULT '07:00:00',
    
    -- Frequency preferences
    digest_enabled BOOLEAN DEFAULT false,
    digest_frequency VARCHAR(20) DEFAULT 'daily' CHECK (digest_frequency IN ('daily', 'weekly', 'monthly')),
    
    -- Custom preferences
    custom_settings JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint per user
    UNIQUE(user_id)
);

-- =====================================================
-- NOTIFICATION DELIVERY LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notification_delivery_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    notification_id UUID NOT NULL REFERENCES public.notifications(id) ON DELETE CASCADE,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('push', 'sms', 'email')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('sent', 'delivered', 'failed', 'bounced')),
    provider VARCHAR(50),
    external_id VARCHAR(255),
    error_message TEXT,
    response_data JSONB DEFAULT '{}',
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    opened_at TIMESTAMP WITH TIME ZONE,
    clicked_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- DIGITAL RECEIPTS TABLE
-- =====================================================
-- First check if transactions table exists, if not create a placeholder
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        -- Create a minimal transactions table if it doesn't exist
        CREATE TABLE public.transactions (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS public.digital_receipts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    receipt_number VARCHAR(50) NOT NULL UNIQUE,
    receipt_data JSONB NOT NULL,
    qr_code_data JSONB NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'generated' CHECK (status IN ('generated', 'sent', 'downloaded', 'shared')),
    download_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint if transactions table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'transactions') THEN
        -- Add foreign key constraint if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'digital_receipts_transaction_id_fkey'
            AND table_name = 'digital_receipts'
        ) THEN
            ALTER TABLE public.digital_receipts
            ADD CONSTRAINT digital_receipts_transaction_id_fkey
            FOREIGN KEY (transaction_id) REFERENCES public.transactions(id) ON DELETE CASCADE;
        END IF;
    END IF;
END $$;

-- =====================================================
-- RECEIPT ACCESS LOGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.receipt_access_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    receipt_id UUID NOT NULL REFERENCES public.digital_receipts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    access_type VARCHAR(20) NOT NULL CHECK (access_type IN ('view', 'download', 'share', 'verify')),
    ip_address INET,
    user_agent TEXT,
    accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- NOTIFICATION TEMPLATES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notification_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL CHECK (channel IN ('push', 'sms', 'email')),
    language VARCHAR(10) DEFAULT 'en',
    subject_template TEXT,
    content_template TEXT NOT NULL,
    html_template TEXT,
    variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- NOTIFICATION ANALYTICS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.notification_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    notification_type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_opened INTEGER DEFAULT 0,
    total_clicked INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    delivery_rate DECIMAL(5,2) DEFAULT 0,
    open_rate DECIMAL(5,2) DEFAULT 0,
    click_rate DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for daily analytics
    UNIQUE(date, notification_type, channel)
);

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Notifications Indexes (with column existence check)
DO $$
BEGIN
    -- Create indexes only if columns exist
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'notifications' AND column_name = 'user_id') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_user_created ON public.notifications(user_id, created_at DESC);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'notifications' AND column_name = 'type') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'notifications' AND column_name = 'status') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_status ON public.notifications(status);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'notifications' AND column_name = 'priority') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_priority ON public.notifications(priority);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'notifications' AND column_name = 'read_at') THEN
        CREATE INDEX IF NOT EXISTS idx_notifications_unread ON public.notifications(user_id, read_at) WHERE read_at IS NULL;
    END IF;
END $$;

-- Notification Delivery Logs Indexes
CREATE INDEX IF NOT EXISTS idx_delivery_logs_notification ON public.notification_delivery_logs(notification_id);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_channel_status ON public.notification_delivery_logs(channel, status);
CREATE INDEX IF NOT EXISTS idx_delivery_logs_attempted ON public.notification_delivery_logs(attempted_at DESC);

-- Digital Receipts Indexes
CREATE INDEX IF NOT EXISTS idx_receipts_user_created ON public.digital_receipts(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_receipts_transaction ON public.digital_receipts(transaction_id);
CREATE INDEX IF NOT EXISTS idx_receipts_number ON public.digital_receipts(receipt_number);
CREATE INDEX IF NOT EXISTS idx_receipts_status ON public.digital_receipts(status);

-- Receipt Access Logs Indexes
CREATE INDEX IF NOT EXISTS idx_receipt_access_receipt ON public.receipt_access_logs(receipt_id);
CREATE INDEX IF NOT EXISTS idx_receipt_access_user ON public.receipt_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_receipt_access_type ON public.receipt_access_logs(access_type);
CREATE INDEX IF NOT EXISTS idx_receipt_access_time ON public.receipt_access_logs(accessed_at DESC);

-- Notification Templates Indexes
CREATE INDEX IF NOT EXISTS idx_templates_type_channel ON public.notification_templates(type, channel);
CREATE INDEX IF NOT EXISTS idx_templates_active ON public.notification_templates(is_active);

-- Notification Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_analytics_date ON public.notification_analytics(date DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_type ON public.notification_analytics(notification_type);
CREATE INDEX IF NOT EXISTS idx_analytics_channel ON public.notification_analytics(channel);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_delivery_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.digital_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.receipt_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_analytics ENABLE ROW LEVEL SECURITY;

-- Notifications Policies (with safe creation)
DO $$
BEGIN
    -- Only create policies if table exists and has required columns
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'user_id') THEN

        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
        DROP POLICY IF EXISTS "System can insert notifications" ON public.notifications;
        DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;

        -- Create new policies
        CREATE POLICY "Users can view their own notifications" ON public.notifications
            FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "System can insert notifications" ON public.notifications
            FOR INSERT WITH CHECK (true);

        CREATE POLICY "Users can update their own notifications" ON public.notifications
            FOR UPDATE USING (auth.uid() = user_id);
    END IF;
END $$;

-- Notification Preferences Policies
CREATE POLICY "Users can manage their own preferences" ON public.notification_preferences
    FOR ALL USING (auth.uid() = user_id);

-- Notification Delivery Logs Policies (Read-only for users)
CREATE POLICY "Users can view delivery logs for their notifications" ON public.notification_delivery_logs
    FOR SELECT USING (
        notification_id IN (
            SELECT id FROM public.notifications WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert delivery logs" ON public.notification_delivery_logs
    FOR INSERT WITH CHECK (true);

-- Digital Receipts Policies
CREATE POLICY "Users can view their own receipts" ON public.digital_receipts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert receipts" ON public.digital_receipts
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own receipts" ON public.digital_receipts
    FOR UPDATE USING (auth.uid() = user_id);

-- Receipt Access Logs Policies
CREATE POLICY "Users can view access logs for their receipts" ON public.receipt_access_logs
    FOR SELECT USING (
        receipt_id IN (
            SELECT id FROM public.digital_receipts WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert access logs" ON public.receipt_access_logs
    FOR INSERT WITH CHECK (true);

-- Notification Templates Policies (Read-only for users)
CREATE POLICY "Users can view active templates" ON public.notification_templates
    FOR SELECT USING (is_active = true);

-- Notification Analytics Policies (Read-only for users)
CREATE POLICY "Users can view analytics" ON public.notification_analytics
    FOR SELECT USING (true);

-- =====================================================
-- FUNCTIONS FOR NOTIFICATIONS & RECEIPTS
-- =====================================================

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID, p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.notifications
    SET read_at = NOW(), updated_at = NOW()
    WHERE id = p_notification_id AND user_id = p_user_id AND read_at IS NULL;
    
    RETURN FOUND;
END;
$$;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO v_count
    FROM public.notifications
    WHERE user_id = p_user_id AND read_at IS NULL AND expires_at > NOW();
    
    RETURN v_count;
END;
$$;

-- Function to cleanup expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_deleted INTEGER;
BEGIN
    -- Update expired notifications
    UPDATE public.notifications
    SET status = 'expired', updated_at = NOW()
    WHERE expires_at < NOW() AND status NOT IN ('expired', 'delivered');
    
    GET DIAGNOSTICS v_deleted = ROW_COUNT;
    
    RETURN v_deleted;
END;
$$;

-- Function to get receipt by transaction
CREATE OR REPLACE FUNCTION get_receipt_by_transaction(p_transaction_id UUID, p_user_id UUID)
RETURNS TABLE (
    receipt_id UUID,
    receipt_number VARCHAR,
    receipt_data JSONB,
    qr_code_data JSONB,
    status VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dr.id,
        dr.receipt_number,
        dr.receipt_data,
        dr.qr_code_data,
        dr.status,
        dr.created_at
    FROM public.digital_receipts dr
    WHERE dr.transaction_id = p_transaction_id AND dr.user_id = p_user_id;
END;
$$;

-- Function to update notification analytics
CREATE OR REPLACE FUNCTION update_notification_analytics(
    p_date DATE,
    p_type VARCHAR,
    p_channel VARCHAR,
    p_event VARCHAR
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.notification_analytics (date, notification_type, channel, total_sent)
    VALUES (p_date, p_type, p_channel, 1)
    ON CONFLICT (date, notification_type, channel)
    DO UPDATE SET
        total_sent = CASE WHEN p_event = 'sent' THEN notification_analytics.total_sent + 1 ELSE notification_analytics.total_sent END,
        total_delivered = CASE WHEN p_event = 'delivered' THEN notification_analytics.total_delivered + 1 ELSE notification_analytics.total_delivered END,
        total_opened = CASE WHEN p_event = 'opened' THEN notification_analytics.total_opened + 1 ELSE notification_analytics.total_opened END,
        total_clicked = CASE WHEN p_event = 'clicked' THEN notification_analytics.total_clicked + 1 ELSE notification_analytics.total_clicked END,
        total_failed = CASE WHEN p_event = 'failed' THEN notification_analytics.total_failed + 1 ELSE notification_analytics.total_failed END;
END;
$$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
GRANT ALL ON public.notification_preferences TO authenticated;
GRANT SELECT, INSERT ON public.notification_delivery_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.digital_receipts TO authenticated;
GRANT SELECT, INSERT ON public.receipt_access_logs TO authenticated;
GRANT SELECT ON public.notification_templates TO authenticated;
GRANT SELECT ON public.notification_analytics TO authenticated;

GRANT EXECUTE ON FUNCTION mark_notification_read TO authenticated;
GRANT EXECUTE ON FUNCTION get_unread_notification_count TO authenticated;
GRANT EXECUTE ON FUNCTION get_receipt_by_transaction TO authenticated;
