name: JiraniPay CI/CD Pipeline

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Install dependencies
        run: |
          cd backend
          npm ci

      - name: Run ESLint
        run: |
          cd backend
          npm run lint

      - name: Run Prettier check
        run: |
          cd backend
          npm run format:check

      - name: Security audit
        run: |
          cd backend
          npm audit --audit-level=high

      - name: Run CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

      - name: Run Snyk Security Scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  # Unit and Integration Tests
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: jiranipay_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Install dependencies
        run: |
          cd backend
          npm ci

      - name: Setup test database
        run: |
          cd backend
          npm run db:migrate:test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/jiranipay_test
          REDIS_URL: redis://localhost:6379

      - name: Run unit tests
        run: |
          cd backend
          npm run test:unit
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/jiranipay_test
          REDIS_URL: redis://localhost:6379

      - name: Run integration tests
        run: |
          cd backend
          npm run test:integration
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/jiranipay_test
          REDIS_URL: redis://localhost:6379

      - name: Generate test coverage
        run: |
          cd backend
          npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Build and Push Docker Images
  build:
    name: Build & Push Images
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name != 'pull_request'
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile.production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: Generate SBOM
        uses: anchore/sbom-action@v0
        with:
          image: ${{ steps.meta.outputs.tags }}
          format: spdx-json
          output-file: sbom.spdx.json

      - name: Upload SBOM
        uses: actions/upload-artifact@v4
        with:
          name: sbom
          path: sbom.spdx.json

  # Deploy to Development
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region us-east-1 --name jiranipay-dev-cluster

      - name: Deploy to Kubernetes
        run: |
          envsubst < k8s/overlays/development/kustomization.yaml | kubectl apply -k -
        env:
          IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
          ENVIRONMENT: development

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/jiranipay-backend -n jiranipay-dev
          kubectl get pods -n jiranipay-dev

      - name: Run smoke tests
        run: |
          cd backend
          npm run test:smoke -- --env=development
        env:
          API_BASE_URL: https://api-dev.jiranipay.com

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/staging'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region us-east-1 --name jiranipay-staging-cluster

      - name: Deploy to Kubernetes
        run: |
          envsubst < k8s/overlays/staging/kustomization.yaml | kubectl apply -k -
        env:
          IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
          ENVIRONMENT: staging

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/jiranipay-backend -n jiranipay-staging
          kubectl get pods -n jiranipay-staging

      - name: Run E2E tests
        run: |
          cd backend
          npm run test:e2e -- --env=staging
        env:
          API_BASE_URL: https://api-staging.jiranipay.com

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || github.event_name == 'release'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region us-east-1 --name jiranipay-prod-cluster

      - name: Blue-Green Deployment
        run: |
          # Deploy to green environment
          envsubst < k8s/overlays/production/kustomization-green.yaml | kubectl apply -k -
          
          # Wait for green deployment to be ready
          kubectl rollout status deployment/jiranipay-backend-green -n jiranipay-prod
          
          # Run health checks on green environment
          kubectl exec -n jiranipay-prod deployment/jiranipay-backend-green -- curl -f http://localhost:3000/health
          
          # Switch traffic to green (blue-green switch)
          kubectl patch service jiranipay-backend-service -n jiranipay-prod -p '{"spec":{"selector":{"version":"green"}}}'
          
          # Wait and verify
          sleep 30
          
          # Scale down blue environment
          kubectl scale deployment jiranipay-backend-blue --replicas=0 -n jiranipay-prod
        env:
          IMAGE_TAG: ${{ needs.build.outputs.image-tag }}
          ENVIRONMENT: production

      - name: Verify production deployment
        run: |
          kubectl get pods -n jiranipay-prod
          curl -f https://api.jiranipay.com/health

      - name: Run production smoke tests
        run: |
          cd backend
          npm run test:smoke -- --env=production
        env:
          API_BASE_URL: https://api.jiranipay.com

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name != 'pull_request'
    
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ needs.build.outputs.image-tag }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

  # Notification
  notify:
    name: Notify Teams
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
