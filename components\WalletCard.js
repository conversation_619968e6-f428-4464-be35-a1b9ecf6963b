import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import currencyService from '../services/currencyService';

const { width } = Dimensions.get('window');

/**
 * Simplified Modern WalletCard Component
 * Minimal, clean design with essential wallet information
 * Features contemporary fintech aesthetics with East African cultural elements
 */
const WalletCard = ({
  balance = 0,
  currency = 'UGX',
  accountNumber,
  onTopUp,
  onSend,
  onQRPay,
  onHistory,
  onRefresh,
  hideBalance = false,
  loading = false
}) => {
  const { theme, isDarkMode } = useTheme();
  const { t } = useLanguage();
  const [balanceVisible, setBalanceVisible] = useState(!hideBalance);
  const [scaleAnim] = useState(new Animated.Value(1));
  const [refreshAnim] = useState(new Animated.Value(0));
  const [userCurrency, setUserCurrency] = useState(currency);

  // Load user's preferred currency on mount
  useEffect(() => {
    loadUserCurrency();

    // Listen for currency changes
    const unsubscribe = currencyService.addCurrencyChangeListener((newCurrency) => {
      setUserCurrency(newCurrency);
    });

    return unsubscribe;
  }, []);

  const loadUserCurrency = async () => {
    try {
      const preferredCurrency = await currencyService.getUserPreferredCurrency();
      setUserCurrency(preferredCurrency);
    } catch (error) {
      console.error('❌ Error loading user currency:', error);
    }
  };

  const formatBalance = (amount) => {
    if (!balanceVisible) return '••••••';

    const numAmount = parseFloat(amount) || 0;

    try {
      // Use enhanced currency service for formatting
      return currencyService.formatAmountWithCurrency(numAmount, userCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false
      });
    } catch (error) {
      console.error('❌ Error formatting balance:', error);
      // Fallback to simple formatting
      return `${userCurrency} ${numAmount.toLocaleString()}`;
    }
  };

  const toggleBalanceVisibility = () => {
    // Animate the card slightly when toggling
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    setBalanceVisible(!balanceVisible);
  };

  const formatAccountNumber = (accountNum) => {
    if (!accountNum) return 'JP••••••••';
    return accountNum.replace(/(.{2})(.*)(.{4})/, '$1••••$3');
  };

  const handleTopUpPress = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onTopUp?.();
  };

  const handleSendPress = () => {
    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    onSend?.();
  };

  const handleRefreshPress = () => {
    // Animate refresh icon
    Animated.timing(refreshAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start(() => {
      refreshAnim.setValue(0);
    });

    onRefresh?.();
  };

  const handleQuickAction = (action) => {
    // Animate card slightly on action
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    switch (action) {
      case 'qr':
        onQRPay?.();
        break;
      case 'history':
        onHistory?.();
        break;
      default:
        break;
    }
  };

  // Dynamic gradient colors based on theme
  const gradientColors = isDarkMode
    ? ['#1a1a2e', '#16213e', '#0f3460']
    : [Colors.primary.main, Colors.accent.gold, '#E67E22'];

  const styles = createStyles(theme, isDarkMode);

  return (
    <View style={styles.container}>
      {/* JiraniPay Wallet Card - Exact copy of reference design */}
      <Animated.View
        style={[
          styles.cardContainer,
          { transform: [{ scale: scaleAnim }] }
        ]}
      >
        <LinearGradient
          colors={['#E67E22', '#D35400']} // Orange gradient like JiraniPay reference
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.card}
        >
          {/* Header Section */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.walletTitle}>{t('wallet.jiranipayWallet')}</Text>
              <Text style={styles.accountType}>{t('wallet.mainAccount')}</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={handleRefreshPress}
                activeOpacity={0.7}
              >
                <Animated.View
                  style={{
                    transform: [{
                      rotate: refreshAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    }],
                  }}
                >
                  <Ionicons
                    name="refresh"
                    size={20}
                    color="rgba(255, 255, 255, 0.9)"
                  />
                </Animated.View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={toggleBalanceVisibility}
                activeOpacity={0.7}
              >
                <Ionicons
                  name={balanceVisible ? "eye-outline" : "eye-off-outline"}
                  size={20}
                  color="rgba(255, 255, 255, 0.9)"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Balance Section */}
          <View style={styles.balanceSection}>
            <Text style={styles.balance}>{formatBalance(balance)}</Text>
            <Text style={styles.balanceLabel}>{t('wallet.availableBalance')}</Text>
          </View>

          {/* Action Buttons - JiraniPay Style */}
          <View style={styles.cardActions}>
            <TouchableOpacity
              style={styles.cardActionButton}
              onPress={handleSendPress}
              activeOpacity={0.8}
            >
              <Ionicons name="send" size={16} color="#E67E22" />
              <Text style={styles.cardActionText}>{t('wallet.send')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cardActionButton}
              onPress={handleTopUpPress}
              activeOpacity={0.8}
            >
              <Ionicons name="add" size={16} color="#E67E22" />
              <Text style={styles.cardActionText}>{t('wallet.topUp')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cardActionButton}
              onPress={() => handleQuickAction('qr')}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons name="qrcode-scan" size={16} color="#E67E22" />
              <Text style={styles.cardActionText}>{t('wallet.qrPay')}</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    </View>
  );
};

const createStyles = (theme, isDarkMode) => StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: 24,
    alignItems: 'center', // Center the card horizontally
  },

  // Card Container - JiraniPay Style
  cardContainer: {
    marginBottom: 0,
    width: width - 32, // Full width minus margins
    alignSelf: 'center', // Ensure card is centered
  },
  card: {
    borderRadius: 20,
    padding: 24,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
    minHeight: 200,
    width: '100%',
  },

  // Header Section
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  headerLeft: {
    flex: 1,
  },
  walletTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  accountType: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },
  headerRight: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },

  // Balance Section
  balanceSection: {
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  balance: {
    fontSize: 32,
    fontWeight: '700',
    color: '#fff',
    letterSpacing: -1,
    marginBottom: 4,
  },
  balanceLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },

  // Card Action Buttons - JiraniPay Style
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  cardActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 18,
    paddingVertical: 12,
    borderRadius: 22,
    flex: 1,
    justifyContent: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    minHeight: 44,
  },
  cardActionText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#E67E22',
  },
});

export default WalletCard;
