/**
 * Production API Service
 * Manages all production API integrations and real-world service connections
 */

import { isProductionMode } from '../config/environment';
import productionServices from '../config/productionServices';

class ProductionApiService {
  constructor() {
    this.baseUrl = 'https://api.jiranipay.com';
    this.timeout = 30000; // 30 seconds
    this.retryAttempts = 3;
  }

  /**
   * Initialize production API service
   */
  async initialize() {
    if (isProductionMode()) {
      try {
        const validation = productionServices.validate();
        if (validation.isValid) {
          if (validation.hasWarnings) {
            console.log('⚠️ Production API service initialized with warnings');
            console.log(`💡 ${validation.message}`);
          } else {
            console.log('✅ Production API service initialized successfully');
          }
          return { success: true, warnings: validation.warnings };
        } else {
          console.error('❌ Production API service initialization failed');
          validation.errors.forEach(error => console.error(`- ${error}`));
          return { success: false, errors: validation.errors };
        }
      } catch (error) {
        console.error('❌ Production API service initialization failed:', error);
        // Don't throw error - allow service to initialize with limited functionality
        console.log('💡 Continuing with limited API functionality');
        return { success: true, limited: true };
      }
    }

    console.log('🔧 Development mode: Production API service not initialized');
    return { success: true };
  }

  /**
   * Make authenticated API request
   */
  async makeRequest(endpoint, options = {}) {
    if (!isProductionMode()) {
      throw new Error('Production API calls not available in development mode');
    }

    const {
      method = 'GET',
      body = null,
      headers = {},
      timeout = this.timeout,
      retries = this.retryAttempts
    } = options;

    const url = `${this.baseUrl}${endpoint}`;
    
    const requestOptions = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Version': '1.0',
        'X-Client-Platform': 'mobile',
        ...headers
      },
      body: body ? JSON.stringify(body) : null,
    };

    // Add authentication headers
    const authToken = await this.getAuthToken();
    if (authToken) {
      requestOptions.headers['Authorization'] = `Bearer ${authToken}`;
    }

    let lastError;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        const response = await fetch(url, {
          ...requestOptions,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`API Error: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        return { success: true, data };
        
      } catch (error) {
        lastError = error;
        
        if (attempt < retries) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
          console.log(`🔄 Retrying API request (attempt ${attempt + 2}/${retries + 1})`);
        }
      }
    }
    
    console.error('❌ API request failed after all retries:', lastError);
    return { success: false, error: lastError.message };
  }

  /**
   * Get authentication token for API requests
   */
  async getAuthToken() {
    // Implementation would get token from secure storage or auth service
    // For now, return null - this should be implemented based on your auth system
    return null;
  }

  /**
   * Mobile Money API Integration
   */
  async processMobileMoneyPayment(provider, paymentData) {
    if (!isProductionMode()) {
      throw new Error('Mobile money payments not available in development mode');
    }

    const credentials = productionServices.getCredentials(provider);
    if (!credentials) {
      throw new Error(`No credentials configured for ${provider}`);
    }

    const endpoint = productionServices.endpoints.mobileMoney[provider]?.requestToPay;
    if (!endpoint) {
      throw new Error(`Mobile money endpoint not configured for ${provider}`);
    }

    return await this.makeRequest(endpoint, {
      method: 'POST',
      body: paymentData,
      headers: {
        'X-Provider': provider.toUpperCase(),
        'X-Provider-Key': credentials.apiKey,
      }
    });
  }

  /**
   * Bank API Integration
   */
  async verifyBankAccount(bankCode, accountNumber) {
    if (!isProductionMode()) {
      throw new Error('Bank verification not available in development mode');
    }

    return await this.makeRequest(productionServices.endpoints.banking.accountVerification, {
      method: 'POST',
      body: {
        bankCode,
        accountNumber,
        country: 'UG'
      }
    });
  }

  /**
   * Bill Payment API Integration
   */
  async validateBillAccount(providerId, accountNumber) {
    if (!isProductionMode()) {
      throw new Error('Bill validation not available in development mode');
    }

    return await this.makeRequest(productionServices.endpoints.bills.validate, {
      method: 'POST',
      body: {
        providerId,
        accountNumber
      }
    });
  }

  /**
   * Process bill payment
   */
  async processBillPayment(paymentData) {
    if (!isProductionMode()) {
      throw new Error('Bill payments not available in development mode');
    }

    return await this.makeRequest(productionServices.endpoints.bills.pay, {
      method: 'POST',
      body: paymentData
    });
  }

  /**
   * KYC and Compliance
   */
  async performAMLCheck(userData) {
    if (!isProductionMode()) {
      throw new Error('AML checks not available in development mode');
    }

    return await this.makeRequest(productionServices.endpoints.compliance.amlCheck, {
      method: 'POST',
      body: userData
    });
  }

  /**
   * Sanction screening
   */
  async performSanctionScreening(userData) {
    if (!isProductionMode()) {
      throw new Error('Sanction screening not available in development mode');
    }

    return await this.makeRequest(productionServices.endpoints.compliance.sanctionScreening, {
      method: 'POST',
      body: userData
    });
  }

  /**
   * Currency exchange rates
   */
  async getExchangeRates(baseCurrency = 'UGX') {
    if (!isProductionMode()) {
      // Return mock rates for development
      return {
        success: true,
        data: {
          base: 'UGX',
          rates: {
            KES: 0.32,
            TZS: 0.85,
            RWF: 0.37,
            BIF: 0.67,
            ETB: 0.015
          },
          timestamp: new Date().toISOString()
        }
      };
    }

    const credentials = productionServices.getCredentials('exchangeRate');
    const url = `${credentials.baseUrl}/latest/${baseCurrency}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${credentials.apiKey}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Exchange rate API error: ${response.status}`);
      }
      
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('❌ Exchange rate API error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Fraud detection
   */
  async checkTransactionForFraud(transactionData) {
    if (!isProductionMode()) {
      // Return safe result for development
      return {
        success: true,
        data: {
          riskScore: 0.1,
          riskLevel: 'LOW',
          approved: true,
          checks: ['amount_check', 'velocity_check', 'location_check']
        }
      };
    }

    const credentials = productionServices.getCredentials('fraudDetection');
    
    return await this.makeRequest('/fraud/check', {
      method: 'POST',
      body: transactionData,
      headers: {
        'X-Fraud-API-Key': credentials.apiKey
      }
    });
  }

  /**
   * Audit trail logging
   */
  async logAuditEvent(eventData) {
    if (!isProductionMode()) {
      console.log('🔧 Development audit log:', eventData);
      return { success: true };
    }

    return await this.makeRequest(productionServices.endpoints.compliance.auditTrail, {
      method: 'POST',
      body: {
        ...eventData,
        timestamp: new Date().toISOString(),
        environment: 'production'
      }
    });
  }
}

export default new ProductionApiService();
