-- JiraniPay Wallet Functions and Procedures
-- Run this after 001_create_core_tables.sql

-- Function to create a wallet for a user
CREATE OR REPLACE FUNCTION create_user_wallet_safe(
    p_user_id UUID,
    p_phone_number TEXT DEFAULT NULL
)
RET<PERSON>NS JSON AS $$
DECLARE
    v_wallet_id UUID;
    v_account_number TEXT;
    v_result JSON;
BEGIN
    -- Generate account number based on phone or user ID
    IF p_phone_number IS NOT NULL THEN
        v_account_number := 'JP' || REPLACE(p_phone_number, '+', '');
    ELSE
        v_account_number := 'JP' || SUBSTRING(p_user_id::TEXT, 1, 10);
    END IF;
    
    -- Check if wallet already exists
    SELECT id INTO v_wallet_id 
    FROM public.wallets 
    WHERE user_id = p_user_id;
    
    IF v_wallet_id IS NOT NULL THEN
        -- Return existing wallet
        SELECT json_build_object(
            'success', true,
            'data', json_build_object(
                'id', id,
                'user_id', user_id,
                'account_number', account_number,
                'balance', balance,
                'currency', currency,
                'created_at', created_at
            )
        ) INTO v_result
        FROM public.wallets 
        WHERE id = v_wallet_id;
        
        RETURN v_result;
    END IF;
    
    -- Create new wallet
    INSERT INTO public.wallets (
        user_id,
        account_number,
        account_type,
        balance,
        available_balance,
        currency,
        provider_name
    ) VALUES (
        p_user_id,
        v_account_number,
        'wallet',
        0.00,
        0.00,
        'UGX',
        'JiraniPay'
    ) RETURNING id INTO v_wallet_id;
    
    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'id', id,
            'user_id', user_id,
            'account_number', account_number,
            'balance', balance,
            'currency', currency,
            'created_at', created_at
        )
    ) INTO v_result
    FROM public.wallets 
    WHERE id = v_wallet_id;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get wallet balance
CREATE OR REPLACE FUNCTION get_wallet_balance(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    v_result JSON;
BEGIN
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'balance', COALESCE(balance, 0),
            'available_balance', COALESCE(available_balance, 0),
            'pending_balance', COALESCE(pending_balance, 0),
            'currency', COALESCE(currency, 'UGX'),
            'daily_limit', COALESCE(daily_limit, 500000),
            'monthly_limit', COALESCE(monthly_limit, 2000000),
            'spent_today', COALESCE(spent_today, 0),
            'spent_this_month', COALESCE(spent_this_month, 0)
        )
    ) INTO v_result
    FROM public.wallets 
    WHERE user_id = p_user_id AND is_active = true;
    
    IF v_result IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Wallet not found'
        );
    END IF;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a transaction
CREATE OR REPLACE FUNCTION create_transaction(
    p_user_id UUID,
    p_transaction_type TEXT,
    p_amount DECIMAL,
    p_description TEXT DEFAULT NULL,
    p_recipient_phone TEXT DEFAULT NULL,
    p_category TEXT DEFAULT 'general'
)
RETURNS JSON AS $$
DECLARE
    v_wallet_id UUID;
    v_transaction_id UUID;
    v_reference_number TEXT;
    v_result JSON;
BEGIN
    -- Get user's wallet
    SELECT id INTO v_wallet_id 
    FROM public.wallets 
    WHERE user_id = p_user_id AND is_active = true;
    
    IF v_wallet_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Wallet not found'
        );
    END IF;
    
    -- Generate reference number
    v_reference_number := 'TXN' || EXTRACT(EPOCH FROM NOW())::BIGINT || FLOOR(RANDOM() * 10000)::TEXT;
    
    -- Create transaction
    INSERT INTO public.transactions (
        user_id,
        wallet_id,
        transaction_type,
        amount,
        description,
        reference_number,
        recipient_phone,
        category,
        status
    ) VALUES (
        p_user_id,
        v_wallet_id,
        p_transaction_type,
        p_amount,
        p_description,
        v_reference_number,
        p_recipient_phone,
        p_category,
        'completed'
    ) RETURNING id INTO v_transaction_id;
    
    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'id', id,
            'reference_number', reference_number,
            'amount', amount,
            'transaction_type', transaction_type,
            'status', status,
            'created_at', created_at
        )
    ) INTO v_result
    FROM public.transactions 
    WHERE id = v_transaction_id;
    
    RETURN v_result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
