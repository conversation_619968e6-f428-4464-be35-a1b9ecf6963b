/**
 * Database Service for JiraniPay
 * 
 * This service handles all database operations using Supabase.
 * It provides a clean interface for user management, profiles, and transactions.
 */

import supabase from './supabaseClient';
import config from '../config/environment';

class DatabaseService {
  constructor() {
    this.tableName = {
      userProfiles: 'profiles', // FIXED: Use correct table name from schema-essential.sql
      userPreferences: 'user_preferences',
      paymentAccounts: 'payment_accounts',
      transactions: 'transactions',
    };
  }

  /**
   * Create user profile after successful authentication
   * @param {Object} profileData - User profile information
   * @returns {Promise<Object>} - Result object with success status
   */
  async createUserProfile(profileData) {
    try {
      console.log('📝 DatabaseService: Creating profile with data:', profileData);
      console.log('📝 Using table name:', this.tableName.userProfiles);

      // Use schema-essential.sql structure
      const profileRecord = {
        id: profileData.userId, // Primary key that references auth.users(id)
        full_name: profileData.fullName,
        phone: profileData.phoneNumber,
        phone_number: profileData.phoneNumber, // ✅ FIX: Also set phone_number for compatibility
        email: profileData.email || null,
        country_code: profileData.countryCode || 'UG',
        language_preference: profileData.preferredLanguage || 'en',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 Profile record to insert:', profileRecord);

      const { data, error } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .insert([profileRecord])
        .select()
        .single();

      if (error) {
        // Handle duplicate key constraint violation (race condition)
        if (error.code === '23505') {
          console.log('⚠️ Profile already exists (race condition), fetching existing profile...');

          // Fetch the existing profile
          const { data: existingProfile, error: fetchError } = await supabase
            .from(this.tableName.userProfiles)
            .select('*')
            .eq('id', profileData.userId)
            .single();

          if (existingProfile && !fetchError) {
            console.log('✅ Retrieved existing profile after race condition');
            return { success: true, data: existingProfile };
          } else {
            console.error('❌ Could not fetch existing profile after race condition:', fetchError);
            throw error;
          }
        } else {
          throw error;
        }
      }

      // Also create user preferences with defaults
      await this.createUserPreferences(profileData.userId);

      // Initialize baseline completion steps (phone verification)
      // Import here to avoid circular dependency
      const profileManagementService = (await import('./profileManagementService')).default;
      await profileManagementService.initializeBaselineSteps(profileData.userId);

      console.log('✅ User profile created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create user preferences with default values
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with success status
   */
  async createUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .insert([
          {
            id: userId,
            biometric_enabled: false,
            notifications_enabled: true,
            dark_mode_enabled: false,
            preferred_currency: config.regional.defaultCurrency,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user profile by user ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with user profile data
   */
  async getUserProfile(userId) {
    try {
      // Use schema-essential.sql structure
      const { data, error } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .select('*')
        .eq('id', userId) // id field directly references auth.users(id)
        .single();

      // Handle the specific "no rows returned" error gracefully
      if (error && error.code === 'PGRST116') {
        console.log('⚠️ User profile not found, this is normal for new users');
        return { success: false, error: 'Profile not found', code: 'PROFILE_NOT_FOUND' };
      }

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user profile:', error);

      // Handle profile not found error gracefully
      if (error.code === 'PGRST116') {
        console.log('ℹ️ Profile not found, this is expected for new users');
        return {
          success: false,
          error: 'Profile not found - this is normal for new users',
          code: 'PROFILE_NOT_FOUND'
        };
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * Update user profile (PRODUCTION FIX)
   * @param {string} userId - User ID
   * @param {Object} updates - Profile updates
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateUserProfile(userId, updates) {
    try {
      console.log('🔄 Updating user profile for:', userId);
      console.log('📝 Updates:', updates);

      // PRODUCTION FIX: Use UPSERT with schema-essential.sql structure
      const { data, error } = await supabase
        .from(this.tableName.userProfiles) // 'profiles' table
        .upsert({
          id: userId, // Primary key that references auth.users(id)
          ...updates,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error:', error);
        throw error;
      }

      console.log('✅ User profile upserted successfully:', data);
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user preferences
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with user preferences
   */
  async getUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .select('*')
        .eq('id', userId)
        .single();

      // Handle the specific "no rows returned" error gracefully
      if (error && error.code === 'PGRST116') {
        console.log('⚠️ User preferences not found, creating default preferences');

        // Create default preferences for new users
        const defaultPreferences = {
          id: userId,
          biometric_enabled: false,
          notifications_enabled: true,
          sms_notifications: true,
          email_notifications: true,
          dark_mode_enabled: false,
          preferred_currency: 'UGX',
          transaction_limit_daily: 1000000,
          transaction_limit_monthly: 10000000,
          security_pin_enabled: false,
          auto_logout_minutes: 30,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        // Return default preferences for new users

        // Try to create preferences in database
        try {
          const { data: newData, error: createError } = await supabase
            .from(this.tableName.userPreferences)
            .insert([defaultPreferences])
            .select()
            .single();

          if (createError) {
            console.log('⚠️ Could not create preferences in database, using defaults');
            return { success: true, data: defaultPreferences };
          }

          return { success: true, data: newData };
        } catch (createErr) {
          console.log('⚠️ Using default preferences due to database error');
          return { success: true, data: defaultPreferences };
        }
      }

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user preferences:', error);

      // Provide default preferences as fallback
      console.log('⚠️ Using default preferences due to database error');
      const defaultPreferences = {
        id: userId,
        biometric_enabled: false,
        notifications_enabled: true,
        sms_notifications: true,
        email_notifications: true,
        dark_mode_enabled: false,
        preferred_currency: 'UGX',
        transaction_limit_daily: 1000000,
        transaction_limit_monthly: 10000000,
        security_pin_enabled: false,
        auto_logout_minutes: 30,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      return { success: true, data: defaultPreferences };

      return { success: false, error: error.message };
    }
  }

  /**
   * Update user preferences
   * @param {string} userId - User ID
   * @param {Object} preferences - Preference updates
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateUserPreferences(userId, preferences) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .update({
          ...preferences,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ User preferences updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add payment account for user
   * @param {string} userId - User ID
   * @param {Object} accountData - Payment account information
   * @returns {Promise<Object>} - Result object with success status
   */
  async addPaymentAccount(userId, accountData) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.paymentAccounts)
        .insert([
          {
            user_id: userId,
            account_type: accountData.accountType,
            provider_name: accountData.providerName,
            account_number: accountData.accountNumber,
            account_name: accountData.accountName,
            is_primary: accountData.isPrimary || false,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Payment account added successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error adding payment account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's payment accounts
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with payment accounts
   */
  async getUserPaymentAccounts(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.paymentAccounts)
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_primary', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting payment accounts:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create transaction record
   * @param {Object} transactionData - Transaction information
   * @returns {Promise<Object>} - Result object with success status
   */
  async createTransaction(transactionData) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.transactions)
        .insert([
          {
            user_id: transactionData.userId,
            transaction_type: transactionData.transactionType,
            amount: transactionData.amount,
            currency: transactionData.currency || config.regional.defaultCurrency,
            status: transactionData.status || 'pending',
            reference_number: transactionData.referenceNumber,
            description: transactionData.description,
            metadata: transactionData.metadata || {},
          }
        ])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Transaction created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's transaction history
   * @param {string} userId - User ID
   * @param {Object} options - Query options (limit, offset, status filter)
   * @returns {Promise<Object>} - Result object with transactions
   */
  async getUserTransactions(userId, options = {}) {
    try {
      let query = supabase
        .from(this.tableName.transactions)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update transaction status
   * @param {string} transactionId - Transaction ID
   * @param {string} status - New status
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateTransactionStatus(transactionId, status, metadata = {}) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.transactions)
        .update({
          status,
          metadata,
          updated_at: new Date().toISOString(),
        })
        .eq('id', transactionId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Transaction status updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating transaction status:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user profile exists
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Whether profile exists
   */
  async userProfileExists(userId) {
    try {
      // Try both schema structures
      let data, error;

      // Try new schema first (user_id field)
      const result1 = await supabase
        .from(this.tableName.userProfiles)
        .select('id')
        .eq('user_id', userId)
        .single();

      if (result1.error && result1.error.code === 'PGRST116') {
        // Try old schema (id field)
        const result2 = await supabase
          .from(this.tableName.userProfiles)
          .select('id')
          .eq('id', userId)
          .single();

        data = result2.data;
        error = result2.error;
      } else {
        data = result1.data;
        error = result1.error;
      }

      return !error && data;
    } catch (error) {
      return false;
    }
  }
}

// Create and export a singleton instance
const databaseService = new DatabaseService();
export default databaseService;
