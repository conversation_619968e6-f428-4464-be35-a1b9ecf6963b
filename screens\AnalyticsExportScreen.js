/**
 * Analytics Export Screen
 * Handles export of analytics reports in various formats
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

// Services
import analyticsExportService from '../services/analyticsExportService';

// Hooks and Contexts
import { useTheme } from '../contexts/ThemeContext';
import authService from '../services/authService';

// Constants
import { Colors } from '../constants/Colors';

const AnalyticsExportScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const [user, setUser] = useState(null);
  
  const { chartType, data, period } = route.params || {};
  
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [exporting, setExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState({
    includeCharts: true,
    includeInsights: true,
    includeRawData: false
  });

  const styles = createStyles(theme);

  useEffect(() => {
    navigation.setOptions({
      title: 'Export Analytics',
      headerStyle: {
        backgroundColor: theme.colors.surface,
      },
      headerTintColor: theme.colors.text,
    });

    // Get current user
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, [navigation, theme]);

  /**
   * Export formats configuration
   */
  const exportFormats = [
    {
      key: 'pdf',
      title: 'PDF Report',
      description: 'Comprehensive report with charts and insights',
      icon: 'document-text',
      color: Colors.secondary.heritage,
      recommended: true
    },
    {
      key: 'excel',
      title: 'Excel/CSV',
      description: 'Raw data for further analysis',
      icon: 'grid',
      color: Colors.secondary.savanna,
      recommended: false
    }
  ];

  /**
   * Chart type titles
   */
  const chartTitles = {
    all: 'Complete Analytics Report',
    spending_trend: 'Spending Trends Analysis',
    category_spending: 'Category Spending Report',
    monthly_comparison: 'Monthly Comparison Report',
    savings_progress: 'Savings Progress Report',
    investment_performance: 'Investment Performance Report'
  };

  /**
   * Handle format selection
   */
  const handleFormatSelect = (format) => {
    setSelectedFormat(format);
    Haptics.selectionAsync();
  };

  /**
   * Handle option toggle
   */
  const handleOptionToggle = (option) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
    Haptics.selectionAsync();
  };

  /**
   * Handle export
   */
  const handleExport = async () => {
    try {
      setExporting(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const exportTitle = chartTitles[chartType] || 'Analytics Report';
      const userInfo = {
        name: user?.full_name || user?.email || 'User',
        phone: user?.phone_number
      };

      let exportResult;

      if (chartType === 'all') {
        // Export complete analytics report
        if (selectedFormat === 'pdf') {
          exportResult = await analyticsExportService.exportToPDF(data, {
            title: exportTitle,
            period,
            includeCharts: exportOptions.includeCharts,
            userInfo
          });
        } else {
          exportResult = await analyticsExportService.exportToExcel(data, {
            title: exportTitle,
            period,
            format: 'csv'
          });
        }
      } else {
        // Export specific chart data
        const chartData = getChartData(chartType);
        exportResult = await analyticsExportService.exportChartData(chartType, chartData, {
          format: selectedFormat === 'pdf' ? 'pdf' : 'csv',
          title: exportTitle,
          period
        });
      }

      if (exportResult.success) {
        // Share the exported file
        const shareResult = await analyticsExportService.shareReport(exportResult, {
          dialogTitle: `Share ${exportTitle}`
        });

        if (shareResult.success) {
          Alert.alert(
            'Export Successful',
            `${exportTitle} has been exported successfully!`,
            [
              { text: 'OK', onPress: () => navigation.goBack() }
            ]
          );
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }
      } else {
        throw new Error(exportResult.error || 'Export failed');
      }
    } catch (error) {
      console.error('❌ Export error:', error);
      Alert.alert(
        'Export Failed',
        error.message || 'Failed to export analytics. Please try again.',
        [{ text: 'OK' }]
      );
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setExporting(false);
    }
  };

  /**
   * Get chart-specific data
   */
  const getChartData = (type) => {
    if (!data) return [];

    switch (type) {
      case 'spending_trend':
      case 'monthly_comparison':
        return data.monthlyTrends || [];
      case 'category_spending':
        return data.spendingCategories || [];
      case 'savings_progress':
        return data.savings?.goalProgress?.goals || [];
      case 'investment_performance':
        return data.investments?.performanceMetrics || [];
      default:
        return [];
    }
  };

  /**
   * Render format selector
   */
  const renderFormatSelector = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Export Format
      </Text>
      {exportFormats.map((format) => (
        <TouchableOpacity
          key={format.key}
          style={[
            styles.formatCard,
            selectedFormat === format.key && styles.formatCardSelected,
            { backgroundColor: theme.colors.surface }
          ]}
          onPress={() => handleFormatSelect(format.key)}
        >
          <View style={styles.formatCardContent}>
            <View style={[styles.formatIcon, { backgroundColor: format.color + '20' }]}>
              <Ionicons name={format.icon} size={24} color={format.color} />
            </View>
            <View style={styles.formatInfo}>
              <View style={styles.formatHeader}>
                <Text style={[styles.formatTitle, { color: theme.colors.text }]}>
                  {format.title}
                </Text>
                {format.recommended && (
                  <View style={styles.recommendedBadge}>
                    <Text style={styles.recommendedText}>Recommended</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.formatDescription, { color: theme.colors.textSecondary }]}>
                {format.description}
              </Text>
            </View>
            <View style={styles.formatSelector}>
              <View style={[
                styles.radioButton,
                selectedFormat === format.key && styles.radioButtonSelected
              ]}>
                {selectedFormat === format.key && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  /**
   * Render export options
   */
  const renderExportOptions = () => {
    if (selectedFormat !== 'pdf' || chartType !== 'all') return null;

    const options = [
      {
        key: 'includeCharts',
        title: 'Include Charts',
        description: 'Add visual charts to the report',
        icon: 'bar-chart'
      },
      {
        key: 'includeInsights',
        title: 'Include Insights',
        description: 'Add AI-generated financial insights',
        icon: 'bulb'
      },
      {
        key: 'includeRawData',
        title: 'Include Raw Data',
        description: 'Add detailed transaction data',
        icon: 'list'
      }
    ];

    return (
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Export Options
        </Text>
        {options.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[styles.optionCard, { backgroundColor: theme.colors.surface }]}
            onPress={() => handleOptionToggle(option.key)}
          >
            <View style={styles.optionContent}>
              <Ionicons
                name={option.icon}
                size={20}
                color={theme.colors.primary}
                style={styles.optionIcon}
              />
              <View style={styles.optionInfo}>
                <Text style={[styles.optionTitle, { color: theme.colors.text }]}>
                  {option.title}
                </Text>
                <Text style={[styles.optionDescription, { color: theme.colors.textSecondary }]}>
                  {option.description}
                </Text>
              </View>
              <View style={styles.optionToggle}>
                <View style={[
                  styles.toggleSwitch,
                  exportOptions[option.key] && styles.toggleSwitchActive
                ]}>
                  <View style={[
                    styles.toggleThumb,
                    exportOptions[option.key] && styles.toggleThumbActive
                  ]} />
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  /**
   * Render export preview
   */
  const renderExportPreview = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
        Export Preview
      </Text>
      <View style={[styles.previewCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.previewHeader}>
          <Ionicons
            name="document-text"
            size={24}
            color={theme.colors.primary}
          />
          <Text style={[styles.previewTitle, { color: theme.colors.text }]}>
            {chartTitles[chartType] || 'Analytics Report'}
          </Text>
        </View>
        <View style={styles.previewDetails}>
          <Text style={[styles.previewDetail, { color: theme.colors.textSecondary }]}>
            Period: {period?.charAt(0).toUpperCase() + period?.slice(1)}
          </Text>
          <Text style={[styles.previewDetail, { color: theme.colors.textSecondary }]}>
            Format: {selectedFormat.toUpperCase()}
          </Text>
          <Text style={[styles.previewDetail, { color: theme.colors.textSecondary }]}>
            Generated: {new Date().toLocaleDateString()}
          </Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderFormatSelector()}
        {renderExportOptions()}
        {renderExportPreview()}
      </ScrollView>

      {/* Export Button */}
      <View style={[styles.footer, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.exportButton,
            { backgroundColor: theme.colors.primary },
            exporting && styles.exportButtonDisabled
          ]}
          onPress={handleExport}
          disabled={exporting}
        >
          {exporting ? (
            <ActivityIndicator size="small" color={Colors.neutral.white} />
          ) : (
            <Ionicons name="download" size={20} color={Colors.neutral.white} />
          )}
          <Text style={styles.exportButtonText}>
            {exporting ? 'Exporting...' : 'Export Report'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

/**
 * Create styles based on theme
 */
const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  formatCard: {
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  formatCardSelected: {
    borderColor: theme.colors.primary,
  },
  formatCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  formatIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  formatInfo: {
    flex: 1,
  },
  formatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  formatTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  recommendedBadge: {
    backgroundColor: Colors.secondary.savanna,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  formatDescription: {
    fontSize: 14,
  },
  formatSelector: {
    marginLeft: 12,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: theme.colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: theme.colors.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: theme.colors.primary,
  },
  optionCard: {
    borderRadius: 12,
    marginBottom: 8,
    elevation: 1,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  optionIcon: {
    marginRight: 12,
  },
  optionInfo: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  optionDescription: {
    fontSize: 14,
  },
  optionToggle: {
    marginLeft: 12,
  },
  toggleSwitch: {
    width: 44,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleSwitchActive: {
    backgroundColor: theme.colors.primary,
  },
  toggleThumb: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.neutral.white,
    elevation: 2,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  previewCard: {
    borderRadius: 12,
    padding: 16,
    elevation: 1,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
  previewDetails: {
    paddingLeft: 36,
  },
  previewDetail: {
    fontSize: 14,
    marginBottom: 4,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  exportButtonDisabled: {
    opacity: 0.6,
  },
  exportButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
});

export default AnalyticsExportScreen;
