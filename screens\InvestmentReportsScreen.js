/**
 * Investment Reports Screen
 * Screen for generating and viewing investment reports
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import investmentPortfolioService from '../services/investmentPortfolioService';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const InvestmentReportsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [reports, setReports] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  const periods = [
    { key: 'week', label: 'This Week' },
    { key: 'month', label: 'This Month' },
    { key: 'quarter', label: 'This Quarter' },
    { key: 'year', label: 'This Year' },
    { key: 'custom', label: 'Custom Range' }
  ];

  const reportTypes = [
    {
      id: 'performance',
      title: 'Performance Report',
      description: 'Detailed portfolio performance analysis',
      icon: 'trending-up',
      color: '#4ECDC4'
    },
    {
      id: 'holdings',
      title: 'Holdings Report',
      description: 'Current portfolio holdings and allocations',
      icon: 'briefcase',
      color: '#45B7D1'
    },
    {
      id: 'transactions',
      title: 'Transaction Report',
      description: 'Complete transaction history and analysis',
      icon: 'receipt',
      color: '#96CEB4'
    },
    {
      id: 'tax',
      title: 'Tax Report',
      description: 'Tax-related information and realized gains/losses',
      icon: 'document-text',
      color: '#FECA57'
    },
    {
      id: 'dividend',
      title: 'Dividend Report',
      description: 'Dividend income and yield analysis',
      icon: 'cash',
      color: '#FF6B35'
    },
    {
      id: 'risk',
      title: 'Risk Analysis',
      description: 'Portfolio risk metrics and analysis',
      icon: 'shield-checkmark',
      color: '#6C5CE7'
    }
  ];

  useEffect(() => {
    loadReports();
  }, [selectedPeriod]);

  const loadReports = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Load real investment reports data
      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view reports');
        navigation.goBack();
        return;
      }
      
      setReports({
        summary: {
          totalValue: 25000,
          totalReturn: 3250.75,
          totalReturnPercent: 14.9,
          totalDividends: 425.50,
          totalFees: 45.99,
          numberOfTrades: 18,
          bestPerformer: { symbol: 'STANBIC', return: 23.5 },
          worstPerformer: { symbol: 'DFCU', return: -8.2 }
        },
        recentReports: [
          {
            id: '1',
            type: 'performance',
            title: 'Monthly Performance Report',
            period: 'November 2024',
            generatedAt: new Date().toISOString(),
            status: 'ready'
          },
          {
            id: '2',
            type: 'tax',
            title: 'Q3 2024 Tax Report',
            period: 'Q3 2024',
            generatedAt: new Date(Date.now() - 86400000).toISOString(),
            status: 'ready'
          },
          {
            id: '3',
            type: 'dividend',
            title: 'Dividend Income Report',
            period: 'October 2024',
            generatedAt: new Date(Date.now() - *********).toISOString(),
            status: 'ready'
          }
        ]
      });

    } catch (error) {
      console.error('❌ Error loading reports:', error);
      Alert.alert('Error', 'Failed to load reports');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadReports(true);
  };

  const generateInvestmentReport = async (reportType) => {
    try {
      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to generate reports');
        return;
      }

      // Get user's investment data
      const [portfoliosResult, transactionsResult] = await Promise.all([
        investmentPortfolioService.getUserPortfolios(userId),
        investmentPortfolioService.getUserTransactions(userId, { limit: 100 })
      ]);

      if (!portfoliosResult.success || !transactionsResult.success) {
        Alert.alert('Error', 'Failed to load investment data for report generation');
        return;
      }

      const portfolios = portfoliosResult.portfolios || [];
      const transactions = transactionsResult.transactions || [];

      // Generate report content
      const reportContent = generateReportHTML(reportType, portfolios, transactions);
      const fileName = `${reportType.key}_${new Date().toISOString().split('T')[0]}.pdf`;

      // Generate PDF
      const { uri } = await Print.printToFileAsync({
        html: reportContent,
        base64: false
      });

      // Share the report
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Investment Report'
        });

        Alert.alert(
          'Report Generated',
          `${reportType.title} has been generated and shared successfully!`
        );
      } else {
        Alert.alert(
          'Report Generated',
          `${reportType.title} has been generated successfully!`
        );
      }

    } catch (error) {
      console.error('❌ Error generating report:', error);
      Alert.alert('Error', `Failed to generate ${reportType.title}`);
    } finally {
      setLoading(false);
    }
  };

  const generateReportHTML = (reportType, portfolios, transactions) => {
    const currentDate = new Date().toLocaleDateString();
    const periodLabel = periods.find(p => p.key === selectedPeriod)?.label || 'All Time';

    let content = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { color: #2E7D32; font-size: 24px; font-weight: bold; }
            .subtitle { color: #666; font-size: 14px; margin-top: 5px; }
            .section { margin: 20px 0; }
            .section-title { color: #2E7D32; font-size: 18px; font-weight: bold; margin-bottom: 10px; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .amount { text-align: right; }
            .positive { color: #2E7D32; }
            .negative { color: #D32F2F; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">JiraniPay Investment Report</div>
            <div class="subtitle">${reportType.title} • ${periodLabel}</div>
            <div class="subtitle">Generated: ${currentDate}</div>
          </div>
    `;

    if (reportType.key === 'portfolio_performance') {
      content += generatePortfolioSection(portfolios);
    } else if (reportType.key === 'transaction_history') {
      content += generateTransactionSection(transactions);
    } else if (reportType.key === 'dividend_report') {
      const dividendTransactions = transactions.filter(t => t.type === 'dividend');
      content += generateDividendSection(dividendTransactions);
    }

    content += `
        </body>
      </html>
    `;

    return content;
  };

  const generatePortfolioSection = (portfolios) => {
    if (!portfolios || portfolios.length === 0) {
      return '<div class="section"><p>No portfolios found.</p></div>';
    }

    let section = '<div class="section"><div class="section-title">Portfolio Performance</div>';
    section += '<table><tr><th>Portfolio</th><th>Current Value</th><th>Total Return</th><th>Return %</th></tr>';

    portfolios.forEach(portfolio => {
      const returnAmount = (portfolio.currentValue || 0) - (portfolio.totalInvested || 0);
      const returnPercent = portfolio.totalInvested > 0 ? (returnAmount / portfolio.totalInvested) * 100 : 0;

      section += `
        <tr>
          <td>${portfolio.name}</td>
          <td class="amount">${formatCurrency(portfolio.currentValue || 0, 'UGX')}</td>
          <td class="amount ${returnAmount >= 0 ? 'positive' : 'negative'}">
            ${formatCurrency(returnAmount, 'UGX')}
          </td>
          <td class="amount ${returnPercent >= 0 ? 'positive' : 'negative'}">
            ${returnPercent.toFixed(2)}%
          </td>
        </tr>
      `;
    });

    section += '</table></div>';
    return section;
  };

  const generateTransactionSection = (transactions) => {
    if (!transactions || transactions.length === 0) {
      return '<div class="section"><p>No transactions found.</p></div>';
    }

    let section = '<div class="section"><div class="section-title">Transaction History</div>';
    section += '<table><tr><th>Date</th><th>Type</th><th>Asset</th><th>Quantity</th><th>Price</th><th>Total</th></tr>';

    transactions.slice(0, 20).forEach(transaction => {
      section += `
        <tr>
          <td>${formatDate(transaction.createdAt)}</td>
          <td>${transaction.type.toUpperCase()}</td>
          <td>${transaction.asset?.symbol || 'N/A'}</td>
          <td class="amount">${transaction.quantity || 0}</td>
          <td class="amount">${formatCurrency(transaction.price || 0, transaction.currency || 'UGX')}</td>
          <td class="amount">${formatCurrency(transaction.totalAmount || 0, transaction.currency || 'UGX')}</td>
        </tr>
      `;
    });

    section += '</table></div>';
    return section;
  };

  const generateDividendSection = (dividendTransactions) => {
    if (!dividendTransactions || dividendTransactions.length === 0) {
      return '<div class="section"><p>No dividend payments found.</p></div>';
    }

    let section = '<div class="section"><div class="section-title">Dividend Payments</div>';
    section += '<table><tr><th>Date</th><th>Asset</th><th>Amount</th><th>Status</th></tr>';

    dividendTransactions.forEach(transaction => {
      section += `
        <tr>
          <td>${formatDate(transaction.createdAt)}</td>
          <td>${transaction.asset?.symbol || 'N/A'}</td>
          <td class="amount positive">${formatCurrency(transaction.totalAmount || 0, transaction.currency || 'UGX')}</td>
          <td>${transaction.status || 'Completed'}</td>
        </tr>
      `;
    });

    section += '</table></div>';
    return section;
  };

  const handleGenerateReport = (reportType) => {
    Alert.alert(
      'Generate Report',
      `Generate ${reportType.title} for ${periods.find(p => p.key === selectedPeriod)?.label}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Generate',
          onPress: () => generateInvestmentReport(reportType)
        }
      ]
    );
  };

  const handleViewReport = (report) => {
    Alert.alert('View Report', `Opening ${report.title}...`);
  };

  const handleExportAllReports = async () => {
    try {
      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to export reports');
        return;
      }

      // Generate comprehensive report with all data
      const [portfoliosResult, transactionsResult] = await Promise.all([
        investmentPortfolioService.getUserPortfolios(userId),
        investmentPortfolioService.getUserTransactions(userId, { limit: 200 })
      ]);

      if (!portfoliosResult.success || !transactionsResult.success) {
        Alert.alert('Error', 'Failed to load data for comprehensive report');
        return;
      }

      const portfolios = portfoliosResult.portfolios || [];
      const transactions = transactionsResult.transactions || [];

      // Generate comprehensive HTML report
      const htmlContent = generateComprehensiveReport(portfolios, transactions);
      const fileName = `comprehensive_investment_report_${new Date().toISOString().split('T')[0]}.pdf`;

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false
      });

      // Share the comprehensive report
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Comprehensive Investment Report'
        });

        Alert.alert('Export Successful', 'Comprehensive investment report exported successfully!');
      } else {
        Alert.alert('Export Successful', 'Comprehensive report has been generated successfully!');
      }

    } catch (error) {
      console.error('❌ Error exporting all reports:', error);
      Alert.alert('Export Failed', 'Failed to export comprehensive report');
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleReports = () => {
    Alert.alert(
      'Schedule Reports',
      'Choose report frequency:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Weekly', onPress: () => scheduleReport('weekly') },
        { text: 'Monthly', onPress: () => scheduleReport('monthly') },
        { text: 'Quarterly', onPress: () => scheduleReport('quarterly') }
      ]
    );
  };

  const scheduleReport = async (frequency) => {
    try {
      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to schedule reports');
        return;
      }

      // Save schedule preference
      await AsyncStorage.setItem(`investment_report_schedule_${userId}`, frequency);

      Alert.alert(
        'Report Scheduled',
        `${frequency.charAt(0).toUpperCase() + frequency.slice(1)} investment reports have been scheduled. You will receive notifications when new reports are ready.`
      );
    } catch (error) {
      console.error('❌ Error scheduling reports:', error);
      Alert.alert('Error', 'Failed to schedule reports');
    }
  };

  const handleEmailReports = () => {
    Alert.alert(
      'Email Reports',
      'Choose reports to email:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Portfolio Performance', onPress: () => emailReport('portfolio_performance') },
        { text: 'Transaction History', onPress: () => emailReport('transaction_history') },
        { text: 'All Reports', onPress: () => emailReport('all') }
      ]
    );
  };

  const emailReport = async (reportType) => {
    try {
      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to email reports');
        return;
      }

      // Generate the requested report
      let reportContent = '';
      let subject = '';

      if (reportType === 'all') {
        const [portfoliosResult, transactionsResult] = await Promise.all([
          investmentPortfolioService.getUserPortfolios(userId),
          investmentPortfolioService.getUserTransactions(userId, { limit: 100 })
        ]);

        if (portfoliosResult.success && transactionsResult.success) {
          reportContent = generateComprehensiveReport(portfoliosResult.portfolios, transactionsResult.transactions);
          subject = 'JiraniPay - Comprehensive Investment Report';
        }
      } else {
        reportContent = generateReportHTML({ key: reportType, title: reportType.replace('_', ' ') }, [], []);
        subject = `JiraniPay - ${reportType.replace('_', ' ')} Report`;
      }

      // Create PDF
      const { uri } = await Print.printToFileAsync({
        html: reportContent,
        base64: false
      });

      // Open email with attachment
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Email Investment Report'
        });

        Alert.alert('Email Ready', 'Report prepared for email. Choose your email app to send.');
      } else {
        Alert.alert('Email Not Available', 'Email sharing is not available on this device');
      }

    } catch (error) {
      console.error('❌ Error emailing reports:', error);
      Alert.alert('Email Failed', 'Failed to prepare report for email');
    } finally {
      setLoading(false);
    }
  };

  const handlePrintReports = () => {
    Alert.alert(
      'Print Reports',
      'Choose report to print:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Portfolio Summary', onPress: () => printReport('portfolio_performance') },
        { text: 'Transaction History', onPress: () => printReport('transaction_history') },
        { text: 'Comprehensive Report', onPress: () => printReport('comprehensive') }
      ]
    );
  };

  const printReport = async (reportType) => {
    try {
      setLoading(true);

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to print reports');
        return;
      }

      let reportContent = '';

      if (reportType === 'comprehensive') {
        const [portfoliosResult, transactionsResult] = await Promise.all([
          investmentPortfolioService.getUserPortfolios(userId),
          investmentPortfolioService.getUserTransactions(userId, { limit: 100 })
        ]);

        if (portfoliosResult.success && transactionsResult.success) {
          reportContent = generateComprehensiveReport(portfoliosResult.portfolios, transactionsResult.transactions);
        }
      } else {
        reportContent = generateReportHTML({ key: reportType, title: reportType.replace('_', ' ') }, [], []);
      }

      // Print the report
      await Print.printAsync({
        html: reportContent
      });

      Alert.alert('Print Successful', 'Report sent to printer successfully!');

    } catch (error) {
      console.error('❌ Error printing reports:', error);
      Alert.alert('Print Failed', 'Failed to print report');
    } finally {
      setLoading(false);
    }
  };

  const generateComprehensiveReport = (portfolios, transactions) => {
    const currentDate = new Date().toLocaleDateString();

    return `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { color: #2E7D32; font-size: 28px; font-weight: bold; }
            .subtitle { color: #666; font-size: 16px; margin-top: 5px; }
            .section { margin: 30px 0; page-break-inside: avoid; }
            .section-title { color: #2E7D32; font-size: 20px; font-weight: bold; margin-bottom: 15px; border-bottom: 2px solid #2E7D32; padding-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin: 15px 0; }
            th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            .amount { text-align: right; }
            .positive { color: #2E7D32; }
            .negative { color: #D32F2F; }
            .summary-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin: 20px 0; }
            .summary-item { text-align: center; padding: 15px; background-color: #f9f9f9; border-radius: 8px; }
            .summary-value { font-size: 24px; font-weight: bold; color: #2E7D32; }
            .summary-label { font-size: 14px; color: #666; margin-top: 5px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">JiraniPay Comprehensive Investment Report</div>
            <div class="subtitle">East African Investment Portfolio Analysis</div>
            <div class="subtitle">Generated: ${currentDate}</div>
          </div>

          <div class="section">
            <div class="section-title">Executive Summary</div>
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-value">${portfolios.length}</div>
                <div class="summary-label">Active Portfolios</div>
              </div>
              <div class="summary-item">
                <div class="summary-value">${formatCurrency(portfolios.reduce((sum, p) => sum + (p.currentValue || 0), 0), 'UGX')}</div>
                <div class="summary-label">Total Portfolio Value</div>
              </div>
              <div class="summary-item">
                <div class="summary-value">${transactions.length}</div>
                <div class="summary-label">Total Transactions</div>
              </div>
            </div>
          </div>

          ${generatePortfolioSection(portfolios)}
          ${generateTransactionSection(transactions)}

          <div class="section">
            <div class="section-title">East African Market Focus</div>
            <p>This report focuses on East African markets including Uganda Securities Exchange (USE), Nairobi Securities Exchange (NSE), Dar es Salaam Stock Exchange (DSE), and Rwanda Stock Exchange (RSE).</p>
            <p>All amounts are displayed in East African currencies with UGX as the primary currency.</p>
          </div>
        </body>
      </html>
    `;
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {periods.map((period) => (
          <TouchableOpacity
            key={period.key}
            style={[
              styles.periodButton,
              selectedPeriod === period.key && styles.periodButtonActive
            ]}
            onPress={() => setSelectedPeriod(period.key)}
          >
            <Text style={[
              styles.periodButtonText,
              selectedPeriod === period.key && styles.periodButtonTextActive
            ]}>
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderSummaryCard = () => (
    <View style={styles.summaryCard}>
      <Text style={styles.summaryTitle}>Portfolio Summary</Text>
      
      <View style={styles.summaryGrid}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{formatCurrency(reports.summary.totalValue, 'UGX')}</Text>
          <Text style={styles.summaryLabel}>Total Value</Text>
        </View>
        
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryValue, { color: theme.colors.success }]}>
            +{formatCurrency(reports.summary.totalReturn, 'UGX')}
          </Text>
          <Text style={styles.summaryLabel}>Total Return</Text>
        </View>
        
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryValue, { color: theme.colors.success }]}>
            +{reports.summary.totalReturnPercent.toFixed(1)}%
          </Text>
          <Text style={styles.summaryLabel}>Return %</Text>
        </View>
        
        <View style={styles.summaryItem}>
          <Text style={styles.summaryValue}>{formatCurrency(reports.summary.totalDividends, 'UGX')}</Text>
          <Text style={styles.summaryLabel}>Dividends</Text>
        </View>
      </View>
      
      <View style={styles.performanceRow}>
        <View style={styles.performanceItem}>
          <Text style={styles.performanceLabel}>Best Performer</Text>
          <Text style={[styles.performanceValue, { color: theme.colors.success }]}>
            {reports.summary.bestPerformer.symbol} +{reports.summary.bestPerformer.return}%
          </Text>
        </View>
        
        <View style={styles.performanceItem}>
          <Text style={styles.performanceLabel}>Worst Performer</Text>
          <Text style={[styles.performanceValue, { color: theme.colors.error }]}>
            {reports.summary.worstPerformer.symbol} {reports.summary.worstPerformer.return}%
          </Text>
        </View>
      </View>
    </View>
  );

  const renderReportTypeCard = (reportType) => (
    <TouchableOpacity 
      key={reportType.id}
      style={styles.reportTypeCard}
      onPress={() => handleGenerateReport(reportType)}
    >
      <View style={[styles.reportTypeIcon, { backgroundColor: reportType.color }]}>
        <Ionicons name={reportType.icon} size={24} color={theme.colors.white} />
      </View>
      <View style={styles.reportTypeContent}>
        <Text style={styles.reportTypeTitle}>{reportType.title}</Text>
        <Text style={styles.reportTypeDescription}>{reportType.description}</Text>
      </View>
      <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  const renderRecentReports = () => (
    <View style={styles.recentReportsCard}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Recent Reports</Text>
        <TouchableOpacity onPress={() => Alert.alert('Coming Soon', 'View all reports will be available soon!')}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      
      {reports.recentReports.map((report) => (
        <TouchableOpacity 
          key={report.id}
          style={styles.recentReportItem}
          onPress={() => handleViewReport(report)}
        >
          <View style={styles.recentReportInfo}>
            <Text style={styles.recentReportTitle}>{report.title}</Text>
            <Text style={styles.recentReportPeriod}>{report.period}</Text>
            <Text style={styles.recentReportDate}>Generated {formatDate(report.generatedAt)}</Text>
          </View>
          <View style={styles.recentReportActions}>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>{report.status}</Text>
            </View>
            <Ionicons name="download-outline" size={16} color={theme.colors.primary} />
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleExportAllReports}
        >
          <Ionicons name="download" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Export All</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleScheduleReports}
        >
          <Ionicons name="calendar" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Schedule</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handleEmailReports}
        >
          <Ionicons name="mail" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Email</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={handlePrintReports}
        >
          <Ionicons name="print" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Print</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading reports...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Investment Reports</Text>
        <TouchableOpacity onPress={() => Alert.alert('Coming Soon', 'Report settings will be available soon!')}>
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        
        {reports && (
          <>
            {renderSummaryCard()}
            
            <View style={styles.reportTypesSection}>
              <Text style={styles.sectionTitle}>Generate Reports</Text>
              {reportTypes.map(renderReportTypeCard)}
            </View>
            
            {renderRecentReports()}
            {renderQuickActions()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  periodSelector: {
    marginBottom: 20,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 8,
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  periodButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  periodButtonTextActive: {
    color: theme.colors.white,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    width: '48%',
    marginBottom: 12,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  performanceItem: {
    flex: 1,
  },
  performanceLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  performanceValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  reportTypesSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  reportTypeCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reportTypeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  reportTypeContent: {
    flex: 1,
  },
  reportTypeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  reportTypeDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  recentReportsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  recentReportItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  recentReportInfo: {
    flex: 1,
  },
  recentReportTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  recentReportPeriod: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  recentReportDate: {
    fontSize: 10,
    color: theme.colors.textSecondary,
  },
  recentReportActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusBadge: {
    backgroundColor: theme.colors.success + '20',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
  },
  statusText: {
    fontSize: 10,
    color: theme.colors.success,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  quickActionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    alignItems: 'center',
    flex: 1,
    paddingVertical: 12,
  },
  quickActionText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default InvestmentReportsScreen;
