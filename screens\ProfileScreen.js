import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  ActivityIndicator,
  RefreshControl,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import databaseService from '../services/databaseService';
import { preferenceService } from '../services/preferenceService';
import notificationService from '../services/notificationService';
import walletService from '../services/walletService';
import profileManagementService from '../services/profileManagementService';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import LanguageSelector from '../components/LanguageSelector';
import CurrencySelector from '../components/CurrencySelector.js';
import ResponsiveText from '../components/ResponsiveText.js';
import NetworkStatusIndicator, { useNetworkStatus } from '../components/NetworkStatusIndicator';
import offlineStorageService from '../services/offlineStorageService';
import networkService from '../services/networkService';
import currencyService from '../services/currencyService';

const ProfileScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [userPreferences, setUserPreferences] = useState(null);
  const [walletData, setWalletData] = useState(null);
  const [transactionCount, setTransactionCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [pendingChanges, setPendingChanges] = useState([]);

  // Use theme, language, and currency contexts
  const { isDarkMode, setTheme, theme, isInitialized } = useTheme();
  const { currentLanguage, changeLanguage, t } = useLanguage();
  const { userCurrency, updateUserCurrency, convertAndFormat } = useCurrencyContext();

  // Network status
  const networkStatus = useNetworkStatus();

  useEffect(() => {
    loadUserData();
  }, []);

  // Synchronize userPreferences dark_mode_enabled with actual theme state
  useEffect(() => {
    if (isInitialized && userPreferences && userPreferences.dark_mode_enabled !== isDarkMode) {
      console.log(`🔄 Synchronizing dark mode preference: database=${userPreferences.dark_mode_enabled}, theme=${isDarkMode}`);
      setUserPreferences(prev => ({
        ...prev,
        dark_mode_enabled: isDarkMode
      }));
    }
  }, [isDarkMode, isInitialized, userPreferences]);

  // Add focus listener to refresh data when returning from other screens
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadUserData();
    });

    return unsubscribe;
  }, [navigation]);

  const loadUserData = async (forceOnline = false) => {
    try {
      setLoading(true);
      const isOnline = networkStatus.isConnected && networkStatus.canMakeRequests;

      console.log(`📱 ProfileScreen: Loading user data - Online: ${isOnline}, Force: ${forceOnline}`);

      // Get current user
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);

        // Load profile data with offline-first approach
        await loadProfileData(currentUser, isOnline && !forceOnline);

        // Load preferences with offline-first approach
        await loadPreferencesData(currentUser, isOnline && !forceOnline);

        // Load wallet data (try online first, fallback to cache)
        await loadWalletData(currentUser, isOnline);

        // Load transaction data (try online first, fallback to cache)
        await loadTransactionData(isOnline, currentUser);

        // Update sync status
        const syncTimes = await offlineStorageService.getLastSyncTimes();
        setLastSyncTime(syncTimes.profile || null);

        // Check for pending changes
        const pending = await offlineStorageService.getPendingTransactions();
        setPendingChanges(pending.filter(t => t.type === 'profile_update'));

        setIsOfflineMode(!isOnline);
      }
    } catch (error) {
      console.error('❌ ProfileScreen: Error loading user data:', error);

      // Try to load from offline storage as fallback
      await loadOfflineData();
    } finally {
      setLoading(false);
    }
  };

  const loadProfileData = async (currentUser, tryOfflineFirst = true) => {
    try {
      // Try offline first if requested
      if (tryOfflineFirst) {
        const cachedProfile = await offlineStorageService.getUserProfile();
        if (cachedProfile) {
          console.log('💾 ProfileScreen: Loaded profile from offline storage');
          setUserProfile(cachedProfile);
          return;
        }
      }

      // Try online if connected
      if (networkStatus.isConnected) {
        const profileResult = await authService.getUserProfile(currentUser.id);
        if (profileResult.success) {
          console.log('🌐 ProfileScreen: Loaded profile from server');
          setUserProfile(profileResult.data);

          // Cache for offline use
          await offlineStorageService.saveUserProfile(profileResult.data);
          await offlineStorageService.updateLastSyncTime('profile');
          return;
        }
      }

      // Fallback to default profile for new users
      const defaultProfile = {
        id: currentUser.id,
        full_name: currentUser.full_name || 'User',
        email: currentUser.email || '',
        phone_number: currentUser.phone_number || '',
        avatar_url: null,
        created_at: new Date().toISOString(),
        is_new_user: true,
        offline_mode: true
      };

      setUserProfile(defaultProfile);
      await offlineStorageService.saveUserProfile(defaultProfile);

    } catch (error) {
      console.error('❌ ProfileScreen: Error loading profile data:', error);
    }
  };

  const loadPreferencesData = async (currentUser, tryOfflineFirst = true) => {
    try {
      // Try offline first if requested
      if (tryOfflineFirst) {
        const cachedSettings = await offlineStorageService.getUserSettings();
        if (cachedSettings) {
          console.log('💾 ProfileScreen: Loaded preferences from offline storage');
          setUserPreferences(cachedSettings);
          return;
        }
      }

      // Try online if connected
      if (networkStatus.isConnected) {
        const preferencesResult = await databaseService.getUserPreferences(currentUser.id);
        if (preferencesResult.success) {
          console.log('🌐 ProfileScreen: Loaded preferences from server');
          setUserPreferences(preferencesResult.data);

          // Cache for offline use
          await offlineStorageService.saveUserSettings(preferencesResult.data);
          await offlineStorageService.updateLastSyncTime('preferences');
          return;
        }
      }

      // Fallback to default preferences
      const defaultPreferences = {
        id: currentUser.id,
        biometric_enabled: false,
        notifications_enabled: true,
        sms_notifications: true,
        email_notifications: true,
        dark_mode_enabled: isDarkMode,
        preferred_currency: 'UGX',
        transaction_limit_daily: 1000000,
        transaction_limit_monthly: 10000000,
        security_pin_enabled: false,
        auto_logout_minutes: 30,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        offline_mode: true
      };

      setUserPreferences(defaultPreferences);
      await offlineStorageService.saveUserSettings(defaultPreferences);

    } catch (error) {
      console.error('❌ ProfileScreen: Error loading preferences data:', error);
    }
  };

  const loadWalletData = async (currentUser, isOnline) => {
    try {
      if (isOnline && currentUser?.id) {
        console.log('🔍 ProfileScreen: Loading wallet data for user:', currentUser.id);
        const walletResult = await walletService.getWalletBalance(currentUser.id);
        if (walletResult.success) {
          console.log('🌐 ProfileScreen: Loaded wallet data from server');
          setWalletData(walletResult.data);

          // Cache wallet data
          await offlineStorageService.cacheData('wallet_data', walletResult.data, 1); // 1 hour cache
          return;
        }
      }

      // Try cached wallet data
      const cachedWallet = await offlineStorageService.getCachedData('wallet_data');
      if (cachedWallet && cachedWallet.data) {
        console.log('💾 ProfileScreen: Loaded wallet data from cache');
        setWalletData({
          ...cachedWallet.data,
          fromCache: true,
          expired: cachedWallet.expired
        });
        return;
      }

      // Fallback to default wallet data
      setWalletData({
        balance: 0,
        currency: 'UGX',
        account_number: currentUser.phone_number || 'Not Available',
        offline_mode: true
      });

    } catch (error) {
      console.error('❌ ProfileScreen: Error loading wallet data:', error);
    }
  };

  const loadOfflineData = async () => {
    try {
      console.log('💾 ProfileScreen: Loading all data from offline storage');

      const cachedProfile = await offlineStorageService.getUserProfile();
      const cachedSettings = await offlineStorageService.getUserSettings();
      const cachedWallet = await offlineStorageService.getCachedData('wallet_data');

      if (cachedProfile) setUserProfile(cachedProfile);
      if (cachedSettings) setUserPreferences(cachedSettings);
      if (cachedWallet) setWalletData(cachedWallet.data);

      setIsOfflineMode(true);

    } catch (error) {
      console.error('❌ ProfileScreen: Error loading offline data:', error);
    }
  };

  const loadTransactionData = async (isOnline, currentUser = null) => {
    try {
      if (isOnline && currentUser?.id) {
        console.log('🔍 ProfileScreen: Loading transactions for user:', currentUser.id);
        // Fix parameter order: (limit, userId) not (userId, limit)
        const transactionsResult = await walletService.getRecentTransactions(100, currentUser.id);
        if (transactionsResult.success && transactionsResult.data) {
          console.log('🌐 ProfileScreen: Loaded transactions from server');
          setTransactionCount(transactionsResult.data.length);

          // Cache transaction history
          await offlineStorageService.saveTransactionHistory(transactionsResult.data);
          return;
        }
      }

      // Try cached transaction data
      const cachedTransactions = await offlineStorageService.getTransactionHistory();
      if (cachedTransactions && cachedTransactions.data) {
        console.log('💾 ProfileScreen: Loaded transactions from cache');
        setTransactionCount(cachedTransactions.data.length);
        return;
      }

      // Fallback to zero
      setTransactionCount(0);

    } catch (error) {
      console.error('❌ ProfileScreen: Error loading transaction data:', error);
      setTransactionCount(0);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);

    if (networkStatus.isConnected) {
      // Force online refresh
      await loadUserData(true);
      console.log('🔄 ProfileScreen: Forced online refresh completed');
    } else {
      // Offline refresh - just reload cached data
      await loadOfflineData();
      console.log('💾 ProfileScreen: Offline refresh completed');
    }

    setRefreshing(false);
  };

  const syncPendingChanges = async () => {
    if (!networkStatus.isConnected || pendingChanges.length === 0) return;

    console.log(`🔄 ProfileScreen: Syncing ${pendingChanges.length} pending changes`);

    for (const change of pendingChanges) {
      try {
        // Process pending profile updates
        if (change.type === 'profile_update') {
          // Implement sync logic here
          await offlineStorageService.removePendingTransaction(change.id);
        }
      } catch (error) {
        console.error('❌ ProfileScreen: Error syncing change:', error);
      }
    }

    // Reload pending changes
    const remaining = await offlineStorageService.getPendingTransactions();
    setPendingChanges(remaining.filter(t => t.type === 'profile_update'));
  };

  // Sync when connection is restored
  useEffect(() => {
    if (networkStatus.isConnected && pendingChanges.length > 0) {
      syncPendingChanges();
    }
  }, [networkStatus.isConnected]);

  const handleImagePicker = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      t('profile.updateProfilePicture'),
      t('profile.chooseUpdateMethod'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('profile.takePhoto'), onPress: () => takePhoto() },
        { text: t('profile.chooseFromGallery'), onPress: () => pickFromGallery() }
      ]
    );
  };

  const takePhoto = async () => {
    try {
      // Navigate to ProfilePictureScreen for camera functionality
      navigation.navigate('ProfilePicture');
    } catch (error) {
      console.error('❌ Error taking photo:', error);
      Alert.alert(t('common.errorTitle'), t('profile.errorTakingPhoto'));
    }
  };

  const pickFromGallery = async () => {
    try {
      // Navigate to ProfilePictureScreen which handles both camera and gallery
      navigation.navigate('ProfilePicture');
    } catch (error) {
      console.error('❌ Error picking from gallery:', error);
      Alert.alert(t('common.errorTitle'), t('profile.errorPickingImage'));
    }
  };



  const togglePreference = async (key, value) => {
    try {
      console.log(`🔧 Toggling preference: ${key} = ${value}`);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Update local state immediately for better UX
      setUserPreferences(prev => ({ ...prev, [key]: value }));

      // Handle special preference changes
      if (key === 'dark_mode_enabled') {
        console.log(`🎨 Setting theme to: ${value ? 'dark' : 'light'}`);
        await setTheme(value);
        console.log('✅ Theme updated successfully');
      } else if (key === 'notifications_enabled') {
        console.log(`🔔 Setting notifications to: ${value}`);
        try {
          const result = await notificationService.setNotificationEnabled(value);
          if (result.warning) {
            console.log(`⚠️ Notification warning: ${result.warning}`);
            // Show user-friendly message for Expo Go limitations
            if (result.warning.includes('Expo Go')) {
              Alert.alert(
                t('profile.notifications'),
                t('profile.notificationsSaved'),
                [{ text: t('common.ok') }]
              );
            }
          } else {
            console.log('✅ Notifications updated successfully');
          }
        } catch (error) {
          console.error('❌ Error updating notifications:', error);
          Alert.alert(
            t('common.errorTitle'),
            t('profile.errorUpdatingNotifications'),
            [{ text: t('common.ok') }]
          );
        }
      }

      // Use preference service for persistence
      console.log('💾 Saving preference to storage...');
      await preferenceService.set(key, value, user?.id);
      console.log('✅ Preference saved to storage');

      // Also try to update in database (may fail in development due to UUID issue)
      if (user && user.id) {
        try {
          await databaseService.updateUserPreferences(user.id, { [key]: value });
          console.log('✅ Preference saved to database');
        } catch (error) {
          console.log('⚠️ Database update failed (expected in development):', error.message);
        }
      }
    } catch (error) {
      console.error('❌ Error updating preference:', error);
    }
  };

  const handleLanguageChange = async (languageCode) => {
    try {
      await changeLanguage(languageCode);
      await preferenceService.set('preferred_language', languageCode, user?.id);
      setUserPreferences(prev => ({ ...prev, preferred_language: languageCode }));
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(t('common.errorTitle'), t('profile.errorChangingLanguage'));
    }
  };

  const handleCurrencyChange = async (currencyCode, currencyInfo) => {
    try {
      // Update currency through context (this will handle all synchronization)
      await updateUserCurrency(currencyCode);

      // Update local preferences for backup
      await preferenceService.set('preferred_currency', currencyCode, user?.id);
      setUserPreferences(prev => ({ ...prev, preferred_currency: currencyCode }));

      // Provide haptic feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Success message is handled by CurrencySelector
    } catch (error) {
      console.error('❌ Error changing currency:', error);
      Alert.alert(t('common.errorTitle'), t('profile.errorChangingCurrency'));
    }
  };

  const handleLogout = () => {
    Alert.alert(
      t('profile.logout'),
      t('profile.logoutConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('profile.logout'),
          style: 'destructive',
          onPress: async () => {
            try {
              await authService.signOut();
              // Navigation will be handled by auth state change
            } catch (error) {
              Alert.alert(t('common.errorTitle'), t('profile.errorLogout'));
            }
          },
        },
      ]
    );
  };

  const renderProfileHeader = () => (
    <LinearGradient
      colors={Colors.gradients.sunset}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.profileHeader}
    >
      <View style={styles.profileImageContainer}>
        <TouchableOpacity onPress={handleImagePicker} style={styles.imageWrapper}>
          {userProfile?.avatar_url ? (
            <Image
              source={{ uri: userProfile.avatar_url }}
              style={styles.profileImage}
              onLoad={() => console.log('🖼️ Profile image loaded successfully')}
              onError={(error) => console.log('❌ Profile image load error:', error)}
            />
          ) : (
            <View style={styles.defaultAvatar}>
              <Ionicons name="person" size={40} color={Colors.neutral.warmGray} />
            </View>
          )}
          {console.log('🖼️ Profile picture URL:', userProfile?.avatar_url)}
          <View style={styles.editImageButton}>
            <Ionicons name="camera" size={16} color={Colors.neutral.white} />
          </View>
        </TouchableOpacity>
      </View>
      
      <View style={styles.profileInfo}>
        <Text style={styles.profileName}>
          {userProfile?.full_name || user?.user_metadata?.full_name || t('wallet.user')}
        </Text>
        <Text style={styles.profilePhone}>
          {userProfile?.phone_number || user?.phone || t('wallet.phoneNotAvailable')}
        </Text>
        <View style={styles.verificationBadge}>
          <Ionicons 
            name={userProfile?.is_verified ? "checkmark-circle" : "time-outline"} 
            size={16} 
            color={Colors.neutral.white} 
          />
          <Text style={styles.verificationText}>
            {userProfile?.is_verified ? t('profile.verifiedAccount') : t('profile.pendingVerification')}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );

  // Create theme-aware styles
  const styles = createStyles(theme);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>{t('wallet.loadingProfile')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Static Header with Network Status */}
      <View style={styles.staticHeader}>
        <Text style={styles.headerTitle}>{t('navigation.profile')}</Text>
        <View style={styles.headerActions}>
          <NetworkStatusIndicator
            compact={true}
            showDetails={false}
            style={styles.networkIndicator}
          />
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Offline Mode Banner */}
      {isOfflineMode && (
        <View style={styles.offlineBanner}>
          <Ionicons name="cloud-offline" size={16} color={Colors.neutral.white} />
          <Text style={styles.offlineBannerText}>
            {t('common.workingOfflineWithCachedData')}
          </Text>
          {lastSyncTime && (
            <Text style={styles.lastSyncText}>
              Last sync: {new Date(lastSyncTime).toLocaleTimeString()}
            </Text>
          )}
        </View>
      )}

      {/* Pending Changes Banner */}
      {pendingChanges.length > 0 && (
        <View style={styles.pendingBanner}>
          <Ionicons name="sync" size={16} color={Colors.neutral.white} />
          <Text style={styles.pendingBannerText}>
            {pendingChanges.length} changes will sync when online
          </Text>
        </View>
      )}

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderProfileHeader()}
      
      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {convertAndFormat(walletData?.balance || 0)}
          </Text>
          <Text style={styles.statLabel}>{t('profile.walletBalance')}</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{transactionCount}</Text>
          <Text style={styles.statLabel}>{t('profile.transactions')}</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {userProfile?.verification_level || 'Basic'}
          </Text>
          <Text style={styles.statLabel}>{t('profile.accountLevel')}</Text>
        </View>
      </View>

      {/* Account Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('profile.accountManagement')}</Text>

        <TouchableOpacity
          style={styles.menuItem}
          onPress={() => navigation.navigate('EditProfile', {
            userProfile: userProfile,
            onProfileUpdated: loadUserData
          })}
        >
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.main }]}>
              <Ionicons name="person-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.editProfile')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.updatePersonalInfo')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('AccountVerification')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.savanna }]}>
              <Ionicons name="shield-checkmark-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.accountVerification')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.accountVerificationDesc')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Security & Privacy */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('profile.securityAndPrivacy')}</Text>



        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('SecuritySettings')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.lake }]}>
              <Ionicons name="shield-checkmark-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.securitySettings')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.securitySettingsDesc')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => navigation.navigate('PrivacyControls')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="lock-closed-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.privacyAndData')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.privacyDataDesc')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* App Preferences */}
      <View style={styles.section}>
        <ResponsiveText style={styles.sectionTitle}>{t('profile.appPreferences')}</ResponsiveText>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.neutral.charcoal }]}>
              <Ionicons name="moon-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.darkMode')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.darkModeDesc')}</Text>
            </View>
          </View>
          <Switch
            value={isDarkMode}
            onValueChange={(value) => togglePreference('dark_mode_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={isDarkMode ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="notifications-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.notifications')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.notificationsDesc')}</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.notifications_enabled || false}
            onValueChange={(value) => togglePreference('notifications_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.notifications_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.light }]}>
              <Ionicons name="language-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View style={styles.menuItemContent}>
              <ResponsiveText style={styles.menuItemTitle}>{t('settings.language')}</ResponsiveText>
              <ResponsiveText style={styles.menuItemSubtitle}>{t('settings.languageDescription')}</ResponsiveText>
            </View>
          </View>
          <View style={styles.selectorContainer}>
            <LanguageSelector
              currentLanguage={currentLanguage}
              onLanguageChange={handleLanguageChange}
              style={styles.languageSelector}
              showAllLanguages={true}
            />
          </View>
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.earth }]}>
              <Ionicons name="card-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View style={styles.menuItemContent}>
              <ResponsiveText style={styles.menuItemTitle}>{t('profile.currency')}</ResponsiveText>
              <ResponsiveText style={styles.menuItemSubtitle}>{t('profile.currencyDesc')}</ResponsiveText>
            </View>
          </View>
          <View style={styles.selectorContainer}>
            <CurrencySelector
              selectedCurrency={userCurrency || 'UGX'}
              onCurrencyChange={handleCurrencyChange}
              style={styles.currencySelector}
              compact={true}
              showConversion={false}
            />
          </View>
        </View>
      </View>

      {/* Help & Support */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{t('profile.helpAndSupport')}</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => {
          try {
            navigation.navigate('FAQ');
          } catch (error) {
            Alert.alert(t('profile.faq'), t('profile.faqAlert'));
          }
        }}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.lake }]}>
              <Ionicons name="help-circle-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.faq')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.faqDesc')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => {
          console.log('🤖 Navigating to AI Assistant...');
          navigation.navigate('AIChat');
        }}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="chatbubble-ellipses-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('profile.aiAssistant')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.aiAssistantDesc')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => {
          try {
            navigation.navigate('ContactSupport');
          } catch (error) {
            Alert.alert(t('profile.contactUs'), t('profile.contactSupportAlert'));
          }
        }}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.main }]}>
              <Ionicons name="call-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>{t('settings.contactUs')}</Text>
              <Text style={styles.menuItemSubtitle}>{t('profile.customerService')}</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Logout */}
      <View style={styles.section}>
        <TouchableOpacity style={[styles.menuItem, styles.logoutButton]} onPress={handleLogout}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.status.error }]}>
              <Ionicons name="log-out-outline" size={20} color={Colors.neutral.white} />
            </View>
            <Text style={[styles.menuItemTitle, { color: Colors.status.error }]}>{t('profile.logout')}</Text>
          </View>
        </TouchableOpacity>
      </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  // Static Header Styles
  staticHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    zIndex: 1000,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  networkIndicator: {
    marginRight: 4,
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  offlineBanner: {
    backgroundColor: Colors.status.warning,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  offlineBannerText: {
    color: Colors.neutral.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  lastSyncText: {
    color: Colors.neutral.white,
    fontSize: 12,
    opacity: 0.9,
  },
  pendingBanner: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 16,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pendingBannerText: {
    color: Colors.neutral.white,
    fontSize: 13,
    fontWeight: '500',
    marginLeft: 8,
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  profileHeader: {
    paddingTop: 20,
    paddingBottom: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  profileImageContainer: {
    marginBottom: 16,
  },
  imageWrapper: {
    position: 'relative',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: theme.colors.background,
  },
  defaultAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: theme.colors.background,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary.dark,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.neutral.white,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 4,
  },
  profilePhone: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginBottom: 8,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verificationText: {
    color: Colors.neutral.white,
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  quickStats: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    marginHorizontal: 24,
    marginTop: -16,
    borderRadius: 16,
    paddingVertical: 20,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: theme.colors.border,
    marginVertical: 8,
  },
  section: {
    marginTop: 24,
    marginHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuItemContent: {
    flex: 1,
  },
  selectorContainer: {
    flex: 1,
    marginLeft: 16,
  },
  languageSelector: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  currencySelector: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  logoutButton: {
    borderColor: Colors.status.error,
    borderWidth: 1,
    backgroundColor: 'rgba(192, 57, 43, 0.05)',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default ProfileScreen;
