# JiraniPay Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost
API_VERSION=v1

# Database Configuration (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
DATABASE_URL=postgresql://postgres:password@localhost:5432/jiranipay

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_minimum_32_characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Keys
ENCRYPTION_KEY=your_32_character_encryption_key_here
HASH_SALT_ROUNDS=12

# SMS Configuration (Africa's Talking)
AFRICASTALKING_USERNAME=your_username
AFRICASTALKING_API_KEY=your_api_key
SMS_SENDER_ID=JiraniPay

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
EMAIL_FROM=JiraniPay <<EMAIL>>

# Mobile Money APIs
# MTN Mobile Money
MTN_API_BASE_URL=https://sandbox.momodeveloper.mtn.com
MTN_SUBSCRIPTION_KEY=your_mtn_subscription_key
MTN_API_USER_ID=your_mtn_api_user_id
MTN_API_KEY=your_mtn_api_key
MTN_CALLBACK_URL=https://api.jiranipay.com/webhooks/mtn

# Airtel Money
AIRTEL_API_BASE_URL=https://openapiuat.airtel.africa
AIRTEL_CLIENT_ID=your_airtel_client_id
AIRTEL_CLIENT_SECRET=your_airtel_client_secret
AIRTEL_CALLBACK_URL=https://api.jiranipay.com/webhooks/airtel

# Banking APIs
# Stanbic Bank
STANBIC_API_BASE_URL=https://api.stanbicbank.co.ug
STANBIC_CLIENT_ID=your_stanbic_client_id
STANBIC_CLIENT_SECRET=your_stanbic_client_secret

# Utility Providers
# UMEME (Electricity)
UMEME_API_BASE_URL=https://api.umeme.co.ug
UMEME_API_KEY=your_umeme_api_key
UMEME_MERCHANT_ID=your_umeme_merchant_id

# NWSC (Water)
NWSC_API_BASE_URL=https://api.nwsc.co.ug
NWSC_API_KEY=your_nwsc_api_key
NWSC_MERCHANT_ID=your_nwsc_merchant_id

# Currency Exchange
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key
EXCHANGE_RATE_BASE_URL=https://api.exchangerate-api.com/v4

# Security & Compliance
# KYC Provider
KYC_API_BASE_URL=https://api.kyc-provider.com
KYC_API_KEY=your_kyc_api_key

# Fraud Detection
FRAUD_DETECTION_API_URL=https://api.fraud-detection.com
FRAUD_DETECTION_API_KEY=your_fraud_detection_key

# Monitoring & Logging
SENTRY_DSN=your_sentry_dsn_for_error_tracking
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf

# Business Configuration
DEFAULT_CURRENCY=UGX
SUPPORTED_CURRENCIES=UGX,KES,TZS,RWF,BIF,ETB
DEFAULT_COUNTRY=UG
SUPPORTED_COUNTRIES=UG,KE,TZ,RW,BI,ET

# Transaction Limits (in UGX)
MAX_TRANSACTION_AMOUNT=10000000
DAILY_TRANSACTION_LIMIT=50000000
KYC_REQUIRED_AMOUNT=1000000
BIOMETRIC_REQUIRED_AMOUNT=500000

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_key
WEBHOOK_TIMEOUT=30000

# Development/Testing
ENABLE_MOCK_PAYMENTS=false
ENABLE_TEST_ENDPOINTS=false
BYPASS_KYC_IN_DEV=false
