import * as Contacts from 'expo-contacts';
import AsyncStorage from '@react-native-async-storage/async-storage';
import supabase from './supabaseClient';
import authService from './authService';
import { formatPhoneNumber, validatePhoneNumber } from '../utils/phoneValidation';

/**
 * ContactService - Manages device contacts and favorite contacts for money transfers
 * Handles contact permissions, phone validation, and favorite contact management
 */
class ContactService {
  constructor() {
    this.deviceContacts = [];
    this.favoriteContacts = [];
    this.contactsLoaded = false;
    this.permissionGranted = false;
  }

  /**
   * Request permission to access device contacts
   * @returns {Promise<Object>} Permission result
   */
  async requestContactsPermission() {
    try {
      console.log('📱 Requesting contacts permission...');
      
      const { status } = await Contacts.requestPermissionsAsync();
      this.permissionGranted = status === 'granted';
      
      console.log('📱 Contacts permission:', status);
      
      return {
        success: this.permissionGranted,
        status,
        message: this.permissionGranted 
          ? 'Contacts permission granted' 
          : 'Contacts permission denied'
      };
    } catch (error) {
      console.error('❌ Error requesting contacts permission:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Load contacts from device
   * @returns {Promise<Object>} Contacts data
   */
  async loadDeviceContacts() {
    try {
      if (!this.permissionGranted) {
        const permissionResult = await this.requestContactsPermission();
        if (!permissionResult.success) {
          return permissionResult;
        }
      }

      console.log('📱 Loading device contacts...');
      
      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.ID
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      // Process and validate contacts
      this.deviceContacts = this.processDeviceContacts(data);
      this.contactsLoaded = true;

      console.log(`📱 Loaded ${this.deviceContacts.length} valid contacts`);

      return {
        success: true,
        contacts: this.deviceContacts,
        count: this.deviceContacts.length
      };
    } catch (error) {
      console.error('❌ Error loading device contacts:', error);
      return {
        success: false,
        error: error.message,
        contacts: []
      };
    }
  }

  /**
   * Process raw device contacts and validate phone numbers
   * @param {Array} rawContacts Raw contacts from device
   * @returns {Array} Processed contacts with valid phone numbers
   */
  processDeviceContacts(rawContacts) {
    const processedContacts = [];

    rawContacts.forEach(contact => {
      if (!contact.phoneNumbers || contact.phoneNumbers.length === 0) {
        return; // Skip contacts without phone numbers
      }

      contact.phoneNumbers.forEach(phoneData => {
        const phoneNumber = phoneData.number;
        const validation = validatePhoneNumber(phoneNumber);
        
        if (validation.isValid) {
          processedContacts.push({
            id: `${contact.id}_${phoneNumber}`,
            contactId: contact.id,
            name: contact.name || 'Unknown Contact',
            phoneNumber: validation.formatted,
            originalPhone: phoneNumber,
            networkProvider: validation.networkProvider,
            isFavorite: false
          });
        }
      });
    });

    // Remove duplicates based on phone number
    const uniqueContacts = processedContacts.filter((contact, index, self) =>
      index === self.findIndex(c => c.phoneNumber === contact.phoneNumber)
    );

    return uniqueContacts.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Search contacts by name or phone number
   * @param {string} query Search query
   * @returns {Array} Filtered contacts
   */
  searchContacts(query) {
    if (!query || query.trim().length === 0) {
      return this.deviceContacts;
    }

    const searchTerm = query.toLowerCase().trim();
    
    return this.deviceContacts.filter(contact => 
      contact.name.toLowerCase().includes(searchTerm) ||
      contact.phoneNumber.includes(searchTerm) ||
      contact.originalPhone.includes(searchTerm)
    );
  }

  /**
   * Load favorite contacts from database
   * @returns {Promise<Object>} Favorite contacts
   */
  async loadFavoriteContacts() {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log('⭐ Loading favorite contacts...');

      const { data, error } = await supabase
        .from('favorite_contacts')
        .select('*')
        .eq('user_id', user.id)
        .order('frequency_count', { ascending: false });

      if (error) {
        console.error('❌ Error loading favorite contacts:', error);
        return { success: false, error: error.message };
      }

      this.favoriteContacts = data || [];
      
      // Mark device contacts as favorites
      this.markFavoriteContacts();

      console.log(`⭐ Loaded ${this.favoriteContacts.length} favorite contacts`);

      return {
        success: true,
        favorites: this.favoriteContacts
      };
    } catch (error) {
      console.error('❌ Error loading favorite contacts:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Mark device contacts as favorites based on database data
   */
  markFavoriteContacts() {
    const favoritePhones = new Set(this.favoriteContacts.map(fav => fav.contact_phone));
    
    this.deviceContacts.forEach(contact => {
      contact.isFavorite = favoritePhones.has(contact.phoneNumber);
    });
  }

  /**
   * Add contact to favorites
   * @param {Object} contact Contact to add to favorites
   * @returns {Promise<Object>} Result
   */
  async addToFavorites(contact) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log('⭐ Adding contact to favorites:', contact.name);

      // Check if already exists
      const existing = this.favoriteContacts.find(
        fav => fav.contact_phone === contact.phoneNumber
      );

      if (existing) {
        // Update frequency count
        const { error } = await supabase
          .from('favorite_contacts')
          .update({ 
            frequency_count: existing.frequency_count + 1,
            last_used: new Date().toISOString()
          })
          .eq('id', existing.id);

        if (error) {
          console.error('❌ Error updating favorite contact:', error);
          return { success: false, error: error.message };
        }

        existing.frequency_count += 1;
        existing.last_used = new Date().toISOString();
      } else {
        // Add new favorite
        const { data, error } = await supabase
          .from('favorite_contacts')
          .insert([{
            user_id: user.id,
            contact_phone: contact.phoneNumber,
            contact_name: contact.name,
            frequency_count: 1,
            last_used: new Date().toISOString()
          }])
          .select()
          .single();

        if (error) {
          console.error('❌ Error adding favorite contact:', error);
          return { success: false, error: error.message };
        }

        this.favoriteContacts.push(data);
      }

      // Update device contacts
      const deviceContact = this.deviceContacts.find(
        dc => dc.phoneNumber === contact.phoneNumber
      );
      if (deviceContact) {
        deviceContact.isFavorite = true;
      }

      console.log('✅ Contact added to favorites successfully');

      return { success: true };
    } catch (error) {
      console.error('❌ Error adding to favorites:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Remove contact from favorites
   * @param {string} phoneNumber Phone number to remove
   * @returns {Promise<Object>} Result
   */
  async removeFromFavorites(phoneNumber) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      console.log('⭐ Removing contact from favorites:', phoneNumber);

      const { error } = await supabase
        .from('favorite_contacts')
        .delete()
        .eq('user_id', user.id)
        .eq('contact_phone', phoneNumber);

      if (error) {
        console.error('❌ Error removing favorite contact:', error);
        return { success: false, error: error.message };
      }

      // Update local data
      this.favoriteContacts = this.favoriteContacts.filter(
        fav => fav.contact_phone !== phoneNumber
      );

      // Update device contacts
      const deviceContact = this.deviceContacts.find(
        dc => dc.phoneNumber === phoneNumber
      );
      if (deviceContact) {
        deviceContact.isFavorite = false;
      }

      console.log('✅ Contact removed from favorites successfully');

      return { success: true };
    } catch (error) {
      console.error('❌ Error removing from favorites:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all contacts (device + favorites) sorted by relevance
   * @returns {Array} All contacts sorted by favorites first, then frequency
   */
  getAllContacts() {
    const allContacts = [...this.deviceContacts];
    
    // Sort by favorites first, then by name
    return allContacts.sort((a, b) => {
      if (a.isFavorite && !b.isFavorite) return -1;
      if (!a.isFavorite && b.isFavorite) return 1;
      return a.name.localeCompare(b.name);
    });
  }

  /**
   * Get favorite contacts only
   * @returns {Array} Favorite contacts sorted by frequency
   */
  getFavoriteContacts() {
    return this.favoriteContacts.sort((a, b) => b.frequency_count - a.frequency_count);
  }

  /**
   * Initialize contact service
   * @returns {Promise<Object>} Initialization result
   */
  async initialize() {
    try {
      console.log('📱 Initializing contact service...');
      
      const [contactsResult, favoritesResult] = await Promise.all([
        this.loadDeviceContacts(),
        this.loadFavoriteContacts()
      ]);

      return {
        success: true,
        contactsLoaded: contactsResult.success,
        favoritesLoaded: favoritesResult.success,
        contactCount: this.deviceContacts.length,
        favoriteCount: this.favoriteContacts.length
      };
    } catch (error) {
      console.error('❌ Error initializing contact service:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Export singleton instance
const contactService = new ContactService();
export default contactService;
