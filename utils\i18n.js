/**
 * Internationalization (i18n) configuration for JiraniPay
 * Supports multiple East African languages for enhanced user experience
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Language configurations with native names and country associations
// All 8 East African languages with proper priority ordering
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇬🇧',
    countries: ['UG', 'KE', 'TZ', 'RW', 'BI', 'SS', 'ET', 'SO', 'DJ', 'ER'],
    rtl: false,
    priority: 1
  },
  sw: {
    code: 'sw',
    name: 'Swahili',
    nativeName: 'Kiswahili',
    flag: '🇹🇿',
    countries: ['KE', 'TZ', 'UG'],
    rtl: false,
    priority: 2
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    countries: ['RW', 'BI', 'DJ'],
    rtl: false,
    priority: 3
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    countries: ['SS', 'SO', 'DJ', 'ER'],
    rtl: true,
    priority: 4
  },
  am: {
    code: 'am',
    name: 'Amharic',
    nativeName: 'አማርኛ',
    flag: '🇪🇹',
    countries: ['ET'],
    rtl: false,
    priority: 5
  },
  lg: {
    code: 'lg',
    name: 'Luganda',
    nativeName: 'Oluganda',
    flag: '🇺🇬',
    countries: ['UG'],
    rtl: false,
    priority: 6
  },
  rw: {
    code: 'rw',
    name: 'Kinyarwanda',
    nativeName: 'Ikinyarwanda',
    flag: '🇷🇼',
    countries: ['RW'],
    rtl: false,
    priority: 7
  },
  rn: {
    code: 'rn',
    name: 'Kirundi',
    nativeName: 'Ikirundi',
    flag: '🇧🇮',
    countries: ['BI'],
    rtl: false,
    priority: 8
  }
};

// Import modular translation files
import enTranslations from '../locales/en.js';
import swTranslations from '../locales/sw.js';
import frTranslations from '../locales/fr.js';
import arTranslations from '../locales/ar.js';
import amTranslations from '../locales/am.js';
import lgTranslations from '../locales/lg.js';
import rwTranslations from '../locales/rw.js';
import rnTranslations from '../locales/rn.js';

// Translation strings organized by language
export const TRANSLATIONS = {
  en: enTranslations,
  sw: swTranslations,
  fr: frTranslations,
  ar: arTranslations,
  am: amTranslations,
  lg: lgTranslations, // Luganda
  rw: rwTranslations, // Kinyarwanda
  rn: rnTranslations, // Kirundi
};



// Current language state
let currentLanguage = 'en';
let currentTranslations = TRANSLATIONS.en;

/**
 * Initialize i18n system
 * Loads saved language preference or detects from device/country
 */
export const initializeI18n = async (selectedCountryCode = 'UG') => {
  try {
    // Try to load saved language preference
    const savedLanguage = await AsyncStorage.getItem('selectedLanguage');

    if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
      currentLanguage = savedLanguage;
    } else {
      // Auto-detect based on country selection
      currentLanguage = getDefaultLanguageForCountry(selectedCountryCode);
    }

    currentTranslations = TRANSLATIONS[currentLanguage] || TRANSLATIONS.en;
    return currentLanguage;
  } catch (error) {
    console.error('Error initializing i18n:', error);
    currentLanguage = 'en';
    currentTranslations = TRANSLATIONS.en;
    return 'en';
  }
};

/**
 * Get default language for a country
 * @param {string} countryCode - Two letter country code
 * @returns {string} - Language code
 */
export const getDefaultLanguageForCountry = (countryCode) => {
  const languageMap = {
    'UG': 'en', // Uganda - English primary
    'KE': 'sw', // Kenya - Swahili primary
    'TZ': 'sw', // Tanzania - Swahili primary
    'RW': 'fr', // Rwanda - French/English (using French)
    'BI': 'fr', // Burundi - French
    'SS': 'ar', // South Sudan - Arabic/English (using Arabic)
    'ET': 'am', // Ethiopia - Amharic
    'SO': 'ar', // Somalia - Arabic/Somali (using Arabic)
    'DJ': 'fr', // Djibouti - French/Arabic (using French)
    'ER': 'ar'  // Eritrea - Arabic/Tigrinya (using Arabic)
  };

  return languageMap[countryCode] || 'en';
};

/**
 * Change current language
 * @param {string} languageCode - Language code to switch to
 */
export const changeLanguage = async (languageCode) => {
  if (SUPPORTED_LANGUAGES[languageCode]) {
    currentLanguage = languageCode;
    currentTranslations = TRANSLATIONS[languageCode] || TRANSLATIONS.en;

    // Save preference
    try {
      await AsyncStorage.setItem('selectedLanguage', languageCode);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  }
};

/**
 * Get current language
 * @returns {string} - Current language code
 */
export const getCurrentLanguage = () => currentLanguage;

/**
 * Get current language configuration
 * @returns {Object} - Current language configuration
 */
export const getCurrentLanguageConfig = () => SUPPORTED_LANGUAGES[currentLanguage];

/**
 * Get available languages for a specific country
 * @param {string} countryCode - Two letter country code
 * @returns {Array} - Array of supported languages for the country
 */
export const getLanguagesForCountry = (countryCode) => {
  return Object.values(SUPPORTED_LANGUAGES).filter(lang =>
    lang.countries.includes(countryCode)
  );
};

/**
 * DEPRECATED: Static translation function - DO NOT USE
 * Use the reactive t() function from LanguageContext instead
 * This function is kept for backward compatibility only
 *
 * @deprecated Use useLanguage().t() instead for reactive translations
 */
export const t = (key, params = {}) => {
  console.warn('⚠️ Using deprecated static t() function. Use useLanguage().t() for reactive translations.');

  const keys = key.split('.');
  let translation = currentTranslations;

  // Navigate through nested keys
  for (const k of keys) {
    translation = translation?.[k];
    if (!translation) break;
  }

  // Fallback to English if translation not found
  if (!translation) {
    let fallback = TRANSLATIONS.en;
    for (const k of keys) {
      fallback = fallback?.[k];
      if (!fallback) break;
    }
    translation = fallback || key;
  }

  // Handle string interpolation
  if (typeof translation === 'string' && params) {
    return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey] || match;
    });
  }

  return translation || key;
};

/**
 * Get East African timezone time
 * Uganda is in East Africa Time (EAT) which is UTC+3
 * @returns {Date} - Date object adjusted for East African timezone
 */
export const getEastAfricanTime = () => {
  const now = new Date();
  // Get UTC time and add 3 hours for East Africa Time (EAT)
  const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
  const eastAfricanTime = new Date(utc + (3 * 3600000)); // UTC+3
  return eastAfricanTime;
};

/**
 * Get time-based greeting with optional name
 * Uses East African timezone for accurate time-based greetings
 * Three-period system: Morning (00:00AM-11:59AM), Afternoon (12PM-5:59PM), Evening (6PM-11:59PM)
 * @param {string} name - Optional user name
 * @param {boolean} useLocalTime - Whether to use local device time instead of EAT (default: false)
 * @param {Function} translateFn - Translation function to use (optional, falls back to static t)
 * @returns {string} - Localized greeting
 */
export const getTimeBasedGreeting = (name = null, useLocalTime = false, translateFn = null) => {
  // Use East African Time for Uganda market, or local time if specified
  const now = useLocalTime ? new Date() : getEastAfricanTime();
  const hour = now.getHours();

  console.log(`🕐 Greeting calculation - Hour: ${hour}, EAT: ${!useLocalTime}, Name: ${name || 'none'}`);

  let timeKey;
  if (hour >= 0 && hour < 12) {
    // 00:00 AM to 11:59 AM - Good Morning
    timeKey = name ? 'goodMorningName' : 'goodMorning';
  } else if (hour >= 12 && hour < 18) {
    // 12:00 PM to 5:59 PM - Good Afternoon
    timeKey = name ? 'goodAfternoonName' : 'goodAfternoon';
  } else {
    // 6:00 PM to 11:59 PM - Good Evening
    timeKey = name ? 'goodEveningName' : 'goodEvening';
  }

  // Use provided translation function or fall back to static t (with warning)
  const translationFunction = translateFn || t;
  if (!translateFn) {
    console.warn('⚠️ Using deprecated static t() function. Use useLanguage().t() for reactive translations.');
  }

  const greeting = translationFunction(`greetings.${timeKey}`, { name });
  console.log(`🎯 Generated greeting: "${greeting}" for hour ${hour}`);

  return greeting;
};

/**
 * Check if current language is RTL (Right-to-Left)
 * @returns {boolean} - True if current language is RTL
 */
export const isRTL = () => {
  return SUPPORTED_LANGUAGES[currentLanguage]?.rtl || false;
};

export default {
  initializeI18n,
  changeLanguage,
  getCurrentLanguage,
  getCurrentLanguageConfig,
  getLanguagesForCountry,
  getDefaultLanguageForCountry,
  getTimeBasedGreeting,
  isRTL,
  t,
  SUPPORTED_LANGUAGES,
  TRANSLATIONS
};
