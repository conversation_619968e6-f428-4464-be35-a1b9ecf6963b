#!/usr/bin/env node

/**
 * Data Consent Production Verification Script
 * 
 * Verifies that all data consent features are production-ready and compliant
 * with East African data protection regulations.
 */

const fs = require('fs');
const path = require('path');

class DataConsentVerifier {
  constructor() {
    this.results = {
      overall: 'PENDING',
      checks: [],
      errors: [],
      warnings: [],
      recommendations: []
    };
  }

  async runVerification() {
    console.log('🔍 Starting Data Consent Production Verification...\n');

    try {
      // Check service implementations
      await this.checkServiceImplementations();
      
      // Check database schema
      await this.checkDatabaseSchema();
      
      // Check UI implementation
      await this.checkUIImplementation();
      
      // Check compliance features
      await this.checkComplianceFeatures();
      
      // Check integration points
      await this.checkIntegrationPoints();
      
      // Generate final report
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Verification failed:', error);
      this.results.overall = 'FAILED';
      this.results.errors.push(`Verification error: ${error.message}`);
    }
  }

  async checkServiceImplementations() {
    console.log('📋 Checking Service Implementations...');
    
    const requiredServices = [
      'services/consentEnforcementService.js',
      'services/privacyManagementService.js',
      'services/marketingService.js',
      'services/complianceVerificationService.js'
    ];

    for (const service of requiredServices) {
      const servicePath = path.join(__dirname, '..', service);
      if (fs.existsSync(servicePath)) {
        this.addCheck(`✅ ${service} exists`);
        
        // Check if service has required methods
        const content = fs.readFileSync(servicePath, 'utf8');
        this.checkServiceMethods(service, content);
      } else {
        this.addError(`❌ Missing service: ${service}`);
      }
    }
  }

  checkServiceMethods(serviceName, content) {
    const requiredMethods = {
      'consentEnforcementService.js': [
        'hasConsent',
        'trackAnalytics',
        'sendMarketing',
        'shareData',
        'updateConsent'
      ],
      'privacyManagementService.js': [
        'getPrivacySettings',
        'updateConsent',
        'exportUserData',
        'logPrivacyEvent'
      ],
      'marketingService.js': [
        'sendPromotionalEmail',
        'sendPromotionalSMS',
        'unsubscribeUser'
      ],
      'complianceVerificationService.js': [
        'runComplianceAudit',
        'checkUgandaCompliance',
        'checkGDPRCompliance'
      ]
    };

    const serviceKey = serviceName.split('/').pop();
    const methods = requiredMethods[serviceKey] || [];
    
    for (const method of methods) {
      if (content.includes(method)) {
        this.addCheck(`✅ ${serviceName} has ${method} method`);
      } else {
        this.addError(`❌ ${serviceName} missing ${method} method`);
      }
    }
  }

  async checkDatabaseSchema() {
    console.log('🗄️ Checking Database Schema...');
    
    const schemaFiles = [
      'database/setup_security_privacy_tables.sql',
      'database/privacy_compliance_production.sql'
    ];

    for (const schemaFile of schemaFiles) {
      const schemaPath = path.join(__dirname, '..', schemaFile);
      if (fs.existsSync(schemaPath)) {
        this.addCheck(`✅ ${schemaFile} exists`);
        
        const content = fs.readFileSync(schemaPath, 'utf8');
        this.checkSchemaContent(schemaFile, content);
      } else {
        this.addError(`❌ Missing schema file: ${schemaFile}`);
      }
    }
  }

  checkSchemaContent(fileName, content) {
    const requiredTables = [
      'privacy_settings',
      'consent_audit_trail',
      'marketing_campaigns',
      'marketing_delivery_logs',
      'data_sharing_agreements'
    ];

    for (const table of requiredTables) {
      if (content.includes(table)) {
        this.addCheck(`✅ ${fileName} includes ${table} table`);
      } else {
        this.addWarning(`⚠️ ${fileName} missing ${table} table`);
      }
    }

    // Check for RLS policies
    if (content.includes('ROW LEVEL SECURITY')) {
      this.addCheck(`✅ ${fileName} includes RLS policies`);
    } else {
      this.addError(`❌ ${fileName} missing RLS policies`);
    }
  }

  async checkUIImplementation() {
    console.log('🎨 Checking UI Implementation...');
    
    const uiFiles = [
      'screens/PrivacyControlsScreen.js'
    ];

    for (const uiFile of uiFiles) {
      const uiPath = path.join(__dirname, '..', uiFile);
      if (fs.existsSync(uiPath)) {
        this.addCheck(`✅ ${uiFile} exists`);
        
        const content = fs.readFileSync(uiPath, 'utf8');
        this.checkUIContent(uiFile, content);
      } else {
        this.addError(`❌ Missing UI file: ${uiFile}`);
      }
    }
  }

  checkUIContent(fileName, content) {
    const requiredConsentTypes = [
      'essential',
      'analytics',
      'marketing',
      'dataSharing',
      'locationTracking'
    ];

    for (const consentType of requiredConsentTypes) {
      if (content.includes(consentType)) {
        this.addCheck(`✅ ${fileName} includes ${consentType} consent`);
      } else {
        this.addWarning(`⚠️ ${fileName} missing ${consentType} consent`);
      }
    }

    // Check for consent enforcement integration
    if (content.includes('consentEnforcementService')) {
      this.addCheck(`✅ ${fileName} integrates consent enforcement`);
    } else {
      this.addError(`❌ ${fileName} missing consent enforcement integration`);
    }
  }

  async checkComplianceFeatures() {
    console.log('⚖️ Checking Compliance Features...');
    
    const complianceChecks = [
      {
        name: 'Consent granularity',
        check: () => this.checkConsentGranularity()
      },
      {
        name: 'Audit logging',
        check: () => this.checkAuditLogging()
      },
      {
        name: 'Data export',
        check: () => this.checkDataExport()
      },
      {
        name: 'Consent revocation',
        check: () => this.checkConsentRevocation()
      }
    ];

    for (const complianceCheck of complianceChecks) {
      try {
        const result = complianceCheck.check();
        if (result) {
          this.addCheck(`✅ ${complianceCheck.name} implemented`);
        } else {
          this.addError(`❌ ${complianceCheck.name} not implemented`);
        }
      } catch (error) {
        this.addError(`❌ Error checking ${complianceCheck.name}: ${error.message}`);
      }
    }
  }

  checkConsentGranularity() {
    const privacyServicePath = path.join(__dirname, '..', 'services/privacyManagementService.js');
    if (!fs.existsSync(privacyServicePath)) return false;
    
    const content = fs.readFileSync(privacyServicePath, 'utf8');
    return content.includes('consentTypes') && content.includes('essential') && 
           content.includes('analytics') && content.includes('marketing');
  }

  checkAuditLogging() {
    const privacyServicePath = path.join(__dirname, '..', 'services/privacyManagementService.js');
    if (!fs.existsSync(privacyServicePath)) return false;
    
    const content = fs.readFileSync(privacyServicePath, 'utf8');
    return content.includes('logPrivacyEvent') && content.includes('audit_logs');
  }

  checkDataExport() {
    const privacyServicePath = path.join(__dirname, '..', 'services/privacyManagementService.js');
    if (!fs.existsSync(privacyServicePath)) return false;
    
    const content = fs.readFileSync(privacyServicePath, 'utf8');
    return content.includes('exportUserData');
  }

  checkConsentRevocation() {
    const consentServicePath = path.join(__dirname, '..', 'services/consentEnforcementService.js');
    if (!fs.existsSync(consentServicePath)) return false;
    
    const content = fs.readFileSync(consentServicePath, 'utf8');
    return content.includes('handleConsentRevocation');
  }

  async checkIntegrationPoints() {
    console.log('🔗 Checking Integration Points...');
    
    // Check if analytics service integrates consent
    const analyticsPath = path.join(__dirname, '..', 'services/analyticsService.js');
    if (fs.existsSync(analyticsPath)) {
      const content = fs.readFileSync(analyticsPath, 'utf8');
      if (content.includes('consentEnforcementService')) {
        this.addCheck('✅ Analytics service integrates consent enforcement');
      } else {
        this.addError('❌ Analytics service missing consent integration');
      }
    }

    // Check if App.js initializes consent enforcement
    const appPath = path.join(__dirname, '..', 'App.js');
    if (fs.existsSync(appPath)) {
      const content = fs.readFileSync(appPath, 'utf8');
      if (content.includes('consentEnforcementService')) {
        this.addCheck('✅ App initializes consent enforcement');
      } else {
        this.addError('❌ App missing consent enforcement initialization');
      }
    }
  }

  addCheck(message) {
    this.results.checks.push(message);
    console.log(message);
  }

  addError(message) {
    this.results.errors.push(message);
    console.log(message);
  }

  addWarning(message) {
    this.results.warnings.push(message);
    console.log(message);
  }

  addRecommendation(message) {
    this.results.recommendations.push(message);
  }

  generateReport() {
    console.log('\n📊 VERIFICATION REPORT');
    console.log('='.repeat(50));
    
    const totalChecks = this.results.checks.length;
    const totalErrors = this.results.errors.length;
    const totalWarnings = this.results.warnings.length;
    
    console.log(`✅ Passed Checks: ${totalChecks}`);
    console.log(`❌ Errors: ${totalErrors}`);
    console.log(`⚠️ Warnings: ${totalWarnings}`);
    
    if (totalErrors === 0) {
      this.results.overall = totalWarnings === 0 ? 'EXCELLENT' : 'GOOD';
    } else if (totalErrors <= 2) {
      this.results.overall = 'NEEDS_IMPROVEMENT';
    } else {
      this.results.overall = 'FAILED';
    }
    
    console.log(`\n🎯 Overall Status: ${this.results.overall}`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ CRITICAL ISSUES TO FIX:');
      this.results.errors.forEach(error => console.log(`  ${error}`));
    }
    
    if (this.results.warnings.length > 0) {
      console.log('\n⚠️ WARNINGS:');
      this.results.warnings.forEach(warning => console.log(`  ${warning}`));
    }
    
    // Generate recommendations
    this.generateRecommendations();
    
    if (this.results.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      this.results.recommendations.forEach(rec => console.log(`  ${rec}`));
    }
    
    console.log('\n' + '='.repeat(50));
    
    if (this.results.overall === 'EXCELLENT' || this.results.overall === 'GOOD') {
      console.log('🎉 Data Consent system is production-ready!');
    } else {
      console.log('🔧 Please address the issues above before production deployment.');
    }
  }

  generateRecommendations() {
    if (this.results.errors.length > 0) {
      this.addRecommendation('Fix all critical errors before production deployment');
    }
    
    if (this.results.warnings.length > 0) {
      this.addRecommendation('Address warnings to improve compliance score');
    }
    
    this.addRecommendation('Run database migration scripts in production');
    this.addRecommendation('Test consent enforcement with real user scenarios');
    this.addRecommendation('Verify compliance with local data protection authorities');
  }
}

// Run verification
const verifier = new DataConsentVerifier();
verifier.runVerification().catch(console.error);
