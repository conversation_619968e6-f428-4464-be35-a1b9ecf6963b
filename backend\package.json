{"name": "jiranipay-backend", "version": "1.0.0", "description": "JiraniPay Backend API Server - Production-ready financial services backend for East Africa", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js", "build": "npm run lint && npm test", "docker:build": "docker build -t jiranipay-backend .", "docker:run": "docker run -p 3000:3000 jiranipay-backend", "security-audit": "npm audit --audit-level=high", "setup-production": "node scripts/setup-production-env.js", "validate-production": "node scripts/validate-production-readiness.js", "create-admin": "node scripts/create-admin-user.js", "deploy-production": "bash scripts/deploy-production.sh", "prestart": "node scripts/validate-production-readiness.js"}, "keywords": ["fintech", "mobile-money", "payments", "east-africa", "uganda", "api", "backend"], "author": "JiraniPay Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "@supabase/supabase-js": "^2.50.0", "pg": "^8.11.3", "redis": "^4.6.10", "axios": "^1.6.2", "crypto": "^1.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "africastalking": "^0.6.1", "node-cron": "^3.0.3", "express-async-errors": "^3.1.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "@types/jest": "^29.5.8", "husky": "^8.0.3", "lint-staged": "^15.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/jiranipay/backend.git"}, "bugs": {"url": "https://github.com/jiranipay/backend/issues"}, "homepage": "https://jiranipay.com"}