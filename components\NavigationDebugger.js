import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NavigationUtils from '../utils/navigationUtils';
import { Colors } from '../constants/Colors';

/**
 * Navigation Debugger Component
 * Shows current navigation state and provides testing controls
 * Only visible in development mode
 */
const NavigationDebugger = ({ navigation, visible = __DEV__ }) => {
  const [navState, setNavState] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    if (!visible) return;

    const updateNavState = () => {
      const state = NavigationUtils.getNavigationState();
      setNavState(state);
    };

    // Update immediately
    updateNavState();

    // Update every 2 seconds
    const interval = setInterval(updateNavState, 2000);

    return () => clearInterval(interval);
  }, [visible]);

  if (!visible || !navState) return null;

  const handleTestNavigation = (action) => {
    switch (action) {
      case 'goBack':
        NavigationUtils.safeGoBack(navigation);
        break;
      case 'goHome':
        NavigationUtils.navigateToHome();
        break;
      case 'showExit':
        NavigationUtils.showExitConfirmation();
        break;
      case 'debug':
        NavigationUtils.debugNavigationState();
        break;
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <Text style={styles.headerText}>🧭 Nav Debug</Text>
        <Ionicons 
          name={isExpanded ? 'chevron-up' : 'chevron-down'} 
          size={16} 
          color={Colors.neutral.white} 
        />
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.content}>
          <View style={styles.stateInfo}>
            <Text style={styles.stateText}>Route: {navState.currentRoute}</Text>
            <Text style={styles.stateText}>Can Go Back: {navState.canGoBack ? '✅' : '❌'}</Text>
            <Text style={styles.stateText}>Stack Length: {navState.stackLength}</Text>
            <Text style={styles.stateText}>At Root Home: {navState.isAtRootHome ? '✅' : '❌'}</Text>
            <Text style={styles.stateText}>Is Home Screen: {navState.isHomeScreen ? '✅' : '❌'}</Text>
          </View>

          <View style={styles.controls}>
            <TouchableOpacity 
              style={styles.button}
              onPress={() => handleTestNavigation('goBack')}
            >
              <Text style={styles.buttonText}>Go Back</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.button}
              onPress={() => handleTestNavigation('goHome')}
            >
              <Text style={styles.buttonText}>Go Home</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.button}
              onPress={() => handleTestNavigation('showExit')}
            >
              <Text style={styles.buttonText}>Exit Dialog</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.button}
              onPress={() => handleTestNavigation('debug')}
            >
              <Text style={styles.buttonText}>Log State</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    zIndex: 9999,
    minWidth: 200,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 8,
    backgroundColor: Colors.primary.main,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  headerText: {
    color: Colors.neutral.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  content: {
    padding: 8,
  },
  stateInfo: {
    marginBottom: 8,
  },
  stateText: {
    color: Colors.neutral.white,
    fontSize: 10,
    marginBottom: 2,
  },
  controls: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  button: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginBottom: 4,
  },
  buttonText: {
    color: Colors.neutral.white,
    fontSize: 10,
    fontWeight: '500',
  },
});

export default NavigationDebugger;
