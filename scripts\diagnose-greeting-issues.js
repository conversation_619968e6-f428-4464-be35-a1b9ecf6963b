#!/usr/bin/env node

/**
 * Comprehensive Greeting Issues Diagnostic Script
 * 
 * This script traces the complete data flow from user registration
 * to greeting display to identify where real names are being lost.
 */

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.development.local') });

console.log('🔍 Comprehensive Greeting Issues Diagnostic');
console.log('==========================================\n');

/**
 * Main diagnostic function
 */
async function main() {
  try {
    console.log('🚀 Starting comprehensive greeting diagnostic...\n');

    // Import Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.EXPO_PUBLIC_SUPABASE_URL,
      process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
    );
    
    console.log('✅ Supabase client initialized\n');

    // STEP 1: Check current authenticated user
    console.log('🔍 STEP 1: Checking current authenticated user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('❌ No authenticated user found');
      console.log('Please login first and then run this script again.\n');
      return;
    }

    console.log('✅ Authenticated user found:', {
      id: user.id,
      phone: user.phone,
      email: user.email,
      created_at: user.created_at
    });

    console.log('📋 User metadata:', user.user_metadata);
    console.log('');

    // STEP 2: Check user profile in database
    console.log('🔍 STEP 2: Checking user profile in database...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.log('❌ Profile not found in database:', profileError.message);
      
      // Check if it's in user_profiles table instead
      const { data: userProfile, error: userProfileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
        
      if (userProfileError) {
        console.log('❌ Profile not found in user_profiles table either:', userProfileError.message);
      } else {
        console.log('✅ Found profile in user_profiles table:', userProfile);
      }
    } else {
      console.log('✅ Profile found in profiles table:', profile);
    }
    console.log('');

    // STEP 3: Test name extraction logic
    console.log('🔍 STEP 3: Testing name extraction logic...');
    
    const extractDisplayName = (fullName) => {
      if (!fullName || typeof fullName !== 'string') {
        return null;
      }

      const trimmedName = fullName.trim();
      
      // Handle generated names like "User 9916" - use the full name
      if (trimmedName.match(/^User \d+$/)) {
        console.log('⚠️ Detected auto-generated name, using full name:', trimmedName);
        return trimmedName;
      }

      // Handle other generated patterns
      if (trimmedName.match(/^JiraniPay User$/)) {
        console.log('⚠️ Detected default generated name, using friendly alternative');
        return 'Friend';
      }

      // For real names, use first name only
      const nameParts = trimmedName.split(' ');
      if (nameParts.length > 1) {
        const firstName = nameParts[0];
        if (firstName.length > 1 && /^[A-Za-z]/.test(firstName) && firstName !== 'User') {
          console.log('✅ Using real first name from full name:', firstName);
          return firstName;
        }
      }

      // If it's a single word that looks like a real name, return as-is
      if (trimmedName.length > 1 && /^[A-Za-z]/.test(trimmedName) && trimmedName !== 'User') {
        console.log('✅ Using single real name:', trimmedName);
        return trimmedName;
      }

      console.log('⚠️ Using name as-is:', trimmedName);
      return trimmedName;
    };

    // Test with different name sources
    const nameSources = [
      { source: 'user_metadata.full_name', value: user.user_metadata?.full_name },
      { source: 'user_metadata.name', value: user.user_metadata?.name },
      { source: 'profile.full_name', value: profile?.full_name },
      { source: 'userProfile.full_name', value: profile?.full_name }, // if found in user_profiles
    ];

    nameSources.forEach(({ source, value }) => {
      if (value) {
        console.log(`📝 Testing ${source}: "${value}"`);
        const extracted = extractDisplayName(value);
        console.log(`   Extracted: "${extracted}"`);
      } else {
        console.log(`📝 ${source}: not available`);
      }
    });
    console.log('');

    // STEP 4: Test unified greeting generation
    console.log('🔍 STEP 4: Testing unified greeting generation...');
    
    const getCurrentTimePeriod = () => {
      const hour = new Date().getHours();
      if (hour < 12) return { period: 'morning' };
      if (hour < 18) return { period: 'afternoon' };
      return { period: 'evening' };
    };

    const testUnifiedGreeting = (user, userProfile, greetingType = 'dashboard') => {
      console.log(`🎯 Testing ${greetingType} greeting with:`, {
        hasUser: !!user,
        userId: user?.id,
        hasUserProfile: !!userProfile,
        userProfileName: userProfile?.full_name,
        userMetadata: user?.user_metadata
      });

      let userName = null;
      let isReturningUser = false;

      // Enhanced name extraction with priority order
      if (userProfile?.full_name) {
        userName = extractDisplayName(userProfile.full_name);
        isReturningUser = true;
        console.log('✅ Using name from profile:', userName);
      } else if (user?.user_metadata?.full_name) {
        userName = extractDisplayName(user.user_metadata.full_name);
        isReturningUser = true;
        console.log('✅ Using name from user metadata (full_name):', userName);
      } else if (user?.user_metadata?.name) {
        userName = extractDisplayName(user.user_metadata.name);
        isReturningUser = true;
        console.log('✅ Using name from user metadata (name):', userName);
      } else if (user?.email) {
        const emailName = user.email.split('@')[0];
        if (emailName.length > 2 && /^[a-zA-Z]/.test(emailName)) {
          userName = emailName.charAt(0).toUpperCase() + emailName.slice(1).toLowerCase();
          console.log('✅ Using name from email:', userName);
        }
      }

      // Generate time-based greeting
      const timeOfDay = getCurrentTimePeriod();
      let greeting;

      // Different greeting formats based on type and context
      if (greetingType === 'welcome' && userName && isReturningUser && !user) {
        greeting = `Welcome back, ${userName}!`;
      } else if (userName) {
        greeting = `Good ${timeOfDay.period}, ${userName}!`;
      } else {
        greeting = `Good ${timeOfDay.period}!`;
      }

      console.log(`✅ Generated ${greetingType} greeting:`, greeting);
      return greeting;
    };

    // Test both dashboard and welcome greetings
    const dashboardGreeting = testUnifiedGreeting(user, profile, 'dashboard');
    console.log('');
    const welcomeGreeting = testUnifiedGreeting(user, profile, 'welcome');
    console.log('');

    // STEP 5: Check profile auto-creation service behavior
    console.log('🔍 STEP 5: Testing profile auto-creation logic...');
    
    const testExtractFullName = (user) => {
      console.log('🔍 Testing extractFullName with user data:', {
        hasUserMetadata: !!user.user_metadata,
        fullName: user.user_metadata?.full_name,
        name: user.user_metadata?.name,
        firstName: user.user_metadata?.first_name,
        lastName: user.user_metadata?.last_name,
        email: user.email,
        phone: user.phone
      });

      // Priority 1: Try user_metadata.full_name (from registration)
      if (user.user_metadata?.full_name && user.user_metadata.full_name.trim()) {
        console.log('✅ Would use full_name from user_metadata:', user.user_metadata.full_name);
        return user.user_metadata.full_name.trim();
      }

      // Priority 2: Try user_metadata.name
      if (user.user_metadata?.name && user.user_metadata.name.trim()) {
        console.log('✅ Would use name from user_metadata:', user.user_metadata.name);
        return user.user_metadata.name.trim();
      }

      // Priority 3: Try first_name + last_name combination
      if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
        const fullName = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`.trim();
        console.log('✅ Would use first_name + last_name:', fullName);
        return fullName;
      }

      // Priority 4: Try just first_name
      if (user.user_metadata?.first_name && user.user_metadata.first_name.trim()) {
        console.log('✅ Would use first_name only:', user.user_metadata.first_name);
        return user.user_metadata.first_name.trim();
      }

      // Priority 5: Extract from email (only if it looks like a real name)
      if (user.email) {
        const emailName = user.email.split('@')[0].replace(/[._]/g, ' ').trim();
        if (emailName.length > 2 && /^[a-zA-Z]/.test(emailName) && !/^\d+$/.test(emailName)) {
          console.log('✅ Would use name from email:', emailName);
          return emailName;
        }
      }

      // Priority 6: Phone-based fallback (only as last resort)
      if (user.phone) {
        const phoneName = `User ${user.phone.slice(-4)}`;
        console.log('⚠️ Would use phone-based fallback name:', phoneName);
        return phoneName;
      }
      
      console.log('⚠️ Would use ultimate fallback name: JiraniPay User');
      return 'JiraniPay User';
    };

    const extractedName = testExtractFullName(user);
    console.log('');

    // STEP 6: Summary and recommendations
    console.log('📊 DIAGNOSTIC SUMMARY');
    console.log('=====================');
    console.log(`Current user ID: ${user.id}`);
    console.log(`User metadata full_name: ${user.user_metadata?.full_name || 'NOT SET'}`);
    console.log(`Profile full_name: ${profile?.full_name || 'NOT FOUND'}`);
    console.log(`Auto-creation would generate: ${extractedName}`);
    console.log(`Dashboard greeting: ${dashboardGreeting}`);
    console.log(`Welcome greeting: ${welcomeGreeting}`);
    console.log('');

    // Recommendations
    console.log('🎯 RECOMMENDATIONS');
    console.log('==================');
    
    if (!user.user_metadata?.full_name) {
      console.log('❌ ISSUE: user_metadata.full_name is missing');
      console.log('   This suggests the registration process is not setting user metadata correctly');
      console.log('   Check the authService.registerUserWithPassword function');
    } else {
      console.log('✅ user_metadata.full_name is present');
    }

    if (!profile?.full_name) {
      console.log('❌ ISSUE: Profile full_name is missing from database');
      console.log('   This suggests profile creation is failing or using wrong table');
      console.log('   Check the databaseService.createUserProfile function');
    } else {
      console.log('✅ Profile full_name is present in database');
    }

    if (extractedName.match(/^User \d+$/)) {
      console.log('⚠️ WARNING: Auto-creation would generate phone-based name');
      console.log('   This means real name data is not accessible during profile creation');
    } else {
      console.log('✅ Auto-creation would use real name data');
    }

    console.log('\n🔧 NEXT STEPS:');
    if (!user.user_metadata?.full_name && !profile?.full_name) {
      console.log('1. Check registration flow - ensure user_metadata.full_name is set');
      console.log('2. Check profile creation - ensure full_name is saved to database');
      console.log('3. Run profile name verification service to fix existing users');
    } else if (user.user_metadata?.full_name && !profile?.full_name) {
      console.log('1. Profile creation issue - metadata exists but database profile missing');
      console.log('2. Run profile auto-creation service to create missing profile');
    } else if (!user.user_metadata?.full_name && profile?.full_name) {
      console.log('1. Metadata issue - profile exists but user_metadata missing');
      console.log('2. This is unusual - check how this user was created');
    } else {
      console.log('1. Both metadata and profile exist - greeting should work correctly');
      console.log('2. If greeting still shows auto-generated name, check screen implementations');
    }

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
