import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'react-native';
import * as Haptics from 'expo-haptics';

/**
 * Unified Navigation Utilities for JiraniPay
 * Handles consistent back button behavior across all screens
 */

class NavigationUtils {
  static navigationRef = null;
  static exitDialogVisible = false;

  /**
   * Set the navigation reference for global navigation control
   */
  static setNavigationRef(ref) {
    this.navigationRef = ref;
  }

  /**
   * Check if current screen is the home/dashboard screen AND there's no navigation stack
   */
  static isAtRootHomeScreen() {
    if (!this.navigationRef?.current) return false;

    const currentRoute = this.navigationRef.current.getCurrentRoute();
    const routeName = currentRoute?.name;

    // Check if we're on MainApp (which contains the dashboard) AND can't go back
    const isHomeRoute = routeName === 'MainApp' || routeName === 'Dashboard';
    const canGoBack = this.navigationRef.current.canGoBack();

    return isHomeRoute && !canGoBack;
  }

  /**
   * Check if current screen is any home/dashboard screen (regardless of stack)
   */
  static isHomeScreen() {
    if (!this.navigationRef?.current) return false;

    const currentRoute = this.navigationRef.current.getCurrentRoute();
    const routeName = currentRoute?.name;

    return routeName === 'MainApp' || routeName === 'Dashboard';
  }

  /**
   * Handle unified back button behavior
   * - If at root home screen with no navigation stack: show exit confirmation
   * - If navigation stack exists: navigate back to previous screen
   * - Only fallback to home navigation in edge cases
   */
  static handleBackPress() {
    if (!this.navigationRef?.current) {
      return false;
    }

    // If exit dialog is already visible, don't handle back press
    if (this.exitDialogVisible) {
      return true;
    }

    // Check if we can go back in the navigation stack
    if (this.navigationRef.current.canGoBack()) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      this.navigationRef.current.goBack();
      return true;
    }

    // If we can't go back and we're at the root home screen, show exit confirmation
    if (this.isAtRootHomeScreen()) {
      this.showExitConfirmation();
      return true;
    }

    // Edge case: if we can't go back but we're not on home, navigate to home
    // This should rarely happen in normal navigation flows
    console.log('⚠️ Edge case: No navigation stack and not on home screen');
    this.navigateToHome();
    return true;
  }

  /**
   * Navigate to home/dashboard screen
   */
  static navigateToHome() {
    if (!this.navigationRef?.current) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      // Reset navigation stack to home
      this.navigationRef.current.reset({
        index: 0,
        routes: [{ name: 'MainApp' }],
      });
    } catch (error) {
      console.error('Error navigating to home:', error);
      
      // Fallback: try direct navigation
      try {
        this.navigationRef.current.navigate('MainApp');
      } catch (fallbackError) {
        console.error('Fallback navigation failed:', fallbackError);
      }
    }
  }

  /**
   * Show beautiful exit confirmation dialog with East African design
   */
  static showExitConfirmation() {
    if (this.exitDialogVisible) return;
    
    this.exitDialogVisible = true;
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      '🏠 Exit JiraniPay',
      'Are you sure you want to exit the app?\n\nYour financial data is safe and secure.',
      [
        {
          text: '↩️ Stay',
          style: 'cancel',
          onPress: () => {
            this.exitDialogVisible = false;
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          },
        },
        {
          text: '🚪 Exit App',
          style: 'destructive',
          onPress: () => {
            this.exitDialogVisible = false;
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            
            // Small delay for haptic feedback
            setTimeout(() => {
              BackHandler.exitApp();
            }, 200);
          },
        },
      ],
      {
        cancelable: true,
        onDismiss: () => {
          this.exitDialogVisible = false;
        },
      }
    );
  }

  /**
   * Safe back navigation for screen components
   * Prioritizes navigation stack over jumping to home
   */
  static safeGoBack(navigation) {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // First priority: Use the navigation object's goBack if available
      if (navigation?.goBack) {
        // Check if we can go back in the stack
        if (navigation.canGoBack && navigation.canGoBack()) {
          console.log('📱 Navigating back in stack');
          navigation.goBack();
          return;
        } else if (navigation.canGoBack === undefined) {
          // If canGoBack is not available, try goBack anyway (some navigation objects don't have canGoBack)
          console.log('📱 Attempting navigation.goBack() without canGoBack check');
          navigation.goBack();
          return;
        }
      }

      // Second priority: Use global navigation reference
      if (this.navigationRef?.current?.canGoBack()) {
        console.log('📱 Using global navigation reference to go back');
        this.navigationRef.current.goBack();
        return;
      }

      // Third priority: Check if we're at root home screen
      if (this.isAtRootHomeScreen()) {
        console.log('📱 At root home screen, showing exit confirmation');
        this.showExitConfirmation();
        return;
      }

      // Last resort: Navigate to home (should be rare)
      console.log('⚠️ Last resort: Navigating to home');
      this.navigateToHome();

    } catch (error) {
      console.error('Error in safe go back:', error);

      // Error fallback: try global navigation
      try {
        if (this.navigationRef?.current?.canGoBack()) {
          this.navigationRef.current.goBack();
        } else {
          this.navigateToHome();
        }
      } catch (fallbackError) {
        console.error('Fallback navigation also failed:', fallbackError);
        this.navigateToHome();
      }
    }
  }

  /**
   * Initialize hardware back button handling
   * Call this in App.js or main navigation component
   */
  static initializeBackHandler() {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      this.handleBackPress.bind(this)
    );

    return backHandler;
  }

  /**
   * Get current route information for debugging
   */
  static getCurrentRoute() {
    if (!this.navigationRef?.current) return null;

    try {
      return this.navigationRef.current.getCurrentRoute();
    } catch (error) {
      console.error('Error getting current route:', error);
      return null;
    }
  }

  /**
   * Get navigation state for debugging
   */
  static getNavigationState() {
    if (!this.navigationRef?.current) return null;

    try {
      const state = this.navigationRef.current.getState();
      const currentRoute = this.navigationRef.current.getCurrentRoute();
      const canGoBack = this.navigationRef.current.canGoBack();

      return {
        currentRoute: currentRoute?.name,
        canGoBack,
        stackLength: state?.routes?.length || 0,
        isAtRootHome: this.isAtRootHomeScreen(),
        isHomeScreen: this.isHomeScreen(),
      };
    } catch (error) {
      console.error('Error getting navigation state:', error);
      return null;
    }
  }

  /**
   * Debug navigation state (useful for testing)
   */
  static debugNavigationState() {
    const state = this.getNavigationState();
    console.log('🧭 Navigation State:', state);
    return state;
  }

  /**
   * Check if navigation is ready
   */
  static isNavigationReady() {
    return this.navigationRef?.current != null;
  }
}

export default NavigationUtils;
