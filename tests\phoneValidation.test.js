import {
  cleanPhoneNumber,
  formatPhoneNumber,
  detectNetworkProvider,
  validatePhoneNumber,
  isUgandaNumber,
  getDisplayFormat,
  isSamePhoneNumber,
  getNetworkColor
} from '../utils/phoneValidation';

describe('Phone Validation Utilities', () => {
  describe('cleanPhoneNumber', () => {
    it('should remove all non-digit characters', () => {
      expect(cleanPhoneNumber('+256 777 123 456')).toBe('256777123456');
      expect(cleanPhoneNumber('(256) 777-123-456')).toBe('256777123456');
      expect(cleanPhoneNumber('256.777.123.456')).toBe('256777123456');
    });

    it('should handle empty or null input', () => {
      expect(cleanPhoneNumber('')).toBe('');
      expect(cleanPhoneNumber(null)).toBe('');
      expect(cleanPhoneNumber(undefined)).toBe('');
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format Uganda numbers correctly', () => {
      expect(formatPhoneNumber('777123456')).toBe('+256777123456');
      expect(formatPhoneNumber('**********')).toBe('+256777123456');
      expect(formatPhoneNumber('256777123456')).toBe('+256777123456');
      expect(formatPhoneNumber('+256777123456')).toBe('+256777123456');
    });

    it('should handle different country codes', () => {
      expect(formatPhoneNumber('712345678', 'KE')).toBe('+254712345678');
      expect(formatPhoneNumber('712345678', 'TZ')).toBe('+255712345678');
    });

    it('should return original for invalid length', () => {
      expect(formatPhoneNumber('123')).toBe('123');
      expect(formatPhoneNumber('12345678901234567890')).toBe('12345678901234567890');
    });
  });

  describe('detectNetworkProvider', () => {
    it('should detect MTN Uganda correctly', () => {
      const provider = detectNetworkProvider('777123456');
      expect(provider.provider).toBe('MTN');
      expect(provider.name).toBe('MTN Uganda');
      expect(provider.color).toBe('#FFCC00');
    });

    it('should detect Airtel Uganda correctly', () => {
      const provider = detectNetworkProvider('701234567');
      expect(provider.provider).toBe('Airtel');
      expect(provider.name).toBe('Airtel Uganda');
      expect(provider.color).toBe('#FF0000');
    });

    it('should detect UTL correctly', () => {
      const provider = detectNetworkProvider('711234567');
      expect(provider.provider).toBe('UTL');
      expect(provider.name).toBe('Uganda Telecom');
      expect(provider.color).toBe('#0066CC');
    });

    it('should handle unknown providers', () => {
      const provider = detectNetworkProvider('999123456');
      expect(provider.provider).toBe('Unknown');
      expect(provider.name).toBe('Unknown Provider');
      expect(provider.color).toBe('#666666');
    });

    it('should work with formatted numbers', () => {
      const provider = detectNetworkProvider('+256777123456');
      expect(provider.provider).toBe('MTN');
    });
  });

  describe('validatePhoneNumber', () => {
    it('should validate correct Uganda numbers', () => {
      const result = validatePhoneNumber('777123456');
      expect(result.isValid).toBe(true);
      expect(result.formatted).toBe('+256777123456');
      expect(result.networkProvider.provider).toBe('MTN');
      expect(result.errors).toHaveLength(0);
    });

    it('should validate numbers with country code', () => {
      const result = validatePhoneNumber('+256777123456');
      expect(result.isValid).toBe(true);
      expect(result.formatted).toBe('+256777123456');
    });

    it('should validate numbers with leading zero', () => {
      const result = validatePhoneNumber('**********');
      expect(result.isValid).toBe(true);
      expect(result.formatted).toBe('+256777123456');
    });

    it('should reject empty phone numbers', () => {
      const result = validatePhoneNumber('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is required');
    });

    it('should reject too short numbers', () => {
      const result = validatePhoneNumber('123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is too short');
    });

    it('should reject too long numbers', () => {
      const result = validatePhoneNumber('12345678901234567890');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Phone number is too long');
    });

    it('should reject invalid length for Uganda', () => {
      const result = validatePhoneNumber('77712345'); // 8 digits instead of 9
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid phone number length for Uganda');
    });

    it('should still validate unknown providers', () => {
      const result = validatePhoneNumber('999123456');
      expect(result.isValid).toBe(true);
      expect(result.networkProvider.provider).toBe('Unknown');
    });
  });

  describe('isUgandaNumber', () => {
    it('should identify Uganda numbers correctly', () => {
      expect(isUgandaNumber('256777123456')).toBe(true);
      expect(isUgandaNumber('**********')).toBe(true);
      expect(isUgandaNumber('777123456')).toBe(true);
    });

    it('should reject non-Uganda numbers', () => {
      expect(isUgandaNumber('254712345678')).toBe(false);
      expect(isUgandaNumber('255712345678')).toBe(false);
    });
  });

  describe('getDisplayFormat', () => {
    it('should return international format by default', () => {
      const display = getDisplayFormat('777123456');
      expect(display).toBe('+256777123456');
    });

    it('should return local format when requested', () => {
      const display = getDisplayFormat('777123456', false);
      expect(display).toBe('**********');
    });

    it('should return original for invalid numbers', () => {
      const display = getDisplayFormat('invalid');
      expect(display).toBe('invalid');
    });
  });

  describe('isSamePhoneNumber', () => {
    it('should identify same numbers in different formats', () => {
      expect(isSamePhoneNumber('777123456', '+256777123456')).toBe(true);
      expect(isSamePhoneNumber('**********', '256777123456')).toBe(true);
      expect(isSamePhoneNumber('+256777123456', '+256777123456')).toBe(true);
    });

    it('should identify different numbers', () => {
      expect(isSamePhoneNumber('777123456', '701234567')).toBe(false);
      expect(isSamePhoneNumber('777123456', '254777123456')).toBe(false);
    });
  });

  describe('getNetworkColor', () => {
    it('should return correct colors for providers', () => {
      expect(getNetworkColor('777123456')).toBe('#FFCC00'); // MTN
      expect(getNetworkColor('701234567')).toBe('#FF0000'); // Airtel
      expect(getNetworkColor('711234567')).toBe('#0066CC'); // UTL
      expect(getNetworkColor('999123456')).toBe('#666666'); // Unknown
    });
  });

  describe('Edge Cases', () => {
    it('should handle malformed input gracefully', () => {
      expect(validatePhoneNumber(null).isValid).toBe(false);
      expect(validatePhoneNumber(undefined).isValid).toBe(false);
      expect(cleanPhoneNumber('abc')).toBe('');
    });

    it('should handle international numbers', () => {
      const result = validatePhoneNumber('+1234567890123');
      // Should not crash, but may not be valid for Uganda
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
    });

    it('should handle numbers with spaces and special characters', () => {
      const result = validatePhoneNumber('+256 (777) 123-456');
      expect(result.isValid).toBe(true);
      expect(result.formatted).toBe('+256777123456');
    });
  });
});
