import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions,
  StatusBar,
  SafeAreaView,
  RefreshControl,
  Alert,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import billerManagementService from '../services/billerManagementService';
import { formatCurrency } from '../utils/currencyUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';


const { width } = Dimensions.get('window');

const BillPaymentScreen = ({ navigation, route }) => {
  // Enhanced state management
  const [searchQuery, setSearchQuery] = useState('');
  const [showAllCategories, setShowAllCategories] = useState(false);
  const [categories, setCategories] = useState([]);
  const [popularBillers, setPopularBillers] = useState([]);
  const [recentBillers, setRecentBillers] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searching, setSearching] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  // Use theme and language contexts
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  useEffect(() => {
    loadBillPaymentData();
  }, []);

  useEffect(() => {
    // Handle auto-selection from quick actions
    const { autoSelectCategory, autoSelectService } = route?.params || {};

    if (autoSelectCategory && autoSelectService) {
      console.log(`🎯 Auto-selecting category: ${autoSelectCategory}, service: ${autoSelectService}`);
      handleAutoSelection(autoSelectCategory, autoSelectService);
    }
  }, [route?.params]);

  useEffect(() => {
    if (searchQuery.length > 2) {
      searchBillers();
    } else {
      setSearchResults([]);
      setSearching(false);
    }
  }, [searchQuery]);

  const loadBillPaymentData = async () => {
    try {
      setLoading(true);

      // Get current user ID
      const userId = await getCurrentUserId();

      // Load categories and popular billers (always available)
      const [categoriesResult, popularResult] = await Promise.all([
        billerManagementService.getBillCategories(),
        billerManagementService.getPopularBillers(8)
      ]);

      if (categoriesResult.success) {
        setCategories(categoriesResult.categories);
      }

      if (popularResult.success) {
        setPopularBillers(popularResult.billers);
      }

      // Load recent billers only if user is authenticated
      if (userId) {
        try {
          const recentResult = await billerManagementService.getRecentBillers(userId, 5);
          if (recentResult.success) {
            setRecentBillers(recentResult.billers);
          }
        } catch (recentError) {
          console.log('Could not load recent billers:', recentError.message);
          setRecentBillers([]);
        }
      } else {
        console.log('User not authenticated, skipping recent billers');
        setRecentBillers([]);
      }

    } catch (error) {
      console.error('❌ Error loading bill payment data:', error);
      Alert.alert('Error', 'Failed to load bill payment options. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBillPaymentData();
    setRefreshing(false);
  };

  const searchBillers = async () => {
    try {
      setSearching(true);
      const result = await billerManagementService.searchBillers(searchQuery, {
        limit: 10,
        categoryId: selectedCategory?.id
      });

      if (result.success) {
        setSearchResults(result.billers);
      }
    } catch (error) {
      console.error('❌ Error searching billers:', error);
    } finally {
      setSearching(false);
    }
  };

  const handleAutoSelection = (autoSelectCategory, autoSelectService) => {
    // For mobile category (airtime/data), navigate directly to providers
    if (autoSelectCategory === 'mobile') {
      const airtimeCategory = categories.find(cat => cat.name === 'telecommunications');
      if (airtimeCategory) {
        setTimeout(() => {
          navigation.navigate('BillerSelection', {
            categoryId: airtimeCategory.id,
            categoryName: airtimeCategory.displayName,
            autoSelectService: autoSelectService
          });
        }, 100);
      }
    }
  };

  // Function to ensure all providers have searchKeywords
  const ensureSearchKeywords = (categories) => {
    return categories.map(category => ({
      ...category,
      providers: category.providers.map(provider => ({
        ...provider,
        searchKeywords: provider.searchKeywords || [
          provider.name?.toLowerCase().split(' ').filter(word => word.length > 2) || [],
          provider.type?.toLowerCase() || '',
          provider.id?.toLowerCase() || ''
        ].flat().filter(Boolean)
      }))
    }));
  };

  // Modern Bill Categories with enhanced design
  const rawBillCategories = [
    {
      id: 'utilities',
      title: t('bills.electricity'),
      subtitle: t('bills.utilities'),
      icon: 'flash',
      gradient: ['#FF6B6B', '#FF8E8E'],
      color: '#FF6B6B',
      searchKeywords: ['electricity', 'power', 'umeme', 'bills'],
      providers: [
        { id: 'umeme', name: 'UMEME', type: 'electricity', icon: 'flash', color: '#FF6B6B' },
        { id: 'uedcl', name: 'UEDCL', type: 'electricity', icon: 'flash', color: '#FF6B6B' },
      ]
    },
    {
      id: 'airtime',
      title: t('bills.airtime'),
      subtitle: t('bills.mobile'),
      icon: 'phone-portrait',
      gradient: ['#4ECDC4', '#44A08D'],
      color: '#4ECDC4',
      searchKeywords: ['airtime', 'data', 'mobile', 'mtn', 'airtel', 'utl', 'phone', 'bundles'],
      providers: [
        { id: 'mtn', name: 'MTN Uganda', type: 'mobile', icon: 'phone-portrait', color: '#FFCC02' },
        { id: 'airtel', name: 'Airtel Uganda', type: 'mobile', icon: 'phone-portrait', color: '#FF0000' },
        { id: 'utl', name: 'UTL', type: 'mobile', icon: 'phone-portrait', color: '#0066CC' },
      ]
    },
    {
      id: 'financial',
      title: t('bills.financial'),
      subtitle: t('bills.bankingFinance'),
      icon: 'card',
      gradient: ['#667eea', '#764ba2'],
      color: '#667eea',
      searchKeywords: ['bank', 'finance', 'loan', 'credit', 'equity', 'dfcu', 'standard', 'absa', 'housing', 'postbank', 'kcb', 'boa', 'finca', 'pride', 'ugafode', 'momo', 'airtel money'],
      providers: [
        // Major Commercial Banks
        { id: 'equity', name: 'Equity Bank Uganda', type: 'bank', icon: 'card', color: '#E31E24', searchKeywords: ['equity', 'bank', 'uganda'] },
        { id: 'stanbic', name: 'Stanbic Bank Uganda', type: 'bank', icon: 'card', color: '#0066CC', searchKeywords: ['stanbic', 'bank', 'uganda'] },
        { id: 'dfcu', name: 'DFCU Bank', type: 'bank', icon: 'card', color: '#1B5E20', searchKeywords: ['dfcu', 'bank'] },
        { id: 'standard_chartered', name: 'Standard Chartered Bank', type: 'bank', icon: 'card', color: '#0F4C81', searchKeywords: ['standard', 'chartered', 'bank'] },
        { id: 'absa', name: 'Absa Bank Uganda', type: 'bank', icon: 'card', color: '#FF0000', searchKeywords: ['absa', 'bank', 'uganda'] },
        { id: 'centenary', name: 'Centenary Bank', type: 'bank', icon: 'card', color: '#8B4513', searchKeywords: ['centenary', 'bank'] },
        { id: 'housing_finance', name: 'Housing Finance Bank', type: 'bank', icon: 'card', color: '#FF6600', searchKeywords: ['housing', 'finance', 'bank'] },
        { id: 'postbank', name: 'PostBank Uganda', type: 'bank', icon: 'card', color: '#FFD700', searchKeywords: ['postbank', 'post', 'bank', 'uganda'] },
        { id: 'kcb', name: 'KCB Bank Uganda', type: 'bank', icon: 'card', color: '#0066CC', searchKeywords: ['kcb', 'bank', 'uganda'] },
        { id: 'boa', name: 'Bank of Africa Uganda', type: 'bank', icon: 'card', color: '#FF6600', searchKeywords: ['boa', 'bank', 'africa', 'uganda'] },

        // Microfinance Institutions
        { id: 'finca', name: 'FINCA Uganda', type: 'microfinance', icon: 'wallet', color: '#228B22', searchKeywords: ['finca', 'microfinance', 'uganda'] },
        { id: 'pride', name: 'Pride Microfinance', type: 'microfinance', icon: 'wallet', color: '#4169E1', searchKeywords: ['pride', 'microfinance'] },
        { id: 'ugafode', name: 'UGAFODE Microfinance', type: 'microfinance', icon: 'wallet', color: '#FF4500', searchKeywords: ['ugafode', 'microfinance'] },

        // Mobile Money Services
        { id: 'mtn_momo', name: 'MTN Mobile Money', type: 'mobile_money', icon: 'phone-portrait', color: '#FFCC02', searchKeywords: ['mtn', 'mobile', 'money', 'momo'] },
        { id: 'airtel_money', name: 'Airtel Money', type: 'mobile_money', icon: 'phone-portrait', color: '#FF0000', searchKeywords: ['airtel', 'money', 'mobile'] },
      ]
    },
    {
      id: 'postpaid',
      title: t('bills.postpaid'),
      subtitle: t('bills.monthlySubscriptions'),
      icon: 'phone-portrait',
      gradient: ['#f093fb', '#f5576c'],
      color: '#f093fb',
      searchKeywords: ['postpaid', 'monthly', 'subscription'],
      providers: [
        { id: 'mtn_postpaid', name: 'MTN Postpaid', type: 'postpaid', icon: 'phone-portrait', color: '#FFCC02' },
        { id: 'airtel_postpaid', name: 'Airtel Postpaid', type: 'postpaid', icon: 'phone-portrait', color: '#FF0000' },
      ]
    },
    {
      id: 'solar',
      title: t('bills.solar'),
      subtitle: t('bills.solarEnergyPayments'),
      icon: 'sunny',
      gradient: ['#ffecd2', '#fcb69f'],
      color: '#ffecd2',
      searchKeywords: ['solar', 'energy', 'sun', 'fenix', 'bboxx', 'solarnow', 'azuri', 'dlight', 'greenlight', 'suntransfer'],
      providers: [
        { id: 'fenix', name: 'Fenix Solar', type: 'solar', icon: 'sunny', color: '#FF9500', searchKeywords: ['fenix', 'solar', 'energy'] },
        { id: 'bboxx', name: 'BBOXX Solar', type: 'solar', icon: 'sunny', color: '#00A651', searchKeywords: ['bboxx', 'solar', 'energy'] },
        { id: 'solarnow', name: 'SolarNow', type: 'solar', icon: 'sunny', color: '#FFD700', searchKeywords: ['solarnow', 'solar', 'energy'] },
        { id: 'azuri', name: 'Azuri Technologies', type: 'solar', icon: 'sunny', color: '#4169E1', searchKeywords: ['azuri', 'technologies', 'solar'] },
        { id: 'dlight', name: 'd.light', type: 'solar', icon: 'sunny', color: '#FF6347', searchKeywords: ['dlight', 'solar', 'light'] },
        { id: 'greenlight', name: 'Greenlight Planet', type: 'solar', icon: 'sunny', color: '#32CD32', searchKeywords: ['greenlight', 'planet', 'solar'] },
        { id: 'suntransfer', name: 'SunTransfer Energy', type: 'solar', icon: 'sunny', color: '#FFA500', searchKeywords: ['suntransfer', 'energy', 'solar'] },
      ]
    },
    {
      id: 'paytv',
      title: t('bills.tv'),
      subtitle: t('bills.tvSubscriptions'),
      icon: 'tv',
      gradient: ['#a8edea', '#fed6e3'],
      color: '#a8edea',
      searchKeywords: ['tv', 'television', 'dstv', 'gotv', 'startimes', 'azam', 'multichoice', 'canal'],
      providers: [
        { id: 'dstv', name: 'DStv', type: 'tv', icon: 'tv', color: '#0066CC', searchKeywords: ['dstv', 'tv', 'television', 'satellite'] },
        { id: 'gotv', name: 'GOtv', type: 'tv', icon: 'tv', color: '#FF6600', searchKeywords: ['gotv', 'tv', 'television'] },
        { id: 'startimes', name: 'StarTimes', type: 'tv', icon: 'tv', color: '#FF0000', searchKeywords: ['startimes', 'star', 'times', 'tv'] },
        { id: 'azam_tv', name: 'Azam TV', type: 'tv', icon: 'tv', color: '#00A651', searchKeywords: ['azam', 'tv', 'television'] },
        { id: 'multichoice', name: 'MultiChoice', type: 'tv', icon: 'tv', color: '#0066CC', searchKeywords: ['multichoice', 'multi', 'choice', 'tv'] },
        { id: 'canal_plus', name: 'Canal+', type: 'tv', icon: 'tv', color: '#000000', searchKeywords: ['canal', 'plus', 'tv', 'television'] },
      ]
    },
    {
      id: 'government',
      title: t('bills.governmentServices'),
      subtitle: t('bills.officialPayments'),
      icon: 'shield',
      gradient: ['#667eea', '#764ba2'],
      color: '#667eea',
      searchKeywords: ['government', 'tax', 'ura', 'license', 'passport', 'driving', 'business', 'land', 'court', 'immigration', 'kcca'],
      providers: [
        // Tax Services
        { id: 'ura_tax', name: 'URA Tax Payments', type: 'tax', icon: 'shield', color: '#667eea', searchKeywords: ['ura', 'tax', 'payments', 'revenue'] },
        { id: 'ura_vat', name: 'URA VAT Returns', type: 'tax', icon: 'shield', color: '#667eea', searchKeywords: ['ura', 'vat', 'returns', 'tax'] },
        { id: 'ura_paye', name: 'URA PAYE', type: 'tax', icon: 'shield', color: '#667eea', searchKeywords: ['ura', 'paye', 'tax', 'salary'] },

        // Municipal Services
        { id: 'kcca_services', name: 'KCCA Services', type: 'municipal', icon: 'business', color: '#0066CC', searchKeywords: ['kcca', 'kampala', 'municipal', 'services'] },
        { id: 'kcca_license', name: 'KCCA Business License', type: 'license', icon: 'document', color: '#0066CC', searchKeywords: ['kcca', 'business', 'license', 'kampala'] },
        { id: 'kcca_property', name: 'KCCA Property Tax', type: 'tax', icon: 'home', color: '#0066CC', searchKeywords: ['kcca', 'property', 'tax', 'kampala'] },

        // Immigration & Travel
        { id: 'passport_fees', name: 'Passport Fees', type: 'immigration', icon: 'card', color: '#228B22', searchKeywords: ['passport', 'fees', 'immigration', 'travel'] },
        { id: 'visa_fees', name: 'Visa Application Fees', type: 'immigration', icon: 'card', color: '#228B22', searchKeywords: ['visa', 'application', 'fees', 'immigration'] },
        { id: 'immigration_services', name: 'Immigration Services', type: 'immigration', icon: 'airplane', color: '#228B22', searchKeywords: ['immigration', 'services', 'travel'] },

        // Licensing & Permits
        { id: 'driving_permit', name: 'Driving Permit Fees', type: 'license', icon: 'car', color: '#FF6600', searchKeywords: ['driving', 'permit', 'license', 'car'] },
        { id: 'business_license', name: 'Business License Payments', type: 'license', icon: 'briefcase', color: '#FF6600', searchKeywords: ['business', 'license', 'permit'] },
        { id: 'trade_license', name: 'Trade License Fees', type: 'license', icon: 'storefront', color: '#FF6600', searchKeywords: ['trade', 'license', 'business'] },

        // Land & Property
        { id: 'land_title', name: 'Land Title Fees', type: 'property', icon: 'map', color: '#8B4513', searchKeywords: ['land', 'title', 'property', 'fees'] },
        { id: 'land_registration', name: 'Land Registration', type: 'property', icon: 'document-text', color: '#8B4513', searchKeywords: ['land', 'registration', 'property'] },

        // Court & Legal
        { id: 'court_fees', name: 'Court Fees', type: 'legal', icon: 'library', color: '#4B0082', searchKeywords: ['court', 'fees', 'legal'] },
        { id: 'legal_services', name: 'Legal Service Fees', type: 'legal', icon: 'scale', color: '#4B0082', searchKeywords: ['legal', 'services', 'fees'] },
      ]
    },
    {
      id: 'education',
      title: 'School Fees',
      subtitle: 'Education payments',
      icon: 'school',
      gradient: ['#667eea', '#764ba2'],
      color: '#667eea',
      searchKeywords: ['school', 'education', 'fees', 'university', 'college', 'makerere', 'mubs', 'kyambogo', 'gulu', 'mbarara', 'uneb', 'uace', 'uce', 'ple'],
      providers: [
        // Public Universities
        { id: 'makerere', name: 'Makerere University', type: 'university', icon: 'school', color: '#8B0000', searchKeywords: ['makerere', 'university', 'mak'] },
        { id: 'kyambogo', name: 'Kyambogo University', type: 'university', icon: 'school', color: '#0066CC', searchKeywords: ['kyambogo', 'university'] },
        { id: 'mbarara', name: 'Mbarara University of Science & Technology', type: 'university', icon: 'school', color: '#228B22', searchKeywords: ['mbarara', 'university', 'must', 'science', 'technology'] },
        { id: 'gulu', name: 'Gulu University', type: 'university', icon: 'school', color: '#FF6600', searchKeywords: ['gulu', 'university'] },
        { id: 'busitema', name: 'Busitema University', type: 'university', icon: 'school', color: '#4169E1', searchKeywords: ['busitema', 'university'] },

        // Private Universities
        { id: 'uganda_christian', name: 'Uganda Christian University', type: 'university', icon: 'school', color: '#800080' },
        { id: 'kampala_university', name: 'Kampala University', type: 'university', icon: 'school', color: '#FF4500' },
        { id: 'nkumba', name: 'Nkumba University', type: 'university', icon: 'school', color: '#32CD32' },
        { id: 'uganda_martyrs', name: 'Uganda Martyrs University', type: 'university', icon: 'school', color: '#8B4513' },
        { id: 'cavendish', name: 'Cavendish University', type: 'university', icon: 'school', color: '#FF1493' },

        // Business Schools & Colleges
        { id: 'mubs', name: 'Makerere University Business School', type: 'business_school', icon: 'briefcase', color: '#8B0000', searchKeywords: ['mubs', 'makerere', 'business', 'school'] },
        { id: 'isbat', name: 'ISBAT University', type: 'university', icon: 'school', color: '#4682B4', searchKeywords: ['isbat', 'university'] },
        { id: 'team_university', name: 'TEAM University', type: 'university', icon: 'school', color: '#FF6347', searchKeywords: ['team', 'university'] },

        // Technical & Vocational Institutions
        { id: 'nakawa_vocational', name: 'Nakawa Vocational Institute', type: 'vocational', icon: 'construct', color: '#2E8B57' },
        { id: 'uganda_technical', name: 'Uganda Technical College', type: 'technical', icon: 'construct', color: '#B8860B' },
        { id: 'kampala_technical', name: 'Kampala Technical Institute', type: 'technical', icon: 'construct', color: '#CD853F' },

        // Teacher Training Colleges
        { id: 'kyambogo_ttc', name: 'Kyambogo Teacher Training College', type: 'teacher_training', icon: 'people', color: '#0066CC' },
        { id: 'mubende_ttc', name: 'Mubende Core PTC', type: 'teacher_training', icon: 'people', color: '#9932CC' },

        // Medical Schools
        { id: 'mbarara_medical', name: 'Mbarara Medical School', type: 'medical', icon: 'medical', color: '#DC143C' },
        { id: 'kampala_medical', name: 'Kampala International Medical School', type: 'medical', icon: 'medical', color: '#FF0000' },

        // Secondary Schools (Major Boarding Schools)
        { id: 'kings_college_budo', name: 'King\'s College Budo', type: 'secondary', icon: 'school', color: '#000080' },
        { id: 'gayaza_high', name: 'Gayaza High School', type: 'secondary', icon: 'school', color: '#8B008B' },
        { id: 'namagunga', name: 'Namagunga Boarding School', type: 'secondary', icon: 'school', color: '#FF69B4' },
        { id: 'ntare_school', name: 'Ntare School', type: 'secondary', icon: 'school', color: '#4169E1' },
        { id: 'mengo_senior', name: 'Mengo Senior School', type: 'secondary', icon: 'school', color: '#8B4513' },

        // Examination Bodies
        { id: 'uneb', name: 'UNEB Examination Fees', type: 'examination', icon: 'document-text', color: '#FF6600', searchKeywords: ['uneb', 'examination', 'fees'] },
        { id: 'uace_fees', name: 'UACE Registration Fees', type: 'examination', icon: 'document-text', color: '#FF6600', searchKeywords: ['uace', 'registration', 'fees', 'advanced'] },
        { id: 'uce_fees', name: 'UCE Registration Fees', type: 'examination', icon: 'document-text', color: '#FF6600', searchKeywords: ['uce', 'registration', 'fees', 'ordinary'] },
        { id: 'ple_fees', name: 'PLE Registration Fees', type: 'examination', icon: 'document-text', color: '#FF6600', searchKeywords: ['ple', 'registration', 'fees', 'primary'] },

        // International Schools
        { id: 'kampala_international', name: 'Kampala International School', type: 'international', icon: 'globe', color: '#4682B4' },
        { id: 'lincoln_international', name: 'Lincoln International School', type: 'international', icon: 'globe', color: '#228B22' },
        { id: 'heritage_international', name: 'Heritage International School', type: 'international', icon: 'globe', color: '#FF4500' },

        // Nursery & Primary Schools
        { id: 'rainbow_international', name: 'Rainbow International School', type: 'primary', icon: 'happy', color: '#FF1493' },
        { id: 'greenhill_academy', name: 'Greenhill Academy', type: 'primary', icon: 'happy', color: '#32CD32' },
        { id: 'galaxy_international', name: 'Galaxy International School', type: 'primary', icon: 'happy', color: '#4169E1' },
      ]
    },
  ];

  // Apply searchKeywords to all categories and providers
  const billCategories = ensureSearchKeywords(rawBillCategories);

  const handleCategorySelect = (category) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('BillProviders', { category });
  };



  // Get categories to display (limited or all)
  const getCategoriesToDisplay = () => {
    const filteredCategories = getFilteredCategories();

    // If searching, show all matching results
    if (searchQuery.trim()) {
      return filteredCategories;
    }

    // If "VIEW ALL" is toggled, show all categories
    if (showAllCategories) {
      return filteredCategories;
    }

    // Otherwise, show first 6 categories for better UX
    return filteredCategories.slice(0, 6);
  };

  // Handle VIEW ALL button press
  const handleViewAllPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setShowAllCategories(!showAllCategories);
  };

  // Filter categories and providers based on search with error handling
  const getFilteredCategories = () => {
    try {
      if (!searchQuery.trim()) return billCategories;

      const query = searchQuery.toLowerCase();
      return billCategories.filter(category => {
        // Check category keywords safely
        const categoryMatch = category.searchKeywords && Array.isArray(category.searchKeywords)
          ? category.searchKeywords.some(keyword =>
              keyword && keyword.toLowerCase().includes(query)
            )
          : false;

        // Check category title and subtitle
        const titleMatch = category.title && category.title.toLowerCase().includes(query);
        const subtitleMatch = category.subtitle && category.subtitle.toLowerCase().includes(query);

        // Check provider keywords and names safely
        const providerMatch = category.providers && Array.isArray(category.providers)
          ? category.providers.some(provider => {
              const nameMatch = provider.name && provider.name.toLowerCase().includes(query);
              const keywordMatch = provider.searchKeywords && Array.isArray(provider.searchKeywords)
                ? provider.searchKeywords.some(keyword =>
                    keyword && keyword.toLowerCase().includes(query)
                  )
                : false;
              // Also check provider type and id as fallback
              const typeMatch = provider.type && provider.type.toLowerCase().includes(query);
              const idMatch = provider.id && provider.id.toLowerCase().includes(query);
              return nameMatch || keywordMatch || typeMatch || idMatch;
            })
          : false;

        return categoryMatch || titleMatch || subtitleMatch || providerMatch;
      });
    } catch (error) {
      console.error('❌ Error filtering categories:', error);
      return billCategories; // Return all categories on error
    }
  };





  // Modern category card render function
  const renderModernCategoryCard = (category) => (
    <TouchableOpacity
      key={category.id}
      style={styles.modernCategoryCard}
      onPress={() => handleCategorySelect(category)}
      activeOpacity={0.7}
    >
      <View style={[styles.modernCategoryIcon, { backgroundColor: category.color + '15' }]}>
        <Ionicons name={category.icon} size={24} color={category.color} />
      </View>
      <Text style={styles.modernCategoryTitle}>{category.title}</Text>
    </TouchableOpacity>
  );



  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme.statusBar === 'dark' ? 'dark-content' : 'light-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Modern Header with JiraniPay Orange */}
      <LinearGradient
        colors={['#E67E22', '#D35400']}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton
            navigation={navigation}
            style={styles.modernBackButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.modernHeaderTitle}>{t('bills.payBill')}</Text>
          <View style={styles.headerSpacer} />
        </View>
      </LinearGradient>

      <View style={styles.modernContent}>
        {/* Search Section */}
        <View style={styles.searchSection}>
          <View style={styles.modernSearchContainer}>
            <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
            <TextInput
              style={styles.modernSearchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Enter Paybill name"
              placeholderTextColor="#9CA3AF"
            />
          </View>
        </View>

        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Categories Section */}
          <View style={styles.categoriesSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.modernSectionTitle}>{t('bills.categories')}</Text>
              <TouchableOpacity
                onPress={handleViewAllPress}
                activeOpacity={0.7}
              >
                <Text style={styles.viewAllText}>
                  {showAllCategories ? t('bills.showLess') : t('bills.viewAll')}
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.modernCategoriesGrid}>
              {getCategoriesToDisplay().map(renderModernCategoryCard)}
            </View>
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  // Modern Header Styles
  modernHeader: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modernBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  headerSpacer: {
    width: 40,
  },
  // Modern Content Styles
  modernContent: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  searchSection: {
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  modernSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 12,
  },
  modernSearchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
  },
  scrollContainer: {
    flex: 1,
  },
  categoriesSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modernSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
    letterSpacing: 0.5,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3B82F6',
  },
  modernCategoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  modernCategoryCard: {
    width: (width - 60) / 2,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 16,
  },
  modernCategoryIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  modernCategoryTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
    lineHeight: 18,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.neutral.cream,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  balanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary.main,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Bottom navigation padding
  },
  section: {
    padding: 20,
  },
  // Search Bar Styles
  searchContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 2,
    borderColor: Colors.neutral.creamDark,
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    marginLeft: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 15,
    color: Colors.neutral.warmGray,
    marginBottom: 20,
    lineHeight: 22,
  },
  categoriesGrid: {
    gap: 12,
  },
  categoryCard: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
  },
  selectedCategoryCard: {
    elevation: 6,
    shadowOpacity: 0.2,
    transform: [{ scale: 1.02 }],
  },
  categoryGradient: {
    padding: 16,
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  categoryText: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 3,
  },
  categorySubtitle: {
    fontSize: 13,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  categoryArrow: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backToCategories: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backToCategoriesText: {
    fontSize: 16,
    color: Colors.primary.main,
    marginLeft: 8,
    fontWeight: '600',
  },
  providersGrid: {
    gap: 10,
  },
  providerCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 14,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  selectedProviderCard: {
    borderColor: Colors.primary.main,
    elevation: 4,
    backgroundColor: Colors.primary.main + '05',
  },
  providerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  providerIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  providerName: {
    flex: 1,
    fontSize: 15,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  providerArrow: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: Colors.neutral.cream,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Mobile Service Selector Styles
  serviceSelector: {
    marginBottom: 20,
  },
  serviceSelectorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 12,
  },
  serviceButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  serviceButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: Colors.neutral.creamDark,
    gap: 8,
  },
  selectedServiceButton: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  serviceButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  selectedServiceButtonText: {
    color: Colors.neutral.white,
  },
  // Data Bundles Styles
  dataBundlesSection: {
    marginBottom: 20,
  },
  bundlesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 8,
  },
  bundleCard: {
    width: '48%',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
  },
  selectedBundleCard: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '05',
  },
  bundleContent: {
    alignItems: 'center',
  },
  bundleName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    textAlign: 'center',
    marginBottom: 4,
  },
  bundlePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginBottom: 2,
  },
  bundleValidity: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  // Payment Summary Styles
  paymentSummary: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginBottom: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  backToProviders: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backToProvidersText: {
    fontSize: 16,
    color: Colors.primary.main,
    marginLeft: 8,
    fontWeight: '600',
  },
  paymentForm: {
    gap: 18,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  textInput: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    borderWidth: 2,
    borderColor: Colors.neutral.creamDark,
    elevation: 1,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  payButton: {
    borderRadius: 16,
    overflow: 'hidden',
    marginTop: 24,
    elevation: 4,
    shadowColor: Colors.shadow.medium,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  payButtonDisabled: {
    opacity: 0.6,
    elevation: 2,
  },
  payButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    gap: 12,
  },
  payButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  // Enhanced Bill Payment Styles
  accountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  accountInput: {
    flex: 1,
  },
  inquiryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary.main + '10',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  inquiryButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary.main,
  },
  providerDescription: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 4,
    fontStyle: 'italic',
  },
  billInquiryCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: Colors.primary.main + '20',
  },
  billInquiryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  billInquiryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  billInquiryContent: {
    gap: 8,
  },
  billInquiryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  billInquiryLabel: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    flex: 1,
  },
  billInquiryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    flex: 1,
    textAlign: 'right',
  },
  billAmount: {
    color: Colors.secondary.heritage,
    fontSize: 16,
    fontWeight: 'bold',
  },
  // Phone Validation Styles
  inputError: {
    borderColor: '#E53E3E',
    borderWidth: 2,
  },
  validationErrorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    gap: 8,
  },
  validationErrorText: {
    flex: 1,
    fontSize: 14,
    color: '#E53E3E',
    lineHeight: 18,
  },
  providerSwitchSuggestion: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    gap: 8,
  },
  providerSwitchText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '600',
  },
});

export default BillPaymentScreen;
