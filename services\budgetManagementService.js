/**
 * Budget Management Service
 * Handles budget creation, tracking, alerts, and goal-based budgeting
 * Integrates with predictive analytics for intelligent budget suggestions
 */

import { supabase } from './supabaseClient';
import predictiveAnalyticsService from './predictiveAnalyticsService';
import notificationService from './notificationService';

class BudgetManagementService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.alertSubscriptions = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize budget management service
   */
  async initialize(userId) {
    try {
      console.log('💰 Initializing budget management service for user:', userId);
      this.userId = userId;
      
      // Initialize predictive analytics
      await predictiveAnalyticsService.initialize(userId);
      
      // Set up real-time budget monitoring
      await this.setupRealTimeMonitoring(userId);
      
      this.isInitialized = true;
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing budget management:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new budget with AI suggestions
   */
  async createBudget(userId, budgetData) {
    try {
      console.log('💰 Creating budget for user:', userId);

      const {
        name,
        description,
        budgetType = 'monthly',
        totalAmount,
        startDate,
        endDate,
        useAISuggestions = true,
        targetSavingsRate = 0.2
      } = budgetData;

      // Validate input
      if (!name || !totalAmount || !startDate) {
        throw new Error('Missing required budget information');
      }

      // Create the budget record
      const { data: budget, error: budgetError } = await supabase
        .from('user_budgets')
        .insert({
          user_id: userId,
          name,
          description,
          budget_type: budgetType,
          total_amount: totalAmount,
          start_date: startDate,
          end_date: endDate,
          is_auto_generated: useAISuggestions
        })
        .select()
        .single();

      if (budgetError) throw budgetError;

      // Generate category allocations
      let categories = [];
      if (useAISuggestions) {
        const suggestions = await predictiveAnalyticsService.generateBudgetSuggestions(
          userId,
          targetSavingsRate
        );
        
        if (suggestions.success && suggestions.data.suggestedBudgets) {
          categories = suggestions.data.suggestedBudgets.map(cat => ({
            budget_id: budget.id,
            category_name: cat.categoryName,
            category_id: cat.categoryId,
            allocated_amount: Math.min(cat.suggestedAmount, totalAmount * 0.4), // Cap at 40% of total
            icon: cat.icon,
            color: cat.color,
            priority: cat.priority,
            is_essential: cat.priority >= 3
          }));
        }
      }

      // If no AI suggestions or they failed, create basic categories
      if (categories.length === 0) {
        categories = this.createBasicBudgetCategories(budget.id, totalAmount);
      }

      // Insert budget categories
      const { data: budgetCategories, error: categoriesError } = await supabase
        .from('budget_categories')
        .insert(categories)
        .select();

      if (categoriesError) {
        console.warn('⚠️ Error creating budget categories:', categoriesError);
      }

      // Set up default alerts
      await this.createDefaultAlerts(budget.id, userId);

      // Clear cache
      this.clearUserCache(userId);

      return {
        success: true,
        data: {
          budget,
          categories: budgetCategories || [],
          message: 'Budget created successfully'
        }
      };
    } catch (error) {
      console.error('❌ Error creating budget:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Get user's budgets with current spending
   */
  async getUserBudgets(userId, includeInactive = false) {
    try {
      console.log('💰 Getting budgets for user:', userId);

      const cacheKey = `budgets_${userId}_${includeInactive}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // Get budgets
      let query = supabase
        .from('user_budgets')
        .select(`
          *,
          budget_categories (
            id,
            category_name,
            category_id,
            allocated_amount,
            spent_amount,
            icon,
            color,
            priority,
            is_essential
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (!includeInactive) {
        query = query.eq('is_active', true);
      }

      const { data: budgets, error } = await query;

      if (error) throw error;

      // Calculate budget performance
      const budgetsWithPerformance = await Promise.all(
        (budgets || []).map(async (budget) => {
          const performance = await this.calculateBudgetPerformance(budget);
          return {
            ...budget,
            performance
          };
        })
      );

      const result = {
        success: true,
        data: budgetsWithPerformance
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ Error getting user budgets:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Update budget category spending
   */
  async updateCategorySpending(categoryId, spentAmount) {
    try {
      const { data, error } = await supabase
        .from('budget_categories')
        .update({ 
          spent_amount: spentAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .select(`
          *,
          user_budgets!inner(user_id, total_amount)
        `)
        .single();

      if (error) throw error;

      // Check for budget alerts
      await this.checkBudgetAlerts(data);

      // Clear cache for this user
      this.clearUserCache(data.user_budgets.user_id);

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating category spending:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get budget performance analytics
   */
  async getBudgetAnalytics(userId, budgetId = null, period = 'current') {
    try {
      console.log('📊 Getting budget analytics for user:', userId);

      const cacheKey = `budget_analytics_${userId}_${budgetId}_${period}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      // Get budgets to analyze
      let budgets;
      if (budgetId) {
        const { data, error } = await supabase
          .from('user_budgets')
          .select(`
            *,
            budget_categories (*)
          `)
          .eq('id', budgetId)
          .eq('user_id', userId)
          .single();
        
        if (error) throw error;
        budgets = [data];
      } else {
        const budgetsResult = await this.getUserBudgets(userId);
        if (!budgetsResult.success) throw new Error(budgetsResult.error);
        budgets = budgetsResult.data;
      }

      // Calculate analytics
      const analytics = {
        totalBudgets: budgets.length,
        activeBudgets: budgets.filter(b => b.is_active).length,
        totalBudgeted: 0,
        totalSpent: 0,
        averageUtilization: 0,
        categoriesOverBudget: 0,
        categoriesUnderBudget: 0,
        topSpendingCategories: [],
        budgetTrends: [],
        alerts: []
      };

      // Process each budget
      budgets.forEach(budget => {
        const categories = Array.isArray(budget.budget_categories) ? budget.budget_categories : [];
        
        categories.forEach(category => {
          analytics.totalBudgeted += category.allocated_amount || 0;
          analytics.totalSpent += category.spent_amount || 0;
          
          const utilization = category.allocated_amount > 0 
            ? (category.spent_amount || 0) / category.allocated_amount 
            : 0;
          
          if (utilization > 1) {
            analytics.categoriesOverBudget++;
          } else if (utilization < 0.8) {
            analytics.categoriesUnderBudget++;
          }
        });
      });

      // Calculate average utilization
      analytics.averageUtilization = analytics.totalBudgeted > 0 
        ? analytics.totalSpent / analytics.totalBudgeted 
        : 0;

      // Get top spending categories
      analytics.topSpendingCategories = await this.getTopSpendingCategories(userId, 5);

      const result = {
        success: true,
        data: analytics
      };

      this.setCache(cacheKey, result);
      return result;
    } catch (error) {
      console.error('❌ Error getting budget analytics:', error);
      return {
        success: false,
        error: error.message,
        data: {
          totalBudgets: 0,
          activeBudgets: 0,
          totalBudgeted: 0,
          totalSpent: 0,
          averageUtilization: 0,
          categoriesOverBudget: 0,
          categoriesUnderBudget: 0,
          topSpendingCategories: [],
          budgetTrends: [],
          alerts: []
        }
      };
    }
  }

  /**
   * Create budget alerts
   */
  async createBudgetAlert(userId, alertData) {
    try {
      const {
        budgetId,
        categoryId,
        alertType = 'threshold',
        thresholdPercentage = 80,
        thresholdAmount,
        notificationChannels = ['push']
      } = alertData;

      const { data, error } = await supabase
        .from('budget_alerts')
        .insert({
          budget_id: budgetId,
          category_id: categoryId,
          user_id: userId,
          alert_type: alertType,
          threshold_percentage: thresholdPercentage,
          threshold_amount: thresholdAmount,
          notification_channels: notificationChannels
        })
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating budget alert:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check and trigger budget alerts
   */
  async checkBudgetAlerts(categoryData) {
    try {
      const { allocated_amount, spent_amount, id: categoryId } = categoryData;
      const utilizationPercentage = allocated_amount > 0 ? (spent_amount / allocated_amount) * 100 : 0;

      // Get active alerts for this category
      const { data: alerts, error } = await supabase
        .from('budget_alerts')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_enabled', true);

      if (error) {
        console.warn('⚠️ Error fetching budget alerts:', error);
        return;
      }

      // Check each alert
      for (const alert of alerts || []) {
        let shouldTrigger = false;
        let alertMessage = '';

        switch (alert.alert_type) {
          case 'threshold':
            if (utilizationPercentage >= alert.threshold_percentage) {
              shouldTrigger = true;
              alertMessage = `You've used ${utilizationPercentage.toFixed(1)}% of your budget for this category`;
            }
            break;
          case 'overspend':
            if (utilizationPercentage > 100) {
              shouldTrigger = true;
              alertMessage = `You've exceeded your budget by ${(utilizationPercentage - 100).toFixed(1)}%`;
            }
            break;
        }

        if (shouldTrigger) {
          await this.triggerBudgetAlert(alert, alertMessage, categoryData);
        }
      }
    } catch (error) {
      console.error('❌ Error checking budget alerts:', error);
    }
  }

  /**
   * Trigger a budget alert
   */
  async triggerBudgetAlert(alert, message, categoryData) {
    try {
      // Update last triggered timestamp
      await supabase
        .from('budget_alerts')
        .update({ last_triggered_at: new Date().toISOString() })
        .eq('id', alert.id);

      // Send notifications based on channels
      const channels = Array.isArray(alert.notification_channels) ? alert.notification_channels : ['push'];
      
      for (const channel of channels) {
        switch (channel) {
          case 'push':
            await notificationService.sendNotification(alert.user_id, {
              title: 'Budget Alert',
              body: message,
              type: 'budget_alert',
              data: {
                alertId: alert.id,
                categoryId: categoryData.id,
                budgetId: alert.budget_id
              }
            });
            break;
          // Add email and SMS support later
        }
      }

      console.log('🚨 Budget alert triggered:', message);
    } catch (error) {
      console.error('❌ Error triggering budget alert:', error);
    }
  }

  /**
   * Calculate budget performance
   */
  calculateBudgetPerformance(budget) {
    try {
      const categories = Array.isArray(budget.budget_categories) ? budget.budget_categories : [];
      
      let totalAllocated = 0;
      let totalSpent = 0;
      let categoriesOverBudget = 0;
      let categoriesOnTrack = 0;

      categories.forEach(category => {
        const allocated = category.allocated_amount || 0;
        const spent = category.spent_amount || 0;
        
        totalAllocated += allocated;
        totalSpent += spent;
        
        if (spent > allocated) {
          categoriesOverBudget++;
        } else if (spent >= allocated * 0.8) {
          categoriesOnTrack++;
        }
      });

      const utilizationRate = totalAllocated > 0 ? totalSpent / totalAllocated : 0;
      const variance = totalSpent - totalAllocated;
      const variancePercentage = totalAllocated > 0 ? (variance / totalAllocated) * 100 : 0;

      // Calculate performance score (0-1)
      let performanceScore = 1;
      if (utilizationRate > 1) {
        performanceScore = Math.max(0, 1 - (utilizationRate - 1));
      } else if (utilizationRate < 0.5) {
        performanceScore = utilizationRate * 2; // Penalize very low utilization
      }

      return {
        totalAllocated,
        totalSpent,
        utilizationRate,
        variance,
        variancePercentage,
        categoriesOverBudget,
        categoriesOnTrack,
        performanceScore,
        status: utilizationRate > 1 ? 'over_budget' : utilizationRate > 0.8 ? 'on_track' : 'under_budget'
      };
    } catch (error) {
      console.error('❌ Error calculating budget performance:', error);
      return {
        totalAllocated: 0,
        totalSpent: 0,
        utilizationRate: 0,
        variance: 0,
        variancePercentage: 0,
        categoriesOverBudget: 0,
        categoriesOnTrack: 0,
        performanceScore: 0,
        status: 'unknown'
      };
    }
  }

  /**
   * Create basic budget categories when AI suggestions fail
   */
  createBasicBudgetCategories(budgetId, totalAmount) {
    const basicCategories = [
      { name: 'Food & Dining', id: 'food_dining', percentage: 0.25, priority: 3, essential: true },
      { name: 'Transportation', id: 'transportation', percentage: 0.15, priority: 3, essential: true },
      { name: 'Bills & Utilities', id: 'bills_utilities', percentage: 0.20, priority: 3, essential: true },
      { name: 'Healthcare', id: 'healthcare', percentage: 0.10, priority: 2, essential: true },
      { name: 'Shopping', id: 'shopping', percentage: 0.15, priority: 2, essential: false },
      { name: 'Entertainment', id: 'entertainment', percentage: 0.10, priority: 1, essential: false },
      { name: 'Other', id: 'other', percentage: 0.05, priority: 1, essential: false }
    ];

    return basicCategories.map(cat => ({
      budget_id: budgetId,
      category_name: cat.name,
      category_id: cat.id,
      allocated_amount: Math.round(totalAmount * cat.percentage),
      icon: this.getCategoryIcon(cat.id),
      color: this.getCategoryColor(cat.id),
      priority: cat.priority,
      is_essential: cat.essential
    }));
  }

  /**
   * Get category icon
   */
  getCategoryIcon(categoryId) {
    const icons = {
      'food_dining': 'restaurant',
      'transportation': 'car',
      'bills_utilities': 'receipt',
      'healthcare': 'medical',
      'shopping': 'bag',
      'entertainment': 'play-circle',
      'other': 'ellipsis-horizontal'
    };
    return icons[categoryId] || 'folder';
  }

  /**
   * Get category color
   */
  getCategoryColor(categoryId) {
    const colors = {
      'food_dining': '#FF6B6B',
      'transportation': '#4ECDC4',
      'bills_utilities': '#96CEB4',
      'healthcare': '#FD79A8',
      'shopping': '#45B7D1',
      'entertainment': '#FFEAA7',
      'other': '#B2BEC3'
    };
    return colors[categoryId] || '#B2BEC3';
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Create default alerts for a new budget
   */
  async createDefaultAlerts(budgetId, userId) {
    try {
      const defaultAlerts = [
        {
          budget_id: budgetId,
          user_id: userId,
          alert_type: 'threshold',
          threshold_percentage: 80,
          notification_channels: ['push']
        },
        {
          budget_id: budgetId,
          user_id: userId,
          alert_type: 'overspend',
          threshold_percentage: 100,
          notification_channels: ['push']
        }
      ];

      const { error } = await supabase
        .from('budget_alerts')
        .insert(defaultAlerts);

      if (error) {
        console.warn('⚠️ Error creating default alerts:', error);
      }
    } catch (error) {
      console.error('❌ Error creating default alerts:', error);
    }
  }

  /**
   * Setup real-time budget monitoring
   */
  async setupRealTimeMonitoring(userId) {
    try {
      // Subscribe to transaction changes for budget updates
      const channel = supabase
        .channel(`budget_monitoring_${userId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${userId}`
          },
          (payload) => this.handleTransactionChange(payload)
        )
        .subscribe();

      this.alertSubscriptions.set(userId, channel);
      console.log('📊 Real-time budget monitoring setup for user:', userId);
    } catch (error) {
      console.error('❌ Error setting up real-time monitoring:', error);
    }
  }

  /**
   * Handle transaction changes for budget updates
   */
  async handleTransactionChange(payload) {
    try {
      const { new: newTransaction, old: oldTransaction, eventType } = payload;

      if (eventType === 'INSERT' && newTransaction) {
        await this.updateBudgetFromTransaction(newTransaction);
      } else if (eventType === 'UPDATE' && newTransaction) {
        await this.updateBudgetFromTransaction(newTransaction);
      } else if (eventType === 'DELETE' && oldTransaction) {
        await this.updateBudgetFromTransaction(oldTransaction, true);
      }
    } catch (error) {
      console.error('❌ Error handling transaction change:', error);
    }
  }

  /**
   * Update budget spending from transaction
   */
  async updateBudgetFromTransaction(transaction, isDelete = false) {
    try {
      if (!transaction || !transaction.user_id || !transaction.category) return;

      // Get active budgets for this user
      const { data: budgets, error } = await supabase
        .from('user_budgets')
        .select(`
          id,
          start_date,
          end_date,
          budget_categories!inner(id, category_id)
        `)
        .eq('user_id', transaction.user_id)
        .eq('is_active', true);

      if (error || !budgets) return;

      // Find relevant budget categories
      for (const budget of budgets) {
        const transactionDate = new Date(transaction.created_at);
        const budgetStart = new Date(budget.start_date);
        const budgetEnd = budget.end_date ? new Date(budget.end_date) : null;

        // Check if transaction falls within budget period
        if (transactionDate >= budgetStart && (!budgetEnd || transactionDate <= budgetEnd)) {
          const relevantCategory = budget.budget_categories.find(
            cat => cat.category_id === transaction.category
          );

          if (relevantCategory) {
            // Recalculate spent amount for this category
            await this.recalculateCategorySpending(relevantCategory.id, transaction.user_id);
          }
        }
      }

      // Clear cache for this user
      this.clearUserCache(transaction.user_id);
    } catch (error) {
      console.error('❌ Error updating budget from transaction:', error);
    }
  }

  /**
   * Recalculate category spending
   */
  async recalculateCategorySpending(categoryId, userId) {
    try {
      // Get category and budget info
      const { data: category, error: categoryError } = await supabase
        .from('budget_categories')
        .select(`
          *,
          user_budgets!inner(start_date, end_date)
        `)
        .eq('id', categoryId)
        .single();

      if (categoryError || !category) return;

      const budget = category.user_budgets;
      const startDate = budget.start_date;
      const endDate = budget.end_date || new Date().toISOString();

      // Calculate total spent in this category for the budget period
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('category', category.category_id)
        .lt('amount', 0) // Only expenses
        .gte('created_at', startDate)
        .lte('created_at', endDate);

      if (transError) {
        console.warn('⚠️ Error fetching transactions for category spending:', transError);
        return;
      }

      const totalSpent = (transactions || []).reduce((sum, tx) => sum + Math.abs(tx.amount), 0);

      // Update category spent amount
      const { error: updateError } = await supabase
        .from('budget_categories')
        .update({
          spent_amount: totalSpent,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId);

      if (updateError) {
        console.warn('⚠️ Error updating category spent amount:', updateError);
        return;
      }

      // Check for alerts
      await this.checkBudgetAlerts({
        ...category,
        spent_amount: totalSpent
      });
    } catch (error) {
      console.error('❌ Error recalculating category spending:', error);
    }
  }

  /**
   * Get top spending categories
   */
  async getTopSpendingCategories(userId, limit = 5) {
    try {
      const { data: categories, error } = await supabase
        .from('budget_categories')
        .select(`
          category_name,
          category_id,
          spent_amount,
          allocated_amount,
          icon,
          color,
          user_budgets!inner(user_id)
        `)
        .eq('user_budgets.user_id', userId)
        .order('spent_amount', { ascending: false })
        .limit(limit);

      if (error) {
        console.warn('⚠️ Error fetching top spending categories:', error);
        return [];
      }

      return (categories || []).map(cat => ({
        name: cat.category_name,
        id: cat.category_id,
        spent: cat.spent_amount,
        allocated: cat.allocated_amount,
        utilization: cat.allocated_amount > 0 ? (cat.spent_amount / cat.allocated_amount) * 100 : 0,
        icon: cat.icon,
        color: cat.color
      }));
    } catch (error) {
      console.error('❌ Error getting top spending categories:', error);
      return [];
    }
  }

  /**
   * Get budget recommendations
   */
  async getBudgetRecommendations(userId) {
    try {
      console.log('💡 Getting budget recommendations for user:', userId);

      // Get current budget performance
      const analyticsResult = await this.getBudgetAnalytics(userId);
      if (!analyticsResult.success) {
        throw new Error(analyticsResult.error);
      }

      const analytics = analyticsResult.data;
      const recommendations = [];

      // High utilization warning
      if (analytics.averageUtilization > 0.9) {
        recommendations.push({
          type: 'warning',
          priority: 'high',
          title: 'Budget Overspending',
          message: 'You\'re spending more than 90% of your budget. Consider reducing expenses.',
          action: 'review_spending',
          icon: 'warning'
        });
      }

      // Categories over budget
      if (analytics.categoriesOverBudget > 0) {
        recommendations.push({
          type: 'alert',
          priority: 'high',
          title: 'Categories Over Budget',
          message: `${analytics.categoriesOverBudget} categories are over budget. Review and adjust.`,
          action: 'adjust_categories',
          icon: 'alert-circle'
        });
      }

      // Low utilization suggestion
      if (analytics.averageUtilization < 0.5) {
        recommendations.push({
          type: 'suggestion',
          priority: 'low',
          title: 'Budget Underutilization',
          message: 'You\'re using less than 50% of your budget. Consider reallocating funds.',
          action: 'reallocate_budget',
          icon: 'trending-down'
        });
      }

      // Get predictive recommendations
      const predictiveResult = await predictiveAnalyticsService.generateBudgetSuggestions(userId);
      if (predictiveResult.success && predictiveResult.data.recommendations) {
        recommendations.push(...predictiveResult.data.recommendations.map(rec => ({
          ...rec,
          source: 'predictive_analytics'
        })));
      }

      return {
        success: true,
        data: recommendations
      };
    } catch (error) {
      console.error('❌ Error getting budget recommendations:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Get real-time budget status
   */
  async getRealTimeBudgetStatus(userId) {
    try {
      const budgetsResult = await this.getUserBudgets(userId);
      if (!budgetsResult.success) {
        throw new Error(budgetsResult.error);
      }

      const budgets = budgetsResult.data;
      const status = {
        totalBudgets: budgets.length,
        activeBudgets: budgets.filter(b => b.is_active).length,
        budgetsOverLimit: 0,
        budgetsNearLimit: 0,
        totalUtilization: 0,
        alerts: [],
        lastUpdated: new Date().toISOString()
      };

      // Calculate real-time status
      budgets.forEach(budget => {
        const performance = budget.performance || {};
        const utilization = performance.utilizationRate || 0;

        status.totalUtilization += utilization;

        if (utilization > 1) {
          status.budgetsOverLimit++;
          status.alerts.push({
            type: 'over_budget',
            budgetId: budget.id,
            budgetName: budget.name,
            utilization: utilization * 100,
            message: `${budget.name} is ${((utilization - 1) * 100).toFixed(1)}% over budget`
          });
        } else if (utilization > 0.8) {
          status.budgetsNearLimit++;
          status.alerts.push({
            type: 'near_limit',
            budgetId: budget.id,
            budgetName: budget.name,
            utilization: utilization * 100,
            message: `${budget.name} is ${(utilization * 100).toFixed(1)}% used`
          });
        }
      });

      status.averageUtilization = budgets.length > 0 ? status.totalUtilization / budgets.length : 0;

      return {
        success: true,
        data: status
      };
    } catch (error) {
      console.error('❌ Error getting real-time budget status:', error);
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Subscribe to budget updates
   */
  subscribeToBudgetUpdates(userId, callback) {
    try {
      const channelName = `budget_updates_${userId}`;

      // Remove existing subscription if any
      if (this.alertSubscriptions.has(channelName)) {
        supabase.removeChannel(this.alertSubscriptions.get(channelName));
      }

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'budget_categories',
            filter: `budget_id=in.(${this.getUserBudgetIds(userId).join(',')})`
          },
          (payload) => {
            console.log('📊 Budget category updated:', payload);
            this.handleBudgetUpdate(payload, callback);
          }
        )
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'transactions',
            filter: `user_id=eq.${userId}`
          },
          (payload) => {
            console.log('💰 Transaction updated, checking budgets:', payload);
            this.handleTransactionUpdate(payload, userId, callback);
          }
        )
        .subscribe();

      this.alertSubscriptions.set(channelName, channel);
      console.log('📊 Subscribed to budget updates for user:', userId);

      return {
        success: true,
        channelName,
        unsubscribe: () => {
          supabase.removeChannel(channel);
          this.alertSubscriptions.delete(channelName);
        }
      };
    } catch (error) {
      console.error('❌ Error subscribing to budget updates:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle budget update events
   */
  async handleBudgetUpdate(payload, callback) {
    try {
      const { new: newData, old: oldData, eventType } = payload;

      // Clear cache to ensure fresh data
      this.cache.clear();

      // Get updated budget status
      const budgetId = newData?.budget_id || oldData?.budget_id;
      if (budgetId) {
        const { data: budget } = await supabase
          .from('user_budgets')
          .select('user_id')
          .eq('id', budgetId)
          .single();

        if (budget?.user_id) {
          const statusResult = await this.getRealTimeBudgetStatus(budget.user_id);
          if (statusResult.success && callback) {
            callback({
              type: 'budget_update',
              data: statusResult.data,
              eventType,
              budgetId
            });
          }
        }
      }
    } catch (error) {
      console.error('❌ Error handling budget update:', error);
    }
  }

  /**
   * Handle transaction update events
   */
  async handleTransactionUpdate(payload, userId, callback) {
    try {
      const { new: newTransaction, eventType } = payload;

      if (newTransaction && newTransaction.category) {
        // Update relevant budget categories
        await this.updateBudgetFromTransaction(newTransaction);

        // Get updated status
        const statusResult = await this.getRealTimeBudgetStatus(userId);
        if (statusResult.success && callback) {
          callback({
            type: 'transaction_update',
            data: statusResult.data,
            eventType,
            transaction: newTransaction
          });
        }
      }
    } catch (error) {
      console.error('❌ Error handling transaction update:', error);
    }
  }

  /**
   * Get user budget IDs for subscription filtering
   */
  async getUserBudgetIds(userId) {
    try {
      const { data: budgets, error } = await supabase
        .from('user_budgets')
        .select('id')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        console.warn('⚠️ Error fetching budget IDs:', error);
        return [];
      }

      return (budgets || []).map(b => b.id);
    } catch (error) {
      console.error('❌ Error getting user budget IDs:', error);
      return [];
    }
  }

  /**
   * Send proactive budget notifications
   */
  async sendProactiveBudgetNotifications(userId) {
    try {
      const statusResult = await this.getRealTimeBudgetStatus(userId);
      if (!statusResult.success) return;

      const status = statusResult.data;

      // Send notifications for high-priority alerts
      for (const alert of status.alerts) {
        if (alert.type === 'over_budget') {
          await notificationService.sendNotification(userId, {
            title: 'Budget Alert',
            body: alert.message,
            type: 'budget_overspend',
            priority: 'high',
            data: {
              budgetId: alert.budgetId,
              utilization: alert.utilization
            }
          });
        } else if (alert.type === 'near_limit' && alert.utilization > 90) {
          await notificationService.sendNotification(userId, {
            title: 'Budget Warning',
            body: alert.message,
            type: 'budget_warning',
            priority: 'medium',
            data: {
              budgetId: alert.budgetId,
              utilization: alert.utilization
            }
          });
        }
      }
    } catch (error) {
      console.error('❌ Error sending proactive notifications:', error);
    }
  }

  /**
   * Schedule budget monitoring tasks
   */
  startBudgetMonitoring(userId) {
    try {
      // Check budget status every 5 minutes
      const monitoringInterval = setInterval(async () => {
        await this.sendProactiveBudgetNotifications(userId);
      }, 5 * 60 * 1000); // 5 minutes

      // Store interval for cleanup
      this.alertSubscriptions.set(`monitoring_${userId}`, monitoringInterval);

      console.log('📊 Budget monitoring started for user:', userId);
      return {
        success: true,
        intervalId: monitoringInterval
      };
    } catch (error) {
      console.error('❌ Error starting budget monitoring:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Stop budget monitoring
   */
  stopBudgetMonitoring(userId) {
    try {
      const intervalId = this.alertSubscriptions.get(`monitoring_${userId}`);
      if (intervalId) {
        clearInterval(intervalId);
        this.alertSubscriptions.delete(`monitoring_${userId}`);
        console.log('📊 Budget monitoring stopped for user:', userId);
      }
    } catch (error) {
      console.error('❌ Error stopping budget monitoring:', error);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      // Unsubscribe from all real-time channels
      for (const [key, value] of this.alertSubscriptions) {
        if (typeof value === 'object' && value.unsubscribe) {
          // Supabase channel
          await supabase.removeChannel(value);
        } else if (typeof value === 'number') {
          // Interval ID
          clearInterval(value);
        }
      }
      this.alertSubscriptions.clear();

      // Clear cache
      this.cache.clear();

      this.isInitialized = false;
      console.log('🧹 Budget management service cleanup completed');
    } catch (error) {
      console.error('❌ Error during budget service cleanup:', error);
    }
  }
}

export default new BudgetManagementService();
