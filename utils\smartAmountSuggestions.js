/**
 * Smart Amount Suggestions Utility
 * 
 * Generates intelligent amount suggestions based on:
 * - User's preferred currency
 * - Historical transaction patterns
 * - Currency-specific common amounts
 * - Regional usage patterns
 */

import currencyService from '../services/currencyService';

class SmartAmountSuggestions {
  constructor() {
    this.userTransactionHistory = [];
    this.currencyPatterns = {
      'UGX': {
        common: [5000, 10000, 20000, 50000, 100000, 200000, 500000, 1000000],
        multiplier: 1,
        roundingFactor: 1000
      },
      'KES': {
        common: [100, 500, 1000, 2000, 5000, 10000, 20000, 50000],
        multiplier: 0.32,
        roundingFactor: 100
      },
      'TZS': {
        common: [2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000],
        multiplier: 0.85,
        roundingFactor: 1000
      },
      'RWF': {
        common: [1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000],
        multiplier: 0.37,
        roundingFactor: 1000
      },
      'BIF': {
        common: [2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000],
        multiplier: 0.67,
        roundingFactor: 1000
      },
      'ETB': {
        common: [50, 100, 200, 500, 1000, 2000, 5000, 10000],
        multiplier: 0.015,
        roundingFactor: 50
      }
    };
  }

  /**
   * Generate smart quick amounts for a specific use case
   * @param {string} currency - User's preferred currency
   * @param {string} context - Context: 'topup', 'transfer', 'savings', 'bills'
   * @param {Array} userHistory - User's transaction history (optional)
   * @returns {Array} Array of suggested amounts
   */
  generateQuickAmounts(currency = 'UGX', context = 'general', userHistory = []) {
    try {
      const pattern = this.currencyPatterns[currency] || this.currencyPatterns['UGX'];
      let baseAmounts = [...pattern.common];

      // Adjust amounts based on context
      baseAmounts = this.adjustForContext(baseAmounts, context, currency);

      // Incorporate user history if available
      if (userHistory && userHistory.length > 0) {
        baseAmounts = this.incorporateUserHistory(baseAmounts, userHistory, currency);
      }

      // Ensure amounts are properly rounded and formatted
      baseAmounts = this.roundAndFormat(baseAmounts, pattern.roundingFactor);

      // Return top 6 most relevant amounts
      return baseAmounts.slice(0, 6);
    } catch (error) {
      console.error('❌ Error generating quick amounts:', error);
      return this.getFallbackAmounts(currency);
    }
  }

  /**
   * Adjust amounts based on usage context
   */
  adjustForContext(amounts, context, currency) {
    const multipliers = {
      'topup': 1.2,      // Slightly higher for top-ups
      'transfer': 1.0,   // Standard amounts for transfers
      'savings': 0.8,    // Lower amounts for savings
      'bills': 1.1,      // Slightly higher for bills
      'general': 1.0     // Standard amounts
    };

    const multiplier = multipliers[context] || 1.0;
    return amounts.map(amount => Math.round(amount * multiplier));
  }

  /**
   * Incorporate user's transaction history to personalize suggestions
   */
  incorporateUserHistory(baseAmounts, userHistory, currency) {
    try {
      // Extract common amounts from user history
      const userAmounts = userHistory
        .filter(tx => tx.currency === currency || tx.currency === 'UGX')
        .map(tx => {
          // Convert to user's currency if needed
          if (tx.currency === 'UGX' && currency !== 'UGX') {
            return currencyService.convertFromUGX(tx.amount, currency);
          }
          return tx.amount;
        })
        .filter(amount => amount > 0);

      if (userAmounts.length === 0) return baseAmounts;

      // Find frequently used amounts
      const amountFrequency = {};
      userAmounts.forEach(amount => {
        const rounded = this.roundToNearestSuggestion(amount, currency);
        amountFrequency[rounded] = (amountFrequency[rounded] || 0) + 1;
      });

      // Get top user amounts
      const topUserAmounts = Object.entries(amountFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([amount]) => parseInt(amount));

      // Merge with base amounts, prioritizing user patterns
      const mergedAmounts = [...new Set([...topUserAmounts, ...baseAmounts])];
      return mergedAmounts.sort((a, b) => a - b);
    } catch (error) {
      console.error('❌ Error incorporating user history:', error);
      return baseAmounts;
    }
  }

  /**
   * Round amount to nearest suggestion based on currency
   */
  roundToNearestSuggestion(amount, currency) {
    const pattern = this.currencyPatterns[currency] || this.currencyPatterns['UGX'];
    const factor = pattern.roundingFactor;
    return Math.round(amount / factor) * factor;
  }

  /**
   * Round and format amounts properly
   */
  roundAndFormat(amounts, roundingFactor) {
    return amounts
      .map(amount => Math.round(amount / roundingFactor) * roundingFactor)
      .filter(amount => amount > 0)
      .sort((a, b) => a - b)
      .filter((amount, index, arr) => arr.indexOf(amount) === index); // Remove duplicates
  }

  /**
   * Get fallback amounts if generation fails
   */
  getFallbackAmounts(currency) {
    const pattern = this.currencyPatterns[currency] || this.currencyPatterns['UGX'];
    return pattern.common.slice(0, 6);
  }

  /**
   * Generate amounts for specific bill types
   */
  generateBillAmounts(billType, currency = 'UGX') {
    const billPatterns = {
      'electricity': {
        'UGX': [10000, 25000, 50000, 100000, 150000, 200000],
        'KES': [500, 1000, 2000, 3000, 5000, 8000],
        'TZS': [5000, 15000, 30000, 60000, 100000, 150000]
      },
      'water': {
        'UGX': [5000, 15000, 30000, 50000, 75000, 100000],
        'KES': [200, 500, 1000, 2000, 3000, 5000],
        'TZS': [3000, 8000, 20000, 40000, 60000, 80000]
      },
      'internet': {
        'UGX': [20000, 50000, 100000, 150000, 200000, 300000],
        'KES': [1000, 2000, 3000, 5000, 8000, 12000],
        'TZS': [15000, 35000, 70000, 120000, 180000, 250000]
      },
      'airtime': {
        'UGX': [1000, 2000, 5000, 10000, 20000, 50000],
        'KES': [50, 100, 200, 500, 1000, 2000],
        'TZS': [1000, 2000, 5000, 10000, 20000, 40000]
      }
    };

    const pattern = billPatterns[billType];
    if (pattern && pattern[currency]) {
      return pattern[currency];
    }

    // Fallback to general amounts
    return this.generateQuickAmounts(currency, 'bills');
  }

  /**
   * Update user transaction history for better suggestions
   */
  updateUserHistory(transactions) {
    this.userTransactionHistory = transactions.slice(-50); // Keep last 50 transactions
  }

  /**
   * Get currency-specific formatting suggestions
   */
  getCurrencyDisplayFormat(currency) {
    const formats = {
      'UGX': { decimals: 0, separator: ',', symbol: 'UGX' },
      'KES': { decimals: 2, separator: ',', symbol: 'KSh' },
      'TZS': { decimals: 2, separator: ',', symbol: 'TSh' },
      'RWF': { decimals: 0, separator: ',', symbol: 'RWF' },
      'BIF': { decimals: 0, separator: ',', symbol: 'BIF' },
      'ETB': { decimals: 2, separator: ',', symbol: 'ETB' }
    };

    return formats[currency] || formats['UGX'];
  }
}

// Export singleton instance
const smartAmountSuggestions = new SmartAmountSuggestions();
export default smartAmountSuggestions;
