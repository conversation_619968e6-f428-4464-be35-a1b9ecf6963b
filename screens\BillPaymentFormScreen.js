/**
 * Enhanced Bill Payment Form Screen
 * Comprehensive form for bill payment with validation, account verification,
 * and payment processing
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import billerManagementService from '../services/billerManagementService';
import paymentValidationEngine from '../services/paymentValidationEngine';
import billPaymentService from '../services/billPaymentService';
import { formatCurrency } from '../utils/currencyUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const BillPaymentFormScreen = ({ navigation, route }) => {
  const { billerId, billerName } = route.params;
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [biller, setBiller] = useState(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [verifying, setVerifying] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    accountNumber: '',
    amount: '',
    accountName: '',
    paymentMethod: 'wallet'
  });

  // Validation state
  const [validationResults, setValidationResults] = useState({
    isValid: false,
    errors: [],
    warnings: [],
    info: []
  });

  const [accountVerified, setAccountVerified] = useState(false);
  const [accountVerificationData, setAccountVerificationData] = useState(null);

  useEffect(() => {
    loadBillerDetails();
  }, [billerId]);

  useEffect(() => {
    if (formData.accountNumber && formData.amount) {
      validatePayment();
    }
  }, [formData.accountNumber, formData.amount]);

  const loadBillerDetails = async () => {
    try {
      setLoading(true);
      const result = await billerManagementService.getBillerById(billerId);
      
      if (result.success) {
        setBiller(result.biller);
      } else {
        Alert.alert('Error', 'Failed to load biller details');
        navigation.goBack();
      }
    } catch (error) {
      console.error('❌ Error loading biller details:', error);
      Alert.alert('Error', 'Failed to load biller details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const validatePayment = async () => {
    try {
      const paymentData = {
        billerId,
        accountNumber: formData.accountNumber,
        amount: parseFloat(formData.amount) || 0,
        paymentMethod: formData.paymentMethod
      };

      const userId = 'current-user-id'; // Replace with actual user ID
      const results = await paymentValidationEngine.validatePayment(userId, paymentData);
      setValidationResults(results);
    } catch (error) {
      console.error('❌ Error validating payment:', error);
    }
  };

  const verifyAccount = async () => {
    if (!formData.accountNumber || formData.accountNumber.length < 6) {
      Alert.alert('Invalid Account', 'Please enter a valid account number');
      return;
    }

    try {
      setVerifying(true);
      
      // Mock account verification - in production, this would call biller APIs
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockVerificationData = {
        accountName: `Account Holder ${formData.accountNumber.slice(-4)}`,
        accountStatus: 'Active',
        outstandingBalance: Math.floor(Math.random() * 100000) + 10000,
        lastPayment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      };

      setAccountVerificationData(mockVerificationData);
      setAccountVerified(true);
      setFormData(prev => ({ ...prev, accountName: mockVerificationData.accountName }));
      
      Alert.alert(
        'Account Verified',
        `Account holder: ${mockVerificationData.accountName}\nStatus: ${mockVerificationData.accountStatus}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error verifying account:', error);
      Alert.alert('Verification Failed', 'Unable to verify account. Please check the account number and try again.');
    } finally {
      setVerifying(false);
    }
  };

  const processPayment = async () => {
    if (!validationResults.isValid) {
      Alert.alert('Validation Error', 'Please fix the validation errors before proceeding');
      return;
    }

    try {
      setProcessing(true);

      const paymentData = {
        billerId,
        accountNumber: formData.accountNumber,
        amount: parseFloat(formData.amount),
        accountName: formData.accountName,
        paymentMethod: formData.paymentMethod,
        currency: 'UGX'
      };

      const userId = 'current-user-id'; // Replace with actual user ID

      // Navigate to confirmation screen instead of processing directly
      navigation.navigate('PaymentConfirmation', {
        paymentData,
        biller,
        userId,
        feeCalculation: biller ? calculateFeePreview() : null
      });

    } catch (error) {
      console.error('❌ Error preparing payment:', error);
      Alert.alert('Error', 'Failed to prepare payment. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const calculateFeePreview = () => {
    if (!biller || !formData.amount) return null;

    const amount = parseFloat(formData.amount) || 0;
    let fee = 0;

    switch (biller.feeStructure?.type) {
      case 'fixed':
        fee = biller.feeStructure.amount || 0;
        break;
      case 'percentage':
        fee = (amount * (biller.feeStructure.percentage || 0)) / 100;
        break;
      case 'tiered':
        const tiers = biller.feeStructure.structure?.tiers || [];
        for (const tier of tiers) {
          if (amount >= tier.min_amount && amount <= tier.max_amount) {
            fee = tier.fee;
            break;
          }
        }
        break;
    }

    return {
      fee: Math.round(fee * 100) / 100,
      totalAmount: amount + fee,
      feeType: biller.feeStructure?.type
    };
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Reset account verification when account number changes
    if (field === 'accountNumber') {
      setAccountVerified(false);
      setAccountVerificationData(null);
    }
  };

  const renderBillerInfo = () => (
    <View style={styles.billerInfo}>
      <View style={styles.billerHeader}>
        <View style={styles.billerIcon}>
          <Ionicons 
            name={getBillerIcon(biller?.category?.name)} 
            size={32} 
            color={biller?.category?.color || theme.colors.primary} 
          />
        </View>
        <View style={styles.billerDetails}>
          <Text style={styles.billerName}>{biller?.displayName}</Text>
          <Text style={styles.billerCategory}>{biller?.category?.displayName}</Text>
          <Text style={styles.billerLimits}>
            Limits: {formatCurrency(biller?.minAmount)} - {formatCurrency(biller?.maxAmount)}
          </Text>
        </View>
        <View style={styles.billerStatus}>
          <View style={[styles.statusDot, { 
            backgroundColor: biller?.isAvailable ? theme.colors.success : theme.colors.error 
          }]} />
          <Text style={styles.statusText}>
            {biller?.isAvailable ? 'Available' : 'Unavailable'}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderAccountNumberInput = () => (
    <View style={styles.inputSection}>
      <Text style={styles.inputLabel}>Account Number</Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder={biller?.validationRules?.accountNumberFormat ? 
            `Enter ${biller.validationRules.accountNumberLength || ''} digit account number` : 
            'Enter account number'
          }
          placeholderTextColor={theme.colors.textSecondary}
          value={formData.accountNumber}
          onChangeText={(value) => updateFormData('accountNumber', value)}
          keyboardType="numeric"
          maxLength={biller?.validationRules?.accountNumberLength || 20}
        />
        <TouchableOpacity
          style={[styles.verifyButton, { 
            backgroundColor: accountVerified ? theme.colors.success : theme.colors.primary 
          }]}
          onPress={verifyAccount}
          disabled={verifying || !formData.accountNumber}
        >
          {verifying ? (
            <Text style={styles.verifyButtonText}>Verifying...</Text>
          ) : accountVerified ? (
            <>
              <Ionicons name="checkmark" size={16} color={theme.colors.white} />
              <Text style={styles.verifyButtonText}>Verified</Text>
            </>
          ) : (
            <Text style={styles.verifyButtonText}>Verify</Text>
          )}
        </TouchableOpacity>
      </View>
      
      {accountVerificationData && (
        <View style={styles.verificationInfo}>
          <Text style={styles.verificationText}>
            Account Holder: {accountVerificationData.accountName}
          </Text>
          <Text style={styles.verificationText}>
            Status: {accountVerificationData.accountStatus}
          </Text>
          {accountVerificationData.outstandingBalance && (
            <Text style={styles.verificationText}>
              Outstanding: {formatCurrency(accountVerificationData.outstandingBalance)}
            </Text>
          )}
        </View>
      )}
    </View>
  );

  const renderAmountInput = () => (
    <View style={styles.inputSection}>
      <Text style={styles.inputLabel}>Payment Amount</Text>
      <View style={styles.amountContainer}>
        <Text style={styles.currencySymbol}>UGX</Text>
        <TextInput
          style={styles.amountInput}
          placeholder="0"
          placeholderTextColor={theme.colors.textSecondary}
          value={formData.amount}
          onChangeText={(value) => updateFormData('amount', value)}
          keyboardType="numeric"
        />
      </View>
      
      {accountVerificationData?.outstandingBalance && (
        <TouchableOpacity
          style={styles.quickAmountButton}
          onPress={() => updateFormData('amount', accountVerificationData.outstandingBalance.toString())}
        >
          <Text style={styles.quickAmountText}>
            Pay Outstanding: {formatCurrency(accountVerificationData.outstandingBalance)}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderValidationResults = () => {
    if (validationResults.errors.length === 0 && validationResults.warnings.length === 0) {
      return null;
    }

    return (
      <View style={styles.validationSection}>
        {validationResults.errors.map((error, index) => (
          <View key={index} style={[styles.validationItem, styles.errorItem]}>
            <Ionicons name="alert-circle" size={16} color={theme.colors.error} />
            <Text style={[styles.validationText, styles.errorText]}>{error.message}</Text>
          </View>
        ))}
        
        {validationResults.warnings.map((warning, index) => (
          <View key={index} style={[styles.validationItem, styles.warningItem]}>
            <Ionicons name="warning" size={16} color={theme.colors.warning} />
            <Text style={[styles.validationText, styles.warningText]}>{warning.message}</Text>
          </View>
        ))}
      </View>
    );
  };

  const renderPaymentSummary = () => {
    if (!formData.amount || !biller) return null;

    const amount = parseFloat(formData.amount) || 0;
    const fee = biller.feeStructure?.type === 'fixed' ? 
      (biller.feeStructure.amount || 0) : 
      (amount * (biller.feeStructure?.percentage || 0)) / 100;
    const total = amount + fee;

    return (
      <View style={styles.summarySection}>
        <Text style={styles.summaryTitle}>Payment Summary</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Amount</Text>
          <Text style={styles.summaryValue}>{formatCurrency(amount)}</Text>
        </View>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Service Fee</Text>
          <Text style={styles.summaryValue}>{formatCurrency(fee)}</Text>
        </View>
        <View style={[styles.summaryRow, styles.totalRow]}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalValue}>{formatCurrency(total)}</Text>
        </View>
      </View>
    );
  };

  const getBillerIcon = (categoryName) => {
    const icons = {
      utilities: 'flash',
      telecommunications: 'phone-portrait',
      government: 'business',
      insurance: 'shield-checkmark',
      education: 'school',
      entertainment: 'tv'
    };
    return icons[categoryName] || 'card';
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading biller details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Pay Bill</Text>
        <View style={styles.headerButton} />
      </View>

      <KeyboardAvoidingView 
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderBillerInfo()}
          {renderAccountNumberInput()}
          {renderAmountInput()}
          {renderValidationResults()}
          {renderPaymentSummary()}
        </ScrollView>

        {/* Payment Buttons */}
        <View style={styles.paymentButtonContainer}>
          <TouchableOpacity
            style={[styles.paymentButton, {
              backgroundColor: validationResults.isValid && !processing ?
                theme.colors.primary : theme.colors.border
            }]}
            onPress={processPayment}
            disabled={!validationResults.isValid || processing}
          >
            {processing ? (
              <Text style={styles.paymentButtonText}>Processing Payment...</Text>
            ) : (
              <Text style={styles.paymentButtonText}>
                Pay {formData.amount ? formatCurrency(parseFloat(formData.amount) || 0) : 'Bill'}
              </Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.recurringButton}
            onPress={() => navigation.navigate('RecurringPaymentSetup', {
              billerId,
              billerName,
              accountNumber: formData.accountNumber,
              accountName: formData.accountName
            })}
          >
            <Ionicons name="repeat" size={20} color={theme.colors.primary} />
            <Text style={styles.recurringButtonText}>Set Up Recurring Payment</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  keyboardContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  billerInfo: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  billerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  billerIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  billerDetails: {
    flex: 1,
  },
  billerName: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  billerCategory: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  billerLimits: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  billerStatus: {
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    marginRight: 12,
  },
  verifyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  verifyButtonText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  verificationInfo: {
    backgroundColor: theme.colors.success + '20',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  verificationText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 2,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencySymbol: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  quickAmountButton: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
    alignItems: 'center',
  },
  quickAmountText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  validationSection: {
    marginBottom: 20,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  errorItem: {
    backgroundColor: theme.colors.error + '20',
  },
  warningItem: {
    backgroundColor: theme.colors.warning + '20',
  },
  validationText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  errorText: {
    color: theme.colors.error,
  },
  warningText: {
    color: theme.colors.warning,
  },
  summarySection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 8,
    marginTop: 4,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  paymentButtonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  paymentButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  recurringButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.background,
  },
  recurringButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.primary,
    marginLeft: 8,
  },
});

export default BillPaymentFormScreen;
