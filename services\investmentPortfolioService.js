/**
 * Investment Portfolio Service
 * Comprehensive service for managing investment portfolios with
 * holdings tracking, performance analytics, and portfolio optimization
 */

import { supabase } from './supabaseClient';
import marketDataService from './marketDataService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { isValidUUID, requireAuthentication } from '../utils/userUtils';

class InvestmentPortfolioService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
    this.portfolioUpdateInterval = 15 * 60 * 1000; // 15 minutes
    
    // Start background processors
    this.startPortfolioUpdater();
  }

  /**
   * Create a new investment portfolio
   */
  async createPortfolio(userId, portfolioData) {
    try {
      console.log('📈 Creating investment portfolio:', { userId, portfolioData });

      // Validate user ID
      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      // Validate portfolio data
      const validation = this.validatePortfolioData(portfolioData);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', ')
        };
      }

      // Create portfolio
      const { data: portfolio, error } = await supabase
        .from('investment_portfolios')
        .insert({
          user_id: userId,
          portfolio_name: portfolioData.portfolioName,
          portfolio_type: portfolioData.portfolioType || 'general',
          currency: portfolioData.currency || 'USD',
          risk_level: portfolioData.riskLevel || 'moderate',
          risk_score: portfolioData.riskScore || 5.0,
          is_managed: portfolioData.isManaged || false,
          description: portfolioData.description,
          metadata: {
            created_via: 'mobile_app',
            initial_cash: portfolioData.initialCash || 0,
            investment_strategy: portfolioData.investmentStrategy
          }
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating portfolio:', error);
        return {
          success: false,
          error: 'Failed to create portfolio'
        };
      }

      // Add initial cash if provided
      if (portfolioData.initialCash && portfolioData.initialCash > 0) {
        await this.addCashToPortfolio(portfolio.id, portfolioData.initialCash, 'Initial deposit');
      }

      // Clear cache
      this.clearUserCache(userId);

      // Send confirmation notification
      try {
        await enhancedNotificationService.sendNotification(userId, {
          type: 'portfolio_created',
          title: 'Investment Portfolio Created',
          content: `Your ${portfolioData.portfolioName} portfolio has been created successfully!`,
          data: {
            portfolioId: portfolio.id,
            portfolioName: portfolioData.portfolioName,
            portfolioType: portfolioData.portfolioType
          }
        });
      } catch (notificationError) {
        console.warn('⚠️ Failed to send portfolio creation notification:', notificationError);
      }

      console.log('✅ Investment portfolio created:', portfolio.id);

      return {
        success: true,
        portfolio: this.formatPortfolioResponse(portfolio)
      };
    } catch (error) {
      console.error('❌ Error creating investment portfolio:', error);
      return {
        success: false,
        error: 'Failed to create portfolio'
      };
    }
  }

  /**
   * Get user's investment portfolios
   */
  async getUserPortfolios(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      // Check cache first
      const cacheKey = this.getCacheKey(userId, 'portfolios', options);
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        return { success: true, portfolios: cached };
      }

      console.log('📈 Fetching investment portfolios for user:', userId);

      let query = supabase
        .from('investment_portfolios')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options.portfolioType) {
        query = query.eq('portfolio_type', options.portfolioType);
      }
      if (options.isActive !== undefined) {
        query = query.eq('is_active', options.isActive);
      }

      const { data: portfolios, error } = await query;

      if (error) {
        console.error('❌ Error fetching portfolios:', error);
        return {
          success: false,
          error: 'Failed to fetch portfolios'
        };
      }

      // Get holdings for each portfolio
      const portfoliosWithHoldings = await Promise.all(
        portfolios.map(async (portfolio) => {
          const holdingsResult = await this.getPortfolioHoldings(portfolio.id);
          return {
            ...this.formatPortfolioResponse(portfolio),
            holdings: holdingsResult.success ? holdingsResult.holdings : [],
            holdingsCount: holdingsResult.success ? holdingsResult.holdings.length : 0
          };
        })
      );

      // Cache the result
      this.setCachedData(cacheKey, portfoliosWithHoldings);

      console.log(`✅ Found ${portfoliosWithHoldings.length} investment portfolios`);

      return {
        success: true,
        portfolios: portfoliosWithHoldings
      };
    } catch (error) {
      console.error('❌ Error getting user portfolios:', error);
      return {
        success: false,
        error: 'Failed to fetch portfolios'
      };
    }
  }

  /**
   * Get portfolio details with holdings and performance
   */
  async getPortfolioDetails(portfolioId, userId) {
    try {
      if (!portfolioId || !isValidUUID(portfolioId)) {
        return {
          success: false,
          error: 'Valid portfolio ID is required'
        };
      }

      if (!userId || !isValidUUID(userId)) {
        return {
          success: false,
          error: 'Valid user ID is required'
        };
      }

      console.log('📈 Fetching portfolio details:', portfolioId);

      const { data: portfolio, error } = await supabase
        .from('investment_portfolios')
        .select('*')
        .eq('id', portfolioId)
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('❌ Error fetching portfolio details:', error);
        return {
          success: false,
          error: 'Portfolio not found'
        };
      }

      // Get holdings with current market data
      const holdingsResult = await this.getPortfolioHoldings(portfolioId);
      const holdings = holdingsResult.success ? holdingsResult.holdings : [];

      // Get recent transactions
      const transactionsResult = await this.getPortfolioTransactions(portfolioId, { limit: 10 });
      const recentTransactions = transactionsResult.success ? transactionsResult.transactions : [];

      // Calculate performance metrics
      const performance = await this.calculatePortfolioPerformance(portfolioId);

      // Get asset allocation
      const allocation = await this.calculateAssetAllocation(portfolioId);

      return {
        success: true,
        portfolio: {
          ...this.formatPortfolioResponse(portfolio),
          holdings,
          recentTransactions,
          performance,
          allocation,
          holdingsCount: holdings.length
        }
      };
    } catch (error) {
      console.error('❌ Error getting portfolio details:', error);
      return {
        success: false,
        error: 'Failed to fetch portfolio details'
      };
    }
  }

  /**
   * Get portfolio holdings with current market data
   */
  async getPortfolioHoldings(portfolioId) {
    try {
      if (!portfolioId || !isValidUUID(portfolioId)) {
        return {
          success: false,
          error: 'Valid portfolio ID is required'
        };
      }

      const { data: holdings, error } = await supabase
        .from('portfolio_holdings')
        .select(`
          *,
          asset:investment_assets(*)
        `)
        .eq('portfolio_id', portfolioId)
        .eq('is_active', true)
        .gt('quantity', 0)
        .order('current_value', { ascending: false });

      if (error) {
        console.error('❌ Error fetching holdings:', error);
        return {
          success: false,
          error: 'Failed to fetch holdings'
        };
      }

      // Update holdings with current market data
      const updatedHoldings = await Promise.all(
        holdings.map(async (holding) => {
          if (holding.asset) {
            // Get current market price
            const marketData = await marketDataService.getAssetPrice(holding.asset.symbol);
            if (marketData.success) {
              const currentPrice = marketData.price;
              const currentValue = holding.quantity * currentPrice;
              const unrealizedGainLoss = currentValue - holding.total_cost;
              const unrealizedGainLossPercent = holding.total_cost > 0 ? 
                (unrealizedGainLoss / holding.total_cost) * 100 : 0;

              // Update holding in database
              await supabase
                .from('portfolio_holdings')
                .update({
                  current_value: currentValue,
                  unrealized_gain_loss: unrealizedGainLoss,
                  unrealized_gain_loss_percent: unrealizedGainLossPercent,
                  updated_at: new Date().toISOString()
                })
                .eq('id', holding.id);

              return {
                ...this.formatHoldingResponse(holding),
                currentPrice,
                currentValue,
                unrealizedGainLoss,
                unrealizedGainLossPercent
              };
            }
          }
          return this.formatHoldingResponse(holding);
        })
      );

      return {
        success: true,
        holdings: updatedHoldings
      };
    } catch (error) {
      console.error('❌ Error getting portfolio holdings:', error);
      return {
        success: false,
        error: 'Failed to fetch holdings'
      };
    }
  }

  /**
   * Calculate portfolio performance metrics
   */
  async calculatePortfolioPerformance(portfolioId) {
    try {
      // Use the database function for performance calculation
      const { data: performance, error } = await supabase
        .rpc('calculate_portfolio_performance', { p_portfolio_id: portfolioId });

      if (error) {
        console.error('❌ Error calculating performance:', error);
        return {
          totalValue: 0,
          totalInvested: 0,
          totalReturn: 0,
          returnPercentage: 0
        };
      }

      const result = performance[0] || {};
      
      // Calculate additional metrics
      const dayChange = await this.calculateDayChange(portfolioId);
      const monthChange = await this.calculatePeriodChange(portfolioId, 30);
      const yearChange = await this.calculatePeriodChange(portfolioId, 365);

      return {
        totalValue: parseFloat(result.total_value || 0),
        totalInvested: parseFloat(result.total_invested || 0),
        totalReturn: parseFloat(result.total_return || 0),
        returnPercentage: parseFloat(result.return_percentage || 0),
        dayChange: dayChange || 0,
        monthChange: monthChange || 0,
        yearChange: yearChange || 0
      };
    } catch (error) {
      console.error('❌ Error calculating portfolio performance:', error);
      return {
        totalValue: 0,
        totalInvested: 0,
        totalReturn: 0,
        returnPercentage: 0,
        dayChange: 0,
        monthChange: 0,
        yearChange: 0
      };
    }
  }

  /**
   * Calculate asset allocation
   */
  async calculateAssetAllocation(portfolioId) {
    try {
      const holdingsResult = await this.getPortfolioHoldings(portfolioId);
      if (!holdingsResult.success) {
        return [];
      }

      const holdings = holdingsResult.holdings;
      const totalValue = holdings.reduce((sum, holding) => sum + (holding.currentValue || 0), 0);

      if (totalValue === 0) {
        return [];
      }

      // Group by asset type
      const allocation = {};
      holdings.forEach(holding => {
        const assetType = holding.asset?.asset_type || 'unknown';
        if (!allocation[assetType]) {
          allocation[assetType] = {
            type: assetType,
            value: 0,
            percentage: 0,
            count: 0
          };
        }
        allocation[assetType].value += holding.currentValue || 0;
        allocation[assetType].count += 1;
      });

      // Calculate percentages
      Object.values(allocation).forEach(item => {
        item.percentage = (item.value / totalValue) * 100;
      });

      return Object.values(allocation).sort((a, b) => b.percentage - a.percentage);
    } catch (error) {
      console.error('❌ Error calculating asset allocation:', error);
      return [];
    }
  }

  /**
   * Add cash to portfolio
   */
  async addCashToPortfolio(portfolioId, amount, description = 'Cash deposit') {
    try {
      const { data: portfolio, error: portfolioError } = await supabase
        .from('investment_portfolios')
        .select('*')
        .eq('id', portfolioId)
        .single();

      if (portfolioError || !portfolio) {
        return { success: false, error: 'Portfolio not found' };
      }

      // Update portfolio cash balance
      await supabase
        .from('investment_portfolios')
        .update({
          cash_balance: portfolio.cash_balance + amount,
          updated_at: new Date().toISOString()
        })
        .eq('id', portfolioId);

      // Create transaction record
      const reference = await this.generateTransactionReference();
      await supabase
        .from('investment_transactions')
        .insert({
          portfolio_id: portfolioId,
          user_id: portfolio.user_id,
          transaction_type: 'deposit',
          total_amount: amount,
          net_amount: amount,
          reference_number: reference,
          status: 'executed',
          executed_at: new Date().toISOString(),
          notes: description
        });

      return { success: true };
    } catch (error) {
      console.error('❌ Error adding cash to portfolio:', error);
      return { success: false, error: 'Failed to add cash' };
    }
  }

  /**
   * Get portfolio transactions
   */
  async getPortfolioTransactions(portfolioId, options = {}) {
    try {
      let query = supabase
        .from('investment_transactions')
        .select(`
          *,
          asset:investment_assets(symbol, name)
        `)
        .eq('portfolio_id', portfolioId)
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }

      const { data: transactions, error } = await query;

      if (error) {
        console.error('❌ Error fetching transactions:', error);
        return { success: false, error: 'Failed to fetch transactions' };
      }

      return {
        success: true,
        transactions: transactions.map(t => this.formatTransactionResponse(t))
      };
    } catch (error) {
      console.error('❌ Error getting portfolio transactions:', error);
      return { success: false, error: 'Failed to fetch transactions' };
    }
  }

  /**
   * Utility Methods
   */

  validatePortfolioData(data) {
    const errors = [];

    if (!data.portfolioName || data.portfolioName.trim().length < 2) {
      errors.push('Portfolio name must be at least 2 characters');
    }

    if (data.initialCash && data.initialCash < 0) {
      errors.push('Initial cash cannot be negative');
    }

    if (data.riskScore && (data.riskScore < 1 || data.riskScore > 10)) {
      errors.push('Risk score must be between 1 and 10');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async generateTransactionReference() {
    const prefix = 'INV';
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${timestamp}${random}`;
  }

  async calculateDayChange(portfolioId) {
    // Implementation for day change calculation
    // This would typically involve comparing current value with previous day's close
    return 0; // Placeholder
  }

  async calculatePeriodChange(portfolioId, days) {
    // Implementation for period change calculation
    // This would involve historical data comparison
    return 0; // Placeholder
  }

  formatPortfolioResponse(portfolio) {
    return {
      id: portfolio.id,
      portfolioName: portfolio.portfolio_name,
      portfolioType: portfolio.portfolio_type,
      currency: portfolio.currency,
      totalValue: parseFloat(portfolio.total_value || 0),
      totalInvested: parseFloat(portfolio.total_invested || 0),
      totalGainsLosses: parseFloat(portfolio.total_gains_losses || 0),
      cashBalance: parseFloat(portfolio.cash_balance || 0),
      dailyReturn: parseFloat(portfolio.daily_return || 0),
      totalReturn: parseFloat(portfolio.total_return || 0),
      annualizedReturn: parseFloat(portfolio.annualized_return || 0),
      riskLevel: portfolio.risk_level,
      riskScore: parseFloat(portfolio.risk_score || 0),
      isActive: portfolio.is_active,
      isManaged: portfolio.is_managed,
      description: portfolio.description,
      metadata: portfolio.metadata,
      createdAt: portfolio.created_at,
      updatedAt: portfolio.updated_at
    };
  }

  formatHoldingResponse(holding) {
    return {
      id: holding.id,
      portfolioId: holding.portfolio_id,
      assetId: holding.asset_id,
      asset: holding.asset ? {
        id: holding.asset.id,
        symbol: holding.asset.symbol,
        name: holding.asset.name,
        assetType: holding.asset.asset_type,
        currentPrice: parseFloat(holding.asset.current_price || 0),
        dayChange: parseFloat(holding.asset.day_change || 0),
        dayChangePercent: parseFloat(holding.asset.day_change_percent || 0)
      } : null,
      quantity: parseFloat(holding.quantity || 0),
      averageCost: parseFloat(holding.average_cost || 0),
      totalCost: parseFloat(holding.total_cost || 0),
      currentValue: parseFloat(holding.current_value || 0),
      unrealizedGainLoss: parseFloat(holding.unrealized_gain_loss || 0),
      unrealizedGainLossPercent: parseFloat(holding.unrealized_gain_loss_percent || 0),
      realizedGainLoss: parseFloat(holding.realized_gain_loss || 0),
      portfolioWeight: parseFloat(holding.portfolio_weight || 0),
      firstPurchaseDate: holding.first_purchase_date,
      lastTransactionDate: holding.last_transaction_date,
      createdAt: holding.created_at,
      updatedAt: holding.updated_at
    };
  }

  formatTransactionResponse(transaction) {
    return {
      id: transaction.id,
      portfolioId: transaction.portfolio_id,
      assetId: transaction.asset_id,
      asset: transaction.asset,
      transactionType: transaction.transaction_type,
      quantity: parseFloat(transaction.quantity || 0),
      price: parseFloat(transaction.price || 0),
      totalAmount: parseFloat(transaction.total_amount || 0),
      fees: parseFloat(transaction.fees || 0),
      netAmount: parseFloat(transaction.net_amount || 0),
      orderType: transaction.order_type,
      orderId: transaction.order_id,
      executionPrice: parseFloat(transaction.execution_price || 0),
      status: transaction.status,
      executedAt: transaction.executed_at,
      referenceNumber: transaction.reference_number,
      externalReference: transaction.external_reference,
      notes: transaction.notes,
      metadata: transaction.metadata,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at
    };
  }

  /**
   * Cache Management
   */
  getCacheKey(userId, operation, params = {}) {
    const paramString = Object.keys(params).length > 0 ? JSON.stringify(params) : '';
    return `investment_${operation}_${userId}_${paramString}`;
  }

  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const [key] of this.cache) {
      if (key.includes(userId)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Background Processors
   */
  startPortfolioUpdater() {
    // Update portfolio values every 15 minutes during market hours
    setInterval(async () => {
      try {
        console.log('📈 Updating portfolio values...');
        await this.updateAllPortfolioValues();
      } catch (error) {
        console.error('❌ Error in portfolio updater:', error);
      }
    }, this.portfolioUpdateInterval);
  }

  async updateAllPortfolioValues() {
    try {
      const { data: portfolios, error } = await supabase
        .from('investment_portfolios')
        .select('id')
        .eq('is_active', true);

      if (error) {
        console.error('❌ Error fetching portfolios for update:', error);
        return;
      }

      for (const portfolio of portfolios) {
        await this.updatePortfolioValue(portfolio.id);
      }

      console.log(`✅ Updated ${portfolios.length} portfolios`);
    } catch (error) {
      console.error('❌ Error updating portfolio values:', error);
    }
  }

  async updatePortfolioValue(portfolioId) {
    try {
      const performance = await this.calculatePortfolioPerformance(portfolioId);

      await supabase
        .from('investment_portfolios')
        .update({
          total_value: performance.totalValue,
          total_invested: performance.totalInvested,
          total_gains_losses: performance.totalReturn,
          total_return: performance.returnPercentage,
          daily_return: performance.dayChange,
          updated_at: new Date().toISOString()
        })
        .eq('id', portfolioId);
    } catch (error) {
      console.error(`❌ Error updating portfolio ${portfolioId}:`, error);
    }
  }

  /**
   * Get user's investment transactions across all portfolios
   */
  async getUserTransactions(userId, options = {}) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required', transactions: [] };
      }

      let query = supabase
        .from('investment_transactions')
        .select(`
          *,
          asset:investment_assets(symbol, name, asset_type),
          portfolio:investment_portfolios(portfolio_name)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }
      if (options.portfolioId) {
        query = query.eq('portfolio_id', options.portfolioId);
      }
      if (options.startDate) {
        query = query.gte('created_at', options.startDate.toISOString());
      }
      if (options.endDate) {
        query = query.lte('created_at', options.endDate.toISOString());
      }

      const { data: transactions, error } = await query;

      if (error) {
        console.error('❌ Error fetching user transactions:', error);
        return { success: false, error: 'Failed to fetch transactions', transactions: [] };
      }

      // Format transactions with East African currency defaults
      const formattedTransactions = transactions.map(transaction => ({
        ...this.formatTransactionResponse(transaction),
        currency: transaction.currency || 'UGX', // Default to UGX for East Africa
        portfolioName: transaction.portfolio?.portfolio_name || 'Unknown Portfolio'
      }));

      return {
        success: true,
        transactions: formattedTransactions
      };
    } catch (error) {
      console.error('❌ Error getting user transactions:', error);
      return { success: false, error: 'Failed to fetch transactions', transactions: [] };
    }
  }

  /**
   * Get portfolio summary for user
   */
  async getPortfolioSummary(userId) {
    try {
      if (!userId || !isValidUUID(userId)) {
        return { success: false, error: 'Valid user ID is required' };
      }

      const { data: summary, error } = await supabase
        .from('portfolio_summary')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        console.error('❌ Error fetching portfolio summary:', error);
        return { success: false, error: 'Failed to fetch portfolio summary' };
      }

      return {
        success: true,
        summary: summary || {
          totalPortfolios: 0,
          totalPortfolioValue: 0,
          totalInvested: 0,
          totalGainsLosses: 0,
          averageReturn: 0,
          uniqueAssets: 0
        }
      };
    } catch (error) {
      console.error('❌ Error getting portfolio summary:', error);
      return { success: false, error: 'Failed to fetch portfolio summary' };
    }
  }
}

export default new InvestmentPortfolioService();
