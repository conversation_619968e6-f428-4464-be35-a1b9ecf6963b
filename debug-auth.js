// Debug authentication system
import authService from './services/authService';

const debugAuth = async () => {
  console.log('🔧 Starting authentication debug...');
  
  try {
    // Test 1: Initialize auth service
    console.log('1️⃣ Testing auth service initialization...');
    authService.initialize();
    console.log('✅ Auth service initialized');
    
    // Test 2: Test password login
    console.log('2️⃣ Testing password login...');
    const loginResult = await authService.loginWithPassword('777123456', 'password123', 'UG');
    console.log('Login result:', loginResult);
    
    if (loginResult.success) {
      console.log('✅ Password login successful');
      console.log('User:', loginResult.data.user);
    } else {
      console.log('❌ Password login failed:', loginResult.error);
    }
    
    // Test 3: Test OTP flow
    console.log('3️⃣ Testing OTP flow...');
    const otpResult = await authService.sendOTP('777123456', 'UG');
    console.log('OTP send result:', otpResult);
    
    if (otpResult.success) {
      console.log('✅ OTP sent successfully');
      
      // Test OTP verification
      const verifyResult = await authService.verifyOTP('777123456', '123456', 'UG');
      console.log('OTP verify result:', verifyResult);
      
      if (verifyResult.success) {
        console.log('✅ OTP verification successful');
      } else {
        console.log('❌ OTP verification failed:', verifyResult.error);
      }
    } else {
      console.log('❌ OTP send failed:', otpResult.error);
    }
    
    // Test 4: Check current user
    console.log('4️⃣ Testing current user...');
    const currentUser = authService.getCurrentUser();
    console.log('Current user:', currentUser);
    
    // Test 5: Check stored session
    console.log('5️⃣ Testing stored session...');
    const storedSession = await authService.getStoredSession();
    console.log('Stored session:', storedSession);
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
};

// Export for use in other files
export default debugAuth;

// Run debug if this file is executed directly
if (require.main === module) {
  debugAuth();
}
