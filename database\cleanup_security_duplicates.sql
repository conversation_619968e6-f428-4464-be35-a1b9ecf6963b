-- Cleanup Duplicate Security Settings
-- Run this in Supabase SQL editor to remove duplicate security settings

-- First, let's see what duplicates we have
SELECT 
    user_id,
    COUNT(*) as duplicate_count,
    array_agg(id ORDER BY created_at DESC) as setting_ids,
    array_agg(created_at ORDER BY created_at DESC) as created_dates
FROM public.security_settings 
GROUP BY user_id 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- Clean up duplicates - keep the most recent one for each user
WITH ranked_settings AS (
    SELECT 
        id,
        user_id,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
    FROM public.security_settings
),
duplicates_to_delete AS (
    SELECT id 
    FROM ranked_settings 
    WHERE rn > 1
)
DELETE FROM public.security_settings 
WHERE id IN (SELECT id FROM duplicates_to_delete);

-- Verify cleanup - should show no duplicates
SELECT 
    user_id,
    COUNT(*) as setting_count
FROM public.security_settings 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- Add unique constraint to prevent future duplicates
ALTER TABLE public.security_settings 
ADD CONSTRAINT unique_user_security_settings 
UNIQUE (user_id);

-- Success message
SELECT 'Security settings duplicates cleaned up successfully!' as status;
