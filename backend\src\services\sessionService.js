/**
 * Session Management Service
 * Handles user sessions, device tracking, and security monitoring
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');
const databaseService = require('./database');

class SessionService {
  constructor() {
    this.sessionPrefix = 'session:';
    this.devicePrefix = 'device:';
    this.userSessionsPrefix = 'user_sessions:';
  }

  /**
   * Create a new session
   */
  async createSession(userId, deviceInfo, ipAddress) {
    try {
      const sessionId = uuidv4();
      const deviceId = this.generateDeviceId(deviceInfo);
      
      const sessionData = {
        sessionId,
        userId,
        deviceId,
        deviceInfo: {
          userAgent: deviceInfo.userAgent,
          platform: deviceInfo.platform || 'unknown',
          appVersion: deviceInfo.appVersion || 'unknown',
          deviceModel: deviceInfo.deviceModel || 'unknown'
        },
        ipAddress,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true,
        loginCount: 1
      };

      // Store session data
      await redisService.setSession(sessionId, sessionData, 24 * 60 * 60); // 24 hours

      // Track device
      await this.trackDevice(userId, deviceId, deviceInfo, ipAddress);

      // Add to user's active sessions
      await this.addToUserSessions(userId, sessionId);

      logger.audit('Session created', {
        userId,
        sessionId,
        deviceId,
        ipAddress,
        userAgent: deviceInfo.userAgent
      });

      return { sessionId, deviceId };
    } catch (error) {
      logger.error('Failed to create session:', error);
      throw error;
    }
  }

  /**
   * Get session data
   */
  async getSession(sessionId) {
    try {
      return await redisService.getSession(sessionId);
    } catch (error) {
      logger.error('Failed to get session:', error);
      return null;
    }
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId, ipAddress = null) {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        return false;
      }

      sessionData.lastActivity = new Date().toISOString();
      sessionData.loginCount = (sessionData.loginCount || 0) + 1;
      
      if (ipAddress && ipAddress !== sessionData.ipAddress) {
        sessionData.ipAddress = ipAddress;
        
        // Log IP change for security monitoring
        logger.security('Session IP address changed', {
          userId: sessionData.userId,
          sessionId,
          oldIp: sessionData.ipAddress,
          newIp: ipAddress
        });
      }

      await redisService.setSession(sessionId, sessionData, 24 * 60 * 60);
      return true;
    } catch (error) {
      logger.error('Failed to update session activity:', error);
      return false;
    }
  }

  /**
   * Terminate a session
   */
  async terminateSession(sessionId, reason = 'logout') {
    try {
      const sessionData = await this.getSession(sessionId);
      if (!sessionData) {
        return false;
      }

      // Remove from user's active sessions
      await this.removeFromUserSessions(sessionData.userId, sessionId);

      // Delete session data
      await redisService.deleteSession(sessionId);

      logger.audit('Session terminated', {
        userId: sessionData.userId,
        sessionId,
        reason,
        duration: Date.now() - new Date(sessionData.createdAt).getTime()
      });

      return true;
    } catch (error) {
      logger.error('Failed to terminate session:', error);
      return false;
    }
  }

  /**
   * Get all active sessions for a user
   */
  async getUserSessions(userId) {
    try {
      const sessionIds = await redisService.get(`${this.userSessionsPrefix}${userId}`) || [];
      const sessions = [];

      for (const sessionId of sessionIds) {
        const sessionData = await this.getSession(sessionId);
        if (sessionData && sessionData.isActive) {
          sessions.push({
            sessionId,
            deviceInfo: sessionData.deviceInfo,
            ipAddress: sessionData.ipAddress,
            createdAt: sessionData.createdAt,
            lastActivity: sessionData.lastActivity
          });
        }
      }

      return sessions;
    } catch (error) {
      logger.error('Failed to get user sessions:', error);
      return [];
    }
  }

  /**
   * Terminate all sessions for a user except current
   */
  async terminateAllUserSessions(userId, excludeSessionId = null) {
    try {
      const sessionIds = await redisService.get(`${this.userSessionsPrefix}${userId}`) || [];
      let terminatedCount = 0;

      for (const sessionId of sessionIds) {
        if (sessionId !== excludeSessionId) {
          const success = await this.terminateSession(sessionId, 'force_logout');
          if (success) terminatedCount++;
        }
      }

      logger.audit('All user sessions terminated', {
        userId,
        excludeSessionId,
        terminatedCount
      });

      return terminatedCount;
    } catch (error) {
      logger.error('Failed to terminate all user sessions:', error);
      return 0;
    }
  }

  /**
   * Check for suspicious activity
   */
  async checkSuspiciousActivity(userId, deviceInfo, ipAddress) {
    try {
      const recentSessions = await this.getUserSessions(userId);
      const suspiciousIndicators = [];

      // Check for multiple simultaneous sessions from different IPs
      const uniqueIPs = new Set(recentSessions.map(s => s.ipAddress));
      if (uniqueIPs.size > 3) {
        suspiciousIndicators.push('multiple_ips');
      }

      // Check for rapid session creation
      const recentSessionsCount = recentSessions.filter(s => 
        Date.now() - new Date(s.createdAt).getTime() < 60 * 60 * 1000 // Last hour
      ).length;
      
      if (recentSessionsCount > 5) {
        suspiciousIndicators.push('rapid_sessions');
      }

      // Check for unusual device
      const knownDevices = await this.getUserDevices(userId);
      const currentDeviceId = this.generateDeviceId(deviceInfo);
      const isKnownDevice = knownDevices.some(d => d.deviceId === currentDeviceId);
      
      if (!isKnownDevice) {
        suspiciousIndicators.push('unknown_device');
      }

      if (suspiciousIndicators.length > 0) {
        logger.security('Suspicious activity detected', {
          userId,
          indicators: suspiciousIndicators,
          ipAddress,
          deviceInfo
        });
      }

      return {
        isSuspicious: suspiciousIndicators.length > 0,
        indicators: suspiciousIndicators
      };
    } catch (error) {
      logger.error('Failed to check suspicious activity:', error);
      return { isSuspicious: false, indicators: [] };
    }
  }

  /**
   * Track device information
   */
  async trackDevice(userId, deviceId, deviceInfo, ipAddress) {
    try {
      const deviceKey = `${this.devicePrefix}${userId}:${deviceId}`;
      const existingDevice = await redisService.get(deviceKey);

      const deviceData = {
        deviceId,
        userId,
        deviceInfo,
        firstSeen: existingDevice?.firstSeen || new Date().toISOString(),
        lastSeen: new Date().toISOString(),
        lastIpAddress: ipAddress,
        loginCount: (existingDevice?.loginCount || 0) + 1,
        isTrusted: existingDevice?.isTrusted || false
      };

      await redisService.set(deviceKey, deviceData, 30 * 24 * 60 * 60); // 30 days

      // Add to user's devices list
      const userDevicesKey = `user_devices:${userId}`;
      const userDevices = await redisService.get(userDevicesKey) || [];
      
      if (!userDevices.includes(deviceId)) {
        userDevices.push(deviceId);
        await redisService.set(userDevicesKey, userDevices, 30 * 24 * 60 * 60);
      }

      return deviceData;
    } catch (error) {
      logger.error('Failed to track device:', error);
      return null;
    }
  }

  /**
   * Get user devices
   */
  async getUserDevices(userId) {
    try {
      const userDevicesKey = `user_devices:${userId}`;
      const deviceIds = await redisService.get(userDevicesKey) || [];
      const devices = [];

      for (const deviceId of deviceIds) {
        const deviceKey = `${this.devicePrefix}${userId}:${deviceId}`;
        const deviceData = await redisService.get(deviceKey);
        if (deviceData) {
          devices.push(deviceData);
        }
      }

      return devices;
    } catch (error) {
      logger.error('Failed to get user devices:', error);
      return [];
    }
  }

  /**
   * Generate device ID from device info
   */
  generateDeviceId(deviceInfo) {
    const { userAgent, platform, deviceModel } = deviceInfo;
    const deviceString = `${userAgent || ''}-${platform || ''}-${deviceModel || ''}`;
    
    // Create a hash of the device string
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(deviceString).digest('hex').substring(0, 16);
  }

  /**
   * Add session to user's active sessions
   */
  async addToUserSessions(userId, sessionId) {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessions = await redisService.get(userSessionsKey) || [];
      
      if (!sessions.includes(sessionId)) {
        sessions.push(sessionId);
        await redisService.set(userSessionsKey, sessions, 24 * 60 * 60);
      }
    } catch (error) {
      logger.error('Failed to add to user sessions:', error);
    }
  }

  /**
   * Remove session from user's active sessions
   */
  async removeFromUserSessions(userId, sessionId) {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessions = await redisService.get(userSessionsKey) || [];
      const updatedSessions = sessions.filter(id => id !== sessionId);
      
      await redisService.set(userSessionsKey, updatedSessions, 24 * 60 * 60);
    } catch (error) {
      logger.error('Failed to remove from user sessions:', error);
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions() {
    try {
      // This would typically be run as a scheduled job
      logger.info('Session cleanup completed');
    } catch (error) {
      logger.error('Failed to cleanup expired sessions:', error);
    }
  }
}

// Create singleton instance
const sessionService = new SessionService();

module.exports = sessionService;
