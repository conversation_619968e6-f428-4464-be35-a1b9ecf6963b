# 🔒 Secure Multi-Environment Setup Guide for JiraniPay

This guide explains how to set up secure, isolated environments for JiraniPay development, staging, and production.

## 🚨 Security Overview

### Previous Issues (FIXED)
- ❌ Hardcoded Supabase credentials in source code
- ❌ Same Supabase project used for dev and production
- ❌ Credentials committed to version control
- ❌ No environment isolation

### New Security Features (IMPLEMENTED)
- ✅ Separate Supabase projects for each environment
- ✅ Environment variables only (no hardcoded credentials)
- ✅ Secure configuration validation
- ✅ Proper .gitignore for credential files
- ✅ Environment-specific feature flags

## 🏗️ Environment Architecture

```
Development Environment
├── Supabase Project: jiranipay-dev
├── Database: Development data only
├── Features: Debug mode, test data, mock services
└── Security: Relaxed for development

Staging Environment (Optional)
├── Supabase Project: jiranipay-staging
├── Database: Production-like test data
├── Features: Production features with debug logging
└── Security: Production-like with test credentials

Production Environment
├── Supabase Project: jiranipay-production
├── Database: Real user data
├── Features: Full production features
└── Security: Maximum security, real credentials
```

## 📋 Setup Instructions

### Step 1: Create Separate Supabase Projects

1. **Development Project**
   ```bash
   # Go to https://supabase.com/dashboard
   # Create new project: "jiranipay-dev"
   # Note down the URL and anon key
   ```

2. **Staging Project (Optional)**
   ```bash
   # Create new project: "jiranipay-staging"
   # Note down the URL and anon key
   ```

3. **Production Project**
   ```bash
   # Create new project: "jiranipay-production"
   # Note down the URL and anon key
   ```

### Step 2: Configure Environment Files

1. **Development Environment**
   ```bash
   # Copy template to actual environment file
   cp .env.example .env.development.local
   
   # Edit .env.development.local with your development credentials
   EXPO_PUBLIC_SUPABASE_URL=https://your-dev-project.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your-dev-anon-key
   ```

2. **Production Environment**
   ```bash
   # Copy template to actual environment file
   cp .env.production.template .env.production.local
   
   # Edit .env.production.local with your production credentials
   EXPO_PUBLIC_PROD_SUPABASE_URL=https://your-prod-project.supabase.co
   EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY=your-prod-anon-key
   ```

### Step 3: Backend Configuration

1. **Backend Development**
   ```bash
   cd backend
   cp .env.example .env.development.local
   
   # Edit backend/.env.development.local
   SUPABASE_URL=https://your-dev-project.supabase.co
   SUPABASE_ANON_KEY=your-dev-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-dev-service-role-key
   ```

2. **Backend Production**
   ```bash
   # For production deployment, set environment variables in your deployment platform
   # DO NOT create .env.production.local in production
   ```

### Step 4: Database Schema Setup

Run the database setup for each environment:

```bash
# Development
psql -h db.your-dev-project.supabase.co -U postgres -f database/complete_setup.sql

# Staging
psql -h db.your-staging-project.supabase.co -U postgres -f database/complete_setup.sql

# Production
psql -h db.your-prod-project.supabase.co -U postgres -f database/complete_setup.sql
```

## 🔧 Usage

### Development
```bash
# Set environment
export EXPO_PUBLIC_ENVIRONMENT=development
npm start
```

### Staging
```bash
# Set environment
export EXPO_PUBLIC_ENVIRONMENT=staging
npm start
```

### Production
```bash
# Set environment
export EXPO_PUBLIC_ENVIRONMENT=production
npm run build
```

## 🛡️ Security Best Practices

### 1. Credential Management
- ✅ Use environment variables exclusively
- ✅ Never commit actual credentials to git
- ✅ Use separate projects for each environment
- ✅ Rotate credentials regularly

### 2. Environment Isolation
- ✅ Separate databases for each environment
- ✅ Different API endpoints per environment
- ✅ Environment-specific feature flags
- ✅ Isolated user data

### 3. Deployment Security
- ✅ Use deployment platform secret management
- ✅ Enable certificate pinning in production
- ✅ Implement proper logging and monitoring
- ✅ Regular security audits

## 🚀 Deployment

### Development
```bash
# Local development
npm start
```

### Staging
```bash
# Deploy to staging environment
expo build:web --release-channel staging
```

### Production
```bash
# Deploy to production
expo build:web --release-channel production
```

## 🔍 Validation

The new configuration system includes automatic validation:

```javascript
import secureConfig from './config/secureEnvironment';

// Validates configuration on import
const errors = secureConfig.validate();
if (errors.length > 0) {
  console.error('Configuration errors:', errors);
}
```

## 📞 Support

If you encounter issues with the new secure configuration:

1. Check that all required environment variables are set
2. Verify Supabase project URLs and keys are correct
3. Ensure .env.*.local files are not committed to git
4. Review the console for configuration validation errors

## 🔄 Migration from Old Setup

If you're migrating from the old hardcoded setup:

1. Create new Supabase projects for each environment
2. Copy data from old project to new projects (if needed)
3. Update all environment files with new credentials
4. Test each environment thoroughly
5. Update deployment configurations
6. Remove old hardcoded credentials from codebase

## ⚠️ Important Notes

- **NEVER** commit files ending in `.local` to version control
- **ALWAYS** use separate Supabase projects for different environments
- **REGULARLY** rotate API keys and credentials
- **MONITOR** for any credential leaks in logs or error messages
