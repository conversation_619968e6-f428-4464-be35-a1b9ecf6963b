/**
 * Budget Alert Banner Component
 * Displays real-time budget alerts and warnings
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

// Constants
import { Colors } from '../../constants/Colors';

const BudgetAlertBanner = ({ 
  alerts, 
  onDismiss, 
  onAlertPress, 
  theme,
  style 
}) => {
  if (!alerts || alerts.length === 0) return null;

  // Get the most critical alert
  const criticalAlert = alerts.find(alert => alert.type === 'over_budget') || alerts[0];

  // Get alert styling based on type
  const getAlertStyle = (alertType) => {
    switch (alertType) {
      case 'over_budget':
        return {
          backgroundColor: Colors.status.error + '15',
          borderColor: Colors.status.error,
          iconColor: Colors.status.error,
          icon: 'warning'
        };
      case 'near_limit':
        return {
          backgroundColor: Colors.status.warning + '15',
          borderColor: Colors.status.warning,
          iconColor: Colors.status.warning,
          icon: 'alert-circle'
        };
      default:
        return {
          backgroundColor: Colors.primary.main + '15',
          borderColor: Colors.primary.main,
          iconColor: Colors.primary.main,
          icon: 'information-circle'
        };
    }
  };

  const alertStyle = getAlertStyle(criticalAlert.type);

  const handleAlertPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (onAlertPress) {
      onAlertPress(criticalAlert);
    }
  };

  const handleDismiss = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    if (onDismiss) {
      onDismiss(0); // Dismiss the first alert
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        {
          backgroundColor: alertStyle.backgroundColor,
          borderLeftColor: alertStyle.borderColor
        },
        style
      ]}
      onPress={handleAlertPress}
      activeOpacity={0.8}
    >
      {/* Alert Icon */}
      <View style={[styles.iconContainer, { backgroundColor: alertStyle.iconColor + '20' }]}>
        <Ionicons 
          name={alertStyle.icon} 
          size={20} 
          color={alertStyle.iconColor} 
        />
      </View>

      {/* Alert Content */}
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {criticalAlert.type === 'over_budget' ? 'Budget Exceeded' : 'Budget Warning'}
          </Text>
          {alerts.length > 1 && (
            <View style={[styles.countBadge, { backgroundColor: alertStyle.iconColor }]}>
              <Text style={styles.countText}>
                {alerts.length}
              </Text>
            </View>
          )}
        </View>
        
        <Text style={[styles.message, { color: theme.colors.textSecondary }]}>
          {criticalAlert.message}
        </Text>
        
        <View style={styles.details}>
          <Text style={[styles.budgetName, { color: alertStyle.iconColor }]}>
            {criticalAlert.budgetName}
          </Text>
          <Text style={[styles.utilization, { color: theme.colors.textSecondary }]}>
            {criticalAlert.utilization.toFixed(1)}% used
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleDismiss}
        >
          <Ionicons name="close" size={18} color={theme.colors.textSecondary} />
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleAlertPress}
        >
          <Ionicons name="chevron-forward" size={18} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const BudgetAlertList = ({ 
  alerts, 
  onDismiss, 
  onAlertPress, 
  theme,
  maxVisible = 3 
}) => {
  if (!alerts || alerts.length === 0) return null;

  const visibleAlerts = alerts.slice(0, maxVisible);

  return (
    <View style={styles.listContainer}>
      {visibleAlerts.map((alert, index) => (
        <BudgetAlertItem
          key={index}
          alert={alert}
          index={index}
          onDismiss={onDismiss}
          onAlertPress={onAlertPress}
          theme={theme}
        />
      ))}
      
      {alerts.length > maxVisible && (
        <TouchableOpacity
          style={[styles.moreAlertsButton, { backgroundColor: theme.colors.surface }]}
          onPress={() => onAlertPress && onAlertPress({ type: 'view_all' })}
        >
          <Text style={[styles.moreAlertsText, { color: theme.colors.textSecondary }]}>
            +{alerts.length - maxVisible} more alerts
          </Text>
          <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const BudgetAlertItem = ({ alert, index, onDismiss, onAlertPress, theme }) => {
  const alertStyle = {
    'over_budget': {
      backgroundColor: Colors.status.error + '10',
      borderColor: Colors.status.error,
      iconColor: Colors.status.error,
      icon: 'warning'
    },
    'near_limit': {
      backgroundColor: Colors.status.warning + '10',
      borderColor: Colors.status.warning,
      iconColor: Colors.status.warning,
      icon: 'alert-circle'
    }
  }[alert.type] || {
    backgroundColor: Colors.primary.main + '10',
    borderColor: Colors.primary.main,
    iconColor: Colors.primary.main,
    icon: 'information-circle'
  };

  return (
    <TouchableOpacity
      style={[
        styles.alertItem,
        {
          backgroundColor: alertStyle.backgroundColor,
          borderLeftColor: alertStyle.borderColor
        }
      ]}
      onPress={() => onAlertPress && onAlertPress(alert)}
      activeOpacity={0.7}
    >
      <View style={[styles.alertIcon, { backgroundColor: alertStyle.iconColor + '20' }]}>
        <Ionicons 
          name={alertStyle.icon} 
          size={16} 
          color={alertStyle.iconColor} 
        />
      </View>
      
      <View style={styles.alertContent}>
        <Text style={[styles.alertTitle, { color: theme.colors.text }]}>
          {alert.budgetName}
        </Text>
        <Text style={[styles.alertMessage, { color: theme.colors.textSecondary }]}>
          {alert.utilization.toFixed(1)}% used
        </Text>
      </View>
      
      <TouchableOpacity
        style={styles.dismissButton}
        onPress={() => onDismiss && onDismiss(index)}
      >
        <Ionicons name="close" size={14} color={theme.colors.textSecondary} />
      </TouchableOpacity>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  countBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  countText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  message: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  details: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  budgetName: {
    fontSize: 12,
    fontWeight: '600',
  },
  utilization: {
    fontSize: 12,
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  // List styles
  listContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 3,
    marginBottom: 8,
  },
  alertIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  alertMessage: {
    fontSize: 12,
  },
  dismissButton: {
    padding: 4,
  },
  moreAlertsButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  moreAlertsText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BudgetAlertBanner;
export { BudgetAlertList, BudgetAlertItem };
