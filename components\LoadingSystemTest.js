import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useLoading } from '../contexts/LoadingContext';
import { Ionicons } from '@expo/vector-icons';
import loadingStateManager from '../services/loadingStateManager';

/**
 * Comprehensive test component for the enhanced loading system
 * Tests all critical loading transitions and splash screen scenarios
 */
const LoadingSystemTest = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { isGlobalLoading, showSplashScreen, loadingContext } = useLoading();
  const [activeStates, setActiveStates] = useState([]);
  const [testResults, setTestResults] = useState([]);

  useEffect(() => {
    // Listen to loading state changes
    const unsubscribe = loadingStateManager.addListener((stateChange) => {
      const active = loadingStateManager.getActiveLoadingStates();
      setActiveStates(active);
      
      console.log('🔄 Loading state changed:', stateChange);
    });

    return unsubscribe;
  }, []);

  const addTestResult = (testName, passed, details = '') => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      testName,
      passed,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  // Test scenarios
  const testScenarios = [
    {
      name: 'App Startup Flow',
      description: 'Simulate complete app startup sequence',
      action: () => {
        clearTestResults();
        addTestResult('Starting app startup test', true);
        
        // Simulate app startup
        loadingStateManager.setLoadingState('app_startup', true);
        loadingStateManager.setLoadingState('auth_initialization', true);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('auth_initialization', false);
          addTestResult('Auth initialization', true, 'Completed after 2s');
        }, 2000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('app_startup', false);
          addTestResult('App startup', true, 'Completed after 4s');
        }, 4000);
      }
    },
    {
      name: 'Login Flow',
      description: 'Simulate login → dashboard loading sequence',
      action: () => {
        clearTestResults();
        addTestResult('Starting login flow test', true);
        
        // Simulate login processing
        loadingStateManager.setLoadingState('login_processing', true);
        
        setTimeout(() => {
          // Login successful, start dashboard loading
          loadingStateManager.setLoadingState('login_processing', false);
          loadingStateManager.setLoadingState('dashboard_loading', true);
          loadingStateManager.setLoadingState('wallet_data_loading', true);
          loadingStateManager.setLoadingState('user_profile_loading', true);
          addTestResult('Login processing', true, 'Completed after 2s');
        }, 2000);
        
        setTimeout(() => {
          // Dashboard components loaded
          loadingStateManager.setLoadingState('wallet_data_loading', false);
          addTestResult('Wallet data loading', true, 'Completed after 3s');
        }, 3000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('user_profile_loading', false);
          addTestResult('User profile loading', true, 'Completed after 4s');
        }, 4000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('dashboard_loading', false);
          addTestResult('Dashboard loading', true, 'Completed after 5s');
        }, 5000);
      }
    },
    {
      name: 'Registration Flow',
      description: 'Simulate registration → profile setup sequence',
      action: () => {
        clearTestResults();
        addTestResult('Starting registration flow test', true);
        
        // Simulate registration processing
        loadingStateManager.setLoadingState('registration_processing', true);
        
        setTimeout(() => {
          // Registration successful, start profile setup
          loadingStateManager.setLoadingState('registration_processing', false);
          loadingStateManager.setLoadingState('profile_setup_loading', true);
          addTestResult('Registration processing', true, 'Completed after 3s');
        }, 3000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('profile_setup_loading', false);
          addTestResult('Profile setup', true, 'Completed after 5s');
        }, 5000);
      }
    },
    {
      name: 'Service Initialization',
      description: 'Simulate background service loading',
      action: () => {
        clearTestResults();
        addTestResult('Starting service initialization test', true);
        
        // Start all services
        loadingStateManager.setLoadingState('wallet_service_init', true);
        loadingStateManager.setLoadingState('savings_service_init', true);
        loadingStateManager.setLoadingState('bills_service_init', true);
        loadingStateManager.setLoadingState('currency_service_init', true);
        
        // Complete services one by one
        setTimeout(() => {
          loadingStateManager.setLoadingState('wallet_service_init', false);
          addTestResult('Wallet service', true, 'Initialized after 1s');
        }, 1000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('savings_service_init', false);
          addTestResult('Savings service', true, 'Initialized after 2s');
        }, 2000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('bills_service_init', false);
          addTestResult('Bills service', true, 'Initialized after 3s');
        }, 3000);
        
        setTimeout(() => {
          loadingStateManager.setLoadingState('currency_service_init', false);
          addTestResult('Currency service', true, 'Initialized after 4s');
        }, 4000);
      }
    },
    {
      name: 'Error Recovery',
      description: 'Test error handling and state cleanup',
      action: () => {
        clearTestResults();
        addTestResult('Starting error recovery test', true);
        
        // Start multiple loading states
        loadingStateManager.setLoadingState('login_processing', true);
        loadingStateManager.setLoadingState('dashboard_loading', true);
        
        setTimeout(() => {
          // Simulate error - reset all states
          loadingStateManager.resetAllStates();
          addTestResult('Error recovery', true, 'All states reset after 2s');
        }, 2000);
      }
    }
  ];

  const manualControls = [
    {
      name: 'App Startup',
      stateKey: 'app_startup',
      icon: 'rocket'
    },
    {
      name: 'Login Processing',
      stateKey: 'login_processing',
      icon: 'log-in'
    },
    {
      name: 'Dashboard Loading',
      stateKey: 'dashboard_loading',
      icon: 'grid'
    },
    {
      name: 'Registration',
      stateKey: 'registration_processing',
      icon: 'person-add'
    },
    {
      name: 'Verification',
      stateKey: 'verification_loading',
      icon: 'shield-checkmark'
    }
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Ionicons 
            name="settings" 
            size={32} 
            color={theme.colors.primary} 
            style={styles.headerIcon}
          />
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Loading System Test
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Test enhanced splash screen and loading transitions
          </Text>
        </View>

        {/* Current Status */}
        <View style={[styles.statusCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.statusTitle, { color: theme.colors.text }]}>
            Current Status
          </Text>
          <View style={styles.statusRow}>
            <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
              Global Loading:
            </Text>
            <Text style={[styles.statusValue, { 
              color: isGlobalLoading ? theme.colors.error : theme.colors.success 
            }]}>
              {isGlobalLoading ? 'Active' : 'Inactive'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
              Splash Screen:
            </Text>
            <Text style={[styles.statusValue, { 
              color: showSplashScreen ? theme.colors.primary : theme.colors.textSecondary 
            }]}>
              {showSplashScreen ? `Visible (${loadingContext})` : 'Hidden'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={[styles.statusLabel, { color: theme.colors.textSecondary }]}>
              Active States:
            </Text>
            <Text style={[styles.statusValue, { color: theme.colors.text }]}>
              {activeStates.length > 0 ? activeStates.join(', ') : 'None'}
            </Text>
          </View>
        </View>

        {/* Theme Toggle */}
        <View style={[styles.themeCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
            Theme Settings
          </Text>
          <View style={styles.themeRow}>
            <Text style={[styles.themeLabel, { color: theme.colors.textSecondary }]}>
              Dark Mode
            </Text>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: '#767577', true: theme.colors.primary }}
              thumbColor={isDarkMode ? '#f5dd4b' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* Manual Controls */}
        <View style={[styles.controlsCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
            Manual Controls
          </Text>
          <Text style={[styles.cardSubtitle, { color: theme.colors.textSecondary }]}>
            Toggle individual loading states
          </Text>
          
          {manualControls.map((control, index) => {
            const isActive = activeStates.includes(control.stateKey);
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.controlButton,
                  { 
                    backgroundColor: isActive ? theme.colors.primary : 'transparent',
                    borderColor: theme.colors.primary
                  }
                ]}
                onPress={() => {
                  loadingStateManager.setLoadingState(control.stateKey, !isActive);
                }}
              >
                <Ionicons
                  name={control.icon}
                  size={20}
                  color={isActive ? '#FFFFFF' : theme.colors.primary}
                />
                <Text style={[
                  styles.controlButtonText,
                  { color: isActive ? '#FFFFFF' : theme.colors.primary }
                ]}>
                  {control.name}
                </Text>
                <Text style={[
                  styles.controlStatus,
                  { color: isActive ? 'rgba(255,255,255,0.8)' : theme.colors.textSecondary }
                ]}>
                  {isActive ? 'ON' : 'OFF'}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Test Scenarios */}
        <View style={[styles.testsCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
            Automated Test Scenarios
          </Text>
          
          {testScenarios.map((scenario, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.testButton, { borderColor: theme.colors.primary }]}
              onPress={scenario.action}
            >
              <Text style={[styles.testButtonTitle, { color: theme.colors.text }]}>
                {scenario.name}
              </Text>
              <Text style={[styles.testButtonDescription, { color: theme.colors.textSecondary }]}>
                {scenario.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Test Results */}
        {testResults.length > 0 && (
          <View style={[styles.resultsCard, { backgroundColor: theme.colors.card }]}>
            <View style={styles.resultsHeader}>
              <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
                Test Results
              </Text>
              <TouchableOpacity
                style={[styles.clearButton, { borderColor: theme.colors.error }]}
                onPress={clearTestResults}
              >
                <Text style={[styles.clearButtonText, { color: theme.colors.error }]}>
                  Clear
                </Text>
              </TouchableOpacity>
            </View>
            
            {testResults.map((result) => (
              <View
                key={result.id}
                style={[
                  styles.resultItem,
                  { 
                    backgroundColor: theme.colors.background,
                    borderLeftColor: result.passed ? '#22c55e' : '#ef4444'
                  }
                ]}
              >
                <View style={styles.resultHeader}>
                  <Ionicons
                    name={result.passed ? 'checkmark-circle' : 'close-circle'}
                    size={16}
                    color={result.passed ? '#22c55e' : '#ef4444'}
                  />
                  <Text style={[styles.resultTitle, { color: theme.colors.text }]}>
                    {result.testName}
                  </Text>
                  <Text style={[styles.resultTime, { color: theme.colors.textSecondary }]}>
                    {result.timestamp}
                  </Text>
                </View>
                {result.details && (
                  <Text style={[styles.resultDetails, { color: theme.colors.textSecondary }]}>
                    {result.details}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Emergency Reset */}
        <TouchableOpacity
          style={[styles.emergencyButton, { backgroundColor: theme.colors.error }]}
          onPress={() => {
            Alert.alert(
              'Reset All States',
              'This will clear all loading states. Continue?',
              [
                { text: 'Cancel', style: 'cancel' },
                { 
                  text: 'Reset', 
                  style: 'destructive',
                  onPress: () => {
                    loadingStateManager.resetAllStates();
                    clearTestResults();
                    addTestResult('Emergency reset', true, 'All states cleared');
                  }
                }
              ]
            );
          }}
        >
          <Ionicons name="warning" size={20} color="#FFFFFF" />
          <Text style={styles.emergencyButtonText}>
            Emergency Reset All States
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerIcon: {
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  statusCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
    flex: 1,
    textAlign: 'right',
  },
  themeCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  cardSubtitle: {
    fontSize: 14,
    marginBottom: 12,
  },
  themeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  themeLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  controlsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  controlButtonText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  controlStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  testsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  testButton: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  testButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  testButtonDescription: {
    fontSize: 14,
  },
  resultsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  resultItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  resultTime: {
    fontSize: 12,
  },
  resultDetails: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 24,
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  emergencyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});

export default LoadingSystemTest;
