#!/usr/bin/env node

/**
 * Fix Missing Profiles Script for JiraniPay
 * 
 * This script identifies and fixes missing user profiles
 * by auto-creating them from Supabase auth user data.
 */

console.log('🔧 JiraniPay Missing Profiles Fix');
console.log('=================================\n');

async function fixMissingProfiles() {
  try {
    // Import services (using dynamic import for ES modules)
    const supabaseClient = require('../services/supabaseClient.js').default;
    const profileAutoCreationService = require('../services/profileAutoCreationService.js').default;
    
    console.log('✅ Services loaded successfully');
    
    // Check current user first
    console.log('\n🔍 Checking current authenticated user...');
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    
    if (userError) {
      console.log('❌ No authenticated user found:', userError.message);
      console.log('💡 Please log in to the app first, then run this script');
      return;
    }
    
    if (!user) {
      console.log('❌ No user session found');
      console.log('💡 Please log in to the app first, then run this script');
      return;
    }
    
    console.log('✅ Found authenticated user:', {
      id: user.id,
      email: user.email,
      phone: user.phone,
      created_at: user.created_at
    });
    
    // Check if profile exists
    console.log('\n📋 Checking if user profile exists...');
    const { data: existingProfile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (existingProfile && !profileError) {
      console.log('✅ User profile already exists:', {
        full_name: existingProfile.full_name,
        phone: existingProfile.phone,
        email: existingProfile.email,
        created_at: existingProfile.created_at
      });
      console.log('\n🎉 No action needed - profile exists!');
      return;
    }
    
    if (profileError && profileError.code !== 'PGRST116') {
      console.log('❌ Error checking profile:', profileError.message);
      return;
    }
    
    // Profile doesn't exist, create it
    console.log('⚠️ User profile not found - creating now...');
    
    const result = await profileAutoCreationService.ensureProfileExists(user);
    
    if (result.success) {
      console.log('✅ Profile created successfully!');
      console.log('📄 Profile data:', {
        full_name: result.data.full_name,
        phone: result.data.phone,
        email: result.data.email,
        country_code: result.data.country_code,
        created: result.created ? 'Yes' : 'Already existed'
      });
      
      // Verify the profile was created
      console.log('\n🔍 Verifying profile creation...');
      const { data: verifyProfile, error: verifyError } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (verifyProfile && !verifyError) {
        console.log('✅ Profile verification successful!');
        console.log('🎉 User profile is now available for the app');
      } else {
        console.log('❌ Profile verification failed:', verifyError?.message);
      }
      
    } else {
      console.log('❌ Profile creation failed:', result.error);
      console.log('\n🔧 Troubleshooting suggestions:');
      console.log('1. Check Supabase connection');
      console.log('2. Verify database schema exists');
      console.log('3. Check RLS policies allow profile creation');
      console.log('4. Ensure user has proper permissions');
    }
    
  } catch (error) {
    console.error('❌ Script error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Ensure you are logged in to the app');
    console.log('2. Check your internet connection');
    console.log('3. Verify Supabase configuration');
    console.log('4. Check console for detailed error messages');
  }
}

// Additional helper functions
function showUsage() {
  console.log('\n📖 USAGE:');
  console.log('1. First, log in to the JiraniPay app');
  console.log('2. Then run: npm run fix-profiles');
  console.log('3. The script will auto-create missing profile for current user');
  
  console.log('\n🔧 WHAT THIS SCRIPT DOES:');
  console.log('✅ Checks if current user has a profile');
  console.log('✅ Creates profile from auth user data if missing');
  console.log('✅ Sets up default preferences');
  console.log('✅ Verifies profile creation');
  
  console.log('\n⚠️ REQUIREMENTS:');
  console.log('- User must be logged in to the app');
  console.log('- Supabase connection must be working');
  console.log('- Database schema must exist');
  console.log('- RLS policies must allow profile creation');
}

function showResults() {
  console.log('\n🎯 EXPECTED RESULTS AFTER RUNNING:');
  console.log('✅ No more "no user ID for wallet data" errors');
  console.log('✅ No more "no profile data found" errors');
  console.log('✅ User greeting will show actual name');
  console.log('✅ Wallet, transactions, and insights will work');
  console.log('✅ Profile screen will load properly');
  
  console.log('\n🚀 NEXT STEPS:');
  console.log('1. Restart the JiraniPay app');
  console.log('2. Check that greeting shows your name');
  console.log('3. Verify wallet data loads correctly');
  console.log('4. Test profile screen functionality');
}

// Run the script
console.log('Starting profile fix process...\n');

fixMissingProfiles()
  .then(() => {
    showResults();
  })
  .catch((error) => {
    console.error('❌ Fatal error:', error.message);
    showUsage();
  });
