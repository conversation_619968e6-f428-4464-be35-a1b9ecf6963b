/**
 * Production Transaction Service
 * Handles real transaction processing, validation, and security for production environment
 */

import { isProductionMode } from '../config/environment';
import productionServices from '../config/productionServices';
import productionApiService from './productionApiService';
import supabase from './supabaseClient';
import authService from './authService';

class ProductionTransactionService {
  constructor() {
    this.transactionLimits = productionServices.security.transaction;
    this.encryptionConfig = productionServices.security.encryption;
  }

  /**
   * Validate transaction before processing
   */
  async validateTransaction(transactionData) {
    const { amount, type, userId, accountNumber } = transactionData;
    const errors = [];

    // Amount validation
    if (!amount || amount <= 0) {
      errors.push('Invalid transaction amount');
    }

    if (amount > this.transactionLimits.maxSingleTransaction) {
      errors.push(`Transaction amount exceeds limit of UGX ${this.transactionLimits.maxSingleTransaction.toLocaleString()}`);
    }

    // Daily limit check
    const dailyTotal = await this.getDailyTransactionTotal(userId);
    if (dailyTotal + amount > this.transactionLimits.maxDailyLimit) {
      errors.push(`Transaction would exceed daily limit of UGX ${this.transactionLimits.maxDailyLimit.toLocaleString()}`);
    }

    // Account validation
    if (type !== 'deposit' && !accountNumber) {
      errors.push('Account number is required');
    }

    // User authentication check
    const user = authService.getCurrentUser();
    if (!user || user.id !== userId) {
      errors.push('User authentication required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get daily transaction total for user
   */
  async getDailyTransactionTotal(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const { data, error } = await supabase
        .from('transactions')
        .select('amount')
        .eq('user_id', userId)
        .eq('status', 'completed')
        .gte('created_at', today.toISOString());

      if (error) throw error;

      return data.reduce((total, transaction) => total + parseFloat(transaction.amount), 0);
    } catch (error) {
      console.error('Error getting daily transaction total:', error);
      return 0;
    }
  }

  /**
   * Check if transaction requires additional security
   */
  requiresAdditionalSecurity(amount) {
    return {
      requiresBiometric: amount >= this.transactionLimits.requireBiometricAbove,
      requiresOTP: amount >= this.transactionLimits.requireOTPAbove,
      requiresKYC: amount >= productionServices.validation.kyc.requiredDocuments ? 1000000 : Infinity
    };
  }

  /**
   * Process transaction with production security
   */
  async processTransaction(transactionData) {
    try {
      // Validate transaction
      const validation = await this.validateTransaction(transactionData);
      if (!validation.isValid) {
        return { success: false, errors: validation.errors };
      }

      // Check security requirements
      const securityCheck = this.requiresAdditionalSecurity(transactionData.amount);
      
      if (isProductionMode()) {
        // Fraud detection check
        const fraudCheck = await productionApiService.checkTransactionForFraud(transactionData);
        if (!fraudCheck.success || !fraudCheck.data.approved) {
          await this.logSecurityEvent('fraud_detection_failed', transactionData);
          return { success: false, error: 'Transaction flagged for security review' };
        }

        // AML screening for high-value transactions
        if (transactionData.amount >= 1000000) { // UGX 1M
          const amlCheck = await productionApiService.performAMLCheck({
            userId: transactionData.userId,
            amount: transactionData.amount,
            type: transactionData.type
          });
          
          if (!amlCheck.success) {
            await this.logSecurityEvent('aml_check_failed', transactionData);
            return { success: false, error: 'Transaction requires compliance review' };
          }
        }
      }

      // Generate secure transaction reference
      const reference = this.generateSecureReference();
      
      // Create transaction record
      const transaction = {
        id: reference,
        user_id: transactionData.userId,
        type: transactionData.type,
        amount: transactionData.amount,
        currency: 'UGX',
        description: transactionData.description,
        account_number: transactionData.accountNumber,
        provider: transactionData.provider,
        status: 'pending',
        reference: reference,
        created_at: new Date().toISOString(),
        metadata: {
          security_level: this.getSecurityLevel(transactionData.amount),
          requires_biometric: securityCheck.requiresBiometric,
          requires_otp: securityCheck.requiresOTP,
          ip_address: transactionData.ipAddress,
          device_id: transactionData.deviceId,
          environment: isProductionMode() ? 'production' : 'development'
        }
      };

      // Save transaction to database
      const { data, error } = await supabase
        .from('transactions')
        .insert([transaction])
        .select()
        .single();

      if (error) throw error;

      // Log audit event
      await this.logAuditEvent('transaction_created', {
        transactionId: reference,
        userId: transactionData.userId,
        amount: transactionData.amount,
        type: transactionData.type
      });

      return {
        success: true,
        data: {
          transaction: data,
          reference,
          securityRequirements: securityCheck
        }
      };

    } catch (error) {
      console.error('Transaction processing error:', error);
      await this.logSecurityEvent('transaction_processing_error', { error: error.message, ...transactionData });
      return { success: false, error: 'Transaction processing failed' };
    }
  }

  /**
   * Complete transaction after security verification
   */
  async completeTransaction(transactionId, verificationData = {}) {
    try {
      // Get transaction
      const { data: transaction, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single();

      if (error || !transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.status !== 'pending') {
        throw new Error('Transaction is not in pending status');
      }

      // Update transaction status
      const { data: updatedTransaction, error: updateError } = await supabase
        .from('transactions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          verification_data: verificationData
        })
        .eq('id', transactionId)
        .select()
        .single();

      if (updateError) throw updateError;

      // Log completion
      await this.logAuditEvent('transaction_completed', {
        transactionId,
        userId: transaction.user_id,
        amount: transaction.amount
      });

      return { success: true, data: updatedTransaction };

    } catch (error) {
      console.error('Transaction completion error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate secure transaction reference
   */
  generateSecureReference() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const checksum = this.calculateChecksum(timestamp + random);
    return `JP${timestamp.slice(-8)}${random}${checksum}`;
  }

  /**
   * Calculate checksum for reference
   */
  calculateChecksum(input) {
    let sum = 0;
    for (let i = 0; i < input.length; i++) {
      sum += input.charCodeAt(i);
    }
    return (sum % 100).toString().padStart(2, '0');
  }

  /**
   * Get security level based on amount
   */
  getSecurityLevel(amount) {
    if (amount >= 10000000) return 'CRITICAL'; // 10M+
    if (amount >= 1000000) return 'HIGH';      // 1M+
    if (amount >= 500000) return 'MEDIUM';     // 500K+
    return 'STANDARD';
  }

  /**
   * Log security event
   */
  async logSecurityEvent(eventType, eventData) {
    try {
      const securityEvent = {
        event_type: eventType,
        event_data: eventData,
        timestamp: new Date().toISOString(),
        environment: isProductionMode() ? 'production' : 'development'
      };

      if (isProductionMode()) {
        await productionApiService.logAuditEvent(securityEvent);
      } else {
        console.log('🔒 Security Event:', securityEvent);
      }
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * Log audit event
   */
  async logAuditEvent(eventType, eventData) {
    try {
      const auditEvent = {
        event_type: eventType,
        event_data: eventData,
        timestamp: new Date().toISOString(),
        environment: isProductionMode() ? 'production' : 'development'
      };

      if (isProductionMode()) {
        await productionApiService.logAuditEvent(auditEvent);
      } else {
        console.log('📋 Audit Event:', auditEvent);
      }
    } catch (error) {
      console.error('Failed to log audit event:', error);
    }
  }
}

export default new ProductionTransactionService();
