import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  ScrollView,
  TextInput,
  Modal,
} from 'react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';
import { supabase } from '../services/supabaseClient';

const DocumentUploadScreen = ({ navigation, route }) => {
  const { documentType = 'national_id' } = route.params || {};
  
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [hasPermission, setHasPermission] = useState(null);
  const [showCamera, setShowCamera] = useState(false);
  const [capturedImage, setCapturedImage] = useState(null);
  const [frontImage, setFrontImage] = useState(null);
  const [backImage, setBackImage] = useState(null);
  const [documentNumber, setDocumentNumber] = useState('');
  const [user, setUser] = useState(null);
  const [captureStep, setCaptureStep] = useState('front'); // 'front', 'back', 'preview'
  const [showPreview, setShowPreview] = useState(false);

  const cameraRef = useRef(null);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [permission, requestPermission] = useCameraPermissions();

  useEffect(() => {
    loadUserData();
  }, []);

  // Handle permission state changes
  useEffect(() => {
    console.log('📷 Permission state changed:', permission);
    if (permission !== null) {
      setHasPermission(permission.granted);
      console.log('📷 Updated hasPermission to:', permission.granted);
    }
  }, [permission]);

  // Request permission on mount if needed
  useEffect(() => {
    if (permission !== null && !permission.granted) {
      console.log('📷 Permission not granted, requesting...');
      requestCameraPermission();
    }
  }, [permission, requestCameraPermission]);

  const loadUserData = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        Alert.alert('Error', 'Please log in to continue');
        navigation.goBack();
        return;
      }

      // Ensure we have the authenticated user ID for RLS
      console.log('📋 Current user for document upload:', currentUser);
      console.log('📋 User ID for RLS:', currentUser.id);

      // Verify the user is properly authenticated with Supabase
      const { data: { user: supabaseUser }, error } = await supabase.auth.getUser();
      if (error || !supabaseUser) {
        console.error('❌ Supabase auth error:', error);
        Alert.alert('Error', 'Authentication error. Please log in again.');
        navigation.goBack();
        return;
      }

      console.log('📋 Supabase authenticated user:', supabaseUser.id);
      console.log('📋 AuthService user:', currentUser.id);
      console.log('📋 User IDs match:', supabaseUser.id === currentUser.id);

      // Use the Supabase authenticated user to ensure RLS compatibility
      setUser(supabaseUser);
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      Alert.alert('Error', 'Failed to load user data');
      navigation.goBack();
    }
  };

  const requestCameraPermission = useCallback(async () => {
    try {
      console.log('📷 Requesting camera permission...');
      const result = await requestPermission();
      console.log('📷 Permission request result:', result);
      setHasPermission(result.granted);
    } catch (error) {
      console.error('❌ Error requesting camera permission:', error);
      setHasPermission(false);
    }
  }, [requestPermission]);

  const getDocumentInfo = (type) => {
    const documents = {
      national_id: {
        title: 'National ID',
        description: 'Upload a clear photo of your National ID card',
        icon: 'card-outline',
        tips: [
          'Ensure all text is clearly visible',
          'Avoid glare and shadows',
          'Include all four corners of the ID',
          'Make sure the photo is not blurry'
        ],
        numberLabel: 'ID Number',
        numberPlaceholder: 'Enter your National ID number'
      },
      passport: {
        title: 'Passport',
        description: 'Upload a clear photo of your passport bio-data page',
        icon: 'airplane-outline',
        tips: [
          'Capture the bio-data page only',
          'Ensure all text is clearly readable',
          'Avoid reflections from the plastic cover',
          'Make sure your photo is visible'
        ],
        numberLabel: 'Passport Number',
        numberPlaceholder: 'Enter your passport number'
      },
      driving_license: {
        title: 'Driving License',
        description: 'Upload a clear photo of your driving license',
        icon: 'car-outline',
        tips: [
          'Capture both front and back if required',
          'Ensure all details are visible',
          'Avoid glare and shadows',
          'Make sure the license is valid'
        ],
        numberLabel: 'License Number',
        numberPlaceholder: 'Enter your license number'
      },
      utility_bill: {
        title: 'Utility Bill',
        description: 'Upload a recent utility bill (not older than 3 months)',
        icon: 'receipt-outline',
        tips: [
          'Bill must be in your name',
          'Must be dated within last 3 months',
          'Include full address clearly',
          'Accepted: Electricity, Water, Gas bills'
        ],
        numberLabel: 'Account Number',
        numberPlaceholder: 'Enter account number (optional)'
      }
    };
    return documents[type] || documents.national_id;
  };

  const docInfo = getDocumentInfo(documentType);

  const takePicture = async () => {
    if (cameraRef.current) {
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.8,
          base64: false,
        });

        if (documentType === 'national_id') {
          // Handle dual-side capture for National ID
          if (captureStep === 'front') {
            setFrontImage(photo.uri);
            setCaptureStep('back');
            // Don't close camera, continue to back capture
          } else if (captureStep === 'back') {
            setBackImage(photo.uri);
            setShowCamera(false);
            setShowPreview(true);
          }
        } else {
          // Single capture for other documents
          setCapturedImage(photo.uri);
          setShowCamera(false);
        }
      } catch (error) {
        console.error('❌ Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      }
    }
  };

  const pickImageFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        if (documentType === 'national_id') {
          // Handle dual-side selection for National ID
          if (captureStep === 'front') {
            setFrontImage(result.assets[0].uri);
            setCaptureStep('back');
            Alert.alert(
              'Front Side Captured',
              'Now please select the BACK side of your National ID',
              [{ text: 'OK', onPress: () => setShowCamera(true) }]
            );
          } else if (captureStep === 'back') {
            setBackImage(result.assets[0].uri);
            setShowPreview(true);
          }
        } else {
          // Single selection for other documents
          setCapturedImage(result.assets[0].uri);
        }
      }
    } catch (error) {
      console.error('❌ Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const uploadDocument = async () => {
    // Validate images based on document type
    if (documentType === 'national_id') {
      if (!frontImage || !backImage) {
        Alert.alert('Error', 'Please capture both front and back sides of your National ID');
        return;
      }
    } else {
      if (!capturedImage) {
        Alert.alert('Error', 'Please capture or select a document image');
        return;
      }
    }

    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    setUploading(true);
    try {
      if (documentType === 'national_id') {
        // Upload both front and back for National ID
        console.log('📄 Uploading National ID front and back...');
        console.log('📄 Front image URI:', frontImage);
        console.log('📄 Back image URI:', backImage);
        console.log('📄 User ID:', user.id);
        console.log('📄 Document number:', documentNumber.trim() || null);

        const frontResult = await profileManagementService.uploadKYCDocument(
          user.id,
          'national_id_front',
          frontImage,
          documentNumber.trim() || null
        );

        if (!frontResult.success) {
          throw new Error(frontResult.error || 'Failed to upload front side');
        }

        const backResult = await profileManagementService.uploadKYCDocument(
          user.id,
          'national_id_back',
          backImage,
          documentNumber.trim() || null
        );

        if (!backResult.success) {
          throw new Error(backResult.error || 'Failed to upload back side');
        }

        Alert.alert(
          'Success',
          'National ID (front and back) uploaded successfully! We will review it within 24-48 hours.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        // Single upload for other documents
        const result = await profileManagementService.uploadKYCDocument(
          user.id,
          documentType,
          capturedImage,
          documentNumber.trim() || null
        );

        if (result.success) {
          Alert.alert(
            'Success',
            'Document uploaded successfully! We will review it within 24-48 hours.',
            [
              {
                text: 'OK',
                onPress: () => navigation.goBack()
              }
            ]
          );
        } else {
          Alert.alert('Error', result.error || 'Failed to upload document');
        }
      }
    } catch (error) {
      console.error('❌ Error uploading document:', error);
      Alert.alert('Error', 'Failed to upload document. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const renderPreviewScreen = () => {
    return (
      <Modal visible={showPreview} animationType="slide">
        <View style={styles.previewContainer}>
          <View style={styles.previewHeader}>
            <TouchableOpacity
              style={styles.previewBackButton}
              onPress={() => {
                setShowPreview(false);
                setCaptureStep('front');
                setFrontImage(null);
                setBackImage(null);
              }}
            >
              <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text style={styles.previewTitle}>Review Your Photos</Text>
            <View style={styles.previewBackButton} />
          </View>

          <ScrollView style={styles.previewContent}>
            <View style={styles.previewSection}>
              <Text style={styles.previewSectionTitle}>Front Side</Text>
              <View style={styles.previewImageContainer}>
                <Image source={{ uri: frontImage }} style={styles.previewImage} />
                <TouchableOpacity
                  style={styles.retakeButton}
                  onPress={() => {
                    setShowPreview(false);
                    setCaptureStep('front');
                    setFrontImage(null);
                    setShowCamera(true);
                  }}
                >
                  <Text style={styles.retakeButtonText}>Retake Front</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.previewSection}>
              <Text style={styles.previewSectionTitle}>Back Side</Text>
              <View style={styles.previewImageContainer}>
                <Image source={{ uri: backImage }} style={styles.previewImage} />
                <TouchableOpacity
                  style={styles.retakeButton}
                  onPress={() => {
                    setShowPreview(false);
                    setCaptureStep('back');
                    setBackImage(null);
                    setShowCamera(true);
                  }}
                >
                  <Text style={styles.retakeButtonText}>Retake Back</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          <View style={styles.previewActions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.uploadButton]}
              onPress={() => {
                setShowPreview(false);
                uploadDocument();
              }}
            >
              <Text style={styles.actionButtonText}>Upload Both Photos</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  const renderCameraView = () => {
    console.log('📷 renderCameraView - hasPermission:', hasPermission, 'permission:', permission);

    // Show loading while permission is being determined
    if (permission === null || hasPermission === null) {
      return (
        <View style={styles.permissionContainer}>
          <ActivityIndicator size="large" color={Colors.primary.main} />
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }

    // Show permission denied state with retry option
    if (hasPermission === false) {
      return (
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-outline" size={64} color={Colors.neutral.warmGray} />
          <Text style={styles.permissionText}>Camera permission denied</Text>
          <Text style={styles.permissionSubText}>
            Camera access is required to capture document photos
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={requestCameraPermission}
          >
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <Modal visible={showCamera} animationType="slide">
        <View style={styles.cameraContainer}>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing="back"
          />
          <View style={styles.cameraOverlay}>
            <View style={styles.cameraHeader}>
              <TouchableOpacity
                style={styles.cameraCloseButton}
                onPress={() => {
                  setShowCamera(false);
                  if (documentType === 'national_id' && captureStep === 'back') {
                    setCaptureStep('front');
                  }
                }}
              >
                <Ionicons name="close" size={24} color={Colors.neutral.white} />
              </TouchableOpacity>
              <Text style={styles.cameraTitle}>
                {documentType === 'national_id'
                  ? `Capture ${captureStep.toUpperCase()} of National ID`
                  : `Capture ${docInfo.title}`
                }
              </Text>
              <View style={styles.cameraCloseButton} />
            </View>

            {documentType === 'national_id' && (
              <View style={styles.stepIndicator}>
                <Text style={styles.stepText}>
                  Step {captureStep === 'front' ? '1' : '2'} of 2
                </Text>
              </View>
            )}

            <View style={styles.cameraGuide}>
              <View style={styles.documentFrame} />
              <Text style={styles.cameraInstructions}>
                {documentType === 'national_id'
                  ? captureStep === 'front'
                    ? 'Take a photo of the FRONT of your National ID'
                    : 'FLIP THE CARD and take a photo of the BACK of your National ID'
                  : 'Position the document within the frame'
                }
              </Text>
            </View>

            <View style={styles.cameraControls}>
              <TouchableOpacity
                style={styles.galleryButton}
                onPress={() => {
                  setShowCamera(false);
                  pickImageFromGallery();
                }}
              >
                <Ionicons name="images-outline" size={24} color={Colors.neutral.white} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.captureButton}
                onPress={takePicture}
              >
                <View style={styles.captureButtonInner} />
              </TouchableOpacity>

              <View style={styles.galleryButton} />
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>Upload {docInfo.title}</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Document Info */}
        <View style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <View style={styles.infoIcon}>
              <Ionicons name={docInfo.icon} size={24} color={Colors.primary.main} />
            </View>
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>{docInfo.title}</Text>
              <Text style={styles.infoDescription}>{docInfo.description}</Text>
            </View>
          </View>
        </View>

        {/* Tips */}
        <View style={styles.tipsCard}>
          <Text style={styles.tipsTitle}>📸 Photo Tips</Text>
          {docInfo.tips.map((tip, index) => (
            <View key={index} style={styles.tipItem}>
              <Text style={styles.tipBullet}>•</Text>
              <Text style={styles.tipText}>{tip}</Text>
            </View>
          ))}
        </View>

        {/* Document Number Input */}
        <View style={styles.inputCard}>
          <Text style={styles.inputLabel}>{docInfo.numberLabel}</Text>
          <TextInput
            style={styles.textInput}
            value={documentNumber}
            onChangeText={setDocumentNumber}
            placeholder={docInfo.numberPlaceholder}
            placeholderTextColor={theme.colors.textSecondary}
            autoCapitalize="characters"
          />
        </View>

        {/* Image Preview */}
        {documentType === 'national_id' ? (
          // Dual-side preview for National ID
          <View>
            {frontImage && (
              <View style={styles.previewCard}>
                <Text style={styles.previewTitle}>Front Side Preview</Text>
                <Image source={{ uri: frontImage }} style={styles.previewImage} />
                <TouchableOpacity
                  style={styles.retakeButton}
                  onPress={() => {
                    setFrontImage(null);
                    setCaptureStep('front');
                  }}
                >
                  <Ionicons name="refresh-outline" size={16} color={Colors.primary.main} />
                  <Text style={styles.retakeButtonText}>Retake Front</Text>
                </TouchableOpacity>
              </View>
            )}

            {backImage && (
              <View style={styles.previewCard}>
                <Text style={styles.previewTitle}>Back Side Preview</Text>
                <Image source={{ uri: backImage }} style={styles.previewImage} />
                <TouchableOpacity
                  style={styles.retakeButton}
                  onPress={() => {
                    setBackImage(null);
                    setCaptureStep('back');
                  }}
                >
                  <Ionicons name="refresh-outline" size={16} color={Colors.primary.main} />
                  <Text style={styles.retakeButtonText}>Retake Back</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        ) : (
          // Single image preview for other documents
          capturedImage && (
            <View style={styles.previewCard}>
              <Text style={styles.previewTitle}>Document Preview</Text>
              <Image source={{ uri: capturedImage }} style={styles.previewImage} />
              <TouchableOpacity
                style={styles.retakeButton}
                onPress={() => setCapturedImage(null)}
              >
                <Ionicons name="refresh-outline" size={16} color={Colors.primary.main} />
                <Text style={styles.retakeButtonText}>Retake Photo</Text>
              </TouchableOpacity>
            </View>
          )
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {documentType === 'national_id' ? (
            // Dual-side capture buttons for National ID
            !frontImage || !backImage ? (
              <>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => setShowCamera(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="camera-outline" size={20} color={Colors.neutral.white} />
                  <Text style={styles.actionButtonText}>
                    {!frontImage ? 'Take Front Photo' : 'Take Back Photo'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.actionButton, styles.secondaryButton]}
                  onPress={pickImageFromGallery}
                  activeOpacity={0.7}
                >
                  <Ionicons name="images-outline" size={20} color={Colors.primary.main} />
                  <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                    {!frontImage ? 'Choose Front from Gallery' : 'Choose Back from Gallery'}
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => setShowPreview(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="eye-outline" size={20} color={Colors.neutral.white} />
                  <Text style={styles.actionButtonText}>Review Both Photos</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.actionButton, uploading && styles.actionButtonDisabled]}
                  onPress={uploadDocument}
                  disabled={uploading}
                  activeOpacity={0.7}
                >
                  {uploading ? (
                    <ActivityIndicator size="small" color={Colors.neutral.white} />
                  ) : (
                    <Ionicons name="cloud-upload-outline" size={20} color={Colors.neutral.white} />
                  )}
                  <Text style={styles.actionButtonText}>
                    {uploading ? 'Uploading...' : 'Upload Both Photos'}
                  </Text>
                </TouchableOpacity>
              </>
            )
          ) : (
            // Single capture buttons for other documents
            !capturedImage ? (
              <>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => setShowCamera(true)}
                  activeOpacity={0.7}
                >
                  <Ionicons name="camera-outline" size={20} color={Colors.neutral.white} />
                  <Text style={styles.actionButtonText}>Take Photo</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.actionButton, styles.secondaryButton]}
                  onPress={pickImageFromGallery}
                  activeOpacity={0.7}
                >
                  <Ionicons name="images-outline" size={20} color={Colors.primary.main} />
                  <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                    Choose from Gallery
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <TouchableOpacity
                style={[styles.actionButton, uploading && styles.actionButtonDisabled]}
                onPress={uploadDocument}
                disabled={uploading}
                activeOpacity={0.7}
              >
                {uploading ? (
                  <ActivityIndicator size="small" color={Colors.neutral.white} />
                ) : (
                  <Ionicons name="cloud-upload-outline" size={20} color={Colors.neutral.white} />
                )}
                <Text style={styles.actionButtonText}>
                  {uploading ? 'Uploading...' : 'Upload Document'}
                </Text>
              </TouchableOpacity>
            )
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {renderCameraView()}
      {renderPreviewScreen()}
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  infoCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  tipsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  tipBullet: {
    fontSize: 16,
    color: Colors.primary.main,
    marginRight: 8,
    marginTop: 2,
  },
  tipText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
    lineHeight: 20,
  },
  inputCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: theme.colors.background,
    color: theme.colors.text,
  },
  previewCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 12,
  },
  retakeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  retakeButtonText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
    marginLeft: 4,
  },
  actionsContainer: {
    marginVertical: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionButtonDisabled: {
    opacity: 0.6,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.primary.main,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: Colors.primary.main,
  },
  bottomSpacing: {
    height: 40,
  },
  // Camera styles
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'space-between',
  },
  cameraHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  cameraCloseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cameraTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    textAlign: 'center',
  },
  cameraGuide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  documentFrame: {
    width: 280,
    height: 180,
    borderWidth: 2,
    borderColor: Colors.neutral.white,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  cameraInstructions: {
    fontSize: 16,
    color: Colors.neutral.white,
    textAlign: 'center',
    marginTop: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  cameraControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 40,
    paddingBottom: 40,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary.main,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: 40,
  },
  permissionText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginVertical: 20,
  },
  permissionSubText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.8,
  },
  permissionButton: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  permissionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  stepIndicator: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  stepText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  previewContainer: {
    flex: 1,
    backgroundColor: '#fcf7f0',
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.lightGray,
  },
  previewBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.darkGray,
  },
  previewContent: {
    flex: 1,
    padding: 20,
  },
  previewSection: {
    marginBottom: 30,
  },
  previewSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.darkGray,
    marginBottom: 15,
  },
  previewImageContainer: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
  },
  previewActions: {
    padding: 20,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  uploadButton: {
    backgroundColor: Colors.primary.main,
  },
});

export default DocumentUploadScreen;
