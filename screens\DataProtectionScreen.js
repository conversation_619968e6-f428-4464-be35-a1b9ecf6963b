/**
 * Data Protection Screen for JiraniPay
 * 
 * Comprehensive information about data protection rights and procedures
 * under East African data protection laws and GDPR
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Linking
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import privacyManagementService from '../services/privacyManagementService';
import legalComplianceService from '../services/legalComplianceService';
import authService from '../services/authService';

const DataProtectionScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('rights');

  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
    }
  }, []);

  const handleTabChange = (tab) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setActiveTab(tab);
  };

  const handleDataRequest = (requestType) => {
    Alert.alert(t('dataSubjectRequest'), t('wouldYouLikeToSubmitARequesttypeRequestWeWillProce'),
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Submit Request', 
          onPress: () => submitDataRequest(requestType)
        }
      ]
    );
  };

  const submitDataRequest = async (requestType) => {
    try {
      if (!user) {
        Alert.alert(t('error'), t('pleaseLogInToSubmitADataRequest'));
        return;
      }

      // Submit request using legal compliance service
      const result = await legalComplianceService.submitDataSubjectRequest(
        user.id,
        requestType, // requestType is already in the correct format (access, rectification, etc.)
        {
          user_email: user.email,
          user_phone: user.phone,
          submission_method: 'mobile_app',
          additional_details: `Request submitted via JiraniPay mobile app for ${requestType}`
        }
      );

      if (result.success) {
        // Generate reference number for user tracking
        const referenceNumber = `DSR-${Date.now().toString().slice(-6)}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

        // Also log in privacy management service for backward compatibility
        await privacyManagementService.logPrivacyEvent(user.id, 'data_subject_request', {
          request_type: requestType,
          reference_number: referenceNumber,
          request_id: result.data?.id,
          timestamp: new Date().toISOString(),
          status: 'submitted'
        });

        Alert.alert(t('requestSubmittedSuccessfully'), t('yourRequesttypeRequestHasBeenSubmittedToOurDataPro') +
          `Reference Number: ${referenceNumber}\n` +
          `Processing Timeline: 30 days\n` +
          `Contact: <EMAIL>\n\n` +
          `You will receive updates via email and app notifications.`,
          [{ text: 'OK' }]
        );

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else {
        throw new Error(result.error || 'Failed to submit request');
      }
    } catch (error) {
      console.error('❌ Error submitting data request:', error);
      Alert.alert(t('submissionFailed'), t('failedToSubmitYourRequestPleaseTryAgainOrContactOu'),
        [{ text: 'OK' }]
      );
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const handleComplaint = () => {
    Alert.alert(t('fileAComplaint'), t('youCanFileAComplaintWithTheDataProtectionAuthority'),
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Uganda (NITA-U)', 
          onPress: () => Linking.openURL('https://www.nita.go.ug/data-protection')
        },
        { 
          text: 'Kenya (ODPC)', 
          onPress: () => Linking.openURL('https://www.odpc.go.ke')
        },
        { 
          text: 'Contact Us', 
          onPress: () => Linking.openURL('mailto:<EMAIL>')
        }
      ]
    );
  };

  const dataRights = [
    {
      id: 'access',
      title: 'Right to Access',
      description: 'Request a copy of all personal data we hold about you',
      icon: 'eye',
      color: Colors.primary.main,
      details: [
        'Free of charge (once per year)',
        'Provided within 30 days',
        'Includes data sources and processing purposes',
        'Available in electronic format'
      ],
      action: () => handleDataRequest('access')
    },
    {
      id: 'rectification',
      title: 'Right to Rectification',
      description: 'Correct inaccurate or incomplete personal information',
      icon: 'create',
      color: Colors.secondary.savanna,
      details: [
        'Update incorrect personal details',
        'Complete incomplete information',
        'Processed within 30 days',
        'Notification to third parties if applicable'
      ],
      action: () => handleDataRequest('rectification')
    },
    {
      id: 'erasure',
      title: 'Right to Erasure',
      description: 'Request deletion of your personal data',
      icon: 'trash',
      color: Colors.status.error,
      details: [
        'Subject to legal retention requirements',
        'Financial records kept for 7 years (regulatory requirement)',
        'Marketing data deleted immediately',
        'Account closure triggers automatic deletion'
      ],
      action: () => handleDataRequest('erasure')
    },
    {
      id: 'portability',
      title: 'Right to Data Portability',
      description: 'Receive your data in a structured, machine-readable format',
      icon: 'download',
      color: Colors.accent.gold,
      details: [
        'JSON or CSV format available',
        'Includes all personal data',
        'Can be transferred to another service',
        'Available through app export feature'
      ],
      action: () => navigation.navigate('PrivacyControls')
    },
    {
      id: 'restrict',
      title: 'Right to Restrict Processing',
      description: 'Limit how we process your personal data',
      icon: 'pause',
      color: Colors.status.warning,
      details: [
        'Suspend processing while verifying accuracy',
        'Limit processing to storage only',
        'Maintain data but restrict use',
        'Notification before lifting restrictions'
      ],
      action: () => handleDataRequest('restriction')
    },
    {
      id: 'object',
      title: 'Right to Object',
      description: 'Object to processing based on legitimate interests',
      icon: 'stop',
      color: Colors.status.info,
      details: [
        'Object to marketing communications',
        'Object to profiling and automated decisions',
        'Object to processing for legitimate interests',
        'Immediate cessation unless compelling grounds exist'
      ],
      action: () => handleDataRequest('objection')
    }
  ];

  const protectionMeasures = [
    {
      title: 'Technical Safeguards',
      icon: 'shield-checkmark',
      color: Colors.status.success,
      measures: [
        'AES-256 encryption for data at rest',
        'TLS 1.3 encryption for data in transit',
        'Multi-factor authentication',
        'Regular security audits and penetration testing',
        'Secure cloud infrastructure (ISO 27001 certified)',
        'Automated threat detection and response'
      ]
    },
    {
      title: 'Organizational Measures',
      icon: 'people',
      color: Colors.primary.main,
      measures: [
        'Data protection officer appointed',
        'Regular staff training on data protection',
        'Strict access controls and need-to-know basis',
        'Data processing agreements with third parties',
        'Privacy impact assessments for new features',
        'Incident response procedures'
      ]
    },
    {
      title: 'Legal Compliance',
      icon: 'document-text',
      color: Colors.secondary.savanna,
      measures: [
        'Uganda Data Protection Act 2019 compliance',
        'Kenya Data Protection Act 2019 compliance',
        'Tanzania Data Protection Act 2022 compliance',
        'GDPR compliance for international users',
        'Bank of Uganda regulatory compliance',
        'Regular compliance audits and assessments'
      ]
    }
  ];

  const renderRightCard = (right) => (
    <TouchableOpacity
      key={right.id}
      style={styles.rightCard}
      onPress={right.action}
      activeOpacity={0.7}
    >
      <View style={styles.rightHeader}>
        <View style={[styles.rightIcon, { backgroundColor: right.color + '20' }]}>
          <Ionicons name={right.icon} size={24} color={right.color} />
        </View>
        <View style={styles.rightContent}>
          <Text style={styles.rightTitle}>{right.title}</Text>
          <Text style={styles.rightDescription}>{right.description}</Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
      </View>
      <View style={styles.rightDetails}>
        {right.details.map((detail, index) => (
          <Text key={index} style={styles.rightDetail}>• {detail}</Text>
        ))}
      </View>
    </TouchableOpacity>
  );

  const renderProtectionCard = (protection) => (
    <View key={protection.title} style={styles.protectionCard}>
      <View style={styles.protectionHeader}>
        <View style={[styles.protectionIcon, { backgroundColor: protection.color + '20' }]}>
          <Ionicons name={protection.icon} size={24} color={protection.color} />
        </View>
        <Text style={styles.protectionTitle}>{protection.title}</Text>
      </View>
      <View style={styles.protectionMeasures}>
        {protection.measures.map((measure, index) => (
          <Text key={index} style={styles.protectionMeasure}>• {measure}</Text>
        ))}
      </View>
    </View>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'rights':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabDescription}>
              {t('underEastAfricanDataProtectionLawsYouHaveTheFollow')}
            </Text>
            {dataRights.map(renderRightCard)}
          </View>
        );
      
      case 'protection':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabDescription}>
              {t('weImplementComprehensiveTechnicalOrganizationalAnd')}
            </Text>
            {protectionMeasures.map(renderProtectionCard)}
          </View>
        );
      
      case 'procedures':
        return (
          <View style={styles.tabContent}>
            <Text style={styles.tabDescription}>
              {t('howToExerciseYourDataProtectionRightsAndOurRespons')}
            </Text>
            
            <View style={styles.procedureCard}>
              <Text style={styles.procedureTitle}>{t('dataSubjectRequests')}</Text>
              <Text style={styles.procedureText}>
                1. Submit your request through the app <NAME_EMAIL>{'\n'}
                2. We verify your identity within 3 business days{'\n'}
                3. We process your request within 30 days{'\n'}
                4. You receive a response with the requested information or action taken
              </Text>
            </View>

            <View style={styles.procedureCard}>
              <Text style={styles.procedureTitle}>{t('dataBreachNotification')}</Text>
              <Text style={styles.procedureText}>
                • We notify authorities within 72 hours of discovering a breach{'\n'}
                • You are notified within 72 hours if the breach poses high risk{'\n'}
                • We provide details about the breach and mitigation steps{'\n'}
                • We implement additional security measures to prevent recurrence
              </Text>
            </View>

            <TouchableOpacity style={styles.complaintButton} onPress={handleComplaint}>
              <Ionicons name="warning" size={24} color={Colors.status.error} />
              <View style={styles.complaintContent}>
                <Text style={styles.complaintTitle}>{t('fileAComplaint')}</Text>
                <Text style={styles.complaintText}>
                  {t('contactDataProtectionAuthoritiesIfYoureNotSatisfie')}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
            </TouchableOpacity>
          </View>
        );
      
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('dataProtection')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('yourRightsAndHowWeProtectYourData')}
        </Text>
      </LinearGradient>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'rights' && styles.activeTab]}
          onPress={() => handleTabChange('rights')}
        >
          <Text style={[styles.tabText, activeTab === 'rights' && styles.activeTabText]}>
            {t('yourRights')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'protection' && styles.activeTab]}
          onPress={() => handleTabChange('protection')}
        >
          <Text style={[styles.tabText, activeTab === 'protection' && styles.activeTabText]}>
            {t('protection')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'procedures' && styles.activeTab]}
          onPress={() => handleTabChange('procedures')}
        >
          <Text style={[styles.tabText, activeTab === 'procedures' && styles.activeTabText]}>
            {t('procedures')}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderTabContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    marginTop: -15,
    borderRadius: 12,
    padding: 4,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: Colors.primary.main,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  activeTabText: {
    color: Colors.neutral.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  tabContent: {
    paddingTop: 20,
    paddingBottom: 40,
  },
  tabDescription: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    lineHeight: 22,
    marginBottom: 20,
    textAlign: 'center',
  },
  rightCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  rightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  rightIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  rightContent: {
    flex: 1,
  },
  rightTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  rightDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 18,
  },
  rightDetails: {
    paddingLeft: 52,
  },
  rightDetail: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
    lineHeight: 16,
    marginBottom: 2,
  },
  protectionCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  protectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  protectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  protectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  protectionMeasures: {
    paddingLeft: 52,
  },
  protectionMeasure: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 18,
    marginBottom: 4,
  },
  procedureCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  procedureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  procedureText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
  },
  complaintButton: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  complaintContent: {
    flex: 1,
    marginLeft: 12,
  },
  complaintTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.status.error,
    marginBottom: 4,
  },
  complaintText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 18,
  },
});

export default DataProtectionScreen;
