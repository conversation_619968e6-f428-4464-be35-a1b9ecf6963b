import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, Camera } from 'expo-camera';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import qrCodeService from '../services/qrCodeService';
import UnifiedBackButton from '../components/UnifiedBackButton';
import NavigationUtils from '../utils/navigationUtils';

const { width, height } = Dimensions.get('window');

/**
 * QRScannerScreen - QR Code scanning with camera integration
 * Features real-time scanning, manual entry, and payment processing
 */
const QRScannerScreen = ({ navigation, route }) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [flashOn, setFlashOn] = useState(false);
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [manualCode, setManualCode] = useState('');
  const [processing, setProcessing] = useState(false);
  const cameraRef = useRef(null);

  const scanType = route.params?.type || 'payment'; // 'payment' or 'receive'

  useEffect(() => {
    requestCameraPermission();
  }, []);

  const requestCameraPermission = async () => {
    try {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'JiraniPay needs camera access to scan QR codes. You can also enter codes manually.',
          [
            { text: 'Manual Entry', onPress: () => setShowManualEntry(true) },
            { text: 'Settings', onPress: () => {/* Open settings */} },
            { text: 'Cancel', onPress: () => NavigationUtils.safeGoBack(navigation) }
          ]
        );
      }
    } catch (error) {
      console.error('Camera permission error:', error);
      setHasPermission(false);
    }
  };

  const handleBarCodeScanned = async ({ type, data }) => {
    if (scanned || processing) return;
    
    try {
      setScanned(true);
      setProcessing(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      console.log('📱 QR Code scanned:', { type, data });
      
      await processQRCode(data);
    } catch (error) {
      console.error('QR scan processing error:', error);
      Alert.alert('Scan Error', 'Failed to process QR code. Please try again.');
      setScanned(false);
    } finally {
      setProcessing(false);
    }
  };

  const processQRCode = async (qrData) => {
    try {
      const parseResult = qrCodeService.parseQRCode(qrData);
      
      if (!parseResult.success) {
        Alert.alert('Invalid QR Code', parseResult.error, [
          { text: 'Scan Again', onPress: () => setScanned(false) },
          { text: 'Manual Entry', onPress: () => setShowManualEntry(true) }
        ]);
        return;
      }

      const { qrData: parsedData } = parseResult;
      
      // Save to recent QR codes
      await qrCodeService.saveToRecentQRCodes(parsedData);
      
      // Navigate based on QR type
      if (parsedData.type === 'payment_request' || parsedData.type === 'receive_payment') {
        handlePaymentQR(parsedData);
      } else {
        Alert.alert('Unsupported QR Code', 'This QR code type is not supported yet.');
        setScanned(false);
      }
    } catch (error) {
      console.error('Process QR code error:', error);
      Alert.alert('Processing Error', 'Failed to process QR code data.');
      setScanned(false);
    }
  };

  const handlePaymentQR = (qrData) => {
    const { user, amount, purpose } = qrData;
    
    // Create recipient object for Send Money flow
    const recipient = {
      id: user.id,
      name: user.name,
      phoneNumber: user.phone,
      source: 'qr_code',
      qrData: qrData,
    };

    if (amount) {
      // Fixed amount QR - go directly to confirmation
      navigation.replace('TransferConfirmation', {
        recipient: recipient,
        amount: amount,
        purpose: purpose,
        feeCalculation: {
          amount: amount,
          fee: amount >= 10000 ? Math.ceil(amount * 0.01) : 0,
          total: amount + (amount >= 10000 ? Math.ceil(amount * 0.01) : 0),
          freeTransfer: amount < 10000,
        },
        validation: { success: true },
        fromQR: true,
      });
    } else {
      // Open amount QR - go to amount entry
      navigation.replace('TransferAmount', {
        recipient: recipient,
        fromQR: true,
      });
    }
  };

  const handleManualEntry = async () => {
    if (!manualCode.trim()) {
      Alert.alert('Invalid Input', 'Please enter a QR code');
      return;
    }

    try {
      setProcessing(true);
      await processQRCode(manualCode.trim());
      setShowManualEntry(false);
      setManualCode('');
    } catch (error) {
      console.error('Manual entry error:', error);
      Alert.alert('Error', 'Failed to process manual code entry');
    } finally {
      setProcessing(false);
    }
  };

  const toggleFlash = () => {
    setFlashOn(!flashOn);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const renderCameraView = () => {
    if (hasPermission === null) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.permissionText}>Requesting camera permission...</Text>
        </View>
      );
    }

    if (hasPermission === false) {
      return (
        <View style={styles.centerContainer}>
          <Ionicons name="camera-off" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.permissionText}>Camera access denied</Text>
          <Text style={styles.permissionSubtext}>
            Enable camera permission in settings or use manual entry
          </Text>
          <TouchableOpacity
            style={styles.manualEntryButton}
            onPress={() => setShowManualEntry(true)}
            activeOpacity={0.8}
          >
            <Text style={styles.manualEntryButtonText}>Manual Entry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <CameraView
        ref={cameraRef}
        style={styles.camera}
        facing="back"
        enableTorch={flashOn}
        onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
        barcodeScannerSettings={{
          barcodeTypes: ['qr'],
        }}
      >
        <View style={styles.overlay}>
          {/* Scanning Frame */}
          <View style={styles.scanFrame}>
            <View style={[styles.corner, styles.topLeft]} />
            <View style={[styles.corner, styles.topRight]} />
            <View style={[styles.corner, styles.bottomLeft]} />
            <View style={[styles.corner, styles.bottomRight]} />
          </View>

          {/* Scanning Line Animation */}
          {!scanned && (
            <View style={styles.scanLine} />
          )}
        </View>
      </CameraView>
    );
  };

  const renderManualEntryModal = () => (
    <Modal
      visible={showManualEntry}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowManualEntry(false)}
    >
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={() => setShowManualEntry(false)}
            style={styles.modalCloseButton}
          >
            <Ionicons name="close" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Enter QR Code</Text>
          <View style={styles.modalSpacer} />
        </View>

        <View style={styles.modalContent}>
          <Text style={styles.manualInstructions}>
            Enter the QR code data manually if you can't scan it with the camera
          </Text>
          
          <TextInput
            style={styles.manualInput}
            value={manualCode}
            onChangeText={setManualCode}
            placeholder="Paste or type QR code data here..."
            placeholderTextColor={theme.colors.placeholder}
            multiline
            autoFocus
          />

          <TouchableOpacity
            style={[
              styles.processButton,
              (!manualCode.trim() || processing) && styles.processButtonDisabled
            ]}
            onPress={handleManualEntry}
            disabled={!manualCode.trim() || processing}
            activeOpacity={0.8}
          >
            <Text style={styles.processButtonText}>
              {processing ? 'Processing...' : 'Process Code'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <SafeAreaView style={styles.header}>
        <UnifiedBackButton
          navigation={navigation}
          style={styles.headerButton}
          iconColor={theme.colors.text}
          iconSize={24}
          backgroundColor="transparent"
        />
        
        <Text style={styles.headerTitle}>
          {scanType === 'receive' ? 'Scan to Receive' : 'Scan QR Code'}
        </Text>
        
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => setShowManualEntry(true)}
          activeOpacity={0.7}
        >
          <Ionicons name="keypad" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </SafeAreaView>

      {/* Camera View */}
      <View style={styles.cameraContainer}>
        {renderCameraView()}
      </View>

      {/* Instructions */}
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsTitle}>
          {processing ? 'Processing QR Code...' : 'Position QR code within the frame'}
        </Text>
        <Text style={styles.instructionsText}>
          {scanType === 'receive' 
            ? 'Scan a payment QR code to receive money'
            : 'Scan a JiraniPay QR code to make a payment'
          }
        </Text>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        {hasPermission && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={toggleFlash}
            activeOpacity={0.8}
          >
            <Ionicons 
              name={flashOn ? "flash" : "flash-off"} 
              size={24} 
              color={theme.colors.text}
            />
            <Text style={styles.controlButtonText}>Flash</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setShowManualEntry(true)}
          activeOpacity={0.8}
        >
          <Ionicons name="keypad" size={24} color={theme.colors.text} />
          <Text style={styles.controlButtonText}>Manual</Text>
        </TouchableOpacity>
        
        {scanned && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={() => setScanned(false)}
            activeOpacity={0.8}
          >
            <Ionicons name="refresh" size={24} color={theme.colors.text} />
            <Text style={styles.controlButtonText}>Scan Again</Text>
          </TouchableOpacity>
        )}
      </View>

      {renderManualEntryModal()}
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: theme.colors.primary,
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  scanLine: {
    position: 'absolute',
    width: 250,
    height: 2,
    backgroundColor: theme.colors.primary,
    opacity: 0.8,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background + '80',
  },
  permissionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginTop: 16,
  },
  permissionSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 40,
    lineHeight: 20,
  },
  manualEntryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 20,
  },
  manualEntryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  instructionsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: theme.colors.surface,
  },
  controlButton: {
    alignItems: 'center',
    padding: 12,
  },
  controlButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    marginTop: 4,
    fontWeight: '500',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalCloseButton: {
    padding: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalSpacer: {
    width: 40,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  manualInstructions: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  manualInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 2,
    borderColor: theme.colors.primary + '30',
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 24,
  },
  processButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  processButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  processButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
});

export default QRScannerScreen;
