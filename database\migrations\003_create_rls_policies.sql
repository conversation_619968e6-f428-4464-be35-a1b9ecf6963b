-- JiraniPay Complete Database Setup with RLS
-- This is a comprehensive, bulletproof database setup
-- Run this ENTIRE script in Supabase SQL Editor

-- Step 1: Clean slate - drop existing tables if they exist
DROP TABLE IF EXISTS public.transactions CASCADE;
DROP TABLE IF EXISTS public.wallets CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- Step 2: Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Step 3: Create user_profiles table
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    full_name TEXT,
    phone_number TEXT UNIQUE,
    email TEXT,
    date_of_birth DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    address TEXT,
    city TEXT,
    country TEXT DEFAULT 'UG',
    profile_picture_url TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    phone_verified BOOLEAN DEFAULT false,
    kyc_status TEXT DEFAULT 'pending' CHECK (kyc_status IN ('pending', 'verified', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Create wallets table
CREATE TABLE public.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    account_number TEXT UNIQUE,
    account_type TEXT DEFAULT 'wallet' CHECK (account_type IN ('wallet', 'savings', 'business')),
    balance DECIMAL(15,2) DEFAULT 0.00,
    available_balance DECIMAL(15,2) DEFAULT 0.00,
    pending_balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'UGX',
    daily_limit DECIMAL(15,2) DEFAULT 500000.00,
    monthly_limit DECIMAL(15,2) DEFAULT 2000000.00,
    spent_today DECIMAL(15,2) DEFAULT 0.00,
    spent_this_month DECIMAL(15,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    provider_name TEXT DEFAULT 'JiraniPay',
    last_balance_update TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_user_wallet UNIQUE(user_id)
);

-- Step 5: Create transactions table
CREATE TABLE public.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    wallet_id UUID,
    transaction_type TEXT CHECK (transaction_type IN ('deposit', 'withdrawal', 'transfer', 'payment', 'refund')),
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT DEFAULT 'UGX',
    description TEXT,
    reference_number TEXT UNIQUE,
    external_reference TEXT,
    recipient_phone TEXT,
    recipient_name TEXT,
    sender_phone TEXT,
    sender_name TEXT,
    category TEXT DEFAULT 'general',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    provider TEXT DEFAULT 'JiraniPay',
    provider_transaction_id TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Create indexes for performance
CREATE INDEX idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX idx_user_profiles_phone ON public.user_profiles(phone_number);
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);

CREATE INDEX idx_wallets_user_id ON public.wallets(user_id);
CREATE INDEX idx_wallets_account_number ON public.wallets(account_number);
CREATE INDEX idx_wallets_active ON public.wallets(is_active);

CREATE INDEX idx_transactions_user_id ON public.transactions(user_id);
CREATE INDEX idx_transactions_wallet_id ON public.transactions(wallet_id);
CREATE INDEX idx_transactions_reference ON public.transactions(reference_number);
CREATE INDEX idx_transactions_created_at ON public.transactions(created_at);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_transactions_type ON public.transactions(transaction_type);

-- Step 7: Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Step 8: Create RLS Policies for user_profiles
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Step 9: Create RLS Policies for wallets
CREATE POLICY "Users can view own wallet" ON public.wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own wallet" ON public.wallets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own wallet" ON public.wallets
    FOR UPDATE USING (auth.uid() = user_id);

-- Step 10: Create RLS Policies for transactions
CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own transactions" ON public.transactions
    FOR UPDATE USING (auth.uid() = user_id);

-- Step 11: Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 12: Add updated_at triggers
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wallets_updated_at
    BEFORE UPDATE ON public.wallets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at
    BEFORE UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 13: Create wallet management functions
CREATE OR REPLACE FUNCTION create_user_wallet(
    p_user_id UUID,
    p_phone_number TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_wallet_id UUID;
    v_account_number TEXT;
    v_result JSON;
BEGIN
    -- Generate account number
    IF p_phone_number IS NOT NULL THEN
        v_account_number := 'JP' || REPLACE(REPLACE(p_phone_number, '+', ''), ' ', '');
    ELSE
        v_account_number := 'JP' || SUBSTRING(p_user_id::TEXT, 1, 10);
    END IF;

    -- Check if wallet already exists
    SELECT id INTO v_wallet_id
    FROM public.wallets
    WHERE user_id = p_user_id;

    IF v_wallet_id IS NOT NULL THEN
        -- Return existing wallet
        SELECT json_build_object(
            'success', true,
            'data', json_build_object(
                'id', id,
                'user_id', user_id,
                'account_number', account_number,
                'balance', balance,
                'currency', currency,
                'created_at', created_at
            )
        ) INTO v_result
        FROM public.wallets
        WHERE id = v_wallet_id;

        RETURN v_result;
    END IF;

    -- Create new wallet
    INSERT INTO public.wallets (
        user_id,
        account_number,
        account_type,
        balance,
        available_balance,
        currency,
        provider_name
    ) VALUES (
        p_user_id,
        v_account_number,
        'wallet',
        0.00,
        0.00,
        'UGX',
        'JiraniPay'
    ) RETURNING id INTO v_wallet_id;

    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'id', id,
            'user_id', user_id,
            'account_number', account_number,
            'balance', balance,
            'currency', currency,
            'created_at', created_at
        )
    ) INTO v_result
    FROM public.wallets
    WHERE id = v_wallet_id;

    RETURN v_result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 14: Create balance retrieval function
CREATE OR REPLACE FUNCTION get_user_wallet_balance(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    v_result JSON;
BEGIN
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'balance', COALESCE(balance, 0),
            'available_balance', COALESCE(available_balance, 0),
            'pending_balance', COALESCE(pending_balance, 0),
            'currency', COALESCE(currency, 'UGX'),
            'daily_limit', COALESCE(daily_limit, 500000),
            'monthly_limit', COALESCE(monthly_limit, 2000000),
            'spent_today', COALESCE(spent_today, 0),
            'spent_this_month', COALESCE(spent_this_month, 0)
        )
    ) INTO v_result
    FROM public.wallets
    WHERE user_id = p_user_id AND is_active = true;

    IF v_result IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Wallet not found'
        );
    END IF;

    RETURN v_result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 15: Create transaction management function
CREATE OR REPLACE FUNCTION create_user_transaction(
    p_user_id UUID,
    p_transaction_type TEXT,
    p_amount DECIMAL,
    p_description TEXT DEFAULT NULL,
    p_recipient_phone TEXT DEFAULT NULL,
    p_category TEXT DEFAULT 'general'
)
RETURNS JSON AS $$
DECLARE
    v_wallet_id UUID;
    v_transaction_id UUID;
    v_reference_number TEXT;
    v_result JSON;
BEGIN
    -- Get user's wallet
    SELECT id INTO v_wallet_id
    FROM public.wallets
    WHERE user_id = p_user_id AND is_active = true;

    IF v_wallet_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Wallet not found'
        );
    END IF;

    -- Generate reference number
    v_reference_number := 'TXN' || EXTRACT(EPOCH FROM NOW())::BIGINT || FLOOR(RANDOM() * 10000)::TEXT;

    -- Create transaction
    INSERT INTO public.transactions (
        user_id,
        wallet_id,
        transaction_type,
        amount,
        description,
        reference_number,
        recipient_phone,
        category,
        status
    ) VALUES (
        p_user_id,
        v_wallet_id,
        p_transaction_type,
        p_amount,
        p_description,
        v_reference_number,
        p_recipient_phone,
        p_category,
        'completed'
    ) RETURNING id INTO v_transaction_id;

    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'id', id,
            'reference_number', reference_number,
            'amount', amount,
            'transaction_type', transaction_type,
            'status', status,
            'created_at', created_at
        )
    ) INTO v_result
    FROM public.transactions
    WHERE id = v_transaction_id;

    RETURN v_result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 16: Create user profile management function
CREATE OR REPLACE FUNCTION create_user_profile(
    p_user_id UUID,
    p_phone_number TEXT,
    p_email TEXT DEFAULT NULL,
    p_full_name TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    v_profile_id UUID;
    v_result JSON;
BEGIN
    -- Check if profile already exists
    SELECT id INTO v_profile_id
    FROM public.user_profiles
    WHERE user_id = p_user_id;

    IF v_profile_id IS NOT NULL THEN
        -- Return existing profile
        SELECT json_build_object(
            'success', true,
            'data', json_build_object(
                'id', id,
                'user_id', user_id,
                'phone_number', phone_number,
                'email', email,
                'full_name', full_name,
                'created_at', created_at
            )
        ) INTO v_result
        FROM public.user_profiles
        WHERE id = v_profile_id;

        RETURN v_result;
    END IF;

    -- Create new profile
    INSERT INTO public.user_profiles (
        user_id,
        phone_number,
        email,
        full_name,
        is_active,
        phone_verified
    ) VALUES (
        p_user_id,
        p_phone_number,
        p_email,
        p_full_name,
        true,
        false
    ) RETURNING id INTO v_profile_id;

    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', json_build_object(
            'id', id,
            'user_id', user_id,
            'phone_number', phone_number,
            'email', email,
            'full_name', full_name,
            'created_at', created_at
        )
    ) INTO v_result
    FROM public.user_profiles
    WHERE id = v_profile_id;

    RETURN v_result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 17: Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Step 18: Grant execute permissions on specific functions
GRANT EXECUTE ON FUNCTION create_user_wallet(UUID, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_wallet_balance(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_user_transaction(UUID, TEXT, DECIMAL, TEXT, TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION create_user_profile(UUID, TEXT, TEXT, TEXT) TO anon, authenticated;

-- Step 19: Create automatic user setup function
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create user profile
    PERFORM create_user_profile(NEW.id, NEW.phone, NEW.email, NEW.raw_user_meta_data->>'full_name');

    -- Create wallet for new user
    PERFORM create_user_wallet(NEW.id, NEW.phone);

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the user creation
        RAISE WARNING 'Error creating user profile/wallet: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 20: Create trigger for automatic user setup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Step 21: Verification and test data
DO $$
DECLARE
    test_user_id UUID := uuid_generate_v4();
    profile_result JSON;
    wallet_result JSON;
    transaction_result JSON;
BEGIN
    RAISE NOTICE '🧪 Testing database setup...';

    -- Test profile creation
    SELECT create_user_profile(test_user_id, '+************', '<EMAIL>', 'Test User') INTO profile_result;
    RAISE NOTICE 'Profile creation: %', profile_result->>'success';

    -- Test wallet creation
    SELECT create_user_wallet(test_user_id, '+************') INTO wallet_result;
    RAISE NOTICE 'Wallet creation: %', wallet_result->>'success';

    -- Test transaction creation
    SELECT create_user_transaction(test_user_id, 'deposit', 50000.00, 'Initial deposit') INTO transaction_result;
    RAISE NOTICE 'Transaction creation: %', transaction_result->>'success';

    -- Test balance retrieval
    SELECT get_user_wallet_balance(test_user_id) INTO wallet_result;
    RAISE NOTICE 'Balance retrieval: %', wallet_result->>'success';

    RAISE NOTICE '✅ Database setup completed successfully!';
    RAISE NOTICE '📊 Tables created: user_profiles, wallets, transactions';
    RAISE NOTICE '🔧 Functions created: create_user_profile, create_user_wallet, get_user_wallet_balance, create_user_transaction';
    RAISE NOTICE '🔐 RLS policies enabled for data security';
    RAISE NOTICE '🚀 JiraniPay database is ready for production!';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test failed: %', SQLERRM;
END $$;
