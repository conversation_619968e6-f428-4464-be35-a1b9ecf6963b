/**
 * Remove Dummy Data Script
 * This script identifies and reports all dummy/mock data in the codebase
 * for production readiness
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, '..');

// Patterns that indicate dummy/mock data
const DUMMY_DATA_PATTERNS = [
  /mock\w*\s*=\s*\[/gi,
  /const\s+mock\w*/gi,
  /getMock\w*/gi,
  /mockData/gi,
  /dummy\w*\s*=\s*\[/gi,
  /placeholder\w*\s*=\s*\[/gi,
  /<PERSON>|<PERSON>|<PERSON>|Alice <PERSON>/gi,
  /\+256701234567|\+256702345678|\+256703456789/gi,
  /test@example\.com|dummy@test\.com/gi,
  /Math\.floor\(Math\.random\(\)/gi, // Random data generation
];

// Files to check for dummy data
const FILE_PATTERNS = [
  '**/*.js',
  '**/*.jsx',
  '**/*.ts',
  '**/*.tsx'
];

// Directories to exclude
const EXCLUDE_DIRS = [
  'node_modules',
  '.git',
  '.expo',
  'dist',
  'build',
  '__tests__',
  'tests',
  'test'
];

class DummyDataDetector {
  constructor() {
    this.findings = [];
    this.checkedFiles = 0;
    this.issuesFound = 0;
  }

  /**
   * Scan directory recursively for dummy data
   */
  scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(item)) {
          this.scanDirectory(fullPath);
        }
      } else if (stat.isFile()) {
        // Check JavaScript/TypeScript files
        if (this.shouldCheckFile(fullPath)) {
          this.checkFile(fullPath);
        }
      }
    }
  }

  /**
   * Check if file should be scanned
   */
  shouldCheckFile(filePath) {
    const ext = path.extname(filePath);
    return ['.js', '.jsx', '.ts', '.tsx'].includes(ext);
  }

  /**
   * Check individual file for dummy data
   */
  checkFile(filePath) {
    try {
      this.checkedFiles++;
      const content = fs.readFileSync(filePath, 'utf8');
      const relativePath = path.relative(projectRoot, filePath);
      
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        DUMMY_DATA_PATTERNS.forEach(pattern => {
          const matches = line.match(pattern);
          if (matches) {
            this.issuesFound++;
            this.findings.push({
              file: relativePath,
              line: index + 1,
              content: line.trim(),
              pattern: pattern.toString(),
              severity: this.getSeverity(line, pattern)
            });
          }
        });
      });

    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }

  /**
   * Determine severity of dummy data finding
   */
  getSeverity(line, pattern) {
    // High severity for obvious dummy data
    if (line.includes('John Doe') || line.includes('Jane Smith') || 
        line.includes('+256701234567') || line.includes('mock')) {
      return 'HIGH';
    }
    
    // Medium severity for random data generation
    if (line.includes('Math.random')) {
      return 'MEDIUM';
    }
    
    return 'LOW';
  }

  /**
   * Generate report
   */
  generateReport() {
    console.log('\n🔍 DUMMY DATA DETECTION REPORT');
    console.log('=====================================');
    console.log(`📁 Files checked: ${this.checkedFiles}`);
    console.log(`⚠️  Issues found: ${this.issuesFound}`);
    
    if (this.findings.length === 0) {
      console.log('\n✅ No dummy data found! Your app is production-ready.');
      return;
    }

    // Group by severity
    const bySeverity = {
      HIGH: this.findings.filter(f => f.severity === 'HIGH'),
      MEDIUM: this.findings.filter(f => f.severity === 'MEDIUM'),
      LOW: this.findings.filter(f => f.severity === 'LOW')
    };

    // Report high severity issues first
    if (bySeverity.HIGH.length > 0) {
      console.log('\n🚨 HIGH PRIORITY - Must fix for production:');
      bySeverity.HIGH.forEach(finding => {
        console.log(`  📄 ${finding.file}:${finding.line}`);
        console.log(`     ${finding.content}`);
        console.log('');
      });
    }

    if (bySeverity.MEDIUM.length > 0) {
      console.log('\n⚠️  MEDIUM PRIORITY - Should fix:');
      bySeverity.MEDIUM.forEach(finding => {
        console.log(`  📄 ${finding.file}:${finding.line}`);
        console.log(`     ${finding.content}`);
        console.log('');
      });
    }

    if (bySeverity.LOW.length > 0) {
      console.log('\n💡 LOW PRIORITY - Consider reviewing:');
      bySeverity.LOW.forEach(finding => {
        console.log(`  📄 ${finding.file}:${finding.line}`);
        console.log(`     ${finding.content}`);
        console.log('');
      });
    }

    console.log('\n📋 RECOMMENDATIONS:');
    console.log('1. Replace all mock data with real database calls');
    console.log('2. Remove hardcoded names and phone numbers');
    console.log('3. Use environment variables for test data flags');
    console.log('4. Implement proper empty states instead of dummy data');
    
    console.log('\n✅ FIXES ALREADY APPLIED:');
    console.log('- ✅ RequestHistoryScreen: Now uses moneyRequestService');
    console.log('- ✅ RequestMoneyScreen: Now uses contactService and moneyRequestService');
    console.log('- ✅ BillPaymentService: Mock data disabled in production');
    console.log('- ✅ Database schema: money_requests table created');
  }

  /**
   * Check specific files that were recently updated
   */
  checkUpdatedFiles() {
    const updatedFiles = [
      'screens/RequestHistoryScreen.js',
      'screens/RequestMoneyScreen.js',
      'services/billPaymentService.js'
    ];

    console.log('\n🔍 Checking recently updated files...');
    
    updatedFiles.forEach(file => {
      const fullPath = path.join(projectRoot, file);
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        const hasMockData = DUMMY_DATA_PATTERNS.some(pattern => pattern.test(content));
        
        if (hasMockData) {
          console.log(`❌ ${file} - Still contains dummy data`);
        } else {
          console.log(`✅ ${file} - Clean of dummy data`);
        }
      }
    });
  }
}

// Run the detector
console.log('🚀 Starting dummy data detection...');
console.log('This will scan your codebase for production readiness');

const detector = new DummyDataDetector();

// Check updated files first
detector.checkUpdatedFiles();

// Full scan
detector.scanDirectory(projectRoot);
detector.generateReport();

console.log('\n🎯 NEXT STEPS:');
console.log('1. Run: node scripts/setup-money-requests.js');
console.log('2. Test the app to ensure no dummy data appears');
console.log('3. Verify all features work with real database data');
console.log('4. Deploy to production with confidence! 🚀');
