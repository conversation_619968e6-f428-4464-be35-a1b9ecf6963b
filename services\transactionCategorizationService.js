/**
 * Smart Transaction Categorization Service
 * AI-powered transaction categorization with machine learning capabilities
 * Provides intelligent categorization, learning algorithms, and pattern recognition
 */

import supabase from './supabaseClient';
import analyticsPerformanceService from './analyticsPerformanceService';

class TransactionCategorizationService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes
    this.learningData = new Map();
    this.categoryPatterns = new Map();
    this.userPreferences = new Map();
    
    // Predefined category system
    this.predefinedCategories = {
      'food_dining': {
        id: 'food_dining',
        name: 'Food & Dining',
        icon: 'restaurant',
        color: '#FF6B6B',
        keywords: ['restaurant', 'cafe', 'food', 'dining', 'pizza', 'burger', 'coffee', 'bar', 'pub', 'kitchen', 'meal', 'lunch', 'dinner', 'breakfast'],
        patterns: [/restaurant/i, /cafe/i, /food/i, /pizza/i, /coffee/i, /dining/i, /kitchen/i, /meal/i]
      },
      'transportation': {
        id: 'transportation',
        name: 'Transportation',
        icon: 'car',
        color: '#4ECDC4',
        keywords: ['uber', 'taxi', 'bus', 'train', 'fuel', 'gas', 'petrol', 'parking', 'transport', 'metro', 'subway', 'flight', 'airline'],
        patterns: [/uber/i, /taxi/i, /fuel/i, /gas/i, /petrol/i, /parking/i, /transport/i, /airline/i]
      },
      'shopping': {
        id: 'shopping',
        name: 'Shopping',
        icon: 'bag',
        color: '#45B7D1',
        keywords: ['shop', 'store', 'mall', 'market', 'supermarket', 'grocery', 'retail', 'purchase', 'buy', 'clothing', 'fashion'],
        patterns: [/shop/i, /store/i, /mall/i, /market/i, /supermarket/i, /grocery/i, /retail/i]
      },
      'bills_utilities': {
        id: 'bills_utilities',
        name: 'Bills & Utilities',
        icon: 'receipt',
        color: '#96CEB4',
        keywords: ['electric', 'water', 'internet', 'phone', 'utility', 'bill', 'payment', 'subscription', 'service', 'insurance'],
        patterns: [/electric/i, /water/i, /internet/i, /utility/i, /bill/i, /subscription/i, /insurance/i]
      },
      'entertainment': {
        id: 'entertainment',
        name: 'Entertainment',
        icon: 'play-circle',
        color: '#FFEAA7',
        keywords: ['movie', 'cinema', 'theater', 'game', 'music', 'streaming', 'netflix', 'spotify', 'entertainment', 'fun'],
        patterns: [/movie/i, /cinema/i, /theater/i, /game/i, /music/i, /streaming/i, /netflix/i, /spotify/i]
      },
      'healthcare': {
        id: 'healthcare',
        name: 'Healthcare',
        icon: 'medical',
        color: '#FD79A8',
        keywords: ['hospital', 'doctor', 'pharmacy', 'medical', 'health', 'clinic', 'medicine', 'drug', 'treatment'],
        patterns: [/hospital/i, /doctor/i, /pharmacy/i, /medical/i, /health/i, /clinic/i, /medicine/i]
      },
      'education': {
        id: 'education',
        name: 'Education',
        icon: 'school',
        color: '#A29BFE',
        keywords: ['school', 'university', 'education', 'course', 'training', 'book', 'tuition', 'fee', 'learning'],
        patterns: [/school/i, /university/i, /education/i, /course/i, /training/i, /tuition/i]
      },
      'personal_care': {
        id: 'personal_care',
        name: 'Personal Care',
        icon: 'person',
        color: '#74B9FF',
        keywords: ['salon', 'spa', 'beauty', 'haircut', 'cosmetic', 'personal', 'care', 'hygiene'],
        patterns: [/salon/i, /spa/i, /beauty/i, /haircut/i, /cosmetic/i, /personal/i]
      },
      'transfer': {
        id: 'transfer',
        name: 'Transfers',
        icon: 'swap-horizontal',
        color: '#636E72',
        keywords: ['transfer', 'send', 'receive', 'payment', 'remittance', 'wire'],
        patterns: [/transfer/i, /send/i, /receive/i, /remittance/i, /wire/i]
      },
      'other': {
        id: 'other',
        name: 'Other',
        icon: 'ellipsis-horizontal',
        color: '#B2BEC3',
        keywords: [],
        patterns: []
      }
    };
  }

  /**
   * Initialize categorization service
   */
  async initialize(userId) {
    try {
      console.log('🤖 Initializing transaction categorization service for user:', userId);
      
      // Load user preferences and learning data
      await this.loadUserPreferences(userId);
      await this.loadLearningData(userId);
      await this.buildCategoryPatterns(userId);
      
      console.log('✅ Transaction categorization service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing categorization service:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Categorize a single transaction using ML-based approach
   */
  async categorizeTransaction(transaction, userId) {
    try {
      const cacheKey = `categorize_${transaction.id}_${userId}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      console.log('🤖 Categorizing transaction:', transaction.id);

      // Extract features from transaction
      const features = this.extractFeatures(transaction);
      
      // Get category predictions with confidence scores
      const predictions = await this.predictCategory(features, userId);
      
      // Apply learning algorithm adjustments
      const adjustedPredictions = this.applyLearningAdjustments(predictions, features, userId);
      
      // Select best category with confidence score
      const bestPrediction = adjustedPredictions[0];
      
      const result = {
        transactionId: transaction.id,
        category: bestPrediction.category,
        confidence: bestPrediction.confidence,
        suggestions: adjustedPredictions.slice(1, 4), // Top 3 alternatives
        features: features,
        timestamp: new Date().toISOString()
      };

      this.setCache(cacheKey, result);
      
      console.log('✅ Transaction categorized:', result.category, 'confidence:', result.confidence);
      return { success: true, data: result };
    } catch (error) {
      console.error('❌ Error categorizing transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Bulk categorize multiple transactions
   */
  async bulkCategorizeTransactions(transactions, userId) {
    try {
      console.log('🤖 Bulk categorizing', transactions.length, 'transactions');
      
      const results = [];
      const batchSize = 10; // Process in batches for performance
      
      for (let i = 0; i < transactions.length; i += batchSize) {
        const batch = transactions.slice(i, i + batchSize);
        
        const batchPromises = batch.map(transaction => 
          this.categorizeTransaction(transaction, userId)
        );
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value.success) {
            results.push(result.value.data);
          } else {
            console.warn('⚠️ Failed to categorize transaction:', batch[index].id);
            results.push({
              transactionId: batch[index].id,
              category: 'other',
              confidence: 0.1,
              suggestions: [],
              features: {},
              error: result.reason?.message || 'Categorization failed'
            });
          }
        });
      }
      
      console.log('✅ Bulk categorization completed:', results.length, 'transactions');
      return { success: true, data: results };
    } catch (error) {
      console.error('❌ Error in bulk categorization:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract features from transaction for ML processing
   */
  extractFeatures(transaction) {
    const features = {
      // Basic transaction data
      amount: Math.abs(transaction.amount || 0),
      amountRange: this.getAmountRange(Math.abs(transaction.amount || 0)),
      
      // Text features
      description: (transaction.description || '').toLowerCase(),
      merchantName: (transaction.merchant_name || transaction.recipient || '').toLowerCase(),
      reference: (transaction.reference || '').toLowerCase(),
      
      // Temporal features
      hour: new Date(transaction.created_at).getHours(),
      dayOfWeek: new Date(transaction.created_at).getDay(),
      isWeekend: [0, 6].includes(new Date(transaction.created_at).getDay()),
      
      // Transaction type
      transactionType: transaction.transaction_type || 'unknown',
      isDebit: (transaction.amount || 0) < 0,
      
      // Combined text for pattern matching
      combinedText: [
        transaction.description,
        transaction.merchant_name,
        transaction.recipient,
        transaction.reference
      ].filter(Boolean).join(' ').toLowerCase()
    };

    // Extract keywords from combined text
    features.keywords = this.extractKeywords(features.combinedText);
    
    return features;
  }

  /**
   * Predict category using pattern matching and ML algorithms
   */
  async predictCategory(features, userId) {
    const predictions = [];
    
    // 1. Pattern-based prediction
    const patternPredictions = this.predictByPatterns(features);
    predictions.push(...patternPredictions);
    
    // 2. Keyword-based prediction
    const keywordPredictions = this.predictByKeywords(features);
    predictions.push(...keywordPredictions);
    
    // 3. Amount-based prediction
    const amountPredictions = this.predictByAmount(features, userId);
    predictions.push(...amountPredictions);
    
    // 4. Temporal pattern prediction
    const temporalPredictions = this.predictByTemporal(features, userId);
    predictions.push(...temporalPredictions);
    
    // 5. User history-based prediction
    const historyPredictions = await this.predictByHistory(features, userId);
    predictions.push(...historyPredictions);
    
    // Combine and rank predictions
    const combinedPredictions = this.combineAndRankPredictions(predictions);
    
    // Ensure we always have a fallback
    if (combinedPredictions.length === 0) {
      combinedPredictions.push({
        category: 'other',
        confidence: 0.1,
        source: 'fallback'
      });
    }
    
    return combinedPredictions;
  }

  /**
   * Pattern-based category prediction
   */
  predictByPatterns(features) {
    const predictions = [];
    
    Object.values(this.predefinedCategories).forEach(category => {
      let confidence = 0;
      
      category.patterns.forEach(pattern => {
        if (pattern.test(features.combinedText)) {
          confidence += 0.3; // High confidence for pattern matches
        }
      });
      
      if (confidence > 0) {
        predictions.push({
          category: category.id,
          confidence: Math.min(confidence, 0.9),
          source: 'pattern'
        });
      }
    });
    
    return predictions;
  }

  /**
   * Keyword-based category prediction
   */
  predictByKeywords(features) {
    const predictions = [];
    
    Object.values(this.predefinedCategories).forEach(category => {
      let matchCount = 0;
      let totalKeywords = category.keywords.length;
      
      if (totalKeywords === 0) return;
      
      category.keywords.forEach(keyword => {
        if (features.combinedText.includes(keyword.toLowerCase())) {
          matchCount++;
        }
      });
      
      if (matchCount > 0) {
        const confidence = (matchCount / totalKeywords) * 0.7; // Medium confidence for keywords
        predictions.push({
          category: category.id,
          confidence: Math.min(confidence, 0.8),
          source: 'keyword'
        });
      }
    });
    
    return predictions;
  }

  /**
   * Amount-based category prediction
   */
  predictByAmount(features, userId) {
    const predictions = [];
    const userPatterns = this.userPreferences.get(userId);
    
    if (!userPatterns || !userPatterns.amountPatterns) return predictions;
    
    Object.entries(userPatterns.amountPatterns).forEach(([category, pattern]) => {
      const { min, max, frequency } = pattern;
      
      if (features.amount >= min && features.amount <= max) {
        const confidence = Math.min(frequency / 10, 0.6); // Lower confidence for amount-based
        predictions.push({
          category,
          confidence,
          source: 'amount'
        });
      }
    });
    
    return predictions;
  }

  /**
   * Temporal pattern prediction
   */
  predictByTemporal(features, userId) {
    const predictions = [];
    const userPatterns = this.userPreferences.get(userId);
    
    if (!userPatterns || !userPatterns.temporalPatterns) return predictions;
    
    Object.entries(userPatterns.temporalPatterns).forEach(([category, pattern]) => {
      let confidence = 0;
      
      // Check hour patterns
      if (pattern.hours && pattern.hours.includes(features.hour)) {
        confidence += 0.2;
      }
      
      // Check day of week patterns
      if (pattern.daysOfWeek && pattern.daysOfWeek.includes(features.dayOfWeek)) {
        confidence += 0.2;
      }
      
      // Check weekend patterns
      if (pattern.isWeekend !== undefined && pattern.isWeekend === features.isWeekend) {
        confidence += 0.1;
      }
      
      if (confidence > 0) {
        predictions.push({
          category,
          confidence: Math.min(confidence, 0.5),
          source: 'temporal'
        });
      }
    });
    
    return predictions;
  }

  /**
   * History-based prediction using user's past transactions
   */
  async predictByHistory(features, userId) {
    try {
      const { data: similarTransactions, error } = await supabase
        .from('transactions')
        .select('category, merchant_name, description, amount')
        .eq('user_id', userId)
        .not('category', 'is', null)
        .limit(100)
        .order('created_at', { ascending: false });

      if (error || !similarTransactions) return [];

      const predictions = [];
      const categoryScores = {};

      similarTransactions.forEach(tx => {
        if (!tx.category) return;

        let similarity = 0;

        // Merchant name similarity
        if (tx.merchant_name && features.merchantName) {
          similarity += this.calculateTextSimilarity(
            tx.merchant_name.toLowerCase(),
            features.merchantName
          ) * 0.4;
        }

        // Description similarity
        if (tx.description && features.description) {
          similarity += this.calculateTextSimilarity(
            tx.description.toLowerCase(),
            features.description
          ) * 0.3;
        }

        // Amount similarity
        const amountSimilarity = 1 - Math.abs(Math.abs(tx.amount) - features.amount) /
          Math.max(Math.abs(tx.amount), features.amount);
        similarity += amountSimilarity * 0.3;

        if (similarity > 0.3) { // Threshold for considering similar
          categoryScores[tx.category] = (categoryScores[tx.category] || 0) + similarity;
        }
      });

      Object.entries(categoryScores).forEach(([category, score]) => {
        predictions.push({
          category,
          confidence: Math.min(score / 3, 0.8), // Normalize and cap confidence
          source: 'history'
        });
      });

      return predictions;
    } catch (error) {
      console.error('❌ Error in history-based prediction:', error);
      return [];
    }
  }

  /**
   * Combine and rank predictions from different sources
   */
  combineAndRankPredictions(predictions) {
    const categoryScores = {};

    predictions.forEach(prediction => {
      const { category, confidence, source } = prediction;

      if (!categoryScores[category]) {
        categoryScores[category] = {
          category,
          totalConfidence: 0,
          sources: [],
          count: 0
        };
      }

      // Weight different sources
      const sourceWeights = {
        pattern: 1.0,
        keyword: 0.8,
        history: 0.9,
        amount: 0.6,
        temporal: 0.5
      };

      const weightedConfidence = confidence * (sourceWeights[source] || 0.5);
      categoryScores[category].totalConfidence += weightedConfidence;
      categoryScores[category].sources.push(source);
      categoryScores[category].count++;
    });

    // Convert to array and sort by confidence
    const rankedPredictions = Object.values(categoryScores)
      .map(score => ({
        category: score.category,
        confidence: Math.min(score.totalConfidence / Math.sqrt(score.count), 0.95),
        sources: [...new Set(score.sources)]
      }))
      .sort((a, b) => b.confidence - a.confidence);

    return rankedPredictions;
  }

  /**
   * Apply learning algorithm adjustments
   */
  applyLearningAdjustments(predictions, features, userId) {
    const learningData = this.learningData.get(userId);
    if (!learningData) return predictions;

    return predictions.map(prediction => {
      const adjustments = learningData[prediction.category];
      if (!adjustments) return prediction;

      // Apply user feedback adjustments
      let adjustedConfidence = prediction.confidence;

      if (adjustments.userCorrections > 0) {
        const correctionRatio = adjustments.correctPredictions /
          (adjustments.correctPredictions + adjustments.userCorrections);
        adjustedConfidence *= correctionRatio;
      }

      // Apply frequency adjustments
      if (adjustments.frequency > 0) {
        adjustedConfidence *= Math.min(1 + (adjustments.frequency / 100), 1.2);
      }

      return {
        ...prediction,
        confidence: Math.min(Math.max(adjustedConfidence, 0.05), 0.95)
      };
    });
  }

  /**
   * Learn from user feedback
   */
  async learnFromFeedback(transactionId, correctCategory, predictedCategory, userId) {
    try {
      console.log('🧠 Learning from user feedback:', transactionId, correctCategory);

      // Update learning data
      const learningData = this.learningData.get(userId) || {};

      // Update correct category stats
      if (!learningData[correctCategory]) {
        learningData[correctCategory] = {
          correctPredictions: 0,
          userCorrections: 0,
          frequency: 0
        };
      }

      if (correctCategory === predictedCategory) {
        learningData[correctCategory].correctPredictions++;
      } else {
        learningData[correctCategory].userCorrections++;

        // Decrease confidence for incorrect prediction
        if (learningData[predictedCategory]) {
          learningData[predictedCategory].userCorrections++;
        }
      }

      learningData[correctCategory].frequency++;
      this.learningData.set(userId, learningData);

      // Store learning data in database
      await this.saveLearningData(userId, learningData);

      // Update transaction category in database
      const { error } = await supabase
        .from('transactions')
        .update({
          category: correctCategory,
          category_confidence: correctCategory === predictedCategory ? 0.9 : 1.0,
          updated_at: new Date().toISOString()
        })
        .eq('id', transactionId)
        .eq('user_id', userId);

      if (error) throw error;

      console.log('✅ Learning data updated successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error learning from feedback:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get categorization suggestions for a transaction
   */
  async getSuggestions(transactionId, userId) {
    try {
      const { data: transaction, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .eq('user_id', userId)
        .single();

      if (error || !transaction) {
        throw new Error('Transaction not found');
      }

      const result = await this.categorizeTransaction(transaction, userId);

      if (!result.success) {
        throw new Error(result.error);
      }

      return {
        success: true,
        data: {
          currentCategory: transaction.category,
          suggestions: [
            {
              category: result.data.category,
              confidence: result.data.confidence,
              isPrimary: true
            },
            ...result.data.suggestions.map(s => ({
              ...s,
              isPrimary: false
            }))
          ]
        }
      };
    } catch (error) {
      console.error('❌ Error getting suggestions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Utility methods
   */
  getAmountRange(amount) {
    if (amount < 1000) return 'micro';
    if (amount < 10000) return 'small';
    if (amount < 100000) return 'medium';
    if (amount < 1000000) return 'large';
    return 'xlarge';
  }

  extractKeywords(text) {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    return text
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.includes(word))
      .slice(0, 10); // Limit to 10 keywords
  }

  calculateTextSimilarity(text1, text2) {
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  /**
   * Cache management
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Load user preferences and patterns
   */
  async loadUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from('user_categorization_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error;
      }

      if (data) {
        this.userPreferences.set(userId, {
          amountPatterns: data.amount_patterns || {},
          temporalPatterns: data.temporal_patterns || {},
          customCategories: data.custom_categories || {},
          preferences: data.preferences || {}
        });
      }
    } catch (error) {
      console.error('❌ Error loading user preferences:', error);
    }
  }

  /**
   * Load learning data
   */
  async loadLearningData(userId) {
    try {
      const { data, error } = await supabase
        .from('categorization_learning_data')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        throw error;
      }

      if (data) {
        this.learningData.set(userId, data.learning_data || {});
      }
    } catch (error) {
      console.error('❌ Error loading learning data:', error);
    }
  }

  /**
   * Save learning data
   */
  async saveLearningData(userId, learningData) {
    try {
      const { error } = await supabase
        .from('categorization_learning_data')
        .upsert({
          user_id: userId,
          learning_data: learningData,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('❌ Error saving learning data:', error);
    }
  }

  /**
   * Build category patterns from user data
   */
  async buildCategoryPatterns(userId) {
    try {
      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('category, merchant_name, description, amount, created_at')
        .eq('user_id', userId)
        .not('category', 'is', null)
        .limit(500)
        .order('created_at', { ascending: false });

      if (error || !transactions) return;

      const patterns = {};

      transactions.forEach(tx => {
        if (!patterns[tx.category]) {
          patterns[tx.category] = {
            merchants: new Set(),
            keywords: new Set(),
            amountRanges: [],
            hours: new Set(),
            daysOfWeek: new Set()
          };
        }

        const pattern = patterns[tx.category];

        if (tx.merchant_name) {
          pattern.merchants.add(tx.merchant_name.toLowerCase());
        }

        if (tx.description) {
          const keywords = this.extractKeywords(tx.description.toLowerCase());
          keywords.forEach(keyword => pattern.keywords.add(keyword));
        }

        pattern.amountRanges.push(Math.abs(tx.amount));

        const date = new Date(tx.created_at);
        pattern.hours.add(date.getHours());
        pattern.daysOfWeek.add(date.getDay());
      });

      this.categoryPatterns.set(userId, patterns);
    } catch (error) {
      console.error('❌ Error building category patterns:', error);
    }
  }

  /**
   * Get predefined categories
   */
  getPredefinedCategories() {
    return Object.values(this.predefinedCategories);
  }

  /**
   * Get category by ID
   */
  getCategoryById(categoryId) {
    return this.predefinedCategories[categoryId] || null;
  }

  /**
   * Cleanup
   */
  cleanup() {
    this.cache.clear();
    this.learningData.clear();
    this.categoryPatterns.clear();
    this.userPreferences.clear();
  }
}

export default new TransactionCategorizationService();
