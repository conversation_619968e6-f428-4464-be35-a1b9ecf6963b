#!/usr/bin/env node

/**
 * Production SMS Configuration Checker for JiraniPay
 * 
 * This script validates that SMS provider is properly configured
 * in Supabase and provides detailed diagnostics and setup guidance.
 * 
 * Usage: node scripts/checkSMSConfiguration.js
 */

import { testSMSConfiguration, getSMSReport } from '../utils/smsMonitoring.js';
import { isProductionMode, getEnvironmentName } from '../config/environment.js';
import authService from '../services/authService.js';

console.log('🔍 JiraniPay SMS Configuration Checker');
console.log('=====================================');

const runSMSConfigurationCheck = async () => {
  try {
    console.log(`📋 Environment: ${getEnvironmentName().toUpperCase()}`);
    console.log(`🚀 Production Mode: ${isProductionMode() ? 'ENABLED' : 'DISABLED'}`);
    console.log('');

    if (!isProductionMode()) {
      console.log('⚠️  WARNING: Not running in production mode');
      console.log('   SMS configuration check is most relevant for production');
      console.log('   Set PRODUCTION_MODE = true in config/environment.js');
      console.log('');
    }

    // Step 1: Test SMS Configuration
    console.log('🔍 Step 1: Testing SMS Provider Configuration...');
    const configTest = await testSMSConfiguration();
    
    console.log(`📅 Test Timestamp: ${configTest.timestamp}`);
    console.log(`🌍 Environment: ${configTest.environment}`);
    console.log('');

    // Display test results
    let allTestsPassed = true;
    configTest.tests.forEach((test, index) => {
      const statusIcon = test.status === 'PASSED' ? '✅' : 
                        test.status === 'FAILED' ? '❌' : '⚠️';
      
      console.log(`${statusIcon} Test ${index + 1}: ${test.test}`);
      console.log(`   Status: ${test.status}`);
      
      if (test.message) {
        console.log(`   Message: ${test.message}`);
      }
      
      if (test.error) {
        console.log(`   Error: ${test.error}`);
        allTestsPassed = false;
      }
      
      if (test.solution) {
        console.log(`   Solution: ${test.solution}`);
      }
      
      if (test.details) {
        console.log(`   Details: ${test.details}`);
      }
      
      console.log('');
    });

    // Step 2: Validate AuthService SMS Configuration
    console.log('🔍 Step 2: Validating AuthService SMS Configuration...');
    try {
      const authValidation = await authService.validateSMSConfiguration();
      
      if (authValidation.configured) {
        console.log('✅ AuthService SMS validation: PASSED');
        console.log(`   Message: ${authValidation.message}`);
      } else {
        console.log('❌ AuthService SMS validation: FAILED');
        console.log(`   Error: ${authValidation.error}`);
        console.log(`   Message: ${authValidation.message}`);
        console.log(`   Solution: ${authValidation.solution}`);
        allTestsPassed = false;
      }
    } catch (error) {
      console.log('❌ AuthService SMS validation: ERROR');
      console.log(`   Error: ${error.message}`);
      allTestsPassed = false;
    }
    console.log('');

    // Step 3: Generate SMS Report
    console.log('📊 Step 3: SMS Delivery Statistics...');
    const smsReport = getSMSReport();
    
    console.log(`📈 Statistics:`);
    console.log(`   Total Sent: ${smsReport.statistics.totalSent}`);
    console.log(`   Delivered: ${smsReport.statistics.delivered}`);
    console.log(`   Failed: ${smsReport.statistics.failed}`);
    console.log(`   Delivery Rate: ${smsReport.statistics.deliveryRate}`);
    console.log(`   Uptime: ${smsReport.uptime}`);
    console.log('');

    // Step 4: Recommendations
    if (smsReport.recommendations.length > 0) {
      console.log('💡 Recommendations:');
      smsReport.recommendations.forEach((rec, index) => {
        const priorityIcon = rec.priority === 'HIGH' ? '🔴' : 
                           rec.priority === 'MEDIUM' ? '🟡' : '🟢';
        
        console.log(`${priorityIcon} ${index + 1}. ${rec.issue} (${rec.priority})`);
        console.log(`   Recommendation: ${rec.recommendation}`);
        console.log(`   Action: ${rec.action}`);
        console.log('');
      });
    }

    // Final Assessment
    console.log('🎯 FINAL ASSESSMENT');
    console.log('==================');
    
    if (allTestsPassed) {
      console.log('✅ SMS CONFIGURATION: WORKING');
      console.log('   Your SMS provider is properly configured');
      console.log('   JiraniPay registration should work correctly');
      console.log('');
      console.log('📱 Next Steps:');
      console.log('   1. Test registration with real phone numbers');
      console.log('   2. Monitor SMS delivery rates');
      console.log('   3. Set up cost monitoring in SMS provider dashboard');
    } else {
      console.log('❌ SMS CONFIGURATION: ISSUES DETECTED');
      console.log('   SMS provider configuration needs attention');
      console.log('   JiraniPay registration will NOT work until fixed');
      console.log('');
      console.log('🚨 CRITICAL ACTIONS REQUIRED:');
      console.log('   1. Configure SMS provider in Supabase Dashboard');
      console.log('   2. Go to: Authentication → Settings → Phone Auth');
      console.log('   3. Set up Twilio, MessageBird, or another provider');
      console.log('   4. See: docs/PRODUCTION_SMS_SETUP_GUIDE.md');
      console.log('');
      console.log('📞 Support Resources:');
      console.log('   - Supabase Support: https://supabase.com/support');
      console.log('   - Twilio Console: https://console.twilio.com/');
      console.log('   - Setup Guide: docs/PRODUCTION_SMS_SETUP_GUIDE.md');
    }

    console.log('');
    console.log('🔄 Re-run this script after making configuration changes');
    
    return allTestsPassed;

  } catch (error) {
    console.error('❌ SMS Configuration Check Failed:', error);
    console.error('');
    console.error('🚨 CRITICAL ERROR:');
    console.error('   Unable to validate SMS configuration');
    console.error('   This indicates a serious configuration issue');
    console.error('');
    console.error('📋 Troubleshooting Steps:');
    console.error('   1. Check Supabase project URL and API key');
    console.error('   2. Verify network connectivity');
    console.error('   3. Check config/environment.js settings');
    console.error('   4. Review Supabase project status');
    
    return false;
  }
};

// Export for use in other scripts
export default runSMSConfigurationCheck;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSMSConfigurationCheck()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Script execution failed:', error);
      process.exit(1);
    });
}
