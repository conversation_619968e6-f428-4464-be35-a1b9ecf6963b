import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import currencyService from '../services/currencyService';
import walletService from '../services/walletService';

/**
 * RequestAmountScreen - Specify amount and details for money request
 */
const RequestAmountScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [amount, setAmount] = useState('');
  const [note, setNote] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState(null);

  const { contact, user } = route?.params || {};

  useEffect(() => {
    if (!contact) {
      Alert.alert('Error', 'Contact information not found');
      navigation.goBack();
    }
  }, [contact]);

  const requestReasons = [
    { id: 'bill_split', title: 'Bill Split', icon: 'receipt-outline' },
    { id: 'loan_repay', title: 'Loan Repayment', icon: 'card-outline' },
    { id: 'shared_expense', title: 'Shared Expense', icon: 'people-outline' },
    { id: 'emergency', title: 'Emergency', icon: 'warning-outline' },
    { id: 'gift', title: 'Gift/Contribution', icon: 'gift-outline' },
    { id: 'other', title: 'Other', icon: 'ellipsis-horizontal-outline' }
  ];

  const handleAmountChange = (text) => {
    // Remove any non-numeric characters except decimal point
    const cleanText = text.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = cleanText.split('.');
    if (parts.length > 2) {
      return;
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }
    
    setAmount(cleanText);
  };

  const handleReasonSelect = (reason) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedReason(reason);
  };

  const handleSendRequest = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid amount');
      return;
    }

    if (!selectedReason) {
      Alert.alert('Select Reason', 'Please select a reason for this request');
      return;
    }

    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const requestAmount = parseFloat(amount);
      
      // Create request data
      const requestData = {
        id: `req_${Date.now()}`,
        fromUser: user,
        toContact: contact,
        amount: requestAmount,
        reason: selectedReason,
        note: note.trim(),
        status: 'pending',
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      };

      // In a real app, this would send the request via API
      // For now, we'll simulate success
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Safe currency formatting for alert
      const formattedAmount = (() => {
        try {
          if (currencyService && typeof currencyService.formatCurrency === 'function') {
            return currencyService.formatCurrency(requestAmount);
          } else {
            return `UGX ${requestAmount.toLocaleString()}`;
          }
        } catch (error) {
          console.warn('Currency formatting error in alert:', error);
          return `UGX ${requestAmount.toLocaleString()}`;
        }
      })();

      Alert.alert(
        'Request Sent!',
        `Your request for ${formattedAmount} has been sent to ${contact.name}. They will receive a notification and can approve or decline your request.`,
        [
          {
            text: 'View Requests',
            onPress: () => {
              // Navigate to requests history
              navigation.navigate('RequestHistory');
            }
          },
          {
            text: 'Send Another',
            onPress: () => {
              navigation.goBack();
            }
          },
          {
            text: 'Done',
            onPress: () => {
              navigation.navigate('MainApp');
            }
          }
        ]
      );

    } catch (error) {
      console.error('❌ Error sending request:', error);
      Alert.alert('Request Failed', 'Unable to send request. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (value) => {
    if (!value) return '';
    const num = parseFloat(value);
    if (isNaN(num)) return '';

    // Safe currency formatting with fallback
    try {
      if (currencyService && typeof currencyService.formatCurrency === 'function') {
        return currencyService.formatCurrency(num);
      } else {
        // Fallback formatting
        return `UGX ${num.toLocaleString()}`;
      }
    } catch (error) {
      console.warn('Currency formatting error:', error);
      return `UGX ${num.toLocaleString()}`;
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Request Money</Text>
        <Text style={styles.headerSubtitle}>From {contact?.name}</Text>
      </View>
      <View style={styles.headerRight} />
    </View>
  );

  const renderContactInfo = () => (
    <View style={styles.contactCard}>
      <View style={styles.contactAvatar}>
        <Text style={styles.contactAvatarText}>{contact?.avatar || '?'}</Text>
      </View>
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{contact?.name || 'Unknown'}</Text>
        <Text style={styles.contactPhone}>{contact?.phoneNumber || ''}</Text>
      </View>
    </View>
  );

  const renderAmountInput = () => (
    <View style={styles.amountSection}>
      <Text style={styles.sectionTitle}>Request Amount</Text>
      <View style={styles.amountInputContainer}>
        <Text style={styles.currencySymbol}>UGX</Text>
        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={handleAmountChange}
          placeholder="0"
          placeholderTextColor={Colors.neutral.warmGray}
          keyboardType="numeric"
          maxLength={10}
        />
      </View>
      {amount && (
        <Text style={styles.formattedAmount}>
          {formatAmount(amount)}
        </Text>
      )}
    </View>
  );

  const renderReasonSelector = () => (
    <View style={styles.reasonSection}>
      <Text style={styles.sectionTitle}>Reason for Request</Text>
      <View style={styles.reasonGrid}>
        {requestReasons.map((reason) => (
          <TouchableOpacity
            key={reason.id}
            style={[
              styles.reasonCard,
              selectedReason?.id === reason.id && styles.selectedReasonCard
            ]}
            onPress={() => handleReasonSelect(reason)}
            activeOpacity={0.7}
          >
            <Ionicons
              name={reason.icon}
              size={24}
              color={selectedReason?.id === reason.id ? Colors.neutral.white : Colors.primary.main}
            />
            <Text style={[
              styles.reasonText,
              selectedReason?.id === reason.id && styles.selectedReasonText
            ]}>
              {reason.title}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderNoteInput = () => (
    <View style={styles.noteSection}>
      <Text style={styles.sectionTitle}>Add Note (Optional)</Text>
      <TextInput
        style={styles.noteInput}
        value={note}
        onChangeText={setNote}
        placeholder="Add a note about this request..."
        placeholderTextColor={Colors.neutral.warmGray}
        multiline
        maxLength={200}
      />
      <Text style={styles.characterCount}>{note.length}/200</Text>
    </View>
  );

  const renderSendButton = () => (
    <TouchableOpacity
      style={[
        styles.sendButton,
        (!amount || !selectedReason || loading) && styles.sendButtonDisabled
      ]}
      onPress={handleSendRequest}
      disabled={!amount || !selectedReason || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator size="small" color={Colors.neutral.white} />
      ) : (
        <>
          <Ionicons name="paper-plane" size={20} color={Colors.neutral.white} />
          <Text style={styles.sendButtonText}>Send Request</Text>
        </>
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.neutral.cream} />
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderContactInfo()}
        {renderAmountInput()}
        {renderReasonSelector()}
        {renderNoteInput()}
      </ScrollView>

      <View style={styles.footer}>
        {renderSendButton()}
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  amountSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 12,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.primary.main,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  formattedAmount: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 8,
    textAlign: 'center',
  },
  reasonSection: {
    marginBottom: 24,
  },
  reasonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  reasonCard: {
    width: '48%',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  selectedReasonCard: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  reasonText: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },
  selectedReasonText: {
    color: Colors.neutral.white,
  },
  noteSection: {
    marginBottom: 24,
  },
  noteInput: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    padding: 16,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'right',
    marginTop: 4,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  sendButtonDisabled: {
    backgroundColor: Colors.neutral.warmGray,
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
});

export default RequestAmountScreen;
