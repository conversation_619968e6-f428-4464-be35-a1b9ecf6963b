import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabaseClient';
import * as FileSystem from 'expo-file-system';

class SupportTicketService {
  constructor() {
    this.tickets = [];
    this.isInitialized = false;
  }

  // =====================================================
  // INITIALIZATION
  // =====================================================

  async initialize() {
    try {
      console.log('🎫 Initializing Support Ticket Service...');
      this.isInitialized = true;
      console.log('✅ Support Ticket Service initialized');
    } catch (error) {
      console.error('❌ Error initializing Support Ticket Service:', error);
    }
  }

  // =====================================================
  // TICKET MANAGEMENT
  // =====================================================

  async createTicket(userId, ticketData) {
    try {
      const ticket = {
        id: `ticket_${Date.now()}`,
        user_id: userId,
        subject: ticketData.subject,
        description: ticketData.description,
        category: ticketData.category,
        priority: ticketData.priority || 'medium',
        status: 'open',
        attachments: ticketData.attachments || [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        assigned_agent: null,
        resolution: null,
        satisfaction_rating: null
      };

      // Save to database
      const { data, error } = await supabase
        .from('support_tickets')
        .insert(ticket)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating ticket:', error);
        // Fallback to local storage
        await this.saveTicketLocally(ticket);
        return { success: true, ticket, isLocal: true };
      }

      console.log('✅ Support ticket created successfully');
      return { success: true, ticket: data };
    } catch (error) {
      console.error('❌ Error creating support ticket:', error);
      return { success: false, error: error.message };
    }
  }

  async getTickets(userId) {
    try {
      // Try to get from database first
      const { data, error } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Database error getting tickets:', error);
        // Fallback to local storage
        return await this.getTicketsLocally(userId);
      }

      return { success: true, tickets: data };
    } catch (error) {
      console.error('❌ Error getting support tickets:', error);
      return { success: false, error: error.message };
    }
  }

  async updateTicketStatus(ticketId, status, resolution = null) {
    try {
      const updates = {
        status,
        updated_at: new Date().toISOString()
      };

      if (resolution) {
        updates.resolution = resolution;
      }

      const { data, error } = await supabase
        .from('support_tickets')
        .update(updates)
        .eq('id', ticketId)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error updating ticket:', error);
        return { success: false, error: error.message };
      }

      console.log('✅ Support ticket updated successfully');
      return { success: true, ticket: data };
    } catch (error) {
      console.error('❌ Error updating support ticket:', error);
      return { success: false, error: error.message };
    }
  }

  async addTicketMessage(ticketId, message, sender = 'user', attachments = []) {
    try {
      const messageData = {
        ticket_id: ticketId,
        message,
        sender,
        attachments,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('ticket_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) {
        console.error('❌ Database error adding message:', error);
        return { success: false, error: error.message };
      }

      // Update ticket's updated_at timestamp
      await supabase
        .from('support_tickets')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', ticketId);

      console.log('✅ Ticket message added successfully');
      return { success: true, message: data };
    } catch (error) {
      console.error('❌ Error adding ticket message:', error);
      return { success: false, error: error.message };
    }
  }

  async getTicketMessages(ticketId) {
    try {
      const { data, error } = await supabase
        .from('ticket_messages')
        .select('*')
        .eq('ticket_id', ticketId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Database error getting messages:', error);
        return { success: false, error: error.message };
      }

      return { success: true, messages: data };
    } catch (error) {
      console.error('❌ Error getting ticket messages:', error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // FILE ATTACHMENTS
  // =====================================================

  async uploadAttachment(uri, fileName, ticketId) {
    try {
      console.log('📎 Uploading attachment:', fileName);

      // Read file as base64
      const fileInfo = await FileSystem.getInfoAsync(uri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist');
      }

      const base64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Upload to Supabase storage
      const filePath = `support-attachments/${ticketId}/${Date.now()}_${fileName}`;
      const { data, error } = await supabase.storage
        .from('support-files')
        .upload(filePath, base64, {
          contentType: this.getMimeType(fileName),
          cacheControl: '3600',
        });

      if (error) {
        console.error('❌ Storage error uploading file:', error);
        return { success: false, error: error.message };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('support-files')
        .getPublicUrl(filePath);

      const attachment = {
        id: `attachment_${Date.now()}`,
        file_name: fileName,
        file_path: filePath,
        file_url: urlData.publicUrl,
        file_size: fileInfo.size,
        mime_type: this.getMimeType(fileName),
        uploaded_at: new Date().toISOString()
      };

      console.log('✅ Attachment uploaded successfully');
      return { success: true, attachment };
    } catch (error) {
      console.error('❌ Error uploading attachment:', error);
      return { success: false, error: error.message };
    }
  }

  getMimeType(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    const mimeTypes = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain'
    };
    return mimeTypes[extension] || 'application/octet-stream';
  }

  // =====================================================
  // LOCAL STORAGE FALLBACK
  // =====================================================

  async saveTicketLocally(ticket) {
    try {
      const existingTickets = await AsyncStorage.getItem('support_tickets');
      const tickets = existingTickets ? JSON.parse(existingTickets) : [];
      tickets.push(ticket);
      await AsyncStorage.setItem('support_tickets', JSON.stringify(tickets));
      console.log('✅ Ticket saved locally');
    } catch (error) {
      console.error('❌ Error saving ticket locally:', error);
    }
  }

  async getTicketsLocally(userId) {
    try {
      const existingTickets = await AsyncStorage.getItem('support_tickets');
      const allTickets = existingTickets ? JSON.parse(existingTickets) : [];
      const userTickets = allTickets.filter(ticket => ticket.user_id === userId);
      return { success: true, tickets: userTickets, isLocal: true };
    } catch (error) {
      console.error('❌ Error getting local tickets:', error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  getTicketPriorityColor(priority) {
    const colors = {
      low: '#10B981',      // Green
      medium: '#F59E0B',   // Yellow
      high: '#EF4444',     // Red
      urgent: '#DC2626'    // Dark Red
    };
    return colors[priority] || colors.medium;
  }

  getTicketStatusColor(status) {
    const colors = {
      open: '#3B82F6',        // Blue
      in_progress: '#F59E0B', // Yellow
      resolved: '#10B981',    // Green
      closed: '#6B7280'       // Gray
    };
    return colors[status] || colors.open;
  }

  formatTicketId(ticketId) {
    // Format ticket ID for display (e.g., #JP-12345)
    const numericPart = ticketId.replace(/\D/g, '').slice(-5);
    return `#JP-${numericPart}`;
  }

  getEstimatedResponseTime(priority) {
    const times = {
      urgent: '1-2 hours',
      high: '2-4 hours',
      medium: '4-8 hours',
      low: '1-2 business days'
    };
    return times[priority] || times.medium;
  }

  // =====================================================
  // ANALYTICS
  // =====================================================

  async logTicketAnalytics(ticketId, action, metadata = {}) {
    try {
      await supabase
        .from('support_analytics')
        .insert({
          ticket_id: ticketId,
          action,
          metadata,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging ticket analytics:', error);
    }
  }
}

export default new SupportTicketService();
