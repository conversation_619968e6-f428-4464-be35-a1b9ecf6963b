/**
 * JiraniPay Loading State Manager
 * 
 * Centralized management of loading states across the app to ensure
 * seamless branded splash screen experience during critical transitions.
 */

class LoadingStateManager {
  constructor() {
    this.loadingStates = new Map();
    this.listeners = new Set();
    this.isInitialized = false;
    
    // Define critical loading states that should show splash screen
    this.criticalStates = {
      // App initialization
      APP_STARTUP: 'app_startup',
      AUTH_INITIALIZATION: 'auth_initialization',
      
      // Authentication flows
      LOGIN_PROCESSING: 'login_processing',
      LOGOUT_PROCESSING: 'logout_processing',
      SESSION_RESTORATION: 'session_restoration',
      REGISTRATION_PROCESSING: 'registration_processing',
      
      // Dashboard and core data loading
      DASHBOARD_LOADING: 'dashboard_loading',
      WALLET_DATA_LOADING: 'wallet_data_loading',
      USER_PROFILE_LOADING: 'user_profile_loading',
      TRANSACTION_HISTORY_LOADING: 'transaction_history_loading',
      
      // Service initialization
      WALLET_SERVICE_INIT: 'wallet_service_init',
      SAVINGS_SERVICE_INIT: 'savings_service_init',
      BILLS_SERVICE_INIT: 'bills_service_init',
      CURRENCY_SERVICE_INIT: 'currency_service_init',
      
      // Critical navigation transitions
      PROFILE_SETUP_LOADING: 'profile_setup_loading',
      VERIFICATION_LOADING: 'verification_loading',
      PAYMENT_PROCESSING: 'payment_processing',
    };
    
    // Initialize all states as not loading
    Object.values(this.criticalStates).forEach(state => {
      this.loadingStates.set(state, false);
    });
    
    this.isInitialized = true;
    console.log('🔄 LoadingStateManager initialized');
  }

  /**
   * Set a loading state
   * @param {string} stateKey - The loading state key
   * @param {boolean} isLoading - Whether the state is loading
   * @param {object} metadata - Optional metadata about the loading state
   */
  setLoadingState(stateKey, isLoading, metadata = {}) {
    if (!this.isInitialized) {
      console.warn('⚠️ LoadingStateManager not initialized');
      return;
    }

    const previousState = this.loadingStates.get(stateKey);
    this.loadingStates.set(stateKey, isLoading);
    
    const stateChange = {
      stateKey,
      isLoading,
      previousState,
      metadata,
      timestamp: Date.now()
    };
    
    console.log(`🔄 Loading state changed: ${stateKey} = ${isLoading}`, metadata);
    
    // Notify all listeners
    this.listeners.forEach(listener => {
      try {
        listener(stateChange);
      } catch (error) {
        console.error('❌ Error in loading state listener:', error);
      }
    });
  }

  /**
   * Get a specific loading state
   * @param {string} stateKey - The loading state key
   * @returns {boolean} Whether the state is loading
   */
  getLoadingState(stateKey) {
    return this.loadingStates.get(stateKey) || false;
  }

  /**
   * Check if any critical loading states are active
   * @returns {boolean} Whether any critical loading is happening
   */
  isAnyCriticalLoading() {
    return Array.from(this.loadingStates.values()).some(isLoading => isLoading);
  }

  /**
   * Get all currently active loading states
   * @returns {Array} Array of active loading state keys
   */
  getActiveLoadingStates() {
    const activeStates = [];
    this.loadingStates.forEach((isLoading, stateKey) => {
      if (isLoading) {
        activeStates.push(stateKey);
      }
    });
    return activeStates;
  }

  /**
   * Check if specific states are loading
   * @param {Array} stateKeys - Array of state keys to check
   * @returns {boolean} Whether any of the specified states are loading
   */
  isAnyStateLoading(stateKeys) {
    return stateKeys.some(stateKey => this.getLoadingState(stateKey));
  }

  /**
   * Set multiple loading states at once
   * @param {Object} states - Object with stateKey: isLoading pairs
   * @param {Object} metadata - Optional metadata
   */
  setMultipleStates(states, metadata = {}) {
    Object.entries(states).forEach(([stateKey, isLoading]) => {
      this.setLoadingState(stateKey, isLoading, metadata);
    });
  }

  /**
   * Add a listener for loading state changes
   * @param {Function} listener - Callback function for state changes
   * @returns {Function} Unsubscribe function
   */
  addListener(listener) {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Remove all listeners
   */
  clearListeners() {
    this.listeners.clear();
  }

  /**
   * Get loading state summary for debugging
   * @returns {Object} Summary of all loading states
   */
  getLoadingStateSummary() {
    const summary = {
      totalStates: this.loadingStates.size,
      activeStates: this.getActiveLoadingStates(),
      isAnyCriticalLoading: this.isAnyCriticalLoading(),
      allStates: Object.fromEntries(this.loadingStates)
    };
    
    return summary;
  }

  /**
   * Reset all loading states (useful for testing or error recovery)
   */
  resetAllStates() {
    console.log('🔄 Resetting all loading states');
    this.loadingStates.forEach((_, stateKey) => {
      this.setLoadingState(stateKey, false, { reason: 'reset' });
    });
  }

  /**
   * Start authentication flow loading
   */
  startAuthFlow(flowType = 'login') {
    console.log(`🔐 Starting ${flowType} flow`);
    this.setLoadingState(this.criticalStates.LOGIN_PROCESSING, true, { flowType });
  }

  /**
   * Complete authentication flow loading
   */
  completeAuthFlow(flowType = 'login') {
    console.log(`✅ Completed ${flowType} flow`);
    this.setLoadingState(this.criticalStates.LOGIN_PROCESSING, false, { flowType });
  }

  /**
   * Start dashboard loading sequence
   */
  startDashboardLoading() {
    console.log('📊 Starting dashboard loading sequence');
    this.setMultipleStates({
      [this.criticalStates.DASHBOARD_LOADING]: true,
      [this.criticalStates.WALLET_DATA_LOADING]: true,
      [this.criticalStates.USER_PROFILE_LOADING]: true,
    }, { sequence: 'dashboard_init' });
  }

  /**
   * Complete dashboard loading sequence
   */
  completeDashboardLoading() {
    console.log('✅ Dashboard loading sequence completed');
    this.setMultipleStates({
      [this.criticalStates.DASHBOARD_LOADING]: false,
      [this.criticalStates.WALLET_DATA_LOADING]: false,
      [this.criticalStates.USER_PROFILE_LOADING]: false,
    }, { sequence: 'dashboard_complete' });
  }

  /**
   * Start service initialization sequence
   */
  startServiceInitialization() {
    console.log('🔧 Starting service initialization');
    this.setMultipleStates({
      [this.criticalStates.WALLET_SERVICE_INIT]: true,
      [this.criticalStates.SAVINGS_SERVICE_INIT]: true,
      [this.criticalStates.BILLS_SERVICE_INIT]: true,
      [this.criticalStates.CURRENCY_SERVICE_INIT]: true,
    }, { sequence: 'service_init' });
  }

  /**
   * Complete service initialization sequence
   */
  completeServiceInitialization() {
    console.log('✅ Service initialization completed');
    this.setMultipleStates({
      [this.criticalStates.WALLET_SERVICE_INIT]: false,
      [this.criticalStates.SAVINGS_SERVICE_INIT]: false,
      [this.criticalStates.BILLS_SERVICE_INIT]: false,
      [this.criticalStates.CURRENCY_SERVICE_INIT]: false,
    }, { sequence: 'service_complete' });
  }

  /**
   * Check if app is ready (no critical loading states active)
   * @returns {boolean} Whether the app is ready for user interaction
   */
  isAppReady() {
    const criticalStates = [
      this.criticalStates.APP_STARTUP,
      this.criticalStates.AUTH_INITIALIZATION,
      this.criticalStates.LOGIN_PROCESSING,
      this.criticalStates.SESSION_RESTORATION,
      this.criticalStates.DASHBOARD_LOADING,
      this.criticalStates.WALLET_DATA_LOADING,
      this.criticalStates.USER_PROFILE_LOADING,
    ];
    
    return !this.isAnyStateLoading(criticalStates);
  }
}

// Create singleton instance
const loadingStateManager = new LoadingStateManager();

export default loadingStateManager;
