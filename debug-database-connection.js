#!/usr/bin/env node

/**
 * Database Connection Debug Script for JiraniPay
 * 
 * This script helps debug database connection and profile issues
 */

console.log('🔍 JiraniPay Database Connection Debug');
console.log('=====================================\n');

async function debugDatabase() {
  try {
    // Import the Supabase client
    const supabaseClient = require('./services/supabaseClient.js').default;
    console.log('✅ Supabase client loaded successfully');
    
    // Test basic connection
    console.log('\n🔗 Testing Supabase Connection...');
    const { data: connectionTest, error: connectionError } = await supabaseClient
      .from('user_profiles')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.log('❌ Connection to user_profiles failed:', connectionError.message);
      
      // Try alternative table name
      console.log('\n🔄 Trying alternative table name: profiles...');
      const { data: altTest, error: altError } = await supabaseClient
        .from('profiles')
        .select('count')
        .limit(1);
        
      if (altError) {
        console.log('❌ Connection to profiles also failed:', altError.message);
      } else {
        console.log('✅ Connection to profiles table successful!');
      }
    } else {
      console.log('✅ Connection to user_profiles successful!');
    }
    
    // Check what tables exist
    console.log('\n📋 Checking available tables...');
    const { data: tables, error: tablesError } = await supabaseClient
      .rpc('get_table_names')
      .catch(() => null);
    
    if (tables) {
      console.log('Available tables:', tables);
    } else {
      console.log('Could not retrieve table list');
    }
    
    // Try to get current user
    console.log('\n👤 Checking current user session...');
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser();
    
    if (userError) {
      console.log('❌ No current user session:', userError.message);
    } else if (user) {
      console.log('✅ Current user found:', {
        id: user.id,
        email: user.email,
        phone: user.phone,
        created_at: user.created_at
      });
      
      // Try to get user profile
      console.log('\n📄 Checking user profile...');
      
      // Try user_profiles table first
      const { data: profile1, error: profileError1 } = await supabaseClient
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
        
      if (profileError1) {
        console.log('❌ Profile not found in user_profiles:', profileError1.message);
        
        // Try profiles table
        const { data: profile2, error: profileError2 } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (profileError2) {
          console.log('❌ Profile not found in profiles either:', profileError2.message);
          console.log('\n🚨 ISSUE IDENTIFIED: No user profile exists for current user!');
        } else {
          console.log('✅ Profile found in profiles table:', profile2);
        }
      } else {
        console.log('✅ Profile found in user_profiles table:', profile1);
      }
    } else {
      console.log('ℹ️ No user currently logged in');
    }
    
    // Test profile creation
    console.log('\n🔧 Testing profile creation capability...');
    
    // Check if we can insert into user_profiles
    const testProfile = {
      user_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
      full_name: 'Test User',
      phone_number: '+************',
      email: '<EMAIL>'
    };
    
    const { data: insertTest, error: insertError } = await supabaseClient
      .from('user_profiles')
      .insert(testProfile)
      .select()
      .single();
    
    if (insertError) {
      console.log('❌ Cannot insert into user_profiles:', insertError.message);
      
      // Try profiles table structure
      const testProfile2 = {
        id: '00000000-0000-0000-0000-000000000000',
        full_name: 'Test User',
        phone: '+************',
        email: '<EMAIL>'
      };
      
      const { data: insertTest2, error: insertError2 } = await supabaseClient
        .from('profiles')
        .insert(testProfile2)
        .select()
        .single();
        
      if (insertError2) {
        console.log('❌ Cannot insert into profiles either:', insertError2.message);
      } else {
        console.log('✅ Can insert into profiles table');
        // Clean up test data
        await supabaseClient.from('profiles').delete().eq('id', '00000000-0000-0000-0000-000000000000');
      }
    } else {
      console.log('✅ Can insert into user_profiles table');
      // Clean up test data
      await supabaseClient.from('user_profiles').delete().eq('user_id', '00000000-0000-0000-0000-000000000000');
    }
    
  } catch (error) {
    console.error('❌ Debug script error:', error.message);
  }
}

// Recommendations
function showRecommendations() {
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. Check which table structure exists in your Supabase database');
  console.log('2. Ensure user profile is created during registration');
  console.log('3. Verify RLS policies allow profile access');
  console.log('4. Check if table names match between code and database');
  console.log('5. Create missing user profile for current user');
  
  console.log('\n🔧 QUICK FIXES:');
  console.log('- Run database setup: npm run setup-database');
  console.log('- Create user profile manually in Supabase dashboard');
  console.log('- Check RLS policies in Supabase dashboard');
  console.log('- Verify authentication is working');
}

// Run the debug
debugDatabase().then(() => {
  showRecommendations();
}).catch(console.error);
