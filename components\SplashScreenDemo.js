import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import SplashScreen from './SplashScreen';
import EnhancedSplashScreen from './EnhancedSplashScreen';

/**
 * Demo component to showcase both splash screen versions
 * This is for testing and demonstration purposes
 */
const SplashScreenDemo = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const [showSplash, setShowSplash] = useState(false);
  const [showEnhanced, setShowEnhanced] = useState(false);
  const [splashVersion, setSplashVersion] = useState('basic');

  const handleSplashComplete = () => {
    setShowSplash(false);
    setShowEnhanced(false);
  };

  const showBasicSplash = () => {
    setSplashVersion('basic');
    setShowSplash(true);
  };

  const showEnhancedSplash = () => {
    setSplashVersion('enhanced');
    setShowEnhanced(true);
  };

  if (showSplash) {
    return <SplashScreen onAnimationComplete={handleSplashComplete} />;
  }

  if (showEnhanced) {
    return <EnhancedSplashScreen onAnimationComplete={handleSplashComplete} />;
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          JiraniPay Splash Screens
        </Text>
        
        <Text style={[styles.description, { color: theme.colors.textSecondary }]}>
          Choose a splash screen version to preview:
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.primaryButton]}
            onPress={showBasicSplash}
          >
            <Text style={styles.buttonText}>Basic Splash Screen</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.secondaryButton]}
            onPress={showEnhancedSplash}
          >
            <Text style={[styles.buttonText, { color: theme.colors.primary }]}>
              Enhanced Splash Screen
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.themeButton]}
            onPress={toggleTheme}
          >
            <Text style={[styles.buttonText, { color: theme.colors.primary }]}>
              Toggle {isDarkMode ? 'Light' : 'Dark'} Theme
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Features Comparison:
          </Text>
          
          <View style={styles.featureList}>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>
              • Basic: Clean logo animation, connection visualization, progress bar
            </Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>
              • Enhanced: Cultural elements, network pulse, trust indicators, rotating messages
            </Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>
              • Both: Theme support, smooth animations, East African branding
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    padding: 20,
    alignItems: 'center',
    maxWidth: 400,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  buttonContainer: {
    width: '100%',
    marginBottom: 32,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#E67E22',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#E67E22',
  },
  themeButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#95A5A6',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  infoContainer: {
    width: '100%',
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  featureList: {
    paddingLeft: 8,
  },
  featureItem: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
});

export default SplashScreenDemo;
