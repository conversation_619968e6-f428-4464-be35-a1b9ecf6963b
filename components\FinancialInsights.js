import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useCurrencyContext } from '../contexts/CurrencyContext';

/**
 * FinancialInsights Component
 * Displays AI-powered financial insights and recommendations
 */
const FinancialInsights = ({ data, onViewDetails }) => {
  const { convertAndFormat } = useCurrencyContext();

  if (!data) return null;

  // This component now uses convertAndFormat from currency context
  // Removed the old formatCurrency function

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up':
        return 'trending-up';
      case 'down':
        return 'trending-down';
      default:
        return 'remove';
    }
  };

  const getTrendColor = (trend) => {
    switch (trend) {
      case 'up':
        return Colors.accent.coral;
      case 'down':
        return Colors.secondary.savanna;
      default:
        return Colors.neutral.warmGray;
    }
  };

  const getSpendingInsight = () => {
    const { monthlyTrend, totalSpent } = data;
    
    if (monthlyTrend.trend === 'up') {
      return {
        title: 'Spending Alert',
        message: `You've spent ${Math.abs(monthlyTrend.percentageChange).toFixed(0)}% more this month`,
        type: 'warning',
        icon: 'warning-outline',
        color: Colors.accent.coral,
      };
    } else if (monthlyTrend.trend === 'down') {
      return {
        title: 'Great Progress!',
        message: `You've reduced spending by ${Math.abs(monthlyTrend.percentageChange).toFixed(0)}% this month`,
        type: 'success',
        icon: 'checkmark-circle-outline',
        color: Colors.secondary.savanna,
      };
    } else {
      return {
        title: 'Steady Spending',
        message: 'Your spending pattern is consistent this month',
        type: 'info',
        icon: 'information-circle-outline',
        color: Colors.primary.main,
      };
    }
  };

  const getTopCategoryRecommendation = () => {
    const { topSpendingCategory } = data;
    
    if (!topSpendingCategory) return null;

    const recommendations = {
      utilities: 'Consider energy-saving tips to reduce utility bills',
      mobile: 'Look for better data and airtime packages',
      transport: 'Try carpooling or public transport to save money',
      food: 'Plan meals ahead to reduce food expenses',
      entertainment: 'Set a monthly entertainment budget',
    };

    return {
      category: topSpendingCategory.name,
      amount: topSpendingCategory.amount,
      recommendation: recommendations[topSpendingCategory.name] || 'Monitor this category for potential savings',
    };
  };

  const spendingInsight = getSpendingInsight();
  const categoryRecommendation = getTopCategoryRecommendation();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Ionicons name="analytics-outline" size={20} color={Colors.primary.main} />
          <Text style={styles.sectionTitle}>Financial Insights</Text>
        </View>
        <TouchableOpacity onPress={onViewDetails} activeOpacity={0.7}>
          <Text style={styles.viewDetailsText}>View Details</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.insightsContainer}
        style={styles.insightsScroll}
      >
        {/* Monthly Trend Card */}
        <View style={styles.insightCard}>
          <View style={styles.cardHeader}>
            <View style={[styles.trendIconContainer, { backgroundColor: getTrendColor(data.monthlyTrend.trend) + '20' }]}>
              <Ionicons
                name={getTrendIcon(data.monthlyTrend.trend)}
                size={20}
                color={getTrendColor(data.monthlyTrend.trend)}
              />
            </View>
            <Text style={styles.cardTitle}>Monthly Trend</Text>
          </View>
          <Text style={styles.cardAmount}>
            {convertAndFormat(data.monthlyTrend.currentMonth)}
          </Text>
          <Text style={[styles.cardSubtitle, { color: getTrendColor(data.monthlyTrend.trend) }]}>
            {data.monthlyTrend.percentageChange > 0 ? '+' : ''}
            {data.monthlyTrend.percentageChange.toFixed(1)}% from last month
          </Text>
        </View>

        {/* Spending Insight Card */}
        <View style={[styles.insightCard, styles.spendingCard]}>
          <View style={styles.cardHeader}>
            <View style={[styles.iconContainer, { backgroundColor: spendingInsight.color + '20' }]}>
              <Ionicons
                name={spendingInsight.icon}
                size={20}
                color={spendingInsight.color}
              />
            </View>
            <Text style={styles.cardTitle}>{spendingInsight.title}</Text>
          </View>
          <Text style={styles.insightMessage}>{spendingInsight.message}</Text>
          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Total Spent</Text>
              <Text style={styles.statValue}>{convertAndFormat(data.totalSpent)}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Transactions</Text>
              <Text style={styles.statValue}>{data.transactionCount}</Text>
            </View>
          </View>
        </View>

        {/* Top Category Card */}
        {categoryRecommendation && (
          <View style={styles.insightCard}>
            <View style={styles.cardHeader}>
              <View style={[styles.iconContainer, { backgroundColor: Colors.accent.coral + '20' }]}>
                <Ionicons
                  name="pie-chart-outline"
                  size={20}
                  color={Colors.accent.coral}
                />
              </View>
              <Text style={styles.cardTitle}>Top Spending</Text>
            </View>
            <Text style={styles.categoryName}>
              {categoryRecommendation.category.charAt(0).toUpperCase() + categoryRecommendation.category.slice(1)}
            </Text>
            <Text style={styles.cardAmount}>
              {convertAndFormat(categoryRecommendation.amount)}
            </Text>
            <Text style={styles.recommendationText}>
              💡 {categoryRecommendation.recommendation}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginLeft: 8,
  },
  viewDetailsText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  insightsScroll: {
    paddingLeft: 20,
  },
  insightsContainer: {
    paddingRight: 20,
    gap: 16,
  },
  insightCard: {
    width: 280,
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  spendingCard: {
    width: 320,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  trendIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  cardAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  insightMessage: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flex: 1,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  categoryName: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    lineHeight: 16,
    marginTop: 8,
    fontStyle: 'italic',
  },
});

export default FinancialInsights;
