-- Migration: 001_contact_integration.sql
-- Purpose: Add favorite contacts functionality for enhanced money transfer system
-- Task: 1.1.1 Contact Integration & Validation
--
-- IMPORTANT: Execute this in Supabase SQL Editor or via Supabase CLI
-- This migration creates the favorite_contacts table with proper UUID handling

-- Create favorite_contacts table
CREATE TABLE IF NOT EXISTS favorite_contacts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    contact_phone VARCHAR(20) NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    frequency_count INTEGER DEFAULT 1 CHECK (frequency_count > 0),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_user_contact UNIQUE(user_id, contact_phone)
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_favorite_contacts_user_id
    ON favorite_contacts(user_id);

CREATE INDEX IF NOT EXISTS idx_favorite_contacts_frequency
    ON favorite_contacts(user_id, frequency_count DESC);

CREATE INDEX IF NOT EXISTS idx_favorite_contacts_last_used
    ON favorite_contacts(user_id, last_used DESC);

-- Create updated_at trigger function (if it doesn't exist)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_favorite_contacts_updated_at
    BEFORE UPDATE ON favorite_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE favorite_contacts ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only access their own favorite contacts
CREATE POLICY "Users can manage their own favorite contacts"
    ON favorite_contacts
    FOR ALL
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Grant permissions to authenticated users
-- Note: No sequence permissions needed for UUID with gen_random_uuid()
GRANT ALL ON TABLE favorite_contacts TO authenticated;
GRANT ALL ON TABLE favorite_contacts TO service_role;

-- Add helpful comments for documentation
COMMENT ON TABLE favorite_contacts IS 'Stores user favorite contacts for quick money transfers with frequency tracking';
COMMENT ON COLUMN favorite_contacts.id IS 'Primary key using UUID';
COMMENT ON COLUMN favorite_contacts.user_id IS 'Reference to auth.users - the owner of this favorite contact';
COMMENT ON COLUMN favorite_contacts.contact_phone IS 'Phone number in international format (+256...)';
COMMENT ON COLUMN favorite_contacts.contact_name IS 'Display name for the contact';
COMMENT ON COLUMN favorite_contacts.frequency_count IS 'Number of times this contact has been used for transfers';
COMMENT ON COLUMN favorite_contacts.last_used IS 'Timestamp of last transfer to this contact';
COMMENT ON COLUMN favorite_contacts.created_at IS 'When this favorite contact was first added';
COMMENT ON COLUMN favorite_contacts.updated_at IS 'Last time this record was modified';

-- Verify the table was created successfully
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'favorite_contacts') THEN
        RAISE NOTICE 'SUCCESS: favorite_contacts table created successfully';
    ELSE
        RAISE EXCEPTION 'FAILED: favorite_contacts table was not created';
    END IF;
END $$;
