/**
 * Interactive Chart Components
 * Reusable chart components for financial analytics
 */

import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../../constants/Colors';

const { width } = Dimensions.get('window');
const chartWidth = width - 32;

/**
 * Spending Trend Line Chart (Simple Implementation)
 */
export const SpendingTrendChart = ({ data, theme, onDataPointClick }) => {
  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyChart, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="trending-up" size={48} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No spending data available
        </Text>
      </View>
    );
  }

  const maxValue = Math.max(
    ...data.map(item => Math.max(item.income || 0, item.expenses || 0))
  );

  return (
    <View style={styles.chartContainer}>
      {/* Chart Area */}
      <View style={styles.simpleChart}>
        {data.map((item, index) => {
          const incomeHeight = ((item.income || 0) / maxValue) * 150;
          const expenseHeight = ((item.expenses || 0) / maxValue) * 150;

          return (
            <TouchableOpacity
              key={index}
              style={styles.chartColumn}
              onPress={() => onDataPointClick && onDataPointClick(item)}
            >
              <View style={styles.chartBars}>
                <View
                  style={[
                    styles.chartBar,
                    {
                      height: incomeHeight,
                      backgroundColor: '#27AE60',
                      marginRight: 2
                    }
                  ]}
                />
                <View
                  style={[
                    styles.chartBar,
                    {
                      height: expenseHeight,
                      backgroundColor: '#E74C3C'
                    }
                  ]}
                />
              </View>
              <Text style={[styles.chartLabel, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                {item.month}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#27AE60' }]} />
          <Text style={[styles.legendText, { color: theme.colors.text }]}>Income</Text>
        </View>
        <View style={styles.legendItem}>
          <View style={[styles.legendColor, { backgroundColor: '#E74C3C' }]} />
          <Text style={[styles.legendText, { color: theme.colors.text }]}>Expenses</Text>
        </View>
      </View>
    </View>
  );
};

/**
 * Category Spending Chart (Simple Implementation)
 */
export const CategorySpendingChart = ({ data, theme, onSliceClick }) => {
  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyChart, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="pie-chart" size={48} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No category data available
        </Text>
      </View>
    );
  }

  const colors = [
    Colors.primary.main,
    Colors.secondary.savanna,
    Colors.accent.coral,
    Colors.secondary.lake,
    Colors.accent.gold,
    Colors.secondary.heritage,
    Colors.neutral.warmGray
  ];

  const safeData = Array.isArray(data) ? data : [];
  const total = safeData.reduce((sum, item) => sum + (item?.amount || 0), 0);
  const maxAmount = safeData.length > 0 ? Math.max(...safeData.map(item => item?.amount || 0)) : 0;

  return (
    <View style={styles.chartContainer}>
      {/* Simple Bar Chart for Categories */}
      <ScrollView style={styles.categoryChart} showsVerticalScrollIndicator={false}>
        {safeData.slice(0, 7).map((item, index) => {
          const itemAmount = item?.amount || 0;
          const percentage = total > 0 ? ((itemAmount / total) * 100).toFixed(1) : '0.0';
          const barWidth = maxAmount > 0 ? (itemAmount / maxAmount) * 100 : 0;
          const color = colors[index % colors.length];

          return (
            <TouchableOpacity
              key={index}
              style={styles.categoryItem}
              onPress={() => onSliceClick && onSliceClick(item)}
            >
              <View style={styles.categoryHeader}>
                <View style={styles.categoryInfo}>
                  <View style={[styles.categoryDot, { backgroundColor: color }]} />
                  <Text style={[styles.categoryName, { color: theme.colors.text }]} numberOfLines={1}>
                    {item.category}
                  </Text>
                </View>
                <Text style={[styles.categoryPercentage, { color: theme.colors.textSecondary }]}>
                  {percentage}%
                </Text>
              </View>
              <View style={[styles.categoryBarContainer, { backgroundColor: theme.colors.border }]}>
                <View
                  style={[
                    styles.categoryBar,
                    {
                      backgroundColor: color,
                      width: `${barWidth}%`
                    }
                  ]}
                />
              </View>
              <Text style={[styles.categoryAmount, { color: theme.colors.textSecondary }]}>
                {item.amount.toLocaleString()} UGX
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

/**
 * Monthly Comparison Bar Chart (Simple Implementation)
 */
export const MonthlyComparisonChart = ({ data, theme, onBarClick }) => {
  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyChart, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="bar-chart" size={48} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No monthly data available
        </Text>
      </View>
    );
  }

  const maxValue = Math.max(...data.map(item => Math.abs(item.net || 0)));

  return (
    <View style={styles.chartContainer}>
      <View style={styles.monthlyChart}>
        {data.map((item, index) => {
          const netValue = item.net || 0;
          const isPositive = netValue >= 0;
          const barHeight = Math.abs(netValue) / maxValue * 120;
          const color = isPositive ? '#27AE60' : '#E74C3C';

          return (
            <TouchableOpacity
              key={index}
              style={styles.monthlyColumn}
              onPress={() => onBarClick && onBarClick(item)}
            >
              <Text style={[styles.monthlyValue, { color: theme.colors.text }]}>
                {netValue.toLocaleString()}
              </Text>
              <View style={styles.monthlyBarContainer}>
                <View
                  style={[
                    styles.monthlyBar,
                    {
                      height: Math.max(barHeight, 4),
                      backgroundColor: color,
                      marginTop: isPositive ? 'auto' : 0,
                      marginBottom: isPositive ? 0 : 'auto'
                    }
                  ]}
                />
              </View>
              <Text style={[styles.monthlyLabel, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                {item.month}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Zero line indicator */}
      <View style={styles.zeroLine}>
        <View style={[styles.zeroLineBar, { backgroundColor: theme.colors.border }]} />
        <Text style={[styles.zeroLineText, { color: theme.colors.textSecondary }]}>0</Text>
      </View>
    </View>
  );
};

/**
 * Savings Progress Chart
 */
export const SavingsProgressChart = ({ data, theme }) => {
  if (!data || data.goals.length === 0) {
    return (
      <View style={[styles.emptyChart, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="target" size={48} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No savings goals set
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.chartContainer}>
      <View style={styles.progressList}>
        {data.goals.slice(0, 4).map((goal, index) => (
          <View key={index} style={styles.progressItem}>
            <View style={styles.progressHeader}>
              <Text style={[styles.progressTitle, { color: theme.colors.text }]} numberOfLines={1}>
                {goal.accountName}
              </Text>
              <Text style={[styles.progressPercentage, { color: Colors.secondary.savanna }]}>
                {goal.progress.toFixed(1)}%
              </Text>
            </View>
            <View style={[styles.progressBarContainer, { backgroundColor: theme.colors.border }]}>
              <View
                style={[
                  styles.progressBar,
                  {
                    backgroundColor: Colors.secondary.savanna,
                    width: `${Math.min(goal.progress, 100)}%`
                  }
                ]}
              />
            </View>
            <View style={styles.progressAmounts}>
              <Text style={[styles.progressAmount, { color: theme.colors.textSecondary }]}>
                {goal.currentAmount.toLocaleString()} UGX
              </Text>
              <Text style={[styles.progressTarget, { color: theme.colors.textSecondary }]}>
                / {goal.targetAmount.toLocaleString()} UGX
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

/**
 * Investment Performance Chart (Simple Implementation)
 */
export const InvestmentPerformanceChart = ({ data, theme }) => {
  if (!data || data.length === 0) {
    return (
      <View style={[styles.emptyChart, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="trending-up" size={48} color={theme.colors.textSecondary} />
        <Text style={[styles.emptyText, { color: theme.colors.textSecondary }]}>
          No investment data available
        </Text>
      </View>
    );
  }

  const maxReturn = Math.max(...data.map(item => Math.abs(item.return || 0)));
  const minReturn = Math.min(...data.map(item => item.return || 0));

  return (
    <View style={styles.chartContainer}>
      <View style={styles.investmentChart}>
        {data.map((item, index) => {
          const returnValue = item.return || 0;
          const isPositive = returnValue >= 0;
          const normalizedHeight = (Math.abs(returnValue) / maxReturn) * 100;
          const color = isPositive ? '#27AE60' : '#E74C3C';

          return (
            <View key={index} style={styles.investmentColumn}>
              <Text style={[styles.investmentValue, { color: theme.colors.text }]}>
                {returnValue.toFixed(1)}%
              </Text>
              <View style={styles.investmentBarContainer}>
                <View
                  style={[
                    styles.investmentBar,
                    {
                      height: Math.max(normalizedHeight, 4),
                      backgroundColor: color,
                      marginTop: isPositive ? 'auto' : 0,
                      marginBottom: isPositive ? 0 : 'auto'
                    }
                  ]}
                />
              </View>
              <Text style={[styles.investmentLabel, { color: theme.colors.textSecondary }]} numberOfLines={1}>
                {item.month}
              </Text>
            </View>
          );
        })}
      </View>

      {/* Performance Summary */}
      <View style={styles.performanceSummary}>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Best</Text>
          <Text style={[styles.summaryValue, { color: '#27AE60' }]}>
            {Math.max(...data.map(item => item.return || 0)).toFixed(1)}%
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Worst</Text>
          <Text style={[styles.summaryValue, { color: '#E74C3C' }]}>
            {minReturn.toFixed(1)}%
          </Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={[styles.summaryLabel, { color: theme.colors.textSecondary }]}>Average</Text>
          <Text style={[styles.summaryValue, { color: theme.colors.text }]}>
            {(() => {
              const safeData = Array.isArray(data) ? data : [];
              const total = safeData.reduce((sum, item) => sum + (item?.return || 0), 0);
              return safeData.length > 0 ? (total / safeData.length).toFixed(1) : '0.0';
            })()}%
          </Text>
        </View>
      </View>
    </View>
  );
};

/**
 * Chart Container with Header
 */
export const ChartCard = ({ title, subtitle, children, theme, onExport, loading }) => {
  return (
    <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.cardHeader}>
        <View style={styles.cardTitleContainer}>
          <Text style={[styles.cardTitle, { color: theme.colors.text }]}>{title}</Text>
          {subtitle && (
            <Text style={[styles.cardSubtitle, { color: theme.colors.textSecondary }]}>
              {subtitle}
            </Text>
          )}
        </View>
        {onExport && (
          <TouchableOpacity
            style={[styles.exportButton, { backgroundColor: theme.colors.primary }]}
            onPress={onExport}
            disabled={loading}
          >
            <Ionicons 
              name={loading ? "hourglass" : "download"} 
              size={16} 
              color={Colors.neutral.white} 
            />
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.cardContent}>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  emptyChart: {
    height: 220,
    width: chartWidth,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.neutral.warmGrayLight,
    borderStyle: 'dashed',
  },
  emptyText: {
    marginTop: 12,
    fontSize: 14,
    textAlign: 'center',
  },
  card: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardTitleContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
  },
  cardContent: {
    alignItems: 'center',
  },
  exportButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
  },
  // Legend styles
  legend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
    gap: 20,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  legendValue: {
    fontSize: 12,
    marginLeft: 8,
  },
  // Pie chart specific styles
  centerLabel: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerLabelText: {
    fontSize: 12,
    fontWeight: '500',
  },
  centerLabelValue: {
    fontSize: 14,
    fontWeight: '700',
  },
  pieChartLegend: {
    marginTop: 16,
    paddingHorizontal: 16,
  },
  // Bar chart styles
  barTopLabel: {
    fontSize: 10,
    fontWeight: '600',
  },
  // Progress chart styles
  progressList: {
    width: '100%',
    paddingHorizontal: 16,
  },
  progressItem: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '700',
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressAmount: {
    fontSize: 12,
  },
  progressTarget: {
    fontSize: 12,
  },
  // Simple chart styles
  simpleChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: 180,
    paddingHorizontal: 16,
  },
  chartColumn: {
    alignItems: 'center',
    flex: 1,
    maxWidth: 60,
  },
  chartBars: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 150,
    marginBottom: 8,
  },
  chartBar: {
    width: 8,
    borderRadius: 4,
    minHeight: 4,
  },
  chartLabel: {
    fontSize: 10,
    textAlign: 'center',
    transform: [{ rotate: '-45deg' }],
  },
  // Category chart styles
  categoryChart: {
    maxHeight: 200,
    paddingHorizontal: 16,
  },
  categoryItem: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  categoryPercentage: {
    fontSize: 12,
    fontWeight: '600',
  },
  categoryBarContainer: {
    height: 6,
    borderRadius: 3,
    marginBottom: 4,
  },
  categoryBar: {
    height: '100%',
    borderRadius: 3,
    minWidth: 4,
  },
  categoryAmount: {
    fontSize: 12,
    textAlign: 'right',
  },
  // Monthly chart styles
  monthlyChart: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: 180,
    paddingHorizontal: 16,
  },
  monthlyColumn: {
    alignItems: 'center',
    flex: 1,
    maxWidth: 60,
  },
  monthlyValue: {
    fontSize: 10,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  monthlyBarContainer: {
    height: 120,
    width: 16,
    justifyContent: 'center',
    marginBottom: 8,
  },
  monthlyBar: {
    width: '100%',
    borderRadius: 8,
    minHeight: 4,
  },
  monthlyLabel: {
    fontSize: 10,
    textAlign: 'center',
    transform: [{ rotate: '-45deg' }],
  },
  zeroLine: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 16,
  },
  zeroLineBar: {
    flex: 1,
    height: 1,
    marginRight: 8,
  },
  zeroLineText: {
    fontSize: 10,
  },
  // Investment chart styles
  investmentChart: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    height: 140,
    paddingHorizontal: 16,
  },
  investmentColumn: {
    alignItems: 'center',
    flex: 1,
    maxWidth: 60,
  },
  investmentValue: {
    fontSize: 10,
    fontWeight: '600',
    marginBottom: 4,
    textAlign: 'center',
  },
  investmentBarContainer: {
    height: 80,
    width: 12,
    justifyContent: 'center',
    marginBottom: 8,
  },
  investmentBar: {
    width: '100%',
    borderRadius: 6,
    minHeight: 4,
  },
  investmentLabel: {
    fontSize: 10,
    textAlign: 'center',
    transform: [{ rotate: '-45deg' }],
  },
  performanceSummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingHorizontal: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '700',
  },
});
