/**
 * Privacy Policy Screen for JiraniPay
 * 
 * Comprehensive, legally-compliant privacy policy for East African markets
 * Complies with Uganda Data Protection Act 2019, Kenya DPA 2019, Tanzania DPA 2022, and GDPR
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  Linking
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const PrivacyPolicyScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [expandedSections, setExpandedSections] = useState(new Set());
  const [lastUpdated] = useState('December 27, 2024');
  const [effectiveDate] = useState('January 1, 2025');
  const [version] = useState('2.0');

  // Helper function to render text with bold formatting
  const renderFormattedText = (text) => {
    const parts = text.split(/(\*\*.*?\*\*)/g);
    return (
      <Text style={styles.contentText}>
        {parts.map((part, index) => {
          if (part.startsWith('**') && part.endsWith('**')) {
            // Remove the ** and make it bold
            const boldText = part.slice(2, -2);
            return (
              <Text key={index} style={{ fontWeight: 'bold' }}>
                {boldText}
              </Text>
            );
          }
          return part;
        })}
      </Text>
    );
  };

  const toggleSection = (sectionId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const handleContactPress = () => {
    Alert.alert(t('contactDataProtectionOfficer'), t('chooseHowYouWouldLikeToContactUs'),
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Email', 
          onPress: () => Linking.openURL('mailto:<EMAIL>')
        },
        {
          text: 'Phone',
          onPress: () => Linking.openURL('tel:+************')
        }
      ]
    );
  };

  const privacyPolicySections = [
    {
      id: '1',
      title: 'Information We Collect',
      icon: 'information-circle',
      color: Colors.primary.main,
      content: [
        '**Personal Information**: Name, phone number, email address, date of birth, national ID number, and address as required for KYC compliance under Bank of Uganda regulations.',
        '**Financial Information**: Transaction history, account balances, payment methods, and financial behavior patterns for service provision and fraud prevention.',
        '**Device Information**: Device type, operating system, unique device identifiers, IP address, and location data for security and fraud prevention.',
        '**Usage Data**: App usage patterns, feature interactions, and performance metrics to improve our services (only with your consent).',
        '**Biometric Data**: Fingerprint and facial recognition data stored locally on your device for authentication (never transmitted to our servers).',
        '**Communication Data**: Customer support interactions, feedback, and correspondence for service improvement.'
      ]
    },
    {
      id: '2',
      title: 'How We Use Your Information',
      icon: 'settings',
      color: Colors.secondary.savanna,
      content: [
        '**Service Provision**: Processing transactions, maintaining accounts, and providing core financial services.',
        '**Regulatory Compliance**: Meeting KYC/AML requirements under Uganda Financial Intelligence Authority (FIA) and Bank of Uganda regulations.',
        '**Security & Fraud Prevention**: Monitoring transactions for suspicious activity, preventing unauthorized access, and protecting your account.',
        '**Customer Support**: Responding to inquiries, resolving issues, and providing technical assistance.',
        '**Service Improvement**: Analyzing usage patterns to enhance features and user experience (with your consent).',
        '**Marketing Communications**: Sending promotional content and financial tips (only with your explicit consent and opt-out available).',
        '**Legal Obligations**: Complying with court orders, regulatory requests, and legal requirements in Uganda and East Africa.'
      ]
    },
    {
      id: '3',
      title: 'Legal Basis for Processing',
      icon: 'document-text',
      color: Colors.accent.gold,
      content: [
        '**Contract Performance**: Processing necessary for providing financial services you\'ve requested.',
        '**Legal Obligation**: Compliance with Uganda Data Protection Act 2019, Bank of Uganda regulations, and AML/CFT requirements.',
        '**Legitimate Interest**: Fraud prevention, security monitoring, and service improvement where not overridden by your privacy rights.',
        '**Consent**: Marketing communications, analytics, and optional features (withdrawable at any time).',
        '**Vital Interest**: Emergency situations requiring immediate action to protect life or prevent serious harm.'
      ]
    },
    {
      id: '4',
      title: 'Data Sharing & Disclosure',
      icon: 'share',
      color: Colors.status.info,
      content: [
        '**Regulatory Authorities**: Bank of Uganda, Financial Intelligence Authority, Uganda Revenue Authority as required by law.',
        '**Service Providers**: Trusted third-party processors for payment processing, SMS delivery, and cloud services (under strict data processing agreements).',
        '**Financial Partners**: Banks and mobile money operators for transaction processing (limited to transaction-specific data).',
        '**Legal Requirements**: Law enforcement, courts, and regulatory bodies when legally required.',
        '**Business Transfers**: In case of merger, acquisition, or sale (with prior notice and continued protection).',
        '**Emergency Situations**: To prevent fraud, protect safety, or respond to emergencies.',
        '**With Your Consent**: Any other sharing only with your explicit permission.'
      ]
    },
    {
      id: '5',
      title: 'Data Security & Protection',
      icon: 'shield-checkmark',
      color: Colors.status.success,
      content: [
        '**Encryption**: AES-256 encryption for data at rest and TLS 1.3 for data in transit.',
        '**Access Controls**: Multi-factor authentication, role-based access, and principle of least privilege.',
        '**Infrastructure Security**: Secure cloud hosting with ISO 27001 certified providers.',
        '**Regular Audits**: Quarterly security assessments and annual penetration testing.',
        '**Incident Response**: 24/7 monitoring with immediate response to security incidents.',
        '**Data Minimization**: Collecting only necessary data and regular purging of unnecessary information.',
        '**Staff Training**: Regular security awareness training for all personnel handling personal data.'
      ]
    },
    {
      id: '6',
      title: 'Your Rights Under East African Law',
      icon: 'person-circle',
      color: Colors.primary.main,
      content: [
        '**Right to Access**: Request copies of your personal data we hold (free once per year).',
        '**Right to Rectification**: Correct inaccurate or incomplete personal information.',
        '**Right to Erasure**: Request deletion of your data (subject to legal retention requirements).',
        '**Right to Restrict Processing**: Limit how we use your data in certain circumstances.',
        '**Right to Data Portability**: Receive your data in a structured, machine-readable format.',
        '**Right to Object**: Opt-out of marketing communications and non-essential processing.',
        '**Right to Withdraw Consent**: Revoke consent for optional data processing at any time.',
        '**Right to Complain**: Lodge complaints with Uganda\'s National Information Technology Authority (NITA-U).'
      ]
    },
    {
      id: '7',
      title: 'Data Retention',
      icon: 'time',
      color: Colors.secondary.savanna,
      content: [
        '**Transaction Records**: 7 years as required by Bank of Uganda regulations.',
        '**KYC Documents**: 5 years after account closure as per AML requirements.',
        '**Communication Records**: 3 years for customer support and compliance purposes.',
        '**Marketing Data**: Until consent is withdrawn or account is closed.',
        '**Security Logs**: 2 years for fraud prevention and security monitoring.',
        '**Analytics Data**: Anonymized after 1 year, deleted after 3 years.',
        '**Account Data**: Deleted within 30 days of account closure (except legally required records).'
      ]
    },
    {
      id: '8',
      title: 'International Transfers',
      icon: 'globe',
      color: Colors.accent.gold,
      content: [
        '**Primary Storage**: All data primarily stored within East African Community (EAC) region.',
        '**Cloud Services**: Some data processed by international cloud providers with adequate protection (AWS, Google Cloud).',
        '**Safeguards**: Standard Contractual Clauses and adequacy decisions ensure GDPR-level protection.',
        '**Payment Processing**: International payment networks (Visa, Mastercard) for card transactions with appropriate safeguards.',
        '**No Unnecessary Transfers**: Data only transferred internationally when essential for service provision.'
      ]
    },
    {
      id: '9',
      title: 'Children\'s Privacy',
      icon: 'people',
      color: Colors.status.warning,
      content: [
        '**Age Requirement**: JiraniPay services are only available to users 18 years and older.',
        '**No Child Data**: We do not knowingly collect personal information from children under 18.',
        '**Parental Notification**: If we discover child data has been collected, we will delete it immediately and notify parents.',
        '**Verification**: Age verification is part of our KYC process to ensure compliance.'
      ]
    },
    {
      id: '10',
      title: 'Updates to This Policy',
      icon: 'refresh',
      color: Colors.primary.main,
      content: [
        '**Notification**: We will notify you of material changes via app notification and email.',
        '**Effective Date**: Changes take effect 30 days after notification unless immediate compliance is required.',
        '**Version Control**: Each version is numbered and dated for transparency.',
        '**Continued Use**: Continued use of JiraniPay after changes constitutes acceptance of the updated policy.'
      ]
    }
  ];

  const renderSection = (section) => {
    const isExpanded = expandedSections.has(section.id);
    
    return (
      <View key={section.id} style={styles.sectionContainer}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => toggleSection(section.id)}
          activeOpacity={0.7}
        >
          <View style={styles.sectionHeaderLeft}>
            <View style={[styles.sectionIcon, { backgroundColor: section.color + '20' }]}>
              <Ionicons name={section.icon} size={20} color={section.color} />
            </View>
            <Text style={styles.sectionTitle}>{section.title}</Text>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={Colors.neutral.warmGray} 
          />
        </TouchableOpacity>
        
        {isExpanded && (
          <View style={styles.sectionContent}>
            {section.content.map((item, index) => (
              <View key={index} style={{ marginBottom: 12 }}>
                {renderFormattedText(item)}
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('privacyPolicy')}</Text>
          <TouchableOpacity onPress={handleContactPress} style={styles.contactButton}>
            <Ionicons name="mail" size={20} color={Colors.neutral.white} />
          </TouchableOpacity>
        </View>
        <Text style={styles.headerSubtitle}>
          {t('howWeProtectAndUseYourData')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Policy Overview */}
        <View style={styles.overviewSection}>
          <View style={styles.overviewCard}>
            <View style={styles.overviewHeader}>
              <Ionicons name="shield-checkmark" size={32} color={Colors.status.success} />
              <View style={styles.overviewText}>
                <Text style={styles.overviewTitle}>{t('jiranipayPrivacyPolicy')}</Text>
                <Text style={styles.overviewVersion}>Version {version}</Text>
              </View>
            </View>
            <View style={styles.overviewDates}>
              <Text style={styles.overviewDate}>Last Updated: {lastUpdated}</Text>
              <Text style={styles.overviewDate}>Effective Date: {effectiveDate}</Text>
            </View>
            <Text style={styles.overviewDescription}>
              {t('thisPrivacyPolicyExplainsHowJiranipayCollectsUsesA')}
            </Text>
          </View>
        </View>

        {/* Policy Sections */}
        <View style={styles.sectionsContainer}>
          {privacyPolicySections.map(renderSection)}
        </View>

        {/* Contact Information */}
        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>{t('contactOurDataProtectionOfficer')}</Text>
          <TouchableOpacity style={styles.contactCard} onPress={handleContactPress}>
            <Ionicons name="mail" size={24} color={Colors.primary.main} />
            <View style={styles.contactInfo}>
              <Text style={styles.contactEmail}>{t('privacyjiranipaycom')}</Text>
              <Text style={styles.contactPhone}>{t('************')}</Text>
              <Text style={styles.contactAddress}>
                {t('jiranipayLtdPlot123KampalaRoadKampalaUganda')}
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Regulatory Information */}
        <View style={styles.regulatorySection}>
          <Text style={styles.regulatoryTitle}>{t('regulatoryCompliance')}</Text>
          <View style={styles.regulatoryGrid}>
            <View style={styles.regulatoryItem}>
              <Text style={styles.regulatoryLabel}>{t('uganda')}</Text>
              <Text style={styles.regulatoryText}>{t('dataProtectionAct2019')}</Text>
            </View>
            <View style={styles.regulatoryItem}>
              <Text style={styles.regulatoryLabel}>{t('kenya')}</Text>
              <Text style={styles.regulatoryText}>{t('dataProtectionAct2019')}</Text>
            </View>
            <View style={styles.regulatoryItem}>
              <Text style={styles.regulatoryLabel}>{t('tanzania')}</Text>
              <Text style={styles.regulatoryText}>{t('dataProtectionAct2022')}</Text>
            </View>
            <View style={styles.regulatoryItem}>
              <Text style={styles.regulatoryLabel}>{t('international')}</Text>
              <Text style={styles.regulatoryText}>{t('gdprCompliant')}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    width: 40,
    height: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    flex: 1,
    textAlign: 'center',
  },
  contactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  overviewSection: {
    marginTop: 20,
    marginBottom: 30,
  },
  overviewCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  overviewText: {
    marginLeft: 15,
    flex: 1,
  },
  overviewTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  overviewVersion: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  overviewDates: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  overviewDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  overviewDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  sectionsContainer: {
    marginBottom: 30,
  },
  sectionContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  sectionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  sectionContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  contentText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: 12,
  },
  contactSection: {
    marginBottom: 30,
  },
  contactTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 15,
  },
  contactCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  contactInfo: {
    marginLeft: 15,
    flex: 1,
  },
  contactEmail: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactAddress: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    lineHeight: 16,
  },
  regulatorySection: {
    marginBottom: 40,
  },
  regulatoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 15,
  },
  regulatoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  regulatoryItem: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 8,
    padding: 12,
    width: '48%',
    marginBottom: 10,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  regulatoryLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  regulatoryText: {
    fontSize: 11,
    color: Colors.neutral.charcoal,
    lineHeight: 14,
  },
});

export default PrivacyPolicyScreen;
