/**
 * Transaction Routes
 * Handles transaction history, status, and transaction-related operations
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  AuthorizationError
} = require('../middleware/errorHandler');
const { validationMiddleware } = require('../middleware/validation');
const transactionService = require('../services/transactionService');
const walletService = require('../services/walletService');
const currencyService = require('../services/currencyService');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/transactions
 * @desc    Get user transaction history
 * @access  Private
 */
router.get('/', validationMiddleware.transactionHistory, asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    type,
    status,
    startDate,
    endDate
  } = req.query;

  try {
    const history = await walletService.getTransactionHistory(req.user.user_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      status,
      startDate,
      endDate
    });

    // Format transactions for response
    const formattedTransactions = history.transactions.map(tx => ({
      id: tx.id,
      reference: tx.transaction_reference,
      type: tx.type,
      direction: tx.direction,
      amount: tx.amount,
      convertedAmount: tx.converted_amount,
      currency: tx.currency,
      targetCurrency: tx.target_currency,
      exchangeRate: tx.exchange_rate,
      formatted: currencyService.formatCurrency(tx.amount, tx.currency),
      description: tx.description,
      status: tx.status,
      provider: tx.provider,
      recipient: tx.direction === 'outgoing' ? tx.to_user : null,
      sender: tx.direction === 'incoming' ? tx.from_user : null,
      createdAt: tx.created_at,
      updatedAt: tx.updated_at,
      completedAt: tx.completed_at
    }));

    res.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: history.pagination,
        summary: {
          totalTransactions: history.pagination.total,
          totalPages: history.pagination.pages
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction history:', error);
    throw new Error('Failed to retrieve transaction history');
  }
}));

/**
 * @route   GET /api/v1/transactions/:id
 * @desc    Get transaction details
 * @access  Private
 */
router.get('/:id', [
  param('id').isUUID().withMessage('Valid transaction ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { id } = req.params;

  try {
    const transaction = await transactionService.getTransaction(id);
    if (!transaction) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if user has access to this transaction
    if (transaction.from_user_id !== req.user.user_id &&
        transaction.to_user_id !== req.user.user_id) {
      throw new AuthorizationError('Access denied');
    }

    // Get additional details
    const supabase = require('../services/database').getSupabase();
    let fromUser = null;
    let toUser = null;

    if (transaction.from_user_id) {
      const { data } = await supabase
        .from('user_profiles')
        .select('full_name, phone_number')
        .eq('user_id', transaction.from_user_id)
        .single();
      fromUser = data;
    }

    if (transaction.to_user_id) {
      const { data } = await supabase
        .from('user_profiles')
        .select('full_name, phone_number')
        .eq('user_id', transaction.to_user_id)
        .single();
      toUser = data;
    }

    const formattedTransaction = {
      id: transaction.id,
      reference: transaction.transaction_reference,
      type: transaction.type,
      status: transaction.status,
      amount: transaction.amount,
      convertedAmount: transaction.converted_amount,
      currency: transaction.currency,
      targetCurrency: transaction.target_currency,
      exchangeRate: transaction.exchange_rate,
      feeAmount: transaction.fee_amount,
      formatted: {
        amount: currencyService.formatCurrency(transaction.amount, transaction.currency),
        convertedAmount: transaction.converted_amount ?
          currencyService.formatCurrency(transaction.converted_amount, transaction.target_currency) : null,
        fee: currencyService.formatCurrency(transaction.fee_amount || 0, transaction.currency)
      },
      description: transaction.description,
      provider: transaction.provider,
      externalReference: transaction.external_reference,
      metadata: transaction.metadata ? JSON.parse(transaction.metadata) : null,
      fromUser: fromUser ? {
        name: fromUser.full_name,
        phoneNumber: fromUser.phone_number
      } : null,
      toUser: toUser ? {
        name: toUser.full_name,
        phoneNumber: toUser.phone_number
      } : null,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at,
      completedAt: transaction.completed_at
    };

    res.json({
      success: true,
      data: {
        transaction: formattedTransaction
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction details:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/transactions
 * @desc    Create a new transaction
 * @access  Private
 */
router.post('/', [
  body('type').isIn(['transfer', 'topup', 'withdrawal', 'bill_payment']).withMessage('Invalid transaction type'),
  body('amount').isNumeric().withMessage('Valid amount is required'),
  body('currency').isIn(require('../config/config').business.supportedCurrencies).withMessage('Invalid currency'),
  body('description').optional().isString().withMessage('Description must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const {
    type,
    amount,
    currency,
    toUserId,
    description,
    metadata = {},
    provider
  } = req.body;

  try {
    const transactionData = {
      type,
      fromUserId: req.user.user_id,
      toUserId,
      amount,
      currency,
      description,
      metadata,
      provider
    };

    const transaction = await transactionService.createTransaction(transactionData);

    res.status(201).json({
      success: true,
      message: 'Transaction created successfully',
      data: {
        transaction: {
          id: transaction.id,
          reference: transaction.transaction_reference,
          type: transaction.type,
          status: transaction.status,
          amount: transaction.amount,
          currency: transaction.currency,
          formatted: currencyService.formatCurrency(transaction.amount, transaction.currency),
          createdAt: transaction.created_at
        }
      }
    });
  } catch (error) {
    logger.error('Failed to create transaction:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/transactions/:id/process
 * @desc    Process a pending transaction
 * @access  Private
 */
router.post('/:id/process', [
  param('id').isUUID().withMessage('Valid transaction ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { id } = req.params;

  try {
    const transaction = await transactionService.getTransaction(id);
    if (!transaction) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if user has access to process this transaction
    if (transaction.from_user_id !== req.user.user_id) {
      throw new AuthorizationError('Access denied');
    }

    const result = await transactionService.processTransaction(id);

    res.json({
      success: true,
      message: 'Transaction processed successfully',
      data: {
        transactionId: id,
        result
      }
    });
  } catch (error) {
    logger.error('Failed to process transaction:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/transactions/:id/cancel
 * @desc    Cancel a pending transaction
 * @access  Private
 */
router.post('/:id/cancel', [
  param('id').isUUID().withMessage('Valid transaction ID is required'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { id } = req.params;
  const { reason = 'Cancelled by user' } = req.body;

  try {
    const transaction = await transactionService.getTransaction(id);
    if (!transaction) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if user has access to cancel this transaction
    if (transaction.from_user_id !== req.user.user_id) {
      throw new AuthorizationError('Access denied');
    }

    await transactionService.cancelTransaction(id, reason);

    res.json({
      success: true,
      message: 'Transaction cancelled successfully',
      data: {
        transactionId: id,
        reason
      }
    });
  } catch (error) {
    logger.error('Failed to cancel transaction:', error);
    throw error;
  }
}));

module.exports = router;
