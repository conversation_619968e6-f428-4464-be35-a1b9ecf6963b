// Conditional imports for optional dependencies
let LocalAuthentication, SecureStore;
try {
  LocalAuthentication = require('expo-local-authentication');
  SecureStore = require('expo-secure-store');
} catch (error) {
  console.log('⚠️ Biometric dependencies not installed, using mock implementation');
  LocalAuthentication = null;
  SecureStore = null;
}

import { Alert } from 'react-native';

class BiometricService {
  constructor() {
    this.isInitialized = false;
    this.biometricType = null;
    this.isAvailable = false;
  }

  async initialize() {
    try {
      console.log('🔐 Initializing biometric service...');

      // Check if dependencies are available
      if (!LocalAuthentication || !SecureStore) {
        console.log('⚠️ Biometric dependencies not available, using mock implementation');
        this.isAvailable = false;
        this.biometricType = 'Mock Biometric';
        this.isInitialized = true;
        return { success: true, isAvailable: false, mock: true };
      }

      // Check if biometric authentication is available
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();

      this.isAvailable = isAvailable && isEnrolled;
      this.biometricType = this.getBiometricType(supportedTypes);

      console.log('🔐 Biometric service initialized:', {
        isAvailable: this.isAvailable,
        biometricType: this.biometricType,
        supportedTypes
      });

      this.isInitialized = true;
      return { success: true, isAvailable: this.isAvailable };
    } catch (error) {
      console.error('❌ Error initializing biometric service:', error);
      this.isInitialized = true; // Still mark as initialized to prevent repeated attempts
      this.isAvailable = false;
      return { success: false, error: error.message };
    }
  }

  getBiometricType(supportedTypes) {
    if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION)) {
      return 'Face ID';
    } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.FINGERPRINT)) {
      return 'Fingerprint';
    } else if (supportedTypes.includes(LocalAuthentication.AuthenticationType.IRIS)) {
      return 'Iris';
    }
    return 'Biometric';
  }

  async authenticate(options = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.isAvailable) {
        return {
          success: false,
          error: 'Biometric authentication is not available on this device'
        };
      }

      const defaultOptions = {
        promptMessage: 'Authenticate to continue',
        subPrompt: 'Use your biometric to verify your identity',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
        disableDeviceFallback: false,
      };

      const authOptions = { ...defaultOptions, ...options };

      console.log('🔐 Requesting biometric authentication...');
      const result = await LocalAuthentication.authenticateAsync(authOptions);

      if (result.success) {
        console.log('✅ Biometric authentication successful');
        return { success: true };
      } else {
        console.log('❌ Biometric authentication failed:', result.error);
        return {
          success: false,
          error: result.error || 'Authentication failed',
          userCancel: result.error === 'UserCancel'
        };
      }
    } catch (error) {
      console.error('❌ Error during biometric authentication:', error);
      return { success: false, error: error.message };
    }
  }

  async authenticateForTransaction(transactionType, amount) {
    const amountText = amount ? ` of UGX ${amount.toLocaleString()}` : '';
    
    return await this.authenticate({
      promptMessage: `Confirm ${transactionType}`,
      subPrompt: `Authenticate to proceed with ${transactionType}${amountText}`,
      cancelLabel: 'Cancel Transaction',
    });
  }

  async authenticateForWalletAccess() {
    return await this.authenticate({
      promptMessage: 'Access Wallet',
      subPrompt: 'Authenticate to view your wallet details',
      cancelLabel: 'Cancel',
    });
  }

  async authenticateForTopUp(amount) {
    return await this.authenticateForTransaction('top-up', amount);
  }

  async authenticateForSendMoney(amount) {
    return await this.authenticateForTransaction('money transfer', amount);
  }

  async authenticateForBillPayment(amount, billType) {
    return await this.authenticate({
      promptMessage: `Pay ${billType} Bill`,
      subPrompt: `Authenticate to pay UGX ${amount.toLocaleString()} for ${billType}`,
      cancelLabel: 'Cancel Payment',
    });
  }

  async isBiometricEnabled() {
    try {
      const enabled = await SecureStore.getItemAsync('biometric_enabled');
      return enabled === 'true';
    } catch (error) {
      console.error('❌ Error checking biometric preference:', error);
      return false;
    }
  }

  async setBiometricEnabled(enabled) {
    try {
      await SecureStore.setItemAsync('biometric_enabled', enabled.toString());
      console.log('✅ Biometric preference updated:', enabled);
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting biometric preference:', error);
      return { success: false, error: error.message };
    }
  }

  async showBiometricSetupPrompt() {
    if (!this.isAvailable) {
      Alert.alert(
        'Biometric Authentication',
        'Biometric authentication is not available on this device. Please ensure you have set up fingerprint or face recognition in your device settings.',
        [{ text: 'OK' }]
      );
      return false;
    }

    return new Promise((resolve) => {
      Alert.alert(
        'Enable Biometric Authentication',
        `Use ${this.biometricType} to secure your wallet transactions and provide quick access to your account.`,
        [
          {
            text: 'Not Now',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Enable',
            onPress: async () => {
              const authResult = await this.authenticate({
                promptMessage: 'Enable Biometric Authentication',
                subPrompt: 'Authenticate to enable biometric security for your wallet'
              });
              
              if (authResult.success) {
                await this.setBiometricEnabled(true);
                resolve(true);
              } else {
                resolve(false);
              }
            }
          }
        ]
      );
    });
  }

  getBiometricTypeDisplay() {
    return this.biometricType || 'Biometric';
  }

  isSupported() {
    return this.isAvailable;
  }

  // Mock methods for development
  async mockAuthenticate(success = true) {
    console.log('🔧 Mock biometric authentication:', success ? 'SUCCESS' : 'FAILED');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success,
          error: success ? null : 'Mock authentication failed'
        });
      }, 1000); // Simulate authentication delay
    });
  }
}

// Create and export singleton instance
const biometricService = new BiometricService();
export default biometricService;
