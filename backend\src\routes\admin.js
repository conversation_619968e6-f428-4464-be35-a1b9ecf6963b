/**
 * Admin Routes
 * Comprehensive administrative operations and system management
 */

const express = require('express');
const { adminAuthMiddleware } = require('../middleware/adminAuth');

// Import admin route modules
const dashboardRoutes = require('./admin/dashboard');
const userRoutes = require('./admin/users');
const transactionRoutes = require('./admin/transactions');
const monitoringRoutes = require('./admin/monitoring');

const router = express.Router();

// Apply admin authentication to all routes
router.use(adminAuthMiddleware);

// Mount admin route modules
router.use('/dashboard', dashboardRoutes);
router.use('/users', userRoutes);
router.use('/transactions', transactionRoutes);
router.use('/monitoring', monitoringRoutes);

module.exports = router;
