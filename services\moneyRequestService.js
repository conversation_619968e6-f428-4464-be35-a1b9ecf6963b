import supabase from './supabaseClient';
import authService from './authService';
import { formatPhoneNumber, validatePhoneNumber } from '../utils/phoneValidation';

/**
 * MoneyRequestService - Handles money request operations
 * Replaces all dummy data with real database operations
 */
class MoneyRequestService {
  constructor() {
    this.requestStatuses = {
      PENDING: 'pending',
      APPROVED: 'approved',
      DECLINED: 'declined',
      EXPIRED: 'expired',
      CANCELLED: 'cancelled'
    };
  }

  /**
   * Create a new money request
   * @param {Object} requestData - Request details
   * @returns {Promise<Object>} Request result
   */
  async createMoneyRequest(requestData) {
    try {
      console.log('💰 Creating money request:', requestData);

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Validate required fields
      const { recipient, amount, purpose, note, expiresInDays = 7 } = requestData;
      
      if (!recipient || !recipient.phoneNumber || !amount) {
        throw new Error('Missing required fields: recipient phone and amount');
      }

      // Validate phone number
      const phoneValidation = validatePhoneNumber(recipient.phoneNumber);
      if (!phoneValidation.isValid) {
        throw new Error('Invalid recipient phone number');
      }

      const formattedPhone = formatPhoneNumber(recipient.phoneNumber);

      // Create request using database function
      const { data, error } = await supabase.rpc('create_money_request', {
        p_requester_id: currentUser.id,
        p_recipient_phone: formattedPhone,
        p_recipient_name: recipient.name || 'Unknown',
        p_amount: parseFloat(amount),
        p_currency: 'UGX',
        p_purpose: purpose || 'Payment Request',
        p_note: note || '',
        p_expires_in_days: expiresInDays
      });

      if (error) {
        console.error('❌ Error creating money request:', error);
        throw new Error(error.message || 'Failed to create money request');
      }

      if (!data || data.length === 0 || !data[0].success) {
        throw new Error(data?.[0]?.message || 'Failed to create money request');
      }

      console.log('✅ Money request created successfully:', data[0]);

      return {
        success: true,
        requestId: data[0].request_id,
        message: data[0].message
      };

    } catch (error) {
      console.error('❌ MoneyRequestService.createMoneyRequest error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user's money request history
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Request history
   */
  async getMoneyRequestHistory(options = {}) {
    try {
      console.log('📋 Loading money request history...');

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const {
        limit = 50,
        offset = 0,
        status = null
      } = options;

      // Get requests using database function
      const { data, error } = await supabase.rpc('get_user_money_requests', {
        p_user_id: currentUser.id,
        p_limit: limit,
        p_offset: offset,
        p_status: status
      });

      if (error) {
        console.error('❌ Error loading money request history:', error);
        throw new Error(error.message || 'Failed to load request history');
      }

      // Format the data for the UI
      const formattedRequests = (data || []).map(request => ({
        id: request.id,
        type: request.type, // 'sent' or 'received'
        contact: {
          name: request.contact_name || 'Unknown',
          phone: request.contact_phone,
          avatar: (request.contact_name || 'U')[0].toUpperCase()
        },
        amount: parseFloat(request.amount),
        currency: request.currency,
        reason: {
          id: this.getPurposeId(request.purpose),
          title: request.purpose || 'Payment Request'
        },
        note: request.note || '',
        status: request.status,
        createdAt: new Date(request.created_at),
        updatedAt: new Date(request.updated_at),
        expiresAt: new Date(request.expires_at),
        approvedAt: request.approved_at ? new Date(request.approved_at) : null,
        declinedAt: request.declined_at ? new Date(request.declined_at) : null
      }));

      console.log(`✅ Loaded ${formattedRequests.length} money requests`);

      return {
        success: true,
        data: formattedRequests,
        pagination: {
          limit,
          offset,
          total: formattedRequests.length,
          hasMore: formattedRequests.length === limit
        }
      };

    } catch (error) {
      console.error('❌ MoneyRequestService.getMoneyRequestHistory error:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Respond to a money request (approve/decline)
   * @param {string} requestId - Request ID
   * @param {string} response - 'approve' or 'decline'
   * @returns {Promise<Object>} Response result
   */
  async respondToMoneyRequest(requestId, response) {
    try {
      console.log(`💰 Responding to money request ${requestId} with: ${response}`);

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      if (!['approve', 'decline'].includes(response)) {
        throw new Error('Invalid response. Use "approve" or "decline"');
      }

      // Respond using database function
      const { data, error } = await supabase.rpc('respond_to_money_request', {
        p_request_id: requestId,
        p_user_id: currentUser.id,
        p_response: response
      });

      if (error) {
        console.error('❌ Error responding to money request:', error);
        throw new Error(error.message || 'Failed to respond to money request');
      }

      if (!data || data.length === 0 || !data[0].success) {
        throw new Error(data?.[0]?.message || 'Failed to respond to money request');
      }

      console.log('✅ Money request response successful:', data[0]);

      return {
        success: true,
        message: data[0].message
      };

    } catch (error) {
      console.error('❌ MoneyRequestService.respondToMoneyRequest error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cancel a money request (only by requester)
   * @param {string} requestId - Request ID
   * @returns {Promise<Object>} Cancel result
   */
  async cancelMoneyRequest(requestId) {
    try {
      console.log(`💰 Cancelling money request ${requestId}`);

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Update request status to cancelled
      const { data, error } = await supabase
        .from('money_requests')
        .update({
          status: this.requestStatuses.CANCELLED,
          cancelled_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .eq('requester_id', currentUser.id)
        .eq('status', this.requestStatuses.PENDING)
        .select()
        .single();

      if (error) {
        console.error('❌ Error cancelling money request:', error);
        throw new Error(error.message || 'Failed to cancel money request');
      }

      if (!data) {
        throw new Error('Request not found or cannot be cancelled');
      }

      console.log('✅ Money request cancelled successfully');

      return {
        success: true,
        message: 'Money request cancelled successfully'
      };

    } catch (error) {
      console.error('❌ MoneyRequestService.cancelMoneyRequest error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get recent contacts from money request history
   * @param {number} limit - Number of contacts to return
   * @returns {Promise<Array>} Recent contacts
   */
  async getRecentContacts(limit = 10) {
    try {
      const historyResult = await this.getMoneyRequestHistory({ limit: 50 });
      
      if (!historyResult.success) {
        return [];
      }

      // Extract unique contacts from request history
      const contactMap = new Map();
      
      historyResult.data.forEach(request => {
        const contactKey = request.contact.phone;
        if (!contactMap.has(contactKey)) {
          contactMap.set(contactKey, {
            id: `recent_${contactKey}`,
            name: request.contact.name,
            phoneNumber: request.contact.phone,
            avatar: request.contact.avatar,
            lastRequestDate: request.createdAt,
            isRecent: true
          });
        }
      });

      // Convert to array and sort by last request date
      const recentContacts = Array.from(contactMap.values())
        .sort((a, b) => new Date(b.lastRequestDate) - new Date(a.lastRequestDate))
        .slice(0, limit);

      console.log(`✅ Found ${recentContacts.length} recent contacts`);
      return recentContacts;

    } catch (error) {
      console.error('❌ Error getting recent contacts:', error);
      return [];
    }
  }

  /**
   * Helper method to get purpose ID from purpose text
   * @param {string} purpose - Purpose text
   * @returns {string} Purpose ID
   */
  getPurposeId(purpose) {
    const purposeMap = {
      'Bill Split': 'bill_split',
      'Shared Expense': 'shared_expense',
      'Loan Repayment': 'loan_repay',
      'Payment Request': 'payment_request',
      'Emergency': 'emergency',
      'Other': 'other'
    };

    return purposeMap[purpose] || 'other';
  }

  /**
   * Get request statistics for user
   * @returns {Promise<Object>} Request statistics
   */
  async getRequestStatistics() {
    try {
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('money_requests')
        .select('status, type:requester_id, amount')
        .or(`requester_id.eq.${currentUser.id},recipient_id.eq.${currentUser.id}`);

      if (error) {
        console.error('❌ Error getting request statistics:', error);
        return { success: false, error: error.message };
      }

      const stats = {
        total: data.length,
        sent: data.filter(r => r.type === currentUser.id).length,
        received: data.filter(r => r.type !== currentUser.id).length,
        pending: data.filter(r => r.status === 'pending').length,
        approved: data.filter(r => r.status === 'approved').length,
        declined: data.filter(r => r.status === 'declined').length
      };

      return { success: true, data: stats };

    } catch (error) {
      console.error('❌ Error getting request statistics:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new MoneyRequestService();
