import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import { useLanguage } from '../contexts/LanguageContext';
import smartAmountSuggestions from '../utils/smartAmountSuggestions';
import { detectNetworkProvider } from '../utils/countriesConfig';
import verificationService from '../services/verificationService';

const TopUpScreen = ({ navigation }) => {
  // Use theme, currency, and language contexts
  const { theme } = useTheme();
  const { convertFromUGX, formatAmount, getCurrencySymbol, userCurrency } = useCurrencyContext();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [selectedMethod, setSelectedMethod] = useState(null);
  const [amount, setAmount] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [detectedNetwork, setDetectedNetwork] = useState(null);
  const [networkMismatch, setNetworkMismatch] = useState(false);
  const [mismatchMessage, setMismatchMessage] = useState('');

  const topUpMethods = [
    {
      id: 'bank_transfer',
      name: 'Bank Transfer',
      description: 'Instant transfer from your bank account',
      icon: 'card-outline',
      color: '#E67E22', // JiraniPay primary orange
      fee: 'Free',
      processingTime: 'Instant'
    },
    {
      id: 'mtn_mobile_money',
      name: 'MTN Mobile Money',
      description: 'Pay using MTN MoMo',
      icon: 'phone-portrait-outline',
      color: '#F39C12', // MTN yellow
      fee: '1.5%',
      processingTime: 'Instant'
    },
    {
      id: 'airtel_money',
      name: 'Airtel Money',
      description: 'Pay using Airtel Money',
      icon: 'phone-portrait-outline',
      color: '#E74C3C', // Airtel red
      fee: '1.5%',
      processingTime: 'Instant'
    },
    {
      id: 'agent_location',
      name: 'Agent Location',
      description: 'Visit any JiraniPay agent near you',
      icon: 'location-outline',
      color: '#27AE60', // Agent green
      fee: '1%',
      processingTime: '5-10 minutes'
    },
    {
      id: 'debit_card',
      name: 'Debit/Credit Card',
      description: 'Pay with Visa or Mastercard',
      icon: 'card-outline',
      color: '#3498DB', // Bank blue
      fee: '2.5%',
      processingTime: 'Instant'
    }
  ];

  // Smart quick amounts based on user's currency and usage patterns
  const quickAmounts = smartAmountSuggestions.generateQuickAmounts(userCurrency, 'topup');

  // Network detection and validation functions
  const detectPhoneNetwork = (phoneNumber) => {
    if (!phoneNumber || phoneNumber.length < 9) {
      return null;
    }

    // Clean phone number and extract local number
    const cleaned = phoneNumber.replace(/\D/g, '');
    let localNumber = cleaned;

    if (cleaned.startsWith('256')) {
      localNumber = cleaned.substring(3); // Remove +256
    } else if (cleaned.startsWith('0')) {
      localNumber = cleaned.substring(1); // Remove leading 0
    }

    // Use the same detection logic as other screens
    const provider = detectNetworkProvider(localNumber, 'UG');
    return provider;
  };

  const validateNetworkMatch = (phoneNumber, selectedMethod) => {
    if (!selectedMethod || !phoneNumber) {
      return { isValid: true, message: '' };
    }

    // Only validate for mobile money methods
    if (selectedMethod.id !== 'mtn_mobile_money' && selectedMethod.id !== 'airtel_money') {
      return { isValid: true, message: '' };
    }

    const detectedProvider = detectPhoneNetwork(phoneNumber);

    if (!detectedProvider) {
      return {
        isValid: false,
        message: 'Invalid Uganda phone number format. Please enter a valid mobile number.'
      };
    }

    // Map selected method to expected network
    const expectedNetworks = {
      'mtn_mobile_money': 'MTN',
      'airtel_money': 'Airtel'
    };

    const expectedNetwork = expectedNetworks[selectedMethod.id];
    const detectedNetwork = detectedProvider.key;

    if (expectedNetwork && detectedNetwork !== expectedNetwork) {
      const detectedName = detectedProvider.name;
      const expectedName = expectedNetwork === 'MTN' ? 'MTN Uganda' : 'Airtel Uganda';

      // Get example prefixes for the expected network
      const examplePrefixes = {
        'MTN': '077X/078X/076X/039X',
        'Airtel': '075X/070X/074X/020X'
      };

      return {
        isValid: false,
        message: `Phone number mismatch: You selected ${selectedMethod.name} but entered a ${detectedName} number. Please enter an ${expectedName} number (${examplePrefixes[expectedNetwork]}) or change your selected provider.`,
        detectedProvider: detectedProvider,
        canAutoSwitch: true
      };
    }

    return { isValid: true, message: '', detectedProvider: detectedProvider };
  };

  const handlePhoneNumberChange = (text) => {
    setPhoneNumber(text);

    // Real-time network detection and validation
    const detected = detectPhoneNetwork(text);
    setDetectedNetwork(detected);

    if (selectedMethod) {
      const validation = validateNetworkMatch(text, selectedMethod);
      setNetworkMismatch(!validation.isValid);
      setMismatchMessage(validation.message);
    }
  };

  const handleMethodSelect = (method) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedMethod(method);

    // Re-validate phone number with new method
    if (phoneNumber) {
      const validation = validateNetworkMatch(phoneNumber, method);
      setNetworkMismatch(!validation.isValid);
      setMismatchMessage(validation.message);
    }
  };

  const handleAutoSwitchProvider = () => {
    if (!detectedNetwork) return;

    // Find the matching provider method
    const providerMethodMap = {
      'MTN': 'mtn_mobile_money',
      'Airtel': 'airtel_money'
    };

    const matchingMethodId = providerMethodMap[detectedNetwork.key];
    if (matchingMethodId) {
      const matchingMethod = topUpMethods.find(method => method.id === matchingMethodId);
      if (matchingMethod) {
        setSelectedMethod(matchingMethod);
        setNetworkMismatch(false);
        setMismatchMessage('');

        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

        Alert.alert(
          'Provider Switched',
          `Automatically switched to ${matchingMethod.name} to match your phone number.`,
          [{ text: 'OK', style: 'default' }]
        );
      }
    }
  };

  const handleQuickAmount = (quickAmount) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setAmount(quickAmount.toString());
  };

  const calculateFee = (amount, method) => {
    if (!amount || !method) return 0;
    
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return 0;
    
    switch (method.id) {
      case 'bank_transfer':
        return 0;
      case 'mtn_mobile_money':
      case 'airtel_money':
        return Math.round(numAmount * 0.015);
      case 'agent_location':
        return Math.round(numAmount * 0.01);
      case 'debit_card':
        return Math.round(numAmount * 0.025);
      default:
        return 0;
    }
  };

  const handleTopUp = async () => {
    if (!selectedMethod) {
      Alert.alert('Error', 'Please select a top-up method');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (parseFloat(amount) < 1000) {
      Alert.alert('Error', 'Minimum top-up amount is UGX 1,000');
      return;
    }

    if (parseFloat(amount) > 5000000) {
      Alert.alert('Error', 'Maximum top-up amount is UGX 5,000,000');
      return;
    }

    if ((selectedMethod.id === 'mtn_mobile_money' || selectedMethod.id === 'airtel_money') && !phoneNumber) {
      Alert.alert('Error', 'Please enter your mobile money phone number');
      return;
    }

    // PRODUCTION FIX: Validate network provider match
    if (selectedMethod.id === 'mtn_mobile_money' || selectedMethod.id === 'airtel_money') {
      const validation = validateNetworkMatch(phoneNumber, selectedMethod);
      if (!validation.isValid) {
        Alert.alert(
          'Network Provider Mismatch',
          validation.message,
          validation.canAutoSwitch ? [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Auto-Switch Provider',
              onPress: handleAutoSwitchProvider,
              style: 'default'
            }
          ] : [{ text: 'OK', style: 'default' }]
        );
        return;
      }
    }

    // Check verification requirements for high-value top-ups
    const verificationCheck = await verificationService.checkTransactionVerification(
      parseFloat(amount),
      'top_up',
      navigation
    );

    if (!verificationCheck.success) {
      if (verificationCheck.requiresVerification) {
        // Show verification alert and handle user choice
        const shouldProceed = await verificationService.showVerificationAlert(verificationCheck);
        if (!shouldProceed) {
          return; // User chose to verify or cancelled
        }
      } else {
        Alert.alert('Transaction Error', verificationCheck.error);
        return;
      }
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    const fee = calculateFee(amount, selectedMethod);
    const totalAmount = parseFloat(amount) + fee;

    Alert.alert(
      t('wallet.confirmTopUp'),
      `${t('wallet.amount')}: UGX ${parseFloat(amount).toLocaleString()}\n${t('wallet.fee')}: UGX ${fee.toLocaleString()}\n${t('wallet.total')}: UGX ${totalAmount.toLocaleString()}\n\n${t('wallet.provider')}: ${selectedMethod.name}`,
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.confirm'),
          onPress: () => processTopUp()
        }
      ]
    );
  };

  const processTopUp = async () => {
    setLoading(true);
    
    // Simulate processing time
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        t('wallet.topUpSuccessful'),
        `UGX ${parseFloat(amount).toLocaleString()} ${t('wallet.topUpSuccessful')}\n\n${t('wallet.transactionId')}: TXN${Date.now()}`,
        [
          {
            text: t('common.ok'),
            onPress: () => navigation.goBack()
          }
        ]
      );
    }, 2000);
  };

  const renderTopUpMethod = (method) => (
    <TouchableOpacity
      key={method.id}
      style={[
        styles.methodCard,
        selectedMethod?.id === method.id && styles.selectedMethodCard
      ]}
      onPress={() => handleMethodSelect(method)}
    >
      <View style={styles.methodLeft}>
        <View style={[styles.methodIcon, { backgroundColor: method.color }]}>
          <Ionicons name={method.icon} size={24} color="#FFFFFF" />
        </View>
        <View style={styles.methodDetails}>
          <Text style={styles.methodName}>{method.name}</Text>
          <Text style={styles.methodDescription}>{method.description}</Text>
          <View style={styles.methodInfo}>
            <Text style={styles.methodFee}>Fee: {method.fee}</Text>
            <Text style={styles.methodTime}>• {method.processingTime}</Text>
          </View>
        </View>
      </View>
      {selectedMethod?.id === method.id && (
        <Ionicons name="checkmark-circle" size={24} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('wallet.topUpWallet')}</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Amount Input */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Enter Amount</Text>
          <View style={styles.amountContainer}>
            <Text style={styles.currencySymbol}>{getCurrencySymbol()}</Text>
            <TextInput
              style={styles.amountInput}
              value={amount}
              onChangeText={setAmount}
              placeholder="0"
              placeholderTextColor={theme.colors.placeholder}
              keyboardType="numeric"
              maxLength={10}
            />
          </View>
          
          {/* Quick Amount Buttons */}
          <View style={styles.quickAmountsContainer}>
            <Text style={styles.quickAmountsTitle}>Quick Amounts</Text>
            <View style={styles.quickAmountsGrid}>
              {quickAmounts.map((quickAmount) => (
                <TouchableOpacity
                  key={quickAmount}
                  style={[
                    styles.quickAmountButton,
                    amount === quickAmount.toString() && styles.selectedQuickAmount
                  ]}
                  onPress={() => handleQuickAmount(quickAmount)}
                >
                  <Text style={[
                    styles.quickAmountText,
                    amount === quickAmount.toString() && styles.selectedQuickAmountText
                  ]}>
                    {formatAmount(quickAmount, userCurrency, { showSymbol: false })}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Top-Up Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Payment Method</Text>
          {topUpMethods.map(renderTopUpMethod)}
        </View>

        {/* Mobile Money Phone Number */}
        {(selectedMethod?.id === 'mtn_mobile_money' || selectedMethod?.id === 'airtel_money') && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Mobile Money Number</Text>
            <View style={styles.phoneInputContainer}>
              <TextInput
                style={[
                  styles.phoneInput,
                  networkMismatch && styles.phoneInputError
                ]}
                value={phoneNumber}
                onChangeText={handlePhoneNumberChange}
                placeholder="Enter your mobile money number"
                placeholderTextColor={theme.colors.placeholder}
                keyboardType="phone-pad"
                maxLength={15}
              />

              {/* Network Detection Badge */}
              {detectedNetwork && detectedNetwork.name !== 'Unknown Network' && (
                <View style={[
                  styles.networkBadge,
                  { backgroundColor: detectedNetwork.color + '20' },
                  networkMismatch && styles.networkBadgeError
                ]}>
                  <Text style={[
                    styles.networkText,
                    { color: networkMismatch ? '#E53E3E' : detectedNetwork.color }
                  ]}>
                    {detectedNetwork.name}
                  </Text>
                </View>
              )}
            </View>

            {/* Network Mismatch Warning */}
            {networkMismatch && mismatchMessage && (
              <View style={styles.mismatchWarning}>
                <Ionicons name="warning" size={16} color="#E53E3E" />
                <Text style={styles.mismatchText}>{mismatchMessage}</Text>
                {detectedNetwork && (
                  <TouchableOpacity
                    style={styles.autoSwitchButton}
                    onPress={handleAutoSwitchProvider}
                  >
                    <Text style={styles.autoSwitchText}>Auto-Switch</Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Helper Text */}
            {selectedMethod && !networkMismatch && (
              <Text style={styles.helperText}>
                {selectedMethod.id === 'mtn_mobile_money'
                  ? 'Enter your MTN Mobile Money number (077X, 078X, 076X, 039X)'
                  : 'Enter your Airtel Money number (075X, 070X, 074X, 020X)'
                }
              </Text>
            )}
          </View>
        )}

        {/* Fee Summary */}
        {selectedMethod && amount && parseFloat(amount) > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Transaction Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Amount</Text>
              <Text style={styles.summaryValue}>{formatAmount(parseFloat(amount))}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Fee</Text>
              <Text style={styles.summaryValue}>{formatAmount(calculateFee(amount, selectedMethod))}</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>
                {formatAmount(parseFloat(amount) + calculateFee(amount, selectedMethod))}
              </Text>
            </View>
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Top-Up Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity
          style={[
            styles.topUpButton,
            (!selectedMethod || !amount || parseFloat(amount) <= 0 || loading) && styles.disabledButton
          ]}
          onPress={handleTopUp}
          disabled={!selectedMethod || !amount || parseFloat(amount) <= 0 || loading}
        >
          <Text style={[
            styles.topUpButtonText,
            (!selectedMethod || !amount || parseFloat(amount) <= 0 || loading) && styles.disabledButtonText
          ]}>
            {loading ? 'Processing...' : 'Top Up Wallet'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.primary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  currencySymbol: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  quickAmountsContainer: {
    marginTop: 8,
  },
  quickAmountsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  quickAmountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickAmountButton: {
    width: '30%',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedQuickAmount: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '20',
  },
  quickAmountText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  selectedQuickAmountText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  methodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginBottom: 12,
  },
  selectedMethodCard: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '10',
  },
  methodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  methodDetails: {
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  methodDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodFee: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  methodTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  phoneInputContainer: {
    position: 'relative',
    marginTop: 8,
  },
  phoneInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: theme.colors.text,
    paddingRight: 120, // Make room for network badge
  },
  phoneInputError: {
    borderColor: theme.colors.error,
    borderWidth: 2,
  },
  networkBadge: {
    position: 'absolute',
    right: 12,
    top: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  networkBadgeError: {
    backgroundColor: '#FED7D7',
    borderColor: '#E53E3E',
  },
  networkText: {
    fontSize: 12,
    fontWeight: '600',
  },
  mismatchWarning: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: theme.colors.errorBackground,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderWidth: 1,
    borderColor: theme.colors.error,
  },
  mismatchText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.error,
    marginLeft: 8,
    lineHeight: 20,
  },
  autoSwitchButton: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  autoSwitchText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
  },
  helperText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 8,
    lineHeight: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  totalRow: {
    borderBottomWidth: 0,
    paddingTop: 12,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: theme.colors.border,
  },
  summaryLabel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  bottomContainer: {
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  topUpButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: theme.colors.disabled,
  },
  topUpButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  disabledButtonText: {
    color: theme.colors.textSecondary,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default TopUpScreen;
