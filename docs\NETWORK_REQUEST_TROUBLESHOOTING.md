# 🌐 JiraniPay Network Request Troubleshooting Guide

## 🚨 **ISSUE: Network Request Failures After Authentication**

This guide addresses "Network request failure" errors that occur specifically AFTER successful user authentication, particularly affecting:
- User preferences loading
- Profile picture uploads
- Subsequent API calls to Supabase

---

## 🔍 **ROOT CAUSE ANALYSIS**

### **Primary Issues Identified:**

1. **Production Mode API Restrictions** ❌
   - Services failing when `__DEV__` is false
   - Development mode code still present in production

2. **Authentication Token Issues** ❌
   - Tokens not being passed correctly in subsequent requests
   - Token expiry not being handled properly

3. **Network Timeout Configuration** ❌
   - Too aggressive timeouts for production environment
   - Poor retry logic for network failures

4. **Supabase Connection Issues** ❌
   - Authentication works but subsequent API calls fail
   - Missing authentication headers in requests

---

## ✅ **SOLUTIONS IMPLEMENTED**

### **1. Enhanced Network Service**
```javascript
// New enhanced network service with:
✅ Automatic token refresh
✅ Retry logic with exponential backoff
✅ Request queuing for poor connections
✅ Comprehensive error handling
✅ Production-ready timeouts
```

### **2. Production Mode Fixes**
```javascript
// Removed development mode code from:
✅ databaseService.js - No more __DEV__ checks
✅ preferenceService.js - Enhanced network handling
✅ profileManagementService.js - Robust upload handling
```

### **3. Authentication Token Management**
```javascript
// Enhanced token handling:
✅ Automatic token refresh before expiry
✅ Token validation for all requests
✅ Fallback authentication methods
✅ Session management improvements
```

### **4. Network Request Improvements**
```javascript
// Improved request handling:
✅ Longer timeouts for production (15-30s)
✅ Retry logic with exponential backoff
✅ Request queuing for offline scenarios
✅ Comprehensive error categorization
```

---

## 🧪 **TESTING & VALIDATION**

### **Run Network Diagnostics**
```bash
npm run test-network
```

**Expected Output:**
```
✅ User Preferences Loading: PASSED
✅ Authentication Token Validation: PASSED  
✅ Profile Picture Upload: PASSED
✅ Network Connectivity: PASSED
```

### **Test Specific Issues**
```bash
# Test app initialization
npm run test-init

# Validate production mode
npm run validate-production

# Check environment configuration
npm run debug-env
```

---

## 🔧 **TROUBLESHOOTING STEPS**

### **Step 1: Check Network Connectivity**
```javascript
// In your app, check network status:
import enhancedNetworkService from './services/enhancedNetworkService';

const networkStatus = enhancedNetworkService.getNetworkStatus();
console.log('Network status:', networkStatus);
```

### **Step 2: Validate Authentication**
```javascript
// Check if user is properly authenticated:
import authService from './services/authService';

const user = authService.getCurrentUser();
console.log('Current user:', user);

// Check session
const session = await authService.getSession();
console.log('Session valid:', !!session);
```

### **Step 3: Test Supabase Connection**
```javascript
// Test direct Supabase connection:
import supabase from './services/supabaseClient';

try {
  const { data, error } = await supabase.auth.getSession();
  console.log('Supabase connection:', error ? 'Failed' : 'Success');
} catch (error) {
  console.error('Supabase error:', error);
}
```

### **Step 4: Check Environment Variables**
```bash
# Verify environment configuration
npm run debug-env

# Expected output should show:
# ✅ EXPO_PUBLIC_ENVIRONMENT: production
# ✅ EXPO_PUBLIC_PROD_SUPABASE_URL: SET
# ✅ EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY: SET
```

---

## 🚨 **COMMON ERROR PATTERNS & SOLUTIONS**

### **Error: "Network request failure" on preferences**
```javascript
// CAUSE: Token not being passed correctly
// SOLUTION: Enhanced network service handles this automatically

// OLD (problematic):
const result = await databaseService.getUserPreferences(userId);

// NEW (fixed):
const result = await enhancedNetworkService.getUserPreferences(userId);
```

### **Error: "Network request failure" on profile picture upload**
```javascript
// CAUSE: Upload timeout or authentication issues
// SOLUTION: Enhanced upload with retry logic

// The service now includes:
✅ Automatic token refresh
✅ Retry logic for failed uploads
✅ Fallback to direct Supabase upload
✅ Proper error handling
```

### **Error: "JWT expired" or authentication errors**
```javascript
// CAUSE: Token expiry not handled
// SOLUTION: Automatic token refresh

// Enhanced network service automatically:
✅ Checks token expiry before requests
✅ Refreshes tokens when needed
✅ Retries requests with new tokens
```

---

## 📊 **MONITORING & DEBUGGING**

### **Enable Debug Logging**
```javascript
// Add to your component to see detailed logs:
useEffect(() => {
  const networkStatus = enhancedNetworkService.getNetworkStatus();
  console.log('🌐 Network Status:', networkStatus);
}, []);
```

### **Monitor Request Queue**
```javascript
// Check if requests are being queued:
console.log('📥 Queued requests:', enhancedNetworkService.requestQueue.length);
```

### **Check Authentication Status**
```javascript
// Monitor auth state changes:
authService.addAuthStateListener((user) => {
  console.log('🔐 Auth state changed:', user ? 'authenticated' : 'not authenticated');
});
```

---

## 🎯 **PERFORMANCE TARGETS**

| Metric | Target | Current Status |
|--------|--------|----------------|
| **User Preferences Load** | <3s | ✅ Enhanced |
| **Profile Picture Upload** | <30s | ✅ Enhanced |
| **Token Refresh** | <2s | ✅ Automatic |
| **Network Error Recovery** | <5s | ✅ Queuing |
| **Offline Request Handling** | Queued | ✅ Implemented |

---

## 🔧 **MANUAL FIXES (If Issues Persist)**

### **Force Token Refresh**
```javascript
// In your component:
import enhancedNetworkService from './services/enhancedNetworkService';

const forceTokenRefresh = async () => {
  await enhancedNetworkService.refreshAuthToken();
  console.log('🔐 Token refreshed manually');
};
```

### **Clear Request Queue**
```javascript
// If requests are stuck in queue:
enhancedNetworkService.clearQueue();
console.log('🧹 Request queue cleared');
```

### **Fallback to Direct Supabase**
```javascript
// If enhanced service fails, use direct Supabase:
import supabase from './services/supabaseClient';

const directRequest = async () => {
  const { data, error } = await supabase
    .from('user_preferences')
    .select('*')
    .eq('id', userId)
    .single();
    
  return { success: !error, data, error };
};
```

---

## 📞 **ADDITIONAL SUPPORT**

### **Available Commands**
```bash
# Comprehensive diagnostics
npm run test-network

# Test app initialization
npm run test-init

# Validate production readiness
npm run validate-production

# Check security configuration
npm run validate-security

# Debug environment variables
npm run debug-env
```

### **Log Analysis**
Look for these patterns in console logs:
```
✅ Enhanced network service initialized
✅ Auth token refreshed successfully
✅ Preferences loaded from database
✅ Enhanced network upload successful
```

### **Error Patterns to Watch**
```
❌ Authentication required but no valid token available
❌ Request timeout
❌ Network request failure
❌ JWT expired
```

---

## 🎉 **SUCCESS INDICATORS**

**✅ Network requests are working correctly when you see:**

1. **Fast preferences loading** - User settings load immediately after login
2. **Successful profile picture uploads** - Images upload without errors
3. **No timeout errors** - Requests complete within expected timeframes
4. **Automatic error recovery** - Failed requests retry automatically
5. **Offline request queuing** - Requests work when connection is restored

**🚀 Your JiraniPay app now has enterprise-grade network request handling with comprehensive error recovery and authentication management!**
