/**
 * JiraniPay Backend Configuration
 * Centralized configuration management for all environments
 */

const path = require('path');

// Load environment variables
require('dotenv').config();

const config = {
  // Server Configuration
  server: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT, 10) || 3000,
    host: process.env.HOST || 'localhost',
    apiVersion: process.env.API_VERSION || 'v1',
    enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING === 'true'
  },

  // Database Configuration
  database: {
    supabase: {
      url: process.env.SUPABASE_URL,
      anonKey: process.env.SUPABASE_ANON_KEY,
      serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY
    },
    postgres: {
      url: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
      pool: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 30000
      }
    }
  },

  // Redis Configuration
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    issuer: 'jiranipay-backend',
    audience: 'jiranipay-mobile'
  },

  // Security Configuration
  security: {
    encryptionKey: process.env.ENCRYPTION_KEY,
    hashSaltRounds: parseInt(process.env.HASH_SALT_ROUNDS, 10) || 12,
    enableEncryption: process.env.NODE_ENV === 'production',
    webhookSecret: process.env.WEBHOOK_SECRET
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100
  },

  // CORS Configuration
  cors: {
    allowedOrigins: [
      'http://localhost:3000',
      'http://localhost:19006', // Expo dev server
      'https://jiranipay.com',
      'https://app.jiranipay.com',
      'https://admin.jiranipay.com'
    ]
  },

  // SMS Configuration
  sms: {
    provider: 'africastalking',
    africastalking: {
      username: process.env.AFRICASTALKING_USERNAME,
      apiKey: process.env.AFRICASTALKING_API_KEY,
      senderId: process.env.SMS_SENDER_ID || 'JiraniPay'
    }
  },

  // Email Configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT, 10) || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    },
    from: process.env.EMAIL_FROM || 'JiraniPay <<EMAIL>>'
  },

  // Mobile Money APIs
  mobileMoneyApis: {
    mtn: {
      baseUrl: process.env.MTN_API_BASE_URL,
      subscriptionKey: process.env.MTN_SUBSCRIPTION_KEY,
      apiUserId: process.env.MTN_API_USER_ID,
      apiKey: process.env.MTN_API_KEY,
      callbackUrl: process.env.MTN_CALLBACK_URL
    },
    airtel: {
      baseUrl: process.env.AIRTEL_API_BASE_URL,
      clientId: process.env.AIRTEL_CLIENT_ID,
      clientSecret: process.env.AIRTEL_CLIENT_SECRET,
      callbackUrl: process.env.AIRTEL_CALLBACK_URL
    }
  },

  // Banking APIs
  bankingApis: {
    stanbic: {
      baseUrl: process.env.STANBIC_API_BASE_URL,
      clientId: process.env.STANBIC_CLIENT_ID,
      clientSecret: process.env.STANBIC_CLIENT_SECRET
    }
  },

  // Utility Provider APIs
  utilityApis: {
    umeme: {
      baseUrl: process.env.UMEME_API_BASE_URL,
      apiKey: process.env.UMEME_API_KEY,
      merchantId: process.env.UMEME_MERCHANT_ID
    },
    nwsc: {
      baseUrl: process.env.NWSC_API_BASE_URL,
      apiKey: process.env.NWSC_API_KEY,
      merchantId: process.env.NWSC_MERCHANT_ID
    }
  },

  // Currency Exchange
  currencyExchange: {
    apiKey: process.env.EXCHANGE_RATE_API_KEY,
    baseUrl: process.env.EXCHANGE_RATE_BASE_URL || 'https://api.exchangerate-api.com/v4'
  },

  // KYC & Compliance
  kyc: {
    apiBaseUrl: process.env.KYC_API_BASE_URL,
    apiKey: process.env.KYC_API_KEY
  },

  // Fraud Detection
  fraudDetection: {
    apiUrl: process.env.FRAUD_DETECTION_API_URL,
    apiKey: process.env.FRAUD_DETECTION_API_KEY
  },

  // File Upload
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
    uploadPath: process.env.UPLOAD_PATH || 'uploads/',
    allowedFileTypes: (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,pdf').split(',')
  },

  // Business Configuration
  business: {
    defaultCurrency: process.env.DEFAULT_CURRENCY || 'UGX',
    supportedCurrencies: (process.env.SUPPORTED_CURRENCIES || 'UGX,KES,TZS,RWF,BIF,ETB').split(','),
    defaultCountry: process.env.DEFAULT_COUNTRY || 'UG',
    supportedCountries: (process.env.SUPPORTED_COUNTRIES || 'UG,KE,TZ,RW,BI,ET').split(',')
  },

  // Transaction Limits (in base currency - UGX)
  transactionLimits: {
    maxTransactionAmount: parseInt(process.env.MAX_TRANSACTION_AMOUNT, 10) || 10000000, // 10M UGX
    dailyTransactionLimit: parseInt(process.env.DAILY_TRANSACTION_LIMIT, 10) || 50000000, // 50M UGX
    kycRequiredAmount: parseInt(process.env.KYC_REQUIRED_AMOUNT, 10) || 1000000, // 1M UGX
    biometricRequiredAmount: parseInt(process.env.BIOMETRIC_REQUIRED_AMOUNT, 10) || 500000 // 500K UGX
  },

  // Webhook Configuration
  webhooks: {
    secret: process.env.WEBHOOK_SECRET,
    timeout: parseInt(process.env.WEBHOOK_TIMEOUT, 10) || 30000
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableFileLogging: process.env.NODE_ENV === 'production',
    logDirectory: path.join(__dirname, '../../logs')
  },

  // Monitoring
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN
  },

  // Development/Testing Configuration
  development: {
    enableMockPayments: process.env.ENABLE_MOCK_PAYMENTS === 'true',
    enableTestEndpoints: process.env.ENABLE_TEST_ENDPOINTS === 'true',
    bypassKycInDev: process.env.BYPASS_KYC_IN_DEV === 'true'
  }
};

// Validation function
function validateConfig() {
  const requiredEnvVars = [
    'JWT_SECRET',
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  // Validate JWT secret length
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  // Validate encryption key in production
  if (config.server.nodeEnv === 'production' && !process.env.ENCRYPTION_KEY) {
    throw new Error('ENCRYPTION_KEY is required in production');
  }
}

// Validate configuration on load
try {
  validateConfig();
} catch (error) {
  console.error('Configuration validation failed:', error.message);
  if (config.server.nodeEnv === 'production') {
    process.exit(1);
  }
}

module.exports = config;
