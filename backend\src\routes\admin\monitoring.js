/**
 * Admin Monitoring Routes
 * Comprehensive system monitoring and observability endpoints for administrators
 */

const express = require('express');
const { query, param, validationResult } = require('express-validator');
const { asyncHandler, ValidationError } = require('../../middleware/errorHandler');
const { adminAuthMiddleware } = require('../../middleware/adminAuth');
const monitoringService = require('../../services/monitoringService');
const databaseOptimizationService = require('../../services/databaseOptimizationService');
const loadBalancerService = require('../../services/loadBalancerService');
const redisService = require('../../services/redis');
const logger = require('../../utils/logger');

const router = express.Router();

// Apply admin authentication to all routes
router.use(adminAuthMiddleware);

/**
 * @route   GET /api/v1/admin/monitoring/dashboard
 * @desc    Get comprehensive monitoring dashboard data
 * @access  Admin
 */
router.get('/dashboard', asyncHandler(async (req, res) => {
  try {
    const dashboardData = await monitoringService.getDashboardData();
    
    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Failed to get monitoring dashboard data:', error);
    throw new Error('Failed to retrieve monitoring dashboard data');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/health
 * @desc    Get comprehensive system health status
 * @access  Admin
 */
router.get('/health', asyncHandler(async (req, res) => {
  try {
    const healthStatus = await monitoringService.getHealthStatus();
    
    res.json({
      success: true,
      data: healthStatus
    });
  } catch (error) {
    logger.error('Failed to get health status:', error);
    throw new Error('Failed to retrieve health status');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/metrics/system
 * @desc    Get detailed system metrics
 * @access  Admin
 */
router.get('/metrics/system', asyncHandler(async (req, res) => {
  try {
    const systemMetrics = await redisService.get('system_metrics');
    
    res.json({
      success: true,
      data: {
        metrics: systemMetrics || {},
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get system metrics:', error);
    throw new Error('Failed to retrieve system metrics');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/metrics/application
 * @desc    Get detailed application metrics
 * @access  Admin
 */
router.get('/metrics/application', asyncHandler(async (req, res) => {
  try {
    const appMetrics = await redisService.get('app_metrics');
    
    res.json({
      success: true,
      data: {
        metrics: appMetrics || {},
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get application metrics:', error);
    throw new Error('Failed to retrieve application metrics');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/metrics/business
 * @desc    Get business metrics and KPIs
 * @access  Admin
 */
router.get('/metrics/business', asyncHandler(async (req, res) => {
  try {
    const businessMetrics = await redisService.get('business_metrics');
    
    res.json({
      success: true,
      data: {
        metrics: businessMetrics || {},
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get business metrics:', error);
    throw new Error('Failed to retrieve business metrics');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/database
 * @desc    Get database performance metrics and optimization data
 * @access  Admin
 */
router.get('/database', asyncHandler(async (req, res) => {
  try {
    const dbMetrics = await databaseOptimizationService.getPerformanceMetrics();
    
    res.json({
      success: true,
      data: dbMetrics
    });
  } catch (error) {
    logger.error('Failed to get database metrics:', error);
    throw new Error('Failed to retrieve database metrics');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/database/slow-queries
 * @desc    Get slow query analysis
 * @access  Admin
 */
router.get('/database/slow-queries', asyncHandler(async (req, res) => {
  try {
    const slowQueries = await redisService.get('slow_queries') || [];
    
    res.json({
      success: true,
      data: {
        slowQueries,
        count: slowQueries.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get slow queries:', error);
    throw new Error('Failed to retrieve slow queries');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/load-balancer
 * @desc    Get load balancer status and metrics
 * @access  Admin
 */
router.get('/load-balancer', asyncHandler(async (req, res) => {
  try {
    const lbStatus = await loadBalancerService.getStatus();
    
    res.json({
      success: true,
      data: lbStatus
    });
  } catch (error) {
    logger.error('Failed to get load balancer status:', error);
    throw new Error('Failed to retrieve load balancer status');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/cache
 * @desc    Get cache performance metrics
 * @access  Admin
 */
router.get('/cache', asyncHandler(async (req, res) => {
  try {
    const cacheStats = await redisService.getStats();
    const cacheHealth = await redisService.healthCheck();
    
    res.json({
      success: true,
      data: {
        stats: cacheStats,
        health: cacheHealth,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get cache metrics:', error);
    throw new Error('Failed to retrieve cache metrics');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/alerts
 * @desc    Get recent alerts and alert history
 * @access  Admin
 */
router.get('/alerts', [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { limit = 50 } = req.query;
    const recentAlerts = await redisService.lrange('recent_alerts', 0, limit - 1);
    
    res.json({
      success: true,
      data: {
        alerts: recentAlerts || [],
        count: recentAlerts?.length || 0,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get alerts:', error);
    throw new Error('Failed to retrieve alerts');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/logs
 * @desc    Get recent system logs
 * @access  Admin
 */
router.get('/logs', [
  query('level').optional().isIn(['error', 'warn', 'info', 'debug']).withMessage('Invalid log level'),
  query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000'),
  query('service').optional().isString().withMessage('Service must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { level, limit = 100, service } = req.query;
    
    // In production, this would query actual log storage (ELK, CloudWatch, etc.)
    const logs = await redisService.lrange('system_logs', 0, limit - 1);
    
    let filteredLogs = logs || [];
    
    if (level) {
      filteredLogs = filteredLogs.filter(log => log.level === level);
    }
    
    if (service) {
      filteredLogs = filteredLogs.filter(log => log.service === service);
    }
    
    res.json({
      success: true,
      data: {
        logs: filteredLogs,
        count: filteredLogs.length,
        filters: { level, service, limit },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to get logs:', error);
    throw new Error('Failed to retrieve logs');
  }
}));

/**
 * @route   POST /api/v1/admin/monitoring/load-balancer/algorithm
 * @desc    Change load balancing algorithm
 * @access  Admin
 */
router.post('/load-balancer/algorithm', [
  query('algorithm').isIn(['round_robin', 'least_connections', 'weighted_round_robin', 'ip_hash', 'least_response_time'])
    .withMessage('Invalid load balancing algorithm')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { algorithm } = req.query;
    const success = loadBalancerService.setAlgorithm(algorithm);
    
    if (success) {
      logger.audit('Load balancing algorithm changed', {
        adminId: req.user.user_id,
        newAlgorithm: algorithm,
        timestamp: new Date().toISOString()
      });
      
      res.json({
        success: true,
        message: `Load balancing algorithm changed to ${algorithm}`,
        data: {
          algorithm,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      throw new ValidationError('Invalid algorithm specified');
    }
  } catch (error) {
    logger.error('Failed to change load balancing algorithm:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/performance
 * @desc    Get comprehensive performance analysis
 * @access  Admin
 */
router.get('/performance', asyncHandler(async (req, res) => {
  try {
    const [
      systemMetrics,
      appMetrics,
      dbMetrics,
      cacheStats,
      lbStatus
    ] = await Promise.all([
      redisService.get('system_metrics'),
      redisService.get('app_metrics'),
      databaseOptimizationService.getPerformanceMetrics(),
      redisService.getStats(),
      loadBalancerService.getStatus()
    ]);

    const performanceAnalysis = {
      overall: {
        status: 'healthy', // Would be calculated based on thresholds
        score: 85, // Performance score out of 100
        timestamp: new Date().toISOString()
      },
      system: {
        cpu: systemMetrics?.cpu || {},
        memory: systemMetrics?.memory || {},
        disk: systemMetrics?.disk || {},
        network: systemMetrics?.network || {}
      },
      application: {
        requests: appMetrics?.requests || {},
        responses: appMetrics?.responses || {},
        errors: appMetrics?.errors || {}
      },
      database: {
        connectionPool: dbMetrics?.connectionPool || {},
        slowQueries: dbMetrics?.slowQueries || [],
        indexUsage: dbMetrics?.indexUsage || {}
      },
      cache: {
        hitRate: cacheStats?.hitRate || 0,
        memoryUsage: cacheStats?.memoryUsage || 0,
        keyCount: cacheStats?.keyCount || 0
      },
      loadBalancer: {
        algorithm: lbStatus?.algorithm || 'round_robin',
        healthyServers: lbStatus?.healthyServers || 0,
        totalServers: lbStatus?.totalServers || 0
      }
    };

    res.json({
      success: true,
      data: performanceAnalysis
    });
  } catch (error) {
    logger.error('Failed to get performance analysis:', error);
    throw new Error('Failed to retrieve performance analysis');
  }
}));

/**
 * @route   GET /api/v1/admin/monitoring/real-time
 * @desc    Get real-time monitoring data (for live dashboards)
 * @access  Admin
 */
router.get('/real-time', asyncHandler(async (req, res) => {
  try {
    const realTimeData = {
      timestamp: new Date().toISOString(),
      system: {
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        activeConnections: Math.floor(Math.random() * 1000)
      },
      transactions: {
        perSecond: Math.floor(Math.random() * 50),
        successRate: 95 + Math.random() * 5,
        averageResponseTime: 100 + Math.random() * 200
      },
      errors: {
        count: Math.floor(Math.random() * 10),
        rate: Math.random() * 2
      },
      alerts: {
        active: Math.floor(Math.random() * 3),
        severity: ['info', 'warning', 'critical'][Math.floor(Math.random() * 3)]
      }
    };

    res.json({
      success: true,
      data: realTimeData
    });
  } catch (error) {
    logger.error('Failed to get real-time data:', error);
    throw new Error('Failed to retrieve real-time data');
  }
}));

module.exports = router;
