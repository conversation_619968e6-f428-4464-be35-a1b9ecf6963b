/**
 * Savings Dashboard Screen
 * Main dashboard for savings accounts with overview, quick actions,
 * and account management features
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import savingsAccountService from '../services/savingsAccountService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId, requireAuthentication } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsDashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  // State
  const [accounts, setAccounts] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadSavingsData();
  }, []);

  const loadSavingsData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to view savings');
        navigation.goBack();
        return;
      }

      // Load accounts and summary in parallel
      const [accountsResult, summaryResult] = await Promise.all([
        savingsAccountService.getUserSavingsAccounts(userId, { isActive: true }),
        savingsAccountService.getSavingsSummary(userId)
      ]);

      if (accountsResult.success) {
        setAccounts(accountsResult.accounts);
      }

      if (summaryResult.success) {
        setSummary(summaryResult.summary);
      }

    } catch (error) {
      console.error('❌ Error loading savings data:', error);
      Alert.alert('Error', 'Failed to load savings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadSavingsData(true);
  };

  const handleCreateAccount = () => {
    navigation.navigate('SavingsAccountCreation');
  };

  const handleAccountPress = (account) => {
    navigation.navigate('SavingsAccountDetails', { accountId: account.id });
  };

  const getAccountTypeIcon = (accountType) => {
    const icons = {
      general: 'wallet',
      goal: 'flag',
      emergency: 'shield-checkmark',
      vacation: 'airplane',
      education: 'school',
      retirement: 'time'
    };
    return icons[accountType] || 'wallet';
  };

  const getAccountTypeColor = (accountType) => {
    const colors = {
      general: '#4ECDC4',
      goal: '#45B7D1',
      emergency: '#FF6B35',
      vacation: '#96CEB4',
      education: '#FECA57',
      retirement: '#6C5CE7'
    };
    return colors[accountType] || '#4ECDC4';
  };

  const renderSummaryCard = () => {
    if (!summary) return null;

    return (
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Total Savings</Text>
          <TouchableOpacity onPress={() => navigation.navigate('SavingsAnalytics')}>
            <Ionicons name="analytics-outline" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.totalAmount}>
          {formatCurrency(summary.totalSavings || 0, 'UGX')}
        </Text>
        
        <View style={styles.summaryStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{summary.totalAccounts || 0}</Text>
            <Text style={styles.statLabel}>Accounts</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{formatCurrency(summary.totalInterest || 0, 'UGX')}</Text>
            <Text style={styles.statLabel}>Interest Earned</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{summary.goalAccounts || 0}</Text>
            <Text style={styles.statLabel}>Goals</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity style={styles.actionItem} onPress={handleCreateAccount}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.primary + '20' }]}>
            <Ionicons name="add" size={24} color={theme.colors.primary} />
          </View>
          <Text style={styles.actionLabel}>Create Account</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('SavingsGoals')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.success + '20' }]}>
            <Ionicons name="flag" size={24} color={theme.colors.success} />
          </View>
          <Text style={styles.actionLabel}>Set Goals</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('SavingsTransfers')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.warning + '20' }]}>
            <Ionicons name="swap-horizontal" size={24} color={theme.colors.warning} />
          </View>
          <Text style={styles.actionLabel}>Transfer</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionItem} onPress={() => navigation.navigate('SavingsReports')}>
          <View style={[styles.actionIcon, { backgroundColor: theme.colors.info + '20' }]}>
            <Ionicons name="bar-chart" size={24} color={theme.colors.info} />
          </View>
          <Text style={styles.actionLabel}>Reports</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAccountCard = ({ item: account }) => (
    <TouchableOpacity 
      style={styles.accountCard}
      onPress={() => handleAccountPress(account)}
    >
      <View style={styles.accountHeader}>
        <View style={[styles.accountIcon, { backgroundColor: getAccountTypeColor(account.accountType) }]}>
          <Ionicons name={getAccountTypeIcon(account.accountType)} size={20} color={theme.colors.white} />
        </View>
        <View style={styles.accountInfo}>
          <Text style={styles.accountName}>{account.accountName}</Text>
          <Text style={styles.accountType}>
            {account.accountType.charAt(0).toUpperCase() + account.accountType.slice(1)} Savings
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </View>
      
      <View style={styles.accountBalance}>
        <Text style={styles.balanceAmount}>{formatCurrency(account.currentBalance, account.currency)}</Text>
        {account.totalInterestEarned > 0 && (
          <Text style={styles.interestEarned}>
            +{formatCurrency(account.totalInterestEarned, account.currency)} interest
          </Text>
        )}
      </View>
      
      {account.targetAmount && (
        <View style={styles.progressContainer}>
          <View style={styles.progressInfo}>
            <Text style={styles.progressText}>
              {account.progressPercentage.toFixed(1)}% of {formatCurrency(account.targetAmount, account.currency)}
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { 
                  width: `${Math.min(account.progressPercentage, 100)}%`,
                  backgroundColor: getAccountTypeColor(account.accountType)
                }
              ]} 
            />
          </View>
        </View>
      )}
      
      {account.autoTransferEnabled && (
        <View style={styles.autoTransferBadge}>
          <Ionicons name="repeat" size={12} color={theme.colors.primary} />
          <Text style={styles.autoTransferText}>Auto Transfer</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="wallet-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Start Your Savings Journey</Text>
      <Text style={styles.emptyDescription}>
        Create your first savings account and start building towards your financial goals
      </Text>
      <TouchableOpacity style={styles.createButton} onPress={handleCreateAccount}>
        <Text style={styles.createButtonText}>Create Savings Account</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading savings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Savings</Text>
        <TouchableOpacity onPress={handleCreateAccount}>
          <Ionicons name="add" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderSummaryCard()}
        {renderQuickActions()}
        
        {accounts.length > 0 ? (
          <View style={styles.accountsSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Your Accounts</Text>
              <TouchableOpacity onPress={() => navigation.navigate('SavingsAccountsList')}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={accounts}
              renderItem={renderAccountCard}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          </View>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 10,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.textSecondary,
  },
  totalAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 20,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  quickActionsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionItem: {
    alignItems: 'center',
    flex: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.colors.text,
    textAlign: 'center',
  },
  accountsSection: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  viewAllText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  accountCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  accountHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  accountIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  accountInfo: {
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  accountType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  accountBalance: {
    marginBottom: 12,
  },
  balanceAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 2,
  },
  interestEarned: {
    fontSize: 12,
    color: theme.colors.success,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressInfo: {
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  progressBar: {
    height: 4,
    backgroundColor: theme.colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  autoTransferBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  autoTransferText: {
    fontSize: 10,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
    marginBottom: 24,
  },
  createButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  createButtonText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SavingsDashboardScreen;
