import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import currencyService from '../services/currencyService';
import { useCurrencyContext } from '../contexts/CurrencyContext';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Currency Selector Component
 * Provides dropdown functionality for selecting app currency
 * Supports East African currencies with real-time conversion display
 */
const CurrencySelector = ({ 
  selectedCurrency = 'UGX',
  onCurrencyChange,
  style = {},
  compact = false,
  showConversion = true
}) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const { updateUserCurrency } = useCurrencyContext();
  const styles = createStyles(theme);
  
  const [isVisible, setIsVisible] = useState(false);
  const [currentCurrency, setCurrentCurrency] = useState(selectedCurrency);
  const [availableCurrencies, setAvailableCurrencies] = useState([]);
  const [exchangeRates, setExchangeRates] = useState({});
  
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  useEffect(() => {
    const initializeCurrencyData = async () => {
      await loadCurrencies();
      await loadExchangeRates();
    };

    initializeCurrencyData();
  }, []);

  useEffect(() => {
    setCurrentCurrency(selectedCurrency);
  }, [selectedCurrency]);

  const loadCurrencies = async () => {
    try {
      // Initialize currency service if not already done
      if (!currencyService.isInitialized) {
        console.log('💱 CurrencySelector: Initializing currency service...');
        await currencyService.initialize();
      }

      // Get only East African currencies
      const eastAfricanCurrencies = currencyService.getEastAfricanCurrencies();

      console.log('🔍 CurrencySelector: Raw currencies from service:', eastAfricanCurrencies);
      setAvailableCurrencies(eastAfricanCurrencies);
      console.log('✅ CurrencySelector: Loaded currencies:', eastAfricanCurrencies.length);
    } catch (error) {
      console.error('❌ Error loading currencies:', error);
      // Fallback to East African currencies only
      setAvailableCurrencies([
        { code: 'UGX', name: 'Ugandan Shilling', symbol: 'UGX', flag: '🇺🇬' },
        { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', flag: '🇰🇪' },
        { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh', flag: '🇹🇿' },
        { code: 'RWF', name: 'Rwandan Franc', symbol: 'RWF', flag: '🇷🇼' },
        { code: 'BIF', name: 'Burundian Franc', symbol: 'BIF', flag: '🇧🇮' },
        { code: 'ETB', name: 'Ethiopian Birr', symbol: 'ETB', flag: '🇪🇹' }
      ]);
    }
  };

  const loadExchangeRates = async () => {
    try {
      // Initialize currency service if not already done
      if (!currencyService.isInitialized) {
        console.log('💱 CurrencySelector: Initializing currency service for exchange rates...');
        await currencyService.initialize();
      }

      const rates = currencyService.getAllExchangeRates();
      setExchangeRates(rates);
      console.log('✅ CurrencySelector: Loaded exchange rates:', Object.keys(rates).length);
    } catch (error) {
      console.error('❌ Error loading exchange rates:', error);
      // Set empty rates as fallback
      setExchangeRates({});
    }
  };

  const openSelector = () => {
    console.log('💱 CurrencySelector: Opening modal with currencies:', availableCurrencies.length);
    console.log('💱 CurrencySelector: Available currencies:', availableCurrencies);
    setIsVisible(true);
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSelector = () => {
    Animated.timing(slideAnim, {
      toValue: screenHeight,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  const handleCurrencySelect = async (currency) => {
    try {
      setCurrentCurrency(currency.code);

      // Update currency through context (this will trigger global updates)
      await updateUserCurrency(currency.code);

      // Call parent callback if provided
      onCurrencyChange && onCurrencyChange(currency.code, currency);
      closeSelector();

      // Show success message
      Alert.alert(
        'Success',
        `Currency updated to ${currency.name}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error selecting currency:', error);
      Alert.alert(
        'Error',
        'Failed to update currency. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const getConversionRate = (fromCurrency, toCurrency) => {
    if (fromCurrency === toCurrency) return '1:1';

    try {
      // Check if service is initialized
      if (!currencyService.isInitialized) {
        return 'Loading...';
      }

      const rate = currencyService.getExchangeRateBetween(fromCurrency, toCurrency);
      return rate ? `1:${rate.toFixed(4)}` : 'N/A';
    } catch (error) {
      console.error('❌ Error getting conversion rate:', error);
      return 'N/A';
    }
  };

  const renderCurrencyItem = ({ item }) => {
    console.log('💱 CurrencySelector: Rendering currency item:', item);
    const isSelected = currentCurrency === item.code;
    const conversionRate = showConversion ? getConversionRate('UGX', item.code) : null;

    return (
      <TouchableOpacity
        style={[
          styles.currencyItem,
          isSelected && styles.selectedCurrencyItem
        ]}
        onPress={() => handleCurrencySelect(item)}
      >
        <View style={styles.currencyItemContent}>
          <View style={styles.currencyMainInfo}>
            <Text style={styles.currencyFlag}>{item.flag}</Text>
            <View style={styles.currencyInfo}>
              <Text style={styles.currencyCode}>{item.code}</Text>
              <Text style={styles.currencyName}>{item.name}</Text>
            </View>
          </View>

          <View style={styles.currencyRightInfo}>
            {showConversion && conversionRate && (
              <Text style={styles.conversionRate}>{conversionRate}</Text>
            )}
            {isSelected && (
              <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const currentCurrencyInfo = availableCurrencies.find(c => c.code === currentCurrency) || 
    { code: currentCurrency, name: currentCurrency, flag: '💱', symbol: currentCurrency };

  if (compact) {
    return (
      <TouchableOpacity
        style={[styles.compactSelector, style]}
        onPress={openSelector}
      >
        <Text style={styles.compactFlag}>{currentCurrencyInfo.flag}</Text>
        <Text style={styles.compactCode}>{currentCurrency}</Text>
        <Ionicons name="chevron-down" size={16} color={theme.colors.textSecondary} />
        
        <Modal
          visible={isVisible}
          transparent={true}
          animationType="none"
          onRequestClose={closeSelector}
        >
          <View style={styles.modalOverlay}>
            <TouchableOpacity 
              style={styles.modalBackground} 
              onPress={closeSelector}
              activeOpacity={1}
            />
            <Animated.View 
              style={[
                styles.modalContent,
                { transform: [{ translateY: slideAnim }] }
              ]}
            >
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Currency</Text>
                <TouchableOpacity onPress={closeSelector}>
                  <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
                </TouchableOpacity>
              </View>
              
              <FlatList
                data={availableCurrencies}
                renderItem={renderCurrencyItem}
                keyExtractor={(item) => item.code}
                style={styles.currencyList}
                showsVerticalScrollIndicator={false}
                onLayout={() => console.log('💱 CurrencySelector: FlatList layout complete')}
                ListEmptyComponent={() => {
                  console.log('💱 CurrencySelector: FlatList is empty, data length:', availableCurrencies.length);
                  return (
                    <View style={{ padding: 20, alignItems: 'center' }}>
                      <Text style={{ color: theme.colors.textSecondary }}>
                        {availableCurrencies.length === 0 ? 'Loading currencies...' : 'No currencies available'}
                      </Text>
                    </View>
                  );
                }}
              />
            </Animated.View>
          </View>
        </Modal>
      </TouchableOpacity>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={openSelector}
      >
        <View style={styles.selectedCurrencyContent}>
          <Text style={styles.selectedFlag}>{currentCurrencyInfo.flag}</Text>
          <View style={styles.selectedCurrencyInfo}>
            <Text style={styles.selectedCurrencyCode}>{currentCurrency}</Text>
            <Text style={styles.selectedCurrencyName}>{currentCurrencyInfo.name}</Text>
          </View>
        </View>
        <Ionicons name="chevron-down" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeSelector}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackground} 
            onPress={closeSelector}
            activeOpacity={1}
          />
          <Animated.View
            style={[
              styles.modalContent,
              { transform: [{ translateY: slideAnim }] }
            ]}
            onLayout={(event) => {
              const { height, width } = event.nativeEvent.layout;
              console.log('💱 CurrencySelector: Modal content layout - Height:', height, 'Width:', width);
            }}
          >
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Currency</Text>
              <TouchableOpacity onPress={closeSelector}>
                <Ionicons name="close" size={24} color={theme.colors.textPrimary} />
              </TouchableOpacity>
            </View>
            
            {showConversion && (
              <View style={styles.conversionHeader}>
                <Text style={styles.conversionHeaderText}>
                  Exchange Rate (UGX:Currency)
                </Text>
              </View>
            )}
            
            <FlatList
              data={availableCurrencies}
              renderItem={renderCurrencyItem}
              keyExtractor={(item) => item.code}
              style={styles.currencyList}
              showsVerticalScrollIndicator={false}
              onLayout={(event) => {
                const { height, width } = event.nativeEvent.layout;
                console.log('💱 CurrencySelector: Full FlatList layout - Height:', height, 'Width:', width);
              }}
              ListEmptyComponent={() => {
                console.log('💱 CurrencySelector: Full FlatList is empty, data length:', availableCurrencies.length);
                return (
                  <View style={{ padding: 20, alignItems: 'center' }}>
                    <Text style={{ color: theme.colors.textSecondary }}>
                      {availableCurrencies.length === 0 ? 'Loading currencies...' : 'No currencies available'}
                    </Text>
                  </View>
                );
              }}
              ListHeaderComponent={() => {
                console.log('💱 CurrencySelector: FlatList header rendering, data count:', availableCurrencies.length);
                return null;
              }}
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    width: '100%',
  },
  compactSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  compactFlag: {
    fontSize: 16,
    marginRight: 6,
  },
  compactCode: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textPrimary,
    marginRight: 4,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  selectedCurrencyContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  selectedCurrencyInfo: {
    flex: 1,
  },
  selectedCurrencyCode: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  selectedCurrencyName: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalBackground: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.8,
    minHeight: screenHeight * 0.4, // Ensure minimum height
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  conversionHeader: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: theme.colors.surface,
  },
  conversionHeaderText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  currencyList: {
    flex: 1,
    minHeight: 200, // Ensure minimum height for FlatList
  },
  currencyItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  selectedCurrencyItem: {
    backgroundColor: theme.colors.primaryLight,
  },
  currencyItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  currencyMainInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  currencyFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  currencyInfo: {
    flex: 1,
  },
  currencyCode: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textPrimary,
  },
  currencyName: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  currencyRightInfo: {
    alignItems: 'flex-end',
  },
  conversionRate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
});

export default CurrencySelector;
