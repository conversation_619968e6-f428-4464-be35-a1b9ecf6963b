/**
 * Predictive Budgeting Testing Script
 * Comprehensive testing for predictive analytics and budget management
 */

import predictiveAnalyticsService from '../services/predictiveAnalyticsService';
import budgetManagementService from '../services/budgetManagementService';
import enhancedAnalyticsService from '../services/enhancedAnalyticsService';

class PredictiveBudgetingTester {
  constructor() {
    this.testResults = [];
    this.testUserId = 'test-user-id'; // Replace with actual test user ID
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Predictive Budgeting Tests...\n');

    try {
      await this.testPredictiveAnalytics();
      await this.testBudgetManagement();
      await this.testEnhancedDashboardIntegration();
      await this.testRealTimeMonitoring();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Test predictive analytics service
   */
  async testPredictiveAnalytics() {
    console.log('📊 Testing Predictive Analytics Service...');

    // Test 1: Service initialization
    await this.runTest('Predictive Analytics Initialization', async () => {
      const result = await predictiveAnalyticsService.initialize(this.testUserId);
      return result.success;
    });

    // Test 2: Spending forecast generation
    await this.runTest('Spending Forecast Generation', async () => {
      const result = await predictiveAnalyticsService.generateSpendingForecast(
        this.testUserId, 
        'month', 
        3
      );
      return result.success && Array.isArray(result.data?.forecasts);
    });

    // Test 3: Budget suggestions
    await this.runTest('Budget Suggestions Generation', async () => {
      const result = await predictiveAnalyticsService.generateBudgetSuggestions(
        this.testUserId, 
        0.2
      );
      return result.success && Array.isArray(result.data?.suggestedBudgets);
    });

    // Test 4: Pattern analysis
    await this.runTest('Spending Pattern Analysis', async () => {
      const mockTransactions = [
        { amount: -50000, category: 'food_dining', created_at: '2024-01-15' },
        { amount: -30000, category: 'transportation', created_at: '2024-01-20' },
        { amount: -100000, category: 'bills_utilities', created_at: '2024-02-01' }
      ];
      
      const patterns = predictiveAnalyticsService.analyzeSpendingPatterns(mockTransactions);
      return patterns && typeof patterns.averageMonthlySpending === 'number';
    });

    console.log('✅ Predictive Analytics tests completed\n');
  }

  /**
   * Test budget management service
   */
  async testBudgetManagement() {
    console.log('💰 Testing Budget Management Service...');

    // Test 1: Service initialization
    await this.runTest('Budget Management Initialization', async () => {
      const result = await budgetManagementService.initialize(this.testUserId);
      return result.success;
    });

    // Test 2: Budget creation
    let createdBudgetId = null;
    await this.runTest('Budget Creation', async () => {
      const budgetData = {
        name: 'Test Budget',
        description: 'Test budget for validation',
        budgetType: 'monthly',
        totalAmount: 2000000,
        startDate: new Date().toISOString().split('T')[0],
        useAISuggestions: true,
        targetSavingsRate: 0.2
      };

      const result = await budgetManagementService.createBudget(this.testUserId, budgetData);
      if (result.success) {
        createdBudgetId = result.data?.budget?.id;
      }
      return result.success;
    });

    // Test 3: Get user budgets
    await this.runTest('Get User Budgets', async () => {
      const result = await budgetManagementService.getUserBudgets(this.testUserId);
      return result.success && Array.isArray(result.data);
    });

    // Test 4: Budget analytics
    await this.runTest('Budget Analytics', async () => {
      const result = await budgetManagementService.getBudgetAnalytics(this.testUserId);
      return result.success && typeof result.data?.totalBudgets === 'number';
    });

    // Test 5: Budget recommendations
    await this.runTest('Budget Recommendations', async () => {
      const result = await budgetManagementService.getBudgetRecommendations(this.testUserId);
      return result.success && Array.isArray(result.data);
    });

    // Test 6: Real-time budget status
    await this.runTest('Real-time Budget Status', async () => {
      const result = await budgetManagementService.getRealTimeBudgetStatus(this.testUserId);
      return result.success && typeof result.data?.totalBudgets === 'number';
    });

    console.log('✅ Budget Management tests completed\n');
  }

  /**
   * Test enhanced dashboard integration
   */
  async testEnhancedDashboardIntegration() {
    console.log('📈 Testing Enhanced Dashboard Integration...');

    // Test 1: Dashboard analytics with predictive data
    await this.runTest('Dashboard Analytics Integration', async () => {
      const result = await enhancedAnalyticsService.getDashboardAnalytics(
        this.testUserId,
        'month'
      );
      
      return result && 
             result.predictiveForecasts !== undefined &&
             result.budgetAnalytics !== undefined &&
             Array.isArray(result.budgetRecommendations);
    });

    // Test 2: Batch query performance
    await this.runTest('Batch Query Performance', async () => {
      const startTime = Date.now();
      
      await enhancedAnalyticsService.getDashboardAnalytics(
        this.testUserId,
        'month'
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 5 seconds
      return duration < 5000;
    });

    console.log('✅ Enhanced Dashboard Integration tests completed\n');
  }

  /**
   * Test real-time monitoring
   */
  async testRealTimeMonitoring() {
    console.log('⚡ Testing Real-time Monitoring...');

    // Test 1: Budget subscription setup
    await this.runTest('Budget Update Subscription', async () => {
      const subscription = budgetManagementService.subscribeToBudgetUpdates(
        this.testUserId,
        (update) => {
          console.log('📊 Received budget update:', update.type);
        }
      );
      
      // Cleanup
      if (subscription.success && subscription.unsubscribe) {
        subscription.unsubscribe();
      }
      
      return subscription.success;
    });

    // Test 2: Budget monitoring start/stop
    await this.runTest('Budget Monitoring Lifecycle', async () => {
      const startResult = budgetManagementService.startBudgetMonitoring(this.testUserId);
      
      if (startResult.success) {
        budgetManagementService.stopBudgetMonitoring(this.testUserId);
        return true;
      }
      
      return false;
    });

    console.log('✅ Real-time Monitoring tests completed\n');
  }

  /**
   * Run individual test
   */
  async runTest(testName, testFunction) {
    try {
      console.log(`  🔍 Running: ${testName}`);
      const startTime = Date.now();
      
      const result = await testFunction();
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (result) {
        console.log(`  ✅ PASS: ${testName} (${duration}ms)`);
        this.testResults.push({
          name: testName,
          status: 'PASS',
          duration,
          error: null
        });
      } else {
        console.log(`  ❌ FAIL: ${testName} (${duration}ms)`);
        this.testResults.push({
          name: testName,
          status: 'FAIL',
          duration,
          error: 'Test returned false'
        });
      }
    } catch (error) {
      console.log(`  ❌ ERROR: ${testName} - ${error.message}`);
      this.testResults.push({
        name: testName,
        status: 'ERROR',
        duration: 0,
        error: error.message
      });
    }
  }

  /**
   * Print test results summary
   */
  printTestResults() {
    console.log('\n📋 TEST RESULTS SUMMARY');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.status === 'PASS').length;
    const failed = this.testResults.filter(r => r.status === 'FAIL').length;
    const errors = this.testResults.filter(r => r.status === 'ERROR').length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`🚨 Errors: ${errors}`);
    console.log(`📊 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    if (failed > 0 || errors > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults
        .filter(r => r.status !== 'PASS')
        .forEach(test => {
          console.log(`  - ${test.name}: ${test.error || 'Failed'}`);
        });
    }
    
    const avgDuration = this.testResults
      .filter(r => r.duration > 0)
      .reduce((sum, r) => sum + r.duration, 0) / this.testResults.length;
    
    console.log(`\n⏱️  Average Test Duration: ${avgDuration.toFixed(0)}ms`);
    
    if (passed === total) {
      console.log('\n🎉 ALL TESTS PASSED! Predictive Budgeting implementation is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review and fix issues before deployment.');
    }
  }

  /**
   * Test data validation
   */
  async testDataValidation() {
    console.log('🔍 Testing Data Validation...');

    // Test forecast data structure
    await this.runTest('Forecast Data Structure', async () => {
      const result = await predictiveAnalyticsService.generateSpendingForecast(
        this.testUserId, 
        'month', 
        3
      );
      
      if (!result.success) return false;
      
      const forecast = result.data.forecasts?.[0];
      return forecast && 
             typeof forecast.predicted === 'number' &&
             typeof forecast.confidence === 'number' &&
             forecast.confidence >= 0 && forecast.confidence <= 1;
    });

    // Test budget data structure
    await this.runTest('Budget Data Structure', async () => {
      const result = await budgetManagementService.getUserBudgets(this.testUserId);
      
      if (!result.success || !result.data.length) return true; // No budgets is valid
      
      const budget = result.data[0];
      return budget &&
             typeof budget.total_amount === 'number' &&
             budget.total_amount > 0 &&
             Array.isArray(budget.budget_categories);
    });

    console.log('✅ Data Validation tests completed\n');
  }
}

// Export for use in testing
export default PredictiveBudgetingTester;

// Run tests if called directly
if (require.main === module) {
  const tester = new PredictiveBudgetingTester();
  tester.runAllTests().catch(console.error);
}
