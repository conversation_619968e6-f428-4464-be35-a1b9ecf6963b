#!/usr/bin/env node

/**
 * JiraniPay Startup Script with Environment Configuration
 *
 * This script properly configures the environment and starts JiraniPay
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get environment from command line argument or default to production
const environment = process.argv[2] || 'production';

console.log(`🚀 Starting JiraniPay in ${environment} mode...\n`);

// Simple environment file parser
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) return;

  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
      }
    }
  }
}

// Set environment variables based on the mode
if (environment === 'production') {
  process.env.EXPO_PUBLIC_ENVIRONMENT = 'production';
  process.env.NODE_ENV = 'production';

  // Load production environment file if it exists
  const prodEnvFile = path.join(__dirname, '.env.production.local');
  if (fs.existsSync(prodEnvFile)) {
    console.log('📁 Loading production environment file...');
    loadEnvFile(prodEnvFile);
  }
} else if (environment === 'development') {
  process.env.EXPO_PUBLIC_ENVIRONMENT = 'development';
  process.env.NODE_ENV = 'development';

  // Load development environment file if it exists
  const devEnvFile = path.join(__dirname, '.env.development.local');
  if (fs.existsSync(devEnvFile)) {
    console.log('📁 Loading development environment file...');
    loadEnvFile(devEnvFile);
  }
}

console.log('🔧 Environment configured:', {
  EXPO_PUBLIC_ENVIRONMENT: process.env.EXPO_PUBLIC_ENVIRONMENT,
  NODE_ENV: process.env.NODE_ENV
});

let backendProcess = null;

// Function to start backend
function startBackend() {
  return new Promise((resolve, reject) => {
    console.log('🔧 Starting backend configuration service...');
    
    const backendPath = path.join(__dirname, 'backend');
    const startScript = path.join(backendPath, 'start-dev.js');
    
    if (!fs.existsSync(startScript)) {
      reject(new Error('Backend start script not found'));
      return;
    }
    
    backendProcess = spawn('node', [startScript], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    
    backendProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      process.stdout.write(text);
      
      // Check if server is ready
      if (text.includes('Server started') || text.includes('listening on')) {
        console.log('✅ Backend configuration service started successfully\n');
        resolve();
      }
    });
    
    backendProcess.stderr.on('data', (data) => {
      const text = data.toString();
      process.stderr.write(text);
    });
    
    backendProcess.on('error', (error) => {
      reject(error);
    });
    
    backendProcess.on('exit', (code) => {
      if (code !== 0) {
        reject(new Error(`Backend process exited with code ${code}`));
      }
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (backendProcess && !backendProcess.killed) {
        console.log('✅ Backend service is starting (may take a moment)...\n');
        resolve();
      }
    }, 30000);
  });
}

// Function to test configuration service
async function testConfigurationService() {
  console.log('🧪 Testing configuration service...');
  
  // Wait a moment for server to be fully ready
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  try {
    const response = await fetch('http://localhost:3001/api/v1/config/health');
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Configuration service is responding');
      console.log(`📊 Status: ${data.status}`);
      console.log(`⏱️ Uptime: ${Math.round(data.uptime)}s\n`);
      return true;
    } else {
      console.log('❌ Configuration service health check failed\n');
      return false;
    }
  } catch (error) {
    console.log('❌ Configuration service not responding:', error.message);
    console.log('⏳ Waiting for service to start...\n');
    
    // Try again after a delay
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    try {
      const response = await fetch('http://localhost:3001/api/v1/config/health');
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Configuration service is now responding\n');
        return true;
      }
    } catch (retryError) {
      console.log('❌ Configuration service still not responding\n');
    }
    
    return false;
  }
}

// Function to test mobile app integration
async function testMobileAppIntegration() {
  console.log('📱 Testing mobile app integration...');
  
  try {
    // Test secure configuration service
    console.log('🔐 Testing secure configuration service...');
    
    const testScript = path.join(__dirname, 'test-config-service.js');
    
    if (fs.existsSync(testScript)) {
      return new Promise((resolve) => {
        const testProcess = spawn('node', [testScript], {
          cwd: __dirname,
          stdio: 'inherit'
        });
        
        testProcess.on('exit', (code) => {
          if (code === 0) {
            console.log('\n✅ Mobile app integration test passed\n');
            resolve(true);
          } else {
            console.log('\n⚠️ Mobile app integration test completed with warnings\n');
            resolve(true); // Continue anyway
          }
        });
        
        testProcess.on('error', (error) => {
          console.log('\n❌ Mobile app integration test failed:', error.message);
          resolve(false);
        });
      });
    } else {
      console.log('⚠️ Integration test script not found, skipping...\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Mobile app integration test error:', error.message);
    return false;
  }
}

// Function to provide next steps
function showNextSteps() {
  console.log('🎯 JIRANIPAY SYSTEM READY!\n');
  console.log('=' .repeat(50));
  console.log('✅ Backend configuration service: Running on port 3001');
  console.log('✅ Security features: Enabled');
  console.log('✅ Configuration endpoints: Available');
  console.log('✅ Mobile app integration: Ready');
  console.log('=' .repeat(50));
  
  console.log('\n🚀 STARTING EXPO APP...');
  console.log(`Environment: ${environment}`);
  console.log(`EXPO_PUBLIC_ENVIRONMENT: ${process.env.EXPO_PUBLIC_ENVIRONMENT}`);

  // Start Expo with the configured environment
  const expoProcess = spawn('expo', ['start'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      EXPO_PUBLIC_ENVIRONMENT: process.env.EXPO_PUBLIC_ENVIRONMENT,
      NODE_ENV: process.env.NODE_ENV
    }
  });

  console.log('\n📋 JIRANIPAY IS STARTING:');
  console.log('1. Backend configuration service: ✅ RUNNING');
  console.log('2. Expo development server: 🚀 STARTING...');
  console.log('');
  console.log('3. Expected behavior:');
  console.log('   - Login screen should NOT show development mode');
  console.log('   - User greeting should show actual username');
  console.log('   - Wallet functionality should work normally');
  console.log('');
  console.log('🔧 TROUBLESHOOTING:');
  console.log('- Backend logs: Check the output above');
  console.log('- Configuration test: node test-config-service.js');
  console.log('- Security test: node test-security-implementation.js');
  console.log('- Stop backend: Ctrl+C');
  console.log('');
  console.log('🌐 ENDPOINTS:');
  console.log('- Health: http://localhost:3001/api/v1/config/health');
  console.log('- Config: http://localhost:3001/api/v1/config/');
  console.log('');
  console.log('Press Ctrl+C to stop the backend service.');
}

// Main startup function
async function startJiraniPay() {
  try {
    // Step 1: Start backend
    await startBackend();
    
    // Step 2: Test configuration service
    const configWorking = await testConfigurationService();
    
    if (!configWorking) {
      console.log('❌ Configuration service failed to start properly');
      console.log('💡 Try running manually: cd backend && node start-dev.js');
      process.exit(1);
    }
    
    // Step 3: Test mobile app integration
    await testMobileAppIntegration();
    
    // Step 4: Show next steps
    showNextSteps();
    
  } catch (error) {
    console.error('❌ Failed to start JiraniPay system:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure Node.js is installed (v16+)');
    console.error('2. Check that port 3001 is available');
    console.error('3. Verify all files are present');
    console.error('4. Try running: cd backend && npm install');
    
    if (backendProcess) {
      backendProcess.kill();
    }
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down JiraniPay system...');
  
  if (backendProcess) {
    backendProcess.kill();
    console.log('✅ Backend service stopped');
  }
  
  console.log('👋 Goodbye!');
  process.exit(0);
});

process.on('SIGTERM', () => {
  if (backendProcess) {
    backendProcess.kill();
  }
  process.exit(0);
});

// Start the system
startJiraniPay();
