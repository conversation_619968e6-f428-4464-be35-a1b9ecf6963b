/**
 * Digital Receipts Screen
 * Displays user's digital receipts with search, filter, and export capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
  TextInput,
  Share
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import digitalReceiptService from '../services/digitalReceiptService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const DigitalReceiptsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [receipts, setReceipts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [pagination, setPagination] = useState({ limit: 20, offset: 0, hasMore: true });

  useEffect(() => {
    loadReceipts();
  }, [filterType]);

  useEffect(() => {
    if (searchQuery.length > 2 || searchQuery.length === 0) {
      searchReceipts();
    }
  }, [searchQuery]);

  const loadReceipts = async (reset = true) => {
    try {
      if (reset) {
        setLoading(true);
        setPagination({ limit: 20, offset: 0, hasMore: true });
      }

      const userId = 'current-user-id'; // Replace with actual user ID from auth
      const options = {
        limit: pagination.limit,
        offset: reset ? 0 : pagination.offset,
        type: filterType === 'all' ? null : filterType
      };

      const result = await digitalReceiptService.getUserReceipts(userId, options);
      
      if (reset) {
        setReceipts(result.receipts);
      } else {
        setReceipts(prev => [...prev, ...result.receipts]);
      }

      setPagination(prev => ({
        ...prev,
        offset: reset ? result.receipts.length : prev.offset + result.receipts.length,
        hasMore: result.pagination.hasMore
      }));

    } catch (error) {
      console.error('❌ Error loading receipts:', error);
      Alert.alert('Error', 'Failed to load receipts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const searchReceipts = async () => {
    if (searchQuery.length === 0) {
      loadReceipts();
      return;
    }

    try {
      setLoading(true);
      const userId = 'current-user-id';
      const result = await digitalReceiptService.searchReceipts(userId, searchQuery);
      setReceipts(result.receipts);
      setPagination({ limit: 20, offset: result.receipts.length, hasMore: result.pagination.hasMore });
    } catch (error) {
      console.error('❌ Error searching receipts:', error);
      Alert.alert('Error', 'Failed to search receipts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReceipts();
    setRefreshing(false);
  };

  const loadMore = () => {
    if (!loading && pagination.hasMore) {
      loadReceipts(false);
    }
  };

  const handleReceiptPress = (receipt) => {
    navigation.navigate('ReceiptDetails', { receiptId: receipt.id });
  };

  const handleShareReceipt = async (receipt) => {
    try {
      const userId = 'current-user-id';
      const shareResult = await digitalReceiptService.shareReceipt(receipt.id, userId, 'link');
      
      await Share.share({
        message: shareResult.message,
        url: shareResult.shareUrl,
        title: 'JiraniPay Receipt'
      });
    } catch (error) {
      console.error('❌ Error sharing receipt:', error);
      Alert.alert('Error', 'Failed to share receipt. Please try again.');
    }
  };

  const getReceiptIcon = (type) => {
    const icons = {
      money_transfer: 'arrow-forward-circle',
      money_received: 'arrow-down-circle',
      bill_payment: 'receipt',
      airtime_purchase: 'phone-portrait',
      withdrawal: 'card',
      deposit: 'wallet'
    };
    return icons[type] || 'document-text';
  };

  const getReceiptColor = (type) => {
    const colors = {
      money_transfer: theme.colors.primary,
      money_received: theme.colors.success,
      bill_payment: theme.colors.warning,
      airtime_purchase: theme.colors.info,
      withdrawal: theme.colors.error,
      deposit: theme.colors.success
    };
    return colors[type] || theme.colors.textSecondary;
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {['all', 'money_transfer', 'bill_payment', 'airtime_purchase'].map((filter) => (
        <TouchableOpacity
          key={filter}
          style={[
            styles.filterButton,
            filterType === filter && styles.filterButtonActive
          ]}
          onPress={() => setFilterType(filter)}
        >
          <Text style={[
            styles.filterButtonText,
            filterType === filter && styles.filterButtonTextActive
          ]}>
            {filter === 'all' ? 'All' : filter.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderReceiptItem = ({ item: receipt }) => (
    <TouchableOpacity 
      style={styles.receiptCard}
      onPress={() => handleReceiptPress(receipt)}
    >
      <View style={styles.receiptHeader}>
        <View style={styles.receiptIconContainer}>
          <Ionicons 
            name={getReceiptIcon(receipt.type)} 
            size={24} 
            color={getReceiptColor(receipt.type)} 
          />
        </View>
        
        <View style={styles.receiptInfo}>
          <Text style={styles.receiptTitle} numberOfLines={1}>
            {receipt.title || receipt.type?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Text>
          <Text style={styles.receiptNumber}>#{receipt.receiptNumber}</Text>
        </View>
        
        <View style={styles.receiptAmount}>
          <Text style={styles.amountText}>
            {formatCurrency(receipt.amount, receipt.currency)}
          </Text>
          <Text style={styles.receiptDate}>
            {formatDate(receipt.createdAt)}
          </Text>
        </View>
      </View>

      <View style={styles.receiptActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('ReceiptDetails', { receiptId: receipt.id })}
        >
          <Ionicons name="eye" size={16} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>View</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => handleShareReceipt(receipt)}
        >
          <Ionicons name="share" size={16} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Share</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('ReceiptDetails', { 
            receiptId: receipt.id, 
            action: 'download' 
          })}
        >
          <Ionicons name="download" size={16} color={theme.colors.primary} />
          <Text style={styles.actionButtonText}>Download</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="receipt-outline" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Receipts Found</Text>
      <Text style={styles.emptyDescription}>
        {searchQuery 
          ? `No receipts match "${searchQuery}"`
          : filterType === 'all'
          ? 'Your transaction receipts will appear here'
          : `No ${filterType.replace('_', ' ')} receipts found`
        }
      </Text>
    </View>
  );

  const renderLoadingFooter = () => {
    if (!loading || receipts.length === 0) return null;
    
    return (
      <View style={styles.loadingFooter}>
        <Text style={styles.loadingText}>Loading more receipts...</Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Digital Receipts</Text>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.navigate('ReceiptSettings')}
        >
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search receipts..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filter Buttons */}
      {renderFilterButtons()}

      {/* Receipts List */}
      <FlatList
        data={receipts}
        renderItem={renderReceiptItem}
        keyExtractor={(item) => item.id}
        style={styles.receiptsList}
        contentContainerStyle={receipts.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        ListFooterComponent={renderLoadingFooter}
        onEndReached={loadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('ReceiptScanner')}
        >
          <Ionicons name="qr-code-outline" size={20} color={theme.colors.white} />
          <Text style={styles.quickActionText}>Scan Receipt</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('ReceiptExport')}
        >
          <Ionicons name="download-outline" size={20} color={theme.colors.white} />
          <Text style={styles.quickActionText}>Export All</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    padding: 5,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingHorizontal: 15,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 10,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 15,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  receiptsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  receiptCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  receiptHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  receiptIconContainer: {
    marginRight: 12,
  },
  receiptInfo: {
    flex: 1,
  },
  receiptTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  receiptNumber: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  receiptAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  receiptDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  receiptActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  actionButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
  loadingFooter: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 5,
  },
  quickActionText: {
    color: theme.colors.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default DigitalReceiptsScreen;
