import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabaseClient';
import enhancedAIKnowledgeBase from './enhancedAIKnowledgeBase';
import codebaseContextService from './codebaseContextService';
import authService from './authService';
import walletService from './walletService';
import profileManagementService from './profileManagementService';

class AIChatService {
  constructor() {
    this.chatHistory = [];
    this.conversationId = null;
    this.isTyping = false;
    this.ugandaFinanceKnowledge = this.initializeKnowledgeBase();
  }

  // =====================================================
  // UGANDA-SPECIFIC FINANCIAL KNOWLEDGE BASE
  // =====================================================

  initializeKnowledgeBase() {
    return {
      // Mobile Money Providers
      mobileMoney: {
        mtn: { name: 'MTN Mobile Money', code: '*165#', prefix: '077, 078' },
        airtel: { name: 'Airtel Money', code: '*185#', prefix: '070, 075' },
        utl: { name: 'UTL Money', code: '*155#', prefix: '071' }
      },

      // Common Ugandan Banking Terms (Multi-language)
      bankingTerms: {
        'mobile money': {
          en: 'Digital payment service using mobile phones',
          sw: 'Huduma ya malipo ya kidijitali kwa kutumia simu za mkononi',
          lg: 'Enkola y\'okusasula ng\'okozesa essimu'
        },
        'airtime': {
          en: 'Phone credit for calls and SMS',
          sw: 'Mkopo wa simu kwa kupiga simu na SMS',
          lg: 'Ssente z\'essimu okukozesa okukuba n\'okuweereza obubaka'
        },
        'data bundles': {
          en: 'Internet packages for mobile phones',
          sw: 'Vifurushi vya mtandao kwa simu za mkononi',
          lg: 'Ebipimo by\'omutimbagano ku ssimu'
        },
        'boda boda': {
          en: 'Motorcycle taxi payment',
          sw: 'Malipo ya pikipiki',
          lg: 'Okusasula boda boda'
        },
        'sente': {
          en: 'Money in Luganda',
          sw: 'Pesa katika lugha ya Luganda',
          lg: 'Ssente mu Luganda'
        }
      },

      // Multi-language responses
      responses: {
        welcome: {
          en: 'Hello! I\'m your JiraniPay AI assistant. I can help you with transactions, account questions, and Uganda mobile money services. How can I assist you today?',
          sw: 'Hujambo! Mimi ni msaidizi wako wa JiraniPay AI. Ninaweza kukusaidia na miamala, maswali ya akaunti, na huduma za pesa za simu za Uganda. Ninawezaje kukusaidia leo?',
          lg: 'Oli otya! Nze omuyambi wo wa JiraniPay AI. Nsobola okukuyamba ku nsimbi, ebibuuzo ku akawunti, ne ku mpeereza za ssente za ssimu mu Uganda. Nkuyinza ntya okukuyamba leero?'
        },
        thanks: {
          en: 'You\'re welcome! Is there anything else I can help you with today?',
          sw: 'Karibu sana! Je, kuna kitu kingine ninachoweza kukusaidia nacho leo?',
          lg: 'Tewali kye nkola! Waliwo ekirala kye nsobola okukuyambamu leero?'
        }
      },

      // Transaction Limits (UGX)
      limits: {
        basic: { daily: 500000, monthly: 2000000 },
        verified: { daily: 2000000, monthly: ******** },
        premium: { daily: ********, monthly: ******** }
      },

      // Common Issues & Solutions
      commonIssues: {
        'failed transaction': 'Check network connection, verify recipient details, ensure sufficient balance',
        'pin forgotten': 'Use PIN recovery in Security Settings or contact support',
        'account locked': 'Contact support immediately at +************',
        'verification needed': 'Complete KYC in Profile → Account Verification'
      }
    };
  }

  // =====================================================
  // QUICK RESPONSES FOR COMMON QUERIES
  // =====================================================

  getQuickResponses() {
    return [
      {
        id: 'balance',
        text: 'Check my balance',
        category: 'wallet',
        response: 'To check your balance: Go to the main dashboard and tap on your wallet card. Your current balance will be displayed prominently. You can also toggle the visibility using the eye icon.'
      },
      {
        id: 'send_money',
        text: 'How to send money',
        category: 'transactions',
        response: 'To send money: 1) Tap "Send Money" on the dashboard, 2) Enter recipient\'s phone number or scan QR code, 3) Enter amount in UGX, 4) Add optional note, 5) Confirm with PIN. Money transfers instantly to other JiraniPay users.'
      },
      {
        id: 'pay_bills',
        text: 'Pay bills',
        category: 'bills',
        response: 'To pay bills: 1) Go to "Pay Bills" section, 2) Select service (UMEME, Water, Airtime, etc.), 3) Enter account/meter number, 4) Enter amount, 5) Confirm payment with PIN. Bills are paid instantly.'
      },
      {
        id: 'security',
        text: 'Security help',
        category: 'security',
        response: 'For security: Enable biometric login, use strong PIN, never share credentials, monitor transactions regularly. For emergencies, call our security hotline: +************'
      },
      {
        id: 'limits',
        text: 'Transaction limits',
        category: 'limits',
        response: 'Daily limits: Basic (UGX 500,000), Verified (UGX 2,000,000), Premium (UGX 10,000,000). Increase limits by completing account verification in Profile settings.'
      },
      {
        id: 'support',
        text: 'Contact support',
        category: 'support',
        response: 'Contact us: Phone: +************ (24/7), Email: <EMAIL>, or use this chat. We\'re here to help!'
      },
      // Quick Actions Response Handlers
      {
        id: 'transaction_history',
        text: 'Transaction history',
        category: 'wallet',
        response: '📊 **Transaction History**\n\n**How to view your transactions:**\n1️⃣ Go to your Dashboard\n2️⃣ Scroll down to "Recent Transactions"\n3️⃣ Tap "View All" for complete history\n4️⃣ Use filters to find specific transactions\n\n**Features:**\n• Filter by date, amount, or type\n• Search by recipient or merchant\n• Export transaction reports\n• View transaction details and receipts\n\n💡 **Tip:** Tap any transaction for detailed information!'
      },
      {
        id: 'top_up_wallet',
        text: 'Top up wallet',
        category: 'wallet',
        response: '⬆️ **Top Up Your Wallet**\n\n**Available Methods:**\n💳 **Bank Transfer** - Direct from your bank account\n📱 **Mobile Money** - MTN Money, Airtel Money\n🏪 **Agent Locations** - Cash deposits at agents\n💰 **Debit/Credit Card** - Instant top-up\n\n**How to top up:**\n1️⃣ Tap "Top Up" on your wallet card\n2️⃣ Choose your preferred method\n3️⃣ Enter amount (min UGX 1,000)\n4️⃣ Follow payment instructions\n5️⃣ Funds reflect instantly\n\n🔒 **Secure & Fast** - All transactions are encrypted!'
      },
      {
        id: 'wallet_settings',
        text: 'Wallet settings',
        category: 'wallet',
        response: '⚙️ **Wallet Settings**\n\n**Available Settings:**\n🔒 **Security Settings** - PIN, biometrics, alerts\n💰 **Spending Limits** - Daily/monthly transaction limits\n🔔 **Notifications** - Transaction alerts, balance updates\n💎 **Savings Goals** - Automatic savings setup\n📊 **Privacy** - Balance visibility, transaction sharing\n\n**How to access:**\n1️⃣ Go to Profile → Wallet Settings\n2️⃣ Or tap the settings icon on your wallet card\n\n🛡️ **Tip:** Enable transaction alerts for better security!'
      },
      {
        id: 'qr_scanner_guide',
        text: 'QR scanner guide',
        category: 'qr',
        response: '📱 **QR Scanner Guide**\n\n**How to use QR Scanner:**\n1️⃣ Tap the QR icon on your dashboard\n2️⃣ Point camera at the QR code\n3️⃣ Wait for automatic detection\n4️⃣ Confirm payment details\n5️⃣ Enter PIN to complete\n\n**What you can scan:**\n🏪 **Merchant QR codes** - Pay at shops, restaurants\n👥 **Personal QR codes** - Send money to friends\n📄 **Bill QR codes** - Pay utilities instantly\n\n💡 **Tip:** Look for JiraniPay QR codes at participating merchants!'
      },
      {
        id: 'send_money_guide',
        text: 'Send money guide',
        category: 'transactions',
        response: '📤 **Complete Send Money Guide**\n\n**Step-by-Step Process:**\n1️⃣ Tap "Send Money" on dashboard\n2️⃣ Choose recipient method:\n   • Enter phone number\n   • Select from contacts\n   • Scan QR code\n3️⃣ Enter amount in UGX\n4️⃣ Add optional note/reference\n5️⃣ Review details carefully\n6️⃣ Confirm with PIN\n\n**Transfer Types:**\n💚 **JiraniPay to JiraniPay** - FREE & Instant\n🏦 **To Bank Account** - UGX 1,000 fee\n📱 **To Mobile Money** - UGX 500 fee\n\n🔒 **Secure & Fast** - Money arrives instantly!'
      },
      {
        id: 'pay_bills_guide',
        text: 'Pay bills guide',
        category: 'bills',
        response: '💳 **Complete Bill Payment Guide**\n\n**Supported Services:**\n⚡ **UMEME** - Electricity bills\n💧 **NWSC** - Water bills\n📱 **MTN/Airtel** - Airtime & data\n📺 **DStv/GoTv** - TV subscriptions\n🌐 **Internet** - WiFi providers\n\n**How to pay:**\n1️⃣ Go to "Pay Bills" section\n2️⃣ Select service provider\n3️⃣ Enter account/meter number\n4️⃣ Enter amount or select package\n5️⃣ Confirm with PIN\n\n💡 **Pro Tips:**\n• Save frequently used accounts\n• Set up recurring payments\n• Check for discounts and offers!'
      },
      {
        id: 'umeme_bills',
        text: 'UMEME bills',
        category: 'bills',
        response: '⚡ **UMEME Electricity Bills**\n\n**How to pay UMEME:**\n1️⃣ Go to Pay Bills → UMEME\n2️⃣ Enter your meter number\n3️⃣ Enter amount (minimum UGX 1,000)\n4️⃣ Confirm details\n5️⃣ Pay with PIN\n\n**Payment Options:**\n💰 **Prepaid** - Buy electricity units\n📄 **Postpaid** - Pay monthly bills\n🔄 **Recurring** - Set up automatic payments\n\n**Benefits:**\n✅ Instant token delivery\n✅ 24/7 availability\n✅ No extra charges\n✅ SMS confirmation\n\n💡 **Tip:** Save your meter number for faster payments!'
      },
      {
        id: 'buy_airtime',
        text: 'Buy airtime',
        category: 'bills',
        response: '📱 **Buy Airtime & Data**\n\n**Supported Networks:**\n📶 **MTN** - Airtime & data bundles\n📶 **Airtel** - Airtime & data bundles\n📶 **UTL** - Airtime packages\n\n**How to buy:**\n1️⃣ Go to Pay Bills → Airtime\n2️⃣ Select network (MTN/Airtel/UTL)\n3️⃣ Enter phone number\n4️⃣ Choose amount or data bundle\n5️⃣ Confirm with PIN\n\n**Quick Amounts:**\n• UGX 1,000 • UGX 2,000 • UGX 5,000\n• UGX 10,000 • UGX 20,000 • Custom\n\n✅ **Instant delivery** - Airtime loads immediately!'
      },
      {
        id: 'water_bills',
        text: 'Water bills',
        category: 'bills',
        response: '💧 **Water Bill Payments**\n\n**Supported Providers:**\n🚰 **NWSC** - National Water & Sewerage\n🏘️ **Local Providers** - Regional water companies\n\n**How to pay:**\n1️⃣ Go to Pay Bills → Water\n2️⃣ Select your water provider\n3️⃣ Enter account number\n4️⃣ Enter payment amount\n5️⃣ Confirm with PIN\n\n**Payment Types:**\n📄 **Monthly Bills** - Regular billing\n💰 **Prepaid Water** - Pay in advance\n🔄 **Recurring** - Automatic monthly payments\n\n💡 **Tip:** Set up recurring payments to never miss a bill!'
      },
      // Additional Quick Actions Handlers
      {
        id: 'check_balance',
        text: 'Check balance',
        category: 'wallet',
        response: '💰 **Check Your Balance**\n\n**Current Balance:** Your wallet balance is displayed on the main dashboard.\n\n**How to check:**\n1️⃣ Open JiraniPay app\n2️⃣ Your balance shows on the wallet card\n3️⃣ Tap the eye icon to toggle visibility\n4️⃣ Pull down to refresh\n\n**Balance Details:**\n💵 **Available Balance** - Ready to spend\n⏳ **Pending** - Transactions processing\n💎 **Savings** - Your savings goals\n\n🔒 **Privacy:** Use the eye icon to hide/show your balance in public!'
      },
      {
        id: 'more_bills',
        text: 'More bills',
        category: 'bills',
        response: '📋 **All Available Bill Services**\n\n**Utilities:**\n⚡ UMEME (Electricity)\n💧 NWSC (Water)\n🔥 Gas providers\n\n**Telecommunications:**\n📱 MTN (Airtime/Data)\n📱 Airtel (Airtime/Data)\n📱 UTL (Airtime)\n🌐 Internet providers\n\n**Entertainment:**\n📺 DStv subscriptions\n📺 GoTv packages\n🎵 Music streaming\n\n**Education:**\n🎓 School fees\n📚 University payments\n\n**Government:**\n🏛️ Tax payments\n📄 License renewals\n\n💡 **Coming Soon:** Insurance, loans, and more services!'
      },
      {
        id: 'generate_qr',
        text: 'Generate QR',
        category: 'qr',
        response: '📱 **Generate Your QR Code**\n\n**How to create your QR:**\n1️⃣ Go to Dashboard → QR Code\n2️⃣ Tap "Generate My QR"\n3️⃣ Set payment amount (optional)\n4️⃣ Add description (optional)\n5️⃣ Share or display QR code\n\n**QR Code Uses:**\n💰 **Receive Payments** - Others scan to pay you\n🏪 **Business Payments** - For merchants\n👥 **Split Bills** - Share with friends\n🎉 **Events** - Collect payments easily\n\n**Features:**\n✅ **Instant Generation**\n✅ **Customizable Amounts**\n✅ **Shareable via WhatsApp/SMS**\n✅ **No Expiry**\n\n💡 **Tip:** Save your QR code image for offline use!'
      },
      {
        id: 'merchant_payments',
        text: 'Merchant payments',
        category: 'qr',
        response: '🏪 **Merchant Payments Guide**\n\n**How to pay merchants:**\n1️⃣ Look for JiraniPay QR code at the shop\n2️⃣ Open JiraniPay → Scan QR\n3️⃣ Point camera at merchant QR\n4️⃣ Confirm amount and merchant details\n5️⃣ Enter PIN to complete payment\n\n**Participating Merchants:**\n🍽️ **Restaurants & Cafes**\n🛒 **Supermarkets & Shops**\n⛽ **Fuel Stations**\n🚌 **Transport (Boda, Taxi)**\n🏥 **Pharmacies & Clinics**\n\n**Benefits:**\n✅ **No Cash Needed**\n✅ **Instant Payment**\n✅ **Digital Receipts**\n✅ **Loyalty Points**\n\n💡 **Look for the JiraniPay sticker at participating merchants!**'
      },
      {
        id: 'qr_help',
        text: 'QR help',
        category: 'qr',
        response: '❓ **QR Code Help & Troubleshooting**\n\n**Common Issues:**\n📷 **Camera not working?**\n• Check camera permissions\n• Clean camera lens\n• Ensure good lighting\n\n🔍 **QR not scanning?**\n• Hold phone steady\n• Move closer/further from QR\n• Ensure QR is not damaged\n\n❌ **Payment failed?**\n• Check internet connection\n• Verify sufficient balance\n• Try scanning again\n\n**QR Code Types:**\n💰 **Payment QR** - For making payments\n👤 **Personal QR** - Your receiving QR\n🏪 **Merchant QR** - Business payments\n\n📞 **Still need help?** Contact support: +************'
      },
      // Security Quick Actions
      {
        id: 'security_guide',
        text: 'Security guide',
        category: 'security',
        response: '🔒 **Complete Security Guide**\n\n**Essential Security Features:**\n🔐 **PIN Protection** - 6-digit secure PIN\n👆 **Biometric Login** - Fingerprint/Face ID\n🔔 **Transaction Alerts** - Real-time notifications\n🛡️ **Fraud Monitoring** - 24/7 protection\n⏰ **Auto-logout** - Automatic session timeout\n\n**Security Best Practices:**\n• Never share your PIN with anyone\n• Enable biometric authentication\n• Check transactions regularly\n• Report suspicious activity immediately\n• Keep app updated\n• Use strong device lock\n\n🚨 **Emergency:** Call +************ for security issues'
      },
      {
        id: 'change_pin',
        text: 'Change PIN',
        category: 'security',
        response: '🔐 **Change Your PIN**\n\n**How to change PIN:**\n1️⃣ Go to Profile → Security Settings\n2️⃣ Tap "Change PIN"\n3️⃣ Enter current PIN\n4️⃣ Enter new 6-digit PIN\n5️⃣ Confirm new PIN\n6️⃣ PIN updated successfully\n\n**PIN Requirements:**\n• Must be 6 digits\n• Avoid obvious patterns (123456, 000000)\n• Don\'t use birthdays or phone numbers\n• Choose something memorable but secure\n\n**Security Tips:**\n🔒 **Keep it secret** - Never share with anyone\n🔄 **Change regularly** - Update every few months\n👀 **Cover when typing** - Prevent shoulder surfing\n\n⚠️ **Forgot PIN?** Use biometric login or contact support'
      },
      {
        id: 'fraud_protection',
        text: 'Fraud protection',
        category: 'security',
        response: '🛡️ **Fraud Protection System**\n\n**How We Protect You:**\n🤖 **AI Monitoring** - Detects unusual patterns\n🔔 **Real-time Alerts** - Instant notifications\n🚫 **Transaction Blocking** - Stops suspicious activity\n📱 **SMS Verification** - Confirms large transactions\n🕐 **Time Limits** - Automatic session timeouts\n\n**Red Flags to Watch:**\n⚠️ **Unexpected transactions**\n⚠️ **Unknown login attempts**\n⚠️ **Requests for PIN/passwords**\n⚠️ **Suspicious messages/calls**\n\n**If You Suspect Fraud:**\n1️⃣ **Immediately** call +************\n2️⃣ Change your PIN\n3️⃣ Check recent transactions\n4️⃣ Report to authorities if needed\n\n🔒 **Remember:** JiraniPay will NEVER ask for your PIN!'
      },
      {
        id: 'account_safety',
        text: 'Account safety',
        category: 'security',
        response: '🛡️ **Account Safety Guidelines**\n\n**Protect Your Account:**\n🔐 **Strong Authentication**\n• Use biometric login\n• Set complex PIN\n• Enable 2FA when available\n\n📱 **Device Security**\n• Keep device locked\n• Install security updates\n• Use official app stores only\n• Don\'t root/jailbreak device\n\n🌐 **Online Safety**\n• Use secure WiFi networks\n• Avoid public WiFi for transactions\n• Log out after use\n• Don\'t save passwords in browsers\n\n👥 **Social Engineering Protection**\n• Never share login details\n• Verify caller identity\n• Be suspicious of urgent requests\n• JiraniPay won\'t ask for sensitive info\n\n📞 **Report Issues:** +************ (24/7)'
      },
      // Additional Quick Reply Handlers
      {
        id: 'start_sending',
        text: 'Start sending',
        category: 'transactions',
        response: '🚀 **Ready to Send Money?**\n\n**Quick Start Guide:**\n1️⃣ Tap "Send Money" on your dashboard\n2️⃣ Choose how to find recipient:\n   • 📱 Enter phone number\n   • 👥 Select from contacts\n   • 📷 Scan their QR code\n3️⃣ Enter amount in UGX\n4️⃣ Add note (optional)\n5️⃣ Review and confirm\n6️⃣ Enter PIN to send\n\n💡 **Pro Tip:** For first-time recipients, double-check the phone number!\n\n🔒 **Security:** All transfers are encrypted and secure.'
      },
      {
        id: 'check_limits',
        text: 'Check limits',
        category: 'limits',
        response: '📊 **Your Transaction Limits**\n\n**Daily Limits by Account Level:**\n🥉 **Basic Account:** UGX 500,000/day\n🥈 **Verified Account:** UGX 2,000,000/day\n🥇 **Premium Account:** UGX 10,000,000/day\n\n**Transaction Limits:**\n💸 **Send Money:** Up to daily limit\n💳 **Bill Payments:** Up to UGX 1,000,000\n⬆️ **Top Up:** Up to UGX 5,000,000\n💰 **Withdrawal:** Up to daily limit\n\n**Upgrade Your Account:**\n• Complete KYC verification\n• Upload valid ID documents\n• Verify phone and email\n\n📈 **Need higher limits?** Contact support: +************'
      },
      {
        id: 'qr_scanner',
        text: 'QR scanner',
        category: 'qr',
        response: '📱 **QR Scanner Ready!**\n\n**How to scan:**\n1️⃣ Tap the QR icon on dashboard\n2️⃣ Point camera at QR code\n3️⃣ Wait for automatic detection\n4️⃣ Confirm payment details\n5️⃣ Enter PIN to complete\n\n**What you can scan:**\n🏪 **Merchant QR** - Pay at shops\n👤 **Personal QR** - Send to friends\n📄 **Bill QR** - Pay utilities\n🎫 **Event QR** - Pay for tickets\n\n**Tips for better scanning:**\n• Ensure good lighting\n• Hold phone steady\n• Keep QR code clean and visible\n\n🔍 **Having trouble?** Try moving closer or further from the QR code!'
      },
      {
        id: 'contact_help',
        text: 'Contact help',
        category: 'support',
        response: '📞 **Get Help Instantly**\n\n**Contact Options:**\n📱 **Phone:** +************ (24/7)\n📧 **Email:** <EMAIL>\n💬 **Live Chat:** Right here in the app!\n🌐 **Website:** www.jiranipay.com/support\n\n**Emergency Support:**\n🚨 **Fraud/Security:** +************\n🔒 **Account Locked:** Use this chat or call\n💳 **Transaction Issues:** Report immediately\n\n**Self-Service Options:**\n❓ **FAQ:** Common questions answered\n📖 **User Guide:** Step-by-step tutorials\n🎥 **Video Tutorials:** Visual guides\n\n**Response Times:**\n• 📱 Phone: Immediate\n• 💬 Chat: Within 2 minutes\n• 📧 Email: Within 2 hours'
      },
      {
        id: 'pay_umeme',
        text: 'Pay UMEME',
        category: 'bills',
        response: '⚡ **Pay UMEME Electricity Bills**\n\n**Quick Payment Steps:**\n1️⃣ Go to Pay Bills → UMEME\n2️⃣ Enter your meter number\n3️⃣ Choose payment amount\n4️⃣ Confirm meter details\n5️⃣ Enter PIN to pay\n6️⃣ Receive token instantly\n\n**Payment Options:**\n💰 **Prepaid:** Buy electricity units\n📄 **Postpaid:** Pay monthly bills\n🔄 **Auto-pay:** Set recurring payments\n\n**Minimum Amount:** UGX 1,000\n**Maximum Amount:** UGX 1,000,000 per transaction\n\n**Benefits:**\n✅ Instant token delivery\n✅ SMS confirmation\n✅ 24/7 availability\n✅ No extra charges\n\n💡 **Tip:** Save your meter number for faster future payments!'
      }
    ];
  }

  // =====================================================
  // CHAT MANAGEMENT
  // =====================================================

  async initializeChat(userId) {
    try {
      console.log(`🤖 AI: Initializing chat for user: ${userId}`);
      this.conversationId = `chat_${userId}_${Date.now()}`;

      // FORCE COMPLETE RESET: Clear all cached data for enhanced responses
      console.log('🧹 AI: FORCING COMPLETE CHAT RESET for enhanced responses');
      await AsyncStorage.removeItem(`chat_history_${userId}`);
      this.chatHistory = []; // Force empty array

      // Skip loading from storage - always start fresh for testing
      console.log('🧹 AI: Skipping storage load - starting with fresh chat');

      // Add welcome message if no history
      if (this.chatHistory.length === 0) {
        console.log('🤖 AI: Creating new welcome message with user context');
        const userContext = await this.getUserContext(userId);
        const welcomeResponse = this.handleGreeting('en', userContext);

        const welcomeMessage = {
          id: `msg_welcome_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          text: welcomeResponse.text,
          sender: 'ai',
          timestamp: new Date().toISOString(),
          type: 'text',
          quickReplies: welcomeResponse.quickReplies
        };

        this.chatHistory.push(welcomeMessage);
        await this.saveChatHistory(userId);
        console.log('🤖 AI: Welcome message created with personalization');
      }

      console.log(`✅ AI: Chat initialized with ${this.chatHistory.length} messages`);
      return { success: true, history: this.chatHistory };
    } catch (error) {
      console.error('❌ Error initializing chat:', error);
      return { success: false, error: error.message };
    }
  }

  async sendMessage(userId, message, type = 'text') {
    try {
      console.log(`🚀 AI: sendMessage called with: "${message}" for user: ${userId}`);

      // Check for duplicate messages to prevent double sending
      const lastMessage = this.chatHistory[this.chatHistory.length - 1];
      if (lastMessage && lastMessage.text === message && lastMessage.sender === 'user') {
        console.log('⚠️ Duplicate message detected, skipping...');
        return { success: false, error: 'Duplicate message' };
      }

      // Generate unique IDs to prevent duplicate keys
      const baseTimestamp = Date.now();
      const userMessageId = `msg_user_${baseTimestamp}_${Math.random().toString(36).substr(2, 9)}`;
      const aiMessageId = `msg_ai_${baseTimestamp + 1}_${Math.random().toString(36).substr(2, 9)}`;

      // Add user message
      const userMessage = {
        id: userMessageId,
        text: message,
        sender: 'user',
        timestamp: new Date().toISOString(),
        type
      };

      this.chatHistory.push(userMessage);

      // Get user context for personalized responses
      console.log(`🤖 AI: Getting user context for message: "${message}"`);
      const userContext = await this.getUserContext(userId);

      // Generate AI response with user context
      console.log(`🤖 AI: Generating response with context:`, {
        hasUser: !!userContext.user,
        hasWallet: !!userContext.wallet
      });
      this.isTyping = true;
      const aiResponse = await this.generateAIResponse(message, null, userContext);
      this.isTyping = false;
      console.log(`🤖 AI: Generated response:`, aiResponse.text.substring(0, 100) + '...');

      // Add AI response
      const aiMessage = {
        id: aiMessageId,
        text: aiResponse.text,
        sender: 'ai',
        timestamp: new Date().toISOString(),
        type: 'text',
        quickReplies: aiResponse.quickReplies || null
      };

      this.chatHistory.push(aiMessage);

      // Save to storage
      await this.saveChatHistory(userId);

      // Log conversation for analytics
      await this.logChatInteraction(userId, message, aiResponse.text);

      return { 
        success: true, 
        userMessage, 
        aiMessage,
        isTyping: false 
      };
    } catch (error) {
      console.error('❌ Error sending message:', error);
      this.isTyping = false;
      return { success: false, error: error.message };
    }
  }

  async getUserContext(userId) {
    try {
      console.log('🔍 AI: Getting user context for userId:', userId);

      // Use static imports (already imported at top of file)
      // Try multiple methods to get user data
      let user = authService.getCurrentUser();
      console.log('🔍 AI: Auth service user:', user ? 'found' : 'not found');

      // Always try to get fresh profile data for complete user info
      if (userId) {
        try {
          const profileResult = await profileManagementService.getProfile(userId);
          if (profileResult.success) {
            user = profileResult.data; // Use profile data as primary source
            console.log('🔍 AI: Profile service user found:', user.full_name);
          }
        } catch (error) {
          console.log('⚠️ Could not fetch user profile for AI context:', error.message);
          // Keep auth service user as fallback
        }
      }

      let walletData = null;

      try {
        const walletResult = await walletService.getWalletBalance(userId);
        walletData = walletResult.data;
        console.log('🔍 AI: Wallet data found, balance:', walletData?.balance);
      } catch (error) {
        console.log('⚠️ Could not fetch wallet data for AI context:', error.message);
      }

      // Debug user data structure
      console.log('🔍 AI: Raw user data:', {
        hasUser: !!user,
        fullName: user?.full_name,
        userKeys: user ? Object.keys(user) : 'no user'
      });

      const userContext = {
        user: user ? {
          name: user.full_name || 'User',
          phone: user.phone_number,
          isVerified: user.is_verified || false,
          verificationLevel: user.verification_level || 'basic'
        } : null,
        wallet: walletData ? {
          balance: walletData.balance || 0,
          currency: walletData.currency || 'UGX',
          accountNumber: walletData.account_number
        } : null,
        hasData: !!(user || walletData)
      };

      console.log('🔍 AI: Final user context:', {
        hasUser: !!userContext.user,
        userName: userContext.user?.name,
        hasWallet: !!userContext.wallet,
        walletBalance: userContext.wallet?.balance
      });

      return userContext;
    } catch (error) {
      console.error('❌ Error getting user context:', error);
      return { hasData: false };
    }
  }

  async generateAIResponse(userMessage, currentScreen = null, userContext = null) {
    try {
      console.log('🚀 AI: generateAIResponse called with:', { userMessage, currentScreen, userContext: !!userContext });
      const message = userMessage.toLowerCase();
      console.log('🔍 AI: Processing message:', userMessage);
      console.log('🔍 AI: Lowercase message:', message);
      const detectedLanguage = this.detectLanguage(message);

      // Use enhanced knowledge base for contextual understanding
      const codebaseContext = codebaseContextService.analyzeUserContext(userMessage, currentScreen);
      const contextualResponse = enhancedAIKnowledgeBase.getContextualResponse(userMessage, codebaseContext.intent);

      // Handle greetings first with personalization
      if (this.isGreeting(message)) {
        console.log('🔍 AI: Detected greeting, returning greeting response');
        return this.handleGreeting(detectedLanguage, userContext);
      }

      console.log('🔍 AI: Not a greeting, continuing to other checks...');

      if (this.isThanks(message)) {
        return this.handleThanks(detectedLanguage);
      }

      // PRIORITY 1: Check for FAQ matches using enhanced knowledge base
      if (contextualResponse.type === 'faq_match') {
        const faq = contextualResponse.content;
        let response = `**${faq.question}**\n\n${faq.answer}`;

        // Add additional related FAQs if available
        if (contextualResponse.additionalFAQs && contextualResponse.additionalFAQs.length > 0) {
          response += '\n\n**Related Questions:**';
          contextualResponse.additionalFAQs.forEach(relatedFaq => {
            response += `\n• ${relatedFaq.question}`;
          });
        }

        return {
          text: response,
          quickReplies: this.generateContextualQuickReplies(userContext)
        };
      }

      // PRIORITY 2: Handle feature guides
      if (contextualResponse.type === 'feature_guide') {
        const guide = contextualResponse.content;
        let response = `**${guide.title}**\n\n${guide.description}\n\n**Steps:**`;
        guide.steps.forEach((step, index) => {
          response += `\n${index + 1}. ${step}`;
        });

        if (guide.tips && guide.tips.length > 0) {
          response += '\n\n**Tips:**';
          guide.tips.forEach(tip => {
            response += `\n• ${tip}`;
          });
        }

        return {
          text: response,
          quickReplies: this.generateFeatureQuickReplies(guide)
        };
      }

      // PRIORITY 3: Handle troubleshooting
      if (contextualResponse.type === 'troubleshooting') {
        const troubleshooting = contextualResponse.content;
        let response = `**${troubleshooting.problem}**\n\n**Try these solutions:**`;
        troubleshooting.solutions.forEach((solution, index) => {
          response += `\n${index + 1}. ${solution}`;
        });

        return {
          text: response,
          quickReplies: ['Try another solution', 'Contact Support', 'Main Menu']
        };
      }

      // PRIORITY 4: Handle balance and transaction queries with real data (MOVED UP)
      console.log('🔍 AI: Checking balance query conditions:', {
        includesBalance: message.includes('balance'),
        includesCheck: message.includes('check'),
        includesMyBalance: message.includes('my balance'),
        message: message
      });

      if (message.includes('balance') || (message.includes('check') && message.includes('balance')) || message.includes('my balance')) {
        console.log('🔍 AI: Detected balance query, calling handleBalanceQuery');
        return this.handleBalanceQuery(userContext, detectedLanguage);
      }

      if (message.includes('transaction') || message.includes('history') || message.includes('recent')) {
        console.log('🔍 AI: Detected transaction query, calling handleTransactionQuery');
        return this.handleTransactionQuery(userContext, detectedLanguage);
      }

      // PRIORITY 5: Enhanced contextual response with app knowledge
      console.log('🔍 AI: Calling generateEnhancedContextualResponse...');
      const enhancedResponse = this.generateEnhancedContextualResponse(userMessage, codebaseContext, detectedLanguage);
      console.log('🔍 AI: Enhanced response result:', enhancedResponse ? 'found' : 'null');
      if (enhancedResponse && enhancedResponse.text) {
        console.log('🔍 AI: Enhanced response text preview:', enhancedResponse.text.substring(0, 50) + '...');
        console.log('🔍 AI: Checking if response includes "Could you please be more specific":', enhancedResponse.text.includes('Could you please be more specific'));
        if (!enhancedResponse.text.includes('Could you please be more specific')) {
          console.log('🔍 AI: Returning enhanced response (early exit)');
          return enhancedResponse;
        }
      }
      console.log('🔍 AI: Enhanced response not suitable, continuing...');

      // PRIORITY 6: Check for quick response matches (legacy support) - ONLY if no better match found
      const quickResponses = this.getQuickResponses();
      for (const quick of quickResponses) {
        // More strict matching for quick responses
        if (message.includes(quick.text.toLowerCase()) && message.length < 50) {
          return {
            text: quick.response,
            quickReplies: this.getRelatedQuickReplies(quick.category)
          };
        }
      }



      // PRIORITY 7: Handle specific Uganda financial queries
      if (message.includes('mobile money') || message.includes('mtn') || message.includes('airtel')) {
        return this.handleMobileMoneyQuery(message, detectedLanguage);
      }

      if (message.includes('ugx') || message.includes('shilling') || message.includes('sente')) {
        return this.handleCurrencyQuery(message, detectedLanguage);
      }

      if (message.includes('boda') || message.includes('matatu') || message.includes('transport')) {
        return this.handleTransportQuery(message, detectedLanguage);
      }

      // PRIORITY 7: Final fallback - return the enhanced response or default
      return contextualResponse || {
        text: 'I understand you need help with JiraniPay. I have comprehensive knowledge about money transfers, bill payments, wallet management, QR codes, and security. Could you please be more specific about what you\'d like to know?',
        quickReplies: ['Send money', 'Pay bills', 'Wallet help', 'QR codes', 'Security', 'Contact support']
      };
    } catch (error) {
      console.error('❌ Error generating AI response:', error);
      return {
        text: 'I apologize, but I\'m having trouble processing your request right now. Please try again or contact our support team at +************ for immediate assistance.',
        quickReplies: ['Contact Support', 'Try Again', 'Main Menu']
      };
    }
  }

  detectLanguage(message) {
    // Simple language detection based on common words
    const swahiliWords = ['hujambo', 'habari', 'asante', 'karibu', 'sawa', 'pesa', 'simu'];
    const lugandaWords = ['oli otya', 'webale', 'ssente', 'ssimu', 'boda', 'okukola'];

    const swahiliCount = swahiliWords.filter(word => message.includes(word)).length;
    const lugandaCount = lugandaWords.filter(word => message.includes(word)).length;

    if (swahiliCount > 0) return 'sw';
    if (lugandaCount > 0) return 'lg';
    return 'en'; // Default to English
  }

  isGreeting(message) {
    const greetings = ['hello', 'hi', 'hey', 'hujambo', 'habari', 'oli otya', 'good morning', 'good afternoon'];
    return greetings.some(greeting => message.includes(greeting));
  }

  isThanks(message) {
    const thanks = ['thank', 'thanks', 'asante', 'webale'];
    return thanks.some(thank => message.includes(thank));
  }

  handleGreeting(language = 'en', userContext = null) {
    const responses = this.ugandaFinanceKnowledge.responses.welcome;
    let greeting = responses[language] || responses.en;

    // Personalize greeting if user context is available
    if (userContext && userContext.hasData && userContext.user) {
      const userName = userContext.user.name.split(' ')[0]; // First name only
      greeting = `Hello ${userName}! 👋 I'm your JiraniPay AI Assistant. I can help you with wallet management, money transfers, bill payments, and any questions about the app. What would you like to do today?`;

      // Add balance info if available
      if (userContext.wallet) {
        const balance = new Intl.NumberFormat('en-UG', {
          style: 'currency',
          currency: 'UGX',
          minimumFractionDigits: 0
        }).format(userContext.wallet.balance);

        greeting += `\n\n💰 Your current wallet balance: ${balance}`;
      }
    }

    return {
      text: greeting,
      quickReplies: ['💰 Check my balance', '📤 Send money', '💡 Pay bills', '🔒 Security help', '📱 App guide']
    };
  }

  handleThanks(language = 'en') {
    const responses = this.ugandaFinanceKnowledge.responses.thanks;
    return {
      text: responses[language] || responses.en,
      quickReplies: ['Send money', 'Pay bills', 'Contact support', 'No, thanks']
    };
  }

  handleBalanceQuery(userContext, language = 'en') {
    if (userContext && userContext.hasData && userContext.wallet) {
      const balance = new Intl.NumberFormat('en-UG', {
        style: 'currency',
        currency: 'UGX',
        minimumFractionDigits: 0
      }).format(userContext.wallet.balance);

      const accountNumber = userContext.wallet.accountNumber;

      return {
        text: `💰 **Your Wallet Balance**\n\n**Current Balance:** ${balance}\n**Account:** ${accountNumber}\n**Currency:** UGX\n\nYour wallet is ready for transactions! You can send money, pay bills, or top up your balance anytime.`,
        quickReplies: ['📤 Send money', '💡 Pay bills', '⬆️ Top up wallet', '📊 Transaction history']
      };
    } else {
      return {
        text: `💰 **Check Your Balance**\n\nI'd love to show you your current balance, but I need to access your wallet data. Please make sure you're logged in and try again.\n\nYou can check your balance by:\n• Going to the Wallet tab\n• Viewing the main dashboard\n• Asking me again once you're logged in`,
        quickReplies: ['🔑 Login help', '📱 App guide', '💡 Other help']
      };
    }
  }

  handleTransactionQuery(userContext, language = 'en') {
    if (userContext && userContext.hasData) {
      return {
        text: `📊 **Transaction History**\n\nTo view your recent transactions:\n\n1️⃣ **Wallet Tab** → Transaction History\n2️⃣ **Dashboard** → Recent Transactions section\n3️⃣ **Analytics** → Detailed spending insights\n\nYou can filter by date, amount, or transaction type. All your JiraniPay transactions are securely stored and easily accessible.`,
        quickReplies: ['📱 Open Wallet', '📊 View Analytics', '🔍 Filter transactions', '💡 Other help']
      };
    } else {
      return {
        text: `📊 **Transaction History**\n\nTo access your transaction history, please log in to your JiraniPay account first.\n\nOnce logged in, you can:\n• View recent transactions on the dashboard\n• Access detailed history in the Wallet tab\n• Get spending insights in Analytics`,
        quickReplies: ['🔑 Login help', '📱 App guide', '💡 Other help']
      };
    }
  }

  matchesCategory(message, category) {
    const categoryKeywords = {
      wallet: ['balance', 'money', 'wallet', 'funds', 'account'],
      transactions: ['send', 'transfer', 'pay', 'transaction', 'payment'],
      bills: ['bill', 'umeme', 'water', 'electricity', 'airtime', 'data'],
      security: ['security', 'pin', 'password', 'safe', 'protect', 'lock'],
      limits: ['limit', 'maximum', 'minimum', 'restriction', 'amount'],
      support: ['help', 'support', 'contact', 'problem', 'issue', 'assistance']
    };

    const keywords = categoryKeywords[category] || [];
    return keywords.some(keyword => message.includes(keyword));
  }

  handleMobileMoneyQuery(message) {
    const providers = this.ugandaFinanceKnowledge.mobileMoney;
    
    if (message.includes('mtn')) {
      return {
        text: `MTN Mobile Money: Dial ${providers.mtn.code} for services. JiraniPay works seamlessly with MTN numbers (${providers.mtn.prefix}). You can top up your JiraniPay wallet directly from MTN Mobile Money.`,
        quickReplies: ['Top up wallet', 'Send money', 'Check balance']
      };
    }
    
    if (message.includes('airtel')) {
      return {
        text: `Airtel Money: Dial ${providers.airtel.code} for services. JiraniPay supports Airtel numbers (${providers.airtel.prefix}). You can link your Airtel Money account for easy top-ups.`,
        quickReplies: ['Link Airtel account', 'Top up wallet', 'Send money']
      };
    }

    return {
      text: 'JiraniPay works with all major Uganda mobile money providers: MTN Mobile Money, Airtel Money, and UTL Money. You can top up your wallet, send money, and pay bills using any of these services.',
      quickReplies: ['MTN Money', 'Airtel Money', 'Top up wallet']
    };
  }

  handleCurrencyQuery(message) {
    return {
      text: 'JiraniPay uses Ugandan Shillings (UGX) for all transactions. Current transaction limits: Basic accounts (UGX 500,000/day), Verified accounts (UGX 2,000,000/day), Premium accounts (UGX 10,000,000/day).',
      quickReplies: ['Check my limits', 'Upgrade account', 'Send money']
    };
  }

  handleTransportQuery(message) {
    return {
      text: 'Pay for boda boda rides and matatu fares easily with JiraniPay! Use QR codes or phone numbers to pay drivers directly. Many transport operators in Kampala and other cities accept JiraniPay.',
      quickReplies: ['Scan QR code', 'Send money', 'Find nearby']
    };
  }

  generateEnhancedContextualResponse(userMessage, userContext, language = 'en') {
    try {
      // Ensure userContext has all required properties with defaults
      const {
        intent = 'general_inquiry',
        relevantFeatures = [],
        suggestedFlow = null,
        troubleshootingContext = null
      } = userContext || {};

      // Defensive programming: Ensure all arrays are properly initialized
      const safeRelevantFeatures = Array.isArray(relevantFeatures) ? relevantFeatures : [];
      const safeUserMessage = userMessage || '';
      const safeIntent = intent || 'general_inquiry';

      // Handle navigation queries
      if (safeIntent === 'navigation') {
        return this.handleNavigationQuery(safeUserMessage, safeRelevantFeatures, suggestedFlow);
      }

      // Handle feature usage queries
      if (safeIntent === 'feature_usage') {
        return this.handleFeatureUsageQuery(safeUserMessage, safeRelevantFeatures);
      }

      // Handle troubleshooting queries
      if (safeIntent === 'troubleshooting' && troubleshootingContext) {
        return this.handleTroubleshootingQuery(safeUserMessage, troubleshootingContext);
      }

      // Handle information requests
      if (safeIntent === 'information') {
        return this.handleInformationQuery(safeUserMessage, safeRelevantFeatures);
      }

      // Handle limits and fees queries
      if (safeIntent === 'limits_fees') {
        return this.handleLimitsFeesQuery(safeUserMessage);
      }

      // Handle security queries
      if (safeIntent === 'security') {
        return this.handleSecurityQuery(safeUserMessage);
      }

      // Enhanced feature matching with better context
      if (safeRelevantFeatures && safeRelevantFeatures.length > 0) {
        const topFeature = safeRelevantFeatures[0];
        // Defensive programming: Ensure topFeature has required properties
        if (!topFeature || !topFeature.feature) {
          console.warn('⚠️ AI: Invalid feature object in relevantFeatures');
          return this.generateFallbackResponse(safeUserMessage);
        }
        const featureName = topFeature.feature.replace('_', ' ');

        // Generate more specific responses based on the feature
        let response = '';
        let quickReplies = [];

        // Safe access to userContext properties
        const safeUserContext = userContext || {};
        const walletBalance = safeUserContext.walletBalance || 0;

        switch (topFeature.feature) {
          case 'send_money':
            response = `💸 **Send Money with JiraniPay**\n\n**Quick Steps:**\n1️⃣ Tap "Send Money" on your dashboard\n2️⃣ Enter recipient's phone number or scan QR code\n3️⃣ Enter amount (minimum UGX 1,000)\n4️⃣ Add optional note\n5️⃣ Confirm with your PIN\n\n✅ **Instant transfers** to any JiraniPay user\n💰 **FREE** JiraniPay-to-JiraniPay transfers\n🔒 **Bank-level security** with PIN protection\n\nYour current balance: USh ${walletBalance}`;
            quickReplies = ['Start sending', 'Check limits', 'QR scanner', 'Contact help'];
            break;
          case 'bill_payments':
            response = `💳 **Pay Bills with JiraniPay**\n\n**Supported Services:**\n⚡ UMEME (Electricity)\n💧 NWSC (Water)\n📱 MTN & Airtel (Airtime/Data)\n📺 DStv & GoTv\n🌐 Internet providers\n\n**How to pay:**\n1️⃣ Go to "Bills" tab\n2️⃣ Select your service provider\n3️⃣ Enter account/meter number\n4️⃣ Enter amount\n5️⃣ Confirm with PIN\n\n💡 **Tip:** Save frequently used accounts for faster payments!`;
            quickReplies = ['Pay UMEME', 'Buy airtime', 'Water bills', 'More bills'];
            break;
          case 'wallet_management':
            response = `💼 **Your JiraniPay Wallet**\n\n**Current Balance:** USh ${walletBalance}\n**Account:** ${safeUserContext.accountNumber || 'Loading...'}\n\n**Wallet Features:**\n💰 Check balance anytime\n📊 View transaction history\n⬆️ Top up from mobile money/bank\n🎯 Set spending limits\n💎 Manage savings goals\n🔒 Bank-level security\n\n**Quick Actions:**`;
            quickReplies = ['Check balance', 'Top up wallet', 'Transaction history', 'Security'];
            break;
          case 'security':
            response = `🔒 **JiraniPay Security Features**\n\n**Your Protection:**\n🔐 PIN-based authentication\n📱 Biometric login (fingerprint/face)\n🔔 Real-time transaction alerts\n🛡️ Fraud monitoring\n⏰ Auto-logout for security\n\n**Security Tips:**\n• Never share your PIN\n• Enable biometric authentication\n• Check transactions regularly\n• Report suspicious activity immediately\n\n**Need Help?** Contact our security team 24/7`;
            quickReplies = ['Enable biometric', 'Change PIN', 'Report fraud', 'Security tips'];
            break;
          case 'qr_scanner':
            response = `📱 **QR Code Scanner**\n\n**Quick Payments:**\n📷 Scan merchant QR codes\n💸 Pay instantly without typing\n🏪 Works at shops, restaurants, boda bodas\n✅ Secure and fast transactions\n\n**How to use:**\n1️⃣ Tap QR scanner icon\n2️⃣ Point camera at QR code\n3️⃣ Confirm payment details\n4️⃣ Enter PIN to complete\n\n💡 **Tip:** Look for JiraniPay QR codes at participating merchants!`;
            quickReplies = ['Scan QR code', 'Find merchants', 'Payment help', 'More info'];
            break;
          default:
            // Defensive programming: Safely access nested properties
            const implementation = topFeature.implementation || {};
            const features = implementation.features || [];
            const firstFeature = features.length > 0 ? features[0] : 'explore this feature';
            response = `I can help you with ${featureName}. ${firstFeature}. Would you like specific guidance?`;
            quickReplies = ['Yes, guide me', 'Show steps', 'More info', 'Other help'];
        }

        return { text: response, quickReplies };
      }

      // Improved fallback with better suggestions
      return {
        text: 'I\'m here to help with all JiraniPay features! I can assist with money transfers, bill payments, wallet management, QR payments, and security settings. What would you like to know about?',
        quickReplies: this.generateSmartQuickReplies(safeUserMessage)
      };

    } catch (error) {
      console.error('❌ Error in generateEnhancedContextualResponse:', error);

      // Return a safe fallback response
      return this.generateFallbackResponse(userMessage || '');
    }
  }

  generateFallbackResponse(userMessage) {
    return {
      text: `I can help you with "${userMessage}". Let me provide you with some general assistance while I resolve a technical issue.`,
      quickReplies: ['💰 Check my balance', 'Main Menu', 'Contact Support', 'Try Again']
    };
  }

  generateSmartQuickReplies(userMessage) {
    const message = userMessage.toLowerCase();

    // Generate contextual quick replies based on user message
    if (message.includes('money') || message.includes('send') || message.includes('transfer')) {
      return ['Send money guide', 'Check limits', 'QR payments', 'Contact help'];
    }

    if (message.includes('bill') || message.includes('pay') || message.includes('umeme') || message.includes('airtime')) {
      return ['Pay bills guide', 'UMEME bills', 'Buy airtime', 'Water bills'];
    }

    if (message.includes('wallet') || message.includes('balance') || message.includes('top up')) {
      return ['Check balance', 'Top up wallet', 'Transaction history', 'Wallet settings'];
    }

    if (message.includes('qr') || message.includes('scan') || message.includes('code')) {
      return ['QR scanner guide', 'Generate QR', 'Merchant payments', 'QR help'];
    }

    if (message.includes('security') || message.includes('safe') || message.includes('pin') || message.includes('fraud')) {
      return ['Security guide', 'Change PIN', 'Fraud protection', 'Account safety'];
    }

    // Default smart suggestions
    return ['Send money', 'Pay bills', 'Wallet help', 'QR scanner', 'Security', 'Contact support'];
  }

  generateContextualResponse(message) {
    // Legacy method for backward compatibility
    return this.generateEnhancedContextualResponse(message, { intent: 'general', relevantFeatures: [], suggestedFlow: null, troubleshootingContext: null });
  }

  generateContextualQuickReplies(userContext) {
    const { intent, relevantFeatures } = userContext;

    if (intent === 'how_to' && relevantFeatures && relevantFeatures.length > 0) {
      const feature = relevantFeatures[0].feature;
      switch (feature) {
        case 'send_money':
          return ['Start sending', 'Check limits', 'QR method', 'More help'];
        case 'bill_payments':
          return ['Pay UMEME', 'Buy airtime', 'Water bills', 'More bills'];
        case 'wallet_management':
          return ['Check balance', 'Top up', 'History', 'Settings'];
        default:
          return ['Step by step', 'More info', 'Try now', 'Other help'];
      }
    }

    return ['More details', 'Related topics', 'Try it', 'Contact support'];
  }

  generateFeatureQuickReplies(guide) {
    const feature = guide.title.toLowerCase();

    if (feature.includes('send') || feature.includes('money')) {
      return ['Start sending', 'Check limits', 'QR scanner', 'More help'];
    }

    if (feature.includes('bill') || feature.includes('pay')) {
      return ['Pay now', 'Set recurring', 'Check bills', 'More options'];
    }

    if (feature.includes('dashboard') || feature.includes('home')) {
      return ['Check balance', 'Quick actions', 'Transactions', 'Settings'];
    }

    return ['Try it now', 'More tips', 'Other features', 'Get help'];
  }

  getRelatedQuickReplies(category) {
    const relatedReplies = {
      wallet: ['Check balance', 'Top up wallet', 'Transaction history'],
      transactions: ['Send money', 'Check limits', 'Transaction status'],
      bills: ['Pay UMEME', 'Buy airtime', 'Pay water bill'],
      security: ['Change PIN', 'Enable biometrics', 'Security tips'],
      limits: ['Upgrade account', 'Verify identity', 'Check limits'],
      support: ['Call support', 'Email support', 'Security hotline']
    };

    return relatedReplies[category] || ['Main menu', 'Get help', 'Contact support'];
  }

  async saveChatHistory(userId) {
    try {
      await AsyncStorage.setItem(`chat_history_${userId}`, JSON.stringify(this.chatHistory));
    } catch (error) {
      console.error('❌ Error saving chat history:', error);
    }
  }

  async logChatInteraction(userId, userMessage, aiResponse) {
    try {
      await supabase
        .from('chat_analytics')
        .insert({
          user_id: userId,
          conversation_id: this.conversationId,
          user_message: userMessage,
          ai_response: aiResponse,
          timestamp: new Date().toISOString()
        });
    } catch (error) {
      console.error('❌ Error logging chat interaction:', error);
    }
  }

  getTypingStatus() {
    return this.isTyping;
  }

  getChatHistory() {
    return this.chatHistory;
  }

  async clearChatHistory(userId) {
    try {
      this.chatHistory = [];
      await AsyncStorage.removeItem(`chat_history_${userId}`);
      return { success: true };
    } catch (error) {
      console.error('❌ Error clearing chat history:', error);
      return { success: false, error: error.message };
    }
  }

  // =====================================================
  // ENHANCED RESPONSE HANDLERS
  // =====================================================

  handleNavigationQuery(userMessage, relevantFeatures, suggestedFlow) {
    if (suggestedFlow) {
      let response = `**To ${suggestedFlow.description}:**\n\n`;
      suggestedFlow.flow.forEach((step, index) => {
        response += `${index + 1}. ${step}\n`;
      });

      return {
        text: response,
        quickReplies: ['Start process', 'More details', 'Other options', 'Main menu']
      };
    }

    if (relevantFeatures && relevantFeatures.length > 0) {
      const feature = relevantFeatures[0];
      return {
        text: `To access ${feature.feature.replace('_', ' ')}, you can navigate through: ${feature.implementation.userFlow}`,
        quickReplies: ['Show me steps', 'More info', 'Other features', 'Main menu']
      };
    }

    // Default fallback response
    return {
      text: 'I can help you navigate to any part of the JiraniPay app. What specific feature or screen are you looking for?',
      quickReplies: ['Dashboard', 'Send Money', 'Pay Bills', 'Wallet', 'Profile', 'QR Scanner']
    };
  }

  handleFeatureUsageQuery(userMessage, relevantFeatures = []) {
    try {
      // Defensive programming: Ensure relevantFeatures is an array
      const safeFeatures = Array.isArray(relevantFeatures) ? relevantFeatures : [];

      if (safeFeatures.length > 0) {
        const feature = safeFeatures[0];

        // Defensive programming: Check if feature has required properties
        if (!feature || !feature.feature) {
          console.warn('⚠️ AI: Invalid feature object in handleFeatureUsageQuery');
          return this.generateFallbackResponse(userMessage);
        }

        const implementation = feature.implementation || {};
        const featureName = feature.feature.replace('_', ' ');
        const userFlow = implementation.userFlow || 'Navigate through the app to access this feature';
        const features = implementation.features || [];

        let response = `**How to use ${featureName}:**\n\n`;
        response += `${userFlow}\n\n`;

        if (features.length > 0) {
          response += `**Key Features:**\n`;
          features.slice(0, 3).forEach(f => {
            response += `• ${f}\n`;
          });
        }

        return {
          text: response,
          quickReplies: ['Step-by-step guide', 'More features', 'Try it now', 'Other help']
        };
      }

      return {
        text: 'I can provide detailed guides for all JiraniPay features. What would you like to learn how to use?',
        quickReplies: ['Send Money', 'Pay Bills', 'QR Scanner', 'Wallet Management', 'Security Settings']
      };

    } catch (error) {
      console.error('❌ Error in handleFeatureUsageQuery:', error);
      return this.generateFallbackResponse(userMessage);
    }
  }

  handleInformationQuery(userMessage, relevantFeatures) {
    try {
      const message = userMessage.toLowerCase();

      if (message.includes('limit') || message.includes('maximum') || message.includes('minimum')) {
        return this.handleLimitsFeesQuery(userMessage);
      }

      if (message.includes('fee') || message.includes('cost') || message.includes('charge')) {
        return {
          text: 'JiraniPay fees: JiraniPay to JiraniPay transfers are FREE! Bank transfers: UGX 1,000. Mobile money: UGX 500. Agent withdrawals: UGX 2,000. No monthly fees!',
          quickReplies: ['Check limits', 'Free transfers', 'Top up options', 'More info']
        };
      }

      // Defensive programming: Safely handle relevantFeatures
      const safeFeatures = Array.isArray(relevantFeatures) ? relevantFeatures : [];
      if (safeFeatures.length > 0) {
        const feature = safeFeatures[0];

        if (feature && feature.feature) {
          const implementation = feature.implementation || {};
          const features = implementation.features || [];
          const featureName = feature.feature.replace('_', ' ');
          const featureList = features.length > 0 ? features.join(', ') : 'various capabilities';

          return {
            text: `${featureName} information: ${featureList}. Would you like detailed guidance?`,
            quickReplies: ['How to use', 'Step by step', 'More features', 'Contact help']
          };
        }
      }

      return {
        text: 'I can provide information about all JiraniPay features, fees, limits, and security. What specific information do you need?',
        quickReplies: ['Transaction limits', 'Fees & charges', 'Security info', 'Feature details']
      };

    } catch (error) {
      console.error('❌ Error in handleInformationQuery:', error);
      return this.generateFallbackResponse(userMessage);
    }
  }

  handleLimitsFeesQuery(userMessage) {
    return {
      text: '**Transaction Limits:**\n• Basic Account: UGX 500,000/day\n• Verified Account: UGX 2,000,000/day\n• Premium Account: UGX 10,000,000/day\n\n**Fees:**\n• JiraniPay transfers: FREE\n• Bank transfers: UGX 1,000\n• Mobile money: UGX 500\n• Cash withdrawal: UGX 2,000',
      quickReplies: ['Upgrade account', 'Verify identity', 'Free transfers', 'More info']
    };
  }

  handleSecurityQuery(userMessage) {
    const message = userMessage.toLowerCase();

    if (message.includes('fraud') || message.includes('suspicious') || message.includes('hack')) {
      return {
        text: '**If you suspect fraud:**\n1. Immediately freeze your account in the app\n2. Contact support via app or hotline: +************\n3. Change your PIN and password\n4. Report the incident\n\nWe have 24/7 fraud monitoring and will investigate promptly.',
        quickReplies: ['Freeze account', 'Contact support', 'Change PIN', 'Security tips']
      };
    }

    return {
      text: '**JiraniPay Security Features:**\n• Biometric login (fingerprint/face)\n• Two-factor authentication\n• Transaction PINs\n• Real-time fraud monitoring\n• Account alerts\n• Instant account freeze\n\nYour money is protected with bank-level security!',
      quickReplies: ['Enable biometrics', 'Setup 2FA', 'Change PIN', 'Security tips']
    };
  }

  handleTroubleshootingQuery(userMessage, troubleshootingContext) {
    if (troubleshootingContext) {
      let response = `**${troubleshootingContext.problem}**\n\n**Try these solutions:**`;
      troubleshootingContext.solutions.forEach((solution, index) => {
        response += `\n${index + 1}. ${solution}`;
      });

      return {
        text: response,
        quickReplies: ['Try solution', 'Contact support', 'More help', 'Other issues']
      };
    }

    return {
      text: 'I can help troubleshoot common issues with JiraniPay. What specific problem are you experiencing?',
      quickReplies: ['Login issues', 'Transaction failed', 'App crashes', 'Biometric problems']
    };
  }



  handleInformationQuery(userMessage, relevantFeatures = []) {
    if (relevantFeatures && relevantFeatures.length > 0) {
      const feature = relevantFeatures[0];
      const implementation = feature.implementation;

      let response = `**About ${feature.feature.replace('_', ' ')}:**\n\n`;
      response += `${implementation.features.join('\n• ')}\n\n`;
      response += `**How to access:** ${implementation.userFlow}`;

      return {
        text: response,
        quickReplies: ['How to use', 'More details', 'Other features', 'Try it now']
      };
    }

    return {
      text: 'I can provide detailed information about any JiraniPay feature. What would you like to know more about?',
      quickReplies: ['App Features', 'Security', 'Fees & Limits', 'Account Types', 'Support Options']
    };
  }

  handleLimitsFeesQuery(userMessage) {
    const limits = this.ugandaFinanceKnowledge.limits;
    let response = `**JiraniPay Transaction Limits:**\n\n`;
    response += `**Basic Account:** UGX ${limits.basic.daily.toLocaleString()}/day, UGX ${limits.basic.monthly.toLocaleString()}/month\n`;
    response += `**Verified Account:** UGX ${limits.verified.daily.toLocaleString()}/day, UGX ${limits.verified.monthly.toLocaleString()}/month\n`;
    response += `**Premium Account:** UGX ${limits.premium.daily.toLocaleString()}/day, UGX ${limits.premium.monthly.toLocaleString()}/month\n\n`;
    response += `**Fees:** Most JiraniPay to JiraniPay transfers are free. Small fees apply for mobile money withdrawals and some bill payments.`;

    return {
      text: response,
      quickReplies: ['Upgrade Account', 'Fee Details', 'Check My Limits', 'Verification Help']
    };
  }



  generateContextualQuickReplies(userContext) {
    const { intent, relevantFeatures } = userContext;

    if (intent === 'navigation') {
      return ['Show me steps', 'Other features', 'Main menu', 'Contact support'];
    }

    if (intent === 'troubleshooting') {
      return ['Try solutions', 'Contact Support', 'Report issue', 'Other help'];
    }

    if (relevantFeatures && relevantFeatures.length > 0) {
      const feature = relevantFeatures[0].feature;
      return [`Use ${feature.replace('_', ' ')}`, 'Step-by-step guide', 'More info', 'Other features'];
    }

    return ['Send money', 'Pay bills', 'Wallet help', 'Contact support'];
  }

  generateFeatureQuickReplies(guide) {
    return ['Try it now', 'More tips', 'Other features', 'Need help'];
  }
}

export default new AIChatService();
