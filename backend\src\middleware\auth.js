/**
 * Authentication Middleware
 * Handles JWT token verification and user authentication
 */

const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('../services/database');
const redisService = require('../services/redis');
const { AuthenticationError, AuthorizationError } = require('./errorHandler');

/**
 * Generate JWT token
 */
const generateToken = (payload, expiresIn = config.jwt.expiresIn) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};

/**
 * Generate refresh token
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};

/**
 * Verify JWT token
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, config.jwt.secret, {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new AuthenticationError('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new AuthenticationError('Invalid token');
    } else {
      throw new AuthenticationError('Token verification failed');
    }
  }
};

/**
 * Extract token from request
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (for web admin panel)
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }
  
  return null;
};

/**
 * Main authentication middleware
 */
const authenticate = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      throw new AuthenticationError('Access token required');
    }

    // Verify token
    const decoded = verifyToken(token);
    
    // Check if token is blacklisted (logout/revoked tokens)
    const isBlacklisted = await redisService.exists(`blacklist:${token}`);
    if (isBlacklisted) {
      throw new AuthenticationError('Token has been revoked');
    }

    // Get user from database
    const user = await getUserById(decoded.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Check if user is active
    if (!user.is_active) {
      throw new AuthenticationError('Account is deactivated');
    }

    // Add user to request object
    req.user = user;
    req.token = token;
    req.tokenPayload = decoded;

    // Log authentication
    logger.audit('User authenticated', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    logger.security('Authentication failed', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      error: error.message
    });
    next(error);
  }
};

/**
 * Admin authentication middleware
 */
const authenticateAdmin = async (req, res, next) => {
  try {
    // First authenticate as regular user
    await authenticate(req, res, () => {});
    
    // Check if user has admin role
    if (!req.user.is_admin) {
      throw new AuthorizationError('Admin access required');
    }

    logger.audit('Admin authenticated', {
      userId: req.user.id,
      email: req.user.email,
      ip: req.ip
    });

    next();
  } catch (error) {
    logger.security('Admin authentication failed', {
      userId: req.user?.id,
      ip: req.ip,
      error: error.message
    });
    next(error);
  }
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
const optionalAuthenticate = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await getUserById(decoded.userId);
      
      if (user && user.is_active) {
        req.user = user;
        req.token = token;
        req.tokenPayload = decoded;
      }
    }
    
    next();
  } catch (error) {
    // Don't fail for optional authentication
    logger.debug('Optional authentication failed:', error.message);
    next();
  }
};

/**
 * Role-based authorization middleware
 */
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    if (!roles.includes(req.user.role)) {
      logger.security('Authorization failed', {
        userId: req.user.id,
        requiredRoles: roles,
        userRole: req.user.role
      });
      return next(new AuthorizationError('Insufficient permissions'));
    }

    next();
  };
};

/**
 * Resource ownership middleware
 */
const requireOwnership = (resourceIdParam = 'id') => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    const resourceId = req.params[resourceIdParam];
    const userId = req.user.id;

    // Admin can access any resource
    if (req.user.is_admin) {
      return next();
    }

    // Check if user owns the resource
    if (resourceId !== userId) {
      logger.security('Ownership check failed', {
        userId,
        resourceId,
        resourceParam: resourceIdParam
      });
      return next(new AuthorizationError('Access denied'));
    }

    next();
  };
};

/**
 * Session validation middleware
 */
const validateSession = async (req, res, next) => {
  try {
    if (!req.user || !req.tokenPayload) {
      return next();
    }

    // Check if session exists in Redis
    const sessionData = await redisService.getSession(req.tokenPayload.sessionId);
    
    if (!sessionData) {
      throw new AuthenticationError('Session expired');
    }

    // Update session activity
    await redisService.setSession(
      req.tokenPayload.sessionId,
      {
        ...sessionData,
        lastActivity: new Date().toISOString(),
        ip: req.ip,
        userAgent: req.get('User-Agent')
      },
      24 * 60 * 60 // 24 hours
    );

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Rate limiting by user
 */
const rateLimitByUser = (maxRequests = 100, windowMinutes = 15) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next();
      }

      const identifier = `user:${req.user.id}`;
      const windowSeconds = windowMinutes * 60;
      
      const { allowed, remaining } = await redisService.checkRateLimit(
        identifier,
        maxRequests,
        windowSeconds
      );

      if (!allowed) {
        logger.security('User rate limit exceeded', {
          userId: req.user.id,
          ip: req.ip,
          maxRequests,
          windowMinutes
        });
        
        res.set({
          'X-RateLimit-Limit': maxRequests,
          'X-RateLimit-Remaining': remaining,
          'X-RateLimit-Reset': new Date(Date.now() + windowSeconds * 1000).toISOString()
        });
        
        return next(new RateLimitError('User rate limit exceeded'));
      }

      res.set({
        'X-RateLimit-Limit': maxRequests,
        'X-RateLimit-Remaining': remaining
      });

      next();
    } catch (error) {
      // Don't fail request if rate limiting fails
      logger.warn('Rate limiting failed:', error.message);
      next();
    }
  };
};

/**
 * Helper function to get user by ID
 */
const getUserById = async (userId) => {
  try {
    // Try to get from cache first
    const cachedUser = await redisService.getCachedUser(userId);
    if (cachedUser) {
      return cachedUser;
    }

    // Get from database
    const supabase = databaseService.getSupabase();
    const { data: user, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        auth_users:user_id (
          email,
          email_confirmed_at,
          last_sign_in_at
        )
      `)
      .eq('user_id', userId)
      .single();

    if (error) {
      logger.error('Failed to get user:', error);
      return null;
    }

    // Cache user data
    await redisService.cacheUser(userId, user, 3600); // 1 hour

    return user;
  } catch (error) {
    logger.error('Error getting user:', error);
    return null;
  }
};

/**
 * Logout helper - blacklist token
 */
const blacklistToken = async (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) {
      const expirationTime = decoded.exp - Math.floor(Date.now() / 1000);
      if (expirationTime > 0) {
        await redisService.set(`blacklist:${token}`, true, expirationTime);
      }
    }
  } catch (error) {
    logger.error('Failed to blacklist token:', error);
  }
};

module.exports = {
  generateToken,
  generateRefreshToken,
  verifyToken,
  authenticate,
  authenticateAdmin,
  optionalAuthenticate,
  authorize,
  requireOwnership,
  validateSession,
  rateLimitByUser,
  blacklistToken,
  getUserById
};
