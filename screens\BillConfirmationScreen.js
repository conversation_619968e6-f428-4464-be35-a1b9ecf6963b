import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import billPaymentService from '../services/billPaymentService';

const BillConfirmationScreen = ({ navigation, route }) => {
  const { category, provider, billDetails, amount } = route.params;
  const [loading, setLoading] = useState(false);
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const handleConfirmPayment = async () => {
    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Create payment data
      const paymentData = {
        providerId: provider.id,
        accountNumber: billDetails.accountNumber,
        amount: amount,
        category: category.title,
        customerName: billDetails.customerName,
        reference: billDetails.reference,
      };

      // Process payment
      const result = await billPaymentService.processBillPayment(paymentData);

      if (result.success) {
        // Navigate to success screen
        navigation.navigate('BillSuccess', {
          category,
          provider,
          billDetails,
          amount,
          transaction: result.data.transaction,
          reference: result.data.reference
        });
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert(
        'Payment Failed',
        error.message || 'Unable to process payment. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (amount) => {
    return amount.toLocaleString();
  };

  const formatDate = () => {
    return new Date().toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={theme.statusBar === 'dark' ? 'dark-content' : 'light-content'}
        backgroundColor="transparent"
        translucent
      />

      {/* Header */}
      <LinearGradient
        colors={['#E67E22', '#D35400']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>Pay Bill</Text>
          <View style={styles.headerSpacer} />
        </View>
      </LinearGradient>

      <View style={styles.content}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Confirmation Header */}
          <View style={styles.confirmationHeader}>
            <View style={styles.confirmationIcon}>
              <Ionicons name="card" size={32} color="#E67E22" />
            </View>
            <Text style={styles.confirmationTitle}>Confirm Payment</Text>
            <Text style={styles.confirmationSubtitle}>
              Please review your payment details
            </Text>
          </View>

          {/* Account Details */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>ACCOUNT DETAILS</Text>
            <View style={styles.detailsCard}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Provider</Text>
                <Text style={styles.detailValue}>{provider.name}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Category</Text>
                <Text style={styles.detailValue}>{category.title}</Text>
              </View>
              {provider?.type === 'mobile' && billDetails?.serviceType && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Service Type</Text>
                  <Text style={styles.detailValue}>
                    {billDetails.serviceType === 'airtime' ? 'Airtime Top-up' : 'Data Bundles'}
                  </Text>
                </View>
              )}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Account</Text>
                <Text style={styles.detailValue}>{billDetails.accountNumber}</Text>
              </View>
              {billDetails.customerName && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Customer</Text>
                  <Text style={styles.detailValue}>{billDetails.customerName}</Text>
                </View>
              )}
              {billDetails.reference && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Reference</Text>
                  <Text style={styles.detailValue}>{billDetails.reference}</Text>
                </View>
              )}
            </View>
          </View>

          {/* Payment Details */}
          <View style={styles.detailsSection}>
            <Text style={styles.sectionTitle}>PAYMENT DETAILS</Text>
            <View style={styles.detailsCard}>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Amount</Text>
                <Text style={styles.detailValue}>UGX {formatAmount(amount)}</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Service Charge</Text>
                <Text style={styles.detailValue}>UGX 0</Text>
              </View>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>VAT</Text>
                <Text style={styles.detailValue}>UGX 0</Text>
              </View>
              <View style={[styles.detailRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalValue}>UGX {formatAmount(amount)}</Text>
              </View>
            </View>
          </View>

          {/* Transaction Info */}
          <View style={styles.transactionInfo}>
            <View style={styles.infoRow}>
              <Ionicons name="time" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.infoText}>Payment will be processed immediately</Text>
            </View>
            <View style={styles.infoRow}>
              <Ionicons name="shield-checkmark" size={16} color={theme.colors.textSecondary} />
              <Text style={styles.infoText}>Your payment is secure and encrypted</Text>
            </View>
          </View>
        </ScrollView>

        {/* Confirm Button */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.confirmButton, loading && styles.confirmButtonDisabled]}
            onPress={handleConfirmPayment}
            disabled={loading}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#E67E22', '#D35400']}
              style={styles.confirmButtonGradient}
            >
              {loading ? (
                <>
                  <ActivityIndicator size="small" color={Colors.neutral.white} />
                  <Text style={styles.confirmButtonText}>Processing...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="checkmark" size={20} color={Colors.neutral.white} />
                  <Text style={styles.confirmButtonText}>
                    Confirm Payment
                  </Text>
                </>
              )}
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 120,
  },
  confirmationHeader: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  confirmationIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E67E22' + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  confirmationTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  confirmationSubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  detailsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  detailsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginBottom: 0,
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'right',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E67E22',
    flex: 1,
    textAlign: 'right',
  },
  transactionInfo: {
    paddingHorizontal: 20,
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  confirmButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  confirmButtonDisabled: {
    opacity: 0.6,
    elevation: 2,
  },
  confirmButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    gap: 12,
  },
  confirmButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
});

export default BillConfirmationScreen;
