/**
 * Category Management Screen
 * Provides interface for managing transaction categories
 * Supports CRUD operations for custom categories and bulk operations
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import authService from '../services/authService';
import categoryManagementService from '../services/categoryManagementService';
import bulkCategorizationService from '../services/bulkCategorizationService';
import { createStyles } from '../styles/CategoryManagementStyles';

const CategoryManagementScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { formatAmount } = useCurrencyContext();
  const [user, setUser] = useState(null);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [categoryStats, setCategoryStats] = useState({});
  const [searchQuery, setSearchQuery] = useState('');

  const styles = createStyles(theme);

  /**
   * Get current user on mount
   */
  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, []);

  /**
   * Load categories when user is available
   */
  useEffect(() => {
    if (user?.id) {
      loadCategories();
    }
  }, [user?.id]);

  /**
   * Load categories and their usage statistics
   */
  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);

      // Load categories
      const categoriesResult = await categoryManagementService.getUserCategories(user.id);
      if (categoriesResult.success) {
        setCategories(categoriesResult.data);

        // Load usage statistics for each category
        const statsPromises = categoriesResult.data.map(async (category) => {
          const statsResult = await categoryManagementService.getCategoryUsageStats(user.id, category.id);
          return {
            categoryId: category.id,
            stats: statsResult.success ? statsResult.data : null
          };
        });

        const statsResults = await Promise.allSettled(statsPromises);
        const statsMap = {};
        
        statsResults.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value.stats) {
            statsMap[result.value.categoryId] = result.value.stats;
          }
        });

        setCategoryStats(statsMap);
      }
    } catch (error) {
      console.error('❌ Error loading categories:', error);
      Alert.alert('Error', 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  }, [loadCategories]);

  /**
   * Handle create category
   */
  const handleCreateCategory = useCallback(async (categoryData) => {
    try {
      const result = await categoryManagementService.createCustomCategory(user.id, categoryData);
      
      if (result.success) {
        setShowCreateModal(false);
        await loadCategories();
        Alert.alert('Success', 'Category created successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to create category');
      }
    } catch (error) {
      console.error('❌ Error creating category:', error);
      Alert.alert('Error', 'Failed to create category');
    }
  }, [user?.id, loadCategories]);

  /**
   * Handle edit category
   */
  const handleEditCategory = useCallback(async (categoryId, updateData) => {
    try {
      const result = await categoryManagementService.updateCustomCategory(user.id, categoryId, updateData);
      
      if (result.success) {
        setEditingCategory(null);
        await loadCategories();
        Alert.alert('Success', 'Category updated successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to update category');
      }
    } catch (error) {
      console.error('❌ Error updating category:', error);
      Alert.alert('Error', 'Failed to update category');
    }
  }, [user?.id, loadCategories]);

  /**
   * Handle delete category
   */
  const handleDeleteCategory = useCallback(async (categoryId, categoryName) => {
    Alert.alert(
      'Delete Category',
      `Are you sure you want to delete "${categoryName}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await categoryManagementService.deleteCustomCategory(user.id, categoryId);
              
              if (result.success) {
                await loadCategories();
                Alert.alert('Success', 'Category deleted successfully');
              } else if (result.canReassign) {
                // Show reassignment options
                Alert.alert(
                  'Category In Use',
                  `This category is used by ${result.transactionCount} transactions. Please reassign them first.`,
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Reassign',
                      onPress: () => navigation.navigate('BulkCategorization', {
                        mode: 'reassign',
                        fromCategory: categoryId
                      })
                    }
                  ]
                );
              } else {
                Alert.alert('Error', result.error || 'Failed to delete category');
              }
            } catch (error) {
              console.error('❌ Error deleting category:', error);
              Alert.alert('Error', 'Failed to delete category');
            }
          }
        }
      ]
    );
  }, [user?.id, loadCategories, navigation]);

  /**
   * Filter categories based on search query
   */
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  /**
   * Render category item
   */
  const renderCategoryItem = ({ item: category }) => {
    const stats = categoryStats[category.id];
    
    return (
      <TouchableOpacity
        style={styles.categoryItem}
        onPress={() => navigation.navigate('CategoryDetails', { categoryId: category.id })}
      >
        <View style={styles.categoryHeader}>
          <View style={styles.categoryIcon}>
            <Ionicons 
              name={category.icon} 
              size={24} 
              color={category.color} 
            />
          </View>
          
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{category.name}</Text>
            {category.description && (
              <Text style={styles.categoryDescription}>{category.description}</Text>
            )}
            
            {stats && (
              <View style={styles.categoryStats}>
                <Text style={styles.statText}>
                  {stats.totalTransactions} transactions • {formatAmount(stats.totalAmount)}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.categoryActions}>
            {category.canEdit && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => setEditingCategory(category)}
              >
                <Ionicons name="pencil" size={16} color={theme.colors.text} />
              </TouchableOpacity>
            )}
            
            {category.canDelete && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleDeleteCategory(category.id, category.name)}
              >
                <Ionicons name="trash" size={16} color={theme.colors.error} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {category.type === 'custom' && (
          <View style={styles.categoryBadge}>
            <Text style={styles.badgeText}>Custom</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading categories...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Categories</Text>
        
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => setShowCreateModal(true)}
        >
          <Ionicons name="add" size={24} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={theme.colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search categories..."
          placeholderTextColor={theme.colors.textSecondary}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('BulkCategorization')}
        >
          <Ionicons name="layers" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Bulk Categorize</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('CategoryInsights')}
        >
          <Ionicons name="analytics" size={20} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Insights</Text>
        </TouchableOpacity>
      </View>

      {/* Categories List */}
      <FlatList
        data={filteredCategories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Category Modal */}
      <CategoryFormModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateCategory}
        title="Create Category"
        theme={theme}
      />

      {/* Edit Category Modal */}
      <CategoryFormModal
        visible={!!editingCategory}
        onClose={() => setEditingCategory(null)}
        onSubmit={(data) => handleEditCategory(editingCategory?.id, data)}
        title="Edit Category"
        initialData={editingCategory}
        theme={theme}
      />
    </View>
  );
};

/**
 * Category Form Modal Component
 */
const CategoryFormModal = ({ visible, onClose, onSubmit, title, initialData, theme }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    icon: 'folder',
    color: '#B2BEC3',
    keywords: []
  });
  const [keywordInput, setKeywordInput] = useState('');

  const styles = createStyles(theme);

  // Available icons for categories
  const availableIcons = [
    'restaurant', 'car', 'bag', 'receipt', 'play-circle', 'medical',
    'school', 'person', 'home', 'airplane', 'card', 'gift',
    'fitness', 'cafe', 'library', 'business', 'construct', 'leaf'
  ];

  // Available colors
  const availableColors = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#FD79A8', '#A29BFE', '#74B9FF', '#636E72', '#B2BEC3',
    '#00B894', '#E17055', '#FDCB6E', '#6C5CE7', '#A0E7E5'
  ];

  useEffect(() => {
    if (initialData) {
      setFormData({
        name: initialData.name || '',
        description: initialData.description || '',
        icon: initialData.icon || 'folder',
        color: initialData.color || '#B2BEC3',
        keywords: initialData.keywords || []
      });
    } else {
      setFormData({
        name: '',
        description: '',
        icon: 'folder',
        color: '#B2BEC3',
        keywords: []
      });
    }
  }, [initialData, visible]);

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Category name is required');
      return;
    }

    onSubmit(formData);
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !formData.keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalCancelButton}>Cancel</Text>
          </TouchableOpacity>

          <Text style={styles.modalTitle}>{title}</Text>

          <TouchableOpacity onPress={handleSubmit}>
            <Text style={styles.modalSaveButton}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* Category Name */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Name *</Text>
            <TextInput
              style={styles.formInput}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="Enter category name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          {/* Description */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Description</Text>
            <TextInput
              style={[styles.formInput, styles.textArea]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Enter description (optional)"
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Icon</Text>
            <View style={styles.iconGrid}>
              {availableIcons.map((icon) => (
                <TouchableOpacity
                  key={icon}
                  style={[
                    styles.iconOption,
                    formData.icon === icon && styles.iconOptionSelected
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, icon }))}
                >
                  <Ionicons name={icon} size={24} color={formData.color} />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Color Selection */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Color</Text>
            <View style={styles.colorGrid}>
              {availableColors.map((color) => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color },
                    formData.color === color && styles.colorOptionSelected
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, color }))}
                />
              ))}
            </View>
          </View>

          {/* Keywords */}
          <View style={styles.formGroup}>
            <Text style={styles.formLabel}>Keywords</Text>
            <View style={styles.keywordInput}>
              <TextInput
                style={styles.keywordTextInput}
                value={keywordInput}
                onChangeText={setKeywordInput}
                placeholder="Add keyword"
                placeholderTextColor={theme.colors.textSecondary}
                onSubmitEditing={addKeyword}
              />
              <TouchableOpacity style={styles.addKeywordButton} onPress={addKeyword}>
                <Ionicons name="add" size={20} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>

            <View style={styles.keywordList}>
              {formData.keywords.map((keyword) => (
                <View key={keyword} style={styles.keywordTag}>
                  <Text style={styles.keywordText}>{keyword}</Text>
                  <TouchableOpacity onPress={() => removeKeyword(keyword)}>
                    <Ionicons name="close" size={16} color={theme.colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

export default CategoryManagementScreen;
