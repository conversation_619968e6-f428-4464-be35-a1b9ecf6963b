# 🚀 JiraniPay Complete Startup Guide

## Overview

This guide provides step-by-step instructions to get the complete JiraniPay system running locally, including the backend configuration service and mobile app with all security features working.

## 🔧 Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **Expo CLI** (for mobile app)
- **Git**

## 📋 Quick Start (5 Minutes)

### Step 1: Start Backend Configuration Service

```bash
# Navigate to backend directory
cd JiraniPay/backend

# Start the development server (auto-installs dependencies)
node start-dev.js
```

**Expected Output:**
```
🚀 Starting JiraniPay Backend Configuration Service...
📦 Installing dependencies... (if needed)
✅ Dependencies installed successfully

🔧 Configuration:
   - Environment: development
   - Port: 3001
   - API Version: v1
   - Supabase URL: Configured

🔄 Starting server...
✅ Server started on port 3001
🔐 Configuration service ready
```

### Step 2: Test Configuration Service

```bash
# In a new terminal, from JiraniPay root directory
node test-config-service.js
```

**Expected Output:**
```
🧪 Testing JiraniPay Configuration Service Integration...

🔍 Testing configuration endpoints...

1. Testing health endpoint...
   ✅ Health check passed
   📊 Status: healthy
   ⏱️ Uptime: 5s

2. Testing Supabase configuration...
   ✅ Supabase config retrieved
   🔗 URL: https://your-project.supabase.co
   🔑 Key: eyJ[REDACTED]...
   🌍 Environment: development

✅ Backend configuration service: Working
✅ Mobile app integration: Working
🚀 JiraniPay is ready for development!
```

### Step 3: Start Mobile App

```bash
# From JiraniPay root directory
npm start
# or
expo start
```

The app should now start without getting stuck at "Initializing Secure Configuration Service"!

## 🔍 Troubleshooting

### Problem: Backend won't start

**Solution:**
```bash
cd backend
npm install
node start-dev.js
```

### Problem: Port 3001 already in use

**Solution:**
```bash
# Kill process on port 3001
npx kill-port 3001

# Or use a different port
PORT=3002 node start-dev.js
```

### Problem: Mobile app still stuck initializing

**Check these steps:**

1. **Verify backend is running:**
   ```bash
   curl http://localhost:3001/api/v1/config/health
   ```

2. **Check mobile app logs:**
   - Look for "🔐 Initializing Secure Configuration Service..."
   - Should see "✅ Secure Configuration Service initialized"

3. **Test configuration loading:**
   ```bash
   node test-config-service.js
   ```

### Problem: Network errors in mobile app

**Solution:**
- Make sure your computer and mobile device are on the same network
- Check that localhost:3001 is accessible
- Try using your computer's IP address instead of localhost

## 📁 Project Structure

```
JiraniPay/
├── backend/                    # Backend configuration service
│   ├── src/
│   │   ├── routes/config.js   # Configuration endpoints
│   │   └── server.js          # Main server
│   ├── .env.development       # Development environment
│   └── start-dev.js           # Development startup script
├── services/                   # Mobile app services
│   ├── secureConfigService.js # Secure configuration loading
│   ├── secureSupabaseClient.js # Secure database client
│   ├── hsmService.js          # HSM integration
│   ├── secretsRotationService.js # Secrets rotation
│   └── secureWalletService.js # Secure wallet operations
├── config/
│   └── secureEnvironment.js   # Secure environment config
└── test-config-service.js     # Integration test script
```

## 🔐 Security Features Working

- ✅ **No hardcoded credentials** in mobile app
- ✅ **Secure configuration loading** from backend
- ✅ **HSM integration** for encryption
- ✅ **Automated secrets rotation** system
- ✅ **Encrypted wallet service** with transaction signing
- ✅ **Fallback mechanisms** when backend is unavailable

## 🧪 Testing

### Test Configuration Service
```bash
node test-config-service.js
```

### Test Security Implementation
```bash
node test-security-implementation.js
```

### Test Wallet Service
```bash
# In mobile app, check wallet loading
# Should see encrypted wallet data and transaction signatures
```

## 🌐 API Endpoints

The backend provides these configuration endpoints:

- `GET /api/v1/config/health` - Health check
- `POST /api/v1/config/supabase` - Supabase configuration
- `POST /api/v1/config/api_endpoints` - API endpoints configuration
- `POST /api/v1/config/hsm` - HSM configuration

### Example Request:
```bash
curl -X POST http://localhost:3001/api/v1/config/supabase \
  -H "Content-Type: application/json" \
  -H "X-Device-ID: test-device" \
  -H "X-App-Version: 1.0.0" \
  -H "X-Platform: test" \
  -d '{
    "environment": "development",
    "timestamp": 1640995200000,
    "deviceFingerprint": "test-device"
  }'
```

### Example Response:
```json
{
  "success": true,
  "config": {
    "url": "https://your-project.supabase.co",
    "anonKey": "eyJ[REDACTED]...",
    "version": "2.0",
    "environment": "development"
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "2.0"
}
```

## 🔄 Development Workflow

1. **Start backend:** `cd backend && node start-dev.js`
2. **Test configuration:** `node test-config-service.js`
3. **Start mobile app:** `npm start`
4. **Make changes** to mobile app or backend
5. **Test changes** with the test scripts
6. **Repeat**

## 🚀 Production Deployment

For production deployment:

1. **Deploy backend configuration service** to your cloud provider
2. **Update mobile app** to use production configuration endpoint
3. **Configure real secrets** in production environment
4. **Enable HSM** for production cryptographic operations
5. **Setup monitoring** and alerting

## 📞 Support

If you encounter issues:

1. **Check the logs** in both backend and mobile app
2. **Run the test scripts** to identify the problem
3. **Verify network connectivity** between services
4. **Check environment variables** and configuration

## 🎯 Success Indicators

When everything is working correctly, you should see:

- ✅ Backend starts on port 3001
- ✅ Configuration endpoints respond successfully
- ✅ Mobile app initializes without hanging
- ✅ Wallet service loads encrypted data
- ✅ All security features operational

**The app should now start and function normally with enterprise-grade security!**
