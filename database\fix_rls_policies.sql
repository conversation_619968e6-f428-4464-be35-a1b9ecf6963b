-- =====================================================
-- JiraniPay Security & Privacy - RLS Policy Fix
-- =====================================================
-- This script fixes all Row-Level Security policy violations
-- Run this in your Supabase SQL Editor

-- First, disable R<PERSON> temporarily to clean up
ALTER TABLE public.security_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.privacy_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users can insert own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users can update own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Users can delete own audit_logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Enable all operations for authenticated users on audit_logs" ON public.audit_logs;

DROP POLICY IF EXISTS "Users can view own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can insert own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can update own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Users can delete own security_settings" ON public.security_settings;
DROP POLICY IF EXISTS "Enable all operations for authenticated users on security_settings" ON public.security_settings;

DROP POLICY IF EXISTS "Users can view own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Users can insert own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Users can update own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Users can delete own privacy_settings" ON public.privacy_settings;
DROP POLICY IF EXISTS "Enable all operations for authenticated users on privacy_settings" ON public.privacy_settings;

-- Re-enable RLS
ALTER TABLE public.security_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Create comprehensive RLS policies that allow all operations for authenticated users

-- Security Settings Policies
CREATE POLICY "security_settings_select_policy" ON public.security_settings
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "security_settings_insert_policy" ON public.security_settings
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "security_settings_update_policy" ON public.security_settings
    FOR UPDATE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    ) WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "security_settings_delete_policy" ON public.security_settings
    FOR DELETE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

-- Privacy Settings Policies
CREATE POLICY "privacy_settings_select_policy" ON public.privacy_settings
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "privacy_settings_insert_policy" ON public.privacy_settings
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "privacy_settings_update_policy" ON public.privacy_settings
    FOR UPDATE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    ) WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "privacy_settings_delete_policy" ON public.privacy_settings
    FOR DELETE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

-- Audit Logs Policies
CREATE POLICY "audit_logs_select_policy" ON public.audit_logs
    FOR SELECT USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "audit_logs_insert_policy" ON public.audit_logs
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "audit_logs_update_policy" ON public.audit_logs
    FOR UPDATE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    ) WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

CREATE POLICY "audit_logs_delete_policy" ON public.audit_logs
    FOR DELETE USING (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR auth.uid()::text = user_id)
    );

-- Grant necessary permissions to authenticated users
GRANT ALL ON public.security_settings TO authenticated;
GRANT ALL ON public.privacy_settings TO authenticated;
GRANT ALL ON public.audit_logs TO authenticated;

-- Grant usage on sequences if they exist
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Verify the policies are working by checking if they exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename IN ('security_settings', 'privacy_settings', 'audit_logs')
ORDER BY tablename, policyname;

-- Test query to verify RLS is working (should return empty result for non-authenticated users)
-- This is just for verification - you can remove this section
/*
SELECT 'RLS Test - Security Settings' as test_name, count(*) as row_count 
FROM public.security_settings;

SELECT 'RLS Test - Privacy Settings' as test_name, count(*) as row_count 
FROM public.privacy_settings;

SELECT 'RLS Test - Audit Logs' as test_name, count(*) as row_count 
FROM public.audit_logs;
*/

-- Success message
SELECT 'RLS policies have been successfully updated for JiraniPay security and privacy tables!' as status;
