/**
 * Admin User Management Routes
 * Basic user and transaction management for administrators
 */

const express = require('express');
const { query, param, body, validationResult } = require('express-validator');
const { asyncHandler, ValidationError, NotFoundError } = require('../../middleware/errorHandler');
const { adminAuthMiddleware, requirePermission } = require('../../middleware/adminAuth');
const databaseService = require('../../services/database');
const walletService = require('../../services/walletService');
const auditService = require('../../services/auditService');
const logger = require('../../utils/logger');

const router = express.Router();

// Apply admin authentication to all routes
router.use(adminAuthMiddleware);

/**
 * @route   GET /api/v1/admin/users
 * @desc    Get all users with pagination and filtering
 * @access  Admin
 */
router.get('/', [
  requirePermission('users:read'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('status').optional().isIn(['active', 'inactive', 'suspended']).withMessage('Invalid status'),
  query('verified').optional().isBoolean().withMessage('Verified must be boolean'),
  query('sortBy').optional().isIn(['created_at', 'full_name', 'last_login']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      verified,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;
    const supabase = databaseService.getSupabase();

    // Build query
    let query = supabase
      .from('user_profiles')
      .select(`
        user_id,
        full_name,
        email,
        phone_number,
        country_code,
        is_verified,
        kyc_status,
        created_at,
        updated_at,
        last_login,
        wallets!inner(
          id,
          balance,
          currency,
          is_active
        )
      `)
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,phone_number.ilike.%${search}%`);
    }

    if (verified !== undefined) {
      query = query.eq('is_verified', verified);
    }

    if (status) {
      const isActive = status === 'active';
      query = query.eq('wallets.is_active', isActive);
    }

    const { data: users, error } = await query;

    if (error) {
      logger.error('Failed to fetch users:', error);
      throw new Error('Failed to retrieve users');
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('user_profiles')
      .select('*', { count: 'exact', head: true });

    if (search) {
      countQuery = countQuery.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,phone_number.ilike.%${search}%`);
    }

    if (verified !== undefined) {
      countQuery = countQuery.eq('is_verified', verified);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      logger.error('Failed to get user count:', countError);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    // Log admin action
    await auditService.logAdminAction(
      'view',
      req.admin.id,
      null,
      'users',
      'list',
      { page, limit, search, status, verified }
    );

    res.json({
      success: true,
      data: {
        users: users.map(user => ({
          userId: user.user_id,
          fullName: user.full_name,
          email: user.email,
          phoneNumber: user.phone_number,
          countryCode: user.country_code,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          walletBalance: user.wallets[0]?.balance || 0,
          walletCurrency: user.wallets[0]?.currency || 'UGX',
          walletActive: user.wallets[0]?.is_active || false,
          createdAt: user.created_at,
          lastLogin: user.last_login
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          pages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get users:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/users/:userId
 * @desc    Get detailed user information
 * @access  Admin
 */
router.get('/:userId', [
  requirePermission('users:read'),
  param('userId').isUUID().withMessage('Valid user ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { userId } = req.params;
    const supabase = databaseService.getSupabase();

    // Get user details
    const { data: user, error } = await supabase
      .from('user_profiles')
      .select(`
        *,
        wallets(*),
        transactions:transactions!from_user_id(
          id,
          type,
          amount,
          currency,
          status,
          created_at
        )
      `)
      .eq('user_id', userId)
      .single();

    if (error || !user) {
      throw new NotFoundError('User not found');
    }

    // Get recent transactions
    const { data: recentTransactions } = await supabase
      .from('transactions')
      .select('*')
      .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
      .order('created_at', { ascending: false })
      .limit(10);

    // Log admin action
    await auditService.logAdminAction(
      'view',
      req.admin.id,
      userId,
      'user',
      userId,
      { action: 'view_details' }
    );

    res.json({
      success: true,
      data: {
        user: {
          userId: user.user_id,
          fullName: user.full_name,
          email: user.email,
          phoneNumber: user.phone_number,
          countryCode: user.country_code,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
          lastLogin: user.last_login,
          wallets: user.wallets,
          recentTransactions: recentTransactions || []
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get user details:', error);
    throw error;
  }
}));

/**
 * @route   PUT /api/v1/admin/users/:userId/status
 * @desc    Update user status (suspend/activate)
 * @access  Admin
 */
router.put('/:userId/status', [
  requirePermission('users:update'),
  param('userId').isUUID().withMessage('Valid user ID is required'),
  body('action').isIn(['suspend', 'activate']).withMessage('Action must be suspend or activate'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { userId } = req.params;
    const { action, reason } = req.body;

    if (action === 'suspend') {
      await walletService.freezeWallet(userId, reason || 'Suspended by admin');
    } else {
      await walletService.unfreezeWallet(userId);
    }

    // Log admin action
    await auditService.logAdminAction(
      action,
      req.admin.id,
      userId,
      'user',
      userId,
      { reason, adminAction: action }
    );

    res.json({
      success: true,
      message: `User ${action}d successfully`,
      data: {
        userId,
        action,
        reason,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to update user status:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/users/:userId/transactions
 * @desc    Get user transaction history
 * @access  Admin
 */
router.get('/:userId/transactions', [
  requirePermission('transactions:read'),
  param('userId').isUUID().withMessage('Valid user ID is required'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('type').optional().isString().withMessage('Type must be a string'),
  query('status').optional().isString().withMessage('Status must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { userId } = req.params;
    const { page = 1, limit = 20, type, status } = req.query;

    const offset = (page - 1) * limit;
    const supabase = databaseService.getSupabase();

    let query = supabase
      .from('transactions')
      .select('*')
      .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (type) {
      query = query.eq('type', type);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data: transactions, error } = await query;

    if (error) {
      logger.error('Failed to fetch user transactions:', error);
      throw new Error('Failed to retrieve transactions');
    }

    // Get total count
    let countQuery = supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true })
      .or(`from_user_id.eq.${userId},to_user_id.eq.${userId}`);

    if (type) countQuery = countQuery.eq('type', type);
    if (status) countQuery = countQuery.eq('status', status);

    const { count } = await countQuery;
    const totalPages = Math.ceil((count || 0) / limit);

    // Log admin action
    await auditService.logAdminAction(
      'view',
      req.admin.id,
      userId,
      'transactions',
      'list',
      { userId, page, limit, type, status }
    );

    res.json({
      success: true,
      data: {
        transactions: transactions || [],
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          pages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get user transactions:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/users/stats
 * @desc    Get user statistics for dashboard
 * @access  Admin
 */
router.get('/stats/overview', [
  requirePermission('users:read')
], asyncHandler(async (req, res) => {
  try {
    const supabase = databaseService.getSupabase();

    // Get user statistics
    const { data: userStats } = await supabase.rpc('get_admin_user_stats').catch(() => ({ data: null }));

    // Fallback to basic queries if RPC function doesn't exist
    if (!userStats) {
      const [
        { count: totalUsers },
        { count: verifiedUsers },
        { count: activeUsers },
        { count: newUsersToday }
      ] = await Promise.all([
        supabase.from('user_profiles').select('*', { count: 'exact', head: true }),
        supabase.from('user_profiles').select('*', { count: 'exact', head: true }).eq('is_verified', true),
        supabase.from('wallets').select('*', { count: 'exact', head: true }).eq('is_active', true),
        supabase.from('user_profiles').select('*', { count: 'exact', head: true })
          .gte('created_at', new Date().toISOString().split('T')[0])
      ]);

      const stats = {
        totalUsers: totalUsers || 0,
        verifiedUsers: verifiedUsers || 0,
        activeUsers: activeUsers || 0,
        newUsersToday: newUsersToday || 0,
        verificationRate: totalUsers > 0 ? ((verifiedUsers / totalUsers) * 100).toFixed(1) : 0
      };

      return res.json({
        success: true,
        data: { stats }
      });
    }

    res.json({
      success: true,
      data: {
        stats: userStats
      }
    });
  } catch (error) {
    logger.error('Failed to get user statistics:', error);
    throw error;
  }
}));

module.exports = router;
