import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';
import { useTheme } from '../contexts/ThemeContext';
import LanguageSelector from './LanguageSelector';

/**
 * Translation Test Component
 * Displays various translated text to verify the reactive translation system
 * This component should update immediately when language changes
 */
const TranslationTest = () => {
  const { t, currentLanguage } = useLanguage();
  const { theme } = useTheme();

  const testKeys = [
    'common.loading',
    'common.error',
    'common.success',
    'dashboard.welcome',
    'dashboard.balance',
    'dashboard.quickActions',
    'dashboard.recentTransactions',
    'profile.title',
    'profile.verifiedAccount',
    'wallet.title',
    'wallet.balance',
    'wallet.sendMoney',
    'greetings.goodMorning',
    'greetings.goodAfternoon',
    'greetings.goodEvening'
  ];

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Translation Test - Current Language: {currentLanguage.toUpperCase()}
        </Text>
        <LanguageSelector 
          compact={true}
          style={styles.languageSelector}
        />
      </View>

      <View style={styles.testSection}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Translation Test Results:
        </Text>
        
        {testKeys.map((key, index) => (
          <View key={index} style={styles.testItem}>
            <Text style={[styles.keyText, { color: theme.colors.textSecondary }]}>
              {key}:
            </Text>
            <Text style={[styles.valueText, { color: theme.colors.text }]}>
              {t(key)}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.instructionSection}>
        <Text style={[styles.instructionTitle, { color: theme.colors.primary }]}>
          Test Instructions:
        </Text>
        <Text style={[styles.instructionText, { color: theme.colors.text }]}>
          1. Change the language using the selector above
        </Text>
        <Text style={[styles.instructionText, { color: theme.colors.text }]}>
          2. All text below should update immediately
        </Text>
        <Text style={[styles.instructionText, { color: theme.colors.text }]}>
          3. No English text should remain when switching languages
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  languageSelector: {
    marginLeft: 10,
  },
  testSection: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 15,
  },
  testItem: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 5,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  keyText: {
    fontSize: 12,
    fontFamily: 'monospace',
    flex: 1,
    marginRight: 10,
  },
  valueText: {
    fontSize: 14,
    fontWeight: '500',
    flex: 2,
  },
  instructionSection: {
    padding: 15,
    backgroundColor: '#F0F8FF',
    borderRadius: 10,
    marginTop: 20,
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  instructionText: {
    fontSize: 14,
    marginBottom: 5,
    lineHeight: 20,
  },
});

export default TranslationTest;
