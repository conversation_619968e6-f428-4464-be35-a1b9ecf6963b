import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Switch,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import currencyService from '../services/currencyService';
import scheduledPaymentsService from '../services/scheduledPaymentsService';
import notificationService from '../services/notificationService';
import spendingLimitService from '../services/spendingLimitService';

const WalletSettingsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { convertAndFormat, userCurrency } = useCurrencyContext();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    dailyLimit: '500000',
    monthlyLimit: '2000000',
    transactionNotifications: true,
    lowBalanceAlerts: true,
    spendingLimitAlerts: true,
    preferredCurrency: 'UGX',
    autoSaveEnabled: false,
    autoSaveAmount: '10000',
    autoSaveFrequency: 'weekly'
  });
  const [autoSaveGoals, setAutoSaveGoals] = useState([]);
  const [currencies, setCurrencies] = useState([]);

  useEffect(() => {
    loadSettings();
  }, []);

  // Reload settings when currency changes
  useEffect(() => {
    if (userCurrency) {
      loadSettings();
    }
  }, [userCurrency]);

  // Refresh auto-save goals when returning from AutomaticSavingsScreen
  useFocusEffect(
    React.useCallback(() => {
      const refreshAutoSaveGoals = async () => {
        try {
          const goals = scheduledPaymentsService.getAutoSaveGoals();
          setAutoSaveGoals(goals);
        } catch (error) {
          console.error('❌ Error refreshing auto-save goals:', error);
        }
      };

      refreshAutoSaveGoals();
    }, [])
  );

  const loadSettings = async () => {
    try {
      setLoading(true);

      // Initialize services
      await currencyService.initialize();
      await scheduledPaymentsService.initialize();
      await notificationService.initialize();
      await spendingLimitService.initialize();
      // Currency service already initialized above

      // Load currencies
      const supportedCurrencies = currencyService.getEastAfricanCurrencies();
      setCurrencies(supportedCurrencies);

      // Load auto-save goals
      const goals = scheduledPaymentsService.getAutoSaveGoals();
      setAutoSaveGoals(goals);

      // Load user preferences from services
      const userCurrency = await currencyService.getUserPreferredCurrency();

      // Safe notification settings loading with fallback
      let notificationSettings = {
        transactionNotifications: true,
        lowBalanceAlerts: true,
        spendingLimitAlerts: true
      };

      try {
        if (notificationService && typeof notificationService.getUserSettings === 'function') {
          notificationSettings = await notificationService.getUserSettings();
        } else {
          console.log('⚠️ Notification service getUserSettings not available, using defaults');
        }
      } catch (error) {
        console.log('⚠️ Error loading notification settings, using defaults:', error);
      }

      const spendingLimits = spendingLimitService.getUserLimits();

      setSettings(prev => ({
        ...prev,
        preferredCurrency: userCurrency,
        transactionNotifications: notificationSettings.transactionNotifications,
        lowBalanceAlerts: notificationSettings.lowBalanceAlerts,
        spendingLimitAlerts: notificationSettings.spendingLimitAlerts,
        dailyLimit: spendingLimits.dailyLimit.toString(),
        monthlyLimit: spendingLimits.monthlyLimit.toString()
      }));

    } catch (error) {
      console.error('❌ Error loading wallet settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key, value) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Validate spending limits
      const dailyLimit = parseFloat(settings.dailyLimit);
      const monthlyLimit = parseFloat(settings.monthlyLimit);

      if (isNaN(dailyLimit) || dailyLimit <= 0) {
        Alert.alert('Invalid Input', 'Please enter a valid daily limit');
        return;
      }

      if (isNaN(monthlyLimit) || monthlyLimit <= 0) {
        Alert.alert('Invalid Input', 'Please enter a valid monthly limit');
        return;
      }

      if (dailyLimit > monthlyLimit) {
        Alert.alert('Invalid Limits', 'Daily limit cannot be greater than monthly limit');
        return;
      }

      // Save currency preference with global updates
      await currencyService.setPreferredCurrency(settings.preferredCurrency);

      // Save notification settings
      await notificationService.updateUserSettings({
        transactionNotifications: settings.transactionNotifications,
        lowBalanceAlerts: settings.lowBalanceAlerts,
        spendingLimitAlerts: settings.spendingLimitAlerts
      });

      // Save spending limits
      await spendingLimitService.updateLimits({
        dailyLimit: dailyLimit,
        monthlyLimit: monthlyLimit
      });

      console.log('💾 All wallet settings saved successfully');

      Alert.alert(
        'Settings Saved',
        'Your wallet settings have been updated successfully. Changes will take effect immediately.',
        [{ text: 'OK', onPress: () => console.log('✅ Settings save confirmed') }]
      );
    } catch (error) {
      console.error('❌ Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const handleCreateAutoSaveGoal = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Navigate to AutomaticSavingsScreen for comprehensive goal creation
    navigation.navigate('AutomaticSavings', {
      setupType: 'automatic',
      source: 'walletSettings',
      recommendation: {
        id: 'wallet_auto_save_goal',
        title: '💰 Create Auto-Save Goal',
        description: 'Set up automatic savings to reach your financial goals'
      }
    });
  };

  const handleAnalytics = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (navigation && navigation.navigate) {
        navigation.navigate('Analytics');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert('Analytics', 'View your spending insights:\n\n• Category breakdown\n• Spending trends\n• Budget recommendations\n• Financial insights\n\nAnalytics feature coming soon!');
    }
  };

  const handleBudgetInsights = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (navigation && navigation.navigate) {
        // Navigate to dedicated Budget Insights screen
        navigation.navigate('BudgetInsights', {
          source: 'walletSettings'
        });
        console.log('✅ Navigating to Budget Insights');
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(
        'Budget Insights',
        'Unable to navigate to Budget Insights. Please ensure the screen is properly configured in navigation.'
      );
    }
  };

  const formatCurrency = (amount) => {
    try {
      return convertAndFormat(parseFloat(amount));
    } catch (error) {
      return convertAndFormat(parseFloat(amount));
    }
  };

  // This function is now handled by the currency context
  // Keeping for backward compatibility but using convertAndFormat
  const formatCurrencyFromUGX = (ugxAmount) => {
    return convertAndFormat(parseFloat(ugxAmount));
  };

  const renderSpendingLimits = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Spending Limits</Text>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Daily Limit</Text>
        <TextInput
          style={styles.input}
          value={settings.dailyLimit}
          onChangeText={(value) => handleSettingChange('dailyLimit', value)}
          placeholder="Enter daily spending limit"
          keyboardType="numeric"
          maxLength={10}
        />
        <Text style={styles.inputHelper}>
          Current: {convertAndFormat(settings.dailyLimit)}
        </Text>
      </View>
      
      <View style={styles.inputGroup}>
        <Text style={styles.inputLabel}>Monthly Limit</Text>
        <TextInput
          style={styles.input}
          value={settings.monthlyLimit}
          onChangeText={(value) => handleSettingChange('monthlyLimit', value)}
          placeholder="Enter monthly spending limit"
          keyboardType="numeric"
          maxLength={10}
        />
        <Text style={styles.inputHelper}>
          Current: {convertAndFormat(settings.monthlyLimit)}
        </Text>
      </View>
    </View>
  );

  const renderNotificationSettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Notification Preferences</Text>
      
      <View style={styles.settingItem}>
        <View style={styles.settingLeft}>
          <Ionicons name="notifications-outline" size={20} color={Colors.accent.gold} />
          <View style={styles.settingDetails}>
            <Text style={styles.settingTitle}>Transaction Notifications</Text>
            <Text style={styles.settingSubtitle}>Get notified for all transactions</Text>
          </View>
        </View>
        <Switch
          value={settings.transactionNotifications}
          onValueChange={(value) => handleSettingChange('transactionNotifications', value)}
          trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
          thumbColor={settings.transactionNotifications ? Colors.primary.main : Colors.neutral.white}
        />
      </View>
      
      <View style={styles.settingItem}>
        <View style={styles.settingLeft}>
          <Ionicons name="warning-outline" size={20} color={Colors.accent.coral} />
          <View style={styles.settingDetails}>
            <Text style={styles.settingTitle}>Low Balance Alerts</Text>
            <Text style={styles.settingSubtitle}>Alert when balance is low</Text>
          </View>
        </View>
        <Switch
          value={settings.lowBalanceAlerts}
          onValueChange={(value) => handleSettingChange('lowBalanceAlerts', value)}
          trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
          thumbColor={settings.lowBalanceAlerts ? Colors.primary.main : Colors.neutral.white}
        />
      </View>
      
      <View style={styles.settingItem}>
        <View style={styles.settingLeft}>
          <Ionicons name="speedometer-outline" size={20} color={Colors.secondary.savanna} />
          <View style={styles.settingDetails}>
            <Text style={styles.settingTitle}>Spending Limit Alerts</Text>
            <Text style={styles.settingSubtitle}>Alert when approaching limits</Text>
          </View>
        </View>
        <Switch
          value={settings.spendingLimitAlerts}
          onValueChange={(value) => handleSettingChange('spendingLimitAlerts', value)}
          trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
          thumbColor={settings.spendingLimitAlerts ? Colors.primary.main : Colors.neutral.white}
        />
      </View>
    </View>
  );

  const renderCurrencySettings = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Currency Preferences</Text>
      
      <Text style={styles.inputLabel}>Preferred Currency</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.currencySelector}>
        {currencies.map((currency) => (
          <TouchableOpacity
            key={currency.code}
            style={[
              styles.currencyOption,
              settings.preferredCurrency === currency.code && styles.selectedCurrencyOption
            ]}
            onPress={() => handleSettingChange('preferredCurrency', currency.code)}
          >
            <Text style={styles.currencyFlag}>{currency.flag}</Text>
            <Text style={[
              styles.currencyCode,
              settings.preferredCurrency === currency.code && styles.selectedCurrencyCode
            ]}>
              {currency.code}
            </Text>
            <Text style={[
              styles.currencyName,
              settings.preferredCurrency === currency.code && styles.selectedCurrencyName
            ]}>
              {currency.country}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderAutoSaveSettings = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Auto-Save Goals</Text>
        <TouchableOpacity onPress={handleCreateAutoSaveGoal} style={styles.addButton}>
          <Ionicons name="add-circle-outline" size={24} color={Colors.primary.main} />
        </TouchableOpacity>
      </View>
      
      {autoSaveGoals.length > 0 ? (
        autoSaveGoals.map((goal) => (
          <View key={goal.id} style={styles.goalItem}>
            <View style={styles.goalLeft}>
              <Ionicons name="wallet-outline" size={20} color={Colors.secondary.lake} />
              <View style={styles.goalDetails}>
                <Text style={styles.goalName}>{goal.name}</Text>
                <Text style={styles.goalProgress}>
                  {formatCurrency(goal.currentAmount)} of {formatCurrency(goal.targetAmount)}
                </Text>
              </View>
            </View>
            <Text style={styles.goalFrequency}>{goal.frequency}</Text>
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="wallet-outline" size={48} color={Colors.neutral.warmGray} />
          <Text style={styles.emptyTitle}>No Auto-Save Goals</Text>
          <Text style={styles.emptySubtitle}>Create goals to automatically save money</Text>
        </View>
      )}
    </View>
  );

  const renderAnalyticsSection = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Analytics & Insights</Text>

      <TouchableOpacity style={styles.analyticsItem} onPress={handleAnalytics}>
        <View style={styles.settingLeft}>
          <Ionicons name="analytics-outline" size={20} color={Colors.secondary.lake} />
          <View style={styles.settingDetails}>
            <Text style={styles.settingTitle}>Spending Analytics</Text>
            <Text style={styles.settingSubtitle}>View detailed spending insights and trends</Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
      </TouchableOpacity>

      <TouchableOpacity style={styles.analyticsItem} onPress={handleBudgetInsights}>
        <View style={styles.settingLeft}>
          <Ionicons name="pie-chart-outline" size={20} color={Colors.accent.gold} />
          <View style={styles.settingDetails}>
            <Text style={styles.settingTitle}>Budget Insights</Text>
            <Text style={styles.settingSubtitle}>AI-powered budget recommendations</Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Wallet Settings</Text>
        <TouchableOpacity onPress={handleSaveSettings} style={styles.saveButton}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSpendingLimits()}
        {renderNotificationSettings()}
        {renderCurrencySettings()}
        {renderAnalyticsSection()}
        {renderAutoSaveSettings()}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.primary.main,
    borderRadius: 8,
  },
  saveButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 12,
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: theme.colors.text,
    backgroundColor: theme.colors.surface,
  },
  inputHelper: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingDetails: {
    marginLeft: 12,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  currencySelector: {
    marginTop: 8,
  },
  currencyOption: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginRight: 8,
    minWidth: 80,
  },
  selectedCurrencyOption: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.light + '20',
  },
  currencyFlag: {
    fontSize: 20,
    marginBottom: 4,
  },
  currencyCode: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  selectedCurrencyCode: {
    color: Colors.primary.main,
  },
  currencyName: {
    fontSize: 10,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  selectedCurrencyName: {
    color: Colors.primary.main,
  },
  addButton: {
    padding: 4,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  goalLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  goalDetails: {
    marginLeft: 12,
    flex: 1,
  },
  goalName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  goalProgress: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  goalFrequency: {
    fontSize: 12,
    color: Colors.primary.main,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  analyticsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default WalletSettingsScreen;
