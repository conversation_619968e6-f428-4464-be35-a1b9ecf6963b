#!/usr/bin/env node

/**
 * Production Readiness Validation Script
 * Comprehensive validation of JiraniPay production environment
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');

// Color codes for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class ProductionValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.passed = [];
    this.envPath = path.join(__dirname, '../.env.production.local');
    this.env = {};
  }

  addError(message) {
    this.errors.push(message);
    log(`❌ ${message}`, 'red');
  }

  addWarning(message) {
    this.warnings.push(message);
    log(`⚠️  ${message}`, 'yellow');
  }

  addPassed(message) {
    this.passed.push(message);
    log(`✅ ${message}`, 'green');
  }

  loadEnvironment() {
    log('\n🔧 Loading Production Environment', 'cyan');
    log('=' .repeat(50), 'cyan');

    if (!fs.existsSync(this.envPath)) {
      this.addError('Production environment file not found: .env.production.local');
      this.addError('Run: node scripts/setup-production-env.js');
      return false;
    }

    try {
      const envContent = fs.readFileSync(this.envPath, 'utf8');
      const lines = envContent.split('\n');
      
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            this.env[key] = valueParts.join('=');
          }
        }
      }

      this.addPassed('Production environment file loaded');
      return true;
    } catch (error) {
      this.addError(`Failed to load environment file: ${error.message}`);
      return false;
    }
  }

  validateSecuritySettings() {
    log('\n🔒 Validating Security Settings', 'cyan');
    log('=' .repeat(50), 'cyan');

    // JWT Secret validation
    const jwtSecret = this.env.JWT_SECRET;
    if (!jwtSecret) {
      this.addError('JWT_SECRET is not set');
    } else if (jwtSecret.includes('CHANGE_THIS') || jwtSecret.includes('dev-')) {
      this.addError('JWT_SECRET contains placeholder or development values');
    } else if (jwtSecret.length < 64) {
      this.addError('JWT_SECRET must be at least 64 characters long');
    } else {
      this.addPassed('JWT_SECRET is properly configured');
    }

    // Encryption Key validation
    const encryptionKey = this.env.ENCRYPTION_KEY;
    if (!encryptionKey) {
      this.addError('ENCRYPTION_KEY is not set');
    } else if (encryptionKey.includes('CHANGE_THIS') || encryptionKey.includes('dev-')) {
      this.addError('ENCRYPTION_KEY contains placeholder or development values');
    } else {
      try {
        const decoded = Buffer.from(encryptionKey, 'base64');
        if (decoded.length !== 32) {
          this.addError('ENCRYPTION_KEY must be 32 bytes when base64 decoded');
        } else {
          this.addPassed('ENCRYPTION_KEY is properly configured');
        }
      } catch (error) {
        this.addError('ENCRYPTION_KEY is not valid base64');
      }
    }

    // Session Secret validation
    const sessionSecret = this.env.SESSION_SECRET;
    if (!sessionSecret) {
      this.addError('SESSION_SECRET is not set');
    } else if (sessionSecret.length < 64) {
      this.addError('SESSION_SECRET must be at least 64 characters long');
    } else {
      this.addPassed('SESSION_SECRET is properly configured');
    }

    // CORS validation
    const corsOrigin = this.env.CORS_ORIGIN;
    if (!corsOrigin) {
      this.addError('CORS_ORIGIN is not set');
    } else if (corsOrigin.includes('localhost') || corsOrigin.includes('127.0.0.1')) {
      this.addWarning('CORS_ORIGIN contains localhost - ensure this is intentional for production');
    } else {
      this.addPassed('CORS_ORIGIN is configured for production');
    }
  }

  async validateDatabaseConnection() {
    log('\n🗄️ Validating Database Connection', 'cyan');
    log('=' .repeat(50), 'cyan');

    const supabaseUrl = this.env.SUPABASE_URL;
    const supabaseKey = this.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl) {
      this.addError('SUPABASE_URL is not set');
      return false;
    }

    if (!supabaseKey) {
      this.addError('SUPABASE_SERVICE_ROLE_KEY is not set');
      return false;
    }

    if (supabaseUrl.includes('your-') || supabaseKey.includes('your-')) {
      this.addError('Supabase credentials contain placeholder values');
      return false;
    }

    try {
      const supabase = createClient(supabaseUrl, supabaseKey);
      
      // Test connection
      const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
      
      if (error) {
        this.addError(`Database connection failed: ${error.message}`);
        return false;
      }

      this.addPassed('Database connection successful');

      // Check if required tables exist
      const requiredTables = [
        'user_profiles', 'wallets', 'transactions', 'wallet_transactions',
        'bill_payments', 'admin_users', 'audit_logs', 'exchange_rates', 'system_settings'
      ];

      for (const table of requiredTables) {
        try {
          const { error: tableError } = await supabase.from(table).select('count').limit(1);
          if (tableError) {
            this.addError(`Required table missing or inaccessible: ${table}`);
          } else {
            this.addPassed(`Table exists: ${table}`);
          }
        } catch (error) {
          this.addError(`Error checking table ${table}: ${error.message}`);
        }
      }

      return true;
    } catch (error) {
      this.addError(`Database validation failed: ${error.message}`);
      return false;
    }
  }

  validatePaymentGateways() {
    log('\n💳 Validating Payment Gateway Configuration', 'cyan');
    log('=' .repeat(50), 'cyan');

    // MTN Mobile Money
    const mtnApiKey = this.env.MTN_API_KEY;
    const mtnApiSecret = this.env.MTN_API_SECRET;
    const mtnEnvironment = this.env.MTN_ENVIRONMENT;

    if (!mtnApiKey || mtnApiKey.includes('your-') || mtnApiKey.includes('dev-')) {
      this.addWarning('MTN Mobile Money API key not configured for production');
    } else {
      this.addPassed('MTN Mobile Money API key configured');
    }

    if (mtnEnvironment !== 'production') {
      this.addWarning('MTN environment is not set to production');
    } else {
      this.addPassed('MTN environment set to production');
    }

    // Airtel Money
    const airtelApiKey = this.env.AIRTEL_API_KEY;
    const airtelEnvironment = this.env.AIRTEL_ENVIRONMENT;

    if (!airtelApiKey || airtelApiKey.includes('your-') || airtelApiKey.includes('dev-')) {
      this.addWarning('Airtel Money API key not configured for production');
    } else {
      this.addPassed('Airtel Money API key configured');
    }

    if (airtelEnvironment !== 'production') {
      this.addWarning('Airtel environment is not set to production');
    } else {
      this.addPassed('Airtel environment set to production');
    }
  }

  validateExternalServices() {
    log('\n🌐 Validating External Services', 'cyan');
    log('=' .repeat(50), 'cyan');

    // SMS Service
    const smsApiKey = this.env.SMS_API_KEY;
    if (!smsApiKey || smsApiKey.includes('your-') || smsApiKey.includes('dev-')) {
      this.addWarning('SMS service not configured for production');
    } else {
      this.addPassed('SMS service configured');
    }

    // Email Service
    const emailApiKey = this.env.EMAIL_API_KEY;
    if (!emailApiKey || emailApiKey.includes('your-') || emailApiKey.includes('dev-')) {
      this.addWarning('Email service not configured for production');
    } else {
      this.addPassed('Email service configured');
    }

    // Monitoring (Sentry)
    const sentryDsn = this.env.SENTRY_DSN;
    if (!sentryDsn || sentryDsn.includes('your-')) {
      this.addWarning('Sentry error monitoring not configured');
    } else {
      this.addPassed('Sentry error monitoring configured');
    }

    // Exchange Rate API
    const exchangeRateApiKey = this.env.EXCHANGE_RATE_API_KEY;
    if (!exchangeRateApiKey || exchangeRateApiKey.includes('your-') || exchangeRateApiKey.includes('dev-')) {
      this.addWarning('Exchange rate API not configured');
    } else {
      this.addPassed('Exchange rate API configured');
    }
  }

  validateEnvironmentSettings() {
    log('\n⚙️ Validating Environment Settings', 'cyan');
    log('=' .repeat(50), 'cyan');

    // Node Environment
    const nodeEnv = this.env.NODE_ENV;
    if (nodeEnv !== 'production') {
      this.addError('NODE_ENV must be set to "production"');
    } else {
      this.addPassed('NODE_ENV set to production');
    }

    // Log Level
    const logLevel = this.env.LOG_LEVEL;
    if (logLevel === 'debug') {
      this.addWarning('LOG_LEVEL is set to debug - consider using "info" or "warn" for production');
    } else {
      this.addPassed('LOG_LEVEL appropriately configured');
    }

    // Port
    const port = this.env.PORT;
    if (!port || port === '3001') {
      this.addWarning('PORT should be set to 3000 for production');
    } else {
      this.addPassed('PORT configured');
    }

    // Rate Limiting
    const rateLimitMax = this.env.RATE_LIMIT_MAX_REQUESTS;
    if (!rateLimitMax || parseInt(rateLimitMax) > 1000) {
      this.addWarning('Rate limiting may be too permissive for production');
    } else {
      this.addPassed('Rate limiting configured');
    }
  }

  validateFilePermissions() {
    log('\n🔐 Validating File Permissions', 'cyan');
    log('=' .repeat(50), 'cyan');

    try {
      const stats = fs.statSync(this.envPath);
      const mode = stats.mode & parseInt('777', 8);
      
      if (mode > parseInt('600', 8)) {
        this.addWarning('Environment file permissions are too permissive (should be 600)');
      } else {
        this.addPassed('Environment file permissions are secure');
      }
    } catch (error) {
      this.addWarning('Could not check file permissions (Windows system)');
    }
  }

  generateReport() {
    log('\n📊 Production Readiness Report', 'cyan');
    log('=' .repeat(60), 'cyan');

    const total = this.passed.length + this.warnings.length + this.errors.length;
    const passRate = ((this.passed.length / total) * 100).toFixed(1);

    log(`\n📈 Summary:`, 'white');
    log(`   Total Checks: ${total}`, 'white');
    log(`   ✅ Passed: ${this.passed.length}`, 'green');
    log(`   ⚠️  Warnings: ${this.warnings.length}`, 'yellow');
    log(`   ❌ Errors: ${this.errors.length}`, 'red');
    log(`   📊 Pass Rate: ${passRate}%`, 'white');

    if (this.errors.length > 0) {
      log(`\n🚨 Critical Issues (Must Fix):`, 'red');
      this.errors.forEach((error, index) => {
        log(`   ${index + 1}. ${error}`, 'red');
      });
    }

    if (this.warnings.length > 0) {
      log(`\n⚠️  Warnings (Recommended):`, 'yellow');
      this.warnings.forEach((warning, index) => {
        log(`   ${index + 1}. ${warning}`, 'yellow');
      });
    }

    log(`\n🎯 Production Readiness Status:`, 'white');
    if (this.errors.length === 0) {
      if (this.warnings.length === 0) {
        log(`   🟢 READY FOR PRODUCTION`, 'green');
      } else {
        log(`   🟡 READY WITH WARNINGS`, 'yellow');
      }
    } else {
      log(`   🔴 NOT READY FOR PRODUCTION`, 'red');
    }

    return this.errors.length === 0;
  }

  async validate() {
    log('🏭 JiraniPay Production Readiness Validation', 'cyan');
    log('=' .repeat(60), 'cyan');

    if (!this.loadEnvironment()) {
      return false;
    }

    this.validateSecuritySettings();
    await this.validateDatabaseConnection();
    this.validatePaymentGateways();
    this.validateExternalServices();
    this.validateEnvironmentSettings();
    this.validateFilePermissions();

    return this.generateReport();
  }
}

// Main execution
async function main() {
  const validator = new ProductionValidator();
  const isReady = await validator.validate();

  if (isReady) {
    log('\n🚀 JiraniPay is ready for production deployment!', 'green');
    process.exit(0);
  } else {
    log('\n🛑 JiraniPay is NOT ready for production deployment.', 'red');
    log('   Please fix the critical issues above before deploying.', 'red');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🏭 JiraniPay Production Readiness Validation

Usage: node validate-production-readiness.js [options]

Options:
  --help, -h     Show this help message

This script validates:
✅ Security configuration (JWT, encryption, CORS)
✅ Database connectivity and schema
✅ Payment gateway configuration
✅ External service integration
✅ Environment settings
✅ File permissions

Exit codes:
  0 - Ready for production
  1 - Not ready for production
`);
  process.exit(0);
}

// Run validation
main().catch((error) => {
  log(`❌ Validation failed: ${error.message}`, 'red');
  process.exit(1);
});
