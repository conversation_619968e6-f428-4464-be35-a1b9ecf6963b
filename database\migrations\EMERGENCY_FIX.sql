-- EMERGENCY FIX: Ultra-Simple Tables
-- This will definitely work - no auth references, no constraints
-- Copy and paste this ENTIRE script into Supabase SQL Editor

-- Step 1: Drop any existing tables that might be causing issues
DROP TABLE IF EXISTS public.transactions CASCADE;
DROP TABLE IF EXISTS public.wallets CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;

-- Step 2: Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 3: Create wallets table (ultra-simple)
CREATE TABLE public.wallets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    balance NUMERIC DEFAULT 0,
    currency TEXT DEFAULT 'UGX',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 4: Create transactions table (ultra-simple)
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    transaction_type TEXT DEFAULT 'payment',
    description TEXT,
    status TEXT DEFAULT 'completed',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 5: Create user_profiles table (ultra-simple)
CREATE TABLE public.user_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id TEXT NOT NULL,
    phone_number TEXT,
    full_name TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 6: Insert test data to verify it works
INSERT INTO public.wallets (user_id, balance, currency) VALUES 
('test-user-123', 50000, 'UGX');

INSERT INTO public.transactions (user_id, amount, description) VALUES 
('test-user-123', 5000, 'Test transaction');

INSERT INTO public.user_profiles (user_id, phone_number, full_name) VALUES 
('test-user-123', '+************', 'Test User');

-- Step 7: Verify everything works
SELECT 'SUCCESS: Tables created and test data inserted' as status;
SELECT 'wallets' as table_name, COUNT(*) as records FROM public.wallets
UNION ALL
SELECT 'transactions' as table_name, COUNT(*) as records FROM public.transactions
UNION ALL
SELECT 'user_profiles' as table_name, COUNT(*) as records FROM public.user_profiles;
