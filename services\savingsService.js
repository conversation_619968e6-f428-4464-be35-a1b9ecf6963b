import { supabase } from './supabaseClient';
import authService from './authService';
import walletService from './walletService';

class SavingsService {
  constructor() {
    this.initialized = false;
    this.mockSavingsData = {};
  }

  async initialize() {
    try {
      console.log('💰 Initializing savings service...');
      this.initialized = true;
      console.log('✅ Savings service initialized');
    } catch (error) {
      console.error('❌ Error initializing savings service:', error);
      throw error;
    }
  }

  /**
   * Get user's savings accounts
   */
  async getSavingsAccounts() {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Check for mock savings data in development mode
      if (__DEV__ && this.mockSavingsData[user.id]) {
        console.log('🔧 Development Mode: Using mock savings data');
        return { success: true, data: this.mockSavingsData[user.id] };
      }

      try {
        const { data, error } = await supabase
          .from('payment_accounts')
          .select('*')
          .eq('user_id', user.id)
          .eq('account_type', 'savings')
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (error) throw error;

        // If no savings accounts exist, create a default one
        if (!data || data.length === 0) {
          const defaultSavings = await this.createSavingsAccount('General Savings', 'general');
          if (defaultSavings.success) {
            return { success: true, data: [defaultSavings.data] };
          }
          return defaultSavings;
        }

        return { success: true, data };
      } catch (dbError) {
        if (__DEV__) {
          console.log('⚠️ Database error in development, using mock savings');
          const mockSavings = this.createMockSavingsAccounts(user.id);
          this.mockSavingsData[user.id] = mockSavings;
          return { success: true, data: mockSavings };
        }
        throw dbError;
      }
    } catch (error) {
      console.error('❌ Error getting savings accounts:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new savings account
   */
  async createSavingsAccount(name, goalType = 'general', targetAmount = 0) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const savingsData = {
        user_id: user.id,
        account_type: 'savings',
        provider_name: 'JiraniPay Savings',
        account_number: this.generateSavingsAccountNumber(),
        account_name: name,
        currency: 'UGX',
        is_primary: false,
        is_active: true,
        balance: 0.00,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: {
          goal_type: goalType,
          target_amount: targetAmount,
          created_via: 'app',
          auto_save_enabled: false,
          auto_save_amount: 0,
          auto_save_frequency: 'weekly'
        }
      };

      if (__DEV__) {
        console.log('🔧 Development Mode: Creating mock savings account');
        const mockSavings = {
          id: `mock_savings_${user.id}_${Date.now()}`,
          ...savingsData,
          last_balance_update: new Date().toISOString()
        };

        if (!this.mockSavingsData[user.id]) {
          this.mockSavingsData[user.id] = [];
        }
        this.mockSavingsData[user.id].push(mockSavings);
        return { success: true, data: mockSavings };
      }

      try {
        const { data, error } = await supabase
          .from('payment_accounts')
          .insert([savingsData])
          .select()
          .single();

        if (error) throw error;

        console.log('✅ Savings account created successfully');
        return { success: true, data };
      } catch (dbError) {
        if (__DEV__) {
          const mockSavings = {
            id: `mock_savings_${user.id}_${Date.now()}`,
            ...savingsData,
            last_balance_update: new Date().toISOString()
          };
          if (!this.mockSavingsData[user.id]) {
            this.mockSavingsData[user.id] = [];
          }
          this.mockSavingsData[user.id].push(mockSavings);
          return { success: true, data: mockSavings };
        }
        throw dbError;
      }
    } catch (error) {
      console.error('❌ Error creating savings account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Transfer money from main wallet to savings
   */
  async transferToSavings(savingsAccountId, amount, description = 'Savings transfer') {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Validate amount
      const transferAmount = parseFloat(amount);
      if (transferAmount <= 0) {
        return { success: false, error: 'Invalid transfer amount' };
      }

      // Check main wallet balance
      const walletBalance = await walletService.getWalletBalance();
      if (!walletBalance.success) {
        return walletBalance;
      }

      if (walletBalance.data.balance < transferAmount) {
        return { success: false, error: 'Insufficient wallet balance' };
      }

      // Get savings account
      const savingsAccounts = await this.getSavingsAccounts();
      if (!savingsAccounts.success) {
        return savingsAccounts;
      }

      const savingsAccount = savingsAccounts.data.find(acc => acc.id === savingsAccountId);
      if (!savingsAccount) {
        return { success: false, error: 'Savings account not found' };
      }

      if (__DEV__) {
        console.log('🔧 Development Mode: Processing savings transfer');
        
        // Update mock wallet balance
        if (walletService.mockWallets && walletService.mockWallets[user.id]) {
          walletService.mockWallets[user.id].balance -= transferAmount;
        }

        // Update mock savings balance
        const savingsIndex = this.mockSavingsData[user.id].findIndex(acc => acc.id === savingsAccountId);
        if (savingsIndex !== -1) {
          this.mockSavingsData[user.id][savingsIndex].balance += transferAmount;
          this.mockSavingsData[user.id][savingsIndex].last_balance_update = new Date().toISOString();
        }

        return {
          success: true,
          data: {
            transaction_id: `mock_transfer_${Date.now()}`,
            amount: transferAmount,
            from_account: walletBalance.data.account_number,
            to_account: savingsAccount.account_number,
            description,
            timestamp: new Date().toISOString()
          }
        };
      }

      // In production, this would involve database transactions
      // For now, we'll simulate the transfer
      try {
        // Deduct from main wallet
        const walletUpdate = await walletService.updateWalletBalance(transferAmount, 'subtract');
        if (!walletUpdate.success) {
          return walletUpdate;
        }

        // Add to savings account
        const { data, error } = await supabase
          .from('payment_accounts')
          .update({ 
            balance: savingsAccount.balance + transferAmount,
            last_balance_update: new Date().toISOString()
          })
          .eq('id', savingsAccountId)
          .select()
          .single();

        if (error) throw error;

        console.log('✅ Savings transfer completed successfully');
        return {
          success: true,
          data: {
            transaction_id: `transfer_${Date.now()}`,
            amount: transferAmount,
            from_account: walletBalance.data.account_number,
            to_account: savingsAccount.account_number,
            description,
            timestamp: new Date().toISOString()
          }
        };
      } catch (dbError) {
        console.error('❌ Database error during transfer:', dbError);
        return { success: false, error: 'Transfer failed' };
      }
    } catch (error) {
      console.error('❌ Error transferring to savings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Withdraw money from savings to main wallet
   */
  async withdrawFromSavings(savingsAccountId, amount, description = 'Savings withdrawal') {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const withdrawAmount = parseFloat(amount);
      if (withdrawAmount <= 0) {
        return { success: false, error: 'Invalid withdrawal amount' };
      }

      // Get savings account
      const savingsAccounts = await this.getSavingsAccounts();
      if (!savingsAccounts.success) {
        return savingsAccounts;
      }

      const savingsAccount = savingsAccounts.data.find(acc => acc.id === savingsAccountId);
      if (!savingsAccount) {
        return { success: false, error: 'Savings account not found' };
      }

      if (savingsAccount.balance < withdrawAmount) {
        return { success: false, error: 'Insufficient savings balance' };
      }

      if (__DEV__) {
        console.log('🔧 Development Mode: Processing savings withdrawal');
        
        // Update mock savings balance
        const savingsIndex = this.mockSavingsData[user.id].findIndex(acc => acc.id === savingsAccountId);
        if (savingsIndex !== -1) {
          this.mockSavingsData[user.id][savingsIndex].balance -= withdrawAmount;
          this.mockSavingsData[user.id][savingsIndex].last_balance_update = new Date().toISOString();
        }

        // Update mock wallet balance
        if (walletService.mockWallets && walletService.mockWallets[user.id]) {
          walletService.mockWallets[user.id].balance += withdrawAmount;
        }

        return {
          success: true,
          data: {
            transaction_id: `mock_withdrawal_${Date.now()}`,
            amount: withdrawAmount,
            from_account: savingsAccount.account_number,
            to_account: 'Main Wallet',
            description,
            timestamp: new Date().toISOString()
          }
        };
      }

      // Production withdrawal logic would go here
      return { success: true, data: { message: 'Withdrawal completed' } };
    } catch (error) {
      console.error('❌ Error withdrawing from savings:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get savings summary and statistics
   */
  async getSavingsSummary() {
    try {
      const savingsAccounts = await this.getSavingsAccounts();
      if (!savingsAccounts.success) {
        return savingsAccounts;
      }

      const accounts = savingsAccounts.data;
      const totalSavings = accounts.reduce((sum, acc) => sum + acc.balance, 0);
      const totalGoals = accounts.filter(acc => acc.metadata?.target_amount > 0).length;
      const completedGoals = accounts.filter(acc => 
        acc.metadata?.target_amount > 0 && acc.balance >= acc.metadata.target_amount
      ).length;

      return {
        success: true,
        data: {
          total_savings: totalSavings,
          total_accounts: accounts.length,
          total_goals: totalGoals,
          completed_goals: completedGoals,
          accounts: accounts
        }
      };
    } catch (error) {
      console.error('❌ Error getting savings summary:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate savings account number
   */
  generateSavingsAccountNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `SAV${timestamp}${random}`;
  }

  /**
   * Create mock savings accounts for development
   */
  createMockSavingsAccounts(userId) {
    return [
      {
        id: `mock_savings_general_${userId}`,
        user_id: userId,
        account_type: 'savings',
        provider_name: 'JiraniPay Savings',
        account_number: 'SAV001001',
        account_name: 'General Savings',
        currency: 'UGX',
        balance: 25000,
        is_active: true,
        created_at: new Date().toISOString(),
        metadata: {
          goal_type: 'general',
          target_amount: 100000,
          auto_save_enabled: false
        }
      },
      {
        id: `mock_savings_emergency_${userId}`,
        user_id: userId,
        account_type: 'savings',
        provider_name: 'JiraniPay Savings',
        account_number: 'SAV001002',
        account_name: 'Emergency Fund',
        currency: 'UGX',
        balance: 75000,
        is_active: true,
        created_at: new Date().toISOString(),
        metadata: {
          goal_type: 'emergency',
          target_amount: 500000,
          auto_save_enabled: true,
          auto_save_amount: 25000,
          auto_save_frequency: 'weekly'
        }
      }
    ];
  }
}

// Export singleton instance
const savingsService = new SavingsService();
export default savingsService;
