#!/usr/bin/env node

/**
 * Network Issues Diagnostic Script for JiraniPay
 * 
 * This script helps diagnose network request failures that occur
 * after successful authentication, particularly focusing on:
 * - User preferences loading
 * - Profile picture uploads
 * - Supabase connectivity
 * - Authentication token issues
 */

console.log('🔍 JiraniPay Network Issues Diagnostic');
console.log('=====================================\n');

// Mock environment for testing
global.__DEV__ = false;
global.console = console;

// Mock React Native modules
const mockNetInfo = {
  fetch: async () => ({
    isConnected: true,
    type: 'wifi',
    details: { strength: 80 }
  }),
  addEventListener: (callback) => {
    console.log('📱 NetInfo: Event listener added');
    return () => console.log('📱 NetInfo: Event listener removed');
  }
};

const mockAsyncStorage = {
  getItem: async (key) => {
    console.log(`📱 AsyncStorage.getItem: ${key}`);
    return null;
  },
  setItem: async (key, value) => {
    console.log(`📱 AsyncStorage.setItem: ${key}`);
    return true;
  }
};

// Mock Supabase client
const mockSupabase = {
  auth: {
    getSession: async () => {
      console.log('🔐 Supabase: Getting session...');
      return {
        data: {
          session: {
            access_token: 'mock_token_12345',
            expires_at: Math.floor(Date.now() / 1000) + 3600,
            user: { id: 'mock_user_id' }
          }
        },
        error: null
      };
    }
  },
  from: (table) => ({
    select: (columns) => ({
      eq: (column, value) => ({
        single: async () => {
          console.log(`🗄️ Supabase: SELECT ${columns} FROM ${table} WHERE ${column} = ${value}`);
          
          // Simulate different scenarios
          if (table === 'user_preferences') {
            // Simulate "no rows found" error
            return {
              data: null,
              error: { code: 'PGRST116', message: 'No rows found' }
            };
          }
          
          return {
            data: { id: value, test_field: 'test_value' },
            error: null
          };
        }
      })
    }),
    upsert: (data) => ({
      select: () => ({
        single: async () => {
          console.log(`🗄️ Supabase: UPSERT INTO ${table}:`, data);
          return {
            data: { ...data, id: 'mock_id' },
            error: null
          };
        }
      })
    })
  }),
  storage: {
    from: (bucket) => ({
      upload: async (fileName, fileData, options) => {
        console.log(`📤 Supabase Storage: Uploading ${fileName} to ${bucket}`);
        console.log(`📤 File size: ${fileData?.length || 'unknown'} bytes`);
        console.log(`📤 Options:`, options);
        
        // Simulate upload success
        return {
          data: { path: fileName },
          error: null
        };
      },
      getPublicUrl: (fileName) => {
        console.log(`🔗 Supabase Storage: Getting public URL for ${fileName}`);
        return {
          data: { publicUrl: `https://mock-storage.supabase.co/${fileName}` }
        };
      }
    })
  }
};

// Test scenarios
const testScenarios = [
  {
    name: 'User Preferences Loading',
    description: 'Test loading user preferences after authentication',
    test: async () => {
      console.log('🧪 Testing user preferences loading...');
      
      try {
        // Simulate getting user preferences
        const result = await mockSupabase
          .from('user_preferences')
          .select('*')
          .eq('id', 'test_user_123')
          .single();
        
        if (result.error && result.error.code === 'PGRST116') {
          console.log('⚠️ No preferences found - this is expected for new users');
          return { success: true, needsCreation: true };
        }
        
        if (result.error) {
          throw new Error(result.error.message);
        }
        
        console.log('✅ Preferences loaded successfully');
        return { success: true, data: result.data };
        
      } catch (error) {
        console.error('❌ Preferences loading failed:', error.message);
        return { success: false, error: error.message };
      }
    }
  },
  
  {
    name: 'Authentication Token Validation',
    description: 'Test authentication token retrieval and validation',
    test: async () => {
      console.log('🧪 Testing authentication token validation...');
      
      try {
        const sessionResult = await mockSupabase.auth.getSession();
        
        if (sessionResult.error) {
          throw new Error(sessionResult.error.message);
        }
        
        const session = sessionResult.data.session;
        if (!session?.access_token) {
          throw new Error('No access token found in session');
        }
        
        // Check token expiry
        const now = Math.floor(Date.now() / 1000);
        const expiresAt = session.expires_at;
        const timeUntilExpiry = expiresAt - now;
        
        console.log(`🔐 Token expires in ${timeUntilExpiry} seconds`);
        
        if (timeUntilExpiry <= 0) {
          throw new Error('Token has expired');
        }
        
        if (timeUntilExpiry < 300) { // Less than 5 minutes
          console.log('⚠️ Token expires soon, should refresh');
        }
        
        console.log('✅ Authentication token is valid');
        return { success: true, token: session.access_token, expiresIn: timeUntilExpiry };
        
      } catch (error) {
        console.error('❌ Token validation failed:', error.message);
        return { success: false, error: error.message };
      }
    }
  },
  
  {
    name: 'Profile Picture Upload',
    description: 'Test profile picture upload functionality',
    test: async () => {
      console.log('🧪 Testing profile picture upload...');
      
      try {
        // Simulate image data
        const mockImageData = new Uint8Array([255, 216, 255, 224]); // JPEG header
        const fileName = `profile-pictures/test_user_123-${Date.now()}.jpg`;
        
        // Test upload
        const uploadResult = await mockSupabase.storage
          .from('user-uploads')
          .upload(fileName, mockImageData, {
            contentType: 'image/jpeg',
            upsert: true
          });
        
        if (uploadResult.error) {
          throw new Error(uploadResult.error.message);
        }
        
        // Test getting public URL
        const urlResult = mockSupabase.storage
          .from('user-uploads')
          .getPublicUrl(fileName);
        
        console.log('✅ Profile picture upload successful');
        return { 
          success: true, 
          uploadData: uploadResult.data,
          publicUrl: urlResult.data.publicUrl
        };
        
      } catch (error) {
        console.error('❌ Profile picture upload failed:', error.message);
        return { success: false, error: error.message };
      }
    }
  },
  
  {
    name: 'Network Connectivity',
    description: 'Test network connectivity and request handling',
    test: async () => {
      console.log('🧪 Testing network connectivity...');
      
      try {
        // Test network status
        const networkState = await mockNetInfo.fetch();
        console.log('🌐 Network state:', networkState);
        
        if (!networkState.isConnected) {
          throw new Error('No network connection');
        }
        
        // Test HTTP request simulation
        const testUrl = 'https://api.example.com/test';
        console.log(`🌐 Simulating HTTP request to ${testUrl}`);
        
        // Simulate request with timeout
        const requestPromise = new Promise((resolve) => {
          setTimeout(() => {
            resolve({ status: 200, data: { success: true } });
          }, 1000);
        });
        
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Request timeout'));
          }, 5000);
        });
        
        const result = await Promise.race([requestPromise, timeoutPromise]);
        
        console.log('✅ Network connectivity test successful');
        return { success: true, networkState, requestResult: result };
        
      } catch (error) {
        console.error('❌ Network connectivity test failed:', error.message);
        return { success: false, error: error.message };
      }
    }
  }
];

// Run diagnostic tests
async function runDiagnostics() {
  console.log('🚀 Starting network diagnostics...\n');
  
  const results = [];
  
  for (const scenario of testScenarios) {
    console.log(`📋 ${scenario.name}`);
    console.log(`   ${scenario.description}`);
    console.log('   ' + '─'.repeat(50));
    
    try {
      const result = await scenario.test();
      results.push({
        name: scenario.name,
        success: result.success,
        data: result,
        error: result.error
      });
      
      if (result.success) {
        console.log('   ✅ PASSED\n');
      } else {
        console.log(`   ❌ FAILED: ${result.error}\n`);
      }
      
    } catch (error) {
      console.log(`   ❌ EXCEPTION: ${error.message}\n`);
      results.push({
        name: scenario.name,
        success: false,
        error: error.message
      });
    }
  }
  
  // Summary
  console.log('📊 DIAGNOSTIC SUMMARY');
  console.log('=====================');
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}\n`);
  
  if (failed > 0) {
    console.log('🔧 FAILED TESTS:');
    results.filter(r => !r.success).forEach(result => {
      console.log(`   ❌ ${result.name}: ${result.error}`);
    });
    console.log();
  }
  
  // Recommendations
  console.log('💡 RECOMMENDATIONS:');
  console.log('===================');
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Network functionality appears to be working correctly.');
    console.log('   If you\'re still experiencing issues, check:');
    console.log('   - Real device network connectivity');
    console.log('   - Supabase project configuration');
    console.log('   - Environment variables');
  } else {
    console.log('⚠️ Some tests failed. Common solutions:');
    console.log('   1. Check internet connectivity');
    console.log('   2. Verify Supabase configuration in .env files');
    console.log('   3. Ensure authentication tokens are valid');
    console.log('   4. Check CORS settings for your domain');
    console.log('   5. Verify API endpoints are accessible');
  }
  
  console.log('\n🔧 NEXT STEPS:');
  console.log('   1. Run: npm run test-network');
  console.log('   2. Check: npm run debug-env');
  console.log('   3. Validate: npm run validate-security');
  
  process.exit(failed > 0 ? 1 : 0);
}

// Run the diagnostics
runDiagnostics().catch(error => {
  console.error('❌ Diagnostic script failed:', error);
  process.exit(1);
});
