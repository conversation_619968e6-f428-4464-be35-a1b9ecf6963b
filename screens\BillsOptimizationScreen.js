import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  TextInput,
  Modal,
  Switch,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import billsManagementService from '../services/billsManagementService';
import currencyService from '../services/currencyService';

const BillsOptimizationScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { convertAndFormat } = useCurrencyContext();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [trackedBills, setTrackedBills] = useState([]);
  const [subscriptions, setSubscriptions] = useState([]);
  const [billsSummary, setBillsSummary] = useState(null);
  const [auditRecommendations, setAuditRecommendations] = useState([]);
  
  // Modal states
  const [addBillModalVisible, setAddBillModalVisible] = useState(false);
  const [addSubscriptionModalVisible, setAddSubscriptionModalVisible] = useState(false);
  const [auditModalVisible, setAuditModalVisible] = useState(false);
  
  // Form states for adding bills
  const [billName, setBillName] = useState('');
  const [billProvider, setBillProvider] = useState('');
  const [billAccountNumber, setBillAccountNumber] = useState('');
  const [billCategory, setBillCategory] = useState('utilities');
  const [billAmount, setBillAmount] = useState('');
  const [billDueDate, setBillDueDate] = useState('15');
  const [billReminderDays, setBillReminderDays] = useState('3');
  
  // Form states for adding subscriptions
  const [subName, setSubName] = useState('');
  const [subProvider, setSubProvider] = useState('');
  const [subCategory, setSubCategory] = useState('entertainment');
  const [subAmount, setSubAmount] = useState('');
  const [subBillingCycle, setSubBillingCycle] = useState('monthly');
  const [subStartDate, setSubStartDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    loadBillsData();
    
    // Handle navigation from Budget Insights with recommendation data
    if (route?.params?.recommendation) {
      const { recommendation } = route.params;
      handleRecommendationSetup(recommendation);
    }
  }, [route?.params]);

  const loadBillsData = async () => {
    try {
      setLoading(true);
      
      // Initialize service
      if (!billsManagementService.isInitialized) {
        await billsManagementService.initialize();
      }
      
      // Load data
      const bills = billsManagementService.getTrackedBills();
      const subs = billsManagementService.getSubscriptions();
      const summary = billsManagementService.getBillsSummary();
      const recommendations = billsManagementService.getSubscriptionAuditRecommendations();
      
      setTrackedBills(bills);
      setSubscriptions(subs);
      setBillsSummary(summary);
      setAuditRecommendations(recommendations);
      
    } catch (error) {
      console.error('❌ Error loading bills data:', error);
      Alert.alert('Error', 'Failed to load bills data');
    } finally {
      setLoading(false);
    }
  };

  const handleRecommendationSetup = (recommendation) => {
    if (recommendation.id === 'bills_optimization') {
      // Show audit modal after a short delay
      setTimeout(() => {
        setAuditModalVisible(true);
      }, 500);
    }
  };

  const formatCurrency = (amount) => {
    try {
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return convertAndFormat(numAmount);
    } catch (error) {
      const numAmount = typeof amount === 'number' ? amount : parseFloat(amount) || 0;
      return convertAndFormat(numAmount);
    }
  };

  const handleAddBill = async () => {
    if (!billName.trim() || !billProvider.trim() || !billAmount) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(billAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const billData = {
        name: billName.trim(),
        provider: billProvider.trim(),
        accountNumber: billAccountNumber.trim(),
        category: billCategory,
        averageAmount: amount,
        dueDate: parseInt(billDueDate),
        reminderDays: parseInt(billReminderDays)
      };

      const result = await billsManagementService.addTrackedBill(billData);

      if (result.success) {
        Alert.alert('Success', `Bill "${billName}" added to tracking!`);
        setAddBillModalVisible(false);
        resetBillForm();
        loadBillsData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Add bill error:', error);
      Alert.alert('Error', 'Failed to add bill');
    }
  };

  const handleAddSubscription = async () => {
    if (!subName.trim() || !subProvider.trim() || !subAmount) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    const amount = parseFloat(subAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      
      const subscriptionData = {
        name: subName.trim(),
        provider: subProvider.trim(),
        category: subCategory,
        monthlyAmount: amount,
        billingCycle: subBillingCycle,
        startDate: subStartDate
      };

      const result = await billsManagementService.addSubscription(subscriptionData);

      if (result.success) {
        Alert.alert('Success', `Subscription "${subName}" added to tracking!`);
        setAddSubscriptionModalVisible(false);
        resetSubscriptionForm();
        loadBillsData(); // Refresh data
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('❌ Add subscription error:', error);
      Alert.alert('Error', 'Failed to add subscription');
    }
  };

  const resetBillForm = () => {
    setBillName('');
    setBillProvider('');
    setBillAccountNumber('');
    setBillCategory('utilities');
    setBillAmount('');
    setBillDueDate('15');
    setBillReminderDays('3');
  };

  const resetSubscriptionForm = () => {
    setSubName('');
    setSubProvider('');
    setSubCategory('entertainment');
    setSubAmount('');
    setSubBillingCycle('monthly');
    setSubStartDate(new Date());
  };

  const handleDateChange = (event, selectedDate) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
    
    if (selectedDate) {
      setSubStartDate(selectedDate);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Bills Optimization</Text>
        <Text style={styles.headerSubtitle}>Manage bills & subscriptions</Text>
      </View>
      <View style={styles.placeholder} />
    </View>
  );

  const renderSummaryCard = () => (
    <LinearGradient
      colors={Colors.gradients.prosperity}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.summaryCard}
    >
      <View style={styles.summaryHeader}>
        <Text style={styles.summaryTitle}>Monthly Bills & Subscriptions</Text>
        <Ionicons name="receipt-outline" size={24} color={Colors.neutral.white} />
      </View>
      <Text style={styles.summaryAmount}>
        {formatCurrency(billsSummary?.totalMonthlyAmount || 0)}
      </Text>
      <View style={styles.summaryStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{billsSummary?.totalBills || 0}</Text>
          <Text style={styles.statLabel}>Bills</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{billsSummary?.totalSubscriptions || 0}</Text>
          <Text style={styles.statLabel}>Subscriptions</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{billsSummary?.billsDueSoon || 0}</Text>
          <Text style={styles.statLabel}>Due Soon</Text>
        </View>
      </View>
    </LinearGradient>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActions}>
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setAddBillModalVisible(true)}
      >
        <Ionicons name="add-circle-outline" size={24} color={Colors.primary.main} />
        <Text style={styles.quickActionText}>Add Bill</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setAddSubscriptionModalVisible(true)}
      >
        <Ionicons name="repeat-outline" size={24} color={Colors.secondary.savanna} />
        <Text style={styles.quickActionText}>Add Subscription</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => setAuditModalVisible(true)}
      >
        <Ionicons name="analytics-outline" size={24} color={Colors.accent.amber} />
        <Text style={styles.quickActionText}>Audit</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.quickActionButton}
        onPress={() => navigation.navigate('BillPayment')}
      >
        <Ionicons name="card-outline" size={24} color={Colors.status.success} />
        <Text style={styles.quickActionText}>Pay Bills</Text>
      </TouchableOpacity>
    </View>
  );

  const renderTrackedBills = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Tracked Bills</Text>
      {trackedBills.length > 0 ? (
        trackedBills.map((bill) => (
          <View key={bill.id} style={styles.billCard}>
            <View style={styles.billHeader}>
              <View style={styles.billInfo}>
                <Text style={styles.billName}>{bill.name}</Text>
                <Text style={styles.billProvider}>{bill.provider}</Text>
              </View>
              <View style={styles.billAmount}>
                <Text style={styles.billAmountText}>{formatCurrency(bill.averageAmount)}</Text>
                <Text style={styles.billDueText}>Due: {bill.dueDate}th</Text>
              </View>
            </View>
            <View style={styles.billCategory}>
              <Text style={styles.billCategoryText}>{bill.category}</Text>
            </View>
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="receipt-outline" size={48} color={Colors.neutral.warmGray} />
          <Text style={styles.emptyStateText}>No bills tracked yet</Text>
          <TouchableOpacity 
            style={styles.createFirstButton}
            onPress={() => setAddBillModalVisible(true)}
          >
            <Text style={styles.createFirstButtonText}>Add Your First Bill</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderSubscriptions = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Active Subscriptions</Text>
      {subscriptions.length > 0 ? (
        subscriptions.map((subscription) => (
          <View key={subscription.id} style={styles.subscriptionCard}>
            <View style={styles.subscriptionHeader}>
              <View style={styles.subscriptionInfo}>
                <Text style={styles.subscriptionName}>{subscription.name}</Text>
                <Text style={styles.subscriptionProvider}>{subscription.provider}</Text>
              </View>
              <View style={styles.subscriptionAmount}>
                <Text style={styles.subscriptionAmountText}>{formatCurrency(subscription.monthlyAmount)}</Text>
                <Text style={styles.subscriptionCycleText}>{subscription.billingCycle}</Text>
              </View>
            </View>
            <View style={styles.subscriptionCategory}>
              <Text style={styles.subscriptionCategoryText}>{subscription.category}</Text>
            </View>
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="repeat-outline" size={48} color={Colors.neutral.warmGray} />
          <Text style={styles.emptyStateText}>No subscriptions tracked yet</Text>
          <TouchableOpacity 
            style={styles.createFirstButton}
            onPress={() => setAddSubscriptionModalVisible(true)}
          >
            <Text style={styles.createFirstButtonText}>Add Your First Subscription</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading bills optimization...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSummaryCard()}
        {renderQuickActions()}
        {renderTrackedBills()}
        {renderSubscriptions()}
        
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Add Bill Modal */}
      <Modal
        visible={addBillModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setAddBillModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add Bill to Track</Text>
            
            <TextInput
              style={styles.textInput}
              placeholder="Bill name (e.g., UMEME Electricity)"
              value={billName}
              onChangeText={setBillName}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Provider (e.g., UMEME)"
              value={billProvider}
              onChangeText={setBillProvider}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Account number (optional)"
              value={billAccountNumber}
              onChangeText={setBillAccountNumber}
            />
            
            <TextInput
              style={styles.textInput}
              placeholder="Average monthly amount"
              value={billAmount}
              onChangeText={setBillAmount}
              keyboardType="numeric"
            />
            
            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Category:</Text>
              <View style={styles.categoryButtons}>
                {['utilities', 'internet', 'insurance', 'rent', 'other'].map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[styles.categoryButton, billCategory === category && styles.categoryButtonActive]}
                    onPress={() => setBillCategory(category)}
                  >
                    <Text style={[styles.categoryButtonText, billCategory === category && styles.categoryButtonTextActive]}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.modalActions}>
              <TouchableOpacity 
                style={styles.cancelButton}
                onPress={() => setAddBillModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.confirmButton}
                onPress={handleAddBill}
              >
                <Text style={styles.confirmButtonText}>Add Bill</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Subscription Modal */}
      <Modal
        visible={addSubscriptionModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setAddSubscriptionModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add Subscription to Track</Text>

            <TextInput
              style={styles.textInput}
              placeholder="Subscription name (e.g., Netflix)"
              value={subName}
              onChangeText={setSubName}
            />

            <TextInput
              style={styles.textInput}
              placeholder="Provider (e.g., Netflix)"
              value={subProvider}
              onChangeText={setSubProvider}
            />

            <TextInput
              style={styles.textInput}
              placeholder="Monthly amount"
              value={subAmount}
              onChangeText={setSubAmount}
              keyboardType="numeric"
            />

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Category:</Text>
              <View style={styles.categoryButtons}>
                {['entertainment', 'productivity', 'fitness', 'news', 'other'].map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[styles.categoryButton, subCategory === category && styles.categoryButtonActive]}
                    onPress={() => setSubCategory(category)}
                  >
                    <Text style={[styles.categoryButtonText, subCategory === category && styles.categoryButtonTextActive]}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.pickerContainer}>
              <Text style={styles.pickerLabel}>Billing Cycle:</Text>
              <View style={styles.categoryButtons}>
                {['monthly', 'yearly', 'weekly'].map((cycle) => (
                  <TouchableOpacity
                    key={cycle}
                    style={[styles.categoryButton, subBillingCycle === cycle && styles.categoryButtonActive]}
                    onPress={() => setSubBillingCycle(cycle)}
                  >
                    <Text style={[styles.categoryButtonText, subBillingCycle === cycle && styles.categoryButtonTextActive]}>
                      {cycle.charAt(0).toUpperCase() + cycle.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <TouchableOpacity
              style={styles.datePickerButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Text style={styles.datePickerText}>
                Start Date: {subStartDate.toLocaleDateString()}
              </Text>
              <Ionicons name="calendar-outline" size={20} color={Colors.primary.main} />
            </TouchableOpacity>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setAddSubscriptionModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.confirmButton}
                onPress={handleAddSubscription}
              >
                <Text style={styles.confirmButtonText}>Add Subscription</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Audit Modal */}
      <Modal
        visible={auditModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setAuditModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Subscription Audit</Text>
            <Text style={styles.modalSubtitle}>Review your subscriptions for potential savings</Text>

            {auditRecommendations.length > 0 ? (
              auditRecommendations.map((rec, index) => (
                <View key={index} style={styles.auditRecommendation}>
                  <View style={styles.auditHeader}>
                    <Text style={styles.auditSubscriptionName}>{rec.subscription.name}</Text>
                    <Text style={styles.auditSavings}>Save {formatCurrency(rec.potentialSavings)}</Text>
                  </View>
                  <Text style={styles.auditReason}>{rec.reason}</Text>
                  <Text style={styles.auditType}>{rec.type.toUpperCase()}</Text>
                </View>
              ))
            ) : (
              <View style={styles.auditEmptyState}>
                <Ionicons name="checkmark-circle-outline" size={48} color={Colors.status.success} />
                <Text style={styles.auditEmptyText}>Great! No optimization recommendations found.</Text>
                <Text style={styles.auditEmptySubtext}>Your subscriptions look well-managed.</Text>
              </View>
            )}

            <TouchableOpacity
              style={styles.auditCloseButton}
              onPress={() => setAuditModalVisible(false)}
            >
              <Text style={styles.auditCloseButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={subStartDate}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateChange}
        />
      )}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  summaryCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryTitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
  },
  summaryAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 16,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.neutral.white,
    opacity: 0.8,
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  quickActionButton: {
    alignItems: 'center',
    padding: 12,
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    minWidth: 70,
    flex: 1,
    marginHorizontal: 4,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionText: {
    fontSize: 11,
    color: Colors.neutral.charcoal,
    marginTop: 4,
    textAlign: 'center',
  },
  section: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  billCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  billHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  billInfo: {
    flex: 1,
  },
  billName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  billProvider: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  billAmount: {
    alignItems: 'flex-end',
  },
  billAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  billDueText: {
    fontSize: 12,
    color: Colors.accent.amber,
    marginTop: 2,
  },
  billCategory: {
    alignSelf: 'flex-start',
  },
  billCategoryText: {
    fontSize: 12,
    color: Colors.primary.main,
    backgroundColor: Colors.primary.main + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  subscriptionCard: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  subscriptionProvider: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  subscriptionAmount: {
    alignItems: 'flex-end',
  },
  subscriptionAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  subscriptionCycleText: {
    fontSize: 12,
    color: Colors.secondary.savanna,
    marginTop: 2,
  },
  subscriptionCategory: {
    alignSelf: 'flex-start',
  },
  subscriptionCategoryText: {
    fontSize: 12,
    color: Colors.secondary.savanna,
    backgroundColor: Colors.secondary.savanna + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyStateText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 12,
    marginBottom: 16,
  },
  createFirstButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createFirstButtonText: {
    color: Colors.neutral.white,
    fontWeight: '600',
    fontSize: 14,
  },
  bottomSpacing: {
    height: 32,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 20,
    textAlign: 'center',
  },
  textInput: {
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: Colors.neutral.creamLight,
  },
  pickerContainer: {
    marginBottom: 16,
  },
  pickerLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginBottom: 8,
    minWidth: '30%',
    alignItems: 'center',
  },
  categoryButtonActive: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  categoryButtonText: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
  },
  categoryButtonTextActive: {
    color: Colors.neutral.white,
    fontWeight: '600',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    backgroundColor: Colors.neutral.creamLight,
  },
  datePickerText: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.neutral.charcoal,
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: Colors.primary.main,
    marginLeft: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
  },
  auditRecommendation: {
    backgroundColor: Colors.neutral.creamLight,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  auditHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  auditSubscriptionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  auditSavings: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.status.success,
  },
  auditReason: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 8,
  },
  auditType: {
    fontSize: 12,
    color: Colors.accent.amber,
    backgroundColor: Colors.accent.amber + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  auditEmptyState: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  auditEmptyText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.status.success,
    marginTop: 12,
  },
  auditEmptySubtext: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 4,
  },
  auditCloseButton: {
    backgroundColor: Colors.primary.main,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 20,
  },
  auditCloseButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
  },
});

export default BillsOptimizationScreen;
