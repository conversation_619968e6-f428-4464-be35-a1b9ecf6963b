import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import TransactionDetailModal from '../components/TransactionDetailModal';

/**
 * TransferSuccessScreen - Success confirmation and receipt
 * Features transaction details, receipt sharing, and navigation options
 */
const TransferSuccessScreen = ({ navigation, route }) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const { transaction, reference, message } = route.params;
  const [showReceiptModal, setShowReceiptModal] = React.useState(false);

  useEffect(() => {
    // Trigger success haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }, []);

  const formatCurrency = (value) => {
    return `UGX ${parseFloat(value).toLocaleString()}`;
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-UG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-UG', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
  };

  const handleViewReceipt = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Convert transaction to format expected by TransactionDetailModal
    const formattedTransaction = {
      id: transaction.reference,
      transaction_type: 'transfer',
      amount: transaction.amount,
      currency: 'UGX',
      description: transaction.description,
      created_at: transaction.timestamp,
      reference_number: transaction.reference,
      status: transaction.status,
      provider: transaction.recipient?.provider?.name,
      category: transaction.purpose,
      account_number: transaction.recipient?.phoneNumber,
      metadata: transaction.metadata,
    };

    setShowReceiptModal(true);
  };

  const handleSendAnother = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('SendMoney');
  };

  const handleGoHome = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    navigation.navigate('Dashboard');
  };

  const handleAddToFavorites = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Alert.alert(
      'Add to Favorites',
      `Add ${transaction.recipient.name} to your favorite contacts for quick transfers?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Add to Favorites', 
          onPress: () => {
            // Implementation would add to favorites
            Alert.alert('Success', 'Contact added to favorites!');
          }
        }
      ]
    );
  };

  const renderSuccessHeader = () => (
    <View style={styles.successHeader}>
      <View style={styles.successIcon}>
        <Ionicons name="checkmark-circle" size={80} color={theme.colors.success} />
      </View>
      <Text style={styles.successTitle}>Transfer Successful!</Text>
      <Text style={styles.successMessage}>{message}</Text>
    </View>
  );

  const renderTransactionSummary = () => {
    const { date, time } = formatDateTime(transaction.timestamp);
    
    return (
      <View style={styles.summaryCard}>
        <View style={styles.summaryHeader}>
          <Text style={styles.summaryTitle}>Transaction Summary</Text>
          <View style={styles.statusBadge}>
            <Text style={styles.statusText}>Completed</Text>
          </View>
        </View>

        <View style={styles.recipientSection}>
          {transaction.recipient.image ? (
            <Image 
              source={{ uri: transaction.recipient.image.uri }} 
              style={styles.recipientAvatar} 
            />
          ) : (
            <View style={[styles.recipientAvatar, styles.recipientAvatarPlaceholder]}>
              <Text style={styles.recipientAvatarText}>
                {transaction.recipient.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          
          <View style={styles.recipientInfo}>
            <Text style={styles.recipientName}>{transaction.recipient.name}</Text>
            <Text style={styles.recipientPhone}>{transaction.recipient.phoneNumber}</Text>
            {transaction.recipient.provider && (
              <View style={[
                styles.providerBadge,
                { backgroundColor: transaction.recipient.provider.color + '20' }
              ]}>
                <Text style={[
                  styles.providerText,
                  { color: transaction.recipient.provider.color }
                ]}>
                  {transaction.recipient.provider.name}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.transactionDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Amount Sent</Text>
            <Text style={styles.detailValue}>{formatCurrency(transaction.amount)}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Transfer Fee</Text>
            <Text style={[
              styles.detailValue,
              transaction.fee === 0 && styles.freeText
            ]}>
              {transaction.fee === 0 ? 'FREE' : formatCurrency(transaction.fee)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Purpose</Text>
            <Text style={styles.detailValue}>{transaction.purpose}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Reference</Text>
            <Text style={styles.detailValue}>{transaction.reference}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Date & Time</Text>
            <Text style={styles.detailValue}>{date} at {time}</Text>
          </View>
          
          <View style={[styles.detailRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total Deducted</Text>
            <Text style={styles.totalValue}>{formatCurrency(transaction.total)}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderActionButtons = () => (
    <View style={styles.actionButtons}>
      <TouchableOpacity
        style={styles.primaryButton}
        onPress={handleViewReceipt}
        activeOpacity={0.8}
      >
        <Ionicons name="receipt-outline" size={20} color={theme.colors.white} />
        <Text style={styles.primaryButtonText}>View Receipt</Text>
      </TouchableOpacity>

      <View style={styles.secondaryButtons}>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleAddToFavorites}
          activeOpacity={0.7}
        >
          <Ionicons name="heart-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.secondaryButtonText}>Add to Favorites</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleSendAnother}
          activeOpacity={0.7}
        >
          <Ionicons name="send-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.secondaryButtonText}>Send Another</Text>
        </TouchableOpacity>
      </View>

      <TouchableOpacity
        style={styles.homeButton}
        onPress={handleGoHome}
        activeOpacity={0.7}
      >
        <Text style={styles.homeButtonText}>Back to Home</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderSuccessHeader()}
        {renderTransactionSummary()}
        {renderActionButtons()}
      </ScrollView>

      {/* Transaction Detail Modal */}
      <TransactionDetailModal
        visible={showReceiptModal}
        onClose={() => setShowReceiptModal(false)}
        transaction={showReceiptModal ? {
          id: transaction.reference,
          transaction_type: 'transfer',
          amount: transaction.amount,
          currency: 'UGX',
          description: transaction.description,
          created_at: transaction.timestamp,
          reference_number: transaction.reference,
          status: transaction.status,
          provider: transaction.recipient?.provider?.name,
          category: transaction.purpose,
          account_number: transaction.recipient?.phoneNumber,
          metadata: transaction.metadata,
        } : null}
      />
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 40,
  },
  successHeader: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  successMessage: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: theme.colors.success + '20',
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  statusBadge: {
    backgroundColor: theme.colors.success + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.success,
  },
  recipientSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 12,
  },
  recipientAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  recipientAvatarPlaceholder: {
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipientAvatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  recipientInfo: {
    flex: 1,
  },
  recipientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  recipientPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 6,
  },
  providerBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  providerText: {
    fontSize: 12,
    fontWeight: '600',
  },
  transactionDetails: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  freeText: {
    color: theme.colors.success,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  actionButtons: {
    paddingHorizontal: 20,
    gap: 16,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  primaryButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
  secondaryButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: theme.colors.primary + '30',
    gap: 6,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  homeButton: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  homeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
});

export default TransferSuccessScreen;
