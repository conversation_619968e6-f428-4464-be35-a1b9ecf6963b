/**
 * Real-time Budget Monitoring Hook
 * Provides real-time budget status updates and alerts
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import budgetManagementService from '../services/budgetManagementService';
import authService from '../services/authService';

const useRealTimeBudgetMonitoring = (enabled = true) => {
  const [budgetStatus, setBudgetStatus] = useState(null);
  const [alerts, setAlerts] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const subscriptionRef = useRef(null);
  const userIdRef = useRef(null);

  /**
   * Initialize budget monitoring
   */
  const initializeMonitoring = useCallback(async () => {
    try {
      if (!enabled) return;

      setLoading(true);
      setError(null);

      // Get current user
      const user = await authService.getCurrentUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      userIdRef.current = user.id;

      // Initialize budget service
      await budgetManagementService.initialize(user.id);

      // Get initial budget status
      const statusResult = await budgetManagementService.getRealTimeBudgetStatus(user.id);
      if (statusResult.success) {
        setBudgetStatus(statusResult.data);
        setAlerts(statusResult.data.alerts || []);
      } else {
        throw new Error(statusResult.error);
      }

      // Subscribe to real-time updates
      const subscription = budgetManagementService.subscribeToBudgetUpdates(
        user.id,
        handleBudgetUpdate
      );

      if (subscription.success) {
        subscriptionRef.current = subscription;
        setIsConnected(true);
        
        // Start proactive monitoring
        budgetManagementService.startBudgetMonitoring(user.id);
      } else {
        console.warn('⚠️ Failed to subscribe to budget updates:', subscription.error);
      }

    } catch (err) {
      console.error('❌ Error initializing budget monitoring:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [enabled]);

  /**
   * Handle budget update events
   */
  const handleBudgetUpdate = useCallback((update) => {
    try {
      console.log('📊 Budget update received:', update);

      if (update.data) {
        setBudgetStatus(update.data);
        setAlerts(update.data.alerts || []);
      }

      // Handle specific update types
      switch (update.type) {
        case 'budget_update':
          console.log('💰 Budget configuration updated');
          break;
        case 'transaction_update':
          console.log('💳 Transaction affected budget');
          break;
        default:
          console.log('📊 General budget update');
      }
    } catch (error) {
      console.error('❌ Error handling budget update:', error);
    }
  }, []);

  /**
   * Refresh budget status manually
   */
  const refreshBudgetStatus = useCallback(async () => {
    try {
      if (!userIdRef.current) return;

      const statusResult = await budgetManagementService.getRealTimeBudgetStatus(userIdRef.current);
      if (statusResult.success) {
        setBudgetStatus(statusResult.data);
        setAlerts(statusResult.data.alerts || []);
      }
    } catch (error) {
      console.error('❌ Error refreshing budget status:', error);
    }
  }, []);

  /**
   * Dismiss an alert
   */
  const dismissAlert = useCallback((alertIndex) => {
    setAlerts(prevAlerts => prevAlerts.filter((_, index) => index !== alertIndex));
  }, []);

  /**
   * Get budget utilization color
   */
  const getUtilizationColor = useCallback((utilization) => {
    if (utilization > 100) return '#FF6B6B'; // Red
    if (utilization > 80) return '#FFB347'; // Orange
    if (utilization > 60) return '#87CEEB'; // Light blue
    return '#90EE90'; // Light green
  }, []);

  /**
   * Get budget status summary
   */
  const getBudgetSummary = useCallback(() => {
    if (!budgetStatus) return null;

    return {
      totalBudgets: budgetStatus.totalBudgets || 0,
      activeBudgets: budgetStatus.activeBudgets || 0,
      budgetsOverLimit: budgetStatus.budgetsOverLimit || 0,
      budgetsNearLimit: budgetStatus.budgetsNearLimit || 0,
      averageUtilization: (budgetStatus.averageUtilization || 0) * 100,
      hasAlerts: alerts.length > 0,
      alertCount: alerts.length,
      lastUpdated: budgetStatus.lastUpdated
    };
  }, [budgetStatus, alerts]);

  /**
   * Get high priority alerts
   */
  const getHighPriorityAlerts = useCallback(() => {
    return alerts.filter(alert => alert.type === 'over_budget');
  }, [alerts]);

  /**
   * Get warning alerts
   */
  const getWarningAlerts = useCallback(() => {
    return alerts.filter(alert => alert.type === 'near_limit' && alert.utilization > 90);
  }, [alerts]);

  /**
   * Check if user has any active budgets
   */
  const hasActiveBudgets = useCallback(() => {
    return budgetStatus && budgetStatus.activeBudgets > 0;
  }, [budgetStatus]);

  /**
   * Initialize on mount
   */
  useEffect(() => {
    initializeMonitoring();

    // Cleanup on unmount
    return () => {
      if (subscriptionRef.current?.unsubscribe) {
        subscriptionRef.current.unsubscribe();
      }
      if (userIdRef.current) {
        budgetManagementService.stopBudgetMonitoring(userIdRef.current);
      }
    };
  }, [initializeMonitoring]);

  /**
   * Reconnect when enabled changes
   */
  useEffect(() => {
    if (enabled && !isConnected && !loading) {
      initializeMonitoring();
    } else if (!enabled && isConnected) {
      // Disconnect
      if (subscriptionRef.current?.unsubscribe) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
      if (userIdRef.current) {
        budgetManagementService.stopBudgetMonitoring(userIdRef.current);
      }
      setIsConnected(false);
    }
  }, [enabled, isConnected, loading, initializeMonitoring]);

  return {
    // Status
    budgetStatus,
    alerts,
    isConnected,
    loading,
    error,
    
    // Actions
    refreshBudgetStatus,
    dismissAlert,
    
    // Utilities
    getUtilizationColor,
    getBudgetSummary,
    getHighPriorityAlerts,
    getWarningAlerts,
    hasActiveBudgets,
    
    // Raw data for custom usage
    rawBudgetStatus: budgetStatus,
    rawAlerts: alerts
  };
};

export default useRealTimeBudgetMonitoring;
