import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialCommunityIcons, FontAwesome5 } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { Colors } from '../constants/Colors';
import loadingStateManager from '../services/loadingStateManager';

const { width, height } = Dimensions.get('window');

/**
 * Adaptive Splash Screen Component
 * 
 * This component adapts its messaging and behavior based on the current
 * loading context (app startup, login, dashboard loading, etc.)
 */
const AdaptiveSplashScreen = ({ 
  onAnimationComplete,
  context = 'startup', // 'startup', 'login', 'dashboard', 'transition'
  showFullAnimation = true,
  customMessage = null,
  minimumDuration = 2000 // Minimum time to show splash
}) => {
  const { theme, isDarkMode, isInitialized } = useTheme();
  const [currentMessage, setCurrentMessage] = useState(0);
  const [loadingContext, setLoadingContext] = useState(context);
  const [activeStates, setActiveStates] = useState([]);

  // Context-specific messages
  const contextMessages = {
    startup: [
      'Securing your financial future...',
      'Connecting communities across East Africa...',
      'Building trust, one transaction at a time...',
      'Empowering financial inclusion...',
    ],
    login: [
      'Authenticating your account...',
      'Securing your session...',
      'Preparing your dashboard...',
      'Loading your financial data...',
    ],
    dashboard: [
      'Loading your wallet information...',
      'Fetching recent transactions...',
      'Updating account balances...',
      'Preparing financial insights...',
    ],
    transition: [
      'Processing your request...',
      'Updating your information...',
      'Securing your data...',
      'Almost ready...',
    ],
    registration: [
      'Creating your account...',
      'Setting up your wallet...',
      'Configuring security settings...',
      'Welcome to JiraniPay...',
    ],
    verification: [
      'Verifying your documents...',
      'Checking your information...',
      'Updating your account status...',
      'Finalizing verification...',
    ]
  };

  const messages = contextMessages[loadingContext] || contextMessages.startup;

  // Animation values
  const logoScale = useRef(new Animated.Value(showFullAnimation ? 0 : 1)).current;
  const logoOpacity = useRef(new Animated.Value(showFullAnimation ? 0 : 1)).current;
  const contentOpacity = useRef(new Animated.Value(showFullAnimation ? 0 : 1)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;
  const messageOpacity = useRef(new Animated.Value(0)).current;
  
  // Floating elements for full animation
  const floatingElement1 = useRef(new Animated.Value(0)).current;
  const floatingElement2 = useRef(new Animated.Value(0)).current;
  const floatingElement3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Listen to loading state changes
    const unsubscribe = loadingStateManager.addListener((stateChange) => {
      const activeStates = loadingStateManager.getActiveLoadingStates();
      setActiveStates(activeStates);
      
      // Update context based on active loading states
      if (activeStates.includes('login_processing')) {
        setLoadingContext('login');
      } else if (activeStates.includes('dashboard_loading')) {
        setLoadingContext('dashboard');
      } else if (activeStates.includes('registration_processing')) {
        setLoadingContext('registration');
      } else if (activeStates.includes('verification_loading')) {
        setLoadingContext('verification');
      } else if (activeStates.length > 0) {
        setLoadingContext('transition');
      }
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    // Message rotation
    const messageInterval = setInterval(() => {
      setCurrentMessage((prev) => (prev + 1) % messages.length);
    }, 1500);

    // Animation sequence
    const startTime = Date.now();
    
    const animationSequence = showFullAnimation 
      ? createFullAnimationSequence()
      : createQuickAnimationSequence();

    animationSequence.start(({ finished }) => {
      if (finished) {
        const elapsed = Date.now() - startTime;
        const remainingTime = Math.max(0, minimumDuration - elapsed);
        
        setTimeout(() => {
          clearInterval(messageInterval);
          onAnimationComplete && onAnimationComplete();
        }, remainingTime);
      }
    });

    return () => clearInterval(messageInterval);
  }, [showFullAnimation, minimumDuration]);

  const createFullAnimationSequence = () => {
    return Animated.sequence([
      // Phase 1: Logo entrance (0-800ms)
      Animated.parallel([
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
      ]),
      
      // Phase 2: Content and floating elements (800-1600ms)
      Animated.parallel([
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.stagger(200, [
          Animated.spring(floatingElement1, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(floatingElement2, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.spring(floatingElement3, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]),
      
      // Phase 3: Loading indicator (1600-3000ms)
      Animated.parallel([
        Animated.timing(messageOpacity, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(progressWidth, {
          toValue: 1,
          duration: 1400,
          useNativeDriver: false,
        }),
      ]),
    ]);
  };

  const createQuickAnimationSequence = () => {
    return Animated.parallel([
      Animated.timing(messageOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(progressWidth, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: false,
      }),
    ]);
  };

  // Start continuous floating animations for full animation mode
  useEffect(() => {
    if (!showFullAnimation) return;

    const createFloatingAnimation = (animatedValue, duration, delay = 0) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: duration,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: duration,
            useNativeDriver: true,
          }),
        ])
      );
    };

    const floatingAnimations = Animated.parallel([
      createFloatingAnimation(floatingElement1, 2000, 0),
      createFloatingAnimation(floatingElement2, 2500, 500),
      createFloatingAnimation(floatingElement3, 2200, 1000),
    ]);

    floatingAnimations.start();

    return () => floatingAnimations.stop();
  }, [showFullAnimation]);

  // Dynamic colors based on theme
  const gradientColors = isDarkMode
    ? ['#1a1a1a', '#2d2d2d', '#1a1a1a']
    : [Colors.neutral.appBackground, '#ffffff', Colors.neutral.appBackground];

  const textColor = isDarkMode ? '#FFFFFF' : Colors.neutral.charcoal;
  const subtitleColor = isDarkMode ? '#B3B3B3' : Colors.neutral.warmGray;
  const accentColor = Colors.primary.main;

  // Context-specific icons
  const getContextIcon = () => {
    switch (loadingContext) {
      case 'login': return 'log-in';
      case 'dashboard': return 'grid';
      case 'registration': return 'person-add';
      case 'verification': return 'shield-checkmark';
      case 'transition': return 'sync';
      default: return 'wallet';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent
      />
      
      <LinearGradient
        colors={gradientColors}
        style={styles.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Background Pattern */}
        {showFullAnimation && (
          <View style={styles.backgroundPattern}>
            <View style={[styles.patternDot, { top: '20%', left: '15%' }]} />
            <View style={[styles.patternDot, { top: '30%', right: '20%' }]} />
            <View style={[styles.patternDot, { bottom: '25%', left: '25%' }]} />
            <View style={[styles.patternDot, { bottom: '35%', right: '15%' }]} />
          </View>
        )}

        {/* Main Content */}
        <View style={styles.content}>
          {/* Logo Section */}
          <Animated.View
            style={[
              styles.logoContainer,
              {
                transform: [{ scale: logoScale }],
                opacity: logoOpacity,
              },
            ]}
          >
            <LinearGradient
              colors={[Colors.primary.main, Colors.accent.gold]}
              style={styles.logoBackground}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Ionicons
                name={getContextIcon()}
                size={showFullAnimation ? 60 : 40}
                color="#FFFFFF"
              />
            </LinearGradient>
          </Animated.View>

          {/* Title and Context */}
          <Animated.View
            style={[
              styles.titleContainer,
              { opacity: contentOpacity },
            ]}
          >
            <Text style={[styles.title, { color: textColor }]}>
              JiraniPay
            </Text>
            {showFullAnimation && (
              <Text style={[styles.titleSubtext, { color: accentColor }]}>
                جيراني • Jirani • Voisin
              </Text>
            )}
          </Animated.View>

          {/* Context-specific tagline */}
          <Animated.View
            style={[
              styles.taglineContainer,
              { opacity: contentOpacity },
            ]}
          >
            <Text style={[styles.tagline, { color: subtitleColor }]}>
              {loadingContext === 'startup' && 'Connecting East Africa'}
              {loadingContext === 'login' && 'Secure Login'}
              {loadingContext === 'dashboard' && 'Preparing Your Dashboard'}
              {loadingContext === 'registration' && 'Setting Up Your Account'}
              {loadingContext === 'verification' && 'Verifying Your Identity'}
              {loadingContext === 'transition' && 'Processing...'}
            </Text>
          </Animated.View>

          {/* Floating Trust Elements (only for full animation) */}
          {showFullAnimation && (
            <>
              <Animated.View
                style={[
                  styles.floatingElement,
                  styles.floatingElement1,
                  {
                    opacity: floatingElement1,
                    transform: [
                      {
                        translateY: floatingElement1.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, -10],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <MaterialCommunityIcons
                  name="shield-check"
                  size={24}
                  color={Colors.status.success}
                />
              </Animated.View>

              <Animated.View
                style={[
                  styles.floatingElement,
                  styles.floatingElement2,
                  {
                    opacity: floatingElement2,
                    transform: [
                      {
                        translateY: floatingElement2.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, -8],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <MaterialCommunityIcons
                  name="lightning-bolt"
                  size={20}
                  color={Colors.accent.gold}
                />
              </Animated.View>

              <Animated.View
                style={[
                  styles.floatingElement,
                  styles.floatingElement3,
                  {
                    opacity: floatingElement3,
                    transform: [
                      {
                        translateY: floatingElement3.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, -12],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <FontAwesome5
                  name="handshake"
                  size={18}
                  color={Colors.primary.main}
                />
              </Animated.View>
            </>
          )}
        </View>

        {/* Loading Section */}
        <Animated.View
          style={[
            styles.loadingContainer,
            { opacity: messageOpacity },
          ]}
        >
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: progressWidth.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                },
              ]}
            />
          </View>
          <Text style={[styles.loadingText, { color: subtitleColor }]}>
            {customMessage || messages[currentMessage]}
          </Text>
          
          {/* Show active loading states for debugging */}
          {__DEV__ && activeStates.length > 0 && (
            <Text style={[styles.debugText, { color: subtitleColor }]}>
              Active: {activeStates.join(', ')}
            </Text>
          )}
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  patternDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.primary.main,
    opacity: 0.3,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 30,
  },
  logoBackground: {
    width: 100,
    height: 100,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.primary.main,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  titleContainer: {
    marginBottom: 8,
    alignItems: 'center',
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 1,
    fontFamily: Platform.OS === 'ios' ? 'System' : 'Roboto',
  },
  titleSubtext: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.8,
  },
  taglineContainer: {
    marginBottom: 40,
    alignItems: 'center',
  },
  tagline: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  floatingElement: {
    position: 'absolute',
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  floatingElement1: {
    top: '25%',
    right: '15%',
  },
  floatingElement2: {
    top: '35%',
    left: '10%',
  },
  floatingElement3: {
    top: '45%',
    right: '20%',
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: Colors.primary.main,
    borderRadius: 2,
    shadowColor: Colors.primary.main,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 2,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 22,
  },
  debugText: {
    fontSize: 10,
    textAlign: 'center',
    marginTop: 8,
    opacity: 0.6,
  },
});

export default AdaptiveSplashScreen;
