/**
 * Asset Details Screen
 * Detailed view of individual East African assets with real-time data,
 * charts, news, and trading capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import marketDataService from '../services/marketDataService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDate } from '../utils/dateUtils';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const { width } = Dimensions.get('window');

const AssetDetailsScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { symbol } = route.params;

  // State
  const [asset, setAsset] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [priceHistory, setPriceHistory] = useState([]);
  const [news, setNews] = useState([]);
  const [selectedTab, setSelectedTab] = useState('overview');

  const tabs = [
    { key: 'overview', label: 'Overview', icon: 'analytics-outline' },
    { key: 'chart', label: 'Chart', icon: 'trending-up-outline' },
    { key: 'news', label: 'News', icon: 'newspaper-outline' },
    { key: 'financials', label: 'Financials', icon: 'calculator-outline' }
  ];

  useEffect(() => {
    loadAssetDetails();
  }, [symbol]);

  const loadAssetDetails = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Load asset details from market data service
      const assetResult = await marketDataService.getAssetDetails(symbol);
      if (assetResult.success) {
        setAsset(assetResult.asset);
      } else {
        Alert.alert('Error', 'Failed to load asset details');
        navigation.goBack();
        return;
      }

      // Load price history
      const historyResult = await marketDataService.getAssetPriceHistory(symbol, '1M');
      if (historyResult.success) {
        setPriceHistory(historyResult.history);
      }

      // Load related news
      const newsResult = await marketDataService.getAssetNews(symbol);
      if (newsResult.success) {
        setNews(newsResult.news);
      }

    } catch (error) {
      console.error('❌ Error loading asset details:', error);
      Alert.alert('Error', 'Failed to load asset information');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    loadAssetDetails(true);
  };

  const handleTrade = (action) => {
    Alert.alert(
      `${action.charAt(0).toUpperCase() + action.slice(1)} ${asset.symbol}`,
      'Choose a portfolio to trade this asset:',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Select Portfolio', onPress: () => navigation.navigate('InvestmentPortfoliosList', { 
          asset, 
          action 
        }) }
      ]
    );
  };

  const handleAddToWatchlist = async () => {
    try {
      const userId = await getCurrentUserId();
      if (!userId) {
        Alert.alert('Authentication Required', 'Please log in to add to watchlist');
        return;
      }

      // Add to watchlist logic here
      Alert.alert('Added to Watchlist', `${asset.symbol} has been added to your watchlist`);
    } catch (error) {
      console.error('❌ Error adding to watchlist:', error);
      Alert.alert('Error', 'Failed to add to watchlist');
    }
  };

  const getChangeColor = (change) => {
    if (change > 0) return theme.colors.success;
    if (change < 0) return theme.colors.error;
    return theme.colors.textSecondary;
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <UnifiedBackButton navigation={navigation} />
      <Text style={styles.headerTitle}>{asset?.symbol || symbol}</Text>
      <TouchableOpacity onPress={handleAddToWatchlist}>
        <Ionicons name="star-outline" size={24} color={theme.colors.text} />
      </TouchableOpacity>
    </View>
  );

  const renderAssetInfo = () => (
    <View style={styles.assetInfoCard}>
      <View style={styles.assetHeader}>
        <View style={styles.assetTitleSection}>
          <Text style={styles.assetName}>{asset.name}</Text>
          <Text style={styles.assetSymbol}>{asset.symbol}</Text>
          <View style={styles.marketInfo}>
            <Text style={styles.marketText}>{asset.market}</Text>
            <Text style={styles.sectorText}>• {asset.sector}</Text>
          </View>
        </View>
      </View>

      <View style={styles.priceSection}>
        <Text style={styles.currentPrice}>
          {formatCurrency(asset.price, asset.currency)}
        </Text>
        <View style={styles.changeContainer}>
          <Text style={[styles.changeText, { color: getChangeColor(asset.change) }]}>
            {asset.change >= 0 ? '+' : ''}{formatCurrency(asset.change, asset.currency)}
          </Text>
          <Text style={[styles.changePercent, { color: getChangeColor(asset.change) }]}>
            ({asset.change >= 0 ? '+' : ''}{asset.changePercent?.toFixed(2)}%)
          </Text>
        </View>
      </View>
    </View>
  );

  const renderTradingButtons = () => (
    <View style={styles.tradingButtons}>
      <TouchableOpacity 
        style={[styles.tradeButton, styles.buyButton]}
        onPress={() => handleTrade('buy')}
      >
        <Ionicons name="trending-up" size={20} color={theme.colors.white} />
        <Text style={styles.tradeButtonText}>Buy</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={[styles.tradeButton, styles.sellButton]}
        onPress={() => handleTrade('sell')}
      >
        <Ionicons name="trending-down" size={20} color={theme.colors.white} />
        <Text style={styles.tradeButtonText}>Sell</Text>
      </TouchableOpacity>
    </View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[styles.tab, selectedTab === tab.key && styles.activeTab]}
          onPress={() => setSelectedTab(tab.key)}
        >
          <Ionicons 
            name={tab.icon} 
            size={16} 
            color={selectedTab === tab.key ? theme.colors.primary : theme.colors.textSecondary} 
          />
          <Text style={[
            styles.tabText, 
            selectedTab === tab.key && styles.activeTabText
          ]}>
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderOverview = () => (
    <View style={styles.tabContent}>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Volume</Text>
          <Text style={styles.statValue}>{asset.volume?.toLocaleString() || 'N/A'}</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Market Cap</Text>
          <Text style={styles.statValue}>
            {asset.marketCap ? formatCurrency(asset.marketCap, asset.currency) : 'N/A'}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>52W High</Text>
          <Text style={styles.statValue}>
            {asset.high52w ? formatCurrency(asset.high52w, asset.currency) : 'N/A'}
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>52W Low</Text>
          <Text style={styles.statValue}>
            {asset.low52w ? formatCurrency(asset.low52w, asset.currency) : 'N/A'}
          </Text>
        </View>
      </View>

      <View style={styles.descriptionSection}>
        <Text style={styles.sectionTitle}>About {asset.name}</Text>
        <Text style={styles.description}>
          {asset.description || `${asset.name} is a leading company in the ${asset.sector} sector, listed on the ${asset.market}. This East African company represents a significant investment opportunity in the regional market.`}
        </Text>
      </View>
    </View>
  );

  const renderChart = () => (
    <View style={styles.tabContent}>
      <View style={styles.chartPlaceholder}>
        <Ionicons name="trending-up" size={48} color={theme.colors.primary} />
        <Text style={styles.chartPlaceholderText}>Price Chart</Text>
        <Text style={styles.chartPlaceholderSubtext}>
          Interactive price charts will be available with real-time market data integration
        </Text>
      </View>
    </View>
  );

  const renderNews = () => (
    <View style={styles.tabContent}>
      {news.length > 0 ? (
        news.map((item, index) => (
          <View key={index} style={styles.newsItem}>
            <Text style={styles.newsTitle}>{item.title}</Text>
            <Text style={styles.newsDate}>{formatDate(item.date)}</Text>
            <Text style={styles.newsSnippet}>{item.snippet}</Text>
          </View>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="newspaper-outline" size={48} color={theme.colors.textSecondary} />
          <Text style={styles.emptyStateText}>No recent news available</Text>
          <Text style={styles.emptyStateSubtext}>
            News and updates for {asset.symbol} will appear here
          </Text>
        </View>
      )}
    </View>
  );

  const renderFinancials = () => (
    <View style={styles.tabContent}>
      <View style={styles.financialsGrid}>
        <View style={styles.financialItem}>
          <Text style={styles.financialLabel}>P/E Ratio</Text>
          <Text style={styles.financialValue}>{asset.peRatio || 'N/A'}</Text>
        </View>
        <View style={styles.financialItem}>
          <Text style={styles.financialLabel}>Dividend Yield</Text>
          <Text style={styles.financialValue}>{asset.dividendYield || 'N/A'}</Text>
        </View>
        <View style={styles.financialItem}>
          <Text style={styles.financialLabel}>EPS</Text>
          <Text style={styles.financialValue}>{asset.eps || 'N/A'}</Text>
        </View>
        <View style={styles.financialItem}>
          <Text style={styles.financialLabel}>Book Value</Text>
          <Text style={styles.financialValue}>{asset.bookValue || 'N/A'}</Text>
        </View>
      </View>
    </View>
  );

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'overview':
        return renderOverview();
      case 'chart':
        return renderChart();
      case 'news':
        return renderNews();
      case 'financials':
        return renderFinancials();
      default:
        return renderOverview();
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading asset details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!asset) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
        {renderHeader()}
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={theme.colors.error} />
          <Text style={styles.errorText}>Asset not found</Text>
          <Text style={styles.errorSubtext}>The requested asset could not be loaded</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {renderHeader()}
      
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderAssetInfo()}
        {renderTradingButtons()}
        {renderTabBar()}
        {renderTabContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    textAlign: 'center',
  },
  errorSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  assetInfoCard: {
    backgroundColor: theme.colors.surface,
    margin: 20,
    borderRadius: 16,
    padding: 20,
  },
  assetHeader: {
    marginBottom: 20,
  },
  assetTitleSection: {
    alignItems: 'center',
  },
  assetName: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  assetSymbol: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.primary,
    marginBottom: 8,
  },
  marketInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  marketText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  sectorText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 4,
  },
  priceSection: {
    alignItems: 'center',
  },
  currentPrice: {
    fontSize: 32,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 8,
  },
  changeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  changeText: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  changePercent: {
    fontSize: 16,
    fontWeight: '600',
  },
  tradingButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tradeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginHorizontal: 5,
  },
  buyButton: {
    backgroundColor: theme.colors.success,
  },
  sellButton: {
    backgroundColor: theme.colors.error,
  },
  tradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
    marginLeft: 8,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
  },
  activeTab: {
    backgroundColor: theme.colors.primary,
  },
  tabText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: 4,
    fontWeight: '500',
  },
  activeTabText: {
    color: theme.colors.white,
    fontWeight: '600',
  },
  tabContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statItem: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  descriptionSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  chartPlaceholder: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
  },
  chartPlaceholderText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
  },
  chartPlaceholderSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  newsItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  newsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  newsDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  newsSnippet: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  financialsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  financialItem: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  financialLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  financialValue: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
});

export default AssetDetailsScreen;
