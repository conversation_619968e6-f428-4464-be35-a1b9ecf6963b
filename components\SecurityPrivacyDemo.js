import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import securityManagementService from '../services/securityManagementService';
import privacyManagementService from '../services/privacyManagementService';

/**
 * Security & Privacy Demo Component
 * Showcases the modern security and privacy features
 */
const SecurityPrivacyDemo = ({ userId, visible = true }) => {
  const [securityStatus, setSecurityStatus] = useState({
    biometricAvailable: false,
    biometricEnabled: false,
    pinEnabled: false,
    twoFactorEnabled: false,
  });

  const [privacyStatus, setPrivacyStatus] = useState({
    dataExportRequested: false,
    marketingConsent: true,
    analyticsConsent: true,
    dataSharing: false,
  });

  useEffect(() => {
    if (visible && userId) {
      loadSecurityPrivacyStatus();
    }
  }, [visible, userId]);

  const loadSecurityPrivacyStatus = async () => {
    try {
      // Load security status
      const biometricResult = await securityManagementService.checkBiometricAvailability();
      const securitySettings = await securityManagementService.getSecuritySettings(userId);

      if (biometricResult.success) {
        setSecurityStatus(prev => ({
          ...prev,
          biometricAvailable: biometricResult.data.isAvailable,
          biometricEnabled: securitySettings.data?.biometric_enabled || false,
          pinEnabled: securitySettings.data?.pin_enabled || false,
          twoFactorEnabled: securitySettings.data?.two_factor_enabled || false,
        }));
      }

      // Load privacy status
      const privacySettings = await privacyManagementService.getPrivacySettings(userId);
      if (privacySettings.success) {
        setPrivacyStatus({
          dataExportRequested: privacySettings.data.data_export_requested || false,
          marketingConsent: privacySettings.data.consent_given?.marketing || true,
          analyticsConsent: privacySettings.data.consent_given?.analytics || true,
          dataSharing: privacySettings.data.consent_given?.dataSharing || false,
        });
      }
    } catch (error) {
      console.error('❌ Error loading security/privacy status:', error);
    }
  };

  const testBiometricAuth = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const result = await securityManagementService.authenticateWithBiometric(
        'Test biometric authentication for JiraniPay'
      );

      if (result.success) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert('✅ Success', 'Biometric authentication successful!');
      } else {
        Alert.alert('❌ Failed', result.error || 'Biometric authentication failed');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to test biometric authentication');
    }
  };

  const testDataExport = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      Alert.alert(
        'Export Data',
        'This will export your data in JSON format. Continue?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Export',
            onPress: async () => {
              const result = await privacyManagementService.exportUserData(userId, 'json');
              
              if (result.success) {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                Alert.alert(
                  '✅ Export Complete',
                  `Data exported successfully!\nFile: ${result.data.fileName}\nSize: ${(result.data.fileSize / 1024).toFixed(1)} KB`
                );
              } else {
                Alert.alert('❌ Export Failed', result.error);
              }
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to export data');
    }
  };

  const renderSecurityFeature = (icon, title, status, onTest, iconColor) => (
    <TouchableOpacity style={styles.featureCard} onPress={onTest} activeOpacity={0.7}>
      <View style={styles.featureHeader}>
        <View style={[styles.featureIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.featureInfo}>
          <Text style={styles.featureTitle}>{title}</Text>
          <View style={styles.statusContainer}>
            <View style={[
              styles.statusDot, 
              { backgroundColor: status ? Colors.status.success : Colors.neutral.warmGray }
            ]} />
            <Text style={[
              styles.statusText,
              { color: status ? Colors.status.success : Colors.neutral.warmGray }
            ]}>
              {status ? 'Enabled' : 'Disabled'}
            </Text>
          </View>
        </View>
      </View>
      <Ionicons name="play-circle" size={20} color={Colors.primary.main} />
    </TouchableOpacity>
  );

  const renderPrivacyFeature = (icon, title, description, status, iconColor) => (
    <View style={styles.privacyCard}>
      <View style={styles.featureHeader}>
        <View style={[styles.featureIcon, { backgroundColor: iconColor + '20' }]}>
          <Ionicons name={icon} size={24} color={iconColor} />
        </View>
        <View style={styles.featureInfo}>
          <Text style={styles.featureTitle}>{title}</Text>
          <Text style={styles.featureDescription}>{description}</Text>
        </View>
      </View>
      <View style={[
        styles.privacyStatus,
        { backgroundColor: status ? Colors.status.success + '20' : Colors.neutral.warmGray + '20' }
      ]}>
        <Text style={[
          styles.privacyStatusText,
          { color: status ? Colors.status.success : Colors.neutral.warmGray }
        ]}>
          {status ? 'Allowed' : 'Blocked'}
        </Text>
      </View>
    </View>
  );

  if (!visible) return null;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>🔒 Security & Privacy Demo</Text>
        <Text style={styles.headerSubtitle}>
          Experience JiraniPay's modern security features
        </Text>
      </LinearGradient>

      {/* Security Features */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🛡️ Security Features</Text>
        <Text style={styles.sectionDescription}>
          Test our advanced security authentication methods
        </Text>

        {renderSecurityFeature(
          'finger-print',
          'Biometric Authentication',
          securityStatus.biometricEnabled,
          testBiometricAuth,
          Colors.primary.main
        )}

        {renderSecurityFeature(
          'keypad',
          'PIN Security',
          securityStatus.pinEnabled,
          () => Alert.alert('PIN Demo', 'PIN setup and verification demo'),
          Colors.secondary.lake
        )}

        {renderSecurityFeature(
          'shield-checkmark',
          'Two-Factor Authentication',
          securityStatus.twoFactorEnabled,
          () => Alert.alert('2FA Demo', '2FA setup and verification demo'),
          Colors.accent.gold
        )}
      </View>

      {/* Privacy Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔐 Privacy Controls</Text>
        <Text style={styles.sectionDescription}>
          Your data privacy preferences and controls
        </Text>

        {renderPrivacyFeature(
          'analytics',
          'Analytics Tracking',
          'Share usage data to improve the app',
          privacyStatus.analyticsConsent,
          Colors.secondary.forest
        )}

        {renderPrivacyFeature(
          'mail',
          'Marketing Communications',
          'Receive promotional content and offers',
          privacyStatus.marketingConsent,
          Colors.primary.main
        )}

        {renderPrivacyFeature(
          'share',
          'Data Sharing',
          'Share data with trusted partners',
          privacyStatus.dataSharing,
          Colors.accent.gold
        )}
      </View>

      {/* Data Rights */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📋 Data Rights</Text>
        <Text style={styles.sectionDescription}>
          Exercise your data protection rights
        </Text>

        <TouchableOpacity style={styles.actionCard} onPress={testDataExport} activeOpacity={0.7}>
          <View style={styles.actionLeft}>
            <View style={[styles.actionIcon, { backgroundColor: Colors.accent.gold + '20' }]}>
              <Ionicons name="download" size={24} color={Colors.accent.gold} />
            </View>
            <View>
              <Text style={styles.actionTitle}>Export My Data</Text>
              <Text style={styles.actionSubtitle}>Download all your data</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.actionCard, styles.dangerCard]} 
          onPress={() => Alert.alert('Account Deletion', 'Account deletion demo')}
          activeOpacity={0.7}
        >
          <View style={styles.actionLeft}>
            <View style={[styles.actionIcon, { backgroundColor: Colors.status.error + '20' }]}>
              <Ionicons name="trash" size={24} color={Colors.status.error} />
            </View>
            <View>
              <Text style={[styles.actionTitle, styles.dangerText]}>Delete Account</Text>
              <Text style={styles.actionSubtitle}>Permanently remove all data</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Compliance Badge */}
      <View style={styles.complianceSection}>
        <View style={styles.complianceBadge}>
          <Ionicons name="shield-checkmark" size={32} color={Colors.status.success} />
          <Text style={styles.complianceTitle}>East African Compliant</Text>
          <Text style={styles.complianceText}>
            Meets regulatory requirements for Uganda, Kenya, Tanzania, and other East African countries
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  header: {
    padding: 20,
    borderRadius: 16,
    margin: 16,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  section: {
    margin: 16,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 16,
    lineHeight: 20,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  privacyCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  featureHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureInfo: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  privacyStatus: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  privacyStatusText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dangerCard: {
    borderColor: Colors.status.error,
    borderWidth: 1,
  },
  actionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  dangerText: {
    color: Colors.status.error,
  },
  actionSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  complianceSection: {
    margin: 16,
    marginTop: 8,
  },
  complianceBadge: {
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  complianceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.status.success,
    marginTop: 8,
    marginBottom: 8,
  },
  complianceText: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default SecurityPrivacyDemo;
