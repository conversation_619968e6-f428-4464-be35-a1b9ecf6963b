/**
 * Advanced Security Service
 * Comprehensive security implementation with HSM, certificate pinning, and advanced encryption
 */

const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');

class SecurityService {
  constructor() {
    this.encryptionAlgorithm = 'aes-256-gcm';
    this.keyDerivationIterations = 100000;
    this.saltLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    
    // Certificate pinning configuration
    this.pinnedCertificates = new Map();
    this.certificateValidityPeriod = 30 * 24 * 60 * 60 * 1000; // 30 days
    
    // HSM configuration
    this.hsmConfig = {
      enabled: config.security.hsm.enabled || false,
      provider: config.security.hsm.provider || 'aws-cloudhsm',
      keyId: config.security.hsm.keyId,
      region: config.security.hsm.region
    };
    
    // Security policies
    this.securityPolicies = {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90 * 24 * 60 * 60 * 1000, // 90 days
        preventReuse: 5
      },
      sessionPolicy: {
        maxDuration: 24 * 60 * 60 * 1000, // 24 hours
        idleTimeout: 30 * 60 * 1000, // 30 minutes
        maxConcurrentSessions: 3
      },
      rateLimiting: {
        loginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        apiRequestsPerMinute: 100
      }
    };
  }

  /**
   * Initialize security service
   */
  async initialize() {
    try {
      // Initialize HSM if enabled
      if (this.hsmConfig.enabled) {
        await this.initializeHSM();
      }
      
      // Load pinned certificates
      await this.loadPinnedCertificates();
      
      // Initialize security monitoring
      await this.initializeSecurityMonitoring();
      
      logger.info('Security service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize security service:', error);
      throw error;
    }
  }

  /**
   * Initialize HSM (Hardware Security Module)
   */
  async initializeHSM() {
    try {
      if (this.hsmConfig.provider === 'aws-cloudhsm') {
        // Initialize AWS CloudHSM
        const AWS = require('aws-sdk');
        this.hsm = new AWS.CloudHSMV2({
          region: this.hsmConfig.region
        });
        
        // Verify HSM connectivity
        await this.hsm.describeClusters().promise();
        logger.info('AWS CloudHSM initialized successfully');
        
      } else if (this.hsmConfig.provider === 'azure-keyvault') {
        // Initialize Azure Key Vault
        const { KeyVaultSecret } = require('@azure/keyvault-secrets');
        const { DefaultAzureCredential } = require('@azure/identity');
        
        this.hsm = new KeyVaultSecret(
          config.security.hsm.vaultUrl,
          new DefaultAzureCredential()
        );
        
        logger.info('Azure Key Vault initialized successfully');
        
      } else {
        throw new Error(`Unsupported HSM provider: ${this.hsmConfig.provider}`);
      }
    } catch (error) {
      logger.error('Failed to initialize HSM:', error);
      throw error;
    }
  }

  /**
   * Load pinned certificates for certificate pinning
   */
  async loadPinnedCertificates() {
    try {
      const certDir = path.join(__dirname, '../config/certificates');
      
      // Load production certificates
      const certFiles = [
        'api.jiranipay.com.pem',
        'mtn-api.com.pem',
        'airtel-api.com.pem',
        'bank-api.com.pem'
      ];
      
      for (const certFile of certFiles) {
        try {
          const certPath = path.join(certDir, certFile);
          const certData = await fs.readFile(certPath, 'utf8');
          const hostname = certFile.replace('.pem', '');
          
          // Calculate certificate fingerprint
          const fingerprint = crypto
            .createHash('sha256')
            .update(certData)
            .digest('hex');
          
          this.pinnedCertificates.set(hostname, {
            fingerprint,
            certificate: certData,
            loadedAt: new Date(),
            expiresAt: new Date(Date.now() + this.certificateValidityPeriod)
          });
          
          logger.info(`Certificate pinned for ${hostname}`);
        } catch (error) {
          logger.warn(`Failed to load certificate for ${certFile}:`, error);
        }
      }
    } catch (error) {
      logger.error('Failed to load pinned certificates:', error);
    }
  }

  /**
   * Verify certificate pinning
   */
  verifyCertificatePinning(hostname, certificate) {
    try {
      const pinnedCert = this.pinnedCertificates.get(hostname);
      if (!pinnedCert) {
        logger.warn(`No pinned certificate found for ${hostname}`);
        return false;
      }
      
      // Check if certificate has expired
      if (Date.now() > pinnedCert.expiresAt.getTime()) {
        logger.warn(`Pinned certificate expired for ${hostname}`);
        return false;
      }
      
      // Calculate fingerprint of provided certificate
      const providedFingerprint = crypto
        .createHash('sha256')
        .update(certificate)
        .digest('hex');
      
      const isValid = providedFingerprint === pinnedCert.fingerprint;
      
      if (!isValid) {
        logger.security('Certificate pinning validation failed', {
          hostname,
          expectedFingerprint: pinnedCert.fingerprint,
          providedFingerprint
        });
      }
      
      return isValid;
    } catch (error) {
      logger.error('Certificate pinning verification failed:', error);
      return false;
    }
  }

  /**
   * Advanced encryption using HSM or local keys
   */
  async encryptSensitiveData(data, keyId = null) {
    try {
      if (this.hsmConfig.enabled && keyId) {
        return await this.encryptWithHSM(data, keyId);
      } else {
        return await this.encryptWithLocalKey(data);
      }
    } catch (error) {
      logger.error('Encryption failed:', error);
      throw new Error('Failed to encrypt sensitive data');
    }
  }

  /**
   * Advanced decryption using HSM or local keys
   */
  async decryptSensitiveData(encryptedData, keyId = null) {
    try {
      if (this.hsmConfig.enabled && keyId) {
        return await this.decryptWithHSM(encryptedData, keyId);
      } else {
        return await this.decryptWithLocalKey(encryptedData);
      }
    } catch (error) {
      logger.error('Decryption failed:', error);
      throw new Error('Failed to decrypt sensitive data');
    }
  }

  /**
   * Encrypt data using HSM
   */
  async encryptWithHSM(data, keyId) {
    try {
      if (this.hsmConfig.provider === 'aws-cloudhsm') {
        const kms = new (require('aws-sdk')).KMS({
          region: this.hsmConfig.region
        });
        
        const result = await kms.encrypt({
          KeyId: keyId,
          Plaintext: Buffer.from(data, 'utf8')
        }).promise();
        
        return {
          encryptedData: result.CiphertextBlob.toString('base64'),
          keyId: keyId,
          algorithm: 'aws-kms',
          timestamp: new Date().toISOString()
        };
        
      } else if (this.hsmConfig.provider === 'azure-keyvault') {
        // Azure Key Vault encryption implementation
        const result = await this.hsm.encrypt(keyId, 'RSA-OAEP', Buffer.from(data, 'utf8'));
        
        return {
          encryptedData: result.result.toString('base64'),
          keyId: keyId,
          algorithm: 'azure-keyvault',
          timestamp: new Date().toISOString()
        };
      }
      
      throw new Error('HSM encryption not implemented for this provider');
    } catch (error) {
      logger.error('HSM encryption failed:', error);
      throw error;
    }
  }

  /**
   * Decrypt data using HSM
   */
  async decryptWithHSM(encryptedData, keyId) {
    try {
      if (this.hsmConfig.provider === 'aws-cloudhsm') {
        const kms = new (require('aws-sdk')).KMS({
          region: this.hsmConfig.region
        });
        
        const result = await kms.decrypt({
          CiphertextBlob: Buffer.from(encryptedData.encryptedData, 'base64')
        }).promise();
        
        return result.Plaintext.toString('utf8');
        
      } else if (this.hsmConfig.provider === 'azure-keyvault') {
        const result = await this.hsm.decrypt(
          keyId, 
          'RSA-OAEP', 
          Buffer.from(encryptedData.encryptedData, 'base64')
        );
        
        return result.result.toString('utf8');
      }
      
      throw new Error('HSM decryption not implemented for this provider');
    } catch (error) {
      logger.error('HSM decryption failed:', error);
      throw error;
    }
  }

  /**
   * Encrypt data using local key
   */
  async encryptWithLocalKey(data) {
    try {
      const salt = crypto.randomBytes(this.saltLength);
      const iv = crypto.randomBytes(this.ivLength);
      
      // Derive key from master key
      const key = crypto.pbkdf2Sync(
        config.security.encryptionKey,
        salt,
        this.keyDerivationIterations,
        32,
        'sha256'
      );
      
      // Encrypt data
      const cipher = crypto.createCipher(this.encryptionAlgorithm, key);
      cipher.setAAD(Buffer.from('jiranipay-auth-data'));
      
      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      return {
        encryptedData: encrypted,
        salt: salt.toString('hex'),
        iv: iv.toString('hex'),
        tag: tag.toString('hex'),
        algorithm: this.encryptionAlgorithm,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Local encryption failed:', error);
      throw error;
    }
  }

  /**
   * Decrypt data using local key
   */
  async decryptWithLocalKey(encryptedData) {
    try {
      const salt = Buffer.from(encryptedData.salt, 'hex');
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const tag = Buffer.from(encryptedData.tag, 'hex');
      
      // Derive key from master key
      const key = crypto.pbkdf2Sync(
        config.security.encryptionKey,
        salt,
        this.keyDerivationIterations,
        32,
        'sha256'
      );
      
      // Decrypt data
      const decipher = crypto.createDecipher(encryptedData.algorithm, key);
      decipher.setAAD(Buffer.from('jiranipay-auth-data'));
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(encryptedData.encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      logger.error('Local decryption failed:', error);
      throw error;
    }
  }

  /**
   * Generate secure random tokens
   */
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash sensitive data with salt
   */
  async hashSensitiveData(data, salt = null) {
    try {
      if (!salt) {
        salt = crypto.randomBytes(this.saltLength);
      } else if (typeof salt === 'string') {
        salt = Buffer.from(salt, 'hex');
      }
      
      const hash = crypto.pbkdf2Sync(
        data,
        salt,
        this.keyDerivationIterations,
        64,
        'sha256'
      );
      
      return {
        hash: hash.toString('hex'),
        salt: salt.toString('hex'),
        iterations: this.keyDerivationIterations
      };
    } catch (error) {
      logger.error('Hashing failed:', error);
      throw error;
    }
  }

  /**
   * Verify hashed data
   */
  async verifyHashedData(data, hashedData) {
    try {
      const salt = Buffer.from(hashedData.salt, 'hex');
      const hash = crypto.pbkdf2Sync(
        data,
        salt,
        hashedData.iterations,
        64,
        'sha256'
      );
      
      return crypto.timingSafeEqual(
        Buffer.from(hashedData.hash, 'hex'),
        hash
      );
    } catch (error) {
      logger.error('Hash verification failed:', error);
      return false;
    }
  }

  /**
   * Initialize security monitoring
   */
  async initializeSecurityMonitoring() {
    try {
      // Set up security event monitoring
      setInterval(async () => {
        await this.monitorSecurityEvents();
      }, 60000); // Check every minute
      
      // Set up certificate expiry monitoring
      setInterval(async () => {
        await this.monitorCertificateExpiry();
      }, 24 * 60 * 60 * 1000); // Check daily
      
      logger.info('Security monitoring initialized');
    } catch (error) {
      logger.error('Failed to initialize security monitoring:', error);
    }
  }

  /**
   * Monitor security events
   */
  async monitorSecurityEvents() {
    try {
      // Check for suspicious login patterns
      const suspiciousLogins = await redisService.get('suspicious_logins') || [];
      if (suspiciousLogins.length > 10) {
        logger.security('High number of suspicious login attempts detected', {
          count: suspiciousLogins.length,
          timeframe: '1 hour'
        });
      }
      
      // Check for failed authentication attempts
      const failedAttempts = await redisService.get('failed_auth_attempts') || [];
      if (failedAttempts.length > 50) {
        logger.security('High number of failed authentication attempts', {
          count: failedAttempts.length,
          timeframe: '1 hour'
        });
      }
      
      // Check for unusual transaction patterns
      await this.monitorTransactionSecurity();
      
    } catch (error) {
      logger.error('Security monitoring failed:', error);
    }
  }

  /**
   * Monitor transaction security
   */
  async monitorTransactionSecurity() {
    try {
      // Check for unusual transaction volumes
      const hourlyTransactions = await redisService.get('hourly_transactions') || 0;
      const averageHourlyTransactions = await redisService.get('avg_hourly_transactions') || 100;
      
      if (hourlyTransactions > averageHourlyTransactions * 3) {
        logger.security('Unusual transaction volume detected', {
          current: hourlyTransactions,
          average: averageHourlyTransactions,
          threshold: averageHourlyTransactions * 3
        });
      }
      
      // Check for large transaction amounts
      const largeTransactions = await redisService.get('large_transactions') || [];
      if (largeTransactions.length > 5) {
        logger.security('Multiple large transactions detected', {
          count: largeTransactions.length,
          timeframe: '1 hour'
        });
      }
      
    } catch (error) {
      logger.error('Transaction security monitoring failed:', error);
    }
  }

  /**
   * Monitor certificate expiry
   */
  async monitorCertificateExpiry() {
    try {
      const now = Date.now();
      const warningPeriod = 7 * 24 * 60 * 60 * 1000; // 7 days
      
      for (const [hostname, certInfo] of this.pinnedCertificates) {
        const timeToExpiry = certInfo.expiresAt.getTime() - now;
        
        if (timeToExpiry < warningPeriod) {
          logger.security('Certificate expiring soon', {
            hostname,
            expiresAt: certInfo.expiresAt,
            daysRemaining: Math.floor(timeToExpiry / (24 * 60 * 60 * 1000))
          });
        }
      }
    } catch (error) {
      logger.error('Certificate expiry monitoring failed:', error);
    }
  }

  /**
   * Validate password against security policy
   */
  validatePassword(password) {
    const policy = this.securityPolicies.passwordPolicy;
    const errors = [];
    
    if (password.length < policy.minLength) {
      errors.push(`Password must be at least ${policy.minLength} characters long`);
    }
    
    if (policy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (policy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (policy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (policy.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate security report
   */
  async generateSecurityReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        hsmStatus: this.hsmConfig.enabled ? 'enabled' : 'disabled',
        pinnedCertificates: this.pinnedCertificates.size,
        securityPolicies: this.securityPolicies,
        recentSecurityEvents: await redisService.lrange('security_events', 0, 99),
        certificateStatus: Array.from(this.pinnedCertificates.entries()).map(([hostname, cert]) => ({
          hostname,
          expiresAt: cert.expiresAt,
          daysToExpiry: Math.floor((cert.expiresAt.getTime() - Date.now()) / (24 * 60 * 60 * 1000))
        }))
      };
      
      return report;
    } catch (error) {
      logger.error('Failed to generate security report:', error);
      throw error;
    }
  }

  /**
   * Health check for security service
   */
  async healthCheck() {
    try {
      const checks = {
        hsmConnectivity: false,
        certificatePinning: false,
        encryptionService: false,
        securityMonitoring: false
      };
      
      // Check HSM connectivity
      if (this.hsmConfig.enabled) {
        try {
          if (this.hsmConfig.provider === 'aws-cloudhsm') {
            await this.hsm.describeClusters().promise();
          }
          checks.hsmConnectivity = true;
        } catch (error) {
          logger.error('HSM health check failed:', error);
        }
      } else {
        checks.hsmConnectivity = true; // Not applicable
      }
      
      // Check certificate pinning
      checks.certificatePinning = this.pinnedCertificates.size > 0;
      
      // Check encryption service
      try {
        const testData = 'health-check-test';
        const encrypted = await this.encryptWithLocalKey(testData);
        const decrypted = await this.decryptWithLocalKey(encrypted);
        checks.encryptionService = decrypted === testData;
      } catch (error) {
        logger.error('Encryption health check failed:', error);
      }
      
      // Check security monitoring
      checks.securityMonitoring = true; // Always true if service is running
      
      const allHealthy = Object.values(checks).every(check => check === true);
      
      return {
        status: allHealthy ? 'healthy' : 'degraded',
        checks,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const securityService = new SecurityService();

module.exports = securityService;
