import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import sendMoneyService from '../services/sendMoneyService';

/**
 * ManualRecipientScreen - Manual phone number entry
 * Features phone number validation, network detection, and recipient verification
 */
const ManualRecipientScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [validatedRecipient, setValidatedRecipient] = useState(null);
  const [networkProvider, setNetworkProvider] = useState(null);

  const quickNumbers = [
    { prefix: '+256 77', label: 'MTN', color: '#FFCC00', icon: '📱' },
    { prefix: '+256 70', label: 'Airtel', color: '#FF0000', icon: '📶' },
    { prefix: '+256 78', label: 'MTN', color: '#FFCC00', icon: '📱' },
    { prefix: '+256 71', label: 'UTL', color: '#0066CC', icon: '📡' },
  ];

  const handlePhoneNumberChange = (value) => {
    // Remove any non-numeric characters except +
    let cleanValue = value.replace(/[^\d+]/g, '');
    
    // Auto-format Uganda numbers
    if (cleanValue.startsWith('0') && cleanValue.length > 1) {
      cleanValue = '+256' + cleanValue.substring(1);
    } else if (cleanValue.startsWith('256') && !cleanValue.startsWith('+256')) {
      cleanValue = '+' + cleanValue;
    } else if (/^\d{9}$/.test(cleanValue)) {
      cleanValue = '+256' + cleanValue;
    }
    
    setPhoneNumber(cleanValue);
    setValidatedRecipient(null);
    
    // Detect network provider
    if (cleanValue.length >= 7) {
      const provider = sendMoneyService.detectNetworkProvider(cleanValue);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider(null);
    }
  };

  const handleQuickNumber = (prefix) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setPhoneNumber(prefix + ' ');
  };

  const validateRecipient = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Invalid Number', 'Please enter a phone number');
      return;
    }

    try {
      setValidating(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const result = await sendMoneyService.validateRecipient(phoneNumber);
      
      if (result.success) {
        setValidatedRecipient(result.recipient);
        Alert.alert(
          'Recipient Found',
          `${result.recipient.name} (${result.recipient.phoneNumber})\n\nProceed with transfer?`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Continue', 
              onPress: () => handleContinue(result.recipient)
            }
          ]
        );
      } else {
        Alert.alert('Validation Error', result.error);
      }
    } catch (error) {
      console.error('Recipient validation error:', error);
      Alert.alert('Error', 'Failed to validate recipient. Please try again.');
    } finally {
      setValidating(false);
    }
  };

  const handleContinue = (recipient = null) => {
    const finalRecipient = recipient || validatedRecipient;
    
    if (!finalRecipient) {
      validateRecipient();
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate('TransferAmount', { recipient: finalRecipient });
  };

  const isValidPhoneNumber = () => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    return cleaned.length >= 12 && phoneNumber.startsWith('+256');
  };

  const renderPhoneInput = () => (
    <View style={styles.inputSection}>
      <Text style={styles.sectionTitle}>Enter Phone Number</Text>
      <Text style={styles.sectionSubtitle}>
        Enter the recipient's phone number to send money
      </Text>
      
      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneInputWrapper}>
          <TextInput
            style={styles.phoneInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder="+256 703 089 916"
            placeholderTextColor={Colors.neutral.warmGray}
            keyboardType="phone-pad"
            maxLength={17}
            autoFocus
          />
          
          {networkProvider && networkProvider.id !== 'unknown' && (
            <View style={[
              styles.networkBadge,
              { backgroundColor: networkProvider.color + '20' }
            ]}>
              <Text style={[
                styles.networkText,
                { color: networkProvider.color }
              ]}>
                {networkProvider.name}
              </Text>
            </View>
          )}
        </View>
        
        {isValidPhoneNumber() && (
          <TouchableOpacity
            style={styles.validateButton}
            onPress={validateRecipient}
            disabled={validating}
            activeOpacity={0.7}
          >
            {validating ? (
              <Text style={styles.validateButtonText}>...</Text>
            ) : (
              <>
                <Ionicons name="search" size={16} color={Colors.primary.main} />
                <Text style={styles.validateButtonText}>Verify</Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {validatedRecipient && (
        <View style={styles.recipientCard}>
          <View style={styles.recipientInfo}>
            <View style={styles.recipientAvatar}>
              <Text style={styles.recipientAvatarText}>
                {validatedRecipient.name.charAt(0).toUpperCase()}
              </Text>
            </View>
            
            <View style={styles.recipientDetails}>
              <Text style={styles.recipientName}>{validatedRecipient.name}</Text>
              <Text style={styles.recipientPhone}>{validatedRecipient.phoneNumber}</Text>
              {validatedRecipient.provider && (
                <View style={[
                  styles.providerBadge,
                  { backgroundColor: validatedRecipient.provider.color + '20' }
                ]}>
                  <Text style={[
                    styles.providerText,
                    { color: validatedRecipient.provider.color }
                  ]}>
                    {validatedRecipient.provider.name}
                  </Text>
                </View>
              )}
            </View>
            
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={24} color={Colors.status.success} />
            </View>
          </View>
        </View>
      )}
    </View>
  );

  const renderQuickNumbers = () => (
    <View style={styles.quickNumbersSection}>
      <Text style={styles.sectionTitle}>Quick Start</Text>
      <Text style={styles.sectionSubtitle}>
        Tap a network prefix to get started
      </Text>

      <View style={styles.quickNumbers}>
        {quickNumbers.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.quickNumberButton,
              { borderColor: item.color + '30' }
            ]}
            onPress={() => handleQuickNumber(item.prefix)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.quickNumberIcon,
              { backgroundColor: item.color + '20' }
            ]}>
              <Text style={styles.quickNumberEmoji}>{item.icon}</Text>
            </View>
            <View style={styles.quickNumberContent}>
              <Text style={styles.quickNumberPrefix}>{item.prefix}</Text>
              <Text style={[
                styles.quickNumberLabel,
                { color: item.color }
              ]}>
                {item.label}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderInstructions = () => (
    <View style={styles.instructionsSection}>
      <Text style={styles.instructionsTitle}>Supported Formats</Text>
      <View style={styles.instructionsList}>
        <View style={styles.instructionItem}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.instructionText}>+256 703 089 916</Text>
        </View>
        <View style={styles.instructionItem}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.instructionText}>0703089916</Text>
        </View>
        <View style={styles.instructionItem}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.instructionText}>703089916</Text>
        </View>
      </View>
      
      <View style={styles.noteContainer}>
        <Ionicons name="information-circle" size={16} color={Colors.status.info} />
        <Text style={styles.noteText}>
          Only Uganda phone numbers are supported for transfers
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Enter Phone Number</Text>
        <View style={styles.headerSpacer} />
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {renderPhoneInput()}
          {renderQuickNumbers()}
          {renderInstructions()}
        </ScrollView>

        {/* Continue Button */}
        {validatedRecipient && (
          <View style={styles.bottomSection}>
            <TouchableOpacity
              style={styles.continueButton}
              onPress={() => handleContinue()}
              activeOpacity={0.8}
            >
              <Text style={styles.continueButtonText}>Continue</Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  inputSection: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 20,
    lineHeight: 20,
  },
  phoneInputContainer: {
    gap: 12,
  },
  phoneInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 2,
    borderColor: Colors.primary.main + '30',
    gap: 12,
  },
  phoneInput: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  networkBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  networkText: {
    fontSize: 12,
    fontWeight: '600',
  },
  validateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main + '10',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 6,
  },
  validateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary.main,
  },
  recipientCard: {
    backgroundColor: Colors.status.success + '10',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.status.success + '30',
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recipientAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipientAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  recipientDetails: {
    flex: 1,
  },
  recipientName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  recipientPhone: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 6,
  },
  providerBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  providerText: {
    fontSize: 12,
    fontWeight: '600',
  },
  verifiedBadge: {
    padding: 4,
  },
  quickNumbersSection: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  quickNumbers: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickNumberButton: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    flex: 1,
    minWidth: '47%',
    maxWidth: '48%',
  },
  quickNumberIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  quickNumberEmoji: {
    fontSize: 16,
  },
  quickNumberContent: {
    flex: 1,
  },
  quickNumberPrefix: {
    fontSize: 13,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  quickNumberLabel: {
    fontSize: 11,
    fontWeight: '600',
  },
  instructionsSection: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  instructionsList: {
    gap: 12,
    marginBottom: 20,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    fontFamily: 'monospace',
  },
  noteContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.status.info + '10',
    padding: 12,
    borderRadius: 8,
    gap: 8,
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: Colors.status.info,
    lineHeight: 20,
  },
  bottomSection: {
    padding: 20,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  continueButton: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
});

export default ManualRecipientScreen;
