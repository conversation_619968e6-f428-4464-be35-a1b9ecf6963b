/**
 * Wallet Service Tests
 * Comprehensive tests for wallet management functionality
 */

const request = require('supertest');
const app = require('../src/server');
const walletService = require('../src/services/walletService');
const currencyService = require('../src/services/currencyService');
const databaseService = require('../src/services/database');

describe('Wallet Management', () => {
  let authToken;
  let userId;
  let testUser2Id;
  let authToken2;

  beforeAll(async () => {
    // Initialize database connection
    await databaseService.initialize();
    
    // Create test users and get auth tokens
    const user1Response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        phoneNumber: '+256700000001',
        fullName: 'Test User 1',
        password: 'testpass123',
        countryCode: 'UG'
      });

    const user2Response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        phoneNumber: '+256700000002',
        fullName: 'Test User 2',
        password: 'testpass123',
        countryCode: 'UG'
      });

    // Verify phone numbers and login
    // (In real tests, you'd mock the OTP verification)
    
    const login1Response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        phoneNumber: '+256700000001',
        password: 'testpass123',
        countryCode: 'UG'
      });

    const login2Response = await request(app)
      .post('/api/v1/auth/login')
      .send({
        phoneNumber: '+256700000002',
        password: 'testpass123',
        countryCode: 'UG'
      });

    authToken = login1Response.body.data.tokens.accessToken;
    userId = login1Response.body.data.user.id;
    authToken2 = login2Response.body.data.tokens.accessToken;
    testUser2Id = login2Response.body.data.user.id;
  });

  afterAll(async () => {
    // Clean up test data
    await databaseService.close();
  });

  describe('Wallet Creation and Retrieval', () => {
    test('should create wallet automatically on first access', async () => {
      const response = await request(app)
        .get('/api/v1/wallets')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.wallet).toHaveProperty('id');
      expect(response.body.data.wallet.balance).toBe(0);
      expect(response.body.data.wallet.currency).toBe('UGX');
      expect(response.body.data.wallet.isActive).toBe(true);
    });

    test('should get wallet balance', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/balance')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('balance');
      expect(response.body.data).toHaveProperty('availableBalance');
      expect(response.body.data).toHaveProperty('currency');
      expect(response.body.data).toHaveProperty('formatted');
    });

    test('should get wallet limits', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/limits')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('dailyLimit');
      expect(response.body.data).toHaveProperty('monthlyLimit');
      expect(response.body.data).toHaveProperty('dailySpent');
      expect(response.body.data).toHaveProperty('dailyRemaining');
    });
  });

  describe('Wallet Top-up', () => {
    test('should initiate wallet top-up', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 50000,
          paymentMethod: 'mobile_money',
          provider: 'mtn'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transactionId');
      expect(response.body.data.amount).toBe(50000);
      expect(response.body.data.status).toBe('pending');
    });

    test('should reject invalid top-up amount', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: -1000,
          paymentMethod: 'mobile_money'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject invalid payment method', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/topup')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          paymentMethod: 'invalid_method'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Money Transfer', () => {
    beforeEach(async () => {
      // Add some balance to the first user's wallet for testing
      await walletService.updateBalance(userId, 100000, 'credit', 'Test balance');
    });

    test('should transfer money between users', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 25000,
          phoneNumber: '+256700000002',
          description: 'Test transfer'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transferId');
      expect(response.body.data.amount).toBe(25000);
      expect(response.body.data.status).toBe('completed');
    });

    test('should reject transfer to non-existent user', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          phoneNumber: '+256700000999',
          description: 'Test transfer'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });

    test('should reject transfer to self', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 10000,
          phoneNumber: '+256700000001',
          description: 'Self transfer'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    test('should reject transfer with insufficient balance', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/transfer')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 1000000, // More than available balance
          phoneNumber: '+256700000002',
          description: 'Large transfer'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Transaction History', () => {
    test('should get transaction history', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('transactions');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.transactions)).toBe(true);
    });

    test('should filter transaction history by type', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions?type=transfer')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      
      // All transactions should be of type 'transfer'
      response.body.data.transactions.forEach(tx => {
        expect(tx.type).toBe('transfer');
      });
    });

    test('should paginate transaction history', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/transactions?page=1&limit=5')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.limit).toBe(5);
    });
  });

  describe('Currency Operations', () => {
    test('should get supported currencies', async () => {
      const response = await request(app)
        .get('/api/v1/wallets/currencies')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('currencies');
      expect(response.body.data).toHaveProperty('exchangeRates');
      expect(Array.isArray(response.body.data.currencies)).toBe(true);
    });

    test('should convert currency amounts', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/convert')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 100000,
          fromCurrency: 'UGX',
          toCurrency: 'KES'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('originalAmount');
      expect(response.body.data).toHaveProperty('convertedAmount');
      expect(response.body.data).toHaveProperty('exchangeRate');
      expect(response.body.data.originalAmount).toBe(100000);
    });

    test('should reject invalid currency conversion', async () => {
      const response = await request(app)
        .post('/api/v1/wallets/convert')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          amount: 100000,
          fromCurrency: 'USD', // Unsupported currency
          toCurrency: 'UGX'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('Wallet Service Unit Tests', () => {
    test('should validate currency amounts correctly', () => {
      const validAmount = currencyService.validateAmount(1000, 'UGX');
      expect(validAmount.valid).toBe(true);
      expect(validAmount.amount).toBe(1000);

      const invalidAmount = currencyService.validateAmount(-100, 'UGX');
      expect(invalidAmount.valid).toBe(false);

      const zeroAmount = currencyService.validateAmount(0, 'UGX');
      expect(zeroAmount.valid).toBe(false);
    });

    test('should format currency correctly', () => {
      const formatted = currencyService.formatCurrency(1000000, 'UGX');
      expect(formatted).toContain('UGX');
      expect(formatted).toContain('1,000,000');
    });

    test('should handle currency conversion', async () => {
      const conversion = await currencyService.convertCurrency(100000, 'UGX', 'KES');
      expect(conversion).toHaveProperty('originalAmount');
      expect(conversion).toHaveProperty('convertedAmount');
      expect(conversion).toHaveProperty('exchangeRate');
      expect(conversion.originalAmount).toBe(100000);
    });
  });
});
