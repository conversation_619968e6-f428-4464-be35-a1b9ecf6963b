import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  FlatList,
  RefreshControl,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import productionDataService from '../services/productionDataService';
import walletService from '../services/walletService';
import authService from '../services/authService';
import { isProductionMode } from '../config/environment';

const TransactionHistoryScreen = ({ navigation }) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [error, setError] = useState(null);
  const [hasData, setHasData] = useState(false);

  // Remove animation values for now to ensure content is always visible

  const filterOptions = [
    { id: 'all', name: 'All', icon: 'list-outline' },
    { id: 'credit', name: 'Received', icon: 'arrow-down-circle-outline' },
    { id: 'debit', name: 'Sent', icon: 'arrow-up-circle-outline' },
    { id: 'bills', name: 'Bills', icon: 'receipt-outline' },
    { id: 'topup', name: 'Top-ups', icon: 'add-circle-outline' },
  ];

  useEffect(() => {
    loadTransactions();
  }, []);

  useEffect(() => {
    filterTransactions();
  }, [searchQuery, selectedFilter, transactions]);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Loading transaction history...');

      // Get current user
      const user = authService.getCurrentUser();
      if (!user) {
        console.error('❌ No authenticated user found');
        setError('authentication');
        setTransactions([]);
        setHasData(false);
        return;
      }

      // Load transaction data using the correct method
      let result = null;

      // Try walletService first (preferred method)
      try {
        console.log('📊 Attempting to load transactions via walletService...');
        result = await walletService.getRecentTransactions(50);

        if (!result.success || !result.data || result.data.length === 0) {
          console.log('⚠️ WalletService returned no transactions, trying productionDataService...');
          throw new Error('No transactions from walletService');
        }

        console.log(`✅ Loaded ${result.data.length} transactions from walletService`);
      } catch (walletError) {
        console.log('⚠️ WalletService failed, trying productionDataService:', walletError.message);

        // Fallback to productionDataService with correct method name
        result = await productionDataService.getTransactionData(user.id, 50);

        if (result.success) {
          console.log(`✅ Loaded ${result.data?.length || 0} transactions from productionDataService`);
        }
      }

      if (result && result.success) {
        // Ensure we have data to work with
        const transactionData = result.data || [];

        const formattedTransactions = transactionData.map(transaction => ({
          id: transaction.id,
          type: transaction.transaction_type === 'deposit' ? 'credit' : 'debit',
          category: transaction.category || 'transfer',
          amount: transaction.amount,
          description: transaction.description,
          timestamp: new Date(transaction.created_at),
          status: transaction.status,
          reference: transaction.reference_number,
          recipient: transaction.provider || transaction.provider_name || 'Unknown',
          phone: transaction.account_number || ''
        }));

        setTransactions(formattedTransactions);
        setHasData(formattedTransactions.length > 0);
        setError(null);
        console.log(`✅ Loaded ${formattedTransactions.length} transactions successfully`);
      } else {
        // Handle cases where result exists but success is false
        const errorMessage = result?.error || 'Unknown error occurred';

        // Distinguish between network errors and empty data
        if (errorMessage.includes('network') || errorMessage.includes('connection') || errorMessage.includes('fetch')) {
          setError('network');
          console.error('❌ Network error loading transactions:', errorMessage);
        } else if (errorMessage.includes('not authenticated') || errorMessage.includes('authentication')) {
          setError('authentication');
          console.error('❌ Authentication error loading transactions:', errorMessage);
        } else {
          // Likely just no data available - not an error
          setTransactions([]);
          setHasData(false);
          setError(null);
          console.log('ℹ️ No transactions found for user:', errorMessage);
        }
      }
    } catch (error) {
      console.error('❌ Error loading transactions:', error);
      // Check if it's a network-related error
      if (error.message && (error.message.includes('network') || error.message.includes('fetch') || error.message.includes('connection'))) {
        setError('network');
      } else {
        setError('general');
      }
      setTransactions([]);
      setHasData(false);
    } finally {
      setLoading(false);
      console.log('✅ Loading complete, loading state:', false);
    }
  };

  const filterTransactions = () => {
    let filtered = transactions;

    // Filter by type
    if (selectedFilter !== 'all') {
      if (selectedFilter === 'bills') {
        filtered = filtered.filter(t => t.category === 'bills');
      } else if (selectedFilter === 'topup') {
        filtered = filtered.filter(t => t.category === 'topup');
      } else {
        filtered = filtered.filter(t => t.type === selectedFilter);
      }
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.description.toLowerCase().includes(query) ||
        t.reference.toLowerCase().includes(query) ||
        (t.recipient && t.recipient.toLowerCase().includes(query))
      );
    }

    setFilteredTransactions(filtered);
    console.log('✅ Filtered transactions:', filtered.length, 'from', transactions.length);
  };

  const handleFilterSelect = (filterId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedFilter(filterId);
  };

  const handleTransactionPress = (transaction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    const details = [
      `Reference: ${transaction.reference}`,
      `Date: ${transaction.timestamp.toLocaleDateString()} at ${transaction.timestamp.toLocaleTimeString()}`,
      `Status: ${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}`,
    ];
    
    if (transaction.recipient) {
      details.push(`Recipient: ${transaction.recipient}`);
    }
    if (transaction.phone) {
      details.push(`Phone: ${transaction.phone}`);
    }
    if (transaction.accountNumber) {
      details.push(`Account: ${transaction.accountNumber}`);
    }
    if (transaction.source) {
      details.push(`Source: ${transaction.source}`);
    }
    
    Alert.alert(
      'Transaction Details',
      details.join('\n'),
      [
        { text: 'Close', style: 'cancel' },
        { text: 'Share Receipt', onPress: () => handleShareReceipt(transaction) }
      ]
    );
  };

  const handleShareReceipt = async (transaction) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const receiptHtml = generateReceiptHTML(transaction);
      const { uri } = await Print.printToFileAsync({
        html: receiptHtml,
        base64: false,
      });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Transaction Receipt',
        });
      } else {
        Alert.alert('Success', 'Receipt generated successfully!');
      }
    } catch (error) {
      console.error('Error sharing receipt:', error);
      Alert.alert('Error', 'Failed to generate receipt. Please try again.');
    }
  };

  const handleExport = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setShowExportOptions(true);
  };

  const exportToPDF = async () => {
    try {
      setExporting(true);
      setShowExportOptions(false);

      const htmlContent = generateTransactionHistoryHTML(filteredTransactions);
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(uri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Export Transaction History',
        });
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error exporting PDF:', error);
      Alert.alert('Error', 'Failed to export PDF. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const exportToCSV = async () => {
    try {
      setExporting(true);
      setShowExportOptions(false);

      const csvContent = generateCSVContent(filteredTransactions);
      const fileName = `transactions_${new Date().toISOString().split('T')[0]}.csv`;
      const fileUri = FileSystem.documentDirectory + fileName;

      await FileSystem.writeAsStringAsync(fileUri, csvContent, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'text/csv',
          dialogTitle: 'Export Transaction History',
        });
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      Alert.alert('Error', 'Failed to export CSV. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const generateReceiptHTML = (transaction) => {
    const formatAmount = (amount) => `UGX ${amount.toLocaleString()}`;
    const formatDate = (date) => date.toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Transaction Receipt</title>
          <style>
            body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
            .receipt { max-width: 400px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #E53E3E, #C53030); color: white; padding: 24px; text-align: center; }
            .logo { font-size: 24px; font-weight: bold; margin-bottom: 8px; }
            .subtitle { opacity: 0.9; font-size: 14px; }
            .content { padding: 24px; }
            .status { text-align: center; margin-bottom: 24px; }
            .status-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
            .status-completed { background: #D4F4DD; color: #0F5132; }
            .amount { text-align: center; margin-bottom: 24px; }
            .amount-value { font-size: 32px; font-weight: bold; color: #E53E3E; margin-bottom: 4px; }
            .amount-label { color: #6c757d; font-size: 14px; }
            .details { border-top: 1px solid #e9ecef; padding-top: 20px; }
            .detail-row { display: flex; justify-content: space-between; margin-bottom: 12px; }
            .detail-label { color: #6c757d; font-size: 14px; }
            .detail-value { font-weight: 600; font-size: 14px; text-align: right; }
            .reference { background: #f8f9fa; padding: 12px; border-radius: 8px; margin-top: 20px; text-align: center; }
            .reference-label { color: #6c757d; font-size: 12px; margin-bottom: 4px; }
            .reference-value { font-family: monospace; font-size: 14px; font-weight: bold; }
            .footer { text-align: center; padding: 20px; color: #6c757d; font-size: 12px; border-top: 1px solid #e9ecef; }
          </style>
        </head>
        <body>
          <div class="receipt">
            <div class="header">
              <div class="logo">JiraniPay</div>
              <div class="subtitle">Transaction Receipt</div>
            </div>
            <div class="content">
              <div class="status">
                <span class="status-badge status-completed">${transaction.status}</span>
              </div>
              <div class="amount">
                <div class="amount-value">${formatAmount(transaction.amount)}</div>
                <div class="amount-label">${transaction.type === 'credit' ? 'Received' : 'Sent'}</div>
              </div>
              <div class="details">
                <div class="detail-row">
                  <span class="detail-label">Description</span>
                  <span class="detail-value">${transaction.description}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Date & Time</span>
                  <span class="detail-value">${formatDate(transaction.timestamp)}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">Category</span>
                  <span class="detail-value">${transaction.category}</span>
                </div>
                ${transaction.recipient ? `
                <div class="detail-row">
                  <span class="detail-label">${transaction.type === 'credit' ? 'From' : 'To'}</span>
                  <span class="detail-value">${transaction.recipient}</span>
                </div>
                ` : ''}
                ${transaction.phone ? `
                <div class="detail-row">
                  <span class="detail-label">Phone</span>
                  <span class="detail-value">${transaction.phone}</span>
                </div>
                ` : ''}
              </div>
              <div class="reference">
                <div class="reference-label">Transaction Reference</div>
                <div class="reference-value">${transaction.reference}</div>
              </div>
            </div>
            <div class="footer">
              Generated on ${new Date().toLocaleDateString('en-UG')}<br>
              JiraniPay - Your trusted financial partner
            </div>
          </div>
        </body>
      </html>
    `;
  };

  const generateTransactionHistoryHTML = (transactions) => {
    const formatAmount = (amount) => `UGX ${amount.toLocaleString()}`;
    const formatDate = (date) => date.toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    const transactionRows = transactions.map(transaction => `
      <tr>
        <td>${formatDate(transaction.timestamp)}</td>
        <td>${transaction.description}</td>
        <td>${transaction.category}</td>
        <td style="color: ${transaction.type === 'credit' ? '#28a745' : '#dc3545'};">
          ${transaction.type === 'credit' ? '+' : '-'}${formatAmount(transaction.amount)}
        </td>
        <td>
          <span class="status-badge status-${transaction.status}">${transaction.status}</span>
        </td>
        <td style="font-family: monospace; font-size: 12px;">${transaction.reference}</td>
      </tr>
    `).join('');

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Transaction History</title>
          <style>
            body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
            .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #E53E3E, #C53030); color: white; padding: 32px; text-align: center; }
            .logo { font-size: 28px; font-weight: bold; margin-bottom: 8px; }
            .subtitle { opacity: 0.9; font-size: 16px; }
            .summary { padding: 24px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; }
            .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .summary-item { text-align: center; }
            .summary-value { font-size: 24px; font-weight: bold; color: #E53E3E; margin-bottom: 4px; }
            .summary-label { color: #6c757d; font-size: 14px; }
            .content { padding: 24px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #e9ecef; }
            th { background: #f8f9fa; font-weight: 600; color: #495057; }
            .status-badge { padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; text-transform: uppercase; }
            .status-completed { background: #D4F4DD; color: #0F5132; }
            .status-pending { background: #FFF3CD; color: #856404; }
            .status-failed { background: #F8D7DA; color: #721C24; }
            .footer { text-align: center; padding: 20px; color: #6c757d; font-size: 12px; border-top: 1px solid #e9ecef; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">JiraniPay</div>
              <div class="subtitle">Transaction History Report</div>
            </div>
            <div class="summary">
              <div class="summary-grid">
                <div class="summary-item">
                  <div class="summary-value">${transactions.length}</div>
                  <div class="summary-label">Total Transactions</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">${formatAmount(transactions.reduce((sum, t) => sum + (t.type === 'credit' ? t.amount : 0), 0))}</div>
                  <div class="summary-label">Total Received</div>
                </div>
                <div class="summary-item">
                  <div class="summary-value">${formatAmount(transactions.reduce((sum, t) => sum + (t.type === 'debit' ? t.amount : 0), 0))}</div>
                  <div class="summary-label">Total Sent</div>
                </div>
              </div>
            </div>
            <div class="content">
              <h3>Transaction Details</h3>
              <table>
                <thead>
                  <tr>
                    <th>Date & Time</th>
                    <th>Description</th>
                    <th>Category</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Reference</th>
                  </tr>
                </thead>
                <tbody>
                  ${transactionRows}
                </tbody>
              </table>
            </div>
            <div class="footer">
              Generated on ${new Date().toLocaleDateString('en-UG')} at ${new Date().toLocaleTimeString('en-UG')}<br>
              JiraniPay - Your trusted financial partner
            </div>
          </div>
        </body>
      </html>
    `;
  };

  const generateCSVContent = (transactions) => {
    const headers = ['Date', 'Time', 'Description', 'Category', 'Type', 'Amount (UGX)', 'Status', 'Reference', 'Recipient', 'Phone'];

    const rows = transactions.map(transaction => [
      transaction.timestamp.toLocaleDateString('en-UG'),
      transaction.timestamp.toLocaleTimeString('en-UG', { hour: '2-digit', minute: '2-digit' }),
      `"${transaction.description}"`,
      transaction.category,
      transaction.type,
      transaction.amount,
      transaction.status,
      transaction.reference,
      transaction.recipient || '',
      transaction.phone || ''
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactions();
    setRefreshing(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBudgetInsights = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    try {
      if (navigation && navigation.navigate) {
        // Navigate to Analytics with budget insights focus
        navigation.navigate('Analytics', {
          focusSection: 'budgetInsights',
          source: 'transactionHistory'
        });
      } else {
        throw new Error('Navigation not available');
      }
    } catch (error) {
      console.log('⚠️ Navigation error:', error);
      Alert.alert(
        'Budget Insights',
        'Get AI-powered insights from your transactions:\n\n• Spending pattern analysis\n• Budget recommendations\n• Category insights\n• Savings opportunities\n• Smart alerts\n\nNavigating to Budget Insights...'
      );

      // Fallback navigation attempt
      setTimeout(() => {
        try {
          navigation.navigate('Analytics');
        } catch (fallbackError) {
          console.log('⚠️ Fallback navigation failed:', fallbackError);
        }
      }, 2000);
    }
  };

  const formatCurrency = (amount) => {
    return `UGX ${amount.toLocaleString()}`;
  };

  const getTransactionIcon = (transaction) => {
    if (transaction.category === 'bills') return 'receipt-outline';
    if (transaction.category === 'topup') return 'add-circle-outline';
    return transaction.type === 'credit' ? 'arrow-down-circle-outline' : 'arrow-up-circle-outline';
  };

  const getTransactionColor = (transaction) => {
    if (transaction.category === 'bills') return Colors.secondary.savanna;
    if (transaction.category === 'topup') return Colors.accent.gold;
    return transaction.type === 'credit' ? Colors.status.success : Colors.accent.coral;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return Colors.status.success;
      case 'pending':
      case 'processing':
        return Colors.accent.gold;
      case 'failed':
      case 'error':
      case 'cancelled':
        return Colors.status.error;
      default:
        return Colors.neutral.warmGray;
    }
  };

  const renderTransaction = ({ item: transaction }) => (
    <View style={styles.modernTransactionItem}>
      <TouchableOpacity
        style={styles.transactionTouchable}
        onPress={() => handleTransactionPress(transaction)}
        activeOpacity={0.7}
      >
        <View style={styles.transactionContent}>
          <View style={styles.transactionLeft}>
            <View style={[
              styles.modernTransactionIcon,
              { backgroundColor: getTransactionColor(transaction) + '15' }
            ]}>
              <Ionicons
                name={getTransactionIcon(transaction)}
                size={24}
                color={getTransactionColor(transaction)}
              />
            </View>
            <View style={styles.modernTransactionDetails}>
              <Text style={styles.modernTransactionDescription} numberOfLines={1}>
                {transaction.description}
              </Text>
              <View style={styles.transactionMeta}>
                <Text style={styles.modernTransactionTime}>
                  {transaction.timestamp.toLocaleDateString('en-UG', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </Text>
                <Text style={styles.transactionSeparator}>•</Text>
                <Text style={styles.modernTransactionReference} numberOfLines={1}>
                  {transaction.reference}
                </Text>
              </View>
              {transaction.recipient && (
                <Text style={styles.transactionRecipient} numberOfLines={1}>
                  {transaction.type === 'credit' ? 'From' : 'To'} {transaction.recipient}
                </Text>
              )}
            </View>
          </View>
          <View style={styles.transactionRight}>
            <Text style={[
              styles.modernTransactionAmount,
              { color: getTransactionColor(transaction) }
            ]}>
              {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
            </Text>
            <View style={[
              styles.modernStatusBadge,
              { backgroundColor: getStatusColor(transaction.status) + '15' }
            ]}>
              <View style={[
                styles.statusIndicator,
                { backgroundColor: getStatusColor(transaction.status) }
              ]} />
              <Text style={[
                styles.modernStatusText,
                { color: getStatusColor(transaction.status) }
              ]}>
                {transaction.status}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderFilterChip = (filter) => (
    <TouchableOpacity
      key={filter.id}
      style={[
        styles.compactFilterChip,
        selectedFilter === filter.id && styles.selectedCompactFilterChip
      ]}
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        handleFilterSelect(filter.id);
      }}
      activeOpacity={0.8}
    >
      <Ionicons
        name={filter.icon}
        size={16}
        color={selectedFilter === filter.id ? theme.colors.white : theme.colors.primary}
        style={styles.compactFilterIcon}
      />
      <Text style={[
        styles.compactFilterText,
        selectedFilter === filter.id && styles.selectedCompactFilterText
      ]}>
        {filter.name}
      </Text>
    </TouchableOpacity>
  );

  const renderExportModal = () => (
    showExportOptions && (
      <View style={styles.modalOverlay}>
        <View style={styles.exportModal}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Export Options</Text>
            <TouchableOpacity onPress={() => setShowExportOptions(false)}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          <View style={styles.exportOptions}>
            <TouchableOpacity style={styles.exportOption} onPress={exportToPDF}>
              <View style={styles.exportOptionIcon}>
                <Ionicons name="document-text" size={24} color="#E53E3E" />
              </View>
              <View style={styles.exportOptionContent}>
                <Text style={styles.exportOptionTitle}>Export as PDF</Text>
                <Text style={styles.exportOptionSubtitle}>Professional report format</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.exportOption} onPress={exportToCSV}>
              <View style={styles.exportOptionIcon}>
                <Ionicons name="grid" size={24} color="#28a745" />
              </View>
              <View style={styles.exportOptionContent}>
                <Text style={styles.exportOptionTitle}>Export as CSV</Text>
                <Text style={styles.exportOptionSubtitle}>Spreadsheet compatible</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    )
  );

  const renderEmptyState = () => {
    if (error === 'authentication') {
      return (
        <View style={styles.modernEmptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: '#FED7D7' }]}>
            <Ionicons name="person-outline" size={80} color="#E53E3E" />
          </View>
          <Text style={styles.modernEmptyTitle}>Authentication Required</Text>
          <Text style={styles.modernEmptySubtitle}>
            Please log in to view your transaction history
          </Text>
          <TouchableOpacity
            style={[styles.emptyActionButton, { backgroundColor: '#E53E3E' }]}
            onPress={() => navigation.navigate('Login')}
          >
            <Text style={styles.emptyActionText}>Log In</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (error === 'network') {
      return (
        <View style={styles.modernEmptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: '#FED7D7' }]}>
            <Ionicons name="wifi-outline" size={80} color="#E53E3E" />
          </View>
          <Text style={styles.modernEmptyTitle}>Connection Error</Text>
          <Text style={styles.modernEmptySubtitle}>
            Unable to load transactions. Please check your internet connection and try again.
          </Text>
          <TouchableOpacity
            style={[styles.emptyActionButton, { backgroundColor: '#E53E3E' }]}
            onPress={loadTransactions}
          >
            <Text style={styles.emptyActionText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (error === 'general') {
      return (
        <View style={styles.modernEmptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: '#FED7D7' }]}>
            <Ionicons name="alert-circle-outline" size={80} color="#E53E3E" />
          </View>
          <Text style={styles.modernEmptyTitle}>Something went wrong</Text>
          <Text style={styles.modernEmptySubtitle}>
            We encountered an error while loading your transactions. Please try again.
          </Text>
          <TouchableOpacity
            style={[styles.emptyActionButton, { backgroundColor: '#E53E3E' }]}
            onPress={loadTransactions}
          >
            <Text style={styles.emptyActionText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Default empty state (no error, just no data)
    return (
      <View style={styles.modernEmptyContainer}>
        <View style={styles.emptyIconContainer}>
          <Ionicons name="receipt-outline" size={80} color={theme.colors.textSecondary} />
        </View>
        <Text style={styles.modernEmptyTitle}>No transactions yet</Text>
        <Text style={styles.modernEmptySubtitle}>
          {searchQuery
            ? 'Try adjusting your search or filters'
            : 'Your transactions will appear here when you make payments or transfers'
          }
        </Text>
        {!searchQuery && (
          <TouchableOpacity
            style={styles.emptyActionButton}
            onPress={() => {
              try {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                navigation.navigate('BillPayment');
              } catch (error) {
                console.log('⚠️ Navigation error:', error);
                Alert.alert(
                  'Bill Payment',
                  'Navigate to bill payment to start making payments for utilities, airtime, and more.',
                  [{ text: 'OK', style: 'default' }]
                );
              }
            }}
          >
            <Text style={styles.emptyActionText}>Make a Payment</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Enhanced Modern Header with Gradient */}
      <LinearGradient
        colors={['#E67E22', '#D35400', '#E53E3E']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.modernHeader}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton
            navigation={navigation}
            style={styles.modernBackButton}
            iconColor={theme.colors.white}
            iconSize={24}
          />
          <View style={styles.headerTitleContainer}>
            <Text style={styles.modernHeaderTitle}>Transaction History</Text>
            <Text style={styles.headerSubtitle}>
              {filteredTransactions.length} transaction{filteredTransactions.length !== 1 ? 's' : ''}
            </Text>
          </View>
          <TouchableOpacity
            onPress={handleExport}
            style={styles.modernExportButton}
            disabled={exporting}
          >
            {exporting ? (
              <Ionicons name="sync" size={24} color={theme.colors.white} />
            ) : (
              <Ionicons name="download-outline" size={24} color={theme.colors.white} />
            )}
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Modern Content Container */}
      <View style={styles.modernContent}>
        {/* Enhanced Search Bar */}
        <View style={styles.modernSearchContainer}>
          <View style={styles.modernSearchBar}>
            <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.modernSearchInput}
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholder="Search transactions, references, or recipients..."
              placeholderTextColor={theme.colors.placeholder}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Compact Filter Chips */}
        <View style={styles.compactFiltersSection}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.compactFiltersContainer}
            contentContainerStyle={styles.compactFiltersContent}
          >
            {filterOptions.map(renderFilterChip)}
          </ScrollView>
        </View>

        {/* Compact Budget Insights */}
        <TouchableOpacity style={styles.compactInsightsCard} onPress={handleBudgetInsights}>
          <View style={styles.compactInsightsContent}>
            <Ionicons name="analytics-outline" size={18} color="#E67E22" />
            <Text style={styles.compactInsightsText}>Get AI Budget Insights</Text>
            <Ionicons name="chevron-forward" size={16} color={theme.colors.textSecondary} />
          </View>
        </TouchableOpacity>

        {/* Enhanced Transaction List */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading transactions...</Text>
          </View>
        ) : (
          <FlatList
            data={filteredTransactions}
            renderItem={renderTransaction}
            keyExtractor={(item) => item.id.toString()}
            style={styles.modernTransactionsList}
            contentContainerStyle={styles.modernTransactionsContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={['#E53E3E']}
                tintColor="#E53E3E"
              />
            }
            ListEmptyComponent={renderEmptyState()}
          />
        )}
      </View>

      {/* Export Modal */}
      {renderExportModal()}
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  // Modern Header Styles
  modernHeader: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  modernBackButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.white,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  modernExportButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Enhanced Modern Content Styles
  modernContent: {
    flex: 1,
    backgroundColor: theme.colors.background, // Theme-aware background
    marginTop: -15,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingTop: 5,
  },
  // Enhanced Modern Search Styles
  modernSearchContainer: {
    paddingHorizontal: 20,
    paddingTop: 25,
    paddingBottom: 20,
  },
  modernSearchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    paddingHorizontal: 18,
    paddingVertical: 16,
    elevation: 3,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(230, 126, 34, 0.1)',
  },
  modernSearchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    marginLeft: 12,
    fontWeight: '500',
  },

  // Compact Filter Chip Styles
  compactFiltersSection: {
    backgroundColor: 'rgba(230, 126, 34, 0.03)',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(230, 126, 34, 0.1)',
  },
  compactFiltersContainer: {
    flexGrow: 0,
  },
  compactFiltersContent: {
    paddingHorizontal: 20,
    alignItems: 'center',
  },

  compactFilterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginHorizontal: 4,
    borderWidth: 1.5,
    borderColor: 'rgba(230, 126, 34, 0.2)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedCompactFilterChip: {
    backgroundColor: '#E67E22',
    borderColor: '#D35400',
    shadowColor: '#E67E22',
    shadowOpacity: 0.2,
    elevation: 3,
  },
  compactFilterIcon: {
    marginRight: 6,
  },
  compactFilterText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#E67E22',
    letterSpacing: 0.2,
  },
  selectedCompactFilterText: {
    color: theme.colors.white,
    fontWeight: '700',
  },

  // Modern Transaction List Styles
  modernTransactionsList: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  modernTransactionsContent: {
    padding: 20,
    paddingBottom: 100,
  },
  modernTransactionItem: {
    marginBottom: 18,
  },
  transactionTouchable: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    elevation: 4,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.12,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(230, 126, 34, 0.08)',
  },
  transactionContent: {
    padding: 22,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  modernTransactionIcon: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 18,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  modernTransactionDetails: {
    flex: 1,
  },
  modernTransactionDescription: {
    fontSize: 17,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 8,
    lineHeight: 22,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  modernTransactionTime: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  transactionSeparator: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginHorizontal: 10,
  },
  modernTransactionReference: {
    fontSize: 13,
    color: theme.colors.textSecondary,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    flex: 1,
    fontWeight: '500',
  },
  transactionRecipient: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  modernTransactionAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 22,
  },
  modernStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 6,
  },
  modernStatusText: {
    fontSize: 11,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  // Enhanced Modern Empty State Styles
  modernEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 100,
    paddingHorizontal: 40,
  },
  emptyIconContainer: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: 'rgba(230, 126, 34, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    borderWidth: 3,
    borderColor: 'rgba(230, 126, 34, 0.2)',
  },
  modernEmptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  modernEmptySubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
    maxWidth: 280,
  },
  emptyActionButton: {
    backgroundColor: '#E67E22',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 25,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  emptyActionText: {
    color: theme.colors.white,
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
  },

  // Export Modal Styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  exportModal: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    margin: 20,
    maxWidth: 400,
    width: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  exportOptions: {
    padding: 20,
  },
  exportOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.inputBackground,
    borderRadius: 12,
    marginBottom: 12,
  },
  exportOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  exportOptionContent: {
    flex: 1,
  },
  exportOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
  },
  exportOptionSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },

  // Compact Budget Insights Styles
  compactInsightsCard: {
    backgroundColor: 'rgba(230, 126, 34, 0.08)',
    marginHorizontal: 20,
    marginVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(230, 126, 34, 0.2)',
  },
  compactInsightsContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  compactInsightsText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600',
    color: '#E67E22',
    marginLeft: 10,
    letterSpacing: 0.2,
  },
});

export default TransactionHistoryScreen;
