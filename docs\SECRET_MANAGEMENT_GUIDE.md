# 🔐 Secret Management Guide for JiraniPay

This guide provides comprehensive instructions for managing secrets and credentials securely across all environments.

## 🚨 Security Principles

### Core Rules
1. **NEVER** commit actual credentials to version control
2. **ALWAYS** use environment variables for sensitive data
3. **SEPARATE** environments with different credentials
4. **ROTATE** credentials regularly
5. **VALIDATE** configuration before deployment

### Environment Isolation
```
Development → dev-project.supabase.co
Staging     → staging-project.supabase.co  
Production  → prod-project.supabase.co
```

## 📁 File Structure

```
JiraniPay/
├── .env.example                    # Template with placeholders
├── .env.development.template       # Development template
├── .env.staging.template          # Staging template  
├── .env.production.template       # Production template
├── .env.development.local         # Actual dev credentials (gitignored)
├── .env.staging.local            # Actual staging credentials (gitignored)
├── .env.production.local         # Actual prod credentials (gitignored)
└── backend/
    ├── .env.example              # Backend template
    ├── .env.development.local    # Backend dev credentials (gitignored)
    └── .env.production.local     # Backend prod credentials (gitignored)
```

## 🛠️ Setup Process

### Step 1: Create Supabase Projects

1. **Development Project**
   ```bash
   # Go to https://supabase.com/dashboard
   # Create: "jiranipay-development"
   # Copy URL and anon key
   ```

2. **Staging Project** (Optional)
   ```bash
   # Create: "jiranipay-staging"
   # Copy URL and anon key
   ```

3. **Production Project**
   ```bash
   # Create: "jiranipay-production"
   # Copy URL and anon key
   ```

### Step 2: Environment Configuration

1. **Run Setup Script**
   ```bash
   npm run setup-env
   ```

2. **Manual Setup (Alternative)**
   ```bash
   # Development
   cp .env.example .env.development.local
   # Edit .env.development.local with actual credentials
   
   # Production
   cp .env.production.template .env.production.local
   # Edit .env.production.local with actual credentials
   ```

### Step 3: Validate Configuration

```bash
# Validate current configuration
npm run validate-config

# Test specific environment
EXPO_PUBLIC_ENVIRONMENT=development npm run validate-config
```

## 🔑 Credential Types

### Supabase Credentials
```bash
# Development
EXPO_PUBLIC_SUPABASE_URL=https://dev-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJ...dev-key

# Production  
EXPO_PUBLIC_PROD_SUPABASE_URL=https://prod-project.supabase.co
EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY=eyJ...prod-key
```

### API Keys
```bash
# Mobile Money APIs
MTN_API_KEY=your-mtn-key
AIRTEL_CLIENT_ID=your-airtel-id
AIRTEL_CLIENT_SECRET=your-airtel-secret

# Communication APIs
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
```

### Security Keys
```bash
# Generate with: openssl rand -base64 32
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key
```

## 🚀 Deployment

### Development
```bash
# Start development server
npm run start:dev

# Or manually set environment
EXPO_PUBLIC_ENVIRONMENT=development npm start
```

### Staging
```bash
# Start staging server
npm run start:staging

# Build for staging
npm run build:staging
```

### Production
```bash
# Build for production
npm run build:prod

# Deploy (platform-specific)
# Use your deployment platform's secret management
```

## 🔒 Platform-Specific Secret Management

### Expo EAS
```bash
# Set secrets for EAS builds
eas secret:create --scope project --name EXPO_PUBLIC_PROD_SUPABASE_URL --value "https://..."
eas secret:create --scope project --name EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY --value "eyJ..."
```

### Vercel
```bash
# Set environment variables in Vercel dashboard
# Or use Vercel CLI
vercel env add EXPO_PUBLIC_PROD_SUPABASE_URL production
vercel env add EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY production
```

### Netlify
```bash
# Set in Netlify dashboard under Site settings → Environment variables
# Or use Netlify CLI
netlify env:set EXPO_PUBLIC_PROD_SUPABASE_URL "https://..."
```

### Docker
```bash
# Use Docker secrets or environment files
docker run -e EXPO_PUBLIC_PROD_SUPABASE_URL="https://..." jiranipay
```

### Kubernetes
```yaml
# Create secret
apiVersion: v1
kind: Secret
metadata:
  name: jiranipay-secrets
data:
  supabase-url: <base64-encoded-url>
  supabase-key: <base64-encoded-key>
```

## 🔄 Credential Rotation

### Monthly Rotation (Recommended)
1. Generate new Supabase anon keys
2. Update environment variables
3. Deploy new configuration
4. Revoke old keys

### Emergency Rotation
1. Immediately revoke compromised credentials
2. Generate new credentials
3. Update all environments
4. Notify team of security incident

## 🛡️ Security Best Practices

### Development
- ✅ Use separate development Supabase project
- ✅ Enable debug logging
- ✅ Use test data only
- ✅ Regular credential rotation

### Staging
- ✅ Production-like configuration
- ✅ Real API integrations (sandbox)
- ✅ Performance testing
- ✅ Security testing

### Production
- ✅ Separate production Supabase project
- ✅ Minimal logging (no sensitive data)
- ✅ Real user data protection
- ✅ Monitoring and alerting

## 🚨 Security Incidents

### If Credentials Are Compromised
1. **Immediate Actions**
   - Revoke compromised credentials
   - Generate new credentials
   - Update all deployments
   - Monitor for unauthorized access

2. **Investigation**
   - Review access logs
   - Identify scope of compromise
   - Document incident
   - Implement additional safeguards

3. **Prevention**
   - Review access controls
   - Update security policies
   - Train team on security practices
   - Implement additional monitoring

## 📞 Support

For security-related issues:
1. Check configuration validation: `npm run validate-config`
2. Review setup documentation: `docs/SECURE_ENVIRONMENT_SETUP.md`
3. Run environment setup: `npm run setup-env`
4. Contact security team for credential issues

## ✅ Security Checklist

- [ ] Separate Supabase projects for each environment
- [ ] All credentials in environment variables
- [ ] No hardcoded credentials in source code
- [ ] .env.*.local files in .gitignore
- [ ] Configuration validation passing
- [ ] Regular credential rotation scheduled
- [ ] Team trained on security practices
- [ ] Incident response plan documented
