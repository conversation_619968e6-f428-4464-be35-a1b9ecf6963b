import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';

const EmailVerificationScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [codeSent, setCodeSent] = useState(false);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);

  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    setLoading(true);
    try {
      console.log('🔄 Loading user data for email verification...');

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        console.error('❌ No current user found');
        Alert.alert(t('error'), t('pleaseLogInToContinue'));
        navigation.goBack();
        return;
      }

      console.log('✅ Current user found:', currentUser.id);
      setUser(currentUser);

      // Get user profile to check if email exists (PRODUCTION FIX)
      console.log('📋 Getting user profile...');
      const profileResult = await profileManagementService.getProfile(currentUser.id);

      console.log('📊 Profile result:', profileResult);

      if (profileResult.success) {
        if (profileResult.data) {
          console.log('✅ User profile found:', profileResult.data);
          setUserProfile(profileResult.data);
          if (profileResult.data.email) {
            setEmail(profileResult.data.email);
            console.log('📧 Existing email found:', profileResult.data.email);
          }
        } else if (profileResult.code === 'PROFILE_NOT_FOUND') {
          console.log('⚠️ User profile not found - this is normal for new users');
          setUserProfile(null);
        }
      } else {
        console.error('❌ Failed to get profile:', profileResult.error);
        // Don't show error for missing profile - it's normal for new users
        setUserProfile(null);
      }

      console.log('✅ User data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      Alert.alert(t('errorLoadingData'), t('failedToLoadUserDataPleaseTryAgainOrContactSupport')
      );
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationCode = async () => {
    if (!email || !email.trim()) {
      Alert.alert(t('error'), t('pleaseEnterYourEmailAddress'));
      return;
    }

    if (!isValidEmail(email.trim())) {
      Alert.alert(t('error'), t('pleaseEnterAValidEmailAddress'));
      return;
    }

    setSending(true);
    try {
      // Update profile with email if it's new
      if (!userProfile?.email || userProfile.email !== email.trim()) {
        const updateResult = await profileManagementService.updateProfile(user.id, {
          email: email.trim()
        });
        
        if (!updateResult.success) {
          Alert.alert('Error', updateResult.error || 'Failed to update email');
          return;
        }
      }

      // Simulate sending verification code (in real app, this would send actual email)
      setTimeout(() => {
        setCodeSent(true);
        setSending(false);
        Alert.alert(t('verificationCodeSent'), t('aVerificationCodeHasBeenSentToEmailtrimPleaseCheck')
        );
      }, 2000);

    } catch (error) {
      console.error('❌ Error sending verification code:', error);
      Alert.alert(t('error'), t('failedToSendVerificationCodePleaseTryAgain'));
      setSending(false);
    }
  };

  const verifyEmail = async () => {
    if (!verificationCode || verificationCode.trim().length !== 6) {
      Alert.alert(t('error'), t('pleaseEnterThe6digitVerificationCode'));
      return;
    }

    setVerifying(true);
    try {
      // Simulate verification (in real app, this would verify with backend)
      setTimeout(async () => {
        try {
          // Mark email verification as completed
          const result = await profileManagementService.markStepCompleted(user.id, 'email_verification');
          
          if (result.success) {
            Alert.alert(t('emailVerified'), t('yourEmailAddressHasBeenSuccessfullyVerified'),
              [
                {
                  text: 'OK',
                  onPress: () => navigation.goBack()
                }
              ]
            );
          } else {
            Alert.alert('Error', result.error || 'Failed to mark verification as completed');
          }
        } catch (error) {
          console.error('❌ Error marking step completed:', error);
          Alert.alert(t('error'), t('verificationSuccessfulButFailedToUpdateStatus'));
        } finally {
          setVerifying(false);
        }
      }, 2000);

    } catch (error) {
      console.error('❌ Error verifying email:', error);
      Alert.alert(t('error'), t('failedToVerifyEmailPleaseTryAgain'));
      setVerifying(false);
    }
  };

  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>{t('loading')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>{t('emailVerification')}</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Card */}
        <View style={styles.infoCard}>
          <View style={styles.infoIcon}>
            <Ionicons name="mail-outline" size={24} color={Colors.primary.main} />
          </View>
          <Text style={styles.infoTitle}>{t('verifyYourEmail')}</Text>
          <Text style={styles.infoDescription}>
            {t('verifyYourEmailAddressToSecureYourAccountAndReceiv')}
          </Text>
        </View>

        {/* Email Input */}
        <View style={styles.inputCard}>
          <Text style={styles.inputLabel}>{t('emailAddress')}</Text>
          <TextInput
            style={styles.textInput}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email address"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="email-address"
            autoCapitalize="none"
            editable={!codeSent}
          />
        </View>

        {/* Verification Code Input (shown after code is sent) */}
        {codeSent && (
          <View style={styles.inputCard}>
            <Text style={styles.inputLabel}>{t('verificationCode')}</Text>
            <TextInput
              style={styles.textInput}
              value={verificationCode}
              onChangeText={setVerificationCode}
              placeholder="Enter 6-digit code"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="numeric"
              maxLength={6}
            />
            <Text style={styles.codeHint}>
              {t('checkYourEmailForTheVerificationCode')}
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          {!codeSent ? (
            <TouchableOpacity
              style={[styles.actionButton, sending && styles.actionButtonDisabled]}
              onPress={sendVerificationCode}
              disabled={sending}
              activeOpacity={0.7}
            >
              {sending ? (
                <ActivityIndicator size="small" color={Colors.neutral.white} />
              ) : (
                <Ionicons name="mail-outline" size={20} color={Colors.neutral.white} />
              )}
              <Text style={styles.actionButtonText}>
                {sending ? 'Sending...' : 'Send Verification Code'}
              </Text>
            </TouchableOpacity>
          ) : (
            <>
              <TouchableOpacity
                style={[styles.actionButton, verifying && styles.actionButtonDisabled]}
                onPress={verifyEmail}
                disabled={verifying}
                activeOpacity={0.7}
              >
                {verifying ? (
                  <ActivityIndicator size="small" color={Colors.neutral.white} />
                ) : (
                  <Ionicons name="checkmark-circle-outline" size={20} color={Colors.neutral.white} />
                )}
                <Text style={styles.actionButtonText}>
                  {verifying ? 'Verifying...' : 'Verify Email'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={() => {
                  setCodeSent(false);
                  setVerificationCode('');
                }}
                activeOpacity={0.7}
              >
                <Ionicons name="refresh-outline" size={20} color={Colors.primary.main} />
                <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
                  {t('changeEmail')}
                </Text>
              </TouchableOpacity>
            </>
          )}
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  headerRight: {
    width: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  infoCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  infoIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  infoDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  inputCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: theme.colors.text,
    backgroundColor: theme.colors.background,
  },
  codeHint: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  actionsContainer: {
    marginVertical: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionButtonDisabled: {
    backgroundColor: Colors.neutral.warmGray,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
    marginLeft: 8,
  },
  secondaryButton: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: Colors.primary.main,
  },
  secondaryButtonText: {
    color: Colors.primary.main,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default EmailVerificationScreen;
