/**
 * JiraniPay Manual Translation Guide
 * 
 * This script demonstrates the CORRECT approach to handle translations
 * by manually identifying hardcoded strings and creating proper translation keys.
 */

console.log('🔧 JiraniPay Manual Translation Guide');
console.log('=====================================\n');

// STEP 1: Identify hardcoded strings in each screen
const hardcodedStrings = {
  EditProfileScreen: {
    // Form Labels (these should be proper phrases)
    'Full Name': 'profile.fullName',
    'Email Address': 'profile.emailAddress', 
    'Phone Number': 'profile.phoneNumber',
    'Date of Birth': 'profile.dateOfBirth',
    'Country': 'profile.country',
    'Preferred Language': 'profile.preferredLanguage',
    
    // Validation Messages
    'Full name is required': 'profile.fullNameRequired',
    'Full name must be at least 2 characters': 'profile.fullNameMinLength',
    'Please enter a valid email address': 'profile.validEmailRequired',
    'Phone number is required': 'profile.phoneNumberRequired',
    'Please enter a valid Uganda phone number': 'profile.validUgandaPhoneRequired',
    'Please enter a valid date of birth': 'profile.validDateOfBirthRequired',
    
    // Placeholders
    'Enter your full name': 'profile.enterYourFullName',
    'Enter your email address': 'profile.enterYourEmailAddress',
    'Enter your phone number': 'profile.enterYourPhoneNumber',
    'Select your date of birth': 'profile.selectYourDateOfBirth',
    
    // Actions
    'Save': 'common.save',
    'Cancel': 'common.cancel',
    'Discard': 'profile.discard',
    
    // Messages
    'Your profile has been updated successfully': 'profile.yourProfileHasBeenUpdatedSuccessfully',
    'Unsaved Changes': 'profile.unsavedChanges',
    'You have unsaved changes. Are you sure you want to go back?': 'profile.youHaveUnsavedChangesAreYouSureYouWantToGoBack'
  },
  
  FAQScreen: {
    // Categories (these should be proper phrases)
    'Money Transfer': 'support.moneyTransfer',
    'Bill Payments': 'support.billPayments',
    'Wallet Management': 'support.walletManagement',
    'QR Code Scanner': 'support.qrCodeScanner',
    'Account Security': 'support.accountSecurity',
    
    // Questions and Answers
    'How do I send money to another JiraniPay user?': 'support.howDoISendMoney',
    'Which bills can I pay through JiraniPay?': 'support.whichBillsCanIPay',
    'How do I add money to my wallet?': 'support.howDoIAddMoney',
    'How do I use the QR code scanner?': 'support.howDoIUseQRScanner',
    'How do I verify my account?': 'support.howDoIVerifyAccount'
  },
  
  AIChatScreen: {
    'JiraniPay AI Assistant': 'support.jiranipayAiAssistant',
    'AI is typing...': 'support.aiIsTyping',
    'Quick Help': 'support.quickHelp',
    'Choose what you\'d like help with': 'support.chooseWhatYoudLikeHelpWith',
    'Starting AI Assistant...': 'support.startingAiAssistant',
    'Quick Actions': 'support.quickActions'
  },
  
  ContactSupportScreen: {
    'Contact Support': 'support.contactSupport',
    'We\'re here to help you 24/7': 'support.wereHereToHelpYou247',
    'Customer Support': 'support.customerSupport',
    'Emergency Support': 'support.emergencySupport',
    'Security Emergency': 'support.securityEmergency',
    'Phone Support': 'support.phoneSupport',
    'Email Support': 'support.emailSupport',
    'Technical Support': 'support.technicalSupport'
  }
};

// STEP 2: Create proper English translations
const englishTranslations = {
  profile: {
    // Form Labels - Proper formatting
    fullName: 'Full Name',
    emailAddress: 'Email Address',
    phoneNumber: 'Phone Number',
    dateOfBirth: 'Date of Birth',
    country: 'Country',
    preferredLanguage: 'Preferred Language',
    
    // Validation Messages - Complete sentences
    fullNameRequired: 'Full name is required',
    fullNameMinLength: 'Full name must be at least 2 characters',
    validEmailRequired: 'Please enter a valid email address',
    phoneNumberRequired: 'Phone number is required',
    validUgandaPhoneRequired: 'Please enter a valid Uganda phone number',
    validDateOfBirthRequired: 'Please enter a valid date of birth',
    
    // Placeholders - User-friendly
    enterYourFullName: 'Enter your full name',
    enterYourEmailAddress: 'Enter your email address',
    enterYourPhoneNumber: 'Enter your phone number',
    selectYourDateOfBirth: 'Select your date of birth',
    
    // Messages - Complete sentences
    yourProfileHasBeenUpdatedSuccessfully: 'Your profile has been updated successfully',
    unsavedChanges: 'Unsaved Changes',
    youHaveUnsavedChangesAreYouSureYouWantToGoBack: 'You have unsaved changes. Are you sure you want to go back?',
    discard: 'Discard'
  },
  
  support: {
    // Categories - Proper titles
    moneyTransfer: 'Money Transfer',
    billPayments: 'Bill Payments',
    walletManagement: 'Wallet Management',
    qrCodeScanner: 'QR Code Scanner',
    accountSecurity: 'Account Security',
    
    // AI Chat
    jiranipayAiAssistant: 'JiraniPay AI Assistant',
    aiIsTyping: 'AI is typing...',
    quickHelp: 'Quick Help',
    quickActions: 'Quick Actions',
    
    // Contact Support
    contactSupport: 'Contact Support',
    customerSupport: 'Customer Support',
    emergencySupport: 'Emergency Support',
    phoneSupport: 'Phone Support',
    emailSupport: 'Email Support',
    technicalSupport: 'Technical Support'
  },
  
  common: {
    save: 'Save',
    cancel: 'Cancel',
    ok: 'OK',
    yes: 'Yes',
    no: 'No'
  }
};

// STEP 3: Create proper Swahili translations
const swahiliTranslations = {
  profile: {
    // Form Labels - Proper Swahili
    fullName: 'Jina Kamili',
    emailAddress: 'Anwani ya Barua Pepe',
    phoneNumber: 'Nambari ya Simu',
    dateOfBirth: 'Tarehe ya Kuzaliwa',
    country: 'Nchi',
    preferredLanguage: 'Lugha Unayopendelea',
    
    // Validation Messages - Complete Swahili sentences
    fullNameRequired: 'Jina kamili linahitajika',
    fullNameMinLength: 'Jina kamili lazima liwe na angalau herufi 2',
    validEmailRequired: 'Tafadhali ingiza anwani sahihi ya barua pepe',
    phoneNumberRequired: 'Nambari ya simu inahitajika',
    validUgandaPhoneRequired: 'Tafadhali ingiza nambari sahihi ya simu ya Uganda',
    validDateOfBirthRequired: 'Tafadhali ingiza tarehe sahihi ya kuzaliwa',
    
    // Placeholders - User-friendly Swahili
    enterYourFullName: 'Ingiza jina lako kamili',
    enterYourEmailAddress: 'Ingiza anwani yako ya barua pepe',
    enterYourPhoneNumber: 'Ingiza nambari yako ya simu',
    selectYourDateOfBirth: 'Chagua tarehe yako ya kuzaliwa',
    
    // Messages - Complete Swahili sentences
    yourProfileHasBeenUpdatedSuccessfully: 'Wasifu wako umesasishwa kwa mafanikio',
    unsavedChanges: 'Mabadiliko Yasiyohifadhiwa',
    youHaveUnsavedChangesAreYouSureYouWantToGoBack: 'Una mabadiliko yasiyohifadhiwa. Una uhakika unataka kurudi nyuma?',
    discard: 'Ondoa'
  },
  
  support: {
    // Categories - Proper Swahili titles
    moneyTransfer: 'Uhamisho wa Pesa',
    billPayments: 'Malipo ya Bili',
    walletManagement: 'Usimamizi wa Mkoba',
    qrCodeScanner: 'Kiskani cha Msimbo wa QR',
    accountSecurity: 'Usalama wa Akaunti',
    
    // AI Chat
    jiranipayAiAssistant: 'Msaidizi wa AI wa JiraniPay',
    aiIsTyping: 'AI inaandika...',
    quickHelp: 'Msaada wa Haraka',
    quickActions: 'Vitendo vya Haraka',
    
    // Contact Support
    contactSupport: 'Wasiliana na Msaada',
    customerSupport: 'Msaada wa Wateja',
    emergencySupport: 'Msaada wa Dharura',
    phoneSupport: 'Msaada wa Simu',
    emailSupport: 'Msaada wa Barua Pepe',
    technicalSupport: 'Msaada wa Kiufundi'
  },
  
  common: {
    save: 'Hifadhi',
    cancel: 'Ghairi',
    ok: 'Sawa',
    yes: 'Ndiyo',
    no: 'Hapana'
  }
};

console.log('✅ CORRECT APPROACH DEMONSTRATED');
console.log('\n🎯 Key Principles:');
console.log('1. Use proper phrases as translation values, not camelCase');
console.log('2. Group related translations under logical namespaces');
console.log('3. Ensure translations are complete sentences when appropriate');
console.log('4. Test translations in context to ensure proper formatting');
console.log('\n📝 Next Steps:');
console.log('1. Update translation files with proper values');
console.log('2. Replace hardcoded strings with t() calls');
console.log('3. Test each screen in multiple languages');
console.log('4. Verify formatting and user experience');

module.exports = {
  hardcodedStrings,
  englishTranslations,
  swahiliTranslations
};
