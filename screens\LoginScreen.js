import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TextInput,
  BackHandler,
} from 'react-native';
// Removed PhoneInput to avoid native module issues in Expo Go
// Removed OTP input package to avoid native module issues
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';
import securityManagementService from '../services/securityManagementService';
import CountrySelector from '../components/CountrySelector';
import OTPInput from '../components/OTPInput';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { getWelcomeGreeting } from '../utils/greetingUtils';
import LanguageSelector from '../components/LanguageSelector';

const LoginScreen = ({ navigation }) => {
  // Use theme and language contexts
  const { theme } = useTheme();
  const { t, getTimeBasedGreeting, initializeLanguage } = useLanguage();
  const styles = createStyles(theme);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState(['', '', '', '', '', '']); // Array for individual digits
  const [password, setPassword] = useState('');
  const [step, setStep] = useState('phone'); // 'phone' or 'otp'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [loginMethod, setLoginMethod] = useState('otp'); // 'otp' or 'password'
  const [showPassword, setShowPassword] = useState(false);
  const [greeting, setGreeting] = useState('');
  const [userName, setUserName] = useState('');
  const [selectedCountryCode, setSelectedCountryCode] = useState('UG'); // Default to Uganda

  // Ref for OTP input component
  const otpInputRef = useRef(null);
  const isAutoVerifying = useRef(false);

  useEffect(() => {
    checkBiometricAvailability();
    updateGreeting();

    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  useEffect(() => {
    // Update greeting every minute
    const greetingInterval = setInterval(() => {
      updateGreeting();
    }, 60000); // Update every minute

    // Initial greeting update
    updateGreeting();

    return () => clearInterval(greetingInterval);
  }, []);

  // Update greeting when authentication state changes
  useEffect(() => {
    const authStateListener = (user) => {
      console.log('🔐 Login: Auth state changed, updating greeting for user:', !!user);
      updateGreeting();
    };

    authService.addAuthStateListener(authStateListener);

    return () => {
      authService.removeAuthStateListener(authStateListener);
    };
  }, []);

  // Handle Android hardware back button
  useEffect(() => {
    const backAction = () => {
      if (step === 'otp') {
        handleBackToPhone();
        return true; // Prevent default back action
      }
      return false; // Allow default back action
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [step]);

  /**
   * Updates the greeting message with user context using centralized greeting utility
   */
  const updateGreeting = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      let userProfile = null;

      console.log('🎯 Login: Updating greeting with user data:', {
        hasUser: !!currentUser,
        userId: currentUser?.id,
        userMetadata: currentUser?.user_metadata,
        userEmail: currentUser?.email
      });

      // If user is logged in, try to get their profile for personalized greeting
      if (currentUser) {
        try {
          const profile = await authService.getUserProfile(currentUser.id);
          if (profile.success && profile.data?.full_name) {
            userProfile = profile.data;
            setUserName(profile.data.full_name);
            console.log('✅ Login: Loaded user profile:', profile.data.full_name);
          } else {
            console.log('⚠️ Login: No profile data found, using user metadata');
          }
        } catch (error) {
          console.log('⚠️ Could not load user profile for login screen:', error);
        }
      } else {
        // For returning users: Check cached profile data even when not authenticated
        try {
          const offlineStorageService = await import('../services/offlineStorageService');
          const service = offlineStorageService.default;
          const cachedProfile = await service.getUserProfile();

          if (cachedProfile && cachedProfile.full_name) {
            userProfile = cachedProfile;
            setUserName(cachedProfile.full_name);
            console.log('✅ Login: Loaded cached profile for returning user:', cachedProfile.full_name);
          } else {
            console.log('💾 Login: No cached profile found, showing generic greeting');
          }
        } catch (error) {
          console.log('⚠️ Login: Could not load cached profile:', error);
        }
      }

      // Use centralized greeting utility with three-period system
      const welcomeGreeting = await getWelcomeGreeting(currentUser, userProfile, false, t);
      setGreeting(welcomeGreeting);

      console.log('✅ Login greeting updated:', welcomeGreeting);
      console.log('🎨 Login greeting state set to:', welcomeGreeting);

    } catch (error) {
      console.error('❌ Error updating login greeting:', error);
      // Fallback to basic greeting
      setGreeting(getTimeBasedGreeting());
    }
  };

  const checkBiometricAvailability = async () => {
    try {
      // Check if hardware is available
      const biometric = await authService.isBiometricAvailable();

      // Check if user has enabled biometric authentication
      const biometricEnabled = await securityManagementService.isBiometricEnabled();

      // Only show biometric option if both hardware is available AND user has enabled it
      const shouldShowBiometric = biometric.available && biometricEnabled;

      console.log('🔍 Biometric availability check:', {
        hardware: biometric.available,
        userEnabled: biometricEnabled,
        showOption: shouldShowBiometric
      });

      setBiometricAvailable(shouldShowBiometric);
    } catch (error) {
      console.error('❌ Error checking biometric availability:', error);
      setBiometricAvailable(false);
    }
  };

  /**
   * Handles phone number input changes with country-specific formatting and network detection
   * @param {string} text - The input text
   */
  const handlePhoneNumberChange = (text) => {
    const countryConfig = authService.getCountryConfig(selectedCountryCode);
    if (!countryConfig) return;

    // Remove any non-digit characters and limit to country-specific length
    const cleaned = text.replace(/\D/g, '').substring(0, countryConfig.phoneLength);
    setPhoneNumber(cleaned);

    // Format with selected country code
    const formatted = authService.formatPhoneNumber(cleaned, selectedCountryCode);
    setFormattedValue(formatted);

    // Detect network provider for the selected country
    if (cleaned.length >= countryConfig.phoneLength - 1) {
      const provider = authService.detectNetworkProvider(cleaned, selectedCountryCode);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  /**
   * Handles country selection change
   * @param {string} countryCode - Selected country code
   * @param {Object} country - Country configuration object
   */
  const handleCountrySelect = (countryCode, country) => {
    setSelectedCountryCode(countryCode);
    // Clear phone number and provider when country changes
    setPhoneNumber('');
    setFormattedValue('');
    setNetworkProvider('');
    // Temporarily commented out for debugging
    // initializeLanguage(countryCode);
  };

  /**
   * Handles language selection change
   * @param {string} languageCode - Selected language code
   * @param {Object} language - Language configuration object
   */
  const handleLanguageChange = (languageCode, language) => {
    // Update greeting with new language
    updateGreeting();
  };

  /**
   * Handle OTP input change from the OTP component
   * @param {Array} otpArray - Array of OTP digits
   */
  const handleOtpChange = (otpArray) => {
    setOtpCode(otpArray);
  };

  /**
   * Handle OTP completion (when all 6 digits are entered)
   * @param {string} completeOtp - Complete OTP string
   */
  const handleOtpComplete = async (completeOtp) => {
    if (isAutoVerifying.current || loading) return;

    isAutoVerifying.current = true;
    console.log('🔄 Auto-verifying OTP:', completeOtp);

    try {
      await verifyOTPAndProceed(completeOtp);
    } finally {
      isAutoVerifying.current = false;
    }
  };

  /**
   * Clear OTP inputs (used on error)
   */
  const clearOtpInputs = () => {
    setOtpCode(['', '', '', '', '', '']);
    if (otpInputRef.current?.clearInputs) {
      otpInputRef.current.clearInputs();
    }
  };

  const sendOTP = async () => {
    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!validation.isValid) {
      Alert.alert(t('common.error'), validation.error);
      return;
    }

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);

      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown

        // Show OTP sent message
        Alert.alert(t('otpSent'), t('pleaseCheckYourPhoneForTheVerificationCode'));
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.failedToSendOTP'));
      }
    } catch (error) {
      console.error('❌ Send OTP error:', error);
      Alert.alert(t('error'), t('somethingWentWrongPleaseTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  const loginWithPassword = async () => {
    console.log('🔑 === LOGIN SCREEN DEBUG START ===');
    console.log('🔑 Phone number:', phoneNumber);
    console.log('🔑 Password length:', password?.length);
    console.log('🔑 Country code:', selectedCountryCode);

    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    console.log('🔑 Phone validation result:', validation);

    if (!validation.isValid) {
      console.log('❌ Phone validation failed:', validation.error);
      Alert.alert(t('common.error'), validation.error);
      return;
    }

    if (!password || password.length < 6) {
      console.log('❌ Password validation failed');
      Alert.alert(t('error'), t('pleaseEnterAPasswordWithAtLeast6Characters'));
      return;
    }

    setLoading(true);
    console.log('🔑 Starting login process...');

    try {
      console.log('🔑 Calling authService.loginWithPassword...');
      const result = await authService.loginWithPassword(phoneNumber, password, selectedCountryCode);

      console.log('🔑 Login result received:', {
        success: result.success,
        hasData: !!result.data,
        hasError: !!result.error,
        errorType: typeof result.error,
        requiresPasswordSetup: result.requiresPasswordSetup
      });

      if (result.success) {
        console.log('✅ Password login successful, auth state will handle navigation');

        // No popup needed - auth state change will handle navigation automatically
        // User will see immediate transition to dashboard

        // Don't navigate manually - let the auth state change handle it
      } else {
        console.error('❌ Password login failed:',
          typeof result.error === 'object' ? JSON.stringify(result.error) : result.error);

        // PRODUCTION FIX: Handle password not set scenario
        if (result.error === 'PASSWORD_NOT_SET' || result.requiresPasswordSetup) {
          console.log('🔧 Handling password setup scenario');
          Alert.alert(t('common.passwordNotSet'), t('common.yourAccountWasCreatedWithPhoneVerificationOnly'),
            [
              {
                text: t('auth.useOTPLogin'),
                onPress: () => {
                  console.log('User chose OTP login');
                  // Switch to OTP mode
                  setLoginMethod('otp');
                  Alert.alert(t('common.info'), t('common.switchedToOtpLoginTap'));
                }
              },
              {
                text: t('common.setUpPassword'),
                onPress: () => {
                  console.log('User chose password setup');
                  Alert.alert(t('common.setUpPassword'), t('common.toSetUpAPasswordWeNeedToVerifyYourIdentityFirst'),
                    [
                      {
                        text: t('common.cancel'),
                        style: 'cancel'
                      },
                      {
                        text: t('common.continue'),
                        onPress: async () => {
                          try {
                            const setupResult = await authService.setPasswordForUser(phoneNumber, password, selectedCountryCode);
                            if (setupResult.success && setupResult.requiresOTPVerification) {
                              Alert.alert(t('common.otpSent'), t('common.pleaseVerifyTheOtpSentToYourPhone')
                              );
                              setLoginMethod('otp');
                            }
                          } catch (setupError) {
                            console.error('❌ Password setup error:', setupError);
                            Alert.alert(t('common.error'), t('auth.failedToInitiatePasswordSetup'));
                          }
                        }
                      }
                    ]
                  );
                }
              }
            ]
          );
        } else {
          console.log('🔧 Showing generic error to user');
          const errorMessage = typeof result.error === 'object'
            ? 'Login failed - please check your credentials'
            : (result.error || t('auth.invalidCredentials'));

          Alert.alert(t('auth.loginFailed'), errorMessage, [
            { text: t('auth.tryAgain'), onPress: () => console.log('User will try again') },
            {
              text: t('auth.useOTPInstead'),
              onPress: () => {
                console.log('User switching to OTP login');
                setLoginMethod('otp');
                Alert.alert(t('common.info'), t('common.switchedToOtpLoginTap'));
              }
            }
          ]);
        }
      }
    } catch (error) {
      console.error('❌ Password login exception:', error);
      console.error('❌ Error stack:', error.stack);
      Alert.alert(t('error'), t('somethingWentWrongErrormessagePleaseTryAgain'));
    } finally {
      console.log('🔑 Login process completed, clearing loading state');
      setLoading(false);
    }
  };

  const verifyOTPAndProceed = async (providedOtp = null) => {
    const otpToVerify = providedOtp || otpCode.join('');

    if (!otpToVerify || otpToVerify.length !== 6) {
      Alert.alert(t('common.error'), t('auth.pleaseEnterCompleteOTP'));
      return;
    }

    setLoading(true);
    try {
      console.log('🔐 Verifying OTP:', otpToVerify, 'for phone:', phoneNumber);

      const result = await authService.verifyOTP(phoneNumber, otpToVerify, selectedCountryCode);

      if (result.success) {
        console.log('✅ OTP verified successfully, user:', result.user);

        // Check if user has profile, if not redirect to complete profile
        const profile = await authService.getUserProfile(result.data.user.id);

        if (profile.success) {
          console.log('✅ User profile found');
        } else {
          console.log('⚠️ User profile not found, but user is authenticated');
        }

        // Don't navigate manually - let the auth state change handle it
        console.log('✅ OTP verified successfully, auth state will handle navigation');

      } else {
        console.error('❌ OTP verification failed:', result.error);

        // Provide more specific error messages
        let errorMessage = result.error || t('auth.otpInvalid');
        if (errorMessage.includes('expired')) {
          errorMessage = 'OTP has expired. Please request a new code.';
        } else if (errorMessage.includes('invalid')) {
          errorMessage = 'Invalid OTP. Please check and try again.';
        }

        Alert.alert(
          t('common.error'),
          errorMessage,
          [
            {
              text: 'Try Again',
              onPress: () => clearOtpInputs(),
              style: 'default'
            },
            {
              text: 'Resend OTP',
              onPress: () => {
                clearOtpInputs();
                resendOTP();
              },
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      console.error('❌ OTP verification error:', error);
      Alert.alert(
        t('common.error'),
        'Network error. Please check your connection and try again.',
        [
          {
            text: 'Retry',
            onPress: () => clearOtpInputs(),
            style: 'default'
          },
          {
            text: 'Go Back',
            onPress: () => setStep('phone'),
            style: 'cancel'
          }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  // Legacy function for manual verification button
  const verifyOTP = () => verifyOTPAndProceed();

  const handleBiometricLogin = async () => {
    setLoading(true);
    try {
      const result = await authService.authenticateWithBiometric();
      
      if (result.success) {
        console.log('✅ Biometric login successful, auth state will handle navigation');
        // Don't navigate manually - let the auth state change handle it
      } else {
        Alert.alert(t('common.authenticationFailed'), t('common.pleaseTryAgainOrUsePhoneNumberLogin'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.biometricAuthenticationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (resendTimer > 0) return;

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);

      if (result.success) {
        setResendTimer(60);

        // Show OTP resent message
        Alert.alert(t('common.otpResent'), t('common.aNewVerificationCodeHasBeenSentToYourPhone'));
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.failedToResendOTP'));
      }
    } catch (error) {
      console.error('❌ Resend OTP error:', error);
      Alert.alert(t('error'), t('somethingWentWrongPleaseTryAgain'));
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      {/* Language Selector - Temporarily commented out for debugging */}
      {/* <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <LanguageSelector
          selectedCountryCode={selectedCountryCode}
          onLanguageChange={handleLanguageChange}
          compact={true}
        />
      </View> */}

      <Text style={styles.title} numberOfLines={2} adjustsFontSizeToFit={true}>{greeting}</Text>
      <Text style={styles.subtitle}>{t('auth.chooseLoginMethod')}</Text>

      {/* Login Method Toggle */}
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          style={[styles.toggleButton, loginMethod === 'otp' && styles.toggleButtonActive]}
          onPress={() => setLoginMethod('otp')}
        >
          <Text style={[styles.toggleText, loginMethod === 'otp' && styles.toggleTextActive]}>
            {t('auth.otpLogin')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.toggleButton, loginMethod === 'password' && styles.toggleButtonActive]}
          onPress={() => setLoginMethod('password')}
        >
          <Text style={[styles.toggleText, loginMethod === 'password' && styles.toggleTextActive]}>
            {t('auth.passwordLogin')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Country Selector */}
      <CountrySelector
        selectedCountryCode={selectedCountryCode}
        onCountrySelect={handleCountrySelect}
      />

      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>
            {authService.getCountryConfig(selectedCountryCode)?.code || '+256'}
          </Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder={`${t('auth.enterPhoneNumber')} (e.g., ${authService.getCountryConfig(selectedCountryCode)?.example || '777123456'})`}
            keyboardType="phone-pad"
            maxLength={authService.getCountryConfig(selectedCountryCode)?.phoneLength || 10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              {t('auth.network')}: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      {/* Password Input for Password Login */}
      {loginMethod === 'password' && (
        <View style={styles.passwordContainer}>
          <View style={styles.passwordInputContainer}>
            <TextInput
              style={styles.passwordInput}
              value={password}
              onChangeText={setPassword}
              placeholder={t('auth.enterPassword')}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Ionicons
                name={showPassword ? "eye-off-outline" : "eye-outline"}
                size={20}
                color="#666"
              />
            </TouchableOpacity>
          </View>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>{t('auth.forgotPassword')}</Text>
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={loginMethod === 'otp' ? sendOTP : loginWithPassword}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>
            {loginMethod === 'otp' ? t('auth.sendOTP') : t('auth.login')}
          </Text>
        )}
      </TouchableOpacity>

      {biometricAvailable && (
        <TouchableOpacity
          style={styles.biometricButton}
          onPress={handleBiometricLogin}
        >
          <Ionicons name="finger-print" size={24} color={Colors.primary.main} />
          <Text style={styles.biometricText}>{t('auth.useBiometric')}</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={styles.registerLink}
        onPress={() => navigation.navigate('Register')}
      >
        <Text style={styles.registerText}>
          {t('auth.dontHaveAccount')} <Text style={styles.registerTextBold}>{t('auth.signUp')}</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const handleBackToPhone = () => {
    // Always allow going back to phone step
    setStep('phone');
    setLoading(false); // Ensure loading state is cleared
    clearOtpInputs(); // Clear any entered OTP
    setResendTimer(0); // Reset resend timer
  };

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={handleBackToPhone}
        disabled={false} // Never disable the back button
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>{t('auth.verifyOTP')}</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || phoneNumber}
      </Text>

      <View style={styles.otpContainer}>
        <OTPInput
          ref={otpInputRef}
          length={6}
          value={otpCode}
          onChangeText={handleOtpChange}
          onComplete={handleOtpComplete}
          disabled={loading}
          autoFocus={true}
        />
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>{t('auth.verifyOTP')} & {t('auth.login')}</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `${t('auth.resendOTPIn')} ${resendTimer}s` : t('auth.resendOTP')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'phone' ? renderPhoneStep() : renderOTPStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: -80,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  headerSpacer: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 10,
    flexWrap: 'wrap',
    flexShrink: 1,
    lineHeight: 34,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 4,
    marginBottom: 30,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: Colors.primary.main,
    shadowColor: Colors.primary.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  toggleTextActive: {
    color: Colors.neutral.white,
  },
  phoneInputContainer: {
    marginBottom: 20,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  countryCode: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '600',
    marginRight: 10,
  },
  phoneTextInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    height: '100%',
  },
  passwordContainer: {
    marginBottom: 20,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    height: 60,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    height: '100%',
  },
  eyeButton: {
    padding: 4,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginTop: 10,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  providerName: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  button: {
    backgroundColor: Colors.primary.main,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.primary.dark,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.primary.main,
    backgroundColor: theme.colors.surface,
    marginBottom: 20,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  biometricText: {
    color: Colors.primary.main,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  registerLink: {
    alignItems: 'center',
  },
  registerText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  registerTextBold: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  otpContainer: {
    width: '100%',
    marginBottom: 30,
    paddingHorizontal: 10,
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: theme.colors.textSecondary,
  },
});

export default LoginScreen;

