#!/usr/bin/env node

/**
 * Syntax Validation Script for JiraniPay
 * 
 * This script validates JavaScript syntax in key files to ensure
 * there are no syntax errors before running the app.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 JiraniPay Syntax Validation');
console.log('==============================\n');

// Files to validate
const filesToValidate = [
  'services/authService.js',
  'services/enhancedNetworkService.js',
  'services/databaseService.js',
  'services/preferenceService.js',
  'services/profileManagementService.js',
  'App.js'
];

/**
 * Validate JavaScript syntax
 */
function validateJavaScriptSyntax(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ File not found: ${filePath}`);
      return false;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Basic syntax validation using Node.js
    try {
      // Create a function wrapper to validate syntax without executing
      new Function(content);
      console.log(`✅ ${filePath} - Syntax valid`);
      return true;
    } catch (syntaxError) {
      console.log(`❌ ${filePath} - Syntax error:`);
      console.log(`   ${syntaxError.message}`);
      
      // Try to extract line number from error
      const lineMatch = syntaxError.message.match(/line (\d+)/);
      if (lineMatch) {
        const lineNumber = parseInt(lineMatch[1]);
        const lines = content.split('\n');
        const startLine = Math.max(0, lineNumber - 3);
        const endLine = Math.min(lines.length, lineNumber + 2);
        
        console.log(`   Context around line ${lineNumber}:`);
        for (let i = startLine; i < endLine; i++) {
          const marker = i === lineNumber - 1 ? '>>>' : '   ';
          console.log(`   ${marker} ${i + 1}: ${lines[i]}`);
        }
      }
      
      return false;
    }
  } catch (error) {
    console.log(`❌ ${filePath} - Error reading file: ${error.message}`);
    return false;
  }
}

/**
 * Check for common syntax issues
 */
function checkCommonIssues(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    const content = fs.readFileSync(fullPath, 'utf8');
    
    const issues = [];
    
    // Check for unmatched braces
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;
    if (openBraces !== closeBraces) {
      issues.push(`Unmatched braces: ${openBraces} open, ${closeBraces} close`);
    }
    
    // Check for unmatched parentheses
    const openParens = (content.match(/\(/g) || []).length;
    const closeParens = (content.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      issues.push(`Unmatched parentheses: ${openParens} open, ${closeParens} close`);
    }
    
    // Check for try blocks without catch/finally
    const tryBlocks = content.match(/\btry\s*{/g) || [];
    const catchBlocks = content.match(/}\s*catch\s*\(/g) || [];
    const finallyBlocks = content.match(/}\s*finally\s*{/g) || [];
    
    if (tryBlocks.length > (catchBlocks.length + finallyBlocks.length)) {
      issues.push(`Try blocks without catch/finally: ${tryBlocks.length} try, ${catchBlocks.length} catch, ${finallyBlocks.length} finally`);
    }
    
    if (issues.length > 0) {
      console.log(`⚠️ ${filePath} - Potential issues:`);
      issues.forEach(issue => console.log(`   - ${issue}`));
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`❌ ${filePath} - Error checking issues: ${error.message}`);
    return false;
  }
}

/**
 * Main validation
 */
async function main() {
  console.log('🔍 Validating JavaScript syntax...\n');
  
  let allValid = true;
  
  for (const filePath of filesToValidate) {
    console.log(`📄 Validating ${filePath}:`);
    
    const syntaxValid = validateJavaScriptSyntax(filePath);
    const issuesValid = checkCommonIssues(filePath);
    
    const fileValid = syntaxValid && issuesValid;
    allValid = allValid && fileValid;
    
    console.log(`   ${fileValid ? '✅ VALID' : '❌ INVALID'}\n`);
  }
  
  // Summary
  console.log('📊 VALIDATION SUMMARY');
  console.log('=====================');
  console.log(`Overall result: ${allValid ? '✅ ALL FILES VALID' : '❌ SOME FILES INVALID'}`);
  
  if (allValid) {
    console.log('\n🎉 All files passed syntax validation!');
    console.log('You can now start the app safely.');
    console.log('\nNext steps:');
    console.log('1. Clear Metro cache: npm start -- --clear');
    console.log('2. Or restart the app: npm run start-dev');
  } else {
    console.log('\n⚠️ Some files have syntax errors.');
    console.log('Please fix the errors above before starting the app.');
  }
  
  process.exit(allValid ? 0 : 1);
}

// Run validation
main().catch(error => {
  console.error('❌ Validation script failed:', error);
  process.exit(1);
});
