#!/usr/bin/env node

/**
 * Comprehensive Greeting Issues Fix Script
 * 
 * This script fixes all greeting-related issues by:
 * 1. Cleaning up security settings duplicates
 * 2. Updating user metadata from profile data
 * 3. Fixing auto-generated names
 * 4. Testing greeting generation
 */

const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env.development.local') });

console.log('🔧 Comprehensive Greeting Issues Fix');
console.log('===================================\n');

/**
 * Main fix function
 */
async function main() {
  try {
    console.log('🚀 Starting comprehensive greeting issues fix...\n');

    // Import Supabase client
    const { createClient } = await import('@supabase/supabase-js');
    
    // Initialize Supabase client
    const supabase = createClient(
      process.env.EXPO_PUBLIC_SUPABASE_URL,
      process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
    );
    
    console.log('✅ Supabase client initialized\n');

    // STEP 1: Fix security settings duplicates
    console.log('🔧 STEP 1: Fixing security settings duplicates...');
    await fixSecuritySettingsDuplicates(supabase);
    console.log('');

    // STEP 2: Update user metadata from profiles
    console.log('🔧 STEP 2: Updating user metadata from profiles...');
    await updateUserMetadataFromProfiles(supabase);
    console.log('');

    // STEP 3: Test current user greeting
    console.log('🔧 STEP 3: Testing current user greeting...');
    await testCurrentUserGreeting(supabase);
    console.log('');

    console.log('🎉 Comprehensive fix completed!');
    console.log('\n🔄 Please restart the app to see the changes:');
    console.log('   npm run start-dev');

  } catch (error) {
    console.error('❌ Fix script failed:', error);
    process.exit(1);
  }
}

/**
 * Fix security settings duplicates
 */
async function fixSecuritySettingsDuplicates(supabase) {
  try {
    // Check for duplicates
    const { data: duplicates, error } = await supabase
      .from('security_settings')
      .select('user_id, id, created_at')
      .order('user_id, created_at');
      
    if (error) {
      console.error('❌ Error fetching security settings:', error);
      return;
    }
      
    console.log(`📊 Found ${duplicates?.length || 0} security settings records`);
    
    if (duplicates && duplicates.length > 0) {
      const grouped = {};
      duplicates.forEach(record => {
        if (!grouped[record.user_id]) {
          grouped[record.user_id] = [];
        }
        grouped[record.user_id].push(record);
      });
      
      let duplicatesFound = 0;
      let duplicatesFixed = 0;
      
      for (const [userId, records] of Object.entries(grouped)) {
        if (records.length > 1) {
          duplicatesFound++;
          console.log(`🔍 Duplicate found for user: ${userId} (${records.length} records)`);
          
          // Keep the newest, delete the rest
          const toDelete = records.slice(0, -1);
          for (const record of toDelete) {
            const { error: deleteError } = await supabase
              .from('security_settings')
              .delete()
              .eq('id', record.id);
              
            if (deleteError) {
              console.error('❌ Error deleting duplicate:', deleteError);
            } else {
              console.log(`   ✅ Deleted duplicate: ${record.id}`);
              duplicatesFixed++;
            }
          }
        }
      }
      
      console.log(`📊 Security settings: ${duplicatesFound} duplicates found, ${duplicatesFixed} fixed`);
    } else {
      console.log('✅ No security settings duplicates found');
    }
  } catch (error) {
    console.error('❌ Error fixing security settings:', error);
  }
}

/**
 * Update user metadata from profiles
 */
async function updateUserMetadataFromProfiles(supabase) {
  try {
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name, phone, email, country_code');

    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError);
      return;
    }

    console.log(`📊 Found ${profiles.length} profiles to check`);

    let updated = 0;
    let unchanged = 0;
    let errors = 0;

    for (const profile of profiles) {
      try {
        // Get user from auth
        const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(profile.id);
        
        if (userError || !user) {
          console.log(`⚠️ Could not get auth user for profile: ${profile.id}`);
          errors++;
          continue;
        }

        // Check if metadata needs updating
        const needsUpdate = !user.user_metadata?.full_name || 
                           user.user_metadata.full_name !== profile.full_name ||
                           user.user_metadata.full_name.match(/^User \d+$/);

        if (!needsUpdate) {
          unchanged++;
          continue;
        }

        console.log(`🔄 Updating metadata for user: ${profile.id}`);
        console.log(`   Profile name: "${profile.full_name}"`);
        console.log(`   Current metadata name: "${user.user_metadata?.full_name || 'NOT SET'}"`);

        // Update user metadata
        const { error: updateError } = await supabase.auth.admin.updateUserById(profile.id, {
          user_metadata: {
            ...user.user_metadata,
            full_name: profile.full_name,
            name: profile.full_name,
            phone: profile.phone,
            email: profile.email,
            country_code: profile.country_code || 'UG',
            last_metadata_update: new Date().toISOString()
          }
        });

        if (updateError) {
          console.error(`❌ Error updating metadata for ${profile.id}:`, updateError);
          errors++;
        } else {
          console.log(`   ✅ Metadata updated: "${profile.full_name}"`);
          updated++;
        }

      } catch (error) {
        console.error(`❌ Error processing profile ${profile.id}:`, error);
        errors++;
      }
    }

    console.log(`📊 Metadata updates: ${updated} updated, ${unchanged} unchanged, ${errors} errors`);

  } catch (error) {
    console.error('❌ Error updating user metadata:', error);
  }
}

/**
 * Test current user greeting
 */
async function testCurrentUserGreeting(supabase) {
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('⚠️ No authenticated user found for greeting test');
      return;
    }

    console.log('🎯 Testing greeting for current user:', user.id);

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.log('⚠️ No profile found for current user');
    }

    // Test greeting generation
    const extractDisplayName = (fullName) => {
      if (!fullName || typeof fullName !== 'string') {
        return null;
      }

      const trimmedName = fullName.trim();
      
      if (trimmedName.match(/^User \d+$/)) {
        return trimmedName;
      }

      if (trimmedName.match(/^JiraniPay User$/)) {
        return 'Friend';
      }

      const nameParts = trimmedName.split(' ');
      if (nameParts.length > 1) {
        const firstName = nameParts[0];
        if (firstName.length > 1 && /^[A-Za-z]/.test(firstName) && firstName !== 'User') {
          return firstName;
        }
      }

      if (trimmedName.length > 1 && /^[A-Za-z]/.test(trimmedName) && trimmedName !== 'User') {
        return trimmedName;
      }

      return trimmedName;
    };

    const getCurrentTimePeriod = () => {
      const hour = new Date().getHours();
      if (hour < 12) return { period: 'morning' };
      if (hour < 18) return { period: 'afternoon' };
      return { period: 'evening' };
    };

    // Test name extraction
    let userName = null;
    const nameSources = [
      { source: 'profile.full_name', value: profile?.full_name },
      { source: 'user_metadata.full_name', value: user.user_metadata?.full_name },
      { source: 'user_metadata.name', value: user.user_metadata?.name }
    ];

    console.log('📋 Available name sources:');
    nameSources.forEach(({ source, value }) => {
      console.log(`   ${source}: "${value || 'NOT SET'}"`);
      if (value && !userName) {
        userName = extractDisplayName(value);
        console.log(`   ✅ Using name from ${source}: "${userName}"`);
      }
    });

    // Generate greeting
    const timeOfDay = getCurrentTimePeriod();
    const greeting = userName ? 
      `Good ${timeOfDay.period}, ${userName}!` : 
      `Good ${timeOfDay.period}!`;

    console.log('');
    console.log('🎯 GREETING TEST RESULTS:');
    console.log(`   Final greeting: "${greeting}"`);
    console.log(`   Name used: "${userName || 'NONE'}"`);
    console.log(`   Time period: ${timeOfDay.period}`);

    // Check if greeting is using auto-generated name
    if (userName && userName.match(/^User \d+$/)) {
      console.log('⚠️ WARNING: Still using auto-generated name');
      console.log('   This suggests the profile or metadata still contains auto-generated data');
    } else if (userName && !userName.match(/^(Friend|User)$/)) {
      console.log('✅ SUCCESS: Using real name in greeting');
    } else {
      console.log('⚠️ INFO: Using fallback greeting (no real name available)');
    }

  } catch (error) {
    console.error('❌ Error testing greeting:', error);
  }
}

// Run the fix
main().catch(error => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
