# 🚀 JiraniPay Supabase Setup Guide

This guide provides step-by-step instructions to set up a complete, production-ready Supabase backend for JiraniPay.

## 📋 Prerequisites

- Supabase account (free tier available)
- SMS provider account (Twilio, MessageBird, or Africa's Talking)
- Basic understanding of SQL and database concepts

## 🏗️ Step 1: Create Supabase Project

1. **Go to [supabase.com](https://supabase.com)**
2. **Sign up/Login** with your account
3. **Create New Project**:
   - Project Name: `JiraniPay`
   - Database Password: Choose a strong password (save it securely)
   - Region: `ap-southeast-1` (Singapore - closest to East Africa)

## 🔑 Step 2: Get API Credentials

1. **Navigate to Settings → API** in your Supabase dashboard
2. **Copy these values**:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **anon public key**: Starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

3. **Update Configuration**:
   - Open `config/environment.js`
   - Replace `YOUR_SUPABASE_PROJECT_URL` with your Project URL
   - Replace `YOUR_SUPABASE_ANON_KEY` with your anon public key

## 📱 Step 3: Configure Phone Authentication

### Enable Phone Auth
1. **Go to Authentication → Settings**
2. **Enable Phone authentication**
3. **Configure the following settings**:
   - Enable phone confirmations: ✅
   - Enable phone change confirmations: ✅

### Configure SMS Provider

#### Option A: Twilio (Recommended)
1. **Sign up at [twilio.com](https://twilio.com)**
2. **Get credentials**:
   - Account SID
   - Auth Token
   - Phone Number (with SMS capabilities)
3. **In Supabase**:
   - Go to Authentication → Settings → SMS
   - Select Twilio
   - Enter your credentials

#### Option B: Africa's Talking (Best for East Africa)
1. **Sign up at [africastalking.com](https://africastalking.com)**
2. **Get API credentials**
3. **Configure in Supabase** (custom provider)

## 🗄️ Step 4: Database Schema Setup

Run these SQL commands in your Supabase SQL Editor:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user profiles table
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    full_name TEXT NOT NULL,
    phone_number TEXT UNIQUE NOT NULL,
    country_code TEXT NOT NULL DEFAULT 'UG',
    preferred_language TEXT DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user preferences table
CREATE TABLE public.user_preferences (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    preferred_currency TEXT DEFAULT 'UGX',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment accounts table (for future use)
CREATE TABLE public.payment_accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    account_type TEXT NOT NULL, -- 'mobile_money', 'bank', 'wallet'
    provider_name TEXT NOT NULL, -- 'MTN', 'Airtel', 'Bank of Uganda', etc.
    account_number TEXT NOT NULL,
    account_name TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table (for future use)
CREATE TABLE public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    transaction_type TEXT NOT NULL, -- 'bill_payment', 'airtime', 'transfer'
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'UGX',
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed'
    reference_number TEXT UNIQUE,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_phone ON public.user_profiles(phone_number);
CREATE INDEX idx_user_profiles_country ON public.user_profiles(country_code);
CREATE INDEX idx_payment_accounts_user ON public.payment_accounts(user_id);
CREATE INDEX idx_transactions_user ON public.transactions(user_id);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_transactions_created ON public.transactions(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Similar policies for other tables
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = id);

CREATE POLICY "Users can manage own payment accounts" ON public.payment_accounts
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);
```

## 🔧 Step 5: Configure Environment Variables

Update your `config/environment.js` file:

```javascript
// Development Environment
const developmentConfig = {
  supabase: {
    url: 'https://your-project-ref.supabase.co',
    anonKey: 'your-actual-anon-key-here',
  },
  // ... rest of config
};

// Production Environment  
const productionConfig = {
  supabase: {
    url: 'https://your-project-ref.supabase.co',
    anonKey: 'your-actual-anon-key-here',
  },
  // ... rest of config
};
```

## ✅ Step 6: Test the Setup

1. **Start your app**: `npx expo start`
2. **Try registering** with a real phone number
3. **Check Supabase logs** in Authentication → Logs
4. **Verify SMS delivery**

## 🚨 Troubleshooting

### Common Issues:

1. **"Invalid API key" error**:
   - Verify your Supabase URL and anon key are correct
   - Check that you've updated `config/environment.js`

2. **SMS not received**:
   - Verify SMS provider configuration
   - Check SMS provider logs
   - Ensure phone number format is correct

3. **Database connection issues**:
   - Verify your database password
   - Check Supabase project status

### Support Resources:
- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord Community](https://discord.supabase.com)
- [JiraniPay GitHub Issues](https://github.com/your-repo/issues)

## 🎯 Next Steps

Once setup is complete:
1. Test user registration and login flows
2. Implement user profile management
3. Add payment account linking
4. Build transaction history
5. Implement bill payment features

## 🔒 Security Considerations

- Never commit API keys to version control
- Use environment variables for sensitive data
- Regularly rotate API keys
- Monitor authentication logs
- Implement proper error handling
- Use HTTPS in production

---

**Need Help?** Contact the development team or create an issue in the project repository.
