-- =====================================================
-- COPY AND PASTE THIS SQL INTO YOUR SUPABASE SQL EDITOR
-- =====================================================
-- This SQL fixes the profile update errors by adding missing columns

-- Add missing columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS date_of_birth DATE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS phone_number TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS gender TEXT CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say'));
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS address TEXT;

-- Copy existing phone data to phone_number column for compatibility
UPDATE public.profiles SET phone_number = phone WHERE phone IS NOT NULL AND phone_number IS NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_profiles_date_of_birth ON public.profiles(date_of_birth);
CREATE INDEX IF NOT EXISTS idx_profiles_gender ON public.profiles(gender);
CREATE UNIQUE INDEX IF NOT EXISTS idx_profiles_phone_number_unique ON public.profiles(phone_number) WHERE phone_number IS NOT NULL;

-- Create trigger to keep phone and phone_number in sync
CREATE OR REPLACE FUNCTION sync_phone_columns()
RETURNS TRIGGER AS $$
BEGIN
    -- If phone_number is updated, sync to phone
    IF NEW.phone_number IS DISTINCT FROM OLD.phone_number THEN
        NEW.phone = NEW.phone_number;
    END IF;
    
    -- If phone is updated, sync to phone_number
    IF NEW.phone IS DISTINCT FROM OLD.phone THEN
        NEW.phone_number = NEW.phone;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger
DROP TRIGGER IF EXISTS sync_phone_columns_trigger ON public.profiles;
CREATE TRIGGER sync_phone_columns_trigger
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_phone_columns();

-- Verify the changes
SELECT 'Profile update schema fix completed successfully!' as status;
