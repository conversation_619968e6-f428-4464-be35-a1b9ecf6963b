/**
 * Wallet Service
 *
 * Handles wallet operations, balance management, and transaction history
 */

import supabase from './supabaseClient';
import authService from './authService';

class WalletService {
  constructor() {
    this.tableName = {
      wallets: 'wallets',
      transactions: 'transactions',
      paymentAccounts: 'payment_accounts'
    };
  }

  /**
   * Get wallet balance for current user
   */
  async getWalletBalance(userId = null) {
    try {
      // Get current user if userId not provided
      let userIdToUse = userId;
      if (!userIdToUse) {
        console.log('🔍 No userId provided, getting current user...');
        const currentUser = await authService.getCurrentUser();
        console.log('🔍 Current user from authService:', {
          user: currentUser,
          id: currentUser?.id,
          type: typeof currentUser?.id
        });

        if (!currentUser || !currentUser.id) {
          console.error('❌ No authenticated user found');
          return {
            success: false,
            error: 'User not authenticated'
          };
        }
        userIdToUse = currentUser.id;
      }

      console.log('🔍 Final userIdToUse:', {
        value: userIdToUse,
        type: typeof userIdToUse,
        length: userIdToUse?.length
      });
      console.log('💰 Getting wallet balance for user:', userIdToUse);

      // Validate userIdToUse is a valid UUID
      if (!userIdToUse || typeof userIdToUse !== 'string') {
        console.error('❌ Invalid user ID for wallet:', userIdToUse);
        return {
          success: true,
          data: {
            balance: 0,
            currency: 'UGX',
            status: 'active',
            created_at: new Date().toISOString()
          }
        };
      }

      // Query wallet data from database
      const { data, error } = await supabase
        .from(this.tableName.wallets)
        .select('*')
        .eq('user_id', userIdToUse)
        .single();

      if (error) {
        // Handle various error cases
        if (error.code === 'PGRST116') {
          // No wallet found, return default wallet data
          console.log('⚠️ No wallet found, returning default data');
          return {
            success: true,
            data: {
              balance: 0,
              currency: 'UGX',
              status: 'active',
              created_at: new Date().toISOString()
            }
          };
        } else if (error.code === '42P01' || error.code === '22P02') {
          // Table doesn't exist or schema mismatch
          console.log('⚠️ Wallets table not found or schema mismatch, returning default data');
          return {
            success: true,
            data: {
              balance: 0,
              currency: 'UGX',
              status: 'active',
              created_at: new Date().toISOString()
            }
          };
        }

        console.error('❌ Error getting wallet balance:', error);
        throw error;
      }

      console.log('✅ Wallet balance retrieved successfully');
      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('❌ Error in getWalletBalance:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get recent transactions for current user
   */
  async getRecentTransactions(limit = 10, userId = null) {
    try {
      // Get current user if userId not provided
      let userIdToUse = userId;
      if (!userIdToUse) {
        console.log('🔍 [Transactions] No userId provided, getting current user...');
        const currentUser = await authService.getCurrentUser();
        console.log('🔍 [Transactions] Current user from authService:', {
          user: currentUser,
          id: currentUser?.id,
          type: typeof currentUser?.id
        });

        if (!currentUser || !currentUser.id) {
          console.error('❌ [Transactions] No authenticated user found');
          return {
            success: false,
            error: 'User not authenticated'
          };
        }
        userIdToUse = currentUser.id;
      }

      console.log('🔍 [Transactions] Final userIdToUse:', {
        value: userIdToUse,
        type: typeof userIdToUse,
        length: userIdToUse?.length
      });
      console.log('📊 Getting recent transactions for user:', userIdToUse, 'limit:', limit);

      // Validate userIdToUse is a valid UUID
      if (!userIdToUse || typeof userIdToUse !== 'string' || userIdToUse.length !== 36) {
        console.error('❌ Invalid user ID for transactions:', userIdToUse);
        return {
          success: true,
          data: [],
          message: 'Invalid user ID provided'
        };
      }

      // Query transactions from database
      const { data, error } = await supabase
        .from(this.tableName.transactions)
        .select('*')
        .eq('user_id', userIdToUse)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        // Handle case where transactions table doesn't exist or has different structure
        if (error.code === '42P01' || error.code === '22P02') {
          console.log('⚠️ Transactions table not found or schema mismatch, returning empty data');
          return {
            success: true,
            data: []
          };
        }

        console.error('❌ Error getting transactions:', error);
        throw error;
      }

      console.log('✅ Transactions retrieved successfully:', data?.length || 0, 'transactions');
      return {
        success: true,
        data: data || []
      };

    } catch (error) {
      console.error('❌ Error in getRecentTransactions:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Create wallet for new user
   */
  async createWallet(userId, initialBalance = 0) {
    try {
      console.log('🆕 Creating wallet for user:', userId);

      const { data, error } = await supabase
        .from(this.tableName.wallets)
        .insert([
          {
            user_id: userId,
            balance: initialBalance,
            currency: 'UGX',
            status: 'active',
            created_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating wallet:', error);
        throw error;
      }

      console.log('✅ Wallet created successfully');
      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('❌ Error in createWallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update wallet balance
   */
  async updateBalance(userId, newBalance) {
    try {
      console.log('💰 Updating wallet balance for user:', userId, 'to:', newBalance);

      const { data, error } = await supabase
        .from(this.tableName.wallets)
        .update({
          balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating wallet balance:', error);
        throw error;
      }

      console.log('✅ Wallet balance updated successfully');
      return {
        success: true,
        data: data
      };

    } catch (error) {
      console.error('❌ Error in updateBalance:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get financial insights for dashboard
   */
  async getFinancialInsights(userId = null) {
    try {
      // Get current user if userId not provided
      let userIdToUse = userId;
      if (!userIdToUse) {
        const currentUser = await authService.getCurrentUser();
        if (!currentUser || !currentUser.id) {
          console.error('❌ No authenticated user found for insights');
          return {
            success: false,
            error: 'User not authenticated'
          };
        }
        userIdToUse = currentUser.id;
      }

      console.log('📊 Getting financial insights for user:', userIdToUse);

      // Get wallet balance
      const walletResult = await this.getWalletBalance(userIdToUse);
      const balance = walletResult.success ? walletResult.data.balance : 0;

      // Get recent transactions for spending analysis
      const transactionsResult = await this.getRecentTransactions(30, userIdToUse);
      const transactions = transactionsResult.success ? transactionsResult.data : [];

      // Calculate basic insights
      const totalSpent = transactions
        .filter(t => t.amount < 0)
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      const totalReceived = transactions
        .filter(t => t.amount > 0)
        .reduce((sum, t) => sum + t.amount, 0);

      const insights = {
        currentBalance: balance,
        totalSpent: totalSpent,
        totalReceived: totalReceived,
        netFlow: totalReceived - totalSpent,
        transactionCount: transactions.length,
        averageTransaction: transactions.length > 0 ? totalSpent / transactions.length : 0,
        lastTransactionDate: transactions.length > 0 ? transactions[0].created_at : null
      };

      console.log('✅ Financial insights calculated successfully');
      return {
        success: true,
        data: insights
      };

    } catch (error) {
      console.error('❌ Error getting financial insights:', error);
      return {
        success: false,
        error: error.message,
        data: {
          currentBalance: 0,
          totalSpent: 0,
          totalReceived: 0,
          netFlow: 0,
          transactionCount: 0,
          averageTransaction: 0,
          lastTransactionDate: null
        }
      };
    }
  }
}

export default new WalletService();