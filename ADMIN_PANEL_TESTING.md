# 🏦 JiraniPay Admin Panel Testing Guide

This guide will help you test the JiraniPay Admin Panel functionality for basic user and transaction management.

## 🚀 Quick Start

### 1. Prerequisites

Make sure you have the following running:
- ✅ JiraniPay backend server (`npm run dev` in `/backend`)
- ✅ PostgreSQL database with JiraniPay schema
- ✅ Redis server for caching
- ✅ Environment variables configured

### 2. Create an Admin User

First, create an admin user for testing:

```bash
cd JiraniPay/backend
node scripts/create-admin-user.js
```

Follow the prompts to create an admin user with these suggested values:
- **Email**: `<EMAIL>`
- **Full Name**: `System Administrator`
- **Phone**: `+************`
- **Password**: `AdminPassword123!`
- **Role**: `admin`

### 3. Access the Test Interface

Open your browser and navigate to:
```
http://localhost:3000/admin-test.html
```

## 🧪 Testing Methods

### Method 1: Web Interface (Recommended for Beginners)

1. **Open the Test Interface**
   ```
   http://localhost:3000/admin-test.html
   ```

2. **Login as Admin**
   - Enter your admin credentials
   - Click "🔑 Login as Admin"
   - You should see "✅ Admin login successful!"

3. **Test Dashboard Features**
   - Click "Test" next to `/api/v1/admin/dashboard`
   - View system statistics and overview
   - Test quick actions and notifications

4. **Test User Management**
   - Click "Test" next to `/api/v1/admin/users`
   - View paginated user list
   - Test user statistics endpoint
   - Try user details with sample ID

5. **Test Transaction Management**
   - Click "Test" next to `/api/v1/admin/transactions`
   - View transaction list with filters
   - Test transaction analytics
   - View transaction trends

### Method 2: API Testing with Postman/Insomnia

1. **Import the Collection**
   Create a new collection with these endpoints:

2. **Set Base URL**
   ```
   http://localhost:3000/api/v1/admin
   ```

3. **Add Authorization Header**
   ```
   Authorization: Bearer your-admin-token
   ```

4. **Test Key Endpoints**

#### Dashboard Endpoints
```http
GET /api/v1/admin/dashboard
GET /api/v1/admin/dashboard/quick-actions
GET /api/v1/admin/dashboard/notifications
```

#### User Management Endpoints
```http
GET /api/v1/admin/users?page=1&limit=10
GET /api/v1/admin/users/stats/overview
GET /api/v1/admin/users/{userId}
GET /api/v1/admin/users/{userId}/transactions
PUT /api/v1/admin/users/{userId}/status
```

#### Transaction Management Endpoints
```http
GET /api/v1/admin/transactions?page=1&limit=10
GET /api/v1/admin/transactions/stats/overview
GET /api/v1/admin/transactions/analytics/trends
GET /api/v1/admin/transactions/{transactionId}
PUT /api/v1/admin/transactions/{transactionId}/status
```

#### Monitoring Endpoints
```http
GET /api/v1/admin/monitoring/dashboard
GET /api/v1/admin/monitoring/health
GET /api/v1/admin/monitoring/real-time
```

### Method 3: Command Line with cURL

```bash
# Set your admin token
ADMIN_TOKEN="your-admin-token"
BASE_URL="http://localhost:3000/api/v1/admin"

# Test dashboard
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$BASE_URL/dashboard"

# Test user list
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$BASE_URL/users?page=1&limit=5"

# Test transaction list
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$BASE_URL/transactions?page=1&limit=5"

# Test system health
curl -H "Authorization: Bearer $ADMIN_TOKEN" \
     "$BASE_URL/monitoring/health"
```

## 📊 Expected Test Results

### Dashboard Response
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalUsers": 0,
      "totalTransactions": 0,
      "todayTransactions": 0,
      "activeWallets": 1,
      "totalVolumeToday": 0,
      "totalVolumeMonth": 0,
      "successRate": 100,
      "lastUpdated": "2024-01-15T10:30:00.000Z"
    },
    "recentTransactions": [],
    "systemHealth": {
      "overall": "healthy",
      "services": [
        {"name": "database", "status": "healthy"},
        {"name": "redis", "status": "healthy"}
      ]
    }
  }
}
```

### User List Response
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "userId": "123e4567-e89b-12d3-a456-426614174000",
        "fullName": "System Administrator",
        "email": "<EMAIL>",
        "phoneNumber": "+************",
        "isVerified": true,
        "kycStatus": "verified",
        "walletBalance": 0,
        "walletCurrency": "UGX",
        "walletActive": true,
        "createdAt": "2024-01-15T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "pages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

## 🔧 Troubleshooting

### Common Issues

1. **"Admin authentication required" Error**
   - Make sure you've created an admin user
   - Check that the admin_users table exists
   - Verify your authentication token

2. **"Database connection failed" Error**
   - Ensure PostgreSQL is running
   - Check database connection string
   - Verify the schema is properly migrated

3. **"Permission denied" Error**
   - Check admin user permissions
   - Verify the role has required permissions
   - Ensure admin user is active

4. **CORS Errors in Browser**
   - Add CORS configuration to your server
   - Use the same domain for API and test interface

### Database Setup

If you need to set up the admin_users table:

```sql
-- Create admin_users table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'admin',
    permissions JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id),
    last_login TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE,
    last_ip INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🎯 Test Scenarios

### Scenario 1: Basic Admin Dashboard
1. Login as admin
2. View dashboard overview
3. Check system health status
4. Review recent transactions (should be empty initially)

### Scenario 2: User Management
1. View user list (should show your admin user)
2. Get user statistics
3. View user details
4. Test user search and filtering

### Scenario 3: Transaction Monitoring
1. View transaction list (should be empty initially)
2. Get transaction statistics
3. View transaction analytics
4. Test transaction filtering

### Scenario 4: System Monitoring
1. Check system health
2. View real-time metrics
3. Monitor database performance
4. Check for alerts

## 📝 Next Steps

After testing the basic functionality:

1. **Create Test Data**
   - Add sample users through the regular registration flow
   - Create test transactions
   - Generate sample data for better testing

2. **Test Advanced Features**
   - User suspension/activation
   - Transaction status updates
   - System alerts and notifications
   - Performance monitoring

3. **Security Testing**
   - Test permission-based access
   - Verify admin authentication
   - Test rate limiting
   - Check audit logging

## 🆘 Support

If you encounter issues:

1. Check the server logs for detailed error messages
2. Verify database connectivity and schema
3. Ensure all environment variables are set
4. Check that all required services are running

For additional help, refer to the main JiraniPay documentation or create an issue in the project repository.
