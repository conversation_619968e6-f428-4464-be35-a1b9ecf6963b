-- IMMEDIATE FIX for Security Settings Constraint Violation
-- Run this in Supabase SQL editor to fix the duplicate key constraint issue

-- 1. First, let's see what duplicates we have
SELECT 
    user_id,
    COUNT(*) as duplicate_count,
    array_agg(id ORDER BY created_at DESC) as setting_ids,
    array_agg(created_at ORDER BY created_at DESC) as created_dates
FROM public.security_settings 
GROUP BY user_id 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. Clean up duplicates - keep the most recent one for each user
WITH ranked_settings AS (
    SELECT 
        id,
        user_id,
        ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
    FROM public.security_settings
),
duplicates_to_delete AS (
    SELECT id 
    FROM ranked_settings 
    WHERE rn > 1
)
DELETE FROM public.security_settings 
WHERE id IN (SELECT id FROM duplicates_to_delete);

-- 3. Verify cleanup - should show no duplicates
SELECT 
    user_id,
    COUNT(*) as setting_count
FROM public.security_settings 
GROUP BY user_id 
HAVING COUNT(*) > 1;

-- 4. Drop the problematic unique constraint temporarily
ALTER TABLE public.security_settings 
DROP CONSTRAINT IF EXISTS unique_user_security_settings;

-- 5. Re-add the constraint with proper handling
ALTER TABLE public.security_settings 
ADD CONSTRAINT unique_user_security_settings 
UNIQUE (user_id);

-- 6. Update the specific user's settings to ensure consistency
-- Replace 'USER_ID_HERE' with the actual user ID from the logs
UPDATE public.security_settings 
SET 
    biometric_enabled = true,
    updated_at = NOW()
WHERE user_id = '233c1e3e-40b7-4154-999f-b1812a557f82';

-- 7. Verify the update worked
SELECT 
    user_id,
    biometric_enabled,
    pin_enabled,
    two_factor_enabled,
    session_timeout_minutes,
    updated_at
FROM public.security_settings 
WHERE user_id = '233c1e3e-40b7-4154-999f-b1812a557f82';

-- 8. Create a function to handle upserts safely
CREATE OR REPLACE FUNCTION upsert_security_settings(
    p_user_id UUID,
    p_updates JSONB
) RETURNS TABLE(
    id UUID,
    user_id UUID,
    biometric_enabled BOOLEAN,
    pin_enabled BOOLEAN,
    two_factor_enabled BOOLEAN,
    session_timeout_minutes INTEGER,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Try to update first
    UPDATE public.security_settings 
    SET 
        biometric_enabled = COALESCE((p_updates->>'biometric_enabled')::BOOLEAN, biometric_enabled),
        pin_enabled = COALESCE((p_updates->>'pin_enabled')::BOOLEAN, pin_enabled),
        pin_hash = COALESCE(p_updates->>'pin_hash', pin_hash),
        two_factor_enabled = COALESCE((p_updates->>'two_factor_enabled')::BOOLEAN, two_factor_enabled),
        session_timeout_minutes = COALESCE((p_updates->>'session_timeout_minutes')::INTEGER, session_timeout_minutes),
        updated_at = NOW()
    WHERE security_settings.user_id = p_user_id;
    
    -- If no rows were updated, insert a new record
    IF NOT FOUND THEN
        INSERT INTO public.security_settings (
            user_id,
            biometric_enabled,
            pin_enabled,
            pin_hash,
            two_factor_enabled,
            session_timeout_minutes,
            created_at,
            updated_at
        ) VALUES (
            p_user_id,
            COALESCE((p_updates->>'biometric_enabled')::BOOLEAN, false),
            COALESCE((p_updates->>'pin_enabled')::BOOLEAN, false),
            p_updates->>'pin_hash',
            COALESCE((p_updates->>'two_factor_enabled')::BOOLEAN, false),
            COALESCE((p_updates->>'session_timeout_minutes')::INTEGER, 30),
            NOW(),
            NOW()
        );
    END IF;
    
    -- Return the updated/inserted record
    RETURN QUERY
    SELECT 
        s.id,
        s.user_id,
        s.biometric_enabled,
        s.pin_enabled,
        s.two_factor_enabled,
        s.session_timeout_minutes,
        s.updated_at
    FROM public.security_settings s
    WHERE s.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- 9. Grant execute permission on the function
GRANT EXECUTE ON FUNCTION upsert_security_settings(UUID, JSONB) TO authenticated;

-- 10. Test the function with the problematic user
SELECT * FROM upsert_security_settings(
    '233c1e3e-40b7-4154-999f-b1812a557f82'::UUID,
    '{"biometric_enabled": true, "session_timeout_minutes": 15}'::JSONB
);

-- Success message
SELECT 'Security settings constraint issue fixed successfully!' as status;
