/**
 * Enhanced AI Assistant Test Suite
 * Tests the comprehensive AI knowledge base and contextual responses
 */

import enhancedAIKnowledgeBase from '../services/enhancedAIKnowledgeBase';
import codebaseContextService from '../services/codebaseContextService';
import aiChatService from '../services/aiChatService';

class EnhancedAIAssistantTest {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  // =====================================================
  // TEST EXECUTION
  // =====================================================

  async runAllTests() {
    console.log('🚀 Starting Enhanced AI Assistant Test Suite...\n');

    // Test categories
    await this.testFAQIntegration();
    await this.testFeatureGuides();
    await this.testTroubleshooting();
    await this.testNavigationHelp();
    await this.testContextualResponses();
    await this.testUserIntentRecognition();
    await this.testAppKnowledgeAccuracy();

    this.printTestSummary();
    return this.generateTestReport();
  }

  // =====================================================
  // FAQ INTEGRATION TESTS
  // =====================================================

  async testFAQIntegration() {
    console.log('📋 Testing FAQ Integration...');

    const faqTestCases = [
      {
        query: 'How do I send money to another JiraniPay user?',
        expectedCategory: 'Money Transfer',
        shouldContain: ['Send Money section', 'phone number', 'QR code', 'PIN']
      },
      {
        query: 'What bills can I pay through JiraniPay?',
        expectedCategory: 'Bill Payments',
        shouldContain: ['Electricity', 'UMEME', 'Water bills', 'airtime']
      },
      {
        query: 'How do I add money to my wallet?',
        expectedCategory: 'Wallet Management',
        shouldContain: ['Bank transfer', 'Mobile money', 'MTN', 'Airtel']
      },
      {
        query: 'How do I use the QR code scanner?',
        expectedCategory: 'QR Code Scanner',
        shouldContain: ['QR scanner icon', 'camera', 'automatically detect']
      },
      {
        query: 'How do I verify my account?',
        expectedCategory: 'Account Security',
        shouldContain: ['Profile', 'Account Verification', 'Valid ID', 'selfie']
      }
    ];

    for (const testCase of faqTestCases) {
      const relevantFAQs = enhancedAIKnowledgeBase.findRelevantFAQ(testCase.query);
      
      if (relevantFAQs.length > 0) {
        const topFAQ = relevantFAQs[0];
        const containsExpected = testCase.shouldContain.every(keyword => 
          topFAQ.answer.toLowerCase().includes(keyword.toLowerCase())
        );

        this.recordTest(
          `FAQ: ${testCase.query}`,
          containsExpected && topFAQ.category === testCase.expectedCategory,
          `Expected category: ${testCase.expectedCategory}, Got: ${topFAQ.category}`
        );
      } else {
        this.recordTest(
          `FAQ: ${testCase.query}`,
          false,
          'No relevant FAQ found'
        );
      }
    }
  }

  // =====================================================
  // FEATURE GUIDES TESTS
  // =====================================================

  async testFeatureGuides() {
    console.log('📖 Testing Feature Guides...');

    const featureTestCases = [
      {
        feature: 'dashboard',
        shouldContain: ['central hub', 'wallet balance', 'quick actions']
      },
      {
        feature: 'send_money',
        shouldContain: ['Transfer money', 'phone number', 'QR code', 'PIN']
      },
      {
        feature: 'bill_payments',
        shouldContain: ['utilities', 'service provider', 'account number']
      }
    ];

    for (const testCase of featureTestCases) {
      const guide = enhancedAIKnowledgeBase.getFeatureGuide(testCase.feature);
      
      if (guide) {
        const containsExpected = testCase.shouldContain.every(keyword => 
          guide.description.toLowerCase().includes(keyword.toLowerCase()) ||
          guide.steps.some(step => step.toLowerCase().includes(keyword.toLowerCase()))
        );

        this.recordTest(
          `Feature Guide: ${testCase.feature}`,
          containsExpected,
          `Guide should contain: ${testCase.shouldContain.join(', ')}`
        );
      } else {
        this.recordTest(
          `Feature Guide: ${testCase.feature}`,
          false,
          'Feature guide not found'
        );
      }
    }
  }

  // =====================================================
  // TROUBLESHOOTING TESTS
  // =====================================================

  async testTroubleshooting() {
    console.log('🔧 Testing Troubleshooting...');

    const troubleshootingTestCases = [
      {
        issue: 'login issues',
        shouldContain: ['internet connection', 'phone number', 'password']
      },
      {
        issue: 'transaction failed',
        shouldContain: ['network connection', 'recipient details', 'balance']
      },
      {
        issue: 'app crashes',
        shouldContain: ['restart', 'update', 'cache']
      }
    ];

    for (const testCase of troubleshootingTestCases) {
      const guide = enhancedAIKnowledgeBase.getTroubleshootingGuide(testCase.issue);
      
      if (guide) {
        const containsExpected = testCase.shouldContain.every(keyword => 
          guide.solutions.some(solution => solution.toLowerCase().includes(keyword.toLowerCase()))
        );

        this.recordTest(
          `Troubleshooting: ${testCase.issue}`,
          containsExpected,
          `Should contain solutions for: ${testCase.shouldContain.join(', ')}`
        );
      } else {
        this.recordTest(
          `Troubleshooting: ${testCase.issue}`,
          false,
          'Troubleshooting guide not found'
        );
      }
    }
  }

  // =====================================================
  // NAVIGATION HELP TESTS
  // =====================================================

  async testNavigationHelp() {
    console.log('🧭 Testing Navigation Help...');

    const navigationTestCases = [
      {
        flow: 'send_money_flow',
        shouldContain: ['Dashboard', 'Send Money', 'recipient', 'amount', 'PIN']
      },
      {
        flow: 'bill_payment_flow',
        shouldContain: ['Pay Bills', 'category', 'provider', 'account', 'PIN']
      },
      {
        flow: 'support_flow',
        shouldContain: ['Profile', 'Contact Support', 'channel', 'issue']
      }
    ];

    for (const testCase of navigationTestCases) {
      const flow = enhancedAIKnowledgeBase.getNavigationFlow(testCase.flow);
      
      if (flow) {
        const containsExpected = testCase.shouldContain.every(keyword => 
          flow.some(step => step.toLowerCase().includes(keyword.toLowerCase()))
        );

        this.recordTest(
          `Navigation: ${testCase.flow}`,
          containsExpected,
          `Flow should contain: ${testCase.shouldContain.join(', ')}`
        );
      } else {
        this.recordTest(
          `Navigation: ${testCase.flow}`,
          false,
          'Navigation flow not found'
        );
      }
    }
  }

  // =====================================================
  // CONTEXTUAL RESPONSES TESTS
  // =====================================================

  async testContextualResponses() {
    console.log('🎯 Testing Contextual Responses...');

    const contextTestCases = [
      {
        query: 'How to send money',
        intent: 'feature_usage',
        expectedType: 'feature_guide'
      },
      {
        query: 'My transaction failed',
        intent: 'problem',
        expectedType: 'troubleshooting'
      },
      {
        query: 'What are the transfer limits?',
        intent: 'information',
        expectedType: 'faq_match'
      }
    ];

    for (const testCase of contextTestCases) {
      const intent = enhancedAIKnowledgeBase.analyzeUserIntent(testCase.query);
      const response = enhancedAIKnowledgeBase.getContextualResponse(testCase.query, intent);
      
      this.recordTest(
        `Context: ${testCase.query}`,
        intent === testCase.intent && response.type === testCase.expectedType,
        `Expected intent: ${testCase.intent}, type: ${testCase.expectedType}. Got intent: ${intent}, type: ${response.type}`
      );
    }
  }

  // =====================================================
  // USER INTENT RECOGNITION TESTS
  // =====================================================

  async testUserIntentRecognition() {
    console.log('🧠 Testing User Intent Recognition...');

    const intentTestCases = [
      { query: 'How do I send money?', expectedIntent: 'how_to' },
      { query: 'My app is not working', expectedIntent: 'problem' },
      { query: 'What is JiraniPay?', expectedIntent: 'information' },
      { query: 'Where is the send money button?', expectedIntent: 'navigation' },
      { query: 'What are the fees?', expectedIntent: 'fees' },
      { query: 'Is my money safe?', expectedIntent: 'security' }
    ];

    for (const testCase of intentTestCases) {
      const detectedIntent = enhancedAIKnowledgeBase.analyzeUserIntent(testCase.query);
      
      this.recordTest(
        `Intent: ${testCase.query}`,
        detectedIntent === testCase.expectedIntent,
        `Expected: ${testCase.expectedIntent}, Got: ${detectedIntent}`
      );
    }
  }

  // =====================================================
  // APP KNOWLEDGE ACCURACY TESTS
  // =====================================================

  async testAppKnowledgeAccuracy() {
    console.log('📱 Testing App Knowledge Accuracy...');

    const knowledgeTestCases = [
      {
        query: 'What screens are in the app?',
        shouldKnow: ['DashboardScreen', 'WalletScreen', 'BillPaymentScreen', 'ProfileScreen']
      },
      {
        query: 'What services does the app use?',
        shouldKnow: ['walletService', 'transactionService', 'billPaymentService', 'aiChatService']
      },
      {
        query: 'What are the main features?',
        shouldKnow: ['money_transfer', 'bill_payments', 'wallet_management', 'qr_functionality']
      }
    ];

    for (const testCase of knowledgeTestCases) {
      const context = codebaseContextService.analyzeUserContext(testCase.query);
      const hasKnowledge = testCase.shouldKnow.some(item => 
        JSON.stringify(context).toLowerCase().includes(item.toLowerCase())
      );

      this.recordTest(
        `Knowledge: ${testCase.query}`,
        hasKnowledge,
        `Should know about: ${testCase.shouldKnow.join(', ')}`
      );
    }
  }

  // =====================================================
  // TEST UTILITIES
  // =====================================================

  recordTest(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });

    if (passed) {
      this.passedTests++;
      console.log(`  ✅ ${testName}`);
    } else {
      this.failedTests++;
      console.log(`  ❌ ${testName}: ${details}`);
    }
  }

  printTestSummary() {
    const totalTests = this.passedTests + this.failedTests;
    const successRate = ((this.passedTests / totalTests) * 100).toFixed(1);

    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.failedTests}`);
    console.log(`Success Rate: ${successRate}%`);
    console.log('='.repeat(50));

    if (this.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`  • ${test.name}: ${test.details}`);
        });
    }
  }

  generateTestReport() {
    return {
      summary: {
        totalTests: this.passedTests + this.failedTests,
        passed: this.passedTests,
        failed: this.failedTests,
        successRate: ((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(1)
      },
      results: this.testResults,
      timestamp: new Date().toISOString()
    };
  }
}

// Export for use in other test files
export default EnhancedAIAssistantTest;

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new EnhancedAIAssistantTest();
  testSuite.runAllTests().then(report => {
    console.log('\n📋 Test report generated successfully');
  }).catch(error => {
    console.error('❌ Test execution failed:', error);
  });
}
