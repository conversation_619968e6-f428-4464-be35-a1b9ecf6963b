import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
  Image,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import smartAmountSuggestions from '../utils/smartAmountSuggestions';
import sendMoneyService from '../services/sendMoneyService';
import authService from '../services/authService';
import walletService from '../services/walletService';
import verificationService from '../services/verificationService';

/**
 * TransferAmountScreen - Amount entry and transfer confirmation
 * Features amount validation, fee calculation, and recipient verification
 */
const TransferAmountScreen = ({ navigation, route }) => {
  // Use theme and currency contexts
  const { theme } = useTheme();
  const { convertFromUGX, formatAmount, userCurrency } = useCurrencyContext();
  const styles = createStyles(theme);

  const { recipient } = route.params;
  const [amount, setAmount] = useState('');
  const [purpose, setPurpose] = useState('');
  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [wallet, setWallet] = useState(null);
  const [feeCalculation, setFeeCalculation] = useState(null);
  const [validatedRecipient, setValidatedRecipient] = useState(null);
  const [user, setUser] = useState(null);

  // Smart quick amounts based on user's currency and transfer patterns
  const quickAmounts = smartAmountSuggestions.generateQuickAmounts(userCurrency, 'transfer');
  const transferPurposes = [
    'Personal Transfer',
    'Family Support',
    'Bill Payment',
    'Business Payment',
    'Gift',
    'Other',
  ];

  useEffect(() => {
    loadInitialData();
    validateRecipient();
  }, []);

  useEffect(() => {
    if (amount && parseFloat(amount) > 0) {
      calculateFees();
    } else {
      setFeeCalculation(null);
    }
  }, [amount]);

  const loadInitialData = async () => {
    try {
      // Get current user
      const currentUser = await authService.getCurrentUser();
      if (currentUser.success) {
        setUser(currentUser.user);
      }

      // Get wallet balance
      const walletResult = await walletService.getWalletBalance();
      if (walletResult.success) {
        setWallet(walletResult.data);
      }
    } catch (error) {
      console.error('Load initial data error:', error);
    }
  };

  const validateRecipient = async () => {
    try {
      setValidating(true);
      const result = await sendMoneyService.validateRecipient(recipient.phoneNumber);
      
      if (result.success) {
        setValidatedRecipient(result.recipient);
      } else {
        Alert.alert('Recipient Validation', result.error);
      }
    } catch (error) {
      console.error('Recipient validation error:', error);
    } finally {
      setValidating(false);
    }
  };

  const calculateFees = () => {
    const transferAmount = parseFloat(amount);
    if (transferAmount > 0) {
      const calculation = sendMoneyService.calculateTransferFee(transferAmount);
      setFeeCalculation(calculation);
    }
  };

  const handleAmountChange = (value) => {
    // Remove any non-numeric characters except decimal point
    const cleanValue = value.replace(/[^0-9.]/g, '');
    
    // Ensure only one decimal point
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      return;
    }
    
    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }
    
    setAmount(cleanValue);
  };

  const handleQuickAmount = (quickAmount) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setAmount(quickAmount.toString());
  };

  const handleContinue = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid transfer amount');
      return;
    }

    if (!validatedRecipient) {
      Alert.alert('Recipient Error', 'Please wait for recipient validation to complete');
      return;
    }

    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Check verification requirements for high-value transactions
      const verificationCheck = await verificationService.checkTransactionVerification(
        parseFloat(amount),
        'transfer',
        navigation
      );

      if (!verificationCheck.success) {
        if (verificationCheck.requiresVerification) {
          // Show verification alert and handle user choice
          const shouldProceed = await verificationService.showVerificationAlert(verificationCheck);
          if (!shouldProceed) {
            return; // User chose to verify or cancelled
          }
        } else {
          Alert.alert('Transaction Error', verificationCheck.error);
          return;
        }
      }

      // Validate transfer amount
      const validation = await sendMoneyService.validateTransferAmount(
        parseFloat(amount),
        user?.id
      );

      if (!validation.success) {
        Alert.alert('Transfer Validation', validation.error);
        return;
      }

      // Navigate to confirmation screen
      navigation.navigate('TransferConfirmation', {
        recipient: validatedRecipient,
        amount: parseFloat(amount),
        purpose: purpose || 'Personal Transfer',
        feeCalculation: feeCalculation,
        validation: validation,
      });
    } catch (error) {
      console.error('Continue error:', error);
      Alert.alert('Error', 'Failed to proceed with transfer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value) => {
    return formatAmount(parseFloat(value), userCurrency);
  };

  const renderRecipientCard = () => (
    <View style={styles.recipientCard}>
      <View style={styles.recipientHeader}>
        <Text style={styles.recipientTitle}>Sending to</Text>
        {validating && (
          <View style={styles.validatingBadge}>
            <Text style={styles.validatingText}>Validating...</Text>
          </View>
        )}
      </View>
      
      <View style={styles.recipientInfo}>
        {recipient.image ? (
          <Image source={{ uri: recipient.image.uri }} style={styles.recipientAvatar} />
        ) : (
          <View style={[styles.recipientAvatar, styles.recipientAvatarPlaceholder]}>
            <Text style={styles.recipientAvatarText}>
              {recipient.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        
        <View style={styles.recipientDetails}>
          <Text style={styles.recipientName}>
            {validatedRecipient?.name || recipient.name}
          </Text>
          <Text style={styles.recipientPhone}>{recipient.phoneNumber}</Text>
          {recipient.provider && (
            <View style={[
              styles.providerBadge,
              { backgroundColor: recipient.provider.color + '20' }
            ]}>
              <Text style={[
                styles.providerText,
                { color: recipient.provider.color }
              ]}>
                {recipient.provider.name}
              </Text>
            </View>
          )}
        </View>
        
        {validatedRecipient && (
          <View style={styles.verifiedBadge}>
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.success} />
          </View>
        )}
      </View>
    </View>
  );

  const renderAmountInput = () => (
    <View style={styles.amountSection}>
      <Text style={styles.sectionTitle}>Enter Amount</Text>
      
      <View style={styles.amountInputContainer}>
        <Text style={styles.currencySymbol}>UGX</Text>
        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={handleAmountChange}
          placeholder="0"
          placeholderTextColor={theme.colors.placeholder}
          keyboardType="numeric"
          maxLength={10}
        />
      </View>

      {wallet && (
        <Text style={styles.balanceText}>
          Available Balance: {formatCurrency(wallet.balance)}
        </Text>
      )}

      {/* Quick Amount Buttons */}
      <View style={styles.quickAmounts}>
        {quickAmounts.map((quickAmount) => (
          <TouchableOpacity
            key={quickAmount}
            style={styles.quickAmountButton}
            onPress={() => handleQuickAmount(quickAmount)}
            activeOpacity={0.7}
          >
            <Text style={styles.quickAmountText}>
              {formatAmount(quickAmount, userCurrency, { showSymbol: false, compact: true })}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderFeeCalculation = () => {
    if (!feeCalculation) return null;

    return (
      <View style={styles.feeSection}>
        <Text style={styles.sectionTitle}>Transfer Summary</Text>
        
        <View style={styles.feeBreakdown}>
          <View style={styles.feeRow}>
            <Text style={styles.feeLabel}>Transfer Amount</Text>
            <Text style={styles.feeValue}>{formatCurrency(feeCalculation.amount)}</Text>
          </View>
          
          <View style={styles.feeRow}>
            <Text style={styles.feeLabel}>Transfer Fee</Text>
            <Text style={[
              styles.feeValue,
              feeCalculation.freeTransfer && styles.freeText
            ]}>
              {feeCalculation.freeTransfer ? 'FREE' : formatCurrency(feeCalculation.fee)}
            </Text>
          </View>
          
          <View style={[styles.feeRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total Amount</Text>
            <Text style={styles.totalValue}>{formatCurrency(feeCalculation.total)}</Text>
          </View>
        </View>

        {feeCalculation.freeTransfer && (
          <View style={styles.freeTransferBadge}>
            <Ionicons name="gift" size={16} color={theme.colors.success} />
            <Text style={styles.freeTransferText}>Free Transfer!</Text>
          </View>
        )}
      </View>
    );
  };

  const renderPurposeSelection = () => (
    <View style={styles.purposeSection}>
      <Text style={styles.sectionTitle}>Transfer Purpose (Optional)</Text>
      
      <View style={styles.purposeButtons}>
        {transferPurposes.map((purposeOption) => (
          <TouchableOpacity
            key={purposeOption}
            style={[
              styles.purposeButton,
              purpose === purposeOption && styles.purposeButtonSelected
            ]}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              setPurpose(purpose === purposeOption ? '' : purposeOption);
            }}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.purposeButtonText,
              purpose === purposeOption && styles.purposeButtonTextSelected
            ]}>
              {purposeOption}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Transfer Amount</Text>
        <View style={styles.headerSpacer} />
      </View>

      <KeyboardAvoidingView
        style={styles.content}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {renderRecipientCard()}
          {renderAmountInput()}
          {renderFeeCalculation()}
          {renderPurposeSelection()}
        </ScrollView>

        {/* Continue Button */}
        <View style={styles.bottomSection}>
          <TouchableOpacity
            style={[
              styles.continueButton,
              (!amount || parseFloat(amount) <= 0 || !validatedRecipient || loading) && styles.continueButtonDisabled
            ]}
            onPress={handleContinue}
            disabled={!amount || parseFloat(amount) <= 0 || !validatedRecipient || loading}
            activeOpacity={0.8}
          >
            <Text style={styles.continueButtonText}>
              {loading ? 'Processing...' : 'Continue'}
            </Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 20,
  },
  recipientCard: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: theme.colors.primary + '20',
  },
  recipientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  recipientTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.textSecondary,
  },
  validatingBadge: {
    backgroundColor: theme.colors.warning + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  validatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.warning,
  },
  recipientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recipientAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  recipientAvatarPlaceholder: {
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipientAvatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  recipientDetails: {
    flex: 1,
  },
  recipientName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  recipientPhone: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 6,
  },
  providerBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  providerText: {
    fontSize: 12,
    fontWeight: '600',
  },
  verifiedBadge: {
    padding: 4,
  },
  amountSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: theme.colors.primary,
    paddingBottom: 8,
    marginBottom: 12,
  },
  currencySymbol: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'left',
  },
  balanceText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
  },
  quickAmounts: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickAmountButton: {
    backgroundColor: theme.colors.inputBackground,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickAmountText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  feeSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  feeBreakdown: {
    gap: 12,
  },
  feeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  feeLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  feeValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  freeText: {
    color: theme.colors.success,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  freeTransferBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.success + '10',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginTop: 12,
    gap: 6,
  },
  freeTransferText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.success,
  },
  purposeSection: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  purposeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  purposeButton: {
    backgroundColor: theme.colors.inputBackground,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  purposeButtonSelected: {
    backgroundColor: theme.colors.primary + '10',
    borderColor: theme.colors.primary,
  },
  purposeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
  },
  purposeButtonTextSelected: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  bottomSection: {
    padding: 20,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  continueButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonDisabled: {
    backgroundColor: theme.colors.disabled,
  },
  continueButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: theme.colors.white,
  },
});

export default TransferAmountScreen;
