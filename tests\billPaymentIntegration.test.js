/**
 * Bill Payment System Integration Tests
 * Comprehensive tests for the complete bill payment system
 */

import billPaymentService from '../services/billPaymentService';
import billerManagementService from '../services/billerManagementService';
import paymentValidationEngine from '../services/paymentValidationEngine';
import paymentHistoryService from '../services/paymentHistoryService';
import recurringPaymentsService from '../services/recurringPaymentsService';

describe('Bill Payment System Integration', () => {
  const testUserId = 'test-user-123';
  const testBillerId = 'test-biller-456';
  
  beforeAll(async () => {
    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
  });

  describe('Biller Management', () => {
    test('should fetch bill categories', async () => {
      const result = await billerManagementService.getBillCategories();
      
      expect(result.success).toBe(true);
      expect(result.categories).toBeInstanceOf(Array);
      expect(result.categories.length).toBeGreaterThan(0);
      
      const category = result.categories[0];
      expect(category).toHaveProperty('id');
      expect(category).toHaveProperty('name');
      expect(category).toHaveProperty('displayName');
    });

    test('should fetch active billers', async () => {
      const result = await billerManagementService.getActiveBillers();
      
      expect(result.success).toBe(true);
      expect(result.billers).toBeInstanceOf(Array);
      
      if (result.billers.length > 0) {
        const biller = result.billers[0];
        expect(biller).toHaveProperty('id');
        expect(biller).toHaveProperty('displayName');
        expect(biller).toHaveProperty('category');
        expect(biller.isActive).toBe(true);
        expect(biller.isAvailable).toBe(true);
      }
    });

    test('should search billers by query', async () => {
      const result = await billerManagementService.searchBillers('MTN');
      
      expect(result.success).toBe(true);
      expect(result.billers).toBeInstanceOf(Array);
      expect(result.query).toBe('MTN');
    });

    test('should check biller availability', async () => {
      const result = await billerManagementService.checkBillerAvailability(testBillerId);
      
      expect(result.success).toBe(true);
      expect(result.availability).toHaveProperty('isAvailable');
      expect(result.availability).toHaveProperty('isActive');
      expect(result.availability).toHaveProperty('maintenanceMode');
    });
  });

  describe('Payment Validation', () => {
    test('should validate valid payment data', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 50000,
        paymentMethod: 'wallet'
      };

      const result = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(result.errors).toBeInstanceOf(Array);
      expect(result.warnings).toBeInstanceOf(Array);
    });

    test('should reject invalid account number', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '123', // Too short
        amount: 50000,
        paymentMethod: 'wallet'
      };

      const result = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toHaveProperty('type');
      expect(result.errors[0]).toHaveProperty('message');
    });

    test('should reject invalid amount', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: -1000, // Negative amount
        paymentMethod: 'wallet'
      };

      const result = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.some(error => error.type === 'invalid_amount')).toBe(true);
    });

    test('should detect duplicate payments', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 50000,
        paymentMethod: 'wallet'
      };

      // First validation should pass
      const firstResult = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      
      // Simulate recent payment by creating one
      await createTestPayment(testUserId, paymentData);
      
      // Second validation should detect duplicate
      const secondResult = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      
      expect(secondResult.isValid).toBe(false);
      expect(secondResult.errors.some(error => error.type === 'duplicate_payment')).toBe(true);
    });
  });

  describe('Bill Payment Processing', () => {
    test('should process successful payment', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 75000,
        currency: 'UGX',
        paymentMethod: 'wallet'
      };

      const result = await billPaymentService.processBillPayment(testUserId, paymentData);
      
      expect(result.success).toBe(true);
      expect(result.payment).toHaveProperty('id');
      expect(result.payment).toHaveProperty('reference');
      expect(result.payment).toHaveProperty('status');
      expect(result.payment.amount).toBe(paymentData.amount);
      expect(result.payment.accountNumber).toBe(paymentData.accountNumber);
    });

    test('should handle payment failure gracefully', async () => {
      const paymentData = {
        billerId: 'invalid-biller-id',
        accountNumber: '**********',
        amount: 50000,
        currency: 'UGX',
        paymentMethod: 'wallet'
      };

      try {
        await billPaymentService.processBillPayment(testUserId, paymentData);
        fail('Expected payment to fail');
      } catch (error) {
        expect(error.message).toContain('Biller not found');
      }
    });

    test('should calculate fees correctly', async () => {
      const biller = await billerManagementService.getBillerById(testBillerId);
      if (biller.success) {
        const amount = 100000;
        const feeCalculation = billPaymentService.calculatePaymentFee(biller.biller, amount);
        
        expect(feeCalculation).toHaveProperty('fee');
        expect(feeCalculation).toHaveProperty('totalAmount');
        expect(feeCalculation).toHaveProperty('feeType');
        expect(feeCalculation.totalAmount).toBe(amount + feeCalculation.fee);
      }
    });
  });

  describe('Payment History', () => {
    test('should fetch payment history', async () => {
      const result = await paymentHistoryService.getPaymentHistory(testUserId, {
        limit: 10,
        offset: 0
      });
      
      expect(result.success).toBe(true);
      expect(result.payments).toBeInstanceOf(Array);
      expect(result.summary).toHaveProperty('totalCount');
      expect(result.summary).toHaveProperty('totalAmount');
      expect(result.pagination).toHaveProperty('limit');
      expect(result.pagination).toHaveProperty('offset');
    });

    test('should get payment details', async () => {
      // Create a test payment first
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 25000,
        currency: 'UGX'
      };

      const paymentResult = await billPaymentService.processBillPayment(testUserId, paymentData);
      
      if (paymentResult.success) {
        const detailsResult = await paymentHistoryService.getPaymentDetails(
          paymentResult.payment.id,
          testUserId
        );
        
        expect(detailsResult.success).toBe(true);
        expect(detailsResult.payment).toHaveProperty('id');
        expect(detailsResult.payment).toHaveProperty('biller');
        expect(detailsResult.payment).toHaveProperty('statusHistory');
      }
    });

    test('should generate payment analytics', async () => {
      const result = await paymentHistoryService.getPaymentAnalytics(testUserId, 'month');
      
      expect(result.success).toBe(true);
      expect(result.analytics).toHaveProperty('period');
      expect(result.analytics).toHaveProperty('totalSpent');
      expect(result.analytics).toHaveProperty('totalPayments');
      expect(result.analytics).toHaveProperty('categoryBreakdown');
      expect(result.analytics.categoryBreakdown).toBeInstanceOf(Array);
    });

    test('should export payment history', async () => {
      const result = await paymentHistoryService.exportPaymentHistory(testUserId, {
        format: 'csv'
      });
      
      expect(result.success).toBe(true);
      expect(result.format).toBe('csv');
      expect(result.content).toBeDefined();
      expect(result.filename).toContain('.csv');
    });
  });

  describe('Recurring Payments', () => {
    test('should create recurring payment', async () => {
      const recurringData = {
        billerId: testBillerId,
        name: 'Monthly Electricity Bill',
        accountNumber: '**********',
        amount: 150000,
        frequency: 'monthly',
        startDate: '2024-02-01',
        reminderEnabled: true,
        reminderDaysBefore: 2
      };

      const result = await recurringPaymentsService.createRecurringPayment(testUserId, recurringData);
      
      expect(result.success).toBe(true);
      expect(result.recurringPayment).toHaveProperty('id');
      expect(result.recurringPayment).toHaveProperty('nextPaymentDate');
      expect(result.recurringPayment.isActive).toBe(true);
      expect(result.recurringPayment.frequency).toBe('monthly');
    });

    test('should fetch user recurring payments', async () => {
      const result = await recurringPaymentsService.getUserRecurringPayments(testUserId);
      
      expect(result.success).toBe(true);
      expect(result.recurringPayments).toBeInstanceOf(Array);
      expect(result.pagination).toHaveProperty('limit');
      expect(result.pagination).toHaveProperty('offset');
    });

    test('should pause and resume recurring payment', async () => {
      // Create a recurring payment first
      const recurringData = {
        billerId: testBillerId,
        name: 'Test Recurring Payment',
        accountNumber: '**********',
        amount: 50000,
        frequency: 'weekly',
        startDate: '2024-02-01'
      };

      const createResult = await recurringPaymentsService.createRecurringPayment(testUserId, recurringData);
      
      if (createResult.success) {
        const recurringId = createResult.recurringPayment.id;
        
        // Pause the payment
        const pauseResult = await recurringPaymentsService.pauseRecurringPayment(recurringId, testUserId);
        expect(pauseResult.success).toBe(true);
        
        // Resume the payment
        const resumeResult = await recurringPaymentsService.resumeRecurringPayment(recurringId, testUserId);
        expect(resumeResult.success).toBe(true);
      }
    });

    test('should calculate next payment date correctly', async () => {
      const currentDate = '2024-01-15';
      
      // Test monthly frequency
      const monthlyNext = recurringPaymentsService.calculateNextPaymentDate(
        currentDate, 'monthly', 1, 15
      );
      expect(monthlyNext).toBe('2024-02-15');
      
      // Test weekly frequency
      const weeklyNext = recurringPaymentsService.calculateNextPaymentDate(
        currentDate, 'weekly', 1
      );
      expect(new Date(weeklyNext)).toBeInstanceOf(Date);
    });

    test('should validate recurring payment data', async () => {
      const validData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 100000,
        frequency: 'monthly',
        startDate: '2024-03-01'
      };

      const validResult = await recurringPaymentsService.validateRecurringPayment(testUserId, validData);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      const invalidData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: -50000, // Invalid amount
        frequency: 'invalid-frequency', // Invalid frequency
        startDate: '2023-01-01' // Past date
      };

      const invalidResult = await recurringPaymentsService.validateRecurringPayment(testUserId, invalidData);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Integration Flow', () => {
    test('should complete full payment flow with notifications', async () => {
      const paymentData = {
        billerId: testBillerId,
        accountNumber: '**********',
        amount: 200000,
        currency: 'UGX',
        paymentMethod: 'wallet'
      };

      // 1. Validate payment
      const validation = await paymentValidationEngine.validatePayment(testUserId, paymentData);
      expect(validation.isValid).toBe(true);

      // 2. Process payment
      const paymentResult = await billPaymentService.processBillPayment(testUserId, paymentData);
      expect(paymentResult.success).toBe(true);

      // 3. Check payment history
      const historyResult = await paymentHistoryService.getPaymentDetails(
        paymentResult.payment.id,
        testUserId
      );
      expect(historyResult.success).toBe(true);

      // 4. Verify status tracking
      expect(historyResult.payment.statusHistory).toBeInstanceOf(Array);
      expect(historyResult.payment.statusHistory.length).toBeGreaterThan(0);
    });

    test('should handle recurring payment processing', async () => {
      // Create recurring payment
      const recurringData = {
        billerId: testBillerId,
        name: 'Integration Test Recurring',
        accountNumber: '**********',
        amount: 75000,
        frequency: 'monthly',
        startDate: new Date().toISOString().split('T')[0] // Today
      };

      const createResult = await recurringPaymentsService.createRecurringPayment(testUserId, recurringData);
      expect(createResult.success).toBe(true);

      // Simulate due payment processing
      const dueResult = await recurringPaymentsService.processDuePayments();
      expect(dueResult.success).toBe(true);
    });
  });

  // Helper functions
  async function setupTestData() {
    // Setup test biller and categories
    // This would typically insert test data into the database
    console.log('Setting up test data...');
  }

  async function cleanupTestData() {
    // Cleanup test data
    // This would typically remove test data from the database
    console.log('Cleaning up test data...');
  }

  async function createTestPayment(userId, paymentData) {
    // Create a test payment for duplicate detection testing
    return await billPaymentService.processBillPayment(userId, {
      ...paymentData,
      accountNumber: paymentData.accountNumber + '_test'
    });
  }
});

// Performance tests
describe('Bill Payment Performance', () => {
  test('should handle concurrent payment validations', async () => {
    const paymentData = {
      billerId: testBillerId,
      accountNumber: '**********',
      amount: 50000,
      paymentMethod: 'wallet'
    };

    const promises = Array(10).fill().map((_, index) => 
      paymentValidationEngine.validatePayment(`test-user-${index}`, paymentData)
    );

    const results = await Promise.all(promises);
    
    results.forEach(result => {
      expect(result).toHaveProperty('isValid');
      expect(result).toHaveProperty('errors');
    });
  });

  test('should handle large payment history queries efficiently', async () => {
    const startTime = Date.now();
    
    const result = await paymentHistoryService.getPaymentHistory(testUserId, {
      limit: 100,
      offset: 0
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    expect(result.success).toBe(true);
    expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
  });
});

export default {
  testUserId,
  testBillerId
};
