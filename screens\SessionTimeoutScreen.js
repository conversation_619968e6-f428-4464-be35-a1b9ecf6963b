import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import securityManagementService from '../services/securityManagementService';
import authService from '../services/authService';

const SessionTimeoutScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [user, setUser] = useState(null);
  const [selectedTimeout, setSelectedTimeout] = useState(30);
  const [securitySettings, setSecuritySettings] = useState(null);

  const timeoutOptions = [
    { value: 5, label: '5 minutes', description: 'High security - frequent re-authentication' },
    { value: 15, label: '15 minutes', description: 'Balanced security and convenience' },
    { value: 30, label: '30 minutes', description: 'Standard security (recommended)' },
    { value: 60, label: '1 hour', description: 'Extended session for convenience' },
    { value: 120, label: '2 hours', description: 'Low security - maximum convenience' },
  ];

  useEffect(() => {
    loadSessionData();
  }, []);

  const loadSessionData = async () => {
    try {
      setLoading(true);

      const currentUser = authService.getCurrentUser();
      console.log('👤 Current user:', currentUser);

      if (currentUser) {
        setUser(currentUser);

        const settingsResult = await securityManagementService.getSecuritySettings(currentUser.id);
        console.log('⚙️ Security settings result:', settingsResult);

        if (settingsResult.success) {
          setSecuritySettings(settingsResult.data);
          const timeoutValue = settingsResult.data.session_timeout_minutes || 30;
          console.log('⏱️ Setting timeout to:', timeoutValue);
          setSelectedTimeout(timeoutValue);
        } else {
          console.error('❌ Failed to load security settings:', settingsResult.error);
          // Set default timeout if loading fails
          setSelectedTimeout(30);
        }
      } else {
        console.error('❌ No current user found');
        Alert.alert(t('error'), t('pleaseLogInToAccessSessionSettings'));
      }
    } catch (error) {
      console.error('❌ Error loading session data:', error);
      Alert.alert(t('error'), t('failedToLoadSessionTimeoutSettings'));
      // Set default timeout on error
      setSelectedTimeout(30);
    } finally {
      setLoading(false);
    }
  };

  const updateSessionTimeout = async (timeoutMinutes) => {
    // Prevent multiple simultaneous updates
    if (updating) {
      console.log('⏳ Update already in progress, ignoring...');
      return;
    }

    try {
      console.log('🔄 Updating session timeout to:', timeoutMinutes, 'minutes');
      console.log('👤 User ID:', user?.id);

      setUpdating(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      if (!user?.id) {
        console.error('❌ No user ID available');
        Alert.alert(t('error'), t('userNotFoundPleaseTryLoggingInAgain'));
        return;
      }

      // Optimistically update the UI first
      setSelectedTimeout(timeoutMinutes);

      const result = await securityManagementService.updateSecuritySettings(user.id, {
        session_timeout_minutes: timeoutMinutes
      });

      console.log('📊 Update result:', result);

      if (result.success) {
        console.log('✅ Session timeout updated successfully');

        // Update the security settings state
        setSecuritySettings(prev => ({
          ...prev,
          session_timeout_minutes: timeoutMinutes
        }));

        // Update the auth service with new timeout
        authService.updateSessionTimeout(timeoutMinutes);

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(t('sessionTimeoutUpdated'), t('yourSessionWillNowTimeoutAfterTimeoutminutesMinute')
        );
      } else {
        console.error('❌ Update failed:', result.error);

        // Revert the optimistic update
        const originalTimeout = securitySettings?.session_timeout_minutes || 30;
        setSelectedTimeout(originalTimeout);

        Alert.alert('Error', result.error || 'Failed to update session timeout');
      }
    } catch (error) {
      console.error('❌ Error updating session timeout:', error);

      // Revert the optimistic update
      const originalTimeout = securitySettings?.session_timeout_minutes || 30;
      setSelectedTimeout(originalTimeout);

      Alert.alert(t('error'), t('failedToUpdateSessionTimeout') + error.message);
    } finally {
      setUpdating(false);
    }
  };

  const renderTimeoutOption = (option) => {
    const isSelected = selectedTimeout === option.value;
    const isDisabled = updating;

    // Get security level indicator
    const getSecurityLevel = (minutes) => {
      if (minutes <= 15) return { level: 'High', color: Colors.status.success, icon: 'shield' };
      if (minutes <= 30) return { level: 'Medium', color: Colors.accent.gold, icon: 'shield-half' };
      return { level: 'Basic', color: Colors.status.warning, icon: 'shield-outline' };
    };

    const security = getSecurityLevel(option.value);

    return (
      <TouchableOpacity
        key={option.value}
        style={[
          styles.timeoutOptionCompact,
          isSelected && styles.selectedOptionCompact,
          isDisabled && styles.disabledOption
        ]}
        onPress={() => {
          if (!isDisabled) {
            updateSessionTimeout(option.value);
          }
        }}
        activeOpacity={isDisabled ? 1 : 0.7}
        disabled={isDisabled}
      >
        <View style={styles.optionHeaderCompact}>
          <View style={styles.optionTitleRowCompact}>
            <Text style={[
              styles.optionTitleCompact,
              isSelected && styles.selectedTextCompact,
              isDisabled && styles.disabledText
            ]}>
              {option.label}
            </Text>
            <View style={styles.securityBadgeCompact}>
              <Ionicons name={security.icon} size={12} color={security.color} />
              <Text style={[styles.securityTextCompact, { color: security.color }]}>
                {security.level}
              </Text>
            </View>
          </View>

          <View style={[
            styles.radioButtonCompact,
            isSelected && styles.selectedRadioCompact,
            isDisabled && styles.disabledRadio
          ]}>
            {isSelected && !isDisabled && (
              <View style={styles.radioInnerCompact} />
            )}
            {updating && isSelected && (
              <View style={styles.loadingIndicatorCompact}>
                <Text style={styles.loadingTextCompact}>⏳</Text>
              </View>
            )}
          </View>
        </View>

        <Text style={[
          styles.optionDescriptionCompact,
          isSelected && styles.selectedDescriptionCompact,
          isDisabled && styles.disabledText
        ]}>
          {option.description}
        </Text>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('loadingSessionSettings')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('sessionTimeout')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('controlHowLongYouStayLoggedIn')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Setting - Compact Design */}
        <View style={styles.section}>
          <View style={styles.currentSettingCompact}>
            <View style={styles.currentIconCompact}>
              <Ionicons name="timer-outline" size={24} color={Colors.primary.main} />
            </View>
            <View style={styles.currentTextCompact}>
              <Text style={styles.currentTitleCompact}>{t('currentTimeout')}</Text>
              <View style={styles.currentValueContainerCompact}>
                <Text style={styles.currentValueCompact}>{selectedTimeout} min</Text>
                {updating && (
                  <View style={styles.updatingIndicator}>
                    <Text style={styles.updatingTextCompact}>{t('updating')}</Text>
                  </View>
                )}
              </View>
            </View>
            <View style={styles.statusIndicator}>
              <Ionicons
                name="shield-checkmark"
                size={20}
                color={Colors.status.success}
              />
            </View>
          </View>
          <Text style={styles.currentDescriptionCompact}>
            Auto-logout after {selectedTimeout} minutes of inactivity
          </Text>
        </View>

        {/* Timeout Options - Enhanced Design */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('timeoutOptions')}</Text>
          <Text style={styles.sectionDescription}>
            {t('chooseYourPreferredAutologoutDuration')}
          </Text>

          <View style={styles.optionsGrid}>
            {timeoutOptions.map(renderTimeoutOption)}
          </View>
        </View>

        {/* Security Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('securityInformation')}</Text>
          
          <View style={styles.infoCard}>
            <Ionicons name="shield-checkmark" size={24} color={Colors.status.success} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('automaticLogout')}</Text>
              <Text style={styles.infoDescription}>
                {t('forYourSecurityYoullBeAutomaticallyLoggedOutAfterT')}
              </Text>
            </View>
          </View>

          <View style={styles.infoCard}>
            <Ionicons name="refresh" size={24} color={Colors.primary.main} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('activityReset')}</Text>
              <Text style={styles.infoDescription}>
                {t('anyAppInteractionWillResetTheTimeoutTimer')}
              </Text>
            </View>
          </View>

          <View style={styles.infoCard}>
            <Ionicons name="warning" size={24} color={Colors.accent.gold} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('securityRecommendation')}</Text>
              <Text style={styles.infoDescription}>
                {t('shorterTimeoutsProvideBetterSecurityEspeciallyOnSh')}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  currentSetting: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  currentSettingCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginBottom: 8,
  },
  currentIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  currentIconCompact: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary.main + '15',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  currentText: {
    flex: 1,
  },
  currentTextCompact: {
    flex: 1,
  },
  currentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  currentTitleCompact: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  currentValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  currentValueContainerCompact: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginRight: 8,
  },
  currentValueCompact: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginRight: 6,
  },
  updatingText: {
    fontSize: 12,
    color: Colors.accent.gold,
    fontWeight: '600',
  },
  updatingTextCompact: {
    fontSize: 10,
    color: Colors.accent.gold,
    fontWeight: '600',
  },
  updatingIndicator: {
    backgroundColor: Colors.accent.gold + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  currentDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  currentDescriptionCompact: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    lineHeight: 16,
    marginTop: 4,
  },
  statusIndicator: {
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  timeoutOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.neutral.lightGray,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  timeoutOptionCompact: {
    backgroundColor: Colors.neutral.white,
    padding: 14,
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    borderColor: Colors.neutral.lightGray,
    elevation: 1,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
  },
  selectedOption: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '10',
  },
  selectedOptionCompact: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '08',
    borderWidth: 2,
  },
  optionsGrid: {
    // Container for options - can be used for future grid layout
  },
  disabledOption: {
    opacity: 0.6,
    backgroundColor: Colors.neutral.lightGray,
  },
  optionLeft: {
    flex: 1,
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  optionTitleCompact: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    flex: 1,
  },
  selectedText: {
    color: Colors.primary.main,
  },
  selectedTextCompact: {
    color: Colors.primary.main,
  },
  optionHeaderCompact: {
    marginBottom: 6,
  },
  optionTitleRowCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  disabledText: {
    color: Colors.neutral.warmGray,
    opacity: 0.7,
  },
  optionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  optionDescriptionCompact: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    lineHeight: 16,
  },
  selectedDescriptionCompact: {
    color: Colors.primary.main + 'CC',
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.neutral.warmGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonCompact: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.neutral.warmGray,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedRadio: {
    borderColor: Colors.primary.main,
  },
  selectedRadioCompact: {
    borderColor: Colors.primary.main,
  },
  disabledRadio: {
    borderColor: Colors.neutral.warmGray,
    opacity: 0.5,
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.primary.main,
  },
  radioInnerCompact: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary.main,
  },
  loadingIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIndicatorCompact: {
    width: 10,
    height: 10,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 8,
  },
  loadingTextCompact: {
    fontSize: 6,
  },
  securityBadgeCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.lightGray,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  securityTextCompact: {
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 3,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
});

export default SessionTimeoutScreen;
