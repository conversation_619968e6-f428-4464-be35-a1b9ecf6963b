import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import networkService from '../services/networkService';

/**
 * Network Status Indicator Component
 * Shows connection status optimized for East African network conditions
 */
const NetworkStatusIndicator = ({ 
  showDetails = false, 
  style = {},
  onPress = null,
  compact = false 
}) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  const [networkStatus, setNetworkStatus] = useState(networkService.getNetworkStatus());
  const [showBanner, setShowBanner] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    // Subscribe to network changes
    const unsubscribe = networkService.addNetworkListener((status) => {
      const wasConnected = networkStatus.isConnected;
      setNetworkStatus(status);

      // Show banner for connection changes
      if (wasConnected !== status.isConnected) {
        setShowBanner(true);
        
        Animated.sequence([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.delay(3000),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => setShowBanner(false));
      }
    });

    return unsubscribe;
  }, [networkStatus.isConnected]);

  const getStatusConfig = () => {
    const { isConnected, connectionQuality, connectionType } = networkStatus;

    if (!isConnected) {
      return {
        icon: 'cloud-offline',
        color: Colors.status.error,
        text: 'Offline',
        description: 'Working offline with cached data',
        bgColor: Colors.status.error + '15'
      };
    }

    switch (connectionQuality) {
      case 'excellent':
        return {
          icon: 'wifi',
          color: Colors.status.success,
          text: 'Excellent',
          description: `Fast ${connectionType} connection`,
          bgColor: Colors.status.success + '15'
        };
      case 'good':
        return {
          icon: 'wifi',
          color: Colors.status.success,
          text: 'Good',
          description: `Stable ${connectionType} connection`,
          bgColor: Colors.status.success + '15'
        };
      case 'fair':
        return {
          icon: 'wifi',
          color: Colors.status.warning,
          text: 'Fair',
          description: `Slow ${connectionType} connection`,
          bgColor: Colors.status.warning + '15'
        };
      case 'poor':
        return {
          icon: 'cellular',
          color: Colors.status.warning,
          text: 'Poor',
          description: `Very slow ${connectionType} connection`,
          bgColor: Colors.status.warning + '15'
        };
      default:
        return {
          icon: 'help-circle',
          color: Colors.neutral.warmGray,
          text: 'Unknown',
          description: 'Connection status unknown',
          bgColor: Colors.neutral.warmGray + '15'
        };
    }
  };

  const statusConfig = getStatusConfig();

  const renderCompactIndicator = () => (
    <TouchableOpacity 
      style={[styles.compactContainer, { backgroundColor: statusConfig.bgColor }, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Ionicons 
        name={statusConfig.icon} 
        size={16} 
        color={statusConfig.color} 
      />
      {showDetails && (
        <Text style={[styles.compactText, { color: statusConfig.color }]}>
          {statusConfig.text}
        </Text>
      )}
    </TouchableOpacity>
  );

  const renderFullIndicator = () => (
    <TouchableOpacity 
      style={[styles.container, { backgroundColor: statusConfig.bgColor }, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        <Ionicons 
          name={statusConfig.icon} 
          size={20} 
          color={statusConfig.color} 
        />
      </View>
      
      <View style={styles.textContainer}>
        <Text style={[styles.statusText, { color: statusConfig.color }]}>
          {statusConfig.text}
        </Text>
        {showDetails && (
          <Text style={styles.descriptionText}>
            {statusConfig.description}
          </Text>
        )}
      </View>

      {networkStatus.isSlowConnection && (
        <View style={styles.warningContainer}>
          <Ionicons 
            name="time" 
            size={14} 
            color={Colors.status.warning} 
          />
        </View>
      )}
    </TouchableOpacity>
  );

  const renderConnectionBanner = () => {
    if (!showBanner) return null;

    const bannerConfig = networkStatus.isConnected 
      ? {
          icon: 'checkmark-circle',
          color: Colors.status.success,
          text: 'Connection restored',
          bgColor: Colors.status.success
        }
      : {
          icon: 'alert-circle',
          color: Colors.status.error,
          text: 'Connection lost - Working offline',
          bgColor: Colors.status.error
        };

    return (
      <Animated.View 
        style={[
          styles.banner, 
          { 
            backgroundColor: bannerConfig.bgColor,
            opacity: fadeAnim 
          }
        ]}
      >
        <Ionicons 
          name={bannerConfig.icon} 
          size={18} 
          color={Colors.neutral.white} 
        />
        <Text style={styles.bannerText}>
          {bannerConfig.text}
        </Text>
      </Animated.View>
    );
  };

  return (
    <>
      {compact ? renderCompactIndicator() : renderFullIndicator()}
      {renderConnectionBanner()}
    </>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  iconContainer: {
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  compactText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  descriptionText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  warningContainer: {
    marginLeft: 8,
  },
  banner: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    zIndex: 1000,
    elevation: 10,
  },
  bannerText: {
    color: Colors.neutral.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});

// Network Status Hook for easy integration
export const useNetworkStatus = () => {
  const [networkStatus, setNetworkStatus] = useState(networkService.getNetworkStatus());

  useEffect(() => {
    const unsubscribe = networkService.addNetworkListener(setNetworkStatus);
    return unsubscribe;
  }, []);

  return networkStatus;
};

// Network Quality Badge Component
export const NetworkQualityBadge = ({ style = {} }) => {
  const { theme } = useTheme();
  const networkStatus = useNetworkStatus();

  if (!networkStatus.isConnected) {
    return (
      <View style={[styles.qualityBadge, { backgroundColor: Colors.status.error + '20' }, style]}>
        <Text style={[styles.qualityText, { color: Colors.status.error }]}>
          Offline
        </Text>
      </View>
    );
  }

  const qualityColors = {
    excellent: Colors.status.success,
    good: Colors.status.success,
    fair: Colors.status.warning,
    poor: Colors.status.error
  };

  const color = qualityColors[networkStatus.connectionQuality] || Colors.neutral.warmGray;

  return (
    <View style={[styles.qualityBadge, { backgroundColor: color + '20' }, style]}>
      <Text style={[styles.qualityText, { color }]}>
        {networkStatus.connectionQuality}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  qualityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  qualityText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

export default NetworkStatusIndicator;
