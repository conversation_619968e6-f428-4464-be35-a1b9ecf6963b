/**
 * Comprehensive configuration for East African countries
 * Includes country codes, phone formats, network providers, and validation rules
 */

export const EAST_AFRICAN_COUNTRIES = {
  UG: {
    name: 'Uganda',
    code: '+256',
    flag: '🇺🇬',
    phoneLength: 10, // Local number length (without country code)
    phoneFormat: '+256XXXXXXXXXX',
    example: '777123456',
    networks: {
      MTN: {
        name: 'MTN Uganda',
        prefixes: ['77', '78', '76', '39'],
        color: '#FFCC00'
      },
      Airtel: {
        name: 'Airtel Uganda',
        prefixes: ['75', '70', '74', '20'],
        color: '#FF0000'
      },
      UTL: {
        name: 'Uganda Telecom',
        prefixes: ['71', '31'],
        color: '#0066CC'
      }
    }
  },
  KE: {
    name: 'Kenya',
    code: '+254',
    flag: '🇰🇪',
    phoneLength: 9,
    phoneFormat: '+254XXXXXXXXX',
    example: '712345678',
    networks: {
      Safaricom: {
        name: 'Safaricom',
        prefixes: ['70', '71', '72', '79'],
        color: '#00A651'
      },
      Airtel: {
        name: 'Airtel Kenya',
        prefixes: ['73', '78', '10', '11'],
        color: '#FF0000'
      },
      Telkom: {
        name: 'Telkom Kenya',
        prefixes: ['77'],
        color: '#0066CC'
      }
    }
  },
  TZ: {
    name: 'Tanzania',
    code: '+255',
    flag: '🇹🇿',
    phoneLength: 9,
    phoneFormat: '+255XXXXXXXXX',
    example: '712345678',
    networks: {
      Vodacom: {
        name: 'Vodacom Tanzania',
        prefixes: ['74', '75', '76'],
        color: '#FF0000'
      },
      Tigo: {
        name: 'Tigo Tanzania',
        prefixes: ['71', '65', '67'],
        color: '#0066CC'
      },
      Airtel: {
        name: 'Airtel Tanzania',
        prefixes: ['78', '68', '69'],
        color: '#FF0000'
      },
      Halotel: {
        name: 'Halotel',
        prefixes: ['62'],
        color: '#FF6600'
      }
    }
  },
  RW: {
    name: 'Rwanda',
    code: '+250',
    flag: '🇷🇼',
    phoneLength: 9,
    phoneFormat: '+250XXXXXXXXX',
    example: '781234567',
    networks: {
      MTN: {
        name: 'MTN Rwanda',
        prefixes: ['78', '79'],
        color: '#FFCC00'
      },
      Airtel: {
        name: 'Airtel Rwanda',
        prefixes: ['73', '72'],
        color: '#FF0000'
      }
    }
  },
  BI: {
    name: 'Burundi',
    code: '+257',
    flag: '🇧🇮',
    phoneLength: 8,
    phoneFormat: '+257XXXXXXXX',
    example: '79123456',
    networks: {
      Econet: {
        name: 'Econet Leo',
        prefixes: ['79', '77'],
        color: '#0066CC'
      },
      Smart: {
        name: 'Smart Mobile',
        prefixes: ['76', '71'],
        color: '#00A651'
      },
      Lumitel: {
        name: 'Lumitel',
        prefixes: ['68', '69'],
        color: '#FF6600'
      }
    }
  },
  SS: {
    name: 'South Sudan',
    code: '+211',
    flag: '🇸🇸',
    phoneLength: 9,
    phoneFormat: '+211XXXXXXXXX',
    example: '912345678',
    networks: {
      Zain: {
        name: 'Zain South Sudan',
        prefixes: ['91', '92', '95'],
        color: '#8B0000'
      },
      MTN: {
        name: 'MTN South Sudan',
        prefixes: ['97', '98'],
        color: '#FFCC00'
      }
    }
  },
  ET: {
    name: 'Ethiopia',
    code: '+251',
    flag: '🇪🇹',
    phoneLength: 9,
    phoneFormat: '+251XXXXXXXXX',
    example: '911234567',
    networks: {
      Ethio: {
        name: 'Ethio Telecom',
        prefixes: ['91', '92', '93', '94', '97'],
        color: '#FF6600'
      }
    }
  },
  SO: {
    name: 'Somalia',
    code: '+252',
    flag: '🇸🇴',
    phoneLength: 8,
    phoneFormat: '+252XXXXXXXX',
    example: '61234567',
    networks: {
      Hormuud: {
        name: 'Hormuud Telecom',
        prefixes: ['61', '62', '63'],
        color: '#0066CC'
      },
      Somtel: {
        name: 'Somtel',
        prefixes: ['90', '68'],
        color: '#00A651'
      },
      Telesom: {
        name: 'Telesom',
        prefixes: ['63', '65'],
        color: '#FF0000'
      }
    }
  },
  DJ: {
    name: 'Djibouti',
    code: '+253',
    flag: '🇩🇯',
    phoneLength: 8,
    phoneFormat: '+253XXXXXXXX',
    example: '77123456',
    networks: {
      Djibouti: {
        name: 'Djibouti Telecom',
        prefixes: ['77', '21'],
        color: '#0066CC'
      }
    }
  },
  ER: {
    name: 'Eritrea',
    code: '+291',
    flag: '🇪🇷',
    phoneLength: 7,
    phoneFormat: '+291XXXXXXX',
    example: '7123456',
    networks: {
      EriTel: {
        name: 'EriTel',
        prefixes: ['7', '1'],
        color: '#FF6600'
      }
    }
  }
};

/**
 * Get country configuration by country code
 * @param {string} countryCode - Two letter country code (e.g., 'UG', 'KE')
 * @returns {Object|null} - Country configuration object or null if not found
 */
export const getCountryByCode = (countryCode) => {
  return EAST_AFRICAN_COUNTRIES[countryCode] || null;
};

/**
 * Get country configuration by phone country code
 * @param {string} phoneCode - Phone country code (e.g., '+256', '+254')
 * @returns {Object|null} - Country configuration object or null if not found
 */
export const getCountryByPhoneCode = (phoneCode) => {
  const countryEntry = Object.entries(EAST_AFRICAN_COUNTRIES).find(
    ([_, country]) => country.code === phoneCode
  );
  return countryEntry ? countryEntry[1] : null;
};

/**
 * Get all countries as an array for dropdown/picker components
 * @returns {Array} - Array of country objects with additional countryCode property
 */
export const getCountriesArray = () => {
  return Object.entries(EAST_AFRICAN_COUNTRIES).map(([countryCode, country]) => ({
    ...country,
    countryCode
  }));
};

/**
 * Detect network provider for a given phone number and country
 * @param {string} phoneNumber - Local phone number (without country code)
 * @param {string} countryCode - Two letter country code
 * @returns {Object|null} - Network provider object or null if not found
 */
export const detectNetworkProvider = (phoneNumber, countryCode) => {
  const country = getCountryByCode(countryCode);
  if (!country) return null;

  // Clean phone number and get first 2 digits
  const cleaned = phoneNumber.replace(/\D/g, '');
  const prefix = cleaned.substring(0, 2);

  // Search through all networks for this country
  for (const [networkKey, network] of Object.entries(country.networks)) {
    if (network.prefixes.includes(prefix)) {
      return {
        ...network,
        key: networkKey
      };
    }
  }

  return null;
};

/**
 * Format phone number according to country-specific rules
 * @param {string} phoneNumber - Raw phone number input
 * @param {string} countryCode - Two letter country code
 * @returns {string} - Formatted phone number with country code
 */
export const formatPhoneNumber = (phoneNumber, countryCode) => {
  const country = getCountryByCode(countryCode);
  if (!country) return phoneNumber;

  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Handle different input formats
  let localNumber = cleaned;
  
  // Remove country code if present
  const countryCodeDigits = country.code.replace('+', '');
  if (cleaned.startsWith(countryCodeDigits)) {
    localNumber = cleaned.substring(countryCodeDigits.length);
  } else if (cleaned.startsWith('0')) {
    // Remove leading zero (common in local formats)
    localNumber = cleaned.substring(1);
  }

  // Limit to expected length
  localNumber = localNumber.substring(0, country.phoneLength);

  return country.code + localNumber;
};

/**
 * Validate phone number for a specific country
 * @param {string} phoneNumber - Phone number to validate
 * @param {string} countryCode - Two letter country code
 * @returns {Object} - Validation result with isValid boolean and error message
 */
export const validatePhoneNumber = (phoneNumber, countryCode) => {
  const country = getCountryByCode(countryCode);
  if (!country) {
    return { isValid: false, error: 'Invalid country selected' };
  }

  const cleaned = phoneNumber.replace(/\D/g, '');
  let localNumber = cleaned;

  // Remove country code if present
  const countryCodeDigits = country.code.replace('+', '');
  if (cleaned.startsWith(countryCodeDigits)) {
    localNumber = cleaned.substring(countryCodeDigits.length);
  } else if (cleaned.startsWith('0')) {
    localNumber = cleaned.substring(1);
  }

  if (localNumber.length < country.phoneLength - 1) {
    return { 
      isValid: false, 
      error: `Phone number too short for ${country.name}. Expected ${country.phoneLength} digits.` 
    };
  }

  if (localNumber.length > country.phoneLength) {
    return { 
      isValid: false, 
      error: `Phone number too long for ${country.name}. Expected ${country.phoneLength} digits.` 
    };
  }

  return { isValid: true, error: null };
};

export default EAST_AFRICAN_COUNTRIES;
