import { createClient } from '@supabase/supabase-js';
import secureConfig from '../config/secureEnvironment';

/**
 * Secure Supabase Client Configuration
 *
 * This client uses environment-specific configuration and validates
 * credentials before initializing the connection.
 *
 * SECURITY FEATURES:
 * - Environment-specific Supabase projects
 * - Credential validation
 * - Secure client configuration
 * - Production-ready settings
 */

// Get environment-specific Supabase configuration
const { url: supabaseUrl, anonKey: supabaseAnonKey } = secureConfig.supabase;

// Validate configuration before proceeding
const configErrors = secureConfig.validate();
if (configErrors.length > 0) {
  console.error('❌ Supabase configuration validation failed:');
  configErrors.forEach(error => console.error(`  - ${error}`));

  if (secureConfig.isProduction()) {
    throw new Error('Invalid Supabase configuration in production environment');
  }
}

// Log configuration status (without exposing sensitive data)
console.log(`🔧 Initializing Supabase client for ${secureConfig.environment} environment`);
console.log(`📍 Supabase URL: ${supabaseUrl ? supabaseUrl.substring(0, 30) + '...' : 'NOT SET'}`);
console.log(`🔑 Anon Key: ${supabaseAnonKey ? 'SET (' + supabaseAnonKey.length + ' chars)' : 'NOT SET'}`);

// Initialize the Supabase client with environment-specific configuration
const supabaseClientConfig = {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    flowType: 'pkce',
    // Production-specific auth settings
    ...(secureConfig.isProduction() && {
      storageKey: 'jiranipay-auth-token',
      storage: undefined, // Use default secure storage
    }),
  },

  // Global configuration
  global: {
    headers: {
      'X-Client-Info': `jiranipay-mobile-app-${secureConfig.environment}`,
      'X-Environment': secureConfig.environment,
    },
  },

  // Database configuration
  db: {
    schema: 'public',
  },

  // Real-time configuration
  realtime: {
    params: {
      eventsPerSecond: secureConfig.isProduction() ? 10 : 100,
    },
  },
};

// Create the Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey, supabaseClientConfig);

// Test connection (non-blocking)
const testConnection = async () => {
  try {
    const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is fine
      console.warn('⚠️ Supabase connection test failed:', error.message);
    } else {
      console.log('✅ Supabase connection test successful');
    }
  } catch (error) {
    console.warn('⚠️ Supabase connection test error:', error.message);
  }
};

// Run connection test in development
if (secureConfig.isDevelopment()) {
  testConnection();
}

console.log(`✅ Supabase client initialized successfully for ${secureConfig.environment}`);

// Export both default and named exports for compatibility
export { supabase };
export default supabase;