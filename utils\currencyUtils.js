/**
 * Currency Utilities for JiraniPay
 * Provides currency formatting and conversion functions for East African currencies
 */

// East African currency configurations
const CURRENCY_CONFIG = {
  UGX: {
    symbol: 'UGX',
    name: 'Ugandan <PERSON>lling',
    decimals: 0,
    symbolPosition: 'before', // UGX 1,000
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Uganda',
    flag: '🇺🇬'
  },
  KES: {
    symbol: 'KES',
    name: 'Kenyan Shilling',
    decimals: 2,
    symbolPosition: 'before', // KES 1,000.00
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Kenya',
    flag: '🇰🇪'
  },
  TZS: {
    symbol: 'TZS',
    name: 'Tanzanian <PERSON>lling',
    decimals: 0,
    symbolPosition: 'before', // TZS 1,000
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Tanzania',
    flag: '🇹🇿'
  },
  RWF: {
    symbol: 'RWF',
    name: 'Rwandan <PERSON>an<PERSON>',
    decimals: 0,
    symbolPosition: 'before', // RWF 1,000
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Rwanda',
    flag: '🇷🇼'
  },
  BIF: {
    symbol: 'BIF',
    name: '<PERSON>urundian Franc',
    decimals: 0,
    symbolPosition: 'before', // BIF 1,000
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Burundi',
    flag: '🇧🇮'
  },
  ETB: {
    symbol: 'ETB',
    name: 'Ethiopian Birr',
    decimals: 2,
    symbolPosition: 'before', // ETB 1,000.00
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'Ethiopia',
    flag: '🇪🇹'
  },
  USD: {
    symbol: '$',
    name: 'US Dollar',
    decimals: 2,
    symbolPosition: 'before', // $1,000.00
    thousandsSeparator: ',',
    decimalSeparator: '.',
    country: 'United States',
    flag: '🇺🇸'
  }
};

/**
 * Format currency amount with proper symbol and formatting
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: UGX)
 * @param {Object} options - Formatting options
 * @returns {string} - Formatted currency string
 */
export const formatCurrency = (amount, currency = 'UGX', options = {}) => {
  const config = CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.UGX;
  const {
    showSymbol = true,
    showDecimals = null,
    compact = false,
    symbolPosition = config.symbolPosition
  } = options;

  // Handle null/undefined amounts
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showSymbol ? `${config.symbol} 0` : '0';
  }

  const numAmount = parseFloat(amount);
  
  // Compact formatting for large numbers
  if (compact && numAmount >= 1000000) {
    const millions = numAmount / 1000000;
    const formatted = millions.toFixed(1);
    return showSymbol ? `${config.symbol} ${formatted}M` : `${formatted}M`;
  } else if (compact && numAmount >= 1000) {
    const thousands = numAmount / 1000;
    const formatted = thousands.toFixed(1);
    return showSymbol ? `${config.symbol} ${formatted}K` : `${formatted}K`;
  }

  // Determine decimal places
  const decimals = showDecimals !== null ? showDecimals : config.decimals;
  
  // Format the number
  const formattedNumber = numAmount.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true
  });

  // Return with or without symbol
  if (!showSymbol) {
    return formattedNumber;
  }

  return symbolPosition === 'before' 
    ? `${config.symbol} ${formattedNumber}`
    : `${formattedNumber} ${config.symbol}`;
};

/**
 * Format currency for display in lists (compact format)
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Compact formatted currency
 */
export const formatCurrencyCompact = (amount, currency = 'UGX') => {
  return formatCurrency(amount, currency, { compact: true });
};

/**
 * Format currency without symbol (numbers only)
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Formatted number without symbol
 */
export const formatAmount = (amount, currency = 'UGX') => {
  return formatCurrency(amount, currency, { showSymbol: false });
};

/**
 * Parse currency string to number
 * @param {string} currencyString - Currency string to parse
 * @returns {number} - Parsed amount
 */
export const parseCurrency = (currencyString) => {
  if (!currencyString) return 0;
  
  // Remove currency symbols and letters, keep only numbers, commas, and dots
  const cleanString = currencyString.replace(/[^\d,.-]/g, '');
  
  // Handle different decimal separators
  const normalizedString = cleanString.replace(/,/g, '');
  
  const parsed = parseFloat(normalizedString);
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Get currency configuration
 * @param {string} currency - Currency code
 * @returns {Object} - Currency configuration
 */
export const getCurrencyConfig = (currency) => {
  return CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.UGX;
};

/**
 * Get all supported currencies
 * @returns {Array} - Array of currency objects
 */
export const getSupportedCurrencies = () => {
  return Object.keys(CURRENCY_CONFIG).map(code => ({
    code,
    ...CURRENCY_CONFIG[code]
  }));
};

/**
 * Get East African currencies only
 * @returns {Array} - Array of East African currency objects
 */
export const getEastAfricanCurrencies = () => {
  const eastAfricanCodes = ['UGX', 'KES', 'TZS', 'RWF', 'BIF', 'ETB'];
  return eastAfricanCodes.map(code => ({
    code,
    ...CURRENCY_CONFIG[code]
  }));
};

/**
 * Check if currency is supported
 * @param {string} currency - Currency code to check
 * @returns {boolean} - True if supported
 */
export const isSupportedCurrency = (currency) => {
  return currency in CURRENCY_CONFIG;
};

/**
 * Format currency for input fields (no symbol, proper decimals)
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Formatted amount for input
 */
export const formatForInput = (amount, currency = 'UGX') => {
  const config = CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.UGX;
  
  if (amount === null || amount === undefined || isNaN(amount)) {
    return '';
  }

  const numAmount = parseFloat(amount);
  return numAmount.toFixed(config.decimals);
};

/**
 * Validate currency amount
 * @param {string|number} amount - Amount to validate
 * @param {string} currency - Currency code
 * @param {Object} options - Validation options
 * @returns {Object} - Validation result
 */
export const validateAmount = (amount, currency = 'UGX', options = {}) => {
  const { min = 0, max = Infinity } = options;
  
  const numAmount = typeof amount === 'string' ? parseCurrency(amount) : parseFloat(amount);
  
  if (isNaN(numAmount)) {
    return { isValid: false, error: 'Invalid amount format' };
  }
  
  if (numAmount < min) {
    return { isValid: false, error: `Amount must be at least ${formatCurrency(min, currency)}` };
  }
  
  if (numAmount > max) {
    return { isValid: false, error: `Amount cannot exceed ${formatCurrency(max, currency)}` };
  }
  
  return { isValid: true, amount: numAmount };
};

/**
 * Convert amount between currencies (placeholder for future exchange rate integration)
 * @param {number} amount - Amount to convert
 * @param {string} fromCurrency - Source currency
 * @param {string} toCurrency - Target currency
 * @returns {Object} - Conversion result
 */
export const convertCurrency = (amount, fromCurrency, toCurrency) => {
  // Placeholder for future exchange rate integration
  // For now, return the same amount with a note
  return {
    originalAmount: amount,
    convertedAmount: amount,
    fromCurrency,
    toCurrency,
    exchangeRate: 1,
    note: 'Exchange rate conversion not yet implemented'
  };
};

/**
 * Get currency symbol only
 * @param {string} currency - Currency code
 * @returns {string} - Currency symbol
 */
export const getCurrencySymbol = (currency) => {
  const config = CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.UGX;
  return config.symbol;
};

/**
 * Format currency for receipts and official documents
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code
 * @returns {string} - Formally formatted currency
 */
export const formatCurrencyFormal = (amount, currency = 'UGX') => {
  const config = CURRENCY_CONFIG[currency] || CURRENCY_CONFIG.UGX;
  const formatted = formatCurrency(amount, currency, { showDecimals: config.decimals });
  return `${formatted} (${config.name})`;
};
