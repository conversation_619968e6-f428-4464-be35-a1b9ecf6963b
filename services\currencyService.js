import AsyncStorage from '@react-native-async-storage/async-storage';

class CurrencyService {
  constructor() {
    this.isInitialized = false;
    this.exchangeRates = {};
    this.lastUpdated = null;
    this.baseCurrency = 'UGX';
    this.preferredCurrency = 'UGX';
    this.changeCallbacks = [];
    
    // East African currencies with comprehensive details
    this.currencies = {
      'UGX': {
        name: 'Ugandan Shilling',
        symbol: 'UGX',
        flag: '🇺🇬',
        country: 'Uganda',
        countryCode: 'UG',
        region: 'East Africa',
        decimals: 0,
        isBaseCurrency: true,
        priority: 1
      },
      'KES': {
        name: 'Kenyan Shilling',
        symbol: 'KSh',
        flag: '🇰🇪',
        country: 'Kenya',
        countryCode: 'KE',
        region: 'East Africa',
        decimals: 2,
        isBaseCurrency: false,
        priority: 2
      },
      'TZS': {
        name: 'Tanzanian Shilling',
        symbol: 'TSh',
        flag: '🇹🇿',
        country: 'Tanzania',
        countryCode: 'TZ',
        region: 'East Africa',
        decimals: 2,
        isBaseCurrency: false,
        priority: 3
      },
      'RWF': {
        name: 'Rwandan Franc',
        symbol: 'RWF',
        flag: '🇷🇼',
        country: 'Rwanda',
        countryCode: 'RW',
        region: 'East Africa',
        decimals: 0,
        isBaseCurrency: false,
        priority: 4
      },
      'BIF': {
        name: 'Burundian Franc',
        symbol: 'BIF',
        flag: '🇧🇮',
        country: 'Burundi',
        countryCode: 'BI',
        region: 'East Africa',
        decimals: 0,
        isBaseCurrency: false,
        priority: 5
      },
      'ETB': {
        name: 'Ethiopian Birr',
        symbol: 'ETB',
        flag: '🇪🇹',
        country: 'Ethiopia',
        countryCode: 'ET',
        region: 'East Africa',
        decimals: 2,
        isBaseCurrency: false,
        priority: 6
      },

    };
    
    // Mock exchange rates (in production, these would come from a real API)
    // All rates are relative to UGX (base currency = 1) - East Africa only
    this.mockExchangeRates = {
      'UGX': 1, // Base currency
      'KES': 0.32, // 1 UGX = 0.32 KES (approx 3100 UGX = 1000 KES)
      'TZS': 0.85, // 1 UGX = 0.85 TZS (approx 1200 UGX = 1000 TZS)
      'RWF': 0.37, // 1 UGX = 0.37 RWF (approx 2700 UGX = 1000 RWF)
      'BIF': 0.67, // 1 UGX = 0.67 BIF (approx 1500 UGX = 1000 BIF)
      'ETB': 0.015 // 1 UGX = 0.015 ETB (approx 67 UGX = 1 ETB)
    };

    // Initialize exchange rates immediately with mock data
    this.exchangeRates = { ...this.mockExchangeRates };
    this.lastUpdated = new Date();

    // User's preferred currency (can be changed in settings)
    this.userPreferredCurrency = 'UGX';

    // Currency change listeners
    this.currencyChangeListeners = [];
  }

  async initialize() {
    try {
      console.log('💱 Initializing currency service...');
      
      // Load stored exchange rates
      await this.loadExchangeRates();

      // Load preferred currency
      await this.loadPreferredCurrency();

      // Update rates if needed (older than 1 hour)
      const now = new Date();
      if (!this.lastUpdated || (now - this.lastUpdated) > 60 * 60 * 1000) {
        await this.updateExchangeRates();
      }
      
      this.isInitialized = true;
      console.log('✅ Currency service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing currency service:', error);
      // Use mock rates as fallback
      this.exchangeRates = { ...this.mockExchangeRates };
      this.lastUpdated = new Date();
      this.isInitialized = true;
      return { success: false, error: error.message };
    }
  }

  async loadExchangeRates() {
    try {
      const stored = await AsyncStorage.getItem('exchange_rates');
      const lastUpdatedStored = await AsyncStorage.getItem('exchange_rates_updated');
      
      if (stored && lastUpdatedStored) {
        this.exchangeRates = JSON.parse(stored);
        this.lastUpdated = new Date(lastUpdatedStored);
        console.log('💱 Loaded stored exchange rates');
      } else {
        // Use mock rates
        this.exchangeRates = { ...this.mockExchangeRates };
        this.lastUpdated = new Date();
      }
    } catch (error) {
      console.error('❌ Error loading exchange rates:', error);
      this.exchangeRates = { ...this.mockExchangeRates };
      this.lastUpdated = new Date();
    }
  }

  async saveExchangeRates() {
    try {
      await AsyncStorage.setItem('exchange_rates', JSON.stringify(this.exchangeRates));
      await AsyncStorage.setItem('exchange_rates_updated', this.lastUpdated.toISOString());
    } catch (error) {
      console.error('❌ Error saving exchange rates:', error);
    }
  }

  async updateExchangeRates() {
    try {
      console.log('💱 Updating exchange rates...');
      
      // In production, this would fetch from a real API
      // For now, we'll simulate rate fluctuations
      const fluctuation = () => 0.95 + (Math.random() * 0.1); // ±5% fluctuation
      
      this.exchangeRates = {
        'UGX': 1,
        'KES': this.mockExchangeRates.KES * fluctuation(),
        'TZS': this.mockExchangeRates.TZS * fluctuation(),
        'RWF': this.mockExchangeRates.RWF * fluctuation(),
        'BIF': this.mockExchangeRates.BIF * fluctuation(),
        'ETB': this.mockExchangeRates.ETB * fluctuation()
      };
      
      this.lastUpdated = new Date();
      await this.saveExchangeRates();
      
      console.log('✅ Exchange rates updated');
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating exchange rates:', error);
      return { success: false, error: error.message };
    }
  }

  convert(amount, fromCurrency, toCurrency) {
    if (!this.isInitialized) {
      console.warn('⚠️ Currency service not initialized');
      return amount;
    }
    
    if (fromCurrency === toCurrency) {
      return amount;
    }
    
    const fromRate = this.exchangeRates[fromCurrency];
    const toRate = this.exchangeRates[toCurrency];
    
    if (!fromRate || !toRate) {
      console.warn('⚠️ Exchange rate not found for', fromCurrency, 'or', toCurrency);
      return amount;
    }
    
    // Convert to base currency (UGX) first, then to target currency
    const baseAmount = amount / fromRate;
    const convertedAmount = baseAmount * toRate;
    
    return convertedAmount;
  }

  format(amount, currency = 'UGX', options = {}) {
    const currencyInfo = this.currencies[currency];
    if (!currencyInfo) {
      return `${currency} ${amount.toLocaleString()}`;
    }

    const {
      showSymbol = true,
      showFlag = false,
      decimals = currencyInfo.decimals
    } = options;

    const formattedAmount = decimals > 0
      ? amount.toFixed(decimals)
      : Math.round(amount).toLocaleString();

    let result = '';

    if (showFlag) {
      result += `${currencyInfo.flag} `;
    }

    if (showSymbol) {
      result += `${currencyInfo.symbol} `;
    }

    result += formattedAmount;

    return result;
  }

  // Alias for format method to maintain compatibility
  formatCurrency(amount, currency = 'UGX', options = {}) {
    return this.format(amount, currency, options);
  }

  formatWithConversion(amount, fromCurrency, toCurrency, options = {}) {
    const convertedAmount = this.convert(amount, fromCurrency, toCurrency);
    return this.format(convertedAmount, toCurrency, options);
  }

  getCurrencyInfo(currency) {
    return this.currencies[currency] || null;
  }

  getSupportedCurrencies() {
    // Return only East African currencies
    return this.getEastAfricanCurrencies();
  }

  getEastAfricanCurrencies() {
    const eastAfricanCodes = ['UGX', 'KES', 'TZS', 'RWF', 'BIF', 'ETB'];
    const result = eastAfricanCodes.map(code => ({
      code,
      ...this.currencies[code]
    }));
    console.log('🌍 getEastAfricanCurrencies returning:', result.length, 'currencies');
    console.log('🌍 Currency codes:', result.map(c => c.code));
    return result;
  }

  getExchangeRate(fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) {
      return 1;
    }
    
    const fromRate = this.exchangeRates[fromCurrency];
    const toRate = this.exchangeRates[toCurrency];
    
    if (!fromRate || !toRate) {
      return null;
    }
    
    return toRate / fromRate;
  }

  async getUserPreferredCurrency() {
    try {
      const stored = await AsyncStorage.getItem('preferred_currency');
      return stored || 'UGX';
    } catch (error) {
      console.error('❌ Error getting preferred currency:', error);
      return 'UGX';
    }
  }

  async setUserPreferredCurrency(currency) {
    try {
      if (!this.currencies[currency]) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      const oldCurrency = this.userPreferredCurrency;
      this.userPreferredCurrency = currency;

      await AsyncStorage.setItem('preferred_currency', currency);

      // Notify listeners of currency change
      this.notifyCurrencyChangeListeners(currency, oldCurrency);

      console.log(`✅ Preferred currency updated: ${oldCurrency} → ${currency}`);
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting preferred currency:', error);
      return { success: false, error: error.message };
    }
  }

  // Currency change listener management
  addCurrencyChangeListener(callback) {
    this.currencyChangeListeners.push(callback);
    return () => {
      this.currencyChangeListeners = this.currencyChangeListeners.filter(cb => cb !== callback);
    };
  }

  notifyCurrencyChangeListeners(newCurrency) {
    this.currencyChangeListeners.forEach(callback => {
      try {
        callback(newCurrency);
      } catch (error) {
        console.error('❌ Error in currency change listener:', error);
      }
    });
  }

  // Get currency for specific country
  getCurrencyForCountry(countryCode) {
    return Object.entries(this.currencies).find(([code, currency]) =>
      currency.countryCode === countryCode
    )?.[0] || 'UGX';
  }

  // Get currencies by region
  getCurrenciesByRegion(region = 'East Africa') {
    return Object.entries(this.currencies)
      .filter(([code, currency]) => currency.region === region)
      .sort((a, b) => a[1].priority - b[1].priority)
      .map(([code, currency]) => ({
        code,
        ...currency
      }));
  }

  // Format amount with proper currency display
  formatAmountWithCurrency(amount, currency = null, options = {}) {
    const targetCurrency = currency || this.userPreferredCurrency;
    const currencyInfo = this.currencies[targetCurrency];

    if (!currencyInfo) {
      return `${targetCurrency} ${amount.toLocaleString()}`;
    }

    const {
      showSymbol = true,
      showFlag = false,
      showCode = true,
      compact = false
    } = options;

    const formattedAmount = currencyInfo.decimals > 0
      ? amount.toFixed(currencyInfo.decimals)
      : Math.round(amount).toLocaleString();

    let result = '';

    if (showFlag && !compact) {
      result += `${currencyInfo.flag} `;
    }

    if (showSymbol) {
      result += `${currencyInfo.symbol} `;
    } else if (showCode) {
      result += `${targetCurrency} `;
    }

    result += formattedAmount;

    return result;
  }

  // Convert and format in one step
  convertAndFormat(amount, fromCurrency, toCurrency = null, options = {}) {
    const targetCurrency = toCurrency || this.userPreferredCurrency;
    const convertedAmount = this.convert(amount, fromCurrency, targetCurrency);
    return this.formatAmountWithCurrency(convertedAmount, targetCurrency, options);
  }

  // Convert from UGX (base currency) to user's preferred currency and format
  convertFromUGXAndFormat(ugxAmount, targetCurrency = null, options = {}) {
    const target = targetCurrency || this.userPreferredCurrency;
    return this.convertAndFormat(ugxAmount, 'UGX', target, options);
  }

  // Get formatted exchange rate string
  getFormattedExchangeRate(fromCurrency, toCurrency) {
    const rate = this.getExchangeRateBetween(fromCurrency, toCurrency);
    if (!rate) return 'Rate unavailable';

    const fromInfo = this.currencies[fromCurrency];
    const toInfo = this.currencies[toCurrency];

    if (!fromInfo || !toInfo) return 'Rate unavailable';

    return `1 ${fromInfo.symbol} = ${rate.toFixed(toInfo.decimals)} ${toInfo.symbol}`;
  }

  // Get exchange rate between two currencies
  getExchangeRateBetween(fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) return 1;

    const fromRate = this.exchangeRates[fromCurrency];
    const toRate = this.exchangeRates[toCurrency];

    if (!fromRate || !toRate) return null;

    return toRate / fromRate;
  }

  // Check if currency is supported
  isCurrencySupported(currency) {
    return !!this.currencies[currency];
  }

  // Get currency by country code
  getCurrencyByCountry(countryCode) {
    return Object.values(this.currencies).find(currency =>
      currency.countryCode === countryCode
    );
  }

  getLastUpdated() {
    return this.lastUpdated;
  }

  isRateStale() {
    if (!this.lastUpdated) return true;
    const now = new Date();
    return (now - this.lastUpdated) > 60 * 60 * 1000; // 1 hour
  }

  // Get all exchange rates
  getAllExchangeRates() {
    if (!this.isInitialized) {
      console.warn('⚠️ Currency service not initialized, using mock rates');
      return { ...this.mockExchangeRates };
    }
    return { ...this.exchangeRates };
  }

  // Mock method for simulating real-time rate updates
  simulateRateUpdate() {
    if (Math.random() < 0.1) { // 10% chance
      this.updateExchangeRates();
    }
  }

  // Get currency conversion summary for display - East Africa only
  getConversionSummary(amount, fromCurrency, targetCurrencies = ['KES', 'TZS', 'RWF', 'BIF', 'ETB']) {
    return targetCurrencies.map(toCurrency => {
      const convertedAmount = this.convert(amount, fromCurrency, toCurrency);
      const rate = this.getExchangeRate(fromCurrency, toCurrency);
      
      return {
        currency: toCurrency,
        amount: convertedAmount,
        formatted: this.format(convertedAmount, toCurrency),
        rate,
        ...this.currencies[toCurrency]
      };
    });
  }

  // ===== PREFERENCE MANAGEMENT METHODS =====

  async loadPreferredCurrency() {
    try {
      const savedCurrency = await AsyncStorage.getItem('preferredCurrency');
      if (savedCurrency && this.currencies[savedCurrency]) {
        this.preferredCurrency = savedCurrency;
        console.log('✅ Loaded preferred currency:', savedCurrency);
      }
    } catch (error) {
      console.error('❌ Error loading preferred currency:', error);
    }
  }

  async setPreferredCurrency(currency) {
    try {
      if (!this.currencies[currency]) {
        throw new Error(`Unsupported currency: ${currency}`);
      }

      const oldCurrency = this.preferredCurrency;
      this.preferredCurrency = currency;

      // Save to storage
      await AsyncStorage.setItem('preferredCurrency', currency);

      // Notify listeners
      this.notifyPreferenceChange(oldCurrency, currency);

      console.log('✅ Preferred currency updated to:', currency);
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting preferred currency:', error);
      return { success: false, error: error.message };
    }
  }

  getPreferredCurrency() {
    return this.preferredCurrency;
  }

  getPreferredCurrencyInfo() {
    return this.currencies[this.preferredCurrency];
  }

  onPreferenceChange(callback) {
    this.changeCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      const index = this.changeCallbacks.indexOf(callback);
      if (index > -1) {
        this.changeCallbacks.splice(index, 1);
      }
    };
  }

  notifyPreferenceChange(oldCurrency, newCurrency) {
    this.changeCallbacks.forEach(callback => {
      try {
        callback(oldCurrency, newCurrency);
      } catch (error) {
        console.error('❌ Error in preference change callback:', error);
      }
    });
  }

  // Format amount in preferred currency
  formatInPreferredCurrency(amount, fromCurrency = 'UGX') {
    const convertedAmount = this.convert(amount, fromCurrency, this.preferredCurrency);
    return this.formatCurrency(convertedAmount, this.preferredCurrency);
  }

  // Get all available currencies for preference selection
  getAvailableCurrencies() {
    return Object.keys(this.currencies).map(code => ({
      code,
      ...this.currencies[code]
    })).sort((a, b) => a.priority - b.priority);
  }
}

// Create and export singleton instance
const currencyService = new CurrencyService();
export default currencyService;
