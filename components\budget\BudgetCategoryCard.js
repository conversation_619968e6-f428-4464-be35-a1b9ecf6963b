/**
 * Budget Category Card Component
 * Displays individual budget category with spending progress
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';

// Constants
import { Colors } from '../../constants/Colors';

const BudgetCategoryCard = ({ 
  category, 
  onPress, 
  theme, 
  formatCurrency, 
  isPreview = false,
  showProgress = true 
}) => {
  // Calculate utilization
  const allocated = category.allocated_amount || category.suggestedAmount || 0;
  const spent = category.spent_amount || 0;
  const utilization = allocated > 0 ? spent / allocated : 0;
  const utilizationPercentage = Math.min(utilization * 100, 100);

  // Get status color
  const getStatusColor = () => {
    if (utilization > 1) return Colors.status.error;
    if (utilization > 0.8) return Colors.status.warning;
    return Colors.status.success;
  };

  // Get category icon
  const getCategoryIcon = () => {
    return category.icon || 'folder';
  };

  // Get category color
  const getCategoryColor = () => {
    return category.color || Colors.primary.main;
  };

  const Container = onPress ? TouchableOpacity : View;

  return (
    <Container
      style={[
        styles.container,
        { backgroundColor: theme.colors.surface },
        isPreview && styles.previewContainer
      ]}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <View style={[styles.iconContainer, { backgroundColor: getCategoryColor() + '20' }]}>
            <Ionicons 
              name={getCategoryIcon()} 
              size={20} 
              color={getCategoryColor()} 
            />
          </View>
          <View style={styles.categoryInfo}>
            <Text style={[styles.categoryName, { color: theme.colors.text }]}>
              {category.category_name || category.categoryName || category.name}
            </Text>
            {category.priority && (
              <View style={styles.priorityContainer}>
                <Text style={[styles.priorityText, { color: theme.colors.textSecondary }]}>
                  Priority: {category.priority === 3 ? 'High' : category.priority === 2 ? 'Medium' : 'Low'}
                </Text>
                {category.is_essential && (
                  <View style={[styles.essentialBadge, { backgroundColor: Colors.status.success + '20' }]}>
                    <Text style={[styles.essentialText, { color: Colors.status.success }]}>
                      Essential
                    </Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </View>
        {onPress && (
          <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
        )}
      </View>

      {/* Amount Information */}
      <View style={styles.amountContainer}>
        <View style={styles.amountRow}>
          <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
            {isPreview ? 'Suggested' : 'Allocated'}
          </Text>
          <Text style={[styles.amountValue, { color: theme.colors.text }]}>
            {formatCurrency(allocated, 'UGX')}
          </Text>
        </View>
        
        {!isPreview && (
          <View style={styles.amountRow}>
            <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
              Spent
            </Text>
            <Text style={[styles.amountValue, { color: getStatusColor() }]}>
              {formatCurrency(spent, 'UGX')}
            </Text>
          </View>
        )}

        {!isPreview && allocated > 0 && (
          <View style={styles.amountRow}>
            <Text style={[styles.amountLabel, { color: theme.colors.textSecondary }]}>
              Remaining
            </Text>
            <Text style={[
              styles.amountValue, 
              { color: (allocated - spent) >= 0 ? Colors.status.success : Colors.status.error }
            ]}>
              {formatCurrencyValue(allocated - spent, 'UGX')}
            </Text>
          </View>
        )}
      </View>

      {/* Progress Bar */}
      {showProgress && !isPreview && allocated > 0 && (
        <View style={styles.progressContainer}>
          <View style={styles.progressHeader}>
            <Text style={[styles.progressLabel, { color: theme.colors.text }]}>
              Usage
            </Text>
            <Text style={[styles.progressPercentage, { color: getStatusColor() }]}>
              {utilizationPercentage.toFixed(1)}%
            </Text>
          </View>
          <View style={[styles.progressTrack, { backgroundColor: theme.colors.border }]}>
            <LinearGradient
              colors={[getStatusColor(), getStatusColor() + '80']}
              style={[
                styles.progressFill,
                { width: `${Math.min(utilizationPercentage, 100)}%` }
              ]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          </View>
        </View>
      )}

      {/* Preview Information */}
      {isPreview && category.percentage && (
        <View style={styles.previewInfo}>
          <Text style={[styles.previewText, { color: theme.colors.textSecondary }]}>
            {category.percentage.toFixed(1)}% of total budget
          </Text>
          {category.historicalAmount && (
            <Text style={[styles.previewText, { color: theme.colors.textSecondary }]}>
              Historical: {formatCurrency(category.historicalAmount, 'UGX')}
            </Text>
          )}
        </View>
      )}

      {/* Status Indicator */}
      {!isPreview && (
        <View style={styles.statusContainer}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor() }]} />
          <Text style={[styles.statusText, { color: getStatusColor() }]}>
            {utilization > 1 ? 'Over Budget' : 
             utilization > 0.8 ? 'On Track' : 
             utilization > 0 ? 'Under Budget' : 'No Spending'}
          </Text>
        </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  previewContainer: {
    marginBottom: 8,
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  priorityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  priorityText: {
    fontSize: 12,
  },
  essentialBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  essentialText: {
    fontSize: 10,
    fontWeight: '600',
  },
  amountContainer: {
    marginBottom: 12,
  },
  amountRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  amountLabel: {
    fontSize: 14,
  },
  amountValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  progressContainer: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '600',
  },
  progressTrack: {
    height: 6,
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  previewInfo: {
    marginTop: 8,
  },
  previewText: {
    fontSize: 12,
    marginBottom: 2,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
});

export default BudgetCategoryCard;
