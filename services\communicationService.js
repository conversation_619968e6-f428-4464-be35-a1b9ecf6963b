/**
 * Unified Communication Service for JiraniPay
 * Consolidates SMS and Email functionality into a single service
 * Handles 2FA, notifications, and all communication needs
 */

import { supabase } from './supabaseClient';
import { isProductionMode } from '../config/environment';
import * as SecureStore from 'expo-secure-store';

class CommunicationService {
  constructor() {
    this.providers = {
      sms: {
        twilio: {
          name: '<PERSON>wi<PERSON>',
          baseUrl: 'https://api.twilio.com/2010-04-01',
          supported: true
        },
        messagebird: {
          name: 'MessageBird',
          baseUrl: 'https://rest.messagebird.com',
          supported: true
        }
      },
      email: {
        sendgrid: {
          name: 'SendGrid',
          apiUrl: 'https://api.sendgrid.com/v3/mail/send',
          supported: true
        },
        aws_ses: {
          name: 'AWS SES',
          supported: true
        }
      }
    };

    this.config = {
      sms: {
        maxRetries: 3,
        retryDelay: 2000,
        codeExpiry: 300, // 5 minutes
        rateLimitWindow: 60, // 1 minute
        maxCodesPerWindow: 3
      },
      email: {
        fromEmail: '<EMAIL>',
        fromName: 'JiraniPay',
        codeExpiry: 300, // 5 minutes
        rateLimitWindow: 60, // 1 minute
        maxEmailsPerWindow: 3
      }
    };

    this.templates = {
      sms: {
        '2fa': 'Your JiraniPay verification code is: {code}. Valid for 5 minutes. Do not share this code.',
        'welcome': 'Welcome to JiraniPay! Your account has been created successfully.',
        'transaction': 'Transaction alert: {amount} {currency} {type}. Balance: {balance}. Ref: {reference}'
      },
      email: {
        '2fa': {
          subject: 'JiraniPay Verification Code',
          template: 'verification_code'
        },
        'welcome': {
          subject: 'Welcome to JiraniPay',
          template: 'welcome'
        },
        'transaction': {
          subject: 'Transaction Notification',
          template: 'transaction_alert'
        }
      }
    };
  }

  /**
   * Send 2FA code via SMS
   */
  async sendSMS2FA(phoneNumber, userId, purpose = '2FA') {
    try {
      console.log('📱 Sending SMS 2FA code to:', phoneNumber);

      // Generate secure 6-digit code
      const code = this.generateSecureCode();
      const expiresAt = new Date(Date.now() + this.config.sms.codeExpiry * 1000);

      // Check rate limiting
      const rateLimitCheck = await this.checkSMSRateLimit(phoneNumber);
      if (!rateLimitCheck.allowed) {
        return { success: false, error: 'RATE_LIMITED', message: rateLimitCheck.message };
      }

      // Send SMS
      const smsResult = await this.sendSMSMessage(phoneNumber, code, purpose);
      
      if (smsResult.success) {
        // Store verification code
        await this.storeVerificationCode(phoneNumber, code, 'sms', expiresAt, userId);
        
        return {
          success: true,
          message: 'SMS sent successfully',
          codeId: smsResult.messageId,
          expiresAt
        };
      }

      return smsResult;
    } catch (error) {
      console.error('❌ Error sending SMS 2FA:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send 2FA code via Email
   */
  async sendEmail2FA(email, userId, purpose = '2FA') {
    try {
      console.log('📧 Sending Email 2FA code to:', email);

      // Generate secure 6-digit code
      const code = this.generateSecureCode();
      const expiresAt = new Date(Date.now() + this.config.email.codeExpiry * 1000);

      // Check rate limiting
      const rateLimitCheck = await this.checkEmailRateLimit(email);
      if (!rateLimitCheck.allowed) {
        return { success: false, error: 'RATE_LIMITED', message: rateLimitCheck.message };
      }

      // Send email
      const emailResult = await this.sendEmailMessage(email, code, purpose);
      
      if (emailResult.success) {
        // Store verification code
        await this.storeVerificationCode(email, code, 'email', expiresAt, userId);
        
        return {
          success: true,
          message: 'Email sent successfully',
          messageId: emailResult.messageId,
          expiresAt
        };
      }

      return emailResult;
    } catch (error) {
      console.error('❌ Error sending Email 2FA:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send SMS message using configured provider
   */
  async sendSMSMessage(phoneNumber, code, purpose) {
    try {
      if (isProductionMode()) {
        return await this.sendProductionSMS(phoneNumber, code, purpose);
      } else {
        return await this.sendDevelopmentSMS(phoneNumber, code, purpose);
      }
    } catch (error) {
      console.error('❌ SMS sending error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send email message using configured provider
   */
  async sendEmailMessage(email, code, purpose) {
    try {
      if (isProductionMode()) {
        return await this.sendProductionEmail(email, code, purpose);
      } else {
        return await this.sendDevelopmentEmail(email, code, purpose);
      }
    } catch (error) {
      console.error('❌ Email sending error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send production SMS via Twilio
   */
  async sendProductionSMS(phoneNumber, code, purpose) {
    try {
      const accountSid = process.env.TWILIO_ACCOUNT_SID;
      const authToken = process.env.TWILIO_AUTH_TOKEN;
      const messagingServiceSid = process.env.TWILIO_MESSAGING_SERVICE_SID;

      if (!accountSid || !authToken) {
        throw new Error('Twilio credentials not configured');
      }

      const message = this.templates.sms[purpose]?.replace('{code}', code) || 
                     `Your JiraniPay verification code is: ${code}`;

      const response = await fetch(`https://api.twilio.com/2010-04-01/Accounts/${accountSid}/Messages.json`, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(`${accountSid}:${authToken}`).toString('base64')}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          To: phoneNumber,
          MessagingServiceSid: messagingServiceSid,
          Body: message
        })
      });

      const result = await response.json();

      if (response.ok) {
        return {
          success: true,
          messageId: result.sid,
          status: result.status
        };
      } else {
        throw new Error(result.message || 'SMS sending failed');
      }
    } catch (error) {
      console.error('❌ Production SMS error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send production email via SendGrid
   */
  async sendProductionEmail(email, code, purpose) {
    try {
      const apiKey = process.env.SENDGRID_API_KEY;
      
      if (!apiKey) {
        throw new Error('SendGrid API key not configured');
      }

      const emailData = {
        personalizations: [{
          to: [{ email }],
          subject: this.templates.email[purpose]?.subject || 'JiraniPay Verification Code'
        }],
        from: {
          email: this.config.email.fromEmail,
          name: this.config.email.fromName
        },
        content: [{
          type: 'text/html',
          value: this.generateEmailHTML(code, purpose)
        }]
      };

      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailData)
      });

      if (response.ok) {
        return {
          success: true,
          messageId: response.headers.get('x-message-id'),
          status: 'sent'
        };
      } else {
        const error = await response.json();
        throw new Error(error.errors?.[0]?.message || 'Email sending failed');
      }
    } catch (error) {
      console.error('❌ Production email error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Development mode SMS (console logging)
   */
  async sendDevelopmentSMS(phoneNumber, code, purpose) {
    console.log('🔧 Development SMS:', {
      to: phoneNumber,
      code,
      purpose,
      message: this.templates.sms[purpose]?.replace('{code}', code)
    });

    return {
      success: true,
      messageId: `dev_sms_${Date.now()}`,
      status: 'development'
    };
  }

  /**
   * Development mode Email (console logging)
   */
  async sendDevelopmentEmail(email, code, purpose) {
    console.log('🔧 Development Email:', {
      to: email,
      code,
      purpose,
      subject: this.templates.email[purpose]?.subject
    });

    return {
      success: true,
      messageId: `dev_email_${Date.now()}`,
      status: 'development'
    };
  }

  /**
   * Generate secure 6-digit verification code
   */
  generateSecureCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Store verification code in database
   */
  async storeVerificationCode(contact, code, type, expiresAt, userId) {
    try {
      const { error } = await supabase
        .from('verification_codes')
        .insert({
          contact,
          code,
          type,
          expires_at: expiresAt.toISOString(),
          user_id: userId,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
      return { success: true };
    } catch (error) {
      console.error('❌ Error storing verification code:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check SMS rate limiting
   */
  async checkSMSRateLimit(phoneNumber) {
    // Implementation for SMS rate limiting
    return { allowed: true };
  }

  /**
   * Check Email rate limiting
   */
  async checkEmailRateLimit(email) {
    // Implementation for Email rate limiting
    return { allowed: true };
  }

  /**
   * Generate HTML email content
   */
  generateEmailHTML(code, purpose) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #FF6B35;">JiraniPay Verification</h2>
        <p>Your verification code is:</p>
        <div style="background: #f5f5f5; padding: 20px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px;">
          ${code}
        </div>
        <p>This code will expire in 5 minutes.</p>
        <p>If you didn't request this code, please ignore this email.</p>
      </div>
    `;
  }

  /**
   * Send notification (SMS or Email based on user preference)
   */
  async sendNotification(userId, message, type = 'info', channels = ['sms']) {
    try {
      const results = [];

      for (const channel of channels) {
        if (channel === 'sms') {
          // Get user phone number and send SMS
          const smsResult = await this.sendNotificationSMS(userId, message, type);
          results.push({ channel: 'sms', ...smsResult });
        } else if (channel === 'email') {
          // Get user email and send email
          const emailResult = await this.sendNotificationEmail(userId, message, type);
          results.push({ channel: 'email', ...emailResult });
        }
      }

      return {
        success: results.some(r => r.success),
        results
      };
    } catch (error) {
      console.error('❌ Error sending notification:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      sms: {
        provider: 'twilio',
        available: !!process.env.TWILIO_ACCOUNT_SID,
        mode: isProductionMode() ? 'production' : 'development'
      },
      email: {
        provider: 'sendgrid',
        available: !!process.env.SENDGRID_API_KEY,
        mode: isProductionMode() ? 'production' : 'development'
      }
    };
  }
}

// Create and export singleton instance
const communicationService = new CommunicationService();
export default communicationService;
