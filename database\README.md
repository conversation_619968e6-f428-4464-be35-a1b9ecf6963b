# JiraniPay Database Setup Guide

This guide will help you set up the complete database schema for JiraniPay in your Supabase project.

## 🚀 Quick Setup

### Step 1: Access Supabase SQL Editor
1. Go to your Supabase project dashboard
2. Navigate to the **SQL Editor** tab
3. Create a new query

### Step 2: Run Migration Scripts
Execute the following SQL files **in order**:

#### 1. Core Tables (Required)
```sql
-- Copy and paste the contents of: migrations/001_create_core_tables.sql
```
This creates:
- `user_profiles` table
- `wallets` table  
- `transactions` table
- Indexes for performance
- Update triggers

#### 2. Wallet Functions (Required)
```sql
-- Copy and paste the contents of: migrations/002_create_wallet_functions.sql
```
This creates:
- `create_user_wallet_safe()` function
- `get_wallet_balance()` function
- `create_transaction()` function

#### 3. Security Policies (Recommended)
```sql
-- Copy and paste the contents of: migrations/003_create_rls_policies.sql
```
This creates:
- Row Level Security (RLS) policies
- User permissions
- Auto-wallet creation trigger

## 🔧 Manual Setup Instructions

### Option 1: Copy-Paste Method (Recommended)
1. Open `migrations/001_create_core_tables.sql`
2. Copy all contents
3. Paste into Supabase SQL Editor
4. Click "Run"
5. Repeat for files 002 and 003

### Option 2: One-by-One Table Creation
If you prefer to create tables manually:

```sql
-- 1. Create user_profiles table
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    phone_number TEXT UNIQUE,
    email TEXT,
    -- ... (see full schema in migration file)
);

-- 2. Create wallets table
CREATE TABLE public.wallets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'UGX',
    -- ... (see full schema in migration file)
);

-- 3. Create transactions table
CREATE TABLE public.transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    wallet_id UUID REFERENCES public.wallets(id) ON DELETE CASCADE,
    transaction_type TEXT CHECK (transaction_type IN ('deposit', 'withdrawal', 'transfer', 'payment', 'refund')),
    amount DECIMAL(15,2) NOT NULL,
    -- ... (see full schema in migration file)
);
```

## ✅ Verification

After running the migrations, verify the setup:

### 1. Check Tables Exist
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_profiles', 'wallets', 'transactions');
```

### 2. Test Wallet Function
```sql
SELECT create_user_wallet_safe(
    '00000000-0000-0000-0000-000000000000'::UUID,
    '+************'
);
```

### 3. Check RLS Policies
```sql
SELECT schemaname, tablename, policyname 
FROM pg_policies 
WHERE schemaname = 'public';
```

## 🔄 Fallback Behavior

The JiraniPay app is designed to work even if the database tables don't exist:

- **Missing Tables**: App automatically falls back to mock data
- **Connection Issues**: Graceful degradation to offline mode
- **Function Errors**: Mock wallet creation and transactions

## 🛠️ Troubleshooting

### Error: "relation public.wallets does not exist"
**Solution**: Run migration script `001_create_core_tables.sql`

### Error: "function create_user_wallet_safe does not exist"
**Solution**: Run migration script `002_create_wallet_functions.sql`

### Error: "permission denied for table wallets"
**Solution**: Run migration script `003_create_rls_policies.sql`

### Error: "invalid input syntax for type uuid"
**Solution**: Ensure user is properly authenticated and user ID is valid UUID

## 📊 Sample Data (Optional)

To add sample data for testing:

```sql
-- Create a test user profile (replace user_id with actual auth.users ID)
INSERT INTO public.user_profiles (
    user_id,
    full_name,
    phone_number,
    email,
    country
) VALUES (
    'your-user-id-here',
    'Test User',
    '+************',
    '<EMAIL>',
    'UG'
);

-- Create wallet for test user
SELECT create_user_wallet_safe(
    'your-user-id-here'::UUID,
    '+************'
);

-- Add sample transactions
SELECT create_transaction(
    'your-user-id-here'::UUID,
    'deposit',
    50000.00,
    'Initial deposit'
);
```

## 🔐 Security Notes

- RLS policies ensure users can only access their own data
- All functions use `SECURITY DEFINER` for controlled access
- Phone numbers and emails are unique to prevent duplicates
- Wallet balances use DECIMAL for precise financial calculations

## 📞 Support

If you encounter issues:
1. Check the Supabase logs in your dashboard
2. Verify your Supabase credentials in `.env.development`
3. Ensure you have the correct permissions in your Supabase project
4. The app will work with mock data even if database setup fails
