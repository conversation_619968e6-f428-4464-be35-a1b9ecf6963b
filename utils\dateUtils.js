/**
 * Date Utilities for JiraniPay
 * Provides date formatting and relative time functions
 */

/**
 * Format a date as relative time (e.g., "2 hours ago", "Yesterday")
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted relative time string
 */
export const formatRelativeTime = (date) => {
  const now = new Date();
  const targetDate = new Date(date);
  const diffTime = Math.abs(now - targetDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  const diffMinutes = Math.ceil(diffTime / (1000 * 60));

  // Future dates
  if (targetDate > now) {
    if (diffMinutes < 60) {
      return `in ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`;
    } else if (diffHours < 24) {
      return `in ${diffHours} hour${diffHours !== 1 ? 's' : ''}`;
    } else if (diffDays === 1) {
      return 'Tomorrow';
    } else if (diffDays <= 7) {
      return `in ${diffDays} day${diffDays !== 1 ? 's' : ''}`;
    } else {
      return targetDate.toLocaleDateString('en-UG', { 
        month: 'short', 
        day: 'numeric',
        year: targetDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
      });
    }
  }

  // Past dates
  if (diffMinutes < 1) {
    return 'Just now';
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays <= 7) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  } else {
    return targetDate.toLocaleDateString('en-UG', { 
      month: 'short', 
      day: 'numeric',
      year: targetDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
};

/**
 * Format a date for display (e.g., "Jan 15, 2024")
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date string
 */
export const formatDate = (date) => {
  const targetDate = new Date(date);
  return targetDate.toLocaleDateString('en-UG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Format a date and time for display (e.g., "Jan 15, 2024 at 3:30 PM")
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date and time string
 */
export const formatDateTime = (date) => {
  const targetDate = new Date(date);
  return targetDate.toLocaleDateString('en-UG', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Format time only (e.g., "3:30 PM")
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted time string
 */
export const formatTime = (date) => {
  const targetDate = new Date(date);
  return targetDate.toLocaleTimeString('en-UG', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Check if a date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if the date is today
 */
export const isToday = (date) => {
  const today = new Date();
  const targetDate = new Date(date);
  
  return today.getDate() === targetDate.getDate() &&
         today.getMonth() === targetDate.getMonth() &&
         today.getFullYear() === targetDate.getFullYear();
};

/**
 * Check if a date is yesterday
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if the date is yesterday
 */
export const isYesterday = (date) => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const targetDate = new Date(date);
  
  return yesterday.getDate() === targetDate.getDate() &&
         yesterday.getMonth() === targetDate.getMonth() &&
         yesterday.getFullYear() === targetDate.getFullYear();
};

/**
 * Get the start of day for a date
 * @param {Date|string} date - Date to process
 * @returns {Date} - Start of day
 */
export const getStartOfDay = (date) => {
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);
  return targetDate;
};

/**
 * Get the end of day for a date
 * @param {Date|string} date - Date to process
 * @returns {Date} - End of day
 */
export const getEndOfDay = (date) => {
  const targetDate = new Date(date);
  targetDate.setHours(23, 59, 59, 999);
  return targetDate;
};

/**
 * Add days to a date
 * @param {Date|string} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} - New date with days added
 */
export const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Get the difference in days between two dates
 * @param {Date|string} date1 - First date
 * @param {Date|string} date2 - Second date
 * @returns {number} - Difference in days
 */
export const getDaysDifference = (date1, date2) => {
  const firstDate = new Date(date1);
  const secondDate = new Date(date2);
  const diffTime = Math.abs(secondDate - firstDate);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Check if a date is in the past
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if the date is in the past
 */
export const isPast = (date) => {
  return new Date(date) < new Date();
};

/**
 * Check if a date is in the future
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if the date is in the future
 */
export const isFuture = (date) => {
  return new Date(date) > new Date();
};

/**
 * Format a date for API requests (ISO string)
 * @param {Date|string} date - Date to format
 * @returns {string} - ISO formatted date string
 */
export const formatForAPI = (date) => {
  return new Date(date).toISOString();
};

/**
 * Parse a date string safely
 * @param {string} dateString - Date string to parse
 * @returns {Date|null} - Parsed date or null if invalid
 */
export const parseDate = (dateString) => {
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    return null;
  }
};
