/**
 * AI Assistant Accuracy Test
 * Tests the improved AI knowledge base and response accuracy
 */

import enhancedAIKnowledgeBase from '../services/enhancedAIKnowledgeBase';
import codebaseContextService from '../services/codebaseContextService';
import aiChatService from '../services/aiChatService';

class AIAccuracyTest {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.totalTests = 0;
  }

  // Test cases that were previously failing
  getAccuracyTestCases() {
    return [
      {
        query: "How do I send money to another JiraniPay user?",
        expectedType: 'faq_match',
        expectedAnswer: 'To send money: 1) Go to the Send Money section',
        description: 'Should match exact FAQ question'
      },
      {
        query: "Can I withdraw money in my wallet",
        expectedType: 'faq_match', 
        expectedAnswer: 'Yes, you can withdraw to: Your linked bank account',
        description: 'Should match withdraw FAQ with slight wording difference'
      },
      {
        query: "What should I do if I suspect fraud?",
        expectedType: 'faq_match',
        expectedAnswer: 'Immediately: 1) Freeze your account in the app',
        description: 'Should match fraud FAQ exactly'
      },
      {
        query: "How to transfer money",
        expectedType: 'faq_match',
        expectedAnswer: 'To send money: 1) Go to the Send Money section',
        description: 'Should match send money FAQ with different wording'
      },
      {
        query: "What are the limits for sending money?",
        expectedType: 'faq_match',
        expectedAnswer: 'Daily limits depend on your verification level',
        description: 'Should match transfer limits FAQ'
      },
      {
        query: "How long does it take to send money?",
        expectedType: 'faq_match',
        expectedAnswer: 'JiraniPay to JiraniPay transfers are instant',
        description: 'Should match transfer time FAQ'
      },
      {
        query: "I want to send cash to my friend",
        expectedType: 'faq_match',
        expectedAnswer: 'To send money: 1) Go to the Send Money section',
        description: 'Should match send money with casual language'
      },
      {
        query: "How do I get money out of my wallet?",
        expectedType: 'faq_match',
        expectedAnswer: 'Yes, you can withdraw to: Your linked bank account',
        description: 'Should match withdraw FAQ with casual language'
      }
    ];
  }

  async runAccuracyTests() {
    console.log('🧪 Running AI Accuracy Tests...\n');
    
    const testCases = this.getAccuracyTestCases();
    
    for (const testCase of testCases) {
      await this.runSingleAccuracyTest(testCase);
    }
    
    this.printResults();
  }

  async runSingleAccuracyTest(testCase) {
    this.totalTests++;
    
    try {
      // Test the enhanced knowledge base directly
      const userContext = codebaseContextService.analyzeUserContext(testCase.query);
      const contextualResponse = enhancedAIKnowledgeBase.getContextualResponse(testCase.query, userContext.intent);
      
      // Test the full AI chat service
      const aiResponse = await aiChatService.generateAIResponse(testCase.query);
      
      // Validate response type
      const typeMatches = contextualResponse.type === testCase.expectedType;
      
      // Validate content accuracy
      let contentMatches = false;
      if (contextualResponse.content && testCase.expectedAnswer) {
        const responseText = contextualResponse.content.answer || contextualResponse.content.text || '';
        contentMatches = responseText.includes(testCase.expectedAnswer.substring(0, 30));
      }
      
      // Validate AI service response
      let aiResponseValid = false;
      if (aiResponse && aiResponse.text) {
        aiResponseValid = aiResponse.text.includes(testCase.expectedAnswer.substring(0, 30));
      }
      
      const testPassed = typeMatches && (contentMatches || aiResponseValid);
      
      if (testPassed) {
        this.passedTests++;
        console.log(`✅ PASS: ${testCase.description}`);
      } else {
        console.log(`❌ FAIL: ${testCase.description}`);
        console.log(`   Query: "${testCase.query}"`);
        console.log(`   Expected Type: ${testCase.expectedType}, Got: ${contextualResponse.type}`);
        console.log(`   Expected Answer: "${testCase.expectedAnswer.substring(0, 50)}..."`);
        console.log(`   Got Answer: "${(contextualResponse.content?.answer || aiResponse?.text || 'No response').substring(0, 50)}..."`);
        console.log(`   Relevance Score: ${contextualResponse.content?.relevance || 'N/A'}`);
      }
      
      this.testResults.push({
        query: testCase.query,
        description: testCase.description,
        passed: testPassed,
        responseType: contextualResponse.type,
        relevanceScore: contextualResponse.content?.relevance || 0
      });
      
    } catch (error) {
      console.log(`❌ ERROR: ${testCase.description} - ${error.message}`);
      this.testResults.push({
        query: testCase.query,
        description: testCase.description,
        passed: false,
        error: error.message
      });
    }
  }

  printResults() {
    console.log('\n📊 AI Accuracy Test Results:');
    console.log('=' .repeat(50));
    console.log(`Total Tests: ${this.totalTests}`);
    console.log(`Passed: ${this.passedTests}`);
    console.log(`Failed: ${this.totalTests - this.passedTests}`);
    console.log(`Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
    
    if (this.passedTests === this.totalTests) {
      console.log('\n🎉 All accuracy tests passed! AI knowledge base is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Review the failed cases above.');
      
      // Show failed tests summary
      const failedTests = this.testResults.filter(test => !test.passed);
      console.log('\n❌ Failed Tests Summary:');
      failedTests.forEach(test => {
        console.log(`   • ${test.description}`);
        console.log(`     Query: "${test.query}"`);
        if (test.error) {
          console.log(`     Error: ${test.error}`);
        }
      });
    }
  }

  // Test relevance scoring improvements
  testRelevanceScoring() {
    console.log('\n🎯 Testing Relevance Scoring...\n');
    
    const scoringTests = [
      {
        query: "How do I send money to another JiraniPay user?",
        expectedHighScore: true,
        description: "Exact FAQ match should get high score"
      },
      {
        query: "send money",
        expectedHighScore: true,
        description: "Key phrase should get good score"
      },
      {
        query: "money",
        expectedHighScore: false,
        description: "Single generic word should get lower score"
      },
      {
        query: "How to transfer funds to friend",
        expectedHighScore: true,
        description: "Synonym matching should work"
      }
    ];
    
    scoringTests.forEach(test => {
      const faqs = enhancedAIKnowledgeBase.findRelevantFAQ(test.query);
      const topScore = faqs.length > 0 ? faqs[0].relevance : 0;
      
      const scoreIsHigh = topScore > 0.6;
      const testPassed = test.expectedHighScore === scoreIsHigh;
      
      console.log(`${testPassed ? '✅' : '❌'} ${test.description}`);
      console.log(`   Query: "${test.query}"`);
      console.log(`   Top Score: ${topScore.toFixed(2)}`);
      console.log(`   Expected High: ${test.expectedHighScore}, Got High: ${scoreIsHigh}\n`);
    });
  }
}

// Export for use in other test files
export default AIAccuracyTest;

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new AIAccuracyTest();
  
  console.log('🚀 Starting AI Assistant Accuracy Tests...\n');
  
  // Test relevance scoring
  tester.testRelevanceScoring();
  
  // Test overall accuracy
  tester.runAccuracyTests().then(() => {
    console.log('\n✅ All tests completed!');
  }).catch(error => {
    console.error('❌ Test execution failed:', error);
  });
}
