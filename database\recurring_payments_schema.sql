-- =====================================================
-- RECURRING PAYMENTS DATABASE SCHEMA
-- Comprehensive schema for recurring bill payments
-- with proper relationships, indexes, and RLS policies
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- =====================================================
-- RECURRING BILL PAYMENTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS recurring_bill_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biller_id UUID NOT NULL REFERENCES billers(id) ON DELETE RESTRICT,
    
    -- Payment Details
    name VARCHAR(255) NOT NULL,
    account_number VARCHAR(100) NOT NULL,
    account_name VARCHAR(255),
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    
    -- Schedule Configuration
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    interval_value INTEGER NOT NULL DEFAULT 1 CHECK (interval_value > 0),
    day_of_month INTEGER CHECK (day_of_month BETWEEN 1 AND 31),
    day_of_week INTEGER CHECK (day_of_week BETWEEN 0 AND 6), -- 0 = Sunday
    
    -- Date Management
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    next_payment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    last_payment_date TIMESTAMP WITH TIME ZONE,
    
    -- Payment Method
    payment_method VARCHAR(50) NOT NULL DEFAULT 'wallet',
    
    -- Status Management
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_paused BOOLEAN NOT NULL DEFAULT false,
    pause_until TIMESTAMP WITH TIME ZONE,
    pause_reason VARCHAR(100),
    
    -- Limits and Controls
    max_amount DECIMAL(15,2),
    max_payments INTEGER,
    
    -- Tracking
    payment_count INTEGER NOT NULL DEFAULT 0,
    failed_attempts INTEGER NOT NULL DEFAULT 0,
    
    -- Reminders
    reminder_enabled BOOLEAN NOT NULL DEFAULT true,
    reminder_days_before INTEGER NOT NULL DEFAULT 1 CHECK (reminder_days_before >= 0),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- RECURRING PAYMENT EXECUTIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS recurring_payment_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recurring_payment_id UUID NOT NULL REFERENCES recurring_bill_payments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biller_id UUID NOT NULL REFERENCES billers(id) ON DELETE RESTRICT,
    
    -- Payment Details
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'UGX',
    account_number VARCHAR(100) NOT NULL,
    payment_method VARCHAR(50) NOT NULL,
    
    -- Execution Details
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    executed_date TIMESTAMP WITH TIME ZONE,
    completed_date TIMESTAMP WITH TIME ZONE,
    
    -- Status Tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'retrying', 'cancelled')),
    attempt_number INTEGER NOT NULL DEFAULT 1,
    
    -- Payment Reference
    payment_id UUID REFERENCES bill_payments(id),
    payment_reference VARCHAR(100),
    
    -- Error Handling
    error_message TEXT,
    error_code VARCHAR(50),
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- RECURRING PAYMENT NOTIFICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS recurring_payment_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    recurring_payment_id UUID NOT NULL REFERENCES recurring_bill_payments(id) ON DELETE CASCADE,
    
    -- Notification Details
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN (
        'reminder', 'success', 'failure', 'retry', 'paused', 'resumed', 'cancelled', 'created', 'updated'
    )),
    payment_date TIMESTAMP WITH TIME ZONE,
    
    -- Delivery Status
    sent_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Notification Content
    title VARCHAR(255),
    content TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Recurring Bill Payments Indexes
CREATE INDEX IF NOT EXISTS idx_recurring_payments_user_id ON recurring_bill_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_biller_id ON recurring_bill_payments(biller_id);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_next_payment ON recurring_bill_payments(next_payment_date) WHERE is_active = true AND is_paused = false;
CREATE INDEX IF NOT EXISTS idx_recurring_payments_status ON recurring_bill_payments(is_active, is_paused);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_frequency ON recurring_bill_payments(frequency);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_reminders ON recurring_bill_payments(reminder_enabled, next_payment_date) WHERE is_active = true AND is_paused = false;

-- Recurring Payment Executions Indexes
CREATE INDEX IF NOT EXISTS idx_executions_recurring_payment ON recurring_payment_executions(recurring_payment_id);
CREATE INDEX IF NOT EXISTS idx_executions_user_id ON recurring_payment_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_executions_status ON recurring_payment_executions(status);
CREATE INDEX IF NOT EXISTS idx_executions_scheduled_date ON recurring_payment_executions(scheduled_date);
CREATE INDEX IF NOT EXISTS idx_executions_payment_id ON recurring_payment_executions(payment_id);

-- Recurring Payment Notifications Indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON recurring_payment_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_recurring_payment ON recurring_payment_notifications(recurring_payment_id);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON recurring_payment_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_notifications_sent_at ON recurring_payment_notifications(sent_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE recurring_bill_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_payment_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE recurring_payment_notifications ENABLE ROW LEVEL SECURITY;

-- Recurring Bill Payments Policies
CREATE POLICY "Users can view their own recurring payments" ON recurring_bill_payments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own recurring payments" ON recurring_bill_payments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own recurring payments" ON recurring_bill_payments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own recurring payments" ON recurring_bill_payments
    FOR DELETE USING (auth.uid() = user_id);

-- Recurring Payment Executions Policies
CREATE POLICY "Users can view their own payment executions" ON recurring_payment_executions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create payment executions" ON recurring_payment_executions
    FOR INSERT WITH CHECK (true); -- System service creates these

CREATE POLICY "System can update payment executions" ON recurring_payment_executions
    FOR UPDATE USING (true); -- System service updates these

-- Recurring Payment Notifications Policies
CREATE POLICY "Users can view their own notifications" ON recurring_payment_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications" ON recurring_payment_notifications
    FOR INSERT WITH CHECK (true); -- System service creates these

CREATE POLICY "Users can update their notification read status" ON recurring_payment_notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_recurring_payments_updated_at 
    BEFORE UPDATE ON recurring_bill_payments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_executions_updated_at 
    BEFORE UPDATE ON recurring_payment_executions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STORED PROCEDURES AND FUNCTIONS
-- =====================================================

-- Function to get due payments for processing
CREATE OR REPLACE FUNCTION get_due_recurring_payments(
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    biller_id UUID,
    amount DECIMAL,
    currency VARCHAR,
    account_number VARCHAR,
    payment_method VARCHAR,
    next_payment_date TIMESTAMP WITH TIME ZONE,
    biller_name VARCHAR,
    biller_available BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rp.id,
        rp.user_id,
        rp.biller_id,
        rp.amount,
        rp.currency,
        rp.account_number,
        rp.payment_method,
        rp.next_payment_date,
        b.display_name as biller_name,
        (b.is_available AND NOT b.maintenance_mode) as biller_available
    FROM recurring_bill_payments rp
    JOIN billers b ON rp.biller_id = b.id
    WHERE rp.is_active = true
        AND rp.is_paused = false
        AND rp.next_payment_date <= NOW()
        AND (rp.end_date IS NULL OR rp.end_date > NOW())
        AND (rp.max_payments IS NULL OR rp.payment_count < rp.max_payments)
        AND b.is_available = true
        AND b.maintenance_mode = false
    ORDER BY rp.next_payment_date ASC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate next payment date
CREATE OR REPLACE FUNCTION calculate_next_payment_date(
    p_current_date TIMESTAMP WITH TIME ZONE,
    p_frequency VARCHAR,
    p_interval_value INTEGER DEFAULT 1
)
RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
    next_date TIMESTAMP WITH TIME ZONE;
BEGIN
    CASE p_frequency
        WHEN 'daily' THEN
            next_date := p_current_date + (p_interval_value || ' days')::INTERVAL;
        WHEN 'weekly' THEN
            next_date := p_current_date + (p_interval_value * 7 || ' days')::INTERVAL;
        WHEN 'monthly' THEN
            next_date := p_current_date + (p_interval_value || ' months')::INTERVAL;
        WHEN 'quarterly' THEN
            next_date := p_current_date + (p_interval_value * 3 || ' months')::INTERVAL;
        WHEN 'yearly' THEN
            next_date := p_current_date + (p_interval_value || ' years')::INTERVAL;
        ELSE
            next_date := p_current_date + '1 month'::INTERVAL; -- Default to monthly
    END CASE;
    
    RETURN next_date;
END;
$$ LANGUAGE plpgsql;

-- Function to update payment after successful execution
CREATE OR REPLACE FUNCTION update_recurring_payment_after_success(
    p_recurring_payment_id UUID,
    p_payment_id UUID,
    p_payment_reference VARCHAR
)
RETURNS VOID AS $$
DECLARE
    payment_record RECORD;
    next_date TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get the recurring payment record
    SELECT * INTO payment_record 
    FROM recurring_bill_payments 
    WHERE id = p_recurring_payment_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Recurring payment not found: %', p_recurring_payment_id;
    END IF;
    
    -- Calculate next payment date
    next_date := calculate_next_payment_date(
        payment_record.next_payment_date,
        payment_record.frequency,
        payment_record.interval_value
    );
    
    -- Update the recurring payment
    UPDATE recurring_bill_payments
    SET 
        last_payment_date = NOW(),
        next_payment_date = next_date,
        payment_count = payment_count + 1,
        failed_attempts = 0,
        updated_at = NOW()
    WHERE id = p_recurring_payment_id;
    
    -- Update the execution record
    UPDATE recurring_payment_executions
    SET 
        status = 'completed',
        payment_id = p_payment_id,
        payment_reference = p_payment_reference,
        completed_date = NOW(),
        updated_at = NOW()
    WHERE recurring_payment_id = p_recurring_payment_id
        AND status = 'processing';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- CRON JOBS FOR AUTOMATED PROCESSING
-- =====================================================

-- Schedule recurring payment processing every hour
-- Note: This requires pg_cron extension and appropriate permissions
-- SELECT cron.schedule('process-recurring-payments', '0 * * * *', 'SELECT process_due_recurring_payments();');

-- Schedule reminder processing every 6 hours
-- SELECT cron.schedule('send-payment-reminders', '0 */6 * * *', 'SELECT send_recurring_payment_reminders();');

-- =====================================================
-- VIEWS FOR REPORTING AND ANALYTICS
-- =====================================================

-- View for recurring payment summary
CREATE OR REPLACE VIEW recurring_payments_summary AS
SELECT 
    rp.user_id,
    COUNT(*) as total_recurring_payments,
    COUNT(*) FILTER (WHERE rp.is_active = true AND rp.is_paused = false) as active_payments,
    COUNT(*) FILTER (WHERE rp.is_paused = true) as paused_payments,
    SUM(rp.amount) FILTER (WHERE rp.is_active = true AND rp.is_paused = false) as total_monthly_amount,
    AVG(rp.amount) as average_payment_amount,
    MIN(rp.next_payment_date) FILTER (WHERE rp.is_active = true AND rp.is_paused = false) as next_payment_due
FROM recurring_bill_payments rp
GROUP BY rp.user_id;

-- View for payment execution analytics
CREATE OR REPLACE VIEW payment_execution_analytics AS
SELECT 
    DATE_TRUNC('month', rpe.scheduled_date) as month,
    COUNT(*) as total_executions,
    COUNT(*) FILTER (WHERE rpe.status = 'completed') as successful_executions,
    COUNT(*) FILTER (WHERE rpe.status = 'failed') as failed_executions,
    ROUND(
        COUNT(*) FILTER (WHERE rpe.status = 'completed')::DECIMAL / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as success_rate,
    SUM(rpe.amount) FILTER (WHERE rpe.status = 'completed') as total_amount_processed
FROM recurring_payment_executions rpe
GROUP BY DATE_TRUNC('month', rpe.scheduled_date)
ORDER BY month DESC;

-- =====================================================
-- GRANTS AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON recurring_bill_payments TO authenticated;
GRANT SELECT ON recurring_payment_executions TO authenticated;
GRANT SELECT, UPDATE ON recurring_payment_notifications TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- INITIAL DATA AND CONFIGURATION
-- =====================================================

-- Insert default configuration if needed
-- This can be customized based on business requirements

COMMENT ON TABLE recurring_bill_payments IS 'Stores recurring bill payment schedules and configurations';
COMMENT ON TABLE recurring_payment_executions IS 'Tracks individual executions of recurring payments';
COMMENT ON TABLE recurring_payment_notifications IS 'Logs notifications sent for recurring payment events';

-- =====================================================
-- END OF SCHEMA
-- =====================================================
