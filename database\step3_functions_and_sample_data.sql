-- =====================================================
-- JiraniPay Database Setup - Step 3: Functions and Sample Data
-- =====================================================
-- Run this script AFTER step2_indexes_and_security.sql
-- =====================================================

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to relevant tables
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON public.user_preferences;
CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_accounts_updated_at ON public.payment_accounts;
CREATE TRIGGER update_payment_accounts_updated_at BEFORE UPDATE ON public.payment_accounts
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_transactions_updated_at ON public.transactions;
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Function to generate transaction reference numbers
CREATE OR REPLACE FUNCTION public.generate_transaction_reference()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.reference_number IS NULL THEN
        NEW.reference_number := 'JP' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(EXTRACT(EPOCH FROM NOW())::TEXT, 10, '0') || LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add reference number generation trigger
DROP TRIGGER IF EXISTS generate_transaction_reference_trigger ON public.transactions;
CREATE TRIGGER generate_transaction_reference_trigger BEFORE INSERT ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.generate_transaction_reference();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Clear existing sample data
DELETE FROM public.bill_providers WHERE name LIKE '%Sample%' OR name LIKE '%Test%';

-- Insert sample bill providers for East African countries
INSERT INTO public.bill_providers (name, category, country_code, is_active, supported_currencies, min_amount, max_amount) VALUES
-- Uganda
('Umeme (Electricity)', 'utilities', 'UG', true, ARRAY['UGX'], 5000, 5000000),
('National Water and Sewerage Corporation', 'utilities', 'UG', true, ARRAY['UGX'], 2000, 2000000),
('MTN Uganda', 'telecom', 'UG', true, ARRAY['UGX'], 500, 500000),
('Airtel Uganda', 'telecom', 'UG', true, ARRAY['UGX'], 500, 500000),
('DSTV Uganda', 'tv', 'UG', true, ARRAY['UGX'], 15000, 200000),

-- Kenya
('Kenya Power (KPLC)', 'utilities', 'KE', true, ARRAY['KES'], 100, 100000),
('Nairobi Water', 'utilities', 'KE', true, ARRAY['KES'], 200, 50000),
('Safaricom', 'telecom', 'KE', true, ARRAY['KES'], 10, 10000),
('Airtel Kenya', 'telecom', 'KE', true, ARRAY['KES'], 10, 10000),

-- Tanzania
('TANESCO (Electricity)', 'utilities', 'TZ', true, ARRAY['TZS'], 1000, 1000000),
('DAWASCO (Water)', 'utilities', 'TZ', true, ARRAY['TZS'], 2000, 500000),
('Vodacom Tanzania', 'telecom', 'TZ', true, ARRAY['TZS'], 500, 200000),
('Airtel Tanzania', 'telecom', 'TZ', true, ARRAY['TZS'], 500, 200000),

-- Rwanda
('EUCL (Electricity)', 'utilities', 'RW', true, ARRAY['RWF'], 1000, 500000),
('WASAC (Water)', 'utilities', 'RW', true, ARRAY['RWF'], 500, 200000),
('MTN Rwanda', 'telecom', 'RW', true, ARRAY['RWF'], 100, 100000),
('Airtel Rwanda', 'telecom', 'RW', true, ARRAY['RWF'], 100, 100000)
ON CONFLICT DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check that all tables exist
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('user_profiles', 'user_preferences', 'payment_accounts', 'transactions', 'bill_providers')
ORDER BY tablename;

-- Check sample data
SELECT 
    'Bill Providers' as table_name,
    COUNT(*) as record_count,
    COUNT(DISTINCT country_code) as countries_count
FROM public.bill_providers;

-- Final success message
SELECT 
    '✅ JiraniPay Database Setup Complete!' as status,
    'All tables, indexes, security policies, and sample data created successfully.' as message,
    'Your app is now ready for production use!' as next_step;
