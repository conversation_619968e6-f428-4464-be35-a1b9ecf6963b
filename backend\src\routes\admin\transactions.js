/**
 * Admin Transaction Management Routes
 * Transaction monitoring, management, and analytics for administrators
 */

const express = require('express');
const { query, param, body, validationResult } = require('express-validator');
const { asyncHandler, ValidationError, NotFoundError } = require('../../middleware/errorHandler');
const { adminAuthMiddleware, requirePermission } = require('../../middleware/adminAuth');
const databaseService = require('../../services/database');
const transactionService = require('../../services/transactionService');
const auditService = require('../../services/auditService');
const currencyService = require('../../services/currencyService');
const logger = require('../../utils/logger');

const router = express.Router();

// Apply admin authentication to all routes
router.use(adminAuthMiddleware);

/**
 * @route   GET /api/v1/admin/transactions
 * @desc    Get all transactions with filtering and pagination
 * @access  Admin
 */
router.get('/', [
  requirePermission('transactions:read'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('type').optional().isString().withMessage('Type must be a string'),
  query('status').optional().isString().withMessage('Status must be a string'),
  query('startDate').optional().isISO8601().withMessage('Valid start date is required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date is required'),
  query('minAmount').optional().isFloat({ min: 0 }).withMessage('Minimum amount must be positive'),
  query('maxAmount').optional().isFloat({ min: 0 }).withMessage('Maximum amount must be positive'),
  query('currency').optional().isString().withMessage('Currency must be a string'),
  query('sortBy').optional().isIn(['created_at', 'amount', 'status']).withMessage('Invalid sort field'),
  query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('Sort order must be asc or desc')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      startDate,
      endDate,
      minAmount,
      maxAmount,
      currency,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;
    const supabase = databaseService.getSupabase();

    // Build query
    let query = supabase
      .from('transactions')
      .select(`
        *,
        from_user:user_profiles!from_user_id(full_name, email, phone_number),
        to_user:user_profiles!to_user_id(full_name, email, phone_number)
      `)
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (type) {
      query = query.eq('type', type);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (startDate) {
      query = query.gte('created_at', startDate);
    }

    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    if (minAmount) {
      query = query.gte('amount', minAmount);
    }

    if (maxAmount) {
      query = query.lte('amount', maxAmount);
    }

    if (currency) {
      query = query.eq('currency', currency);
    }

    const { data: transactions, error } = await query;

    if (error) {
      logger.error('Failed to fetch transactions:', error);
      throw new Error('Failed to retrieve transactions');
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true });

    // Apply same filters to count query
    if (type) countQuery = countQuery.eq('type', type);
    if (status) countQuery = countQuery.eq('status', status);
    if (startDate) countQuery = countQuery.gte('created_at', startDate);
    if (endDate) countQuery = countQuery.lte('created_at', endDate);
    if (minAmount) countQuery = countQuery.gte('amount', minAmount);
    if (maxAmount) countQuery = countQuery.lte('amount', maxAmount);
    if (currency) countQuery = countQuery.eq('currency', currency);

    const { count, error: countError } = await countQuery;

    if (countError) {
      logger.error('Failed to get transaction count:', countError);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    // Format transactions for response
    const formattedTransactions = transactions.map(tx => ({
      id: tx.id,
      reference: tx.transaction_reference,
      type: tx.type,
      amount: tx.amount,
      currency: tx.currency,
      formatted: currencyService.formatCurrency(tx.amount, tx.currency),
      status: tx.status,
      description: tx.description,
      fromUser: tx.from_user ? {
        name: tx.from_user.full_name,
        email: tx.from_user.email,
        phone: tx.from_user.phone_number
      } : null,
      toUser: tx.to_user ? {
        name: tx.to_user.full_name,
        email: tx.to_user.email,
        phone: tx.to_user.phone_number
      } : null,
      createdAt: tx.created_at,
      completedAt: tx.completed_at,
      failedAt: tx.failed_at
    }));

    // Log admin action
    await auditService.logAdminAction(
      'view',
      req.admin.id,
      null,
      'transactions',
      'list',
      { page, limit, filters: { type, status, startDate, endDate } }
    );

    res.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count || 0,
          pages: totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        filters: {
          type,
          status,
          startDate,
          endDate,
          minAmount,
          maxAmount,
          currency
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get transactions:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/transactions/:transactionId
 * @desc    Get detailed transaction information
 * @access  Admin
 */
router.get('/:transactionId', [
  requirePermission('transactions:read'),
  param('transactionId').isUUID().withMessage('Valid transaction ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { transactionId } = req.params;
    const supabase = databaseService.getSupabase();

    // Get transaction details
    const { data: transaction, error } = await supabase
      .from('transactions')
      .select(`
        *,
        from_user:user_profiles!from_user_id(*),
        to_user:user_profiles!to_user_id(*),
        audit_logs!resource_id(*)
      `)
      .eq('id', transactionId)
      .single();

    if (error || !transaction) {
      throw new NotFoundError('Transaction not found');
    }

    // Get related wallet transactions
    const { data: walletTransactions } = await supabase
      .from('wallet_transactions')
      .select('*')
      .eq('transaction_id', transactionId);

    // Log admin action
    await auditService.logAdminAction(
      'view',
      req.admin.id,
      null,
      'transaction',
      transactionId,
      { action: 'view_details' }
    );

    res.json({
      success: true,
      data: {
        transaction: {
          id: transaction.id,
          reference: transaction.transaction_reference,
          type: transaction.type,
          amount: transaction.amount,
          currency: transaction.currency,
          formatted: currencyService.formatCurrency(transaction.amount, transaction.currency),
          status: transaction.status,
          description: transaction.description,
          metadata: transaction.metadata ? JSON.parse(transaction.metadata) : null,
          fromUser: transaction.from_user,
          toUser: transaction.to_user,
          createdAt: transaction.created_at,
          completedAt: transaction.completed_at,
          failedAt: transaction.failed_at,
          walletTransactions: walletTransactions || [],
          auditTrail: transaction.audit_logs || []
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction details:', error);
    throw error;
  }
}));

/**
 * @route   PUT /api/v1/admin/transactions/:transactionId/status
 * @desc    Update transaction status (admin override)
 * @access  Admin
 */
router.put('/:transactionId/status', [
  requirePermission('transactions:update'),
  param('transactionId').isUUID().withMessage('Valid transaction ID is required'),
  body('status').isIn(['completed', 'failed', 'cancelled']).withMessage('Invalid status'),
  body('reason').isString().withMessage('Reason is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  try {
    const { transactionId } = req.params;
    const { status, reason } = req.body;

    // Update transaction status
    if (status === 'completed') {
      await transactionService.completeTransaction(transactionId, { adminOverride: true, reason });
    } else if (status === 'failed') {
      await transactionService.failTransaction(transactionId, reason);
    } else if (status === 'cancelled') {
      await transactionService.cancelTransaction(transactionId, reason);
    }

    // Log admin action
    await auditService.logAdminAction(
      'update',
      req.admin.id,
      null,
      'transaction',
      transactionId,
      { status, reason, adminOverride: true }
    );

    res.json({
      success: true,
      message: `Transaction ${status} successfully`,
      data: {
        transactionId,
        status,
        reason,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Failed to update transaction status:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/transactions/stats/overview
 * @desc    Get transaction statistics for dashboard
 * @access  Admin
 */
router.get('/stats/overview', [
  requirePermission('transactions:read'),
  query('period').optional().isIn(['today', 'week', 'month', 'year']).withMessage('Invalid period')
], asyncHandler(async (req, res) => {
  try {
    const { period = 'today' } = req.query;
    const supabase = databaseService.getSupabase();

    // Calculate date range based on period
    const now = new Date();
    let startDate;

    switch (period) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    }

    // Get transaction statistics
    const { data: transactionStats } = await supabase.rpc('get_admin_transaction_stats', {
      start_date: startDate.toISOString(),
      end_date: now.toISOString()
    }).catch(() => ({ data: null }));

    // Fallback to basic queries if RPC function doesn't exist
    if (!transactionStats) {
      const [
        { count: totalTransactions },
        { count: successfulTransactions },
        { count: failedTransactions },
        { count: pendingTransactions }
      ] = await Promise.all([
        supabase.from('transactions').select('*', { count: 'exact', head: true })
          .gte('created_at', startDate.toISOString()),
        supabase.from('transactions').select('*', { count: 'exact', head: true })
          .eq('status', 'completed').gte('created_at', startDate.toISOString()),
        supabase.from('transactions').select('*', { count: 'exact', head: true })
          .eq('status', 'failed').gte('created_at', startDate.toISOString()),
        supabase.from('transactions').select('*', { count: 'exact', head: true })
          .eq('status', 'pending').gte('created_at', startDate.toISOString())
      ]);

      // Get total volume
      const { data: volumeData } = await supabase
        .from('transactions')
        .select('amount')
        .eq('status', 'completed')
        .gte('created_at', startDate.toISOString());

      const totalVolume = volumeData?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

      const stats = {
        totalTransactions: totalTransactions || 0,
        successfulTransactions: successfulTransactions || 0,
        failedTransactions: failedTransactions || 0,
        pendingTransactions: pendingTransactions || 0,
        totalVolume,
        successRate: totalTransactions > 0 ? ((successfulTransactions / totalTransactions) * 100).toFixed(1) : 0,
        averageAmount: successfulTransactions > 0 ? (totalVolume / successfulTransactions).toFixed(2) : 0
      };

      return res.json({
        success: true,
        data: {
          stats,
          period,
          dateRange: {
            startDate: startDate.toISOString(),
            endDate: now.toISOString()
          }
        }
      });
    }

    res.json({
      success: true,
      data: {
        stats: transactionStats,
        period,
        dateRange: {
          startDate: startDate.toISOString(),
          endDate: now.toISOString()
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction statistics:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/transactions/analytics/trends
 * @desc    Get transaction trends and analytics
 * @access  Admin
 */
router.get('/analytics/trends', [
  requirePermission('transactions:read'),
  query('days').optional().isInt({ min: 1, max: 365 }).withMessage('Days must be between 1 and 365')
], asyncHandler(async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const supabase = databaseService.getSupabase();

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get daily transaction trends
    const { data: trends } = await supabase.rpc('get_transaction_trends', {
      start_date: startDate.toISOString(),
      end_date: new Date().toISOString()
    }).catch(() => ({ data: [] }));

    // Get transaction type distribution
    const { data: typeDistribution } = await supabase
      .from('transactions')
      .select('type, count(*)')
      .gte('created_at', startDate.toISOString())
      .eq('status', 'completed');

    res.json({
      success: true,
      data: {
        trends: trends || [],
        typeDistribution: typeDistribution || [],
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction analytics:', error);
    throw error;
  }
}));

module.exports = router;
