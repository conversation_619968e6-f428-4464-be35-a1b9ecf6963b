# JiraniPay Production Environment Configuration Template
# 🔒 CRITICAL SECURITY NOTICE:
# - This file should NEVER contain real production credentials
# - Copy this to .env.production.local and add your actual production credentials
# - NEVER commit .env.production.local to version control
# - Use your deployment platform's secret management for production

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=production
EXPO_PUBLIC_ENVIRONMENT=production

# =============================================================================
# SUPABASE CONFIGURATION (Production)
# =============================================================================
# Create a separate Supabase project for production
# Get these values from your PRODUCTION Supabase project dashboard
EXPO_PUBLIC_PROD_SUPABASE_URL=https://your-prod-project.supabase.co
EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY=your-prod-anon-key-here

# =============================================================================
# API CONFIGURATION
# =============================================================================
EXPO_PUBLIC_PROD_API_URL=https://api.jiranipay.com

# =============================================================================
# SECURITY SETTINGS (Production)
# =============================================================================
# Generate with: openssl rand -base64 32
JWT_SECRET=your-production-jwt-secret-here
ENCRYPTION_KEY=your-production-encryption-key-here

# =============================================================================
# EXTERNAL API CONFIGURATION (Production)
# =============================================================================
# Mobile Money APIs
MTN_API_KEY=your-mtn-production-key
MTN_SUBSCRIPTION_KEY=your-mtn-subscription-key
AIRTEL_CLIENT_ID=your-airtel-client-id
AIRTEL_CLIENT_SECRET=your-airtel-client-secret

# SMS/Communication APIs
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
AFRICASTALKING_USERNAME=your-africastalking-username
AFRICASTALKING_API_KEY=your-africastalking-api-key

# =============================================================================
# MONITORING & ANALYTICS (Production)
# =============================================================================
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_API_KEY=your-analytics-api-key
PERFORMANCE_MONITORING_KEY=your-performance-key

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# These should be set by your deployment platform
# DEPLOYMENT_ENVIRONMENT=production
# BUILD_VERSION=1.0.0
# RELEASE_CHANNEL=production
