/**
 * Bill Payment Routes
 * Handles bill payment operations and provider management
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');
const { validationMiddleware } = require('../middleware/validation');
const billPaymentService = require('../services/billPaymentService');
const currencyService = require('../services/currencyService');
const config = require('../config/config');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/bills/providers
 * @desc    Get available bill providers
 * @access  Private
 */
router.get('/providers', [
  query('type').optional().isIn(['electricity', 'water', 'internet', 'tv', 'airtime', 'data', 'school_fees', 'insurance']).withMessage('Invalid bill type'),
  query('country').optional().isIn(config.business.supportedCountries).withMessage('Invalid country')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { type, country = 'UG' } = req.query;

  try {
    const providers = await billPaymentService.getBillProviders(type, country);

    res.json({
      success: true,
      data: {
        providers,
        totalProviders: providers.length,
        supportedTypes: ['electricity', 'water', 'internet', 'tv', 'airtime', 'data', 'school_fees', 'insurance']
      }
    });
  } catch (error) {
    logger.error('Failed to get bill providers:', error);
    throw new Error('Failed to retrieve bill providers');
  }
}));

/**
 * @route   POST /api/v1/bills/validate
 * @desc    Validate bill account
 * @access  Private
 */
router.post('/validate', [
  body('providerId').notEmpty().withMessage('Provider ID is required'),
  body('accountNumber').notEmpty().withMessage('Account number is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { providerId, accountNumber } = req.body;

  try {
    const validation = await billPaymentService.validateBillAccount(providerId, accountNumber);

    res.json({
      success: true,
      data: {
        isValid: validation.isValid,
        customerName: validation.customerName,
        accountBalance: validation.accountBalance,
        lastPaymentDate: validation.lastPaymentDate,
        provider: validation.provider
      }
    });
  } catch (error) {
    logger.error('Bill account validation failed:', error);
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
}));

/**
 * @route   POST /api/v1/bills/pay
 * @desc    Pay a bill
 * @access  Private
 */
router.post('/pay', validationMiddleware.billPayment, asyncHandler(async (req, res) => {
  const { providerId, accountNumber, amount, customerName, packageId, description } = req.body;

  try {
    // Validate provider and amount
    const provider = billPaymentService.getProvider(providerId);
    if (!provider) {
      throw new ValidationError('Invalid provider');
    }

    const amountValidation = billPaymentService.validateProviderAmount(providerId, amount);
    if (!amountValidation.valid) {
      throw new ValidationError(amountValidation.error);
    }

    // Process bill payment
    const paymentResult = await billPaymentService.processBillPayment({
      providerId,
      accountNumber,
      amount,
      customerName,
      packageId,
      description
    }, req.user.user_id);

    res.json({
      success: true,
      message: 'Bill payment processed successfully',
      data: {
        transactionId: paymentResult.transactionId,
        reference: paymentResult.reference,
        status: paymentResult.status,
        amount: paymentResult.amount,
        currency: paymentResult.currency,
        formatted: currencyService.formatCurrency(paymentResult.amount, paymentResult.currency),
        provider: paymentResult.provider,
        customerName: paymentResult.customerName,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Bill payment failed:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/bills/history
 * @desc    Get bill payment history
 * @access  Private
 */
router.get('/history', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('providerId').optional().isString().withMessage('Provider ID must be a string'),
  query('billType').optional().isIn(['electricity', 'water', 'internet', 'tv', 'airtime', 'data', 'school_fees', 'insurance']).withMessage('Invalid bill type'),
  query('startDate').optional().isISO8601().withMessage('Valid start date is required'),
  query('endDate').optional().isISO8601().withMessage('Valid end date is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const {
    page = 1,
    limit = 20,
    providerId,
    billType,
    startDate,
    endDate
  } = req.query;

  try {
    const history = await billPaymentService.getBillPaymentHistory(req.user.user_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      providerId,
      billType,
      startDate,
      endDate
    });

    res.json({
      success: true,
      data: {
        payments: history.payments,
        pagination: history.pagination,
        summary: {
          totalPayments: history.pagination.total,
          totalPages: history.pagination.pages || Math.ceil(history.pagination.total / limit)
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get bill payment history:', error);
    throw new Error('Failed to retrieve bill payment history');
  }
}));

/**
 * @route   GET /api/v1/bills/providers/:providerId
 * @desc    Get specific provider details
 * @access  Private
 */
router.get('/providers/:providerId', [
  param('providerId').notEmpty().withMessage('Provider ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { providerId } = req.params;

  try {
    const provider = billPaymentService.getProvider(providerId);
    if (!provider) {
      throw new NotFoundError('Provider not found');
    }

    // Remove sensitive information
    const providerInfo = {
      id: provider.id,
      name: provider.name,
      type: provider.type,
      country: provider.country,
      minAmount: provider.minAmount,
      maxAmount: provider.maxAmount,
      currency: provider.currency,
      accountNumberFormat: provider.accountNumberFormat.toString(),
      packages: provider.packages || null,
      isActive: provider.isActive
    };

    res.json({
      success: true,
      data: {
        provider: providerInfo
      }
    });
  } catch (error) {
    logger.error('Failed to get provider details:', error);
    throw error;
  }
}));

module.exports = router;
