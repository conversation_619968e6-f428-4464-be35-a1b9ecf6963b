import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import profileManagementService from '../services/profileManagementService';

const VerificationStatusScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [user, setUser] = useState(null);
  const [documents, setDocuments] = useState([]);
  const [verificationLevel, setVerificationLevel] = useState(null);
  const [completionStatus, setCompletionStatus] = useState(null);

  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  useEffect(() => {
    loadVerificationStatus();
  }, []);

  const loadVerificationStatus = async () => {
    try {
      setLoading(true);
      
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        Alert.alert(t('error'), t('pleaseLogInToContinue'));
        navigation.goBack();
        return;
      }
      
      setUser(currentUser);

      // Get current verification level
      const levelResult = await profileManagementService.getKYCVerificationLevel(currentUser.id);
      if (levelResult.success) {
        setVerificationLevel(levelResult.data);
      }

      // Get profile completion status
      const statusResult = await profileManagementService.getProfileCompletionStatus(currentUser.id);
      if (statusResult.success) {
        setCompletionStatus(statusResult.data);
      }

      // TODO: Get uploaded documents (would need to add this method to profileManagementService)
      // For now, we'll create mock data based on completion status
      const mockDocuments = createMockDocuments(statusResult.data);
      setDocuments(mockDocuments);

    } catch (error) {
      console.error('❌ Error loading verification status:', error);
      Alert.alert(t('error'), t('failedToLoadVerificationStatus'));
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadVerificationStatus();
    setRefreshing(false);
  };

  const createMockDocuments = (completionData) => {
    const completedSteps = completionData?.completedSteps || [];
    const documents = [];

    // Check if KYC basic is completed (National ID)
    if (completedSteps.some(step => step.step_name === 'kyc_basic')) {
      documents.push({
        id: 'national_id_1',
        document_type: 'national_id',
        verification_status: 'approved',
        created_at: new Date().toISOString(),
        verification_notes: 'Document verified successfully'
      });
    }

    // Check if KYC standard is completed (Utility Bill)
    if (completedSteps.some(step => step.step_name === 'kyc_standard')) {
      documents.push({
        id: 'utility_bill_1',
        document_type: 'utility_bill',
        verification_status: 'approved',
        created_at: new Date().toISOString(),
        verification_notes: 'Address verification completed'
      });
    }

    return documents;
  };

  const getDocumentInfo = (type) => {
    const documents = {
      national_id: {
        title: 'National ID',
        icon: 'card-outline',
        description: 'Government-issued identification'
      },
      passport: {
        title: 'Passport',
        icon: 'airplane-outline',
        description: 'International travel document'
      },
      driving_license: {
        title: 'Driving License',
        icon: 'car-outline',
        description: 'Valid driving permit'
      },
      utility_bill: {
        title: 'Utility Bill',
        icon: 'receipt-outline',
        description: 'Proof of address document'
      }
    };
    return documents[type] || { title: type, icon: 'document-outline', description: 'Document' };
  };

  const getStatusInfo = (status) => {
    const statuses = {
      pending: {
        color: Colors.status.warning,
        icon: 'time-outline',
        text: 'Under Review',
        description: 'Your document is being reviewed by our team'
      },
      approved: {
        color: Colors.status.success,
        icon: 'checkmark-circle-outline',
        text: 'Approved',
        description: 'Document verified successfully'
      },
      rejected: {
        color: Colors.status.error,
        icon: 'close-circle-outline',
        text: 'Rejected',
        description: 'Document needs to be resubmitted'
      },
      expired: {
        color: Colors.neutral.warmGray,
        icon: 'alert-circle-outline',
        text: 'Expired',
        description: 'Document has expired and needs renewal'
      }
    };
    return statuses[status] || statuses.pending;
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getOverallProgress = () => {
    if (!completionStatus) return 0;
    return completionStatus.completionPercentage || 0;
  };

  const renderProgressCard = () => {
    const progress = getOverallProgress();
    const levelInfo = verificationLevel?.verification_level || 'basic';
    
    return (
      <View style={styles.progressCard}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressTitle}>{t('verificationProgress')}</Text>
          <Text style={styles.progressPercentage}>{progress}%</Text>
        </View>
        
        <View style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground}>
            <View 
              style={[
                styles.progressBarFill, 
                { width: `${progress}%` }
              ]} 
            />
          </View>
        </View>

        <View style={styles.progressInfo}>
          <Text style={styles.progressLevel}>
            Current Level: <Text style={styles.progressLevelValue}>{levelInfo.toUpperCase()}</Text>
          </Text>
          <Text style={styles.progressDescription}>
            {completionStatus?.completedCount || 0} of {completionStatus?.totalSteps || 0} steps completed
          </Text>
        </View>
      </View>
    );
  };

  const renderDocumentItem = (document) => {
    const docInfo = getDocumentInfo(document.document_type);
    const statusInfo = getStatusInfo(document.verification_status);

    return (
      <View key={document.id} style={styles.documentItem}>
        <View style={styles.documentLeft}>
          <View style={styles.documentIcon}>
            <Ionicons name={docInfo.icon} size={24} color={Colors.primary.main} />
          </View>
          <View style={styles.documentContent}>
            <Text style={styles.documentTitle}>{docInfo.title}</Text>
            <Text style={styles.documentDescription}>{docInfo.description}</Text>
            <Text style={styles.documentDate}>Uploaded: {formatDate(document.created_at)}</Text>
          </View>
        </View>

        <View style={styles.documentRight}>
          <View style={[styles.statusBadge, { backgroundColor: statusInfo.color + '20' }]}>
            <Ionicons name={statusInfo.icon} size={16} color={statusInfo.color} />
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.text}
            </Text>
          </View>
          
          {document.verification_notes && (
            <Text style={styles.documentNotes}>{document.verification_notes}</Text>
          )}
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="document-outline" size={64} color={Colors.neutral.warmGray} />
      <Text style={styles.emptyTitle}>{t('noDocumentsUploaded')}</Text>
      <Text style={styles.emptyDescription}>
        {t('startYourVerificationProcessByUploadingYourIdentif')}
      </Text>
      <TouchableOpacity
        style={styles.emptyButton}
        onPress={() => navigation.navigate('AccountVerification')}
        activeOpacity={0.7}
      >
        <Text style={styles.emptyButtonText}>{t('startVerification')}</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>{t('loadingVerificationStatus')}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton onPress={() => navigation.goBack()} />
        <Text style={styles.headerTitle}>{t('verificationStatus')}</Text>
        <TouchableOpacity onPress={handleRefresh}>
          <Ionicons name="refresh-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderProgressCard()}

        {/* Documents Section */}
        <View style={styles.documentsSection}>
          <Text style={styles.sectionTitle}>{t('uploadedDocuments')}</Text>
          
          {documents.length > 0 ? (
            documents.map(renderDocumentItem)
          ) : (
            renderEmptyState()
          )}
        </View>

        {/* Next Steps */}
        {completionStatus && completionStatus.pendingSteps?.length > 0 && (
          <View style={styles.nextStepsSection}>
            <Text style={styles.sectionTitle}>{t('nextSteps')}</Text>
            <View style={styles.nextStepsCard}>
              <Text style={styles.nextStepsDescription}>
                {t('completeTheseStepsToIncreaseYourVerificationLevel')}
              </Text>
              {completionStatus.pendingSteps.slice(0, 3).map((step, index) => (
                <View key={step} style={styles.nextStepItem}>
                  <Text style={styles.nextStepBullet}>•</Text>
                  <Text style={styles.nextStepText}>
                    {step.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  progressCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    marginVertical: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  progressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  progressPercentage: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: Colors.neutral.lightGray,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: Colors.primary.main,
    borderRadius: 4,
  },
  progressInfo: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingTop: 16,
  },
  progressLevel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  progressLevelValue: {
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  progressDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  documentsSection: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  documentItem: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  documentLeft: {
    flexDirection: 'row',
    flex: 1,
  },
  documentIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  documentContent: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  documentDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  documentDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  documentRight: {
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  documentNotes: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'right',
    maxWidth: 120,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  emptyButton: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  nextStepsSection: {
    marginVertical: 16,
  },
  nextStepsCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  nextStepsDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  nextStepItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  nextStepBullet: {
    fontSize: 16,
    color: Colors.primary.main,
    marginRight: 8,
    marginTop: 2,
  },
  nextStepText: {
    fontSize: 14,
    color: theme.colors.text,
    flex: 1,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default VerificationStatusScreen;
