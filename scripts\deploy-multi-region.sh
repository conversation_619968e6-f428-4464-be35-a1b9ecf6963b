#!/bin/bash

# JiraniPay Multi-Region Deployment Script
# Automates deployment across multiple AWS regions with health checks and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TERRAFORM_DIR="$PROJECT_ROOT/infrastructure/terraform"
K8S_DIR="$PROJECT_ROOT/k8s"

# Default values
ENVIRONMENT="${ENVIRONMENT:-production}"
DRY_RUN="${DRY_RUN:-false}"
SKIP_TESTS="${SKIP_TESTS:-false}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"
DEPLOYMENT_TIMEOUT="${DEPLOYMENT_TIMEOUT:-1800}" # 30 minutes

# Regions configuration
declare -A REGIONS=(
    ["us-east-1"]="primary"
    ["eu-west-1"]="secondary"
    ["ap-southeast-1"]="secondary"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log_error "Deployment failed with exit code $exit_code"
        if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
            log_warning "Initiating rollback..."
            rollback_deployment
        fi
    fi
    exit $exit_code
}

trap cleanup EXIT

# Help function
show_help() {
    cat << EOF
JiraniPay Multi-Region Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENVIRONMENT    Deployment environment (default: production)
    -d, --dry-run                   Perform a dry run without making changes
    -s, --skip-tests                Skip health checks and tests
    -r, --no-rollback              Disable automatic rollback on failure
    -t, --timeout SECONDS          Deployment timeout in seconds (default: 1800)
    -h, --help                      Show this help message

Environment Variables:
    AWS_PROFILE                     AWS profile to use
    DOCKER_REGISTRY                 Docker registry URL
    IMAGE_TAG                       Docker image tag to deploy
    SLACK_WEBHOOK_URL              Slack webhook for notifications

Examples:
    $0 --environment staging --dry-run
    $0 --environment production --timeout 3600
    ENVIRONMENT=staging $0 --skip-tests

EOF
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -d|--dry-run)
                DRY_RUN="true"
                shift
                ;;
            -s|--skip-tests)
                SKIP_TESTS="true"
                shift
                ;;
            -r|--no-rollback)
                ROLLBACK_ON_FAILURE="false"
                shift
                ;;
            -t|--timeout)
                DEPLOYMENT_TIMEOUT="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Validate prerequisites
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check required tools
    local required_tools=("aws" "kubectl" "terraform" "docker" "jq" "curl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    # Check required environment variables
    if [[ -z "${IMAGE_TAG:-}" ]]; then
        log_error "IMAGE_TAG environment variable is required"
        exit 1
    fi
    
    if [[ -z "${DOCKER_REGISTRY:-}" ]]; then
        log_error "DOCKER_REGISTRY environment variable is required"
        exit 1
    fi
    
    log_success "Prerequisites validated"
}

# Deploy infrastructure using Terraform
deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."
    
    cd "$TERRAFORM_DIR"
    
    # Initialize Terraform
    terraform init -upgrade
    
    # Plan deployment
    terraform plan \
        -var="environment=$ENVIRONMENT" \
        -var="image_tag=$IMAGE_TAG" \
        -out=tfplan
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "Dry run mode - skipping Terraform apply"
        return 0
    fi
    
    # Apply infrastructure changes
    terraform apply tfplan
    
    # Get outputs
    export CLOUDFRONT_DISTRIBUTION_ID=$(terraform output -raw cloudfront_distribution_id)
    export ROUTE53_ZONE_ID=$(terraform output -raw route53_zone_id)
    
    log_success "Infrastructure deployment completed"
}

# Deploy application to Kubernetes
deploy_application() {
    local region=$1
    local region_type=$2
    
    log_info "Deploying application to $region ($region_type)..."
    
    # Update kubeconfig for the region
    aws eks update-kubeconfig --region "$region" --name "jiranipay-$ENVIRONMENT-cluster"
    
    # Apply Kubernetes manifests
    cd "$K8S_DIR"
    
    # Create namespace if it doesn't exist
    kubectl create namespace "jiranipay-$ENVIRONMENT" --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply secrets
    kubectl apply -f secrets/ -n "jiranipay-$ENVIRONMENT"
    
    # Apply base configuration
    kubectl apply -k "overlays/$ENVIRONMENT" -n "jiranipay-$ENVIRONMENT"
    
    # Update image tag
    kubectl set image deployment/jiranipay-backend \
        jiranipay-backend="$DOCKER_REGISTRY/jiranipay:$IMAGE_TAG" \
        -n "jiranipay-$ENVIRONMENT"
    
    # Wait for rollout to complete
    kubectl rollout status deployment/jiranipay-backend \
        -n "jiranipay-$ENVIRONMENT" \
        --timeout="${DEPLOYMENT_TIMEOUT}s"
    
    log_success "Application deployed to $region"
}

# Perform health checks
perform_health_checks() {
    local region=$1
    
    log_info "Performing health checks for $region..."
    
    # Get load balancer URL
    local lb_url=$(kubectl get service jiranipay-backend-service \
        -n "jiranipay-$ENVIRONMENT" \
        -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
    
    if [[ -z "$lb_url" ]]; then
        log_error "Could not get load balancer URL for $region"
        return 1
    fi
    
    # Wait for load balancer to be ready
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "https://$lb_url/health" > /dev/null; then
            log_success "Health check passed for $region"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Health checks failed for $region after $max_attempts attempts"
    return 1
}

# Run smoke tests
run_smoke_tests() {
    local region=$1
    
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "Skipping smoke tests for $region"
        return 0
    fi
    
    log_info "Running smoke tests for $region..."
    
    # Get API endpoint
    local api_endpoint="https://api-$region.jiranipay.com"
    
    # Test authentication endpoint
    local auth_response=$(curl -s -w "%{http_code}" -o /dev/null "$api_endpoint/api/v1/auth/health")
    if [[ "$auth_response" != "200" ]]; then
        log_error "Authentication endpoint test failed for $region (HTTP $auth_response)"
        return 1
    fi
    
    # Test wallet endpoint (requires authentication, so just check if it returns 401)
    local wallet_response=$(curl -s -w "%{http_code}" -o /dev/null "$api_endpoint/api/v1/wallets")
    if [[ "$wallet_response" != "401" ]]; then
        log_error "Wallet endpoint test failed for $region (HTTP $wallet_response)"
        return 1
    fi
    
    # Test bill payment endpoint
    local bills_response=$(curl -s -w "%{http_code}" -o /dev/null "$api_endpoint/api/v1/bills/providers")
    if [[ "$bills_response" != "401" ]]; then
        log_error "Bill payment endpoint test failed for $region (HTTP $bills_response)"
        return 1
    fi
    
    log_success "Smoke tests passed for $region"
}

# Update CloudFront distribution
update_cloudfront() {
    log_info "Updating CloudFront distribution..."
    
    if [[ -n "${CLOUDFRONT_DISTRIBUTION_ID:-}" ]]; then
        # Create invalidation
        aws cloudfront create-invalidation \
            --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
            --paths "/*" > /dev/null
        
        log_success "CloudFront invalidation created"
    else
        log_warning "CloudFront distribution ID not found, skipping invalidation"
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local color="good"
        if [[ "$status" == "failure" ]]; then
            color="danger"
        elif [[ "$status" == "warning" ]]; then
            color="warning"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"JiraniPay Deployment - $ENVIRONMENT\",
                    \"text\": \"$message\",
                    \"fields\": [
                        {\"title\": \"Environment\", \"value\": \"$ENVIRONMENT\", \"short\": true},
                        {\"title\": \"Image Tag\", \"value\": \"$IMAGE_TAG\", \"short\": true},
                        {\"title\": \"Timestamp\", \"value\": \"$(date -u)\", \"short\": true}
                    ]
                }]
            }" \
            "$SLACK_WEBHOOK_URL" > /dev/null
    fi
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    # Get previous image tag from deployment history
    for region in "${!REGIONS[@]}"; do
        log_info "Rolling back $region..."
        
        aws eks update-kubeconfig --region "$region" --name "jiranipay-$ENVIRONMENT-cluster"
        
        # Rollback to previous revision
        kubectl rollout undo deployment/jiranipay-backend \
            -n "jiranipay-$ENVIRONMENT" || true
        
        # Wait for rollback to complete
        kubectl rollout status deployment/jiranipay-backend \
            -n "jiranipay-$ENVIRONMENT" \
            --timeout=300s || true
    done
    
    send_notification "failure" "Deployment failed and was rolled back"
}

# Main deployment function
main() {
    parse_args "$@"
    
    log_info "Starting JiraniPay multi-region deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Image Tag: ${IMAGE_TAG:-not set}"
    log_info "Dry Run: $DRY_RUN"
    
    validate_prerequisites
    
    # Deploy infrastructure
    deploy_infrastructure
    
    # Deploy to each region
    local deployment_success=true
    local deployed_regions=()
    
    for region in "${!REGIONS[@]}"; do
        region_type="${REGIONS[$region]}"
        
        log_info "Deploying to $region ($region_type)..."
        
        if deploy_application "$region" "$region_type"; then
            deployed_regions+=("$region")
            
            if perform_health_checks "$region"; then
                if run_smoke_tests "$region"; then
                    log_success "Deployment to $region completed successfully"
                else
                    log_error "Smoke tests failed for $region"
                    deployment_success=false
                    break
                fi
            else
                log_error "Health checks failed for $region"
                deployment_success=false
                break
            fi
        else
            log_error "Application deployment failed for $region"
            deployment_success=false
            break
        fi
    done
    
    if [[ "$deployment_success" == "true" ]]; then
        # Update CloudFront
        update_cloudfront
        
        log_success "Multi-region deployment completed successfully!"
        send_notification "success" "Deployment completed successfully across all regions"
    else
        log_error "Deployment failed"
        exit 1
    fi
}

# Run main function
main "$@"
