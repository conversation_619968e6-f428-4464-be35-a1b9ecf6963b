/**
 * Webhook Routes
 * Handles incoming webhooks from payment providers and external services
 */

const express = require('express');
const crypto = require('crypto');
const { asyncHandler } = require('../middleware/errorHandler');
const config = require('../config/config');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * Webhook signature verification middleware
 */
const verifyWebhookSignature = (req, res, next) => {
  const signature = req.headers['x-webhook-signature'];
  const payload = JSON.stringify(req.body);
  
  if (!signature || !config.webhooks.secret) {
    logger.security('Webhook signature verification failed', {
      hasSignature: !!signature,
      hasSecret: !!config.webhooks.secret,
      ip: req.ip
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const expectedSignature = crypto
    .createHmac('sha256', config.webhooks.secret)
    .update(payload)
    .digest('hex');

  if (signature !== expectedSignature) {
    logger.security('Invalid webhook signature', {
      ip: req.ip,
      signature,
      expectedSignature
    });
    return res.status(401).json({ error: 'Invalid signature' });
  }

  next();
};

/**
 * @route   POST /api/v1/webhooks/mtn
 * @desc    Handle MTN Mobile Money webhooks
 * @access  Public (with signature verification)
 */
router.post('/mtn', verifyWebhookSignature, asyncHandler(async (req, res) => {
  const { transactionId, status, amount, currency } = req.body;

  logger.audit('MTN webhook received', {
    transactionId,
    status,
    amount,
    currency,
    ip: req.ip
  });

  // TODO: Implement MTN webhook processing logic

  res.json({ success: true, message: 'Webhook processed' });
}));

/**
 * @route   POST /api/v1/webhooks/airtel
 * @desc    Handle Airtel Money webhooks
 * @access  Public (with signature verification)
 */
router.post('/airtel', verifyWebhookSignature, asyncHandler(async (req, res) => {
  const { transactionId, status, amount, currency } = req.body;

  logger.audit('Airtel webhook received', {
    transactionId,
    status,
    amount,
    currency,
    ip: req.ip
  });

  // TODO: Implement Airtel webhook processing logic

  res.json({ success: true, message: 'Webhook processed' });
}));

module.exports = router;
