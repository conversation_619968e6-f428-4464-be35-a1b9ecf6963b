// Conditional imports for optional dependencies with Expo Go compatibility
let Notifications, Device;

// Check if we're running in Expo Go environment
const isExpoGo = typeof global !== 'undefined' && global.__expo && global.__expo.isExpoGo;

try {
  if (isExpoGo) {
    // In Expo Go, use mock implementations to avoid errors
    console.log('⚠️ Running in Expo Go - using mock notification implementation');
    Notifications = null;
    Device = null;
  } else {
    Notifications = require('expo-notifications');
    Device = require('expo-device');
  }
} catch (error) {
  console.log('⚠️ Notification dependencies not available, using mock implementation');
  Notifications = null;
  Device = null;
}

import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { isProductionMode } from '../config/environment';

// Safe Constants import with fallback
let Constants;
try {
  Constants = require('expo-constants').default;
} catch (error) {
  console.log('⚠️ expo-constants not available, using fallback');
  Constants = {
    appOwnership: 'expo',
    isDevice: true
  };
}

// Configure notification behavior (only if available and not in Expo Go)
if (Notifications && !__DEV__) {
  try {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
  } catch (error) {
    console.log('⚠️ Could not set notification handler (expected in Expo Go)');
  }
}

class NotificationService {
  constructor() {
    this.isInitialized = false;
    this.expoPushToken = null;
    this.notificationListener = null;
    this.responseListener = null;
    this.isExpoGo = Constants?.appOwnership === 'expo';
    this.inAppNotifications = [];
    this.notificationCallbacks = [];
    this.userSettings = {
      transactionNotifications: true,
      lowBalanceAlerts: true,
      spendingLimitAlerts: true,
      lowBalanceThreshold: 10000
    };
  }

  async initialize() {
    try {
      console.log('🔔 Initializing notification service...');

      // Check if dependencies are available
      if (!Notifications || !Device) {
        console.log('⚠️ Notification dependencies not available, using mock implementation');
        this.isInitialized = true;
        return { success: true, mock: true };
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('❌ Notification permissions not granted');
        return { success: false, error: 'Notification permissions not granted' };
      }

      // Get push token for development (skip in Expo Go)
      if (Device.isDevice && !__DEV__) {
        try {
          const token = (await Notifications.getExpoPushTokenAsync()).data;
          this.expoPushToken = token;
          console.log('📱 Expo push token:', token);
        } catch (error) {
          console.log('⚠️ Could not get push token (expected in Expo Go):', error.message);
          // Don't treat this as a failure in development
        }
      } else {
        console.log('⚠️ Skipping push token in development/Expo Go environment');
      }

      // Set up notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('wallet-transactions', {
          name: 'Wallet Transactions',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        await Notifications.setNotificationChannelAsync('wallet-alerts', {
          name: 'Wallet Alerts',
          importance: Notifications.AndroidImportance.DEFAULT,
          vibrationPattern: [0, 250, 250, 250],
        });
      }

      // Set up listeners
      this.setupNotificationListeners();

      this.isInitialized = true;
      console.log('✅ Notification service initialized successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing notification service:', error);
      this.isInitialized = true; // Mark as initialized to prevent repeated attempts
      return { success: false, error: error.message };
    }
  }

  setupNotificationListeners() {
    try {
      // Listen for notifications received while app is running
      this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
        console.log('🔔 Notification received:', notification);
      });

      // Listen for user interactions with notifications
      this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
        console.log('👆 Notification response:', response);
        this.handleNotificationResponse(response);
      });
    } catch (error) {
      console.log('⚠️ Could not setup notification listeners (expected in Expo Go):', error.message);
    }
  }

  handleNotificationResponse(response) {
    const { notification } = response;
    const { data } = notification.request.content;
    
    // Handle different notification types
    switch (data?.type) {
      case 'transaction_completed':
        // Navigate to transaction details
        console.log('📱 Opening transaction details:', data.transactionId);
        break;
      case 'low_balance':
        // Navigate to top-up screen
        console.log('📱 Opening top-up screen');
        break;
      case 'spending_limit':
        // Navigate to wallet settings
        console.log('📱 Opening wallet settings');
        break;
      default:
        console.log('📱 Opening wallet screen');
    }
  }

  async scheduleTransactionNotification(transaction) {
    try {
      // Enhanced notification handling for production mode
      if (!Notifications || this.isExpoGo) {
        console.log('🔔 Production Mode: Simulating transaction notification');
        console.log(`📱 Notification: ${transaction.description} - Amount: UGX ${transaction.amount?.toLocaleString()}`);

        // Store notification for later display in app
        await this.storeInAppNotification({
          id: `transaction_${transaction.id || Date.now()}`,
          type: 'transaction',
          title: transaction.type === 'credit' ? '💰 Money Received' : '💸 Payment Sent',
          message: `${transaction.description} - UGX ${transaction.amount?.toLocaleString()}`,
          timestamp: new Date().toISOString(),
          data: transaction
        });
        return;
      }

      const { type, amount, description, status } = transaction;

      let title, body, categoryIdentifier;

      if (status === 'completed') {
        if (type === 'credit') {
          title = '💰 Money Received';
          body = `You received UGX ${amount.toLocaleString()} - ${description}`;
        } else {
          title = '💸 Payment Sent';
          body = `You sent UGX ${amount.toLocaleString()} - ${description}`;
        }
        categoryIdentifier = 'wallet-transactions';
      } else if (status === 'pending') {
        title = '⏳ Transaction Pending';
        body = `Your transaction of UGX ${amount.toLocaleString()} is being processed`;
        categoryIdentifier = 'wallet-alerts';
      } else if (status === 'failed') {
        title = '❌ Transaction Failed';
        body = `Your transaction of UGX ${amount.toLocaleString()} could not be completed`;
        categoryIdentifier = 'wallet-alerts';
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: {
            type: 'transaction_completed',
            transactionId: transaction.id,
            amount: transaction.amount,
          },
          categoryIdentifier,
        },
        trigger: null, // Show immediately
      });

      console.log('🔔 Transaction notification scheduled:', title);
    } catch (error) {
      console.error('❌ Error scheduling transaction notification:', error);
    }
  }

  async scheduleTopUpNotification(amount, method) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: '💰 Wallet Top-Up Successful',
          body: `UGX ${amount.toLocaleString()} has been added to your wallet via ${method}`,
          data: {
            type: 'topup_completed',
            amount,
            method,
          },
          categoryIdentifier: 'wallet-transactions',
        },
        trigger: null,
      });

      console.log('🔔 Top-up notification scheduled');
    } catch (error) {
      console.error('❌ Error scheduling top-up notification:', error);
    }
  }

  async scheduleLowBalanceAlert(currentBalance, threshold = 10000) {
    try {
      if (currentBalance <= threshold) {
        await Notifications.scheduleNotificationAsync({
          content: {
            title: '⚠️ Low Wallet Balance',
            body: `Your wallet balance is UGX ${currentBalance.toLocaleString()}. Consider topping up.`,
            data: {
              type: 'low_balance',
              balance: currentBalance,
            },
            categoryIdentifier: 'wallet-alerts',
          },
          trigger: null,
        });

        console.log('🔔 Low balance alert scheduled');
      }
    } catch (error) {
      console.error('❌ Error scheduling low balance alert:', error);
    }
  }

  async scheduleSpendingLimitAlert(spentAmount, limit, period) {
    try {
      const percentage = (spentAmount / limit) * 100;
      
      if (percentage >= 80) {
        const title = percentage >= 100 ? '🚫 Spending Limit Reached' : '⚠️ Spending Limit Warning';
        const body = percentage >= 100 
          ? `You've reached your ${period} spending limit of UGX ${limit.toLocaleString()}`
          : `You've used ${percentage.toFixed(0)}% of your ${period} spending limit`;

        await Notifications.scheduleNotificationAsync({
          content: {
            title,
            body,
            data: {
              type: 'spending_limit',
              spentAmount,
              limit,
              period,
              percentage,
            },
            categoryIdentifier: 'wallet-alerts',
          },
          trigger: null,
        });

        console.log('🔔 Spending limit alert scheduled');
      }
    } catch (error) {
      console.error('❌ Error scheduling spending limit alert:', error);
    }
  }

  async scheduleSecurityAlert(alertType, details) {
    try {
      let title, body;
      
      switch (alertType) {
        case 'login_new_device':
          title = '🔐 New Device Login';
          body = 'Your account was accessed from a new device';
          break;
        case 'password_changed':
          title = '🔐 Password Changed';
          body = 'Your account password has been updated';
          break;
        case 'biometric_disabled':
          title = '🔐 Biometric Authentication Disabled';
          body = 'Biometric authentication has been turned off for your account';
          break;
        default:
          title = '🔐 Security Alert';
          body = 'There was a security-related change to your account';
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: {
            type: 'security_alert',
            alertType,
            details,
          },
          categoryIdentifier: 'wallet-alerts',
        },
        trigger: null,
      });

      console.log('🔔 Security alert scheduled:', alertType);
    } catch (error) {
      console.error('❌ Error scheduling security alert:', error);
    }
  }

  async isNotificationEnabled() {
    try {
      const enabled = await AsyncStorage.getItem('notifications_enabled');
      return enabled !== 'false'; // Default to true
    } catch (error) {
      console.error('❌ Error checking notification preference:', error);
      return true;
    }
  }

  async requestPermissions() {
    try {
      // Check if dependencies are available
      if (!Notifications || isExpoGo) {
        console.log('⚠️ Notification permissions not available in Expo Go');
        return { success: false, error: 'Not available in Expo Go' };
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('❌ Notification permissions not granted');
        return { success: false, error: 'Notification permissions not granted' };
      }

      console.log('✅ Notification permissions granted');
      return { success: true };
    } catch (error) {
      console.error('❌ Error requesting notification permissions:', error);
      return { success: false, error: error.message };
    }
  }

  async setNotificationEnabled(enabled) {
    try {
      console.log(`🔔 Setting notifications to: ${enabled}`);

      // Always save the preference regardless of platform
      await AsyncStorage.setItem('notifications_enabled', enabled.toString());

      if (enabled) {
        // Check if we're in Expo Go or have notification dependencies
        if (!Notifications || isExpoGo) {
          console.log('⚠️ Running in Expo Go - notifications preference saved but functionality limited');
          console.log('💡 For full notification support, use a development build');
          return { success: true, warning: 'Notifications preference saved but limited in Expo Go' };
        }

        // Re-request permissions if enabling notifications
        const permissionResult = await this.requestPermissions();
        if (permissionResult.success) {
          console.log('✅ Notifications enabled and permissions granted');
        } else {
          console.log('⚠️ Notifications enabled but permissions not granted');
          return { success: true, warning: 'Notifications enabled but permissions not granted' };
        }
      } else {
        // Cancel all scheduled notifications if disabling (only if available)
        if (Notifications && !isExpoGo) {
          await this.cancelAllNotifications();
          console.log('✅ Notifications disabled and all scheduled notifications cancelled');
        } else {
          console.log('✅ Notifications disabled (mock mode)');
        }
      }

      console.log('✅ Notification preference updated:', enabled);
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting notification preference:', error);
      // Still return success for preference storage, even if notification setup fails
      return { success: true, warning: `Preference saved but notification setup failed: ${error.message}` };
    }
  }

  async cancelAllNotifications() {
    try {
      // Check if dependencies are available
      if (!Notifications || isExpoGo) {
        console.log('⚠️ Cannot cancel notifications in Expo Go (mock mode)');
        return { success: true, warning: 'Mock mode - no notifications to cancel' };
      }

      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('✅ All scheduled notifications cancelled');
      return { success: true };
    } catch (error) {
      console.error('❌ Error cancelling notifications:', error);
      return { success: false, error: error.message };
    }
  }

  async getNotificationSettings() {
    try {
      const settings = await Notifications.getPermissionsAsync();
      const enabled = await this.isNotificationEnabled();

      return {
        success: true,
        data: {
          enabled,
          permissions: settings,
          canAskAgain: settings.canAskAgain,
          granted: settings.granted,
          status: settings.status
        }
      };
    } catch (error) {
      console.error('❌ Error getting notification settings:', error);
      return { success: false, error: error.message };
    }
  }

  async clearAllNotifications() {
    try {
      await Notifications.dismissAllNotificationsAsync();
      console.log('🔔 All notifications cleared');
    } catch (error) {
      console.error('❌ Error clearing notifications:', error);
    }
  }

  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  // Enhanced notification methods for production mode
  async storeInAppNotification(notification) {
    try {
      // In production mode, only store notifications if user has enabled the relevant setting
      if (isProductionMode()) {
        const settings = await this.getUserSettings();
        const shouldStore = this.shouldStoreNotification(notification.type, settings);

        if (!shouldStore) {
          console.log(`🔒 Production mode: Skipping notification storage for type '${notification.type}' (user setting disabled)`);
          return;
        }

        console.log(`🔒 Production mode: Storing notification for type '${notification.type}'`);
      }

      this.inAppNotifications.unshift(notification);

      // Keep only last 50 notifications
      if (this.inAppNotifications.length > 50) {
        this.inAppNotifications = this.inAppNotifications.slice(0, 50);
      }

      // Store in AsyncStorage for persistence
      await AsyncStorage.setItem('inAppNotifications', JSON.stringify(this.inAppNotifications));

      // Trigger callbacks for real-time updates
      this.notificationCallbacks.forEach(callback => {
        try {
          callback(notification);
        } catch (error) {
          console.error('❌ Error in notification callback:', error);
        }
      });

      console.log('✅ In-app notification stored:', notification.title);
    } catch (error) {
      console.error('❌ Error storing in-app notification:', error);
    }
  }

  // Helper method to determine if notification should be stored based on user settings
  shouldStoreNotification(notificationType, userSettings) {
    switch (notificationType) {
      case 'transaction':
        return userSettings.transactionNotifications;
      case 'security':
        return userSettings.lowBalanceAlerts; // Using this as security notifications setting
      case 'system':
        return userSettings.spendingLimitAlerts; // Using this as system notifications setting
      default:
        return true; // Store unknown types by default
    }
  }

  async loadInAppNotifications() {
    try {
      const stored = await AsyncStorage.getItem('inAppNotifications');
      if (stored) {
        this.inAppNotifications = JSON.parse(stored);
      }

      // In production mode, filter notifications based on current user settings
      if (isProductionMode()) {
        const settings = await this.getUserSettings();
        const filteredNotifications = this.inAppNotifications.filter(notification =>
          this.shouldStoreNotification(notification.type, settings)
        );

        console.log(`🔒 Production mode: Filtered ${this.inAppNotifications.length} to ${filteredNotifications.length} notifications based on user settings`);
        return filteredNotifications;
      }

      return this.inAppNotifications;
    } catch (error) {
      console.error('❌ Error loading in-app notifications:', error);
      return [];
    }
  }

  getInAppNotifications() {
    return this.inAppNotifications;
  }

  addNotificationCallback(callback) {
    this.notificationCallbacks.push(callback);
  }

  removeNotificationCallback(callback) {
    const index = this.notificationCallbacks.indexOf(callback);
    if (index > -1) {
      this.notificationCallbacks.splice(index, 1);
    }
  }

  async updateUserSettings(settings) {
    try {
      this.userSettings = { ...this.userSettings, ...settings };
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(this.userSettings));
      console.log('✅ Notification settings updated:', settings);
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating notification settings:', error);
      return { success: false, error: error.message };
    }
  }

  async getUserSettings() {
    try {
      const stored = await AsyncStorage.getItem('notificationSettings');
      if (stored) {
        this.userSettings = { ...this.userSettings, ...JSON.parse(stored) };
      }
      return this.userSettings;
    } catch (error) {
      console.error('❌ Error loading notification settings:', error);
      return this.userSettings;
    }
  }

  // Enhanced notification methods with user settings check
  async scheduleTransactionNotificationWithSettings(transaction) {
    const settings = await this.getUserSettings();
    if (settings.transactionNotifications) {
      await this.scheduleTransactionNotification(transaction);
    }
  }

  async scheduleLowBalanceAlertWithSettings(currentBalance) {
    const settings = await this.getUserSettings();
    if (settings.lowBalanceAlerts) {
      await this.scheduleLowBalanceAlert(currentBalance, settings.lowBalanceThreshold);
    }
  }

  async scheduleSpendingLimitAlertWithSettings(spentAmount, limit, period) {
    const settings = await this.getUserSettings();
    if (settings.spendingLimitAlerts) {
      await this.scheduleSpendingLimitAlert(spentAmount, limit, period);
    }
  }
}

// Create and export singleton instance
const notificationService = new NotificationService();
export default notificationService;
