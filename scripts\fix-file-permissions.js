#!/usr/bin/env node

/**
 * File Permissions Security Fix Script for JiraniPay
 * 
 * This script fixes file permissions for sensitive credential files
 * to ensure they are only readable by the owner.
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function logSuccess(message) {
  console.log(colorize('✅ ' + message, 'green'));
}

function logWarning(message) {
  console.log(colorize('⚠️  ' + message, 'yellow'));
}

function logError(message) {
  console.log(colorize('❌ ' + message, 'red'));
}

function logInfo(message) {
  console.log(colorize('ℹ️  ' + message, 'blue'));
}

function logHeader(message) {
  console.log('\n' + colorize('='.repeat(60), 'cyan'));
  console.log(colorize(message, 'cyan'));
  console.log(colorize('='.repeat(60), 'cyan'));
}

// List of sensitive files that need secure permissions
const sensitiveFiles = [
  '.env.production.local',
  '.env.development.local',
  '.env.staging.local',
  '.env.local',
  '.env',
  'backend/.env.production.local',
  'backend/.env.development.local',
  'backend/.env.staging.local',
  'backend/.env.local',
  'backend/.env'
];

// Check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Get current file permissions (Unix-like systems)
function getCurrentPermissions(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const mode = stats.mode & parseInt('777', 8);
    return mode.toString(8);
  } catch (error) {
    return null;
  }
}

// Fix permissions on Unix-like systems
function fixUnixPermissions(filePath) {
  return new Promise((resolve, reject) => {
    // Set permissions to 600 (read/write for owner only)
    exec(`chmod 600 "${filePath}"`, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

// Fix permissions on Windows
function fixWindowsPermissions(filePath) {
  return new Promise((resolve, reject) => {
    const absolutePath = path.resolve(filePath);
    
    // Remove all permissions and grant full control only to current user
    const commands = [
      `icacls "${absolutePath}" /inheritance:r`,
      `icacls "${absolutePath}" /grant:r "%USERNAME%":F`,
      `icacls "${absolutePath}" /remove "Everyone"`,
      `icacls "${absolutePath}" /remove "Users"`,
      `icacls "${absolutePath}" /remove "Authenticated Users"`
    ];
    
    let completed = 0;
    let hasError = false;
    
    commands.forEach((command, index) => {
      exec(command, (error, stdout, stderr) => {
        completed++;
        
        if (error && !hasError) {
          hasError = true;
          reject(error);
          return;
        }
        
        if (completed === commands.length && !hasError) {
          resolve();
        }
      });
    });
  });
}

// Check current permissions and fix if needed
async function secureFile(filePath) {
  if (!fileExists(filePath)) {
    logInfo(`File not found: ${filePath}`);
    return;
  }
  
  console.log(`\n🔍 Checking: ${filePath}`);
  
  try {
    const isWindows = os.platform() === 'win32';
    
    if (isWindows) {
      // On Windows, use icacls to fix permissions
      await fixWindowsPermissions(filePath);
      logSuccess(`Fixed Windows permissions for ${filePath}`);
    } else {
      // On Unix-like systems, check and fix permissions
      const currentPerms = getCurrentPermissions(filePath);
      console.log(`  Current permissions: ${currentPerms}`);
      
      if (currentPerms !== '600') {
        await fixUnixPermissions(filePath);
        const newPerms = getCurrentPermissions(filePath);
        logSuccess(`Fixed permissions: ${currentPerms} → ${newPerms}`);
      } else {
        logSuccess(`Permissions already secure: ${currentPerms}`);
      }
    }
  } catch (error) {
    logError(`Failed to fix permissions for ${filePath}: ${error.message}`);
  }
}

// Create .gitignore entries for secure files
function updateGitignore() {
  const gitignorePath = '.gitignore';
  
  if (!fileExists(gitignorePath)) {
    logWarning('.gitignore file not found');
    return;
  }
  
  const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
  const requiredEntries = [
    '# Environment files with actual credentials - NEVER COMMIT THESE',
    '.env',
    '.env.local',
    '.env.development.local',
    '.env.staging.local',
    '.env.production.local',
    '.env.test.local',
    'backend/.env',
    'backend/.env.local',
    'backend/.env.development.local',
    'backend/.env.staging.local',
    'backend/.env.production.local'
  ];
  
  let needsUpdate = false;
  const missingEntries = [];
  
  requiredEntries.forEach(entry => {
    if (!gitignoreContent.includes(entry)) {
      needsUpdate = true;
      missingEntries.push(entry);
    }
  });
  
  if (needsUpdate) {
    const updatedContent = gitignoreContent + '\n\n' + missingEntries.join('\n') + '\n';
    fs.writeFileSync(gitignorePath, updatedContent);
    logSuccess(`Updated .gitignore with ${missingEntries.length} missing entries`);
  } else {
    logSuccess('.gitignore already contains all required entries');
  }
}

// Main function
async function main() {
  console.clear();
  logHeader('🔐 JIRANIPAY FILE PERMISSIONS SECURITY FIX');
  
  console.log('This script will secure sensitive credential files by setting proper permissions.');
  console.log('Only the file owner will be able to read/write these files.\n');
  
  logInfo(`Operating System: ${os.platform()}`);
  logInfo(`Current User: ${os.userInfo().username}`);
  
  logHeader('🔒 SECURING CREDENTIAL FILES');
  
  // Fix permissions for all sensitive files
  for (const file of sensitiveFiles) {
    await secureFile(file);
  }
  
  logHeader('📝 UPDATING .GITIGNORE');
  updateGitignore();
  
  logHeader('✅ SECURITY FIX COMPLETE');
  
  console.log('\n🎉 File permissions have been secured!');
  console.log('\n📋 What was done:');
  console.log('✅ Set restrictive permissions on all credential files');
  console.log('✅ Ensured only file owner can read/write credential files');
  console.log('✅ Updated .gitignore to prevent accidental commits');
  
  console.log('\n🔍 Verification:');
  console.log('Run the following command to verify the fix:');
  console.log('  npm run validate-security');
  
  console.log('\n⚠️  Important Notes:');
  console.log('- Credential files are now only accessible by you');
  console.log('- Other users on this system cannot read your credentials');
  console.log('- Always use secure file permissions for credential files');
  console.log('- Never commit actual credential files to version control');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  secureFile,
  fixUnixPermissions,
  fixWindowsPermissions,
  updateGitignore
};
