/**
 * Device-Based User Recognition Service
 * 
 * Implements persistent user recognition for returning users
 * Stores user information locally for personalized greetings
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

class DeviceUserRecognitionService {
  constructor() {
    this.STORAGE_KEYS = {
      KNOWN_USERS: 'jiranipay_known_users',
      LAST_USER: 'jiranipay_last_user',
      USER_PREFERENCES: 'jiranipay_user_preferences'
    };
  }

  /**
   * Store user information for device recognition
   */
  async storeUserInfo(userId, userInfo) {
    try {
      console.log('💾 Storing user info for device recognition:', userId);
      
      const userRecord = {
        userId,
        fullName: userInfo.fullName || userInfo.full_name,
        phone: userInfo.phone || userInfo.phoneNumber,
        email: userInfo.email,
        avatarUrl: userInfo.avatarUrl || userInfo.avatar_url,
        lastLoginAt: new Date().toISOString(),
        deviceRecognized: true
      };

      // Store in known users list
      const knownUsers = await this.getKnownUsers();
      knownUsers[userId] = userRecord;
      
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.KNOWN_USERS, 
        JSON.stringify(knownUsers)
      );

      // Store as last user
      await AsyncStorage.setItem(
        this.STORAGE_KEYS.LAST_USER,
        JSON.stringify(userRecord)
      );

      console.log('✅ User info stored for device recognition');
      return { success: true };
    } catch (error) {
      console.error('❌ Error storing user info:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get known users on this device
   */
  async getKnownUsers() {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.KNOWN_USERS);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('❌ Error getting known users:', error);
      return {};
    }
  }

  /**
   * Get last user who logged in on this device
   */
  async getLastUser() {
    try {
      const stored = await AsyncStorage.getItem(this.STORAGE_KEYS.LAST_USER);
      if (stored) {
        const lastUser = JSON.parse(stored);
        console.log('👤 Found last user on device:', lastUser.fullName);
        return lastUser;
      }
      return null;
    } catch (error) {
      console.error('❌ Error getting last user:', error);
      return null;
    }
  }

  /**
   * Check if user is recognized on this device
   */
  async isUserRecognized(userId) {
    try {
      const knownUsers = await this.getKnownUsers();
      const isRecognized = !!knownUsers[userId];
      console.log(`🔍 User ${userId} recognized on device:`, isRecognized);
      return isRecognized;
    } catch (error) {
      console.error('❌ Error checking user recognition:', error);
      return false;
    }
  }

  /**
   * Get user info for recognized user
   */
  async getRecognizedUserInfo(userId) {
    try {
      const knownUsers = await this.getKnownUsers();
      const userInfo = knownUsers[userId];
      
      if (userInfo) {
        console.log('✅ Retrieved recognized user info:', userInfo.fullName);
        return userInfo;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error getting recognized user info:', error);
      return null;
    }
  }

  /**
   * Update last login time for user
   */
  async updateLastLogin(userId) {
    try {
      const knownUsers = await this.getKnownUsers();
      if (knownUsers[userId]) {
        knownUsers[userId].lastLoginAt = new Date().toISOString();
        await AsyncStorage.setItem(
          this.STORAGE_KEYS.KNOWN_USERS,
          JSON.stringify(knownUsers)
        );
        
        // Also update last user
        await AsyncStorage.setItem(
          this.STORAGE_KEYS.LAST_USER,
          JSON.stringify(knownUsers[userId])
        );
      }
    } catch (error) {
      console.error('❌ Error updating last login:', error);
    }
  }

  /**
   * Clear user recognition data (for logout/reset)
   */
  async clearUserRecognition(userId = null) {
    try {
      if (userId) {
        // Clear specific user
        const knownUsers = await this.getKnownUsers();
        delete knownUsers[userId];
        await AsyncStorage.setItem(
          this.STORAGE_KEYS.KNOWN_USERS,
          JSON.stringify(knownUsers)
        );
        
        // Clear last user if it was this user
        const lastUser = await this.getLastUser();
        if (lastUser && lastUser.userId === userId) {
          await AsyncStorage.removeItem(this.STORAGE_KEYS.LAST_USER);
        }
      } else {
        // Clear all recognition data
        await AsyncStorage.removeItem(this.STORAGE_KEYS.KNOWN_USERS);
        await AsyncStorage.removeItem(this.STORAGE_KEYS.LAST_USER);
      }
      
      console.log('🧹 User recognition data cleared');
    } catch (error) {
      console.error('❌ Error clearing user recognition:', error);
    }
  }

  /**
   * Get greeting for user based on recognition status
   */
  async getPersonalizedGreeting(userId = null, currentUserProfile = null) {
    try {
      let userName = null;
      let isReturningUser = false;

      // If user is authenticated, use their profile
      if (userId && currentUserProfile) {
        userName = currentUserProfile.full_name;
        isReturningUser = await this.isUserRecognized(userId);
      } else if (userId) {
        // User is authenticated but no profile loaded, check recognition
        const recognizedInfo = await this.getRecognizedUserInfo(userId);
        if (recognizedInfo) {
          userName = recognizedInfo.fullName;
          isReturningUser = true;
        }
      } else {
        // User not authenticated, check for last user on device
        const lastUser = await this.getLastUser();
        if (lastUser) {
          userName = lastUser.fullName;
          isReturningUser = true;
        }
      }

      // Generate appropriate greeting
      const timeOfDay = this.getTimeOfDay();
      
      if (userName) {
        // Extract display name properly (handles "User 9916" correctly)
        const displayName = this.extractDisplayName(userName);

        if (isReturningUser && !userId) {
          // Not authenticated but recognized
          return `Welcome back, ${displayName}`;
        } else {
          // Authenticated user
          return `Good ${timeOfDay}, ${displayName}`;
        }
      }

      // Default greeting for unknown users
      return `Good ${timeOfDay}`;
    } catch (error) {
      console.error('❌ Error getting personalized greeting:', error);
      return `Good ${this.getTimeOfDay()}`;
    }
  }

  /**
   * Get time of day for greeting
   */
  getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  }

  /**
   * Extract display name from full name, handling generated names properly
   * @param {string} fullName - Full name from profile
   * @returns {string} - Display name for greeting
   */
  extractDisplayName(fullName) {
    if (!fullName || typeof fullName !== 'string') {
      return 'Friend';
    }

    // Handle generated names like "User 9916" - use the full name
    if (fullName.match(/^User \d+$/)) {
      return fullName; // Return "User 9916" instead of just "User"
    }

    // Handle other generated patterns
    if (fullName.match(/^JiraniPay User$/)) {
      return 'Friend'; // More friendly than "JiraniPay"
    }

    // For real names, use first name only
    const nameParts = fullName.trim().split(' ');
    if (nameParts.length > 1) {
      // Check if it looks like a real first name (not just "User")
      const firstName = nameParts[0];
      if (firstName.length > 1 && /^[A-Za-z]/.test(firstName) && firstName !== 'User') {
        return firstName;
      }
    }

    // If it's a single word or doesn't look like a real name, return as-is
    return fullName;
  }

  /**
   * Initialize service and check for returning users
   */
  async initialize() {
    try {
      console.log('🔧 Initializing Device User Recognition Service...');
      
      const lastUser = await this.getLastUser();
      if (lastUser) {
        console.log('👋 Welcome back! Last user found:', lastUser.fullName);
      } else {
        console.log('👤 No previous users found on this device');
      }
      
      console.log('✅ Device User Recognition Service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing Device User Recognition Service:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new DeviceUserRecognitionService();
