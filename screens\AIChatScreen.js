import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import aiChatService from '../services/aiChatService';

const AIChatScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [user, setUser] = useState(null);
  const [showQuickReplies, setShowQuickReplies] = useState(true);
  
  const flatListRef = useRef(null);
  const typingAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    console.log('🚀 AIChatScreen: Component mounted, starting initialization...');
    initializeChat();
  }, []);

  useEffect(() => {
    if (isTyping) {
      startTypingAnimation();
    } else {
      stopTypingAnimation();
    }
  }, [isTyping]);

  const initializeChat = async () => {
    try {
      console.log('🤖 AIChatScreen: Initializing advanced AI chat...');
      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        console.log('❌ AIChatScreen: No current user found');
        Alert.alert(t('error'), t('support.pleaseLogInToUseAiChat'));
        navigation.goBack();
        return;
      }

      console.log('✅ AIChatScreen: Current user found:', currentUser.full_name);
      setUser(currentUser);
      
      console.log('🔄 AIChatScreen: Initializing AI chat service...');
      const result = await aiChatService.initializeChat(currentUser.id);
      console.log('📊 AIChatScreen: AI service result:', result);
      if (result.success) {
        console.log('✅ AIChatScreen: Chat initialized successfully, messages:', result.history.length);
        setMessages(result.history);
      } else {
        console.log('❌ AIChatScreen: Failed to initialize chat:', result.error);
        Alert.alert(t('error'), t('support.failedToInitializeChat'));
      }
    } catch (error) {
      console.error('❌ Error initializing chat:', error);
      Alert.alert(t('error'), t('support.failedToStartChatSession'));
    } finally {
      setIsLoading(false);
    }
  };

  const startTypingAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(typingAnimation, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(typingAnimation, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopTypingAnimation = () => {
    typingAnimation.stopAnimation();
    typingAnimation.setValue(0);
  };

  const sendMessage = async (messageText = inputText) => {
    if (!messageText.trim() || !user) return;

    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      setInputText('');
      setIsTyping(true);
      setShowQuickReplies(false);

      const result = await aiChatService.sendMessage(user.id, messageText.trim());

      if (result.success) {
        // Get the updated chat history from the service to ensure consistency
        const updatedHistory = aiChatService.getChatHistory();
        setMessages([...updatedHistory]);

        // Show quick replies if AI response includes them
        if (result.aiMessage.quickReplies) {
          setShowQuickReplies(true);
        }

        // Scroll to bottom
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } else {
        Alert.alert(t('error'), t('support.failedToSendMessagePleaseTryAgain'));
      }
    } catch (error) {
      console.error('❌ Error sending message:', error);
      Alert.alert(t('error'), t('support.failedToSendMessage'));
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickReply = (replyText) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    sendMessage(replyText);
  };

  const clearChat = () => {
    Alert.alert(t('support.clearChatHistory'), t('support.areYouSureYouWantToClearAllChatMessagesThisActionC'),
      [
        { text: t('cancel'), style: 'cancel' },
        {
          text: t('support.clear'),
          style: 'destructive',
          onPress: async () => {
            const result = await aiChatService.clearChatHistory(user.id);
            if (result.success) {
              setMessages([]);
              initializeChat(); // Reinitialize with welcome message
            }
          }
        }
      ]
    );
  };

  const renderMessage = ({ item, index }) => {
    const isUser = item.sender === 'user';
    const isLastMessage = index === messages.length - 1;

    return (
      <View style={styles.messageWrapper}>
        {/* Main Message Row */}
        <View style={[
          styles.messageRow,
          isUser ? styles.userMessageRow : styles.aiMessageRow
        ]}>
          {/* AI Avatar (left side) */}
          {!isUser && (
            <View style={styles.avatarContainer}>
              <View style={styles.aiAvatar}>
                <Ionicons name="chatbubble-ellipses" size={18} color={Colors.neutral.white} />
              </View>
            </View>
          )}

          {/* Message Content */}
          <View style={[
            styles.messageContent,
            isUser ? styles.userMessageContent : styles.aiMessageContent
          ]}>
            {/* Message Bubble */}
            <View style={[
              styles.messageBubble,
              isUser ? styles.userBubble : styles.aiBubble
            ]}>
              <Text style={[
                styles.messageText,
                isUser ? styles.userMessageText : styles.aiMessageText
              ]}>
                {item.text}
              </Text>
            </View>

            {/* Timestamp */}
            <Text style={[
              styles.messageTime,
              isUser ? styles.userMessageTime : styles.aiMessageTime
            ]}>
              {new Date(item.timestamp).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </Text>
          </View>

          {/* User Avatar (right side) */}
          {isUser && (
            <View style={styles.avatarContainer}>
              <View style={styles.userAvatar}>
                <Ionicons name="person" size={18} color={Colors.neutral.white} />
              </View>
            </View>
          )}
        </View>

        {/* Quick Replies Section - Completely Redesigned */}
        {!isUser && item.quickReplies && isLastMessage && (
          <View style={styles.quickRepliesSection}>
            <View style={styles.quickRepliesHeader}>
              <Ionicons name="flash" size={16} color={Colors.primary.main} />
              <Text style={styles.quickRepliesLabel}>{t('support.quickActions')}</Text>
            </View>
            <View style={styles.quickRepliesContainer}>
              {item.quickReplies.map((reply, replyIndex) => (
                <TouchableOpacity
                  key={replyIndex}
                  style={styles.quickReplyButton}
                  onPress={() => handleQuickReply(reply)}
                  activeOpacity={0.8}
                >
                  <Text style={styles.quickReplyText}>{reply}</Text>
                  <Ionicons name="chevron-forward" size={16} color={Colors.primary.main} />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View style={styles.typingContainer}>
        <View style={styles.aiAvatar}>
          <Ionicons name="chatbubble-ellipses" size={16} color={Colors.primary.main} />
        </View>
        <View style={styles.typingBubble}>
          <Animated.View style={[
            styles.typingDot,
            { opacity: typingAnimation }
          ]} />
          <Animated.View style={[
            styles.typingDot,
            { 
              opacity: typingAnimation,
              transform: [{ 
                scale: typingAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.2]
                })
              }]
            }
          ]} />
          <Animated.View style={[
            styles.typingDot,
            { opacity: typingAnimation }
          ]} />
          <Text style={styles.typingText}>{t('support.aiIsTyping')}</Text>
        </View>
      </View>
    );
  };

  const renderQuickStartOptions = () => {
    if (messages.length > 1 || !showQuickReplies) return null;

    const quickHelpCategories = [
      {
        id: 'wallet',
        icon: 'wallet',
        title: 'Wallet',
        subtitle: 'Balance & transactions',
        action: '💰 Check my balance',
        color: Colors.primary.main
      },
      {
        id: 'send',
        icon: 'paper-plane',
        title: 'Send Money',
        subtitle: 'Transfer to anyone',
        action: '📤 Send money',
        color: '#4CAF50'
      },
      {
        id: 'bills',
        icon: 'receipt',
        title: 'Pay Bills',
        subtitle: 'UMEME, Water, Airtime',
        action: 'Pay bills',
        color: '#FF9800'
      },
      {
        id: 'security',
        icon: 'shield-checkmark',
        title: 'Security',
        subtitle: 'Account protection',
        action: 'Security',
        color: '#9C27B0'
      },
      {
        id: 'qr',
        icon: 'qr-code',
        title: 'QR Scanner',
        subtitle: 'Quick payments',
        action: '📱 QR scanner',
        color: '#2196F3'
      },
      {
        id: 'help',
        icon: 'help-circle',
        title: 'Main Menu',
        subtitle: 'All features',
        action: 'Main Menu',
        color: '#607D8B'
      }
    ];

    return (
      <View style={styles.quickStartContainer}>
        <View style={styles.quickStartHeader}>
          <Ionicons name="flash" size={20} color={Colors.primary.main} />
          <Text style={styles.quickStartTitle}>{t('support.quickHelp')}</Text>
        </View>
        <Text style={styles.quickStartSubtitle}>
          {t('support.chooseWhatYoudLikeHelpWith')}
        </Text>
        <View style={styles.quickStartGrid}>
          {quickHelpCategories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={[styles.quickStartCard, { borderLeftColor: category.color }]}
              onPress={() => handleQuickReply(category.action)}
              activeOpacity={0.8}
            >
              <View style={styles.quickStartCardHeader}>
                <View style={[styles.quickStartIcon, { backgroundColor: category.color + '15' }]}>
                  <Ionicons
                    name={category.icon}
                    size={20}
                    color={category.color}
                  />
                </View>
                <View style={styles.quickStartCardContent}>
                  <Text style={styles.quickStartCardTitle}>{category.title}</Text>
                  <Text style={styles.quickStartCardSubtitle}>{category.subtitle}</Text>
                </View>
              </View>
              <Ionicons
                name="chevron-forward"
                size={16}
                color={Colors.neutral.warmGray}
              />
            </TouchableOpacity>
          ))}
        </View>
        <View style={styles.quickStartFooter}>
          <Text style={styles.quickStartFooterText}>
            {t('support.tipYouCanAlsoTypeYourQuestionsNaturally')}
          </Text>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Ionicons name="chatbubble-ellipses" size={48} color={Colors.primary.main} />
          <Text style={styles.loadingText}>{t('support.startingAiAssistant')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>{t('support.jiranipayAiAssistant')}</Text>
            <Text style={styles.headerSubtitle}>
              {isTyping ? t('support.aiIsThinking') : t('support.readyToHelpWithJiraniPay')}
            </Text>
          </View>
          <TouchableOpacity 
            style={styles.clearButton}
            onPress={clearChat}
            activeOpacity={0.7}
          >
            <Ionicons name="refresh" size={20} color={Colors.neutral.white} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Messages */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
          ListFooterComponent={() => (
            <>
              {renderTypingIndicator()}
              {renderQuickStartOptions()}
            </>
          )}
        />

        {/* Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              value={inputText}
              onChangeText={setInputText}
              placeholder="Type your message..."
              placeholderTextColor={Colors.neutral.warmGray}
              multiline
              maxLength={500}
              returnKeyType="send"
              onSubmitEditing={() => sendMessage()}
              editable={!isTyping}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!inputText.trim() || isTyping) && styles.sendButtonDisabled
              ]}
              onPress={() => sendMessage()}
              disabled={!inputText.trim() || isTyping}
              activeOpacity={0.7}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={(!inputText.trim() || isTyping) ? Colors.neutral.warmGray : Colors.neutral.white} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 16,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginTop: 2,
  },
  clearButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  messagesList: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  messagesContent: {
    paddingTop: 16,
    paddingBottom: 24,
    paddingHorizontal: 16,
  },
  messageWrapper: {
    marginBottom: 20,
    width: '100%',
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  userMessageRow: {
    justifyContent: 'flex-end',
  },
  aiMessageRow: {
    justifyContent: 'flex-start',
  },
  avatarContainer: {
    marginHorizontal: 8,
  },
  messageContent: {
    flex: 1,
    maxWidth: '75%',
  },
  userMessageContent: {
    alignItems: 'flex-end',
  },
  aiMessageContent: {
    alignItems: 'flex-start',
  },
  aiAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.primary.main,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  userAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.neutral.charcoal,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  messageBubble: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 18,
    marginBottom: 4,
  },
  userBubble: {
    backgroundColor: Colors.primary.main,
    borderBottomRightRadius: 4,
    elevation: 1,
    shadowColor: Colors.primary.main,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  aiBubble: {
    backgroundColor: theme.colors.surface,
    borderBottomLeftRadius: 4,
    borderWidth: 1,
    borderColor: theme.colors.border,
    elevation: 1,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
  },
  messageText: {
    fontSize: 15,
    lineHeight: 20,
    fontWeight: '400',
  },
  userMessageText: {
    color: Colors.neutral.white,
  },
  aiMessageText: {
    color: theme.colors.text,
  },
  messageTime: {
    fontSize: 11,
    marginTop: 6,
    fontWeight: '400',
  },
  userMessageTime: {
    color: Colors.neutral.white,
    opacity: 0.7,
    textAlign: 'right',
  },
  aiMessageTime: {
    color: Colors.neutral.warmGray,
    opacity: 0.8,
    textAlign: 'left',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.primary.main,
    marginRight: 4,
  },
  typingText: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  quickRepliesSection: {
    marginTop: 12,
    marginLeft: 44, // Align with AI message content
    marginRight: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 12,
    elevation: 1,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickRepliesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  quickRepliesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.textSecondary,
    marginLeft: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  quickRepliesContainer: {
    gap: 8,
  },
  quickReplyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.primary.main + '08',
    borderColor: Colors.primary.main + '30',
    borderWidth: 1,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderRadius: 8,
    marginBottom: 6,
  },
  quickReplyText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '500',
    flex: 1,
  },
  quickStartContainer: {
    padding: 20,
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    marginHorizontal: 16,
    marginVertical: 12,
    elevation: 3,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray + '50',
  },
  quickStartHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  quickStartTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginLeft: 8,
  },
  quickStartSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginBottom: 16,
  },
  quickStartGrid: {
    gap: 12,
  },
  quickStartCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    borderLeftWidth: 4,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray + '30',
    elevation: 1,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
  },
  quickStartCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  quickStartIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  quickStartCardContent: {
    flex: 1,
  },
  quickStartCardTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  quickStartCardSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  quickStartFooter: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray + '30',
  },
  quickStartFooterText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: Colors.neutral.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: Colors.neutral.lightGray,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    backgroundColor: Colors.neutral.warmGray,
  },
});

export default AIChatScreen;
