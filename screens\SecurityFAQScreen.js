import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SecurityFAQScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [expandedFAQ, setExpandedFAQ] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  const callSupport = (phoneNumber) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(t('callSupport'), t('doYouWantToCallPhonenumber'),
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Call',
          onPress: () => Linking.openURL(`tel:${phoneNumber}`)
        }
      ]
    );
  };

  const emailSupport = (email) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    const subject = 'JiraniPay Security FAQ Support Request';
    const body = 'Hello JiraniPay Support Team,\n\nI have a security question that wasn\'t answered in the FAQ:\n\n[Please describe your question here]\n\nThank you.';

    Alert.alert(t('emailSupport'), t('doYouWantToSendAnEmailToEmail'),
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Email',
          onPress: () => Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`)
        }
      ]
    );
  };

  const faqCategories = [
    {
      id: 'account',
      title: 'Account Security',
      icon: 'shield-checkmark',
      color: Colors.primary.main,
      faqs: [
        {
          id: 'account-1',
          question: 'How do I keep my JiraniPay account secure?',
          answer: 'Enable biometric authentication, use a strong 6-digit PIN, never share your credentials, log out when not using the app, and monitor your account regularly for suspicious activity.'
        },
        {
          id: 'account-2',
          question: 'What should I do if I suspect unauthorized access?',
          answer: 'Immediately contact our security hotline at +************, change your PIN, review recent transactions, and report any unauthorized activities. We will investigate and secure your account.'
        },
        {
          id: 'account-3',
          question: 'How often should I change my PIN?',
          answer: 'Change your PIN every 3-6 months or immediately if you suspect it has been compromised. Avoid using easily guessable numbers like birthdays or sequential digits.'
        }
      ]
    },
    {
      id: 'biometric',
      title: 'Biometric Authentication',
      icon: 'finger-print',
      color: Colors.status.success,
      faqs: [
        {
          id: 'biometric-1',
          question: 'Why is my biometric authentication not working?',
          answer: 'Ensure your device supports biometrics, biometric authentication is enabled in device settings, your fingerprint/face is properly registered, and the sensor is clean and dry.'
        },
        {
          id: 'biometric-2',
          question: 'Is biometric data stored on JiraniPay servers?',
          answer: 'No, biometric data is stored securely on your device using hardware-level security. JiraniPay never receives or stores your biometric information on our servers.'
        },
        {
          id: 'biometric-3',
          question: 'Can I use biometric authentication on multiple devices?',
          answer: 'Yes, you can enable biometric authentication on each device where you install JiraniPay, provided the device supports biometric features.'
        }
      ]
    },
    {
      id: 'pin',
      title: 'PIN & Password Management',
      icon: 'key',
      color: Colors.accent.gold,
      faqs: [
        {
          id: 'pin-1',
          question: 'What makes a strong PIN?',
          answer: 'A strong PIN should be 6 digits, not sequential (123456), not repetitive (111111), not your birthday or phone number, and known only to you.'
        },
        {
          id: 'pin-2',
          question: 'I forgot my PIN. How do I reset it?',
          answer: 'Use the "Forgot PIN" option on the login screen. You will need to verify your identity using SMS OTP and answer security questions before setting a new PIN.'
        },
        {
          id: 'pin-3',
          question: 'My account is locked. What should I do?',
          answer: 'Accounts are locked after 5 failed PIN attempts. Wait 30 minutes for automatic unlock, or contact customer support at +************ for immediate assistance.'
        }
      ]
    },
    {
      id: 'transactions',
      title: 'Transaction Security',
      icon: 'card',
      color: Colors.secondary.forest,
      faqs: [
        {
          id: 'transactions-1',
          question: 'How are my transactions protected?',
          answer: 'All transactions use end-to-end encryption, require PIN/biometric authentication, are monitored for fraud in real-time, and comply with Bank of Uganda security standards.'
        },
        {
          id: 'transactions-2',
          question: 'What are the transaction limits?',
          answer: 'Daily limit: UGX 5,000,000, Monthly limit: UGX 50,000,000. Limits may vary based on your KYC verification level. Higher limits available with enhanced verification.'
        },
        {
          id: 'transactions-3',
          question: 'Can I dispute a transaction?',
          answer: 'Yes, report disputed transactions within 60 days. Contact support at +************ or <EMAIL>. We investigate all disputes within 48 hours.'
        }
      ]
    },
    {
      id: 'suspicious',
      title: 'Suspicious Activity',
      icon: 'warning',
      color: Colors.status.error,
      faqs: [
        {
          id: 'suspicious-1',
          question: 'How do I report suspicious activity?',
          answer: 'Immediately call our security hotline +************, email <EMAIL>, or use the in-app reporting feature. Provide transaction details and any relevant information.'
        },
        {
          id: 'suspicious-2',
          question: 'What constitutes suspicious activity?',
          answer: 'Unauthorized transactions, unknown login locations, unexpected account changes, phishing attempts, or any activity you did not initiate or authorize.'
        },
        {
          id: 'suspicious-3',
          question: 'Will I be compensated for fraudulent transactions?',
          answer: 'Yes, if fraud is confirmed and you followed security guidelines, you will receive full compensation. Report incidents immediately for faster resolution.'
        }
      ]
    },
    {
      id: 'twofa',
      title: 'Two-Factor Authentication',
      icon: 'shield',
      color: Colors.primary.main,
      faqs: [
        {
          id: 'twofa-1',
          question: 'How do I enable two-factor authentication?',
          answer: 'Go to Profile → Security Settings → Two-Factor Authentication. Choose SMS or Email verification, enter the verification code sent to you, and confirm activation.'
        },
        {
          id: 'twofa-2',
          question: 'I\'m not receiving verification codes. What should I do?',
          answer: 'Check your phone signal/internet connection, verify your phone number/email is correct, check spam folders, and ensure you haven\'t blocked JiraniPay messages.'
        },
        {
          id: 'twofa-3',
          question: 'Can I change my 2FA method?',
          answer: 'Yes, go to Security Settings → Two-Factor Authentication, disable current method, then enable your preferred method (SMS or Email) with verification.'
        }
      ]
    }
  ];

  const toggleFAQ = (faqId) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const filteredCategories = faqCategories.map(category => ({
    ...category,
    faqs: category.faqs.filter(faq => 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.faqs.length > 0);

  const renderFAQ = (faq, categoryColor) => {
    const isExpanded = expandedFAQ === faq.id;
    
    return (
      <TouchableOpacity
        key={faq.id}
        style={[styles.faqCard, isExpanded && styles.expandedFaqCard]}
        onPress={() => toggleFAQ(faq.id)}
        activeOpacity={0.7}
      >
        <View style={styles.faqHeader}>
          <View style={styles.faqLeft}>
            <View style={[styles.faqIcon, { backgroundColor: categoryColor + '20' }]}>
              <Ionicons name="help-circle" size={20} color={categoryColor} />
            </View>
            <Text style={styles.faqQuestion}>{faq.question}</Text>
          </View>
          <Ionicons 
            name={isExpanded ? 'chevron-up' : 'chevron-down'} 
            size={20} 
            color={Colors.neutral.warmGray} 
          />
        </View>
        
        {isExpanded && (
          <View style={styles.faqAnswer}>
            <Text style={styles.faqAnswerText}>{faq.answer}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderCategory = (category) => {
    if (category.faqs.length === 0) return null;
    
    return (
      <View key={category.id} style={styles.categorySection}>
        <View style={styles.categoryHeader}>
          <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
            <Ionicons name={category.icon} size={24} color={category.color} />
          </View>
          <Text style={styles.categoryTitle}>{category.title}</Text>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryBadgeText}>{category.faqs.length}</Text>
          </View>
        </View>
        
        {category.faqs.map(faq => renderFAQ(faq, category.color))}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('securityFaq')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('frequentlyAskedSecurityQuestions')}
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search */}
        <View style={styles.section}>
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color={Colors.neutral.warmGray} style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search security questions..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={Colors.neutral.warmGray}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                style={styles.clearButton}
              >
                <Ionicons name="close-circle" size={20} color={Colors.neutral.warmGray} />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* FAQ Summary */}
        <View style={styles.section}>
          <View style={styles.summaryCard}>
            <View style={styles.summaryIcon}>
              <Ionicons name="help-circle" size={32} color={Colors.primary.main} />
            </View>
            <View style={styles.summaryText}>
              <Text style={styles.summaryTitle}>{t('securityFaq')}</Text>
              <Text style={styles.summaryCount}>
                {filteredCategories.reduce((total, cat) => total + cat.faqs.length, 0)} Questions
              </Text>
              <Text style={styles.summaryDescription}>
                {t('findAnswersToCommonSecurityQuestions')}
              </Text>
            </View>
          </View>
        </View>

        {/* FAQ Categories */}
        {filteredCategories.length > 0 ? (
          filteredCategories.map(renderCategory)
        ) : (
          <View style={styles.section}>
            <View style={styles.noResultsCard}>
              <Ionicons name="search" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.noResultsTitle}>{t('noResultsFound')}</Text>
              <Text style={styles.noResultsDescription}>
                {t('tryDifferentKeywordsOrBrowseAllCategories')}
              </Text>
              <TouchableOpacity
                style={styles.clearSearchButton}
                onPress={() => setSearchQuery('')}
                activeOpacity={0.7}
              >
                <Text style={styles.clearSearchText}>{t('clearSearch')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Contact Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('stillNeedHelp')}</Text>
          
          <TouchableOpacity
            style={styles.contactCard}
            activeOpacity={0.7}
            onPress={() => callSupport('+************')}
          >
            <Ionicons name="call" size={24} color={Colors.status.error} />
            <View style={styles.contactText}>
              <Text style={styles.contactTitle}>{t('callSupport')}</Text>
              <Text style={styles.contactNumber}>{t('************')}</Text>
              <Text style={styles.contactDescription}>{t('247SecurityAssistance')}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.contactCard}
            activeOpacity={0.7}
            onPress={() => emailSupport('<EMAIL>')}
          >
            <Ionicons name="mail" size={24} color={Colors.primary.main} />
            <View style={styles.contactText}>
              <Text style={styles.contactTitle}>{t('emailSupport')}</Text>
              <Text style={styles.contactEmail}>{t('customercarejiranipaycom')}</Text>
              <Text style={styles.contactDescription}>{t('nonurgentInquiries')}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    paddingVertical: 16,
  },
  clearButton: {
    padding: 4,
  },
  summaryCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.primary.main + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  summaryText: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  summaryCount: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  summaryDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  categorySection: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    flex: 1,
  },
  categoryBadge: {
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  categoryBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  faqCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  expandedFaqCard: {
    borderWidth: 1,
    borderColor: Colors.primary.main + '30',
  },
  faqHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  faqLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  faqIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    flex: 1,
  },
  faqAnswer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  faqAnswerText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
    textAlign: 'justify',
  },
  noResultsCard: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
  },
  noResultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginBottom: 20,
  },
  clearSearchButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  clearSearchText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 15,
  },
  contactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  contactText: {
    flex: 1,
    marginLeft: 12,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.status.error,
    marginBottom: 4,
  },
  contactEmail: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  contactDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
});

export default SecurityFAQScreen;
