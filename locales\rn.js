/**
 * <PERSON><PERSON><PERSON> (rn) translations for JiraniPay
 * Complete translation coverage for all app features
 */

export default {
  // Common UI elements
  common: {
    continue: '<PERSON><PERSON><PERSON>',
    cancel: '<PERSON><PERSON><PERSON>',
    back: '<PERSON><PERSON><PERSON> inyuma',
    next: '<PERSON><PERSON><PERSON><PERSON>',
    done: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    loading: '<PERSON><PERSON><PERSON><PERSON><PERSON>...',
    error: 'I<PERSON><PERSON>',
    success: 'Vyagen<PERSON> neza',
    retry: 'Ongera ugerageze',
    close: '<PERSON><PERSON>',
    save: '<PERSON><PERSON>',
    edit: 'Hindura',
    delete: '<PERSON><PERSON>',
    confirm: 'Emeza',
    yes: '<PERSON>go',
    no: 'Oya',
    ok: 'Ni vyiza',

    // Financial features
    savings: 'Kubika'
  },

  // Authentication & Onboarding
  auth: {
    welcomeBack: 'Murakaza neza',
    goodMorning: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    goodAfternoon: 'M<PERSON><PERSON>we',
    goodEvening: '<PERSON><PERSON><PERSON>',
    chooseLoginMethod: 'Hitamo uburyo bwo kwinjira',
    otpLogin: 'Kwinjira ukoresheje OTP',
    passwordLogin: 'Kwinjira ukoresheje ijambo ry\'ibanga',
    enterPhoneNumber: '<PERSON>ra nimero ya telefoni',
    enterPassword: 'Shira ijambo ry\'ibanga',
    forgotPassword: 'Wibagiwe ijambo ry\'ibanga?',
    sendOTP: 'Kohereza OTP',
    login: 'Injira',
    verifyOTP: 'Emeza OTP',
    resendOTP: 'Ongera ukohereze OTP',
    resendOTPIn: 'Ongera ukohereze OTP mu',
    dontHaveAccount: 'Nta konti ufise?',
    signUp: 'Iyandikishe',
    useBiometric: 'Koresha kwinjira kwa biometric'
  },

  // Time-based greetings with names
  greetings: {
    goodMorningName: 'Mwaramutse, {name}',
    goodAfternoonName: 'Mwiriwe, {name}',
    goodEveningName: 'Muramuke, {name}',
    goodMorning: 'Mwaramutse',
    goodAfternoon: 'Mwiriwe',
    goodEvening: 'Muramuke'
  },

  // Dashboard & Home
  dashboard: {
    title: 'Ikivaho',
    welcome: 'Murakaza neza',
    balance: 'Amafaranga asigaye',
    totalBalance: 'Amafaranga yose asigaye',
    availableBalance: 'Amafaranga aboneka',
    quickActions: 'Ibikorwa vyihuse',
    recentTransactions: 'Ibikorwa vya vuba',
    viewAll: 'Raba vyose',
    noTransactions: 'Nta bikorwa',
    sendMoney: 'Kohereza amafaranga',
    payBills: 'Kwishyura amadeni',
    topUp: 'Kuzuza',
    scanQR: 'Gusoma QR',
    savings: 'Kubika',
    analytics: 'Isesengura'
  },

  // Wallet & Transactions
  wallet: {
    title: 'Igikoni',
    myWallet: 'Igikoni canje',
    balance: 'Amafaranga asigaye',
    transactions: 'Ibikorwa',
    transactionHistory: 'Amateka y\'ibikorwa',
    sendMoney: 'Kohereza amafaranga',
    receiveMoney: 'Kwakira amafaranga',
    topUp: 'Kuzuza igikoni',
    withdraw: 'Gukura',
    transfer: 'Kwimura',
    deposit: 'Kubika',
    payment: 'Kwishyura',
    refund: 'Kugarura',
    fee: 'Ikiguzi',
    total: 'Igiteranyo',
    amount: 'Umubare',
    recipient: 'Uwakira',
    sender: 'Uwohereza',
    reference: 'Indango',
    description: 'Ibisobanuro',
    date: 'Itariki',
    time: 'Igihe',
    status: 'Uko bimeze',
    type: 'Ubwoko',
    category: 'Icyiciro',
    provider: 'Utanga serivisi',
    account: 'Konti',
    accountNumber: 'Nimero ya konti',
    phoneNumber: 'Nimero ya telefoni',
    transactionId: 'Indangamuntu y\'igikorwa',
    receiptNumber: 'Nimero y\'inyemezabuguzi',
    confirmTransaction: 'Emeza igikorwa',
    transactionSuccessful: 'Igikorwa cyagenze neza',
    transactionFailed: 'Igikorwa cyanze',
    insufficientFunds: 'Amafaranga ntahagije',
    dailyLimitExceeded: 'Urugero rwa buri musi rwarenze',
    monthlyLimitExceeded: 'Urugero rwa buri kwezi rwarenze',
    invalidAmount: 'Umubare utari wo',
    minimumAmount: 'Umubare muto',
    maximumAmount: 'Umubare munini',
    transactionLimits: 'Imipaka y\'ibikorwa',
    dailyLimit: 'Urugero rwa buri musi',
    monthlyLimit: 'Urugero rwa buri kwezi',
    remainingDaily: 'Ibisigaye vya buri musi',
    remainingMonthly: 'Ibisigaye vya buri kwezi'
  },

  // Currency & Formatting
  currency: {
    ugx: 'Ishilingi y\'Ubugande',
    kes: 'Ishilingi ya Kenya',
    tzs: 'Ishilingi ya Tanzaniya',
    rwf: 'Ifaranga y\'u Rwanda',
    bif: 'Ifaranga y\'Uburundi',
    etb: 'Birr y\'Ubwetiyopiya',
    usd: 'Idolari y\'Amerika',
    eur: 'Euro',
    gbp: 'Ipawundi y\'Ubwongereza',
    selectCurrency: 'Hitamo ifaranga',
    preferredCurrency: 'Ifaranga ukunda',
    currencySettings: 'Igenamiterere ry\'ifaranga',
    exchangeRate: 'Igiciro cyo guhana',
    convertedAmount: 'Umubare wahinduwe',
    conversionRate: 'Igiciro cyo guhindura',
    lastUpdated: 'Vyavuguruwe vya nyuma',
    updateRates: 'Vugurura ibiciro',
    rateUnavailable: 'Igiciro ntikiboneka',
    conversionError: 'Ikosa mu guhindura',
    currencyUpdated: 'Ifaranga yahinduwe kuri {currency}',
    updateError: 'Guhindura ifaranga vyanze. Ongera ugerageze.'
  },

  // Settings
  settings: {
    languageAndCurrency: 'Ururimi n\'Ifaranga',
    language: 'Ururimi',
    languageDescription: 'Hitamo ururimi ukunda kuri porogaramu',
    languageInfo: 'Porogaramu izongera itangire kugira ngo ikoreshe impinduka z\'ururimi',
    languageUpdated: 'Ururimi rwavuguruwe neza',
    languageUpdateError: 'Guvugurura ururimi vyanze. Ongera ugerageze.',
    currencyDescription: 'Hitamo ifaranga ukunda mu bikorwa',
    currencyInfo: 'Amabare yose azagaragara mu faranga ukunda hamwe no guhindura mu gihe nyacyo'
  }
};
