#!/usr/bin/env node

/**
 * Create Admin User Script
 * Creates an admin user for testing the admin panel
 */

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcryptjs');
const readline = require('readline');

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'your-supabase-url';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-key';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function createAdminUser() {
  try {
    console.log('🏦 JiraniPay Admin User Creation Script\n');

    // Get admin details
    const email = await question('Enter admin email: ') || '<EMAIL>';
    const fullName = await question('Enter admin full name: ') || 'System Administrator';
    const phoneNumber = await question('Enter admin phone number: ') || '+256700000000';
    const password = await question('Enter admin password: ') || 'AdminPassword123!';
    const role = await question('Enter admin role (super_admin/admin/moderator/support): ') || 'admin';

    console.log('\n⏳ Creating admin user...\n');

    // Initialize Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    // Create auth user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName,
        phone_number: phoneNumber,
        role: 'admin'
      }
    });

    if (authError) {
      console.error('❌ Failed to create auth user:', authError.message);
      return;
    }

    console.log('✅ Auth user created:', authUser.user.id);

    // Create user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        user_id: authUser.user.id,
        full_name: fullName,
        email: email,
        phone_number: phoneNumber,
        country_code: 'UG',
        is_verified: true,
        kyc_status: 'verified'
      })
      .select()
      .single();

    if (profileError) {
      console.error('❌ Failed to create user profile:', profileError.message);
      return;
    }

    console.log('✅ User profile created');

    // Define admin permissions based on role
    let permissions = [];
    switch (role) {
      case 'super_admin':
        permissions = ['*']; // All permissions
        break;
      case 'admin':
        permissions = [
          'dashboard:read',
          'users:read', 'users:update',
          'transactions:read', 'transactions:update',
          'monitoring:read',
          'analytics:read',
          'reports:create'
        ];
        break;
      case 'moderator':
        permissions = [
          'dashboard:read',
          'users:read',
          'transactions:read',
          'monitoring:read'
        ];
        break;
      case 'support':
        permissions = [
          'dashboard:read',
          'users:read',
          'transactions:read'
        ];
        break;
      default:
        permissions = ['dashboard:read'];
    }

    // Create admin user record
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .insert({
        user_id: authUser.user.id,
        email: email,
        role: role,
        permissions: permissions,
        is_active: true,
        created_by: authUser.user.id // Self-created
      })
      .select()
      .single();

    if (adminError) {
      console.error('❌ Failed to create admin user:', adminError.message);
      return;
    }

    console.log('✅ Admin user created');

    // Create wallet for admin user
    const { data: wallet, error: walletError } = await supabase
      .from('wallets')
      .insert({
        user_id: authUser.user.id,
        balance: 0,
        available_balance: 0,
        currency: 'UGX',
        is_active: true
      })
      .select()
      .single();

    if (walletError) {
      console.error('❌ Failed to create wallet:', walletError.message);
      return;
    }

    console.log('✅ Wallet created');

    // Display summary
    console.log('\n🎉 Admin user created successfully!\n');
    console.log('📋 Admin User Details:');
    console.log(`   User ID: ${authUser.user.id}`);
    console.log(`   Email: ${email}`);
    console.log(`   Full Name: ${fullName}`);
    console.log(`   Phone: ${phoneNumber}`);
    console.log(`   Role: ${role}`);
    console.log(`   Permissions: ${permissions.join(', ')}`);
    console.log(`   Wallet ID: ${wallet.id}`);

    console.log('\n🔐 Login Credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);

    console.log('\n🌐 Test the admin panel at:');
    console.log('   http://localhost:3000/admin-test.html');

    console.log('\n📝 API Endpoints to test:');
    console.log('   GET /api/v1/admin/dashboard');
    console.log('   GET /api/v1/admin/users');
    console.log('   GET /api/v1/admin/transactions');
    console.log('   GET /api/v1/admin/monitoring/health');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    rl.close();
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🏦 JiraniPay Admin User Creation Script

Usage: node create-admin-user.js [options]

Options:
  --help, -h     Show this help message

Environment Variables:
  SUPABASE_URL              Supabase project URL
  SUPABASE_SERVICE_ROLE_KEY Supabase service role key

Examples:
  node create-admin-user.js
  SUPABASE_URL=https://xxx.supabase.co node create-admin-user.js

The script will prompt you for admin user details interactively.
`);
  process.exit(0);
}

// Validate environment variables
if (!SUPABASE_URL || SUPABASE_URL === 'your-supabase-url') {
  console.error('❌ Please set SUPABASE_URL environment variable');
  console.log('   Example: export SUPABASE_URL=https://your-project.supabase.co');
  process.exit(1);
}

if (!SUPABASE_SERVICE_KEY || SUPABASE_SERVICE_KEY === 'your-service-key') {
  console.error('❌ Please set SUPABASE_SERVICE_ROLE_KEY environment variable');
  console.log('   Example: export SUPABASE_SERVICE_ROLE_KEY=your-service-role-key');
  process.exit(1);
}

// Run the script
createAdminUser().catch(console.error);
