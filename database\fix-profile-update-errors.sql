-- =====================================================
-- FIX PROFILE UPDATE ERRORS
-- =====================================================
-- This script fixes the profile update errors by adding missing columns
-- and ensuring schema consistency

-- =====================================================
-- STEP 1: ADD MISSING COLUMNS TO PROFILES TABLE
-- =====================================================

-- Add date_of_birth column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'profiles' 
        AND column_name = 'date_of_birth'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN date_of_birth DATE;
        RAISE NOTICE 'Added date_of_birth column to profiles table';
    ELSE
        RAISE NOTICE 'date_of_birth column already exists in profiles table';
    END IF;
END $$;

-- Add phone_number column as alias to phone (for compatibility)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'profiles' 
        AND column_name = 'phone_number'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN phone_number TEXT;
        RAISE NOTICE 'Added phone_number column to profiles table';
        
        -- Copy existing phone data to phone_number
        UPDATE public.profiles 
        SET phone_number = phone 
        WHERE phone IS NOT NULL;
        
        RAISE NOTICE 'Copied phone data to phone_number column';
    ELSE
        RAISE NOTICE 'phone_number column already exists in profiles table';
    END IF;
END $$;

-- Add gender column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'profiles' 
        AND column_name = 'gender'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN gender TEXT CHECK (gender IN ('male', 'female', 'other', 'prefer_not_to_say'));
        RAISE NOTICE 'Added gender column to profiles table';
    ELSE
        RAISE NOTICE 'gender column already exists in profiles table';
    END IF;
END $$;

-- Add address column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'profiles' 
        AND column_name = 'address'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN address TEXT;
        RAISE NOTICE 'Added address column to profiles table';
    ELSE
        RAISE NOTICE 'address column already exists in profiles table';
    END IF;
END $$;

-- =====================================================
-- STEP 2: CREATE INDEXES FOR NEW COLUMNS
-- =====================================================

-- Index for date_of_birth (for age-based queries)
CREATE INDEX IF NOT EXISTS idx_profiles_date_of_birth 
ON public.profiles(date_of_birth);

-- Index for gender (for demographic queries)
CREATE INDEX IF NOT EXISTS idx_profiles_gender 
ON public.profiles(gender);

-- Unique index for phone_number (if not exists)
CREATE UNIQUE INDEX IF NOT EXISTS idx_profiles_phone_number_unique
ON public.profiles(phone_number)
WHERE phone_number IS NOT NULL;

-- =====================================================
-- STEP 3: CREATE TRIGGER TO SYNC PHONE AND PHONE_NUMBER
-- =====================================================

-- Function to sync phone and phone_number columns
CREATE OR REPLACE FUNCTION sync_phone_columns()
RETURNS TRIGGER AS $$
BEGIN
    -- If phone_number is updated, sync to phone
    IF NEW.phone_number IS DISTINCT FROM OLD.phone_number THEN
        NEW.phone = NEW.phone_number;
    END IF;
    
    -- If phone is updated, sync to phone_number
    IF NEW.phone IS DISTINCT FROM OLD.phone THEN
        NEW.phone_number = NEW.phone;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync phone columns
DROP TRIGGER IF EXISTS sync_phone_columns_trigger ON public.profiles;
CREATE TRIGGER sync_phone_columns_trigger
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_phone_columns();

-- =====================================================
-- STEP 4: UPDATE RLS POLICIES FOR NEW COLUMNS
-- =====================================================

-- The existing RLS policies should work for the new columns
-- since they're based on the user ID, but let's verify

-- Check if policies exist and are working
DO $$
BEGIN
    -- Test if we can select from profiles (this will fail if RLS is broken)
    PERFORM 1 FROM public.profiles LIMIT 1;
    RAISE NOTICE 'RLS policies are working correctly';
EXCEPTION
    WHEN insufficient_privilege THEN
        RAISE NOTICE 'RLS policies need to be recreated';
    WHEN OTHERS THEN
        RAISE NOTICE 'RLS check completed with: %', SQLERRM;
END $$;

-- =====================================================
-- STEP 5: VERIFY SCHEMA CHANGES
-- =====================================================

-- Display current profiles table schema
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- =====================================================
-- STEP 6: CREATE SAFE UPDATE FUNCTION
-- =====================================================

-- Function to safely update profile with proper validation
CREATE OR REPLACE FUNCTION update_user_profile(
    user_id UUID,
    profile_data JSONB
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
    updated_profile public.profiles%ROWTYPE;
BEGIN
    -- Validate user_id
    IF user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User ID is required'
        );
    END IF;
    
    -- Update profile with only provided fields
    UPDATE public.profiles SET
        full_name = COALESCE((profile_data->>'full_name'), full_name),
        phone = COALESCE((profile_data->>'phone_number'), (profile_data->>'phone'), phone),
        phone_number = COALESCE((profile_data->>'phone_number'), (profile_data->>'phone'), phone_number),
        email = COALESCE((profile_data->>'email'), email),
        date_of_birth = COALESCE((profile_data->>'date_of_birth')::DATE, date_of_birth),
        gender = COALESCE((profile_data->>'gender'), gender),
        address = COALESCE((profile_data->>'address'), address),
        country_code = COALESCE((profile_data->>'country_code'), country_code),
        language_preference = COALESCE((profile_data->>'preferred_language'), language_preference),
        avatar_url = COALESCE((profile_data->>'avatar_url'), avatar_url),
        updated_at = NOW()
    WHERE id = user_id
    RETURNING * INTO updated_profile;
    
    -- Check if update was successful
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Profile not found or no permission to update'
        );
    END IF;
    
    -- Return success response
    SELECT json_build_object(
        'success', true,
        'data', row_to_json(updated_profile),
        'message', 'Profile updated successfully'
    ) INTO result;
    
    RETURN result;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', SQLERRM
        );
END $$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_user_profile(UUID, JSONB) TO authenticated;

-- =====================================================
-- STEP 7: SUCCESS MESSAGE
-- =====================================================

SELECT 'Profile update errors fix completed successfully!' as status,
       'The profiles table now has all required columns for profile updates' as message;
