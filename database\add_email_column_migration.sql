-- =====================================================
-- ADD EMAIL COLUMN TO USER_PROFILES TABLE
-- =====================================================
-- Migration to add email column to user_profiles table
-- Run this in your Supabase SQL editor

-- Add email column to user_profiles table
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS email TEXT;

-- Add unique constraint on email (allowing NULL values)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_email_unique 
ON public.user_profiles(email) 
WHERE email IS NOT NULL;

-- Add index for email lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_email 
ON public.user_profiles(email);

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;
