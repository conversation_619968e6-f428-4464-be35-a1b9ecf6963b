import React, { useState, useEffect, useRef } from 'react';
import {
  Text,
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

/**
 * ResponsiveText Component
 * Handles text length variations across different languages (50-200% variations)
 * Automatically adjusts font size, wrapping, and truncation to prevent UI displacement
 * 
 * @param {Object} props - Component props
 * @param {string} props.children - Text content to display
 * @param {Object} props.style - Text style object
 * @param {number} props.maxLines - Maximum number of lines (default: 2)
 * @param {number} props.minFontSize - Minimum font size (default: 10)
 * @param {number} props.maxFontSize - Maximum font size (default: 18)
 * @param {number} props.maxWidth - Maximum width constraint
 * @param {boolean} props.adjustFontSize - Whether to auto-adjust font size (default: true)
 * @param {boolean} props.allowWrapping - Whether to allow text wrapping (default: true)
 * @param {string} props.truncationMode - How to truncate: 'tail', 'middle', 'head' (default: 'tail')
 * @param {boolean} props.maintainHeight - Whether to maintain consistent height (default: true)
 * @param {Function} props.onTextLayout - Callback when text layout changes
 */
const ResponsiveText = ({
  children,
  style = {},
  maxLines = 2,
  minFontSize = 10,
  maxFontSize = 18,
  maxWidth = null,
  adjustFontSize = true,
  allowWrapping = true,
  truncationMode = 'tail',
  maintainHeight = true,
  onTextLayout,
  ...props
}) => {
  const { theme } = useTheme();
  const [fontSize, setFontSize] = useState(style.fontSize || 16);
  const [textHeight, setTextHeight] = useState(null);
  const [isTextTruncated, setIsTextTruncated] = useState(false);
  const textRef = useRef(null);

  // Calculate optimal font size based on text length and available space
  useEffect(() => {
    if (adjustFontSize && children) {
      calculateOptimalFontSize();
    }
  }, [children, maxWidth, adjustFontSize]);

  const calculateOptimalFontSize = () => {
    if (!children || typeof children !== 'string') return;

    const textLength = children.length;
    const baseSize = style.fontSize || 16;
    
    // Calculate font size based on text length
    let calculatedSize = baseSize;
    
    if (textLength > 100) {
      // Very long text - reduce font size significantly
      calculatedSize = Math.max(minFontSize, baseSize * 0.7);
    } else if (textLength > 50) {
      // Long text - reduce font size moderately
      calculatedSize = Math.max(minFontSize, baseSize * 0.85);
    } else if (textLength < 20) {
      // Short text - can use larger font size
      calculatedSize = Math.min(maxFontSize, baseSize * 1.1);
    }

    // Ensure font size is within bounds
    calculatedSize = Math.max(minFontSize, Math.min(maxFontSize, calculatedSize));
    
    setFontSize(calculatedSize);
  };

  const handleTextLayout = (event) => {
    const { lines, height } = event.nativeEvent;
    
    // Check if text is truncated
    setIsTextTruncated(lines.length > maxLines);
    
    // Set consistent height if required
    if (maintainHeight && !textHeight) {
      setTextHeight(height);
    }

    // Call parent callback if provided
    if (onTextLayout) {
      onTextLayout(event);
    }
  };

  const getContainerStyle = () => {
    const containerStyle = {
      width: maxWidth || '100%',
    };

    if (maintainHeight && textHeight) {
      containerStyle.height = textHeight;
      containerStyle.overflow = 'hidden';
    }

    return containerStyle;
  };

  const getTextStyle = () => {
    return [
      {
        fontSize: fontSize,
        lineHeight: fontSize * 1.4, // Maintain good line height ratio
        color: theme.colors.textPrimary,
        // Ensure minimum touch target size for accessibility
        minHeight: fontSize < 12 ? 44 : undefined,
      },
      style,
    ];
  };

  const getEllipsizeMode = () => {
    switch (truncationMode) {
      case 'middle':
        return 'middle';
      case 'head':
        return 'head';
      case 'tail':
      default:
        return 'tail';
    }
  };

  // Handle very long text that might break layout
  const processedText = React.useMemo(() => {
    if (!children || typeof children !== 'string') return children;
    
    // For extremely long text without spaces, add soft breaks
    if (children.length > 200 && !children.includes(' ')) {
      return children.replace(/(.{50})/g, '$1\u200B'); // Add zero-width space every 50 chars
    }
    
    return children;
  }, [children]);

  return (
    <View style={getContainerStyle()}>
      <Text
        ref={textRef}
        style={getTextStyle()}
        numberOfLines={allowWrapping ? maxLines : 1}
        ellipsizeMode={getEllipsizeMode()}
        onTextLayout={handleTextLayout}
        adjustsFontSizeToFit={adjustFontSize}
        minimumFontScale={minFontSize / (style.fontSize || 16)}
        allowFontScaling={true}
        {...props}
      >
        {processedText}
      </Text>
      
      {/* Debug info in development */}
      {__DEV__ && isTextTruncated && (
        <Text style={styles.debugText}>
          Text truncated - Original length: {children?.length || 0}
        </Text>
      )}
    </View>
  );
};

/**
 * ResponsiveTextPair Component
 * For displaying two-line text pairs (like greetings) with consistent spacing
 */
export const ResponsiveTextPair = ({
  primaryText,
  secondaryText,
  primaryStyle = {},
  secondaryStyle = {},
  containerStyle = {},
  maxWidth,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <View style={[styles.textPairContainer, containerStyle]}>
      <ResponsiveText
        style={[styles.primaryText, { color: theme.colors.textPrimary }, primaryStyle]}
        maxLines={1}
        maxWidth={maxWidth}
        adjustFontSize={true}
        allowWrapping={false}
        {...props}
      >
        {primaryText}
      </ResponsiveText>
      
      <ResponsiveText
        style={[styles.secondaryText, { color: theme.colors.textSecondary }, secondaryStyle]}
        maxLines={1}
        maxWidth={maxWidth}
        adjustFontSize={true}
        allowWrapping={false}
        {...props}
      >
        {secondaryText}
      </ResponsiveText>
    </View>
  );
};

/**
 * ResponsiveButton Component
 * Button with responsive text that maintains touch targets
 */
export const ResponsiveButton = ({
  title,
  onPress,
  style = {},
  textStyle = {},
  disabled = false,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <TouchableOpacity
      style={[
        styles.responsiveButton,
        {
          backgroundColor: disabled ? theme.colors.disabled : theme.colors.primary,
          borderColor: theme.colors.border,
        },
        style
      ]}
      onPress={onPress}
      disabled={disabled}
      {...props}
    >
      <ResponsiveText
        style={[
          styles.buttonText,
          {
            color: disabled ? theme.colors.textDisabled : theme.colors.white,
          },
          textStyle
        ]}
        maxLines={2}
        adjustFontSize={true}
        allowWrapping={true}
        maintainHeight={false}
        minFontSize={12}
        maxFontSize={16}
      >
        {title}
      </ResponsiveText>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  textPairContainer: {
    alignItems: 'flex-start',
  },
  primaryText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  secondaryText: {
    fontSize: 16,
    fontWeight: '400',
  },
  responsiveButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    minHeight: 44, // Ensure accessibility compliance
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  debugText: {
    fontSize: 10,
    color: 'red',
    marginTop: 2,
  },
});

export default ResponsiveText;
