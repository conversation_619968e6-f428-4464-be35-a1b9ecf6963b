/**
 * User Metadata Update Service
 * 
 * This service ensures user metadata is properly synchronized with profile data
 * to fix greeting display issues where real names are not showing.
 */

import { supabase } from '../config/supabaseClient';

class UserMetadataUpdateService {
  constructor() {
    this.tableName = 'profiles'; // Using schema-essential.sql structure
  }

  /**
   * Update user metadata with real name from profile
   * @param {string} userId - User ID to update
   * @returns {Promise<Object>} - Result object
   */
  async updateUserMetadataFromProfile(userId) {
    try {
      console.log('🔄 Updating user metadata from profile for user:', userId);

      // Get current user from auth
      const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(userId);
      
      if (userError || !user) {
        console.error('❌ Could not get user from auth:', userError);
        return { success: false, error: 'User not found in auth system' };
      }

      // Get user profile from database
      const { data: profile, error: profileError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('❌ Could not get profile from database:', profileError);
        return { success: false, error: 'Profile not found in database' };
      }

      console.log('📋 Current state:', {
        userId: user.id,
        currentMetadata: user.user_metadata,
        profileName: profile.full_name,
        profilePhone: profile.phone,
        profileEmail: profile.email
      });

      // Check if metadata needs updating
      const needsUpdate = this.checkIfMetadataNeedsUpdate(user.user_metadata, profile);
      
      if (!needsUpdate) {
        console.log('✅ User metadata is already up to date');
        return { 
          success: true, 
          data: user.user_metadata, 
          changed: false, 
          message: 'Metadata already up to date' 
        };
      }

      // Prepare updated metadata
      const updatedMetadata = {
        ...user.user_metadata,
        full_name: profile.full_name,
        name: profile.full_name, // Also set 'name' as backup
        phone: profile.phone,
        email: profile.email,
        country_code: profile.country_code || user.user_metadata?.country_code || 'UG',
        last_metadata_update: new Date().toISOString()
      };

      console.log('🔄 Updating user metadata:', updatedMetadata);

      // Update user metadata
      const { data: updatedUser, error: updateError } = await supabase.auth.admin.updateUserById(userId, {
        user_metadata: updatedMetadata
      });

      if (updateError) {
        console.error('❌ Error updating user metadata:', updateError);
        return { success: false, error: updateError.message };
      }

      console.log('✅ User metadata updated successfully');
      return { 
        success: true, 
        data: updatedUser.user.user_metadata, 
        changed: true,
        oldMetadata: user.user_metadata,
        newMetadata: updatedUser.user.user_metadata,
        message: 'User metadata updated from profile data' 
      };

    } catch (error) {
      console.error('❌ Error updating user metadata:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user metadata needs updating based on profile data
   * @param {Object} currentMetadata - Current user metadata
   * @param {Object} profile - Profile data from database
   * @returns {boolean} - True if update is needed
   */
  checkIfMetadataNeedsUpdate(currentMetadata, profile) {
    // Check if full_name is missing or different
    if (!currentMetadata?.full_name || currentMetadata.full_name !== profile.full_name) {
      console.log('🔍 Metadata needs update: full_name mismatch', {
        metadataName: currentMetadata?.full_name,
        profileName: profile.full_name
      });
      return true;
    }

    // Check if name is missing (backup field)
    if (!currentMetadata?.name || currentMetadata.name !== profile.full_name) {
      console.log('🔍 Metadata needs update: name field missing or different');
      return true;
    }

    // Check if phone is missing or different
    if (!currentMetadata?.phone || currentMetadata.phone !== profile.phone) {
      console.log('🔍 Metadata needs update: phone mismatch');
      return true;
    }

    return false;
  }

  /**
   * Update metadata for all users who have profiles but outdated metadata
   * @returns {Promise<Object>} - Results summary
   */
  async updateAllUserMetadataFromProfiles() {
    try {
      console.log('🔍 Finding all users with outdated metadata...');

      // Get all profiles
      const { data: profiles, error: profilesError } = await supabase
        .from(this.tableName)
        .select('id, full_name, phone, email, country_code');

      if (profilesError) {
        throw profilesError;
      }

      console.log(`📊 Found ${profiles.length} profiles to check`);

      const results = {
        total: profiles.length,
        updated: 0,
        unchanged: 0,
        errors: 0,
        details: []
      };

      // Process each profile
      for (const profile of profiles) {
        try {
          const result = await this.updateUserMetadataFromProfile(profile.id);
          
          if (result.success) {
            if (result.changed) {
              results.updated++;
              results.details.push({
                userId: profile.id,
                status: 'updated',
                profileName: profile.full_name,
                message: result.message
              });
            } else {
              results.unchanged++;
              results.details.push({
                userId: profile.id,
                status: 'unchanged',
                reason: result.message
              });
            }
          } else {
            results.errors++;
            results.details.push({
              userId: profile.id,
              status: 'error',
              error: result.error
            });
          }
        } catch (error) {
          results.errors++;
          results.details.push({
            userId: profile.id,
            status: 'error',
            error: error.message
          });
        }
      }

      console.log('📊 Metadata update results:', {
        total: results.total,
        updated: results.updated,
        unchanged: results.unchanged,
        errors: results.errors
      });

      return { success: true, data: results };

    } catch (error) {
      console.error('❌ Error updating all user metadata:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current user's metadata status
   * @returns {Promise<Object>} - Metadata status
   */
  async getCurrentUserMetadataStatus() {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'No authenticated user found' };
      }

      return await this.updateUserMetadataFromProfile(user.id);

    } catch (error) {
      console.error('❌ Error getting current user metadata status:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Force refresh user metadata for current session
   * @returns {Promise<Object>} - Refresh result
   */
  async refreshCurrentUserMetadata() {
    try {
      console.log('🔄 Refreshing current user metadata...');

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'No authenticated user found' };
      }

      // Update metadata from profile
      const updateResult = await this.updateUserMetadataFromProfile(user.id);
      
      if (updateResult.success) {
        // Refresh the session to get updated metadata
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError) {
          console.warn('⚠️ Could not refresh session:', refreshError);
        } else {
          console.log('✅ Session refreshed with updated metadata');
        }
      }

      return updateResult;

    } catch (error) {
      console.error('❌ Error refreshing user metadata:', error);
      return { success: false, error: error.message };
    }
  }
}

// Create and export singleton instance
const userMetadataUpdateService = new UserMetadataUpdateService();
export default userMetadataUpdateService;
