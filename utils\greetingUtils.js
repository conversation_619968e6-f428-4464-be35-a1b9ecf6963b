import { getTimeBasedGreeting, getEastAfricanTime } from './i18n';
import deviceUserRecognitionService from '../services/deviceUserRecognitionService';

/**
 * Extract display name from full name, handling generated names properly
 * @param {string} fullName - Full name from profile
 * @returns {string} - Display name for greeting
 */
const extractDisplayName = (fullName) => {
  if (!fullName || typeof fullName !== 'string') {
    return null;
  }

  const trimmedName = fullName.trim();

  // Handle generated names like "User 9916" - use the full name
  if (trimmedName.match(/^User \d+$/)) {
    console.log('⚠️ Detected auto-generated name, using full name:', trimmedName);
    return trimmedName; // Return "User 9916" instead of just "User"
  }

  // Handle other generated patterns
  if (trimmedName.match(/^JiraniPay User$/)) {
    console.log('⚠️ Detected default generated name, using friendly alternative');
    return 'Friend'; // More friendly than "JiraniPay"
  }

  // For real names, use first name only
  const nameParts = trimmedName.split(' ');
  if (nameParts.length > 1) {
    // Check if it looks like a real first name (not just "User")
    const firstName = nameParts[0];
    if (firstName.length > 1 && /^[A-Za-z]/.test(firstName) && firstName !== 'User') {
      console.log('✅ Using real first name from full name:', firstName);
      return firstName;
    }
  }

  // If it's a single word that looks like a real name, return as-is
  if (trimmedName.length > 1 && /^[A-Za-z]/.test(trimmedName) && trimmedName !== 'User') {
    console.log('✅ Using single real name:', trimmedName);
    return trimmedName;
  }

  // For any other case, return as-is
  console.log('⚠️ Using name as-is:', trimmedName);
  return trimmedName;
};

/**
 * Centralized Greeting Utilities for JiraniPay
 *
 * This module provides consistent, time-based greeting functionality
 * across the entire JiraniPay application with proper timezone handling
 * for the East African market.
 *
 * Three-period system:
 * - Morning: 00:00 AM - 11:59 AM
 * - Afternoon: 12:00 PM - 5:59 PM
 * - Evening: 6:00 PM - 11:59 PM
 */

/**
 * Get current time period for debugging and display
 * Three-period system: Morning, Afternoon, Evening
 * @param {boolean} useLocalTime - Whether to use local device time
 * @returns {Object} - Time period information
 */
export const getCurrentTimePeriod = (useLocalTime = false) => {
  const now = useLocalTime ? new Date() : getEastAfricanTime();
  const hour = now.getHours();

  let period, timeRange, icon;

  if (hour >= 0 && hour < 12) {
    period = 'morning';
    timeRange = '00:00 AM - 11:59 AM';
    icon = '🌅';
  } else if (hour >= 12 && hour < 18) {
    period = 'afternoon';
    timeRange = '12:00 PM - 5:59 PM';
    icon = '☀️';
  } else {
    period = 'evening';
    timeRange = '6:00 PM - 11:59 PM';
    icon = '🌆';
  }

  return {
    period,
    timeRange,
    icon,
    hour,
    currentTime: now.toLocaleTimeString('en-US', {
      timeZone: useLocalTime ? undefined : 'Africa/Kampala',
      hour12: true,
      hour: 'numeric',
      minute: '2-digit'
    }),
    timezone: useLocalTime ? 'Local' : 'EAT (UTC+3)'
  };
};

/**
 * Enhanced greeting function with user profile integration
 * Prioritizes user's actual name over generic messages
 * @param {Object} user - User object from authentication
 * @param {Object} userProfile - User profile data
 * @param {boolean} useLocalTime - Whether to use local device time
 * @param {Function} translateFn - Translation function to use (optional)
 * @returns {string} - Personalized greeting message
 */
export const getPersonalizedGreeting = (user = null, userProfile = null, useLocalTime = false, translateFn = null) => {
  let userName = null;

  console.log('🔍 getPersonalizedGreeting: Extracting user name from:', {
    userProfile: userProfile?.full_name,
    userMetadata: user?.user_metadata?.full_name,
    userMetadataName: user?.user_metadata?.name,
    userEmail: user?.email,
    userPhone: user?.phone
  });

  // Extract user name from various sources, prioritizing profile data
  if (userProfile?.full_name) {
    userName = extractDisplayName(userProfile.full_name);
    console.log('✅ Using name from userProfile:', userName);
  } else if (user?.user_metadata?.full_name) {
    userName = extractDisplayName(user.user_metadata.full_name);
    console.log('✅ Using name from user metadata:', userName);
  } else if (user?.user_metadata?.name) {
    // Some auth systems use 'name' instead of 'full_name'
    userName = extractDisplayName(user.user_metadata.name);
    console.log('✅ Using name from user metadata (name field):', userName);
  } else if (user?.email) {
    // Extract name from email (before @) as last resort
    const emailName = user.email.split('@')[0];
    // Only use email name if it looks like a real name (not just numbers/random chars)
    if (emailName.length > 2 && /^[a-zA-Z]/.test(emailName)) {
      userName = emailName.charAt(0).toUpperCase() + emailName.slice(1).toLowerCase();
      console.log('✅ Using name from email:', userName);
    }
  }

  if (!userName) {
    console.log('⚠️ No user name found, using time-based greeting only');
  }

  // Get time-based greeting with or without name
  return getTimeBasedGreeting(userName, useLocalTime, translateFn);
};

/**
 * Get greeting for dashboard with enhanced user context
 * For existing users: Shows "Good [Time], [FirstName]"
 * For new users: Shows "Good [Time]" only
 * @param {Object} user - User object from authentication
 * @param {Object} userProfile - User profile data from database
 * @param {boolean} useLocalTime - Whether to use local device time
 * @param {Function} translateFn - Translation function to use (optional)
 * @returns {Promise<string>} - Dashboard greeting message
 */
export const getDashboardGreeting = async (user = null, userProfile = null, useLocalTime = false, translateFn = null) => {
  return getUnifiedGreeting(user, userProfile, 'dashboard', useLocalTime, translateFn);
};

/**
 * Unified greeting generation function for all screens
 * @param {Object} user - User object from authentication
 * @param {Object} userProfile - User profile data from database
 * @param {string} greetingType - Type of greeting ('dashboard', 'welcome', 'login')
 * @param {boolean} useLocalTime - Whether to use local device time
 * @param {Function} translateFn - Translation function to use (optional)
 * @returns {Promise<string>} - Greeting message
 */
export const getUnifiedGreeting = async (user = null, userProfile = null, greetingType = 'dashboard', useLocalTime = false, translateFn = null) => {
  try {
    console.log('🎯 getUnifiedGreeting called with:', {
      hasUser: !!user,
      userId: user?.id,
      hasUserProfile: !!userProfile,
      userProfileName: userProfile?.full_name,
      greetingType,
      userMetadata: user?.user_metadata
    });

    let userName = null;
    let isReturningUser = false;

    // Enhanced name extraction with priority order
    if (userProfile?.full_name) {
      userName = extractDisplayName(userProfile.full_name);
      isReturningUser = true;
      console.log('✅ Unified: Using name from profile:', userName);
    } else if (user?.user_metadata?.full_name) {
      userName = extractDisplayName(user.user_metadata.full_name);
      isReturningUser = true;
      console.log('✅ Unified: Using name from user metadata (full_name):', userName);
    } else if (user?.user_metadata?.name) {
      userName = extractDisplayName(user.user_metadata.name);
      isReturningUser = true;
      console.log('✅ Unified: Using name from user metadata (name):', userName);
    } else if (user?.email) {
      const emailName = user.email.split('@')[0];
      if (emailName.length > 2 && /^[a-zA-Z]/.test(emailName)) {
        userName = emailName.charAt(0).toUpperCase() + emailName.slice(1).toLowerCase();
        console.log('✅ Unified: Using name from email:', userName);
      }
    }

    // Generate time-based greeting
    const timeOfDay = getCurrentTimePeriod(useLocalTime);
    let greeting;

    // Different greeting formats based on type and context
    if (greetingType === 'welcome' && userName && isReturningUser && !user) {
      // Not authenticated but recognized (device recognition)
      greeting = `Welcome back, ${userName}!`;
    } else if (userName) {
      // Authenticated user or recognized user
      greeting = `Good ${timeOfDay.period}, ${userName}!`;
    } else {
      // Default greeting for unknown users
      greeting = `Good ${timeOfDay.period}!`;
    }

    console.log(`✅ Unified Greeting Generated (${greetingType}):`, {
      greeting,
      userName,
      isReturningUser,
      userAuthenticated: !!user?.id,
      profileLoaded: !!userProfile?.full_name
    });

    return greeting;
  } catch (error) {
    console.error('❌ Error generating unified greeting:', error);
    // Fallback to basic time-based greeting
    return getTimeBasedGreeting(null, useLocalTime, translateFn);
  }
};

/**
 * Get greeting for welcome/login screen
 * @param {Object} user - User object (if returning user)
 * @param {Object} userProfile - User profile data (optional)
 * @param {boolean} useLocalTime - Whether to use local device time
 * @param {Function} translateFn - Translation function to use (optional)
 * @returns {string} - Welcome screen greeting message
 */
export const getWelcomeGreeting = async (user = null, userProfile = null, useLocalTime = false, translateFn = null) => {
  return getUnifiedGreeting(user, userProfile, 'welcome', useLocalTime, translateFn);
};

/**
 * Hook-like function for real-time greeting updates
 * @param {Function} setGreeting - State setter function
 * @param {Object} user - User object
 * @param {Object} userProfile - User profile data
 * @param {boolean} useLocalTime - Whether to use local device time
 * @param {Function} translateFn - Translation function to use (optional)
 * @returns {Function} - Cleanup function to clear interval
 */
export const useGreetingUpdater = (setGreeting, user = null, userProfile = null, useLocalTime = false, translateFn = null) => {
  // Update greeting immediately
  const updateGreeting = () => {
    const newGreeting = getPersonalizedGreeting(user, userProfile, useLocalTime, translateFn);
    setGreeting(newGreeting);
  };
  
  // Initial update
  updateGreeting();
  
  // Set up interval to update every minute
  const interval = setInterval(updateGreeting, 60000);
  
  // Return cleanup function
  return () => clearInterval(interval);
};

/**
 * Debug function to test all greeting periods
 * Three-period system: Morning, Afternoon, Evening
 * @param {string} userName - Test user name
 * @returns {Object} - All greeting variations for testing
 */
export const debugGreetings = (userName = 'TestUser') => {
  const testHours = [8, 15, 20]; // Morning, Afternoon, Evening
  const results = {};

  testHours.forEach(hour => {
    // Create a test date with specific hour
    const testDate = new Date();
    testDate.setHours(hour, 0, 0, 0);

    // Temporarily override getEastAfricanTime for testing
    const originalGetEAT = getEastAfricanTime;
    global.getEastAfricanTime = () => testDate;

    try {
      const withName = getTimeBasedGreeting(userName);
      const withoutName = getTimeBasedGreeting();
      const period = getCurrentTimePeriod().period;

      // Test different user scenarios
      const mockUser = { id: '123', email: '<EMAIL>' };
      const mockProfile = { full_name: 'John Doe' };

      const dashboardGreeting = getPersonalizedGreeting(mockUser, mockProfile);
      const welcomeGreetingNew = getWelcomeGreeting(null); // New user
      const welcomeGreetingReturning = getWelcomeGreeting(mockUser, mockProfile); // Returning user

      results[`${hour}:00 (${period})`] = {
        withName,
        withoutName,
        dashboardGreeting,
        welcomeGreetingNew,
        welcomeGreetingReturning,
        hour,
        period
      };
    } catch (error) {
      results[`${hour}:00 (error)`] = { error: error.message };
    }

    // Restore original function
    global.getEastAfricanTime = originalGetEAT;
  });

  return results;
};

export default {
  getCurrentTimePeriod,
  getPersonalizedGreeting,
  getDashboardGreeting,
  getWelcomeGreeting,
  useGreetingUpdater,
  debugGreetings
};
