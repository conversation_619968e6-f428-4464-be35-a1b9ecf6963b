/**
 * Text Scaling Utilities for Multi-Language Support
 * Handles text length variations across East African languages (50-200% variations)
 * Ensures UI consistency and accessibility compliance
 */

import { Dimensions, PixelRatio } from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Language-specific text expansion factors
export const LANGUAGE_EXPANSION_FACTORS = {
  'en': 1.0,     // English baseline
  'sw': 1.2,     // Swahili - moderate expansion
  'fr': 1.3,     // French - longer text
  'ar': 1.1,     // Arabic - slightly longer, RTL considerations
  'am': 1.4,     // Amharic - can be significantly longer
  'lg': 1.8,     // Luganda - very long translations
  'rw': 1.6,     // Kinyarwanda - long translations
  'rn': 1.6,     // Kirundi - long translations
};

// Minimum sizes for accessibility compliance
export const ACCESSIBILITY_MINIMUMS = {
  fontSize: 12,
  touchTarget: 44,
  lineHeight: 16,
  letterSpacing: 0.5,
};

/**
 * Calculate responsive font size based on language and content
 * @param {number} baseFontSize - Base font size in pixels
 * @param {string} language - Language code (en, sw, fr, etc.)
 * @param {string} text - Text content to analyze
 * @param {Object} constraints - Size constraints
 * @returns {number} Adjusted font size
 */
export const calculateResponsiveFontSize = (
  baseFontSize,
  language = 'en',
  text = '',
  constraints = {}
) => {
  const {
    minSize = ACCESSIBILITY_MINIMUMS.fontSize,
    maxSize = baseFontSize * 1.2,
    maxWidth = screenWidth * 0.9,
    maxLines = 2
  } = constraints;

  // Get language expansion factor
  const expansionFactor = LANGUAGE_EXPANSION_FACTORS[language] || 1.0;
  
  // Calculate text complexity
  const textLength = text.length;
  const wordCount = text.split(/\s+/).length;
  const avgWordLength = textLength / Math.max(wordCount, 1);
  
  // Base adjustment for language
  let adjustedSize = baseFontSize / Math.sqrt(expansionFactor);
  
  // Adjust for text length
  if (textLength > 100) {
    adjustedSize *= 0.8;
  } else if (textLength > 50) {
    adjustedSize *= 0.9;
  } else if (textLength < 20) {
    adjustedSize *= 1.1;
  }
  
  // Adjust for word complexity (longer words need smaller font)
  if (avgWordLength > 10) {
    adjustedSize *= 0.9;
  }
  
  // Ensure within bounds
  return Math.max(minSize, Math.min(maxSize, adjustedSize));
};

/**
 * Calculate optimal line height for given font size and language
 * @param {number} fontSize - Font size in pixels
 * @param {string} language - Language code
 * @returns {number} Line height in pixels
 */
export const calculateLineHeight = (fontSize, language = 'en') => {
  const baseRatio = 1.4;
  
  // Some languages need more line spacing
  const languageMultipliers = {
    'ar': 1.5,  // Arabic needs more space for diacritics
    'am': 1.5,  // Amharic has complex characters
    'lg': 1.3,  // Luganda can be dense
    'rw': 1.3,  // Kinyarwanda can be dense
    'rn': 1.3,  // Kirundi can be dense
  };
  
  const multiplier = languageMultipliers[language] || baseRatio;
  return Math.max(ACCESSIBILITY_MINIMUMS.lineHeight, fontSize * multiplier);
};

/**
 * Determine if text should be truncated or wrapped
 * @param {string} text - Text content
 * @param {string} language - Language code
 * @param {Object} constraints - Layout constraints
 * @returns {Object} Layout recommendations
 */
export const getTextLayoutStrategy = (text, language = 'en', constraints = {}) => {
  const {
    maxWidth = screenWidth * 0.9,
    maxLines = 2,
    allowWrapping = true
  } = constraints;
  
  const expansionFactor = LANGUAGE_EXPANSION_FACTORS[language] || 1.0;
  const estimatedWidth = text.length * 8 * expansionFactor; // Rough estimation
  
  return {
    shouldWrap: allowWrapping && estimatedWidth > maxWidth,
    shouldTruncate: estimatedWidth > maxWidth * maxLines,
    recommendedLines: Math.min(maxLines, Math.ceil(estimatedWidth / maxWidth)),
    estimatedWidth,
    truncationMode: language === 'ar' ? 'head' : 'tail', // RTL consideration
  };
};

/**
 * Scale dimensions for different screen sizes and languages
 * @param {number} baseSize - Base size in pixels
 * @param {string} type - Type of scaling: 'font', 'padding', 'margin', 'width', 'height'
 * @param {string} language - Language code
 * @returns {number} Scaled size
 */
export const scaleSize = (baseSize, type = 'font', language = 'en') => {
  const pixelRatio = PixelRatio.get();
  const screenScale = Math.min(screenWidth / 375, screenHeight / 667); // Based on iPhone 6/7/8
  
  let scaledSize = baseSize;
  
  // Apply screen scaling
  if (type === 'font') {
    scaledSize = baseSize * Math.min(screenScale, 1.2); // Cap font scaling
  } else {
    scaledSize = baseSize * screenScale;
  }
  
  // Apply language-specific adjustments
  const expansionFactor = LANGUAGE_EXPANSION_FACTORS[language] || 1.0;
  
  switch (type) {
    case 'font':
      scaledSize = scaledSize / Math.sqrt(expansionFactor);
      break;
    case 'width':
    case 'padding':
      scaledSize = scaledSize * Math.min(expansionFactor, 1.5); // Cap expansion
      break;
    case 'height':
      if (expansionFactor > 1.3) {
        scaledSize = scaledSize * 1.2; // Increase height for long text languages
      }
      break;
  }
  
  // Ensure minimum accessibility requirements
  if (type === 'font') {
    scaledSize = Math.max(ACCESSIBILITY_MINIMUMS.fontSize, scaledSize);
  }
  
  return Math.round(scaledSize);
};

/**
 * Get responsive styles for a component based on language
 * @param {Object} baseStyles - Base style object
 * @param {string} language - Language code
 * @param {string} text - Text content (optional)
 * @returns {Object} Responsive styles
 */
export const getResponsiveStyles = (baseStyles, language = 'en', text = '') => {
  const responsiveStyles = { ...baseStyles };
  
  // Scale font size
  if (responsiveStyles.fontSize) {
    responsiveStyles.fontSize = calculateResponsiveFontSize(
      responsiveStyles.fontSize,
      language,
      text
    );
    responsiveStyles.lineHeight = calculateLineHeight(responsiveStyles.fontSize, language);
  }
  
  // Scale padding and margins
  ['padding', 'paddingHorizontal', 'paddingVertical', 'margin', 'marginHorizontal', 'marginVertical'].forEach(prop => {
    if (responsiveStyles[prop]) {
      responsiveStyles[prop] = scaleSize(responsiveStyles[prop], 'padding', language);
    }
  });
  
  // Scale width and height
  if (responsiveStyles.width && typeof responsiveStyles.width === 'number') {
    responsiveStyles.width = scaleSize(responsiveStyles.width, 'width', language);
  }
  
  if (responsiveStyles.height && typeof responsiveStyles.height === 'number') {
    responsiveStyles.height = scaleSize(responsiveStyles.height, 'height', language);
  }
  
  // Add RTL support for Arabic
  if (language === 'ar') {
    responsiveStyles.textAlign = responsiveStyles.textAlign || 'right';
    responsiveStyles.writingDirection = 'rtl';
  }
  
  return responsiveStyles;
};

/**
 * Create a responsive style function for use with StyleSheet
 * @param {Function} styleFunction - Function that returns styles
 * @param {string} language - Language code
 * @returns {Function} Enhanced style function
 */
export const createResponsiveStyleSheet = (styleFunction, language = 'en') => {
  return (theme) => {
    const baseStyles = styleFunction(theme);
    const responsiveStyles = {};
    
    Object.keys(baseStyles).forEach(key => {
      responsiveStyles[key] = getResponsiveStyles(baseStyles[key], language);
    });
    
    return responsiveStyles;
  };
};

/**
 * Utility to check if text will fit in given constraints
 * @param {string} text - Text content
 * @param {Object} style - Text style
 * @param {Object} constraints - Layout constraints
 * @param {string} language - Language code
 * @returns {boolean} Whether text will fit
 */
export const willTextFit = (text, style, constraints, language = 'en') => {
  const { maxWidth, maxLines = 1 } = constraints;
  const { fontSize = 16 } = style;
  
  const expansionFactor = LANGUAGE_EXPANSION_FACTORS[language] || 1.0;
  const estimatedCharWidth = fontSize * 0.6; // Rough estimation
  const estimatedTextWidth = text.length * estimatedCharWidth * expansionFactor;
  
  return estimatedTextWidth <= maxWidth * maxLines;
};

export default {
  calculateResponsiveFontSize,
  calculateLineHeight,
  getTextLayoutStrategy,
  scaleSize,
  getResponsiveStyles,
  createResponsiveStyleSheet,
  willTextFit,
  LANGUAGE_EXPANSION_FACTORS,
  ACCESSIBILITY_MINIMUMS,
};
