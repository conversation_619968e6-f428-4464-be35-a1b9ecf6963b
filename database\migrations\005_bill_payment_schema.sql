-- Bill Payment System Database Schema
-- Creates tables for billers, bill payments, recurring schedules, and payment categories

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- BILL CATEGORIES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bill_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(7), -- Hex color code
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILLERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.billers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES public.bill_categories(id) ON DELETE RESTRICT,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    display_name VARCHAR(200) NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    
    -- Payment configuration
    min_amount DECIMAL(15,2) DEFAULT 1000,
    max_amount DECIMAL(15,2) DEFAULT ********,
    fee_type VARCHAR(20) DEFAULT 'fixed' CHECK (fee_type IN ('fixed', 'percentage', 'tiered')),
    fee_amount DECIMAL(15,2) DEFAULT 0,
    fee_percentage DECIMAL(5,2) DEFAULT 0,
    fee_structure JSONB DEFAULT '{}',
    
    -- Account validation
    account_number_format VARCHAR(100), -- Regex pattern
    account_number_length INTEGER,
    account_number_prefix VARCHAR(20),
    validation_rules JSONB DEFAULT '{}',
    
    -- API configuration
    api_endpoint VARCHAR(500),
    api_key_required BOOLEAN DEFAULT false,
    api_config JSONB DEFAULT '{}',
    
    -- Status and availability
    is_active BOOLEAN DEFAULT true,
    is_available BOOLEAN DEFAULT true,
    maintenance_mode BOOLEAN DEFAULT false,
    maintenance_message TEXT,
    
    -- Metadata
    supported_currencies TEXT[] DEFAULT '{"UGX"}',
    processing_time VARCHAR(50) DEFAULT 'instant',
    business_hours JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILL PAYMENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bill_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biller_id UUID NOT NULL REFERENCES public.billers(id) ON DELETE RESTRICT,
    transaction_id UUID, -- Will be linked after transaction creation
    
    -- Payment details
    account_number VARCHAR(100) NOT NULL,
    account_name VARCHAR(200),
    amount DECIMAL(15,2) NOT NULL,
    fee DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'UGX',
    
    -- Payment metadata
    reference VARCHAR(100) NOT NULL UNIQUE,
    external_reference VARCHAR(100),
    payment_method VARCHAR(50) DEFAULT 'wallet',
    
    -- Status tracking
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'
    )),
    
    -- Validation and verification
    account_verified BOOLEAN DEFAULT false,
    verification_data JSONB DEFAULT '{}',
    
    -- Timing
    scheduled_at TIMESTAMP WITH TIME ZONE,
    processed_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Error handling
    error_code VARCHAR(50),
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- RECURRING BILL PAYMENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.recurring_bill_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    biller_id UUID NOT NULL REFERENCES public.billers(id) ON DELETE RESTRICT,
    
    -- Recurring configuration
    name VARCHAR(200) NOT NULL,
    account_number VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'UGX',
    
    -- Schedule configuration
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    interval_value INTEGER DEFAULT 1, -- Every X frequency (e.g., every 2 weeks)
    day_of_month INTEGER, -- For monthly payments (1-31)
    day_of_week INTEGER, -- For weekly payments (0-6, Sunday=0)
    
    -- Timing
    start_date DATE NOT NULL,
    end_date DATE,
    next_payment_date DATE NOT NULL,
    last_payment_date DATE,
    
    -- Status and control
    is_active BOOLEAN DEFAULT true,
    is_paused BOOLEAN DEFAULT false,
    pause_until DATE,
    
    -- Limits and safety
    max_amount DECIMAL(15,2),
    payment_count INTEGER DEFAULT 0,
    max_payments INTEGER,
    failed_attempts INTEGER DEFAULT 0,
    max_failed_attempts INTEGER DEFAULT 3,
    
    -- Notifications
    reminder_enabled BOOLEAN DEFAULT true,
    reminder_days_before INTEGER DEFAULT 1,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILL PAYMENT HISTORY TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bill_payment_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bill_payment_id UUID NOT NULL REFERENCES public.bill_payments(id) ON DELETE CASCADE,
    recurring_payment_id UUID REFERENCES public.recurring_bill_payments(id) ON DELETE SET NULL,
    
    -- Status change tracking
    old_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    status_reason TEXT,
    
    -- Processing details
    processed_by VARCHAR(100), -- System, user, or external service
    processing_time_ms INTEGER,
    
    -- External system details
    external_transaction_id VARCHAR(100),
    external_response JSONB DEFAULT '{}',
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILLER OUTAGES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.biller_outages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    biller_id UUID NOT NULL REFERENCES public.billers(id) ON DELETE CASCADE,
    
    -- Outage details
    title VARCHAR(200) NOT NULL,
    description TEXT,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- Timing
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    estimated_resolution TIMESTAMP WITH TIME ZONE,
    
    -- Status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('scheduled', 'active', 'resolved')),
    
    -- Communication
    public_message TEXT,
    internal_notes TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Bill Categories Indexes
CREATE INDEX IF NOT EXISTS idx_bill_categories_active ON public.bill_categories(is_active, sort_order);

-- Billers Indexes
CREATE INDEX IF NOT EXISTS idx_billers_category ON public.billers(category_id);
CREATE INDEX IF NOT EXISTS idx_billers_code ON public.billers(code);
CREATE INDEX IF NOT EXISTS idx_billers_active ON public.billers(is_active, is_available);
CREATE INDEX IF NOT EXISTS idx_billers_category_active ON public.billers(category_id, is_active);

-- Bill Payments Indexes
CREATE INDEX IF NOT EXISTS idx_bill_payments_user ON public.bill_payments(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_bill_payments_biller ON public.bill_payments(biller_id);
CREATE INDEX IF NOT EXISTS idx_bill_payments_status ON public.bill_payments(status);
CREATE INDEX IF NOT EXISTS idx_bill_payments_reference ON public.bill_payments(reference);
CREATE INDEX IF NOT EXISTS idx_bill_payments_account ON public.bill_payments(biller_id, account_number);
CREATE INDEX IF NOT EXISTS idx_bill_payments_scheduled ON public.bill_payments(scheduled_at) WHERE scheduled_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_bill_payments_pending ON public.bill_payments(status, created_at) WHERE status IN ('pending', 'processing');

-- Recurring Bill Payments Indexes
CREATE INDEX IF NOT EXISTS idx_recurring_payments_user ON public.recurring_bill_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_biller ON public.recurring_bill_payments(biller_id);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_active ON public.recurring_bill_payments(is_active, is_paused);
CREATE INDEX IF NOT EXISTS idx_recurring_payments_next_date ON public.recurring_bill_payments(next_payment_date) WHERE is_active = true AND is_paused = false;
CREATE INDEX IF NOT EXISTS idx_recurring_payments_user_active ON public.recurring_bill_payments(user_id, is_active);

-- Bill Payment History Indexes
CREATE INDEX IF NOT EXISTS idx_payment_history_payment ON public.bill_payment_history(bill_payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_recurring ON public.bill_payment_history(recurring_payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_history_created ON public.bill_payment_history(created_at DESC);

-- Biller Outages Indexes
CREATE INDEX IF NOT EXISTS idx_biller_outages_biller ON public.biller_outages(biller_id);
CREATE INDEX IF NOT EXISTS idx_biller_outages_active ON public.biller_outages(status, start_time) WHERE status = 'active';
CREATE INDEX IF NOT EXISTS idx_biller_outages_time ON public.biller_outages(start_time DESC);

-- =====================================================
-- ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.bill_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.billers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bill_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_bill_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bill_payment_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.biller_outages ENABLE ROW LEVEL SECURITY;

-- Bill Categories Policies (Read-only for users)
CREATE POLICY "Users can view active bill categories" ON public.bill_categories
    FOR SELECT USING (is_active = true);

-- Billers Policies (Read-only for users)
CREATE POLICY "Users can view active billers" ON public.billers
    FOR SELECT USING (is_active = true AND is_available = true);

-- Bill Payments Policies
CREATE POLICY "Users can view their own bill payments" ON public.bill_payments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own bill payments" ON public.bill_payments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own bill payments" ON public.bill_payments
    FOR UPDATE USING (auth.uid() = user_id);

-- Recurring Bill Payments Policies
CREATE POLICY "Users can manage their own recurring payments" ON public.recurring_bill_payments
    FOR ALL USING (auth.uid() = user_id);

-- Bill Payment History Policies (Read-only for users)
CREATE POLICY "Users can view payment history for their payments" ON public.bill_payment_history
    FOR SELECT USING (
        bill_payment_id IN (
            SELECT id FROM public.bill_payments WHERE user_id = auth.uid()
        )
    );

-- Biller Outages Policies (Read-only for users)
CREATE POLICY "Users can view active biller outages" ON public.biller_outages
    FOR SELECT USING (status IN ('scheduled', 'active'));

-- =====================================================
-- FUNCTIONS FOR BILL PAYMENTS
-- =====================================================

-- Function to get next payment date for recurring payment
CREATE OR REPLACE FUNCTION calculate_next_payment_date(
    p_frequency VARCHAR,
    p_interval_value INTEGER,
    p_current_date DATE,
    p_day_of_month INTEGER DEFAULT NULL,
    p_day_of_week INTEGER DEFAULT NULL
)
RETURNS DATE
LANGUAGE plpgsql
AS $$
DECLARE
    v_next_date DATE;
BEGIN
    CASE p_frequency
        WHEN 'daily' THEN
            v_next_date := p_current_date + (p_interval_value || ' days')::INTERVAL;
        
        WHEN 'weekly' THEN
            v_next_date := p_current_date + (p_interval_value || ' weeks')::INTERVAL;
            IF p_day_of_week IS NOT NULL THEN
                -- Adjust to specific day of week
                v_next_date := v_next_date + (p_day_of_week - EXTRACT(DOW FROM v_next_date))::INTEGER;
            END IF;
        
        WHEN 'monthly' THEN
            v_next_date := p_current_date + (p_interval_value || ' months')::INTERVAL;
            IF p_day_of_month IS NOT NULL THEN
                -- Adjust to specific day of month
                v_next_date := DATE_TRUNC('month', v_next_date) + (p_day_of_month - 1 || ' days')::INTERVAL;
            END IF;
        
        WHEN 'quarterly' THEN
            v_next_date := p_current_date + (p_interval_value * 3 || ' months')::INTERVAL;
        
        WHEN 'yearly' THEN
            v_next_date := p_current_date + (p_interval_value || ' years')::INTERVAL;
        
        ELSE
            v_next_date := p_current_date + '1 month'::INTERVAL;
    END CASE;
    
    RETURN v_next_date;
END;
$$;

-- Function to get due recurring payments
CREATE OR REPLACE FUNCTION get_due_recurring_payments(p_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    biller_id UUID,
    account_number VARCHAR,
    amount DECIMAL,
    currency VARCHAR,
    next_payment_date DATE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rbp.id,
        rbp.user_id,
        rbp.biller_id,
        rbp.account_number,
        rbp.amount,
        rbp.currency,
        rbp.next_payment_date
    FROM public.recurring_bill_payments rbp
    WHERE rbp.is_active = true 
    AND rbp.is_paused = false
    AND rbp.next_payment_date <= p_date
    AND (rbp.end_date IS NULL OR rbp.end_date >= p_date)
    AND (rbp.max_payments IS NULL OR rbp.payment_count < rbp.max_payments)
    AND rbp.failed_attempts < rbp.max_failed_attempts;
END;
$$;

-- Grant necessary permissions
GRANT SELECT ON public.bill_categories TO authenticated;
GRANT SELECT ON public.billers TO authenticated;
GRANT ALL ON public.bill_payments TO authenticated;
GRANT ALL ON public.recurring_bill_payments TO authenticated;
GRANT SELECT ON public.bill_payment_history TO authenticated;
GRANT SELECT ON public.biller_outages TO authenticated;

GRANT EXECUTE ON FUNCTION calculate_next_payment_date TO authenticated;
GRANT EXECUTE ON FUNCTION get_due_recurring_payments TO authenticated;
