-- =====================================================
-- JiraniPay Database Setup - Step 1: Extensions and Core Tables
-- =====================================================
-- Run this script first in your Supabase SQL Editor
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- USER PROFILES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    phone_number TEXT UNIQUE NOT NULL,
    email TEXT,
    country_code TEXT NOT NULL DEFAULT 'UG',
    preferred_language TEXT DEFAULT 'en',
    date_of_birth DATE,
    profile_picture_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_level TEXT DEFAULT 'basic',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- USER PREFERENCES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT TRUE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    preferred_currency TEXT DEFAULT 'UGX',
    transaction_limit_daily DECIMAL(15,2) DEFAULT 1000000,
    transaction_limit_monthly DECIMAL(15,2) DEFAULT ********,
    security_pin_enabled BOOLEAN DEFAULT FALSE,
    auto_logout_minutes INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PAYMENT ACCOUNTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.payment_accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    account_type TEXT NOT NULL,
    provider_name TEXT NOT NULL,
    account_number TEXT NOT NULL,
    account_name TEXT,
    account_holder_name TEXT,
    currency TEXT DEFAULT 'UGX',
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    last_balance_update TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    transaction_type TEXT NOT NULL,
    category TEXT,
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'UGX',
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    status TEXT NOT NULL DEFAULT 'pending',
    reference_number TEXT UNIQUE,
    external_reference TEXT,
    description TEXT,
    recipient_info JSONB,
    payment_method_id UUID REFERENCES public.payment_accounts(id),
    provider_name TEXT,
    provider_response JSONB,
    failure_reason TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILL PROVIDERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.bill_providers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    country_code TEXT NOT NULL,
    logo_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    api_endpoint TEXT,
    supported_currencies TEXT[] DEFAULT ARRAY['UGX'],
    min_amount DECIMAL(15,2) DEFAULT 1000,
    max_amount DECIMAL(15,2) DEFAULT ********,
    fee_structure JSONB,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Success message
SELECT 'Step 1 Complete: Core tables created successfully!' as status;
