/**
 * Currency Service
 * Handles currency conversion, exchange rates, and formatting
 */

const axios = require('axios');
const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');

class CurrencyService {
  constructor() {
    this.baseCurrency = 'UGX'; // Base currency for the system
    this.supportedCurrencies = config.business.supportedCurrencies;
    this.exchangeRateCache = 'exchange_rates';
    this.cacheExpiry = 3600; // 1 hour
  }

  /**
   * Get current exchange rates
   */
  async getExchangeRates() {
    try {
      // Try cache first
      const cachedRates = await redisService.get(this.exchangeRateCache);
      if (cachedRates) {
        return cachedRates;
      }

      // Fetch from external API
      const rates = await this.fetchExchangeRates();
      
      // Cache the rates
      await redisService.set(this.exchangeRateCache, rates, this.cacheExpiry);
      
      return rates;
    } catch (error) {
      logger.error('Failed to get exchange rates:', error);
      
      // Return fallback rates if API fails
      return this.getFallbackRates();
    }
  }

  /**
   * Fetch exchange rates from external API
   */
  async fetchExchangeRates() {
    try {
      if (!config.currencyExchange.apiKey) {
        logger.warn('Exchange rate API key not configured, using fallback rates');
        return this.getFallbackRates();
      }

      const response = await axios.get(
        `${config.currencyExchange.baseUrl}/latest/${this.baseCurrency}`,
        {
          params: {
            access_key: config.currencyExchange.apiKey
          },
          timeout: 10000
        }
      );

      if (response.data && response.data.rates) {
        const rates = {
          base: this.baseCurrency,
          rates: {},
          timestamp: new Date().toISOString(),
          source: 'api'
        };

        // Filter only supported currencies
        for (const currency of this.supportedCurrencies) {
          if (currency === this.baseCurrency) {
            rates.rates[currency] = 1;
          } else if (response.data.rates[currency]) {
            rates.rates[currency] = response.data.rates[currency];
          }
        }

        logger.info('Exchange rates fetched successfully', {
          source: 'api',
          currencies: Object.keys(rates.rates).length
        });

        return rates;
      } else {
        throw new Error('Invalid API response format');
      }
    } catch (error) {
      logger.error('Failed to fetch exchange rates from API:', error);
      throw error;
    }
  }

  /**
   * Get fallback exchange rates (static rates for development/fallback)
   */
  getFallbackRates() {
    return {
      base: this.baseCurrency,
      rates: {
        UGX: 1,        // Uganda Shilling (base)
        KES: 0.096,    // Kenyan Shilling
        TZS: 0.157,    // Tanzanian Shilling
        RWF: 0.305,    // Rwandan Franc
        BIF: 0.195,    // Burundian Franc
        ETB: 0.0656    // Ethiopian Birr
      },
      timestamp: new Date().toISOString(),
      source: 'fallback'
    };
  }

  /**
   * Convert currency
   */
  async convertCurrency(amount, fromCurrency, toCurrency) {
    try {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount) || numericAmount < 0) {
        throw new Error('Invalid amount for conversion');
      }

      // Same currency, no conversion needed
      if (fromCurrency === toCurrency) {
        return {
          originalAmount: numericAmount,
          convertedAmount: numericAmount,
          fromCurrency,
          toCurrency,
          exchangeRate: 1,
          timestamp: new Date().toISOString()
        };
      }

      // Validate currencies
      if (!this.supportedCurrencies.includes(fromCurrency) || 
          !this.supportedCurrencies.includes(toCurrency)) {
        throw new Error('Unsupported currency for conversion');
      }

      const rates = await this.getExchangeRates();
      
      let exchangeRate;
      let convertedAmount;

      if (fromCurrency === this.baseCurrency) {
        // Converting from base currency
        exchangeRate = rates.rates[toCurrency];
        convertedAmount = numericAmount * exchangeRate;
      } else if (toCurrency === this.baseCurrency) {
        // Converting to base currency
        exchangeRate = 1 / rates.rates[fromCurrency];
        convertedAmount = numericAmount * exchangeRate;
      } else {
        // Converting between two non-base currencies
        const fromRate = rates.rates[fromCurrency];
        const toRate = rates.rates[toCurrency];
        exchangeRate = toRate / fromRate;
        convertedAmount = numericAmount * exchangeRate;
      }

      // Round to 2 decimal places
      convertedAmount = Math.round(convertedAmount * 100) / 100;

      logger.debug('Currency conversion completed', {
        originalAmount: numericAmount,
        convertedAmount,
        fromCurrency,
        toCurrency,
        exchangeRate
      });

      return {
        originalAmount: numericAmount,
        convertedAmount,
        fromCurrency,
        toCurrency,
        exchangeRate,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Currency conversion failed:', error);
      throw error;
    }
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount, currency, locale = 'en-UG') {
    try {
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        return '0';
      }

      const currencySymbols = {
        UGX: 'UGX',
        KES: 'KES',
        TZS: 'TZS',
        RWF: 'RWF',
        BIF: 'BIF',
        ETB: 'ETB'
      };

      const symbol = currencySymbols[currency] || currency;

      // Format with appropriate decimal places
      const decimals = this.getCurrencyDecimals(currency);
      const formatted = new Intl.NumberFormat(locale, {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
      }).format(numericAmount);

      return `${symbol} ${formatted}`;
    } catch (error) {
      logger.error('Currency formatting failed:', error);
      return `${currency} ${amount}`;
    }
  }

  /**
   * Get currency decimal places
   */
  getCurrencyDecimals(currency) {
    const decimalPlaces = {
      UGX: 0, // Uganda Shilling - no decimals
      KES: 2, // Kenyan Shilling
      TZS: 0, // Tanzanian Shilling - no decimals
      RWF: 0, // Rwandan Franc - no decimals
      BIF: 0, // Burundian Franc - no decimals
      ETB: 2  // Ethiopian Birr
    };

    return decimalPlaces[currency] || 2;
  }

  /**
   * Get currency info
   */
  getCurrencyInfo(currency) {
    const currencyInfo = {
      UGX: {
        name: 'Uganda Shilling',
        symbol: 'UGX',
        country: 'Uganda',
        decimals: 0
      },
      KES: {
        name: 'Kenyan Shilling',
        symbol: 'KES',
        country: 'Kenya',
        decimals: 2
      },
      TZS: {
        name: 'Tanzanian Shilling',
        symbol: 'TZS',
        country: 'Tanzania',
        decimals: 0
      },
      RWF: {
        name: 'Rwandan Franc',
        symbol: 'RWF',
        country: 'Rwanda',
        decimals: 0
      },
      BIF: {
        name: 'Burundian Franc',
        symbol: 'BIF',
        country: 'Burundi',
        decimals: 0
      },
      ETB: {
        name: 'Ethiopian Birr',
        symbol: 'ETB',
        country: 'Ethiopia',
        decimals: 2
      }
    };

    return currencyInfo[currency] || {
      name: currency,
      symbol: currency,
      country: 'Unknown',
      decimals: 2
    };
  }

  /**
   * Get all supported currencies with info
   */
  getSupportedCurrencies() {
    return this.supportedCurrencies.map(currency => ({
      code: currency,
      ...this.getCurrencyInfo(currency)
    }));
  }

  /**
   * Validate currency amount
   */
  validateAmount(amount, currency) {
    try {
      const numericAmount = parseFloat(amount);
      
      if (isNaN(numericAmount)) {
        return { valid: false, error: 'Invalid amount format' };
      }

      if (numericAmount < 0) {
        return { valid: false, error: 'Amount cannot be negative' };
      }

      if (numericAmount === 0) {
        return { valid: false, error: 'Amount cannot be zero' };
      }

      // Check maximum amount (in base currency)
      const maxAmount = config.transactionLimits.maxTransactionAmount;
      if (currency !== this.baseCurrency) {
        // Convert to base currency for limit check
        // This would need the current exchange rate
      }

      const decimals = this.getCurrencyDecimals(currency);
      const factor = Math.pow(10, decimals);
      const rounded = Math.round(numericAmount * factor) / factor;

      if (rounded !== numericAmount && decimals === 0) {
        return { 
          valid: false, 
          error: `${currency} does not support decimal places` 
        };
      }

      return { 
        valid: true, 
        amount: rounded,
        formatted: this.formatCurrency(rounded, currency)
      };

    } catch (error) {
      logger.error('Amount validation failed:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  /**
   * Update exchange rates manually (admin function)
   */
  async updateExchangeRates(rates) {
    try {
      const rateData = {
        base: this.baseCurrency,
        rates: rates,
        timestamp: new Date().toISOString(),
        source: 'manual'
      };

      await redisService.set(this.exchangeRateCache, rateData, this.cacheExpiry);

      logger.audit('Exchange rates updated manually', {
        rates: Object.keys(rates),
        timestamp: rateData.timestamp
      });

      return rateData;
    } catch (error) {
      logger.error('Failed to update exchange rates:', error);
      throw error;
    }
  }

  /**
   * Clear exchange rate cache
   */
  async clearRateCache() {
    try {
      await redisService.del(this.exchangeRateCache);
      logger.info('Exchange rate cache cleared');
    } catch (error) {
      logger.error('Failed to clear rate cache:', error);
    }
  }
}

// Create singleton instance
const currencyService = new CurrencyService();

module.exports = currencyService;
