# JiraniPay Staging Environment Configuration Template
# 🔒 SECURITY NOTICE:
# - This file should NOT contain real credentials
# - Copy this to .env.staging.local and add your actual staging credentials
# - NEVER commit .env.staging.local to version control

# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=staging
EXPO_PUBLIC_ENVIRONMENT=staging

# =============================================================================
# SUPABASE CONFIGURATION (Staging)
# =============================================================================
# Create a separate Supabase project for staging
# Get these values from your STAGING Supabase project dashboard
EXPO_PUBLIC_STAGING_SUPABASE_URL=https://your-staging-project.supabase.co
EXPO_PUBLIC_STAGING_SUPABASE_ANON_KEY=your-staging-anon-key-here

# =============================================================================
# API CONFIGURATION
# =============================================================================
EXPO_PUBLIC_STAGING_API_URL=https://staging-api.jiranipay.com

# =============================================================================
# SECURITY SETTINGS (Staging)
# =============================================================================
# Generate with: openssl rand -base64 32
JWT_SECRET=your-staging-jwt-secret-here
ENCRYPTION_KEY=your-staging-encryption-key-here

# =============================================================================
# EXTERNAL API CONFIGURATION (Staging/Sandbox)
# =============================================================================
# Mobile Money APIs (Sandbox)
MTN_API_KEY=your-mtn-sandbox-key
MTN_SUBSCRIPTION_KEY=your-mtn-sandbox-subscription-key
AIRTEL_CLIENT_ID=your-airtel-sandbox-client-id
AIRTEL_CLIENT_SECRET=your-airtel-sandbox-client-secret

# SMS/Communication APIs (Test)
TWILIO_ACCOUNT_SID=your-twilio-test-account-sid
TWILIO_AUTH_TOKEN=your-twilio-test-auth-token
AFRICASTALKING_USERNAME=your-africastalking-sandbox-username
AFRICASTALKING_API_KEY=your-africastalking-sandbox-api-key

# =============================================================================
# MONITORING & ANALYTICS (Staging)
# =============================================================================
SENTRY_DSN=your-staging-sentry-dsn-here
ANALYTICS_API_KEY=your-staging-analytics-api-key
PERFORMANCE_MONITORING_KEY=your-staging-performance-key

# =============================================================================
# DEVELOPMENT FEATURES (Staging)
# =============================================================================
DEBUG=true
ENABLE_DEBUG_ROUTES=true
ENABLE_TEST_DATA=true
LOG_LEVEL=debug
