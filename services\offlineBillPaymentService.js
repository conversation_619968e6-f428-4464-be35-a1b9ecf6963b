import AsyncStorage from '@react-native-async-storage/async-storage';
import offlineStorageService from './offlineStorageService';
import networkService from './networkService';

/**
 * Offline Bill Payment Service for East African Network Conditions
 * Enables bill payment functionality even with poor or no connectivity
 */
class OfflineBillPaymentService {
  constructor() {
    this.STORAGE_KEYS = {
      BILL_PROVIDERS: 'bill_providers_cache',
      PENDING_PAYMENTS: 'pending_bill_payments',
      PAYMENT_HISTORY: 'bill_payment_history',
      PROVIDER_LOGOS: 'provider_logos_cache',
      AIRTIME_BUNDLES: 'airtime_bundles_cache',
      DATA_BUNDLES: 'data_bundles_cache'
    };
    
    this.defaultProviders = this.getDefaultProviders();
    this.init();
  }

  async init() {
    try {
      // Load default providers if none cached
      const cachedProviders = await this.getBillProviders();
      if (!cachedProviders || cachedProviders.length === 0) {
        await this.cacheDefaultProviders();
      }
      
      console.log('💳 OfflineBillPayment: Service initialized');
    } catch (error) {
      console.error('❌ OfflineBillPayment: Initialization failed:', error);
    }
  }

  getDefaultProviders() {
    return [
      // Electricity Providers
      {
        id: 'umeme',
        name: 'UMEME',
        type: 'electricity',
        category: 'utilities',
        logo: 'flash',
        color: '#FF6B35',
        description: 'Uganda Electricity Distribution',
        fields: [
          { name: 'meter_number', label: 'Meter Number', type: 'text', required: true },
          { name: 'amount', label: 'Amount (UGX)', type: 'number', required: true }
        ],
        minAmount: 1000,
        maxAmount: 1000000,
        offlineCapable: true
      },
      
      // Water Providers
      {
        id: 'nwsc',
        name: 'NWSC',
        type: 'water',
        category: 'utilities',
        logo: 'water',
        color: '#4A90E2',
        description: 'National Water & Sewerage Corp',
        fields: [
          { name: 'account_number', label: 'Account Number', type: 'text', required: true },
          { name: 'amount', label: 'Amount (UGX)', type: 'number', required: true }
        ],
        minAmount: 5000,
        maxAmount: 500000,
        offlineCapable: true
      },
      
      // Mobile Money - Airtime
      {
        id: 'mtn_airtime',
        name: 'MTN Airtime',
        type: 'airtime',
        category: 'mobile',
        logo: 'call',
        color: '#FFCC00',
        description: 'MTN Uganda Airtime',
        fields: [
          { name: 'phone_number', label: 'Phone Number', type: 'phone', required: true },
          { name: 'amount', label: 'Amount (UGX)', type: 'number', required: true }
        ],
        minAmount: 500,
        maxAmount: 100000,
        offlineCapable: true,
        bundles: [
          { amount: 1000, description: '1,000 UGX Airtime' },
          { amount: 2000, description: '2,000 UGX Airtime' },
          { amount: 5000, description: '5,000 UGX Airtime' },
          { amount: 10000, description: '10,000 UGX Airtime' }
        ]
      },
      
      {
        id: 'airtel_airtime',
        name: 'Airtel Airtime',
        type: 'airtime',
        category: 'mobile',
        logo: 'call',
        color: '#E60012',
        description: 'Airtel Uganda Airtime',
        fields: [
          { name: 'phone_number', label: 'Phone Number', type: 'phone', required: true },
          { name: 'amount', label: 'Amount (UGX)', type: 'number', required: true }
        ],
        minAmount: 500,
        maxAmount: 100000,
        offlineCapable: true,
        bundles: [
          { amount: 1000, description: '1,000 UGX Airtime' },
          { amount: 2000, description: '2,000 UGX Airtime' },
          { amount: 5000, description: '5,000 UGX Airtime' },
          { amount: 10000, description: '10,000 UGX Airtime' }
        ]
      },
      
      // Mobile Data
      {
        id: 'mtn_data',
        name: 'MTN Data',
        type: 'data',
        category: 'mobile',
        logo: 'wifi',
        color: '#FFCC00',
        description: 'MTN Uganda Data Bundles',
        fields: [
          { name: 'phone_number', label: 'Phone Number', type: 'phone', required: true },
          { name: 'bundle_id', label: 'Data Bundle', type: 'select', required: true }
        ],
        offlineCapable: true,
        bundles: [
          { id: 'daily_100mb', amount: 1000, data: '100MB', validity: '1 Day', description: '100MB - 1 Day' },
          { id: 'weekly_500mb', amount: 3000, data: '500MB', validity: '7 Days', description: '500MB - 7 Days' },
          { id: 'monthly_1gb', amount: 8000, data: '1GB', validity: '30 Days', description: '1GB - 30 Days' },
          { id: 'monthly_3gb', amount: 20000, data: '3GB', validity: '30 Days', description: '3GB - 30 Days' }
        ]
      },
      
      {
        id: 'airtel_data',
        name: 'Airtel Data',
        type: 'data',
        category: 'mobile',
        logo: 'wifi',
        color: '#E60012',
        description: 'Airtel Uganda Data Bundles',
        fields: [
          { name: 'phone_number', label: 'Phone Number', type: 'phone', required: true },
          { name: 'bundle_id', label: 'Data Bundle', type: 'select', required: true }
        ],
        offlineCapable: true,
        bundles: [
          { id: 'daily_150mb', amount: 1000, data: '150MB', validity: '1 Day', description: '150MB - 1 Day' },
          { id: 'weekly_750mb', amount: 3500, data: '750MB', validity: '7 Days', description: '750MB - 7 Days' },
          { id: 'monthly_1_5gb', amount: 9000, data: '1.5GB', validity: '30 Days', description: '1.5GB - 30 Days' },
          { id: 'monthly_4gb', amount: 22000, data: '4GB', validity: '30 Days', description: '4GB - 30 Days' }
        ]
      },
      
      // TV/Cable
      {
        id: 'dstv',
        name: 'DStv',
        type: 'tv',
        category: 'entertainment',
        logo: 'tv',
        color: '#FF6B00',
        description: 'DStv Uganda',
        fields: [
          { name: 'smartcard_number', label: 'Smartcard Number', type: 'text', required: true },
          { name: 'package_id', label: 'Package', type: 'select', required: true }
        ],
        offlineCapable: true,
        packages: [
          { id: 'access', amount: 15000, name: 'Access', description: 'DStv Access Package' },
          { id: 'family', amount: 35000, name: 'Family', description: 'DStv Family Package' },
          { id: 'compact', amount: 75000, name: 'Compact', description: 'DStv Compact Package' }
        ]
      },
      
      // Internet
      {
        id: 'smile_internet',
        name: 'Smile Internet',
        type: 'internet',
        category: 'internet',
        logo: 'globe',
        color: '#00A651',
        description: 'Smile Communications Uganda',
        fields: [
          { name: 'account_number', label: 'Account Number', type: 'text', required: true },
          { name: 'amount', label: 'Amount (UGX)', type: 'number', required: true }
        ],
        minAmount: 10000,
        maxAmount: 200000,
        offlineCapable: true
      }
    ];
  }

  async cacheDefaultProviders() {
    try {
      await offlineStorageService.cacheData(
        this.STORAGE_KEYS.BILL_PROVIDERS, 
        this.defaultProviders, 
        168 // 7 days cache
      );
      console.log('💳 OfflineBillPayment: Default providers cached');
    } catch (error) {
      console.error('❌ OfflineBillPayment: Failed to cache providers:', error);
    }
  }

  async getBillProviders(category = null) {
    try {
      // Try to get fresh data if online
      const networkStatus = networkService.getNetworkStatus();
      
      if (networkStatus.isConnected && networkStatus.canMakeRequests) {
        try {
          // In a real app, this would fetch from API
          // For now, we'll use cached data with fresh timestamp
          const freshProviders = this.defaultProviders.map(provider => ({
            ...provider,
            lastUpdated: Date.now(),
            fromServer: true
          }));
          
          await offlineStorageService.cacheData(
            this.STORAGE_KEYS.BILL_PROVIDERS,
            freshProviders,
            168
          );
          
          console.log('🌐 OfflineBillPayment: Providers loaded from server');
          return category ? freshProviders.filter(p => p.category === category) : freshProviders;
        } catch (error) {
          console.log('⚠️ OfflineBillPayment: Server fetch failed, using cache');
        }
      }

      // Get from cache
      const cachedData = await offlineStorageService.getCachedData(this.STORAGE_KEYS.BILL_PROVIDERS);
      if (cachedData && cachedData.data) {
        console.log('💾 OfflineBillPayment: Providers loaded from cache');
        const providers = cachedData.data.map(provider => ({
          ...provider,
          fromCache: true,
          expired: cachedData.expired
        }));
        
        return category ? providers.filter(p => p.category === category) : providers;
      }

      // Fallback to defaults
      console.log('📋 OfflineBillPayment: Using default providers');
      return category ? this.defaultProviders.filter(p => p.category === category) : this.defaultProviders;
      
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error getting providers:', error);
      return category ? this.defaultProviders.filter(p => p.category === category) : this.defaultProviders;
    }
  }

  async getProviderById(providerId) {
    try {
      const providers = await this.getBillProviders();
      return providers.find(p => p.id === providerId) || null;
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error getting provider:', error);
      return null;
    }
  }

  async queueBillPayment(paymentData) {
    try {
      const payment = {
        id: `bill_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...paymentData,
        status: 'pending',
        queuedAt: Date.now(),
        type: 'bill_payment',
        retryCount: 0
      };

      // Add to pending transactions
      const pendingId = await offlineStorageService.queuePendingTransaction(payment);
      
      console.log('📥 OfflineBillPayment: Payment queued for processing');
      
      return {
        success: true,
        paymentId: pendingId,
        message: 'Payment queued. Will process when connection is available.',
        queued: true
      };
      
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error queueing payment:', error);
      return {
        success: false,
        error: 'Failed to queue payment for offline processing'
      };
    }
  }

  async processBillPayment(paymentData) {
    try {
      const networkStatus = networkService.getNetworkStatus();
      
      // If offline or poor connection, queue the payment
      if (!networkStatus.isConnected || networkStatus.connectionQuality === 'poor') {
        return await this.queueBillPayment(paymentData);
      }

      // Process payment online
      console.log('🌐 OfflineBillPayment: Processing payment online');
      
      // In a real app, this would make API call to payment processor
      // For demo, we'll simulate processing
      const result = await this.simulatePaymentProcessing(paymentData);
      
      if (result.success) {
        // Save to payment history
        await this.savePaymentToHistory(result.payment);
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error processing payment:', error);
      
      // Queue payment as fallback
      return await this.queueBillPayment(paymentData);
    }
  }

  async simulatePaymentProcessing(paymentData) {
    // Simulate network delay based on connection quality
    const networkStatus = networkService.getNetworkStatus();
    const delay = networkStatus.connectionQuality === 'poor' ? 5000 : 
                  networkStatus.connectionQuality === 'fair' ? 3000 : 1500;
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Simulate 95% success rate
    const success = Math.random() > 0.05;
    
    if (success) {
      const payment = {
        id: `payment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...paymentData,
        status: 'completed',
        processedAt: Date.now(),
        transactionRef: `TXN${Date.now()}`,
        fee: Math.round(paymentData.amount * 0.01) // 1% fee
      };
      
      return {
        success: true,
        payment,
        message: 'Payment processed successfully'
      };
    } else {
      return {
        success: false,
        error: 'Payment processing failed. Please try again.',
        shouldRetry: true
      };
    }
  }

  async savePaymentToHistory(payment) {
    try {
      const history = await this.getPaymentHistory();
      history.unshift(payment); // Add to beginning
      
      // Keep only last 100 payments
      const trimmedHistory = history.slice(0, 100);
      
      await offlineStorageService.cacheData(
        this.STORAGE_KEYS.PAYMENT_HISTORY,
        trimmedHistory,
        720 // 30 days
      );
      
      console.log('💾 OfflineBillPayment: Payment saved to history');
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error saving payment history:', error);
    }
  }

  async getPaymentHistory() {
    try {
      const cachedHistory = await offlineStorageService.getCachedData(this.STORAGE_KEYS.PAYMENT_HISTORY);
      return cachedHistory && cachedHistory.data ? cachedHistory.data : [];
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error getting payment history:', error);
      return [];
    }
  }

  async getPendingPayments() {
    try {
      const pending = await offlineStorageService.getPendingTransactions();
      return pending.filter(t => t.type === 'bill_payment');
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error getting pending payments:', error);
      return [];
    }
  }

  async validatePaymentData(providerId, paymentData) {
    try {
      const provider = await this.getProviderById(providerId);
      if (!provider) {
        return { valid: false, error: 'Provider not found' };
      }

      const errors = [];

      // Validate required fields
      for (const field of provider.fields) {
        if (field.required && !paymentData[field.name]) {
          errors.push(`${field.label} is required`);
        }
      }

      // Validate amount limits
      if (provider.minAmount && paymentData.amount < provider.minAmount) {
        errors.push(`Minimum amount is UGX ${provider.minAmount.toLocaleString()}`);
      }

      if (provider.maxAmount && paymentData.amount > provider.maxAmount) {
        errors.push(`Maximum amount is UGX ${provider.maxAmount.toLocaleString()}`);
      }

      // Validate phone number format for mobile services
      if (paymentData.phone_number) {
        const phoneRegex = /^(\+256|0)?[7][0-9]{8}$/;
        if (!phoneRegex.test(paymentData.phone_number)) {
          errors.push('Please enter a valid Ugandan phone number');
        }
      }

      return {
        valid: errors.length === 0,
        errors: errors.length > 0 ? errors : null
      };
      
    } catch (error) {
      console.error('❌ OfflineBillPayment: Error validating payment:', error);
      return { valid: false, error: 'Validation failed' };
    }
  }

  // Get connection-appropriate timeout for bill payments
  getPaymentTimeout() {
    const networkStatus = networkService.getNetworkStatus();
    switch (networkStatus.connectionQuality) {
      case 'poor': return 45000; // 45 seconds
      case 'fair': return 30000;  // 30 seconds
      case 'good': return 20000;  // 20 seconds
      case 'excellent': return 15000; // 15 seconds
      default: return 30000;
    }
  }

  // Check if provider supports offline payments
  isOfflineCapable(providerId) {
    const provider = this.defaultProviders.find(p => p.id === providerId);
    return provider ? provider.offlineCapable : false;
  }
}

export default new OfflineBillPaymentService();
