/**
 * Production SMS Monitoring and Diagnostics for JiraniPay
 * 
 * This utility provides comprehensive SMS delivery monitoring,
 * diagnostics, and reporting for production environments.
 */

import { supabase } from '../config/supabase';
import { isProductionMode } from '../config/environment';

class SMSMonitoring {
  constructor() {
    this.deliveryStats = {
      sent: 0,
      delivered: 0,
      failed: 0,
      pending: 0
    };
    this.errorLog = [];
    this.startTime = new Date();
  }

  /**
   * Test SMS provider configuration and connectivity
   */
  async testSMSConfiguration() {
    console.log('🔍 Testing Production SMS Configuration...');
    
    const results = {
      timestamp: new Date().toISOString(),
      environment: isProductionMode() ? 'production' : 'development',
      tests: []
    };

    // Test 1: Basic SMS provider connectivity
    try {
      const connectivityTest = await supabase.auth.signInWithOtp({
        phone: '+**********' // Test number
      });

      if (connectivityTest.error) {
        const errorMsg = connectivityTest.error.message?.toLowerCase() || '';
        
        if (errorMsg.includes('sms') || errorMsg.includes('provider')) {
          results.tests.push({
            test: 'SMS Provider Configuration',
            status: 'FAILED',
            error: 'SMS provider not configured',
            solution: 'Configure Twilio/MessageBird in Supabase Dashboard',
            details: connectivityTest.error.message
          });
        } else if (errorMsg.includes('invalid phone')) {
          results.tests.push({
            test: 'SMS Provider Configuration',
            status: 'PASSED',
            message: 'SMS provider is configured (phone validation working)'
          });
        }
      } else {
        results.tests.push({
          test: 'SMS Provider Configuration',
          status: 'PASSED',
          message: 'SMS provider appears to be configured correctly'
        });
      }
    } catch (error) {
      results.tests.push({
        test: 'SMS Provider Configuration',
        status: 'ERROR',
        error: 'Unable to test SMS configuration',
        details: error.message
      });
    }

    // Test 2: Phone number format validation
    const phoneFormats = [
      '+256777123456', // Uganda MTN
      '+256701123456', // Uganda Airtel
      '+254722123456', // Kenya
      '+255754123456'  // Tanzania
    ];

    for (const phone of phoneFormats) {
      try {
        const formatTest = await supabase.auth.signInWithOtp({ phone });
        
        results.tests.push({
          test: `Phone Format: ${phone}`,
          status: formatTest.error ? 'VALIDATION_ERROR' : 'PASSED',
          details: formatTest.error?.message || 'Format accepted'
        });
      } catch (error) {
        results.tests.push({
          test: `Phone Format: ${phone}`,
          status: 'ERROR',
          details: error.message
        });
      }
    }

    return results;
  }

  /**
   * Monitor SMS delivery for a specific registration attempt
   */
  async monitorSMSDelivery(phoneNumber, userId) {
    const deliveryLog = {
      phoneNumber,
      userId,
      timestamp: new Date().toISOString(),
      status: 'INITIATED',
      attempts: 1,
      errors: []
    };

    console.log(`📱 Monitoring SMS delivery to ${phoneNumber}`);

    try {
      // Check user confirmation status periodically
      const checkConfirmation = async () => {
        try {
          const { data: user, error } = await supabase.auth.admin.getUserById(userId);
          
          if (error) {
            deliveryLog.errors.push({
              timestamp: new Date().toISOString(),
              error: 'Failed to check user status',
              details: error.message
            });
            return false;
          }

          if (user?.phone_confirmed_at) {
            deliveryLog.status = 'CONFIRMED';
            deliveryLog.confirmedAt = user.phone_confirmed_at;
            console.log(`✅ SMS delivery confirmed for ${phoneNumber}`);
            return true;
          }

          return false;
        } catch (error) {
          deliveryLog.errors.push({
            timestamp: new Date().toISOString(),
            error: 'Monitoring error',
            details: error.message
          });
          return false;
        }
      };

      // Monitor for 5 minutes
      const monitoringDuration = 5 * 60 * 1000; // 5 minutes
      const checkInterval = 30 * 1000; // 30 seconds
      const startTime = Date.now();

      while (Date.now() - startTime < monitoringDuration) {
        if (await checkConfirmation()) {
          break;
        }
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }

      if (deliveryLog.status !== 'CONFIRMED') {
        deliveryLog.status = 'TIMEOUT';
        deliveryLog.errors.push({
          timestamp: new Date().toISOString(),
          error: 'SMS delivery timeout',
          details: 'User did not confirm phone within 5 minutes'
        });
      }

    } catch (error) {
      deliveryLog.status = 'ERROR';
      deliveryLog.errors.push({
        timestamp: new Date().toISOString(),
        error: 'Monitoring failed',
        details: error.message
      });
    }

    return deliveryLog;
  }

  /**
   * Generate SMS delivery report
   */
  generateDeliveryReport() {
    const uptime = Date.now() - this.startTime.getTime();
    const uptimeHours = (uptime / (1000 * 60 * 60)).toFixed(2);

    return {
      reportTimestamp: new Date().toISOString(),
      environment: isProductionMode() ? 'production' : 'development',
      uptime: `${uptimeHours} hours`,
      statistics: {
        totalSent: this.deliveryStats.sent,
        delivered: this.deliveryStats.delivered,
        failed: this.deliveryStats.failed,
        pending: this.deliveryStats.pending,
        deliveryRate: this.deliveryStats.sent > 0 
          ? ((this.deliveryStats.delivered / this.deliveryStats.sent) * 100).toFixed(2) + '%'
          : '0%'
      },
      recentErrors: this.errorLog.slice(-10), // Last 10 errors
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * Generate recommendations based on delivery statistics
   */
  generateRecommendations() {
    const recommendations = [];
    const deliveryRate = this.deliveryStats.sent > 0 
      ? (this.deliveryStats.delivered / this.deliveryStats.sent) * 100 
      : 0;

    if (deliveryRate < 90) {
      recommendations.push({
        priority: 'HIGH',
        issue: 'Low SMS delivery rate',
        recommendation: 'Check SMS provider status and credentials',
        action: 'Review Supabase Authentication logs and SMS provider dashboard'
      });
    }

    if (this.deliveryStats.failed > 5) {
      recommendations.push({
        priority: 'MEDIUM',
        issue: 'High failure count',
        recommendation: 'Investigate common failure patterns',
        action: 'Review error logs and phone number formats'
      });
    }

    if (this.errorLog.length > 20) {
      recommendations.push({
        priority: 'MEDIUM',
        issue: 'High error frequency',
        recommendation: 'Monitor SMS provider health',
        action: 'Check SMS provider status page and quotas'
      });
    }

    return recommendations;
  }

  /**
   * Log SMS delivery attempt
   */
  logDeliveryAttempt(phoneNumber, status, error = null) {
    this.deliveryStats.sent++;
    
    switch (status) {
      case 'delivered':
        this.deliveryStats.delivered++;
        break;
      case 'failed':
        this.deliveryStats.failed++;
        if (error) {
          this.errorLog.push({
            timestamp: new Date().toISOString(),
            phoneNumber,
            error: error.message || error,
            type: 'delivery_failure'
          });
        }
        break;
      case 'pending':
        this.deliveryStats.pending++;
        break;
    }

    // Keep error log manageable
    if (this.errorLog.length > 100) {
      this.errorLog = this.errorLog.slice(-50);
    }
  }
}

// Create singleton instance
const smsMonitoring = new SMSMonitoring();

export default smsMonitoring;

// Export utility functions
export const testSMSConfiguration = () => smsMonitoring.testSMSConfiguration();
export const monitorSMSDelivery = (phone, userId) => smsMonitoring.monitorSMSDelivery(phone, userId);
export const getSMSReport = () => smsMonitoring.generateDeliveryReport();
export const logSMSAttempt = (phone, status, error) => smsMonitoring.logDeliveryAttempt(phone, status, error);
