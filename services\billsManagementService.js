import AsyncStorage from '@react-native-async-storage/async-storage';
import billPaymentService from './billPaymentService';
import notificationService from './notificationService';

class BillsManagementService {
  constructor() {
    this.isInitialized = false;
    this.trackedBills = [];
    this.subscriptions = [];
    this.billReminders = [];
    this.storageKeys = {
      trackedBills: 'tracked_bills',
      subscriptions: 'user_subscriptions',
      billReminders: 'bill_reminders'
    };
  }

  async initialize() {
    try {
      console.log('📋 Initializing bills management service...');
      
      await this.loadTrackedBills();
      await this.loadSubscriptions();
      await this.loadBillReminders();
      
      this.isInitialized = true;
      console.log('✅ Bills management service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing bills management service:', error);
      return { success: false, error: error.message };
    }
  }

  async loadTrackedBills() {
    try {
      const stored = await AsyncStorage.getItem(this.storageKeys.trackedBills);
      if (stored) {
        this.trackedBills = JSON.parse(stored);
        console.log('📋 Loaded tracked bills:', this.trackedBills.length);
      }
    } catch (error) {
      console.error('❌ Error loading tracked bills:', error);
      this.trackedBills = [];
    }
  }

  async saveTrackedBills() {
    try {
      await AsyncStorage.setItem(this.storageKeys.trackedBills, JSON.stringify(this.trackedBills));
    } catch (error) {
      console.error('❌ Error saving tracked bills:', error);
    }
  }

  async loadSubscriptions() {
    try {
      const stored = await AsyncStorage.getItem(this.storageKeys.subscriptions);
      if (stored) {
        this.subscriptions = JSON.parse(stored);
        console.log('📱 Loaded subscriptions:', this.subscriptions.length);
      }
    } catch (error) {
      console.error('❌ Error loading subscriptions:', error);
      this.subscriptions = [];
    }
  }

  async saveSubscriptions() {
    try {
      await AsyncStorage.setItem(this.storageKeys.subscriptions, JSON.stringify(this.subscriptions));
    } catch (error) {
      console.error('❌ Error saving subscriptions:', error);
    }
  }

  async loadBillReminders() {
    try {
      const stored = await AsyncStorage.getItem(this.storageKeys.billReminders);
      if (stored) {
        this.billReminders = JSON.parse(stored);
        console.log('🔔 Loaded bill reminders:', this.billReminders.length);
      }
    } catch (error) {
      console.error('❌ Error loading bill reminders:', error);
      this.billReminders = [];
    }
  }

  async saveBillReminders() {
    try {
      await AsyncStorage.setItem(this.storageKeys.billReminders, JSON.stringify(this.billReminders));
    } catch (error) {
      console.error('❌ Error saving bill reminders:', error);
    }
  }

  /**
   * Add a bill to tracking
   */
  async addTrackedBill(billData) {
    try {
      const {
        name,
        provider,
        accountNumber,
        category,
        averageAmount,
        dueDate, // Day of month (1-31)
        isRecurring = true,
        reminderDays = 3, // Days before due date to remind
        isActive = true
      } = billData;

      const bill = {
        id: `bill_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        provider,
        accountNumber,
        category,
        averageAmount: parseFloat(averageAmount),
        dueDate: parseInt(dueDate),
        isRecurring,
        reminderDays: parseInt(reminderDays),
        isActive,
        createdAt: new Date(),
        lastPaid: null,
        paymentHistory: []
      };

      this.trackedBills.push(bill);
      await this.saveTrackedBills();

      console.log('✅ Bill added to tracking:', bill.name);
      return { success: true, data: bill };
    } catch (error) {
      console.error('❌ Error adding tracked bill:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a subscription to tracking
   */
  async addSubscription(subscriptionData) {
    try {
      const {
        name,
        provider,
        category,
        monthlyAmount,
        billingCycle, // 'monthly', 'yearly', 'weekly'
        startDate,
        isActive = true,
        reminderEnabled = true
      } = subscriptionData;

      const subscription = {
        id: `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        provider,
        category,
        monthlyAmount: parseFloat(monthlyAmount),
        billingCycle,
        startDate: new Date(startDate),
        nextBillingDate: this.calculateNextBillingDate(new Date(startDate), billingCycle),
        isActive,
        reminderEnabled,
        createdAt: new Date(),
        totalSpent: 0,
        paymentHistory: []
      };

      this.subscriptions.push(subscription);
      await this.saveSubscriptions();

      console.log('✅ Subscription added:', subscription.name);
      return { success: true, data: subscription };
    } catch (error) {
      console.error('❌ Error adding subscription:', error);
      return { success: false, error: error.message };
    }
  }

  calculateNextBillingDate(startDate, billingCycle) {
    const nextDate = new Date(startDate);
    
    switch (billingCycle) {
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      default:
        nextDate.setMonth(nextDate.getMonth() + 1);
    }
    
    return nextDate;
  }

  /**
   * Create bill reminder
   */
  async createBillReminder(reminderData) {
    try {
      const {
        billId,
        title,
        description,
        reminderType, // 'due_date', 'subscription_renewal', 'audit_reminder'
        reminderDate,
        isRecurring = false,
        isActive = true
      } = reminderData;

      const reminder = {
        id: `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        billId,
        title,
        description,
        reminderType,
        reminderDate: new Date(reminderDate),
        isRecurring,
        isActive,
        createdAt: new Date(),
        lastSent: null
      };

      this.billReminders.push(reminder);
      await this.saveBillReminders();

      console.log('✅ Bill reminder created:', reminder.title);
      return { success: true, data: reminder };
    } catch (error) {
      console.error('❌ Error creating bill reminder:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get bills due soon
   */
  getBillsDueSoon(daysAhead = 7) {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + daysAhead);

    return this.trackedBills.filter(bill => {
      if (!bill.isActive) return false;
      
      const dueThisMonth = new Date(today.getFullYear(), today.getMonth(), bill.dueDate);
      const dueNextMonth = new Date(today.getFullYear(), today.getMonth() + 1, bill.dueDate);
      
      return (dueThisMonth >= today && dueThisMonth <= futureDate) ||
             (dueNextMonth >= today && dueNextMonth <= futureDate);
    });
  }

  /**
   * Get subscription audit recommendations
   */
  getSubscriptionAuditRecommendations() {
    const recommendations = [];
    const now = new Date();
    
    this.subscriptions.forEach(sub => {
      // Check for unused subscriptions (no recent activity)
      const daysSinceCreated = Math.floor((now - new Date(sub.createdAt)) / (1000 * 60 * 60 * 24));
      
      if (daysSinceCreated > 30 && sub.paymentHistory.length === 0) {
        recommendations.push({
          type: 'unused',
          subscription: sub,
          reason: 'No payment history - might be unused',
          potentialSavings: sub.monthlyAmount * 12
        });
      }
      
      // Check for expensive subscriptions
      if (sub.monthlyAmount > 50000) {
        recommendations.push({
          type: 'expensive',
          subscription: sub,
          reason: 'High monthly cost - consider alternatives',
          potentialSavings: sub.monthlyAmount * 0.3 // 30% potential savings
        });
      }
    });
    
    return recommendations;
  }

  /**
   * Process bill reminders
   */
  async processBillReminders() {
    const now = new Date();
    
    for (const reminder of this.billReminders) {
      if (!reminder.isActive) continue;
      
      if (reminder.reminderDate <= now) {
        await this.sendBillReminderNotification(reminder);
        
        if (reminder.isRecurring) {
          // Update next reminder date
          reminder.reminderDate = this.calculateNextReminderDate(reminder);
        } else {
          // Deactivate one-time reminder
          reminder.isActive = false;
        }
        
        reminder.lastSent = new Date();
      }
    }
    
    await this.saveBillReminders();
  }

  calculateNextReminderDate(reminder) {
    const nextDate = new Date(reminder.reminderDate);
    nextDate.setMonth(nextDate.getMonth() + 1); // Monthly recurring by default
    return nextDate;
  }

  async sendBillReminderNotification(reminder) {
    try {
      await notificationService.scheduleTransactionNotification({
        id: `bill_reminder_${reminder.id}`,
        type: 'debit',
        amount: 0,
        description: `📋 ${reminder.title}: ${reminder.description}`,
        status: 'pending'
      });
      
      console.log('🔔 Bill reminder notification sent:', reminder.title);
    } catch (error) {
      console.error('❌ Error sending bill reminder notification:', error);
    }
  }

  /**
   * Get bills summary
   */
  getBillsSummary() {
    const activeBills = this.trackedBills.filter(bill => bill.isActive);
    const activeSubscriptions = this.subscriptions.filter(sub => sub.isActive);
    
    const totalMonthlyBills = activeBills.reduce((sum, bill) => sum + bill.averageAmount, 0);
    const totalMonthlySubscriptions = activeSubscriptions.reduce((sum, sub) => sum + sub.monthlyAmount, 0);
    
    return {
      totalBills: activeBills.length,
      totalSubscriptions: activeSubscriptions.length,
      totalMonthlyAmount: totalMonthlyBills + totalMonthlySubscriptions,
      billsDueSoon: this.getBillsDueSoon().length,
      auditRecommendations: this.getSubscriptionAuditRecommendations().length
    };
  }

  // Getter methods
  getTrackedBills() {
    return this.trackedBills.filter(bill => bill.isActive);
  }

  getSubscriptions() {
    return this.subscriptions.filter(sub => sub.isActive);
  }

  getBillReminders() {
    return this.billReminders.filter(reminder => reminder.isActive);
  }
}

// Export singleton instance
const billsManagementService = new BillsManagementService();
export default billsManagementService;
