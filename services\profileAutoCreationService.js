/**
 * Profile Auto-Creation Service for JiraniPay
 * 
 * This service ensures that user profiles are automatically created
 * when they don't exist, preventing "no profile found" errors.
 */

import supabase from './supabaseClient';
import databaseService from './databaseService';

class ProfileAutoCreationService {
  constructor() {
    this.tableName = 'profiles'; // Use the correct table name
    this.creationLocks = new Map(); // Mutex to prevent race conditions
  }

  /**
   * Ensure user profile exists, create if missing
   * @param {Object} user - Supabase auth user object
   * @returns {Promise<Object>} - Profile data
   */
  async ensureProfileExists(user) {
    try {
      if (!user || !user.id) {
        throw new Error('Invalid user object provided');
      }

      // Check if profile creation is already in progress for this user
      if (this.creationLocks.has(user.id)) {
        console.log('⏳ Profile creation already in progress for user:', user.id);
        // Wait for the existing creation to complete
        return await this.creationLocks.get(user.id);
      }

      console.log('🔍 Checking if profile exists for user:', user.id);

      // First, try to get existing profile
      const { data: existingProfile, error: getError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('id', user.id)
        .single();

      // If profile exists, return it
      if (existingProfile && !getError) {
        console.log('✅ Profile already exists:', existingProfile);
        return { success: true, data: existingProfile, created: false };
      }

      // If error is not "no rows found", throw it
      if (getError && getError.code !== 'PGRST116') {
        console.error('❌ Error checking profile:', getError);
        throw getError;
      }

      // Profile doesn't exist, create it with mutex lock
      console.log('🔧 Profile not found, creating new profile...');

      // Create a promise for profile creation and store it in the lock
      const creationPromise = this.createProfileFromUser(user);
      this.creationLocks.set(user.id, creationPromise);

      try {
        const result = await creationPromise;
        return result;
      } finally {
        // Always remove the lock when done
        this.creationLocks.delete(user.id);
      }

    } catch (error) {
      console.error('❌ Error ensuring profile exists:', error);
      // Make sure to clean up the lock on error
      this.creationLocks.delete(user.id);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create profile from Supabase auth user data
   * @param {Object} user - Supabase auth user object
   * @returns {Promise<Object>} - Created profile data
   */
  async createProfileFromUser(user) {
    try {
      console.log('🔧 Creating profile from user data:', {
        id: user.id,
        email: user.email,
        phone: user.phone,
        metadata: user.user_metadata
      });

      // Extract name from various possible sources
      const fullName = this.extractFullName(user);
      const phone = this.extractPhone(user);
      const email = this.extractEmail(user);

      // ✅ CRITICAL FIX: Log what name we're about to use
      console.log('🔍 Name extraction result:', {
        extractedName: fullName,
        isAutoGenerated: fullName.match(/^User \d+$/),
        userMetadataFullName: user.user_metadata?.full_name,
        userMetadataName: user.user_metadata?.name
      });

      // Create profile data
      const profileData = {
        id: user.id, // Primary key that references auth.users(id)
        full_name: fullName,
        phone: phone,
        email: email,
        country_code: 'UG', // Default to Uganda
        language_preference: 'en', // Default to English
        kyc_status: 'pending',
        kyc_level: 1,
        is_active: true,
        metadata: {
          created_by: 'auto_creation_service',
          source: 'auth_user_data',
          created_at: new Date().toISOString()
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📝 Creating profile with data:', profileData);

      // Insert the profile with race condition handling
      const { data: createdProfile, error: createError } = await supabase
        .from(this.tableName)
        .insert(profileData)
        .select()
        .single();

      if (createError) {
        // Handle duplicate key constraint violation (race condition)
        if (createError.code === '23505') {
          console.log('⚠️ Profile already exists (race condition detected), fetching existing profile...');

          // Fetch the existing profile
          const { data: existingProfile, error: fetchError } = await supabase
            .from(this.tableName)
            .select('*')
            .eq('id', user.id)
            .single();

          if (existingProfile && !fetchError) {
            console.log('✅ Retrieved existing profile after race condition');
            return { success: true, data: existingProfile, created: false };
          } else {
            console.error('❌ Could not fetch existing profile after race condition:', fetchError);
            throw createError;
          }
        } else {
          console.error('❌ Error creating profile:', createError);
          throw createError;
        }
      }

      console.log('✅ Profile created successfully:', createdProfile);

      // Also create user preferences
      try {
        await this.createDefaultPreferences(user.id);
      } catch (prefError) {
        console.warn('⚠️ Could not create default preferences:', prefError.message);
      }

      return { success: true, data: createdProfile, created: true };

    } catch (error) {
      console.error('❌ Error creating profile from user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract full name from user data
   * @param {Object} user - Supabase auth user
   * @returns {string} - Full name
   */
  extractFullName(user) {
    console.log('🔍 Extracting full name from user data:', {
      hasUserMetadata: !!user.user_metadata,
      fullName: user.user_metadata?.full_name,
      name: user.user_metadata?.name,
      firstName: user.user_metadata?.first_name,
      lastName: user.user_metadata?.last_name,
      email: user.email,
      phone: user.phone
    });

    // Priority 1: Try user_metadata.full_name (from registration)
    if (user.user_metadata?.full_name && user.user_metadata.full_name.trim()) {
      console.log('✅ Using full_name from user_metadata:', user.user_metadata.full_name);
      return user.user_metadata.full_name.trim();
    }

    // Priority 2: Try user_metadata.name
    if (user.user_metadata?.name && user.user_metadata.name.trim()) {
      console.log('✅ Using name from user_metadata:', user.user_metadata.name);
      return user.user_metadata.name.trim();
    }

    // Priority 3: Try first_name + last_name combination
    if (user.user_metadata?.first_name && user.user_metadata?.last_name) {
      const fullName = `${user.user_metadata.first_name} ${user.user_metadata.last_name}`.trim();
      console.log('✅ Using first_name + last_name:', fullName);
      return fullName;
    }

    // Priority 4: Try just first_name
    if (user.user_metadata?.first_name && user.user_metadata.first_name.trim()) {
      console.log('✅ Using first_name only:', user.user_metadata.first_name);
      return user.user_metadata.first_name.trim();
    }

    // Priority 5: Extract from email (only if it looks like a real name)
    if (user.email) {
      const emailName = user.email.split('@')[0].replace(/[._]/g, ' ').trim();
      // Only use email name if it looks like a real name (not just numbers/random chars)
      if (emailName.length > 2 && /^[a-zA-Z]/.test(emailName) && !/^\d+$/.test(emailName)) {
        console.log('✅ Using name from email:', emailName);
        return emailName;
      }
    }

    // Priority 6: Phone-based fallback (only as last resort)
    if (user.phone) {
      const phoneName = `User ${user.phone.slice(-4)}`;
      console.log('⚠️ Using phone-based fallback name:', phoneName);
      return phoneName;
    }

    console.log('⚠️ Using ultimate fallback name: JiraniPay User');
    return 'JiraniPay User'; // Ultimate fallback
  }

  /**
   * Extract phone number from user data
   * @param {Object} user - Supabase auth user
   * @returns {string} - Phone number
   */
  extractPhone(user) {
    if (user.phone) {
      return user.phone;
    }
    if (user.user_metadata?.phone) {
      return user.user_metadata.phone;
    }
    if (user.user_metadata?.phone_number) {
      return user.user_metadata.phone_number;
    }
    return null;
  }

  /**
   * Extract email from user data
   * @param {Object} user - Supabase auth user
   * @returns {string} - Email
   */
  extractEmail(user) {
    if (user.email) {
      return user.email;
    }
    if (user.user_metadata?.email) {
      return user.user_metadata.email;
    }
    return null;
  }

  /**
   * Create default user preferences
   * @param {string} userId - User ID
   */
  async createDefaultPreferences(userId) {
    try {
      const defaultPreferences = {
        id: userId, // Use 'id' not 'user_id' as per schema
        preferred_currency: 'UGX', // Use 'preferred_currency' not 'currency'
        language_preference: 'en', // Use 'language_preference' not 'language'
        notifications_enabled: true,
        biometric_enabled: false,
        dark_mode_enabled: false,
        sms_notifications: true,
        email_notifications: true,
        transaction_limit_daily: 1000000,
        transaction_limit_monthly: 10000000,
        security_pin_enabled: false,
        auto_logout_minutes: 30,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('user_preferences')
        .insert(defaultPreferences);

      if (error) {
        console.warn('⚠️ Could not create default preferences:', error.message);
      } else {
        console.log('✅ Default preferences created');
      }
    } catch (error) {
      console.warn('⚠️ Error creating default preferences:', error.message);
    }
  }

  /**
   * Auto-fix missing profile for current user
   * @returns {Promise<Object>} - Result
   */
  async autoFixCurrentUser() {
    try {
      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        return { success: false, error: 'No authenticated user found' };
      }

      console.log('🔧 Auto-fixing profile for current user:', user.id);
      return await this.ensureProfileExists(user);

    } catch (error) {
      console.error('❌ Error auto-fixing current user:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Batch fix missing profiles for multiple users
   * @param {Array} userIds - Array of user IDs
   * @returns {Promise<Object>} - Results
   */
  async batchFixProfiles(userIds) {
    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    for (const userId of userIds) {
      try {
        // Get user from auth
        const { data: { user }, error } = await supabase.auth.admin.getUserById(userId);
        
        if (error || !user) {
          results.failed++;
          results.errors.push(`User ${userId}: ${error?.message || 'User not found'}`);
          continue;
        }

        const result = await this.ensureProfileExists(user);
        if (result.success) {
          results.success++;
        } else {
          results.failed++;
          results.errors.push(`User ${userId}: ${result.error}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`User ${userId}: ${error.message}`);
      }
    }

    return results;
  }
}

// Create and export singleton instance
const profileAutoCreationService = new ProfileAutoCreationService();
export default profileAutoCreationService;
