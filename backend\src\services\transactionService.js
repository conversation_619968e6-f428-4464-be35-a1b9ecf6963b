/**
 * Transaction Processing Service
 * Handles transaction lifecycle, validation, status tracking, and audit trails
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('./database');
const redisService = require('./redis');
const walletService = require('./walletService');
const currencyService = require('./currencyService');
const smsService = require('./sms');
const fraudDetectionService = require('./fraudDetectionService');

class TransactionService {
  constructor() {
    this.transactionStatuses = {
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled',
      EXPIRED: 'expired'
    };

    this.transactionTypes = {
      TRANSFER: 'transfer',
      TOPUP: 'topup',
      WITHDRAWAL: 'withdrawal',
      BILL_PAYMENT: 'bill_payment',
      REFUND: 'refund'
    };

    this.processingTimeout = 30 * 60 * 1000; // 30 minutes
  }

  /**
   * Create a new transaction
   */
  async createTransaction(transactionData, userContext = {}) {
    try {
      const {
        type,
        fromUserId,
        toUserId,
        amount,
        currency,
        description,
        metadata = {},
        provider = null,
        externalReference = null
      } = transactionData;

      // Validate transaction data
      await this.validateTransaction(transactionData);

      // Perform fraud detection analysis
      const fraudAnalysis = await fraudDetectionService.analyzeTransaction(
        transactionData,
        userContext
      );

      // Block transaction if high risk
      if (fraudAnalysis.shouldBlock) {
        logger.security('Transaction blocked due to fraud risk', {
          transactionData,
          fraudAnalysis
        });
        throw new Error('Transaction blocked due to security concerns');
      }

      // Generate transaction reference
      const transactionReference = this.generateTransactionReference(type);

      // Determine initial status based on fraud analysis
      let initialStatus = this.transactionStatuses.PENDING;
      if (fraudAnalysis.requiresReview) {
        initialStatus = 'review_required';
        logger.security('Transaction requires manual review', {
          transactionReference,
          fraudAnalysis
        });
      }

      const transaction = {
        id: uuidv4(),
        transaction_reference: transactionReference,
        type,
        status: initialStatus,
        from_user_id: fromUserId || null,
        to_user_id: toUserId || null,
        amount: parseFloat(amount),
        currency,
        description,
        metadata: JSON.stringify({
          ...metadata,
          fraudAnalysis: fraudAnalysis
        }),
        provider,
        external_reference: externalReference,
        created_at: new Date().toISOString()
      };

      // Store in database
      const supabase = databaseService.getSupabase();
      const { data: createdTransaction, error } = await supabase
        .from('transactions')
        .insert(transaction)
        .select()
        .single();

      if (error) {
        logger.error('Failed to create transaction:', error);
        throw new Error('Failed to create transaction');
      }

      // Cache transaction for quick access
      await redisService.set(
        `transaction:${createdTransaction.id}`,
        createdTransaction,
        3600 // 1 hour
      );

      // Set processing timeout
      await this.setProcessingTimeout(createdTransaction.id);

      logger.audit('Transaction created', {
        transactionId: createdTransaction.id,
        reference: transactionReference,
        type,
        amount,
        currency,
        fromUserId,
        toUserId
      });

      return createdTransaction;
    } catch (error) {
      logger.error('Transaction creation failed:', error);
      throw error;
    }
  }

  /**
   * Process a transaction
   */
  async processTransaction(transactionId) {
    try {
      const transaction = await this.getTransaction(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (transaction.status !== this.transactionStatuses.PENDING) {
        throw new Error(`Transaction cannot be processed. Current status: ${transaction.status}`);
      }

      // Update status to processing
      await this.updateTransactionStatus(transactionId, this.transactionStatuses.PROCESSING);

      let result;
      switch (transaction.type) {
        case this.transactionTypes.TRANSFER:
          result = await this.processTransfer(transaction);
          break;
        case this.transactionTypes.TOPUP:
          result = await this.processTopup(transaction);
          break;
        case this.transactionTypes.WITHDRAWAL:
          result = await this.processWithdrawal(transaction);
          break;
        case this.transactionTypes.BILL_PAYMENT:
          result = await this.processBillPayment(transaction);
          break;
        default:
          throw new Error(`Unsupported transaction type: ${transaction.type}`);
      }

      // Update transaction with result
      await this.completeTransaction(transactionId, result);

      return result;
    } catch (error) {
      logger.error('Transaction processing failed:', error);
      await this.failTransaction(transactionId, error.message);
      throw error;
    }
  }

  /**
   * Process money transfer
   */
  async processTransfer(transaction) {
    try {
      const { from_user_id, to_user_id, amount, currency, description } = transaction;

      // Validate users and wallets
      const fromWallet = await walletService.getWallet(from_user_id);
      const toWallet = await walletService.getWallet(to_user_id);

      if (!fromWallet || !fromWallet.is_active) {
        throw new Error('Sender wallet not found or inactive');
      }

      if (!toWallet || !toWallet.is_active) {
        throw new Error('Recipient wallet not found or inactive');
      }

      // Check balance
      if (fromWallet.available_balance < amount) {
        throw new Error('Insufficient balance');
      }

      // Perform transfer using wallet service
      const transferResult = await walletService.transferMoney(
        from_user_id,
        to_user_id,
        amount,
        description
      );

      // Send notifications
      await this.sendTransactionNotifications(transaction, 'completed');

      return {
        success: true,
        transferId: transferResult.transferId,
        amount: transferResult.amount,
        convertedAmount: transferResult.convertedAmount,
        exchangeRate: transferResult.exchangeRate
      };
    } catch (error) {
      logger.error('Transfer processing failed:', error);
      throw error;
    }
  }

  /**
   * Process wallet top-up
   */
  async processTopup(transaction) {
    try {
      // This would integrate with actual payment providers
      // For now, simulate the process
      
      const { to_user_id, amount, currency, provider, external_reference } = transaction;

      // Validate wallet
      const wallet = await walletService.getWallet(to_user_id);
      if (!wallet || !wallet.is_active) {
        throw new Error('Wallet not found or inactive');
      }

      // In production, this would:
      // 1. Call payment provider API
      // 2. Wait for confirmation
      // 3. Update wallet balance on success

      // For simulation, directly update wallet
      await walletService.updateBalance(
        to_user_id,
        amount,
        'credit',
        `Top-up via ${provider}`,
        transaction.id
      );

      // Send notification
      await this.sendTransactionNotifications(transaction, 'completed');

      return {
        success: true,
        amount,
        currency,
        provider,
        externalReference: external_reference
      };
    } catch (error) {
      logger.error('Top-up processing failed:', error);
      throw error;
    }
  }

  /**
   * Process withdrawal
   */
  async processWithdrawal(transaction) {
    try {
      const { from_user_id, amount, currency, provider } = transaction;

      // Validate wallet
      const wallet = await walletService.getWallet(from_user_id);
      if (!wallet || !wallet.is_active) {
        throw new Error('Wallet not found or inactive');
      }

      // Check balance
      if (wallet.available_balance < amount) {
        throw new Error('Insufficient balance');
      }

      // Debit wallet
      await walletService.updateBalance(
        from_user_id,
        amount,
        'debit',
        `Withdrawal via ${provider}`,
        transaction.id
      );

      // In production, initiate withdrawal with payment provider

      return {
        success: true,
        amount,
        currency,
        provider
      };
    } catch (error) {
      logger.error('Withdrawal processing failed:', error);
      throw error;
    }
  }

  /**
   * Process bill payment
   */
  async processBillPayment(transaction) {
    try {
      const { from_user_id, amount, currency, metadata } = transaction;
      const billData = JSON.parse(metadata);

      // Validate wallet
      const wallet = await walletService.getWallet(from_user_id);
      if (!wallet || !wallet.is_active) {
        throw new Error('Wallet not found or inactive');
      }

      // Check balance
      if (wallet.available_balance < amount) {
        throw new Error('Insufficient balance');
      }

      // Debit wallet
      await walletService.updateBalance(
        from_user_id,
        amount,
        'debit',
        `Bill payment: ${billData.provider}`,
        transaction.id
      );

      // In production, call bill provider API

      return {
        success: true,
        amount,
        currency,
        provider: billData.provider,
        accountNumber: billData.accountNumber
      };
    } catch (error) {
      logger.error('Bill payment processing failed:', error);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   */
  async getTransaction(transactionId) {
    try {
      // Try cache first
      const cachedTransaction = await redisService.get(`transaction:${transactionId}`);
      if (cachedTransaction) {
        return cachedTransaction;
      }

      // Get from database
      const supabase = databaseService.getSupabase();
      const { data: transaction, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Failed to get transaction:', error);
        throw new Error('Failed to retrieve transaction');
      }

      if (transaction) {
        // Cache for future access
        await redisService.set(`transaction:${transactionId}`, transaction, 3600);
      }

      return transaction;
    } catch (error) {
      logger.error('Failed to get transaction:', error);
      throw error;
    }
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(transactionId, status, metadata = null) {
    try {
      const updateData = {
        status,
        updated_at: new Date().toISOString()
      };

      if (status === this.transactionStatuses.COMPLETED) {
        updateData.completed_at = new Date().toISOString();
      }

      if (metadata) {
        updateData.metadata = JSON.stringify(metadata);
      }

      const supabase = databaseService.getSupabase();
      const { data: transaction, error } = await supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId)
        .select()
        .single();

      if (error) {
        logger.error('Failed to update transaction status:', error);
        throw new Error('Failed to update transaction status');
      }

      // Update cache
      await redisService.set(`transaction:${transactionId}`, transaction, 3600);

      logger.audit('Transaction status updated', {
        transactionId,
        status,
        previousStatus: transaction.status
      });

      return transaction;
    } catch (error) {
      logger.error('Failed to update transaction status:', error);
      throw error;
    }
  }

  /**
   * Complete transaction
   */
  async completeTransaction(transactionId, result) {
    try {
      await this.updateTransactionStatus(
        transactionId,
        this.transactionStatuses.COMPLETED,
        result
      );

      // Clear processing timeout
      await this.clearProcessingTimeout(transactionId);

      logger.audit('Transaction completed', {
        transactionId,
        result
      });
    } catch (error) {
      logger.error('Failed to complete transaction:', error);
      throw error;
    }
  }

  /**
   * Fail transaction
   */
  async failTransaction(transactionId, reason) {
    try {
      await this.updateTransactionStatus(
        transactionId,
        this.transactionStatuses.FAILED,
        { error: reason }
      );

      // Clear processing timeout
      await this.clearProcessingTimeout(transactionId);

      logger.audit('Transaction failed', {
        transactionId,
        reason
      });
    } catch (error) {
      logger.error('Failed to fail transaction:', error);
    }
  }

  /**
   * Cancel transaction
   */
  async cancelTransaction(transactionId, reason = 'User cancelled') {
    try {
      const transaction = await this.getTransaction(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (![this.transactionStatuses.PENDING, this.transactionStatuses.PROCESSING].includes(transaction.status)) {
        throw new Error(`Cannot cancel transaction with status: ${transaction.status}`);
      }

      await this.updateTransactionStatus(
        transactionId,
        this.transactionStatuses.CANCELLED,
        { reason }
      );

      // Clear processing timeout
      await this.clearProcessingTimeout(transactionId);

      logger.audit('Transaction cancelled', {
        transactionId,
        reason
      });

      return true;
    } catch (error) {
      logger.error('Failed to cancel transaction:', error);
      throw error;
    }
  }

  /**
   * Validate transaction data
   */
  async validateTransaction(transactionData) {
    const { type, amount, currency, fromUserId, toUserId } = transactionData;

    // Validate transaction type
    if (!Object.values(this.transactionTypes).includes(type)) {
      throw new Error(`Invalid transaction type: ${type}`);
    }

    // Validate amount
    const validation = currencyService.validateAmount(amount, currency);
    if (!validation.valid) {
      throw new Error(`Invalid amount: ${validation.error}`);
    }

    // Validate currency
    if (!config.business.supportedCurrencies.includes(currency)) {
      throw new Error(`Unsupported currency: ${currency}`);
    }

    // Type-specific validations
    if (type === this.transactionTypes.TRANSFER) {
      if (!fromUserId || !toUserId) {
        throw new Error('Transfer requires both sender and recipient');
      }
      if (fromUserId === toUserId) {
        throw new Error('Cannot transfer to self');
      }
    }

    // Check transaction limits
    if (amount > config.transactionLimits.maxTransactionAmount) {
      throw new Error('Amount exceeds maximum transaction limit');
    }

    return true;
  }

  /**
   * Generate transaction reference
   */
  generateTransactionReference(type) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const typePrefix = type.substring(0, 3).toUpperCase();
    return `${typePrefix}-${timestamp}-${random}`;
  }

  /**
   * Set processing timeout
   */
  async setProcessingTimeout(transactionId) {
    try {
      await redisService.set(
        `transaction_timeout:${transactionId}`,
        { transactionId, createdAt: Date.now() },
        this.processingTimeout / 1000
      );
    } catch (error) {
      logger.error('Failed to set processing timeout:', error);
    }
  }

  /**
   * Clear processing timeout
   */
  async clearProcessingTimeout(transactionId) {
    try {
      await redisService.del(`transaction_timeout:${transactionId}`);
    } catch (error) {
      logger.error('Failed to clear processing timeout:', error);
    }
  }

  /**
   * Send transaction notifications
   */
  async sendTransactionNotifications(transaction, status) {
    try {
      // Get user phone numbers
      const supabase = databaseService.getSupabase();
      
      if (transaction.from_user_id) {
        const { data: fromUser } = await supabase
          .from('user_profiles')
          .select('phone_number, full_name')
          .eq('user_id', transaction.from_user_id)
          .single();

        if (fromUser) {
          await smsService.sendTransactionNotification(fromUser.phone_number, {
            type: transaction.type,
            amount: transaction.amount,
            currency: transaction.currency,
            reference: transaction.transaction_reference,
            status
          });
        }
      }

      if (transaction.to_user_id && transaction.to_user_id !== transaction.from_user_id) {
        const { data: toUser } = await supabase
          .from('user_profiles')
          .select('phone_number, full_name')
          .eq('user_id', transaction.to_user_id)
          .single();

        if (toUser) {
          await smsService.sendTransactionNotification(toUser.phone_number, {
            type: transaction.type,
            amount: transaction.converted_amount || transaction.amount,
            currency: transaction.target_currency || transaction.currency,
            reference: transaction.transaction_reference,
            status
          });
        }
      }
    } catch (error) {
      logger.error('Failed to send transaction notifications:', error);
      // Don't throw error as this is not critical
    }
  }
}

// Create singleton instance
const transactionService = new TransactionService();

module.exports = transactionService;
