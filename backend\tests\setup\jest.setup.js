/**
 * Jest Global Setup
 * Common setup for all test types
 */

const { config } = require('dotenv');
const path = require('path');

// Load test environment variables
config({ path: path.join(__dirname, '../../.env.test') });

// Global test configuration
global.testConfig = {
  timeout: 30000,
  retries: 2,
  verbose: process.env.TEST_VERBOSE === 'true'
};

// Mock external services by default
jest.mock('../../src/services/sms', () => ({
  sendSMS: jest.fn().mockResolvedValue({ success: true, messageId: 'mock-id' }),
  sendOTP: jest.fn().mockResolvedValue({ success: true, messageId: 'mock-id' }),
  sendTransactionNotification: jest.fn().mockResolvedValue({ success: true })
}));

jest.mock('../../src/services/paymentProviders/mtnMobileMoney', () => ({
  initiatePayment: jest.fn().mockResolvedValue({ success: true, transactionId: 'mock-txn' }),
  checkPaymentStatus: jest.fn().mockResolvedValue({ status: 'completed' }),
  processRefund: jest.fn().mockResolvedValue({ success: true })
}));

jest.mock('../../src/services/paymentProviders/airtelMoney', () => ({
  initiatePayment: jest.fn().mockResolvedValue({ success: true, transactionId: 'mock-txn' }),
  checkPaymentStatus: jest.fn().mockResolvedValue({ status: 'completed' }),
  processRefund: jest.fn().mockResolvedValue({ success: true })
}));

// Global test utilities
global.testUtils = {
  // Generate test user data
  generateTestUser: () => ({
    phoneNumber: `+25670${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`,
    fullName: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    countryCode: 'UG'
  }),

  // Generate test transaction data
  generateTestTransaction: () => ({
    amount: Math.floor(Math.random() * 100000) + 1000,
    currency: 'UGX',
    description: 'Test transaction',
    type: 'transfer'
  }),

  // Wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  // Clean up test data
  cleanupTestData: async () => {
    // Implementation will be added in integration setup
  }
};

// Global error handler for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: process.env.TEST_VERBOSE === 'true' ? originalConsole.log : jest.fn(),
  info: process.env.TEST_VERBOSE === 'true' ? originalConsole.info : jest.fn(),
  warn: originalConsole.warn,
  error: originalConsole.error
};

// Jest custom matchers
expect.extend({
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false
      };
    }
  },

  toBeValidPhoneNumber(received) {
    const phoneRegex = /^\+256[0-9]{9}$/;
    const pass = phoneRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid phone number`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid phone number`,
        pass: false
      };
    }
  },

  toBeValidCurrency(received) {
    const validCurrencies = ['UGX', 'KES', 'TZS', 'RWF', 'BIF', 'ETB'];
    const pass = validCurrencies.includes(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid currency`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid currency (${validCurrencies.join(', ')})`,
        pass: false
      };
    }
  },

  toBeValidTransactionStatus(received) {
    const validStatuses = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
    const pass = validStatuses.includes(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid transaction status`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid transaction status (${validStatuses.join(', ')})`,
        pass: false
      };
    }
  },

  toHaveValidApiResponse(received) {
    const hasSuccess = typeof received.success === 'boolean';
    const hasData = received.success ? received.data !== undefined : true;
    const hasError = !received.success ? received.error !== undefined : true;
    
    const pass = hasSuccess && hasData && hasError;
    
    if (pass) {
      return {
        message: () => `expected response not to have valid API structure`,
        pass: true
      };
    } else {
      return {
        message: () => `expected response to have valid API structure with success, data/error fields`,
        pass: false
      };
    }
  }
});

// Test database helpers
global.testDb = {
  // Will be implemented in integration setup
  createTestUser: null,
  createTestWallet: null,
  createTestTransaction: null,
  cleanupTestData: null
};

// Performance monitoring for tests
global.testPerformance = {
  start: (testName) => {
    global.testPerformance[testName] = Date.now();
  },
  
  end: (testName) => {
    const startTime = global.testPerformance[testName];
    if (startTime) {
      const duration = Date.now() - startTime;
      if (duration > 5000) { // Warn if test takes more than 5 seconds
        console.warn(`⚠️  Slow test detected: ${testName} took ${duration}ms`);
      }
      delete global.testPerformance[testName];
      return duration;
    }
    return 0;
  }
};

// Test data factories
global.testFactories = {
  user: (overrides = {}) => ({
    phoneNumber: `+25670${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`,
    fullName: 'Test User',
    email: `test${Date.now()}@example.com`,
    password: 'TestPassword123!',
    countryCode: 'UG',
    ...overrides
  }),

  wallet: (overrides = {}) => ({
    balance: 100000,
    currency: 'UGX',
    isActive: true,
    ...overrides
  }),

  transaction: (overrides = {}) => ({
    amount: 10000,
    currency: 'UGX',
    type: 'transfer',
    description: 'Test transaction',
    status: 'pending',
    ...overrides
  }),

  billPayment: (overrides = {}) => ({
    providerId: 'umeme',
    accountNumber: '**********',
    amount: 50000,
    currency: 'UGX',
    customerName: 'Test Customer',
    ...overrides
  })
};

// Cleanup after all tests
afterAll(async () => {
  // Close any open connections
  if (global.testDb && global.testDb.close) {
    await global.testDb.close();
  }
  
  // Clear all timers
  jest.clearAllTimers();
  
  // Restore console
  global.console = originalConsole;
});
