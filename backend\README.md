# JiraniPay Backend API

Production-ready backend API server for JiraniPay - East Africa's premier mobile financial services platform.

## 🚀 Features

- **Authentication & Authorization**: JWT-based auth with OTP verification
- **Wallet Management**: Digital wallet operations and balance management
- **Bill Payments**: Integration with East African utility providers
- **Transaction Processing**: Secure transaction handling with audit trails
- **Mobile Money Integration**: MTN Mobile Money and Airtel Money APIs
- **Admin Panel APIs**: Administrative operations and system management
- **Security**: Rate limiting, encryption, fraud detection
- **Monitoring**: Comprehensive logging and health checks
- **Scalability**: Redis caching and database optimization

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Admin Panel   │    │   External APIs │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │    JiraniPay Backend    │
                    │      (Express.js)       │
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐         ┌─────┴─────┐         ┌─────┴─────┐
    │ Supabase  │         │   Redis   │         │  External │
    │ Database  │         │   Cache   │         │ Services  │
    └───────────┘         └───────────┘         └───────────┘
```

## 📋 Prerequisites

- Node.js 18+ and npm 8+
- PostgreSQL 13+ (or Supabase account)
- Redis 6+
- Docker & Docker Compose (optional)

## 🛠️ Installation

### 1. Clone and Setup

```bash
cd JiraniPay/backend
npm install
```

### 2. Environment Configuration

```bash
cp .env.example .env
# Edit .env with your actual configuration values
```

### 3. Database Setup

```bash
# Run database migrations
npm run migrate

# Seed initial data (optional)
npm run seed
```

### 4. Start Development Server

```bash
npm run dev
```

The server will start on `http://localhost:3000`

## 🐳 Docker Deployment

### Development Environment

```bash
docker-compose up -d
```

### Production Environment

```bash
docker-compose --profile production up -d
```

### With Monitoring

```bash
docker-compose --profile monitoring up -d
```

## 📚 API Documentation

### Base URL
- Development: `http://localhost:3000/api/v1`
- Production: `https://api.jiranipay.com/api/v1`

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/register` | Register new user |
| POST | `/auth/login` | User login |
| POST | `/auth/verify-phone` | Verify phone with OTP |
| POST | `/auth/refresh` | Refresh access token |
| POST | `/auth/logout` | User logout |

### Wallet Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/wallets` | Get user wallet |
| POST | `/wallets/topup` | Top up wallet |
| POST | `/wallets/transfer` | Transfer money |
| GET | `/wallets/balance` | Get wallet balance |

### Transaction Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/transactions` | Get transaction history |
| GET | `/transactions/:id` | Get transaction details |
| POST | `/transactions/cancel` | Cancel pending transaction |

### Bill Payment Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/bills/providers` | Get bill providers |
| POST | `/bills/pay` | Pay a bill |
| POST | `/bills/validate` | Validate bill account |

### Admin Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/dashboard` | Admin dashboard data |
| GET | `/admin/users` | Manage users |
| GET | `/admin/transactions` | Monitor transactions |
| GET | `/admin/system/health` | System health check |

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: API abuse protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Parameterized queries
- **CORS Configuration**: Cross-origin request security
- **Helmet.js**: Security headers
- **Encryption**: Sensitive data encryption
- **Audit Logging**: Complete audit trails

## 🔧 Configuration

### Environment Variables

Key environment variables that must be configured:

```bash
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security
JWT_SECRET=your_jwt_secret_32_chars_minimum
ENCRYPTION_KEY=your_encryption_key

# SMS Service
AFRICASTALKING_USERNAME=your_username
AFRICASTALKING_API_KEY=your_api_key

# Mobile Money APIs
MTN_API_KEY=your_mtn_api_key
AIRTEL_CLIENT_ID=your_airtel_client_id
```

### Production Configuration

For production deployment, ensure:

1. Set `NODE_ENV=production`
2. Configure all required API keys
3. Set up SSL certificates
4. Configure monitoring and logging
5. Set up backup procedures

## 📊 Monitoring & Logging

### Health Check

```bash
curl http://localhost:3000/health
```

### Logs

Logs are stored in the `logs/` directory:
- `error-YYYY-MM-DD.log` - Error logs
- `combined-YYYY-MM-DD.log` - All logs
- `access-YYYY-MM-DD.log` - HTTP access logs

### Metrics

When monitoring profile is enabled:
- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3001` (admin/admin)

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 🚀 Deployment

### Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] Monitoring configured
- [ ] Backup procedures in place
- [ ] Load balancer configured
- [ ] CDN configured (if needed)

### Deployment Commands

```bash
# Build for production
npm run build

# Start production server
npm start

# Docker production build
docker build -t jiranipay-backend .
docker run -p 3000:3000 jiranipay-backend
```

## 📞 Support

For technical support and questions:
- Email: <EMAIL>
- Documentation: https://docs.jiranipay.com
- Issues: https://github.com/jiranipay/backend/issues

## 📄 License

Copyright (c) 2024 JiraniPay. All rights reserved.
