import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { AppState } from 'react-native';
import loadingStateManager from '../services/loadingStateManager';
import AdaptiveSplashScreen from '../components/AdaptiveSplashScreen';

/**
 * Loading Context Provider
 * 
 * Manages global loading states and splash screen visibility
 * across the entire JiraniPay application.
 */

const LoadingContext = createContext({
  isGlobalLoading: false,
  showSplashScreen: false,
  loadingContext: 'startup',
  setGlobalLoading: () => {},
  showSplashForContext: () => {},
  hideSplash: () => {},
});

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [isGlobalLoading, setIsGlobalLoading] = useState(false);
  const [showSplashScreen, setShowSplashScreen] = useState(false);
  const [loadingContext, setLoadingContext] = useState('startup');
  const [splashProps, setSplashProps] = useState({});
  const splashTimeoutRef = useRef(null);
  const minimumSplashTimeRef = useRef(null);

  useEffect(() => {
    // Listen to loading state changes
    const unsubscribe = loadingStateManager.addListener((stateChange) => {
      const activeStates = loadingStateManager.getActiveLoadingStates();
      const isAnyLoading = activeStates.length > 0;
      
      console.log('🔄 Loading states changed:', {
        activeStates,
        isAnyLoading,
        stateChange
      });

      // Update global loading state
      setIsGlobalLoading(isAnyLoading);

      // Determine if splash screen should be shown
      const criticalStates = [
        'app_startup',
        'auth_initialization',
        'login_processing',
        'session_restoration',
        'dashboard_loading',
        'registration_processing',
        'verification_loading'
      ];

      const shouldShowSplash = activeStates.some(state => 
        criticalStates.includes(state)
      );

      if (shouldShowSplash && !showSplashScreen) {
        // Determine context based on active states
        let context = 'transition';
        if (activeStates.includes('app_startup')) context = 'startup';
        else if (activeStates.includes('login_processing')) context = 'login';
        else if (activeStates.includes('dashboard_loading')) context = 'dashboard';
        else if (activeStates.includes('registration_processing')) context = 'registration';
        else if (activeStates.includes('verification_loading')) context = 'verification';

        showSplashForContext(context, {
          showFullAnimation: context === 'startup',
          minimumDuration: context === 'startup' ? 3000 : 1500
        });
      } else if (!shouldShowSplash && showSplashScreen) {
        // Don't hide immediately - let minimum duration complete
        if (!minimumSplashTimeRef.current) {
          hideSplash();
        }
      }
    });

    // Handle app state changes
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'active') {
        // App became active - check if we need to show splash for session restoration
        const isSessionRestoring = loadingStateManager.getLoadingState('session_restoration');
        if (isSessionRestoring) {
          showSplashForContext('login', {
            showFullAnimation: false,
            minimumDuration: 1000
          });
        }
      }
    };

    const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      unsubscribe();
      appStateSubscription?.remove();
      if (splashTimeoutRef.current) {
        clearTimeout(splashTimeoutRef.current);
      }
      if (minimumSplashTimeRef.current) {
        clearTimeout(minimumSplashTimeRef.current);
      }
    };
  }, [showSplashScreen]);

  const setGlobalLoading = (isLoading, context = 'transition') => {
    console.log(`🔄 Setting global loading: ${isLoading}, context: ${context}`);
    setIsGlobalLoading(isLoading);
    
    if (isLoading) {
      showSplashForContext(context, {
        showFullAnimation: false,
        minimumDuration: 1000
      });
    } else {
      hideSplash();
    }
  };

  const showSplashForContext = (context, props = {}) => {
    console.log(`🎨 Showing splash for context: ${context}`, props);
    
    // Clear any existing timeouts
    if (splashTimeoutRef.current) {
      clearTimeout(splashTimeoutRef.current);
    }
    if (minimumSplashTimeRef.current) {
      clearTimeout(minimumSplashTimeRef.current);
    }

    setLoadingContext(context);
    setSplashProps(props);
    setShowSplashScreen(true);

    // Set minimum display time
    const minimumDuration = props.minimumDuration || 1500;
    minimumSplashTimeRef.current = setTimeout(() => {
      minimumSplashTimeRef.current = null;
      
      // Check if we should still be showing splash
      const shouldStillShow = loadingStateManager.isAnyCriticalLoading();
      if (!shouldStillShow) {
        hideSplash();
      }
    }, minimumDuration);

    // Set maximum display time (safety timeout)
    const maxDuration = Math.max(minimumDuration * 3, 8000);
    splashTimeoutRef.current = setTimeout(() => {
      console.warn('⚠️ Splash screen timeout - forcing hide');
      hideSplash();
    }, maxDuration);
  };

  const hideSplash = () => {
    console.log('🎨 Hiding splash screen');
    
    // Clear timeouts
    if (splashTimeoutRef.current) {
      clearTimeout(splashTimeoutRef.current);
      splashTimeoutRef.current = null;
    }
    if (minimumSplashTimeRef.current) {
      clearTimeout(minimumSplashTimeRef.current);
      minimumSplashTimeRef.current = null;
    }

    setShowSplashScreen(false);
    setSplashProps({});
  };

  const handleSplashComplete = () => {
    console.log('🎨 Splash animation completed');
    
    // Check if we should still be showing splash
    const shouldStillShow = loadingStateManager.isAnyCriticalLoading();
    if (!shouldStillShow) {
      hideSplash();
    }
  };

  const contextValue = {
    isGlobalLoading,
    showSplashScreen,
    loadingContext,
    setGlobalLoading,
    showSplashForContext,
    hideSplash,
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
      
      {/* Global Splash Screen Overlay */}
      {showSplashScreen && (
        <AdaptiveSplashScreen
          context={loadingContext}
          onAnimationComplete={handleSplashComplete}
          {...splashProps}
        />
      )}
    </LoadingContext.Provider>
  );
};

export default LoadingContext;
