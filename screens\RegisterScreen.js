import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TextInput,
  BackHandler,
} from 'react-native';
// Removed PhoneInput to avoid native module issues in Expo Go
// Removed OTP input package to avoid native module issues
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';
import CountrySelector from '../components/CountrySelector';
import OTPInput from '../components/OTPInput';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';

const RegisterScreen = ({ navigation }) => {
  // Use theme context
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState(['', '', '', '', '', '']); // Array for individual digits
  const [fullName, setFullName] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [step, setStep] = useState('details'); // 'details', 'phone', or 'otp'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [selectedCountryCode, setSelectedCountryCode] = useState('UG'); // Default to Uganda

  // Ref for OTP input component
  const otpInputRef = useRef(null);
  const isAutoVerifying = useRef(false);

  useEffect(() => {
    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Handle Android hardware back button
  useEffect(() => {
    const backAction = () => {
      if (step === 'otp') {
        setStep('phone');
        setLoading(false);
        clearOtpInputs();
        setResendTimer(0);
        return true; // Prevent default back action
      } else if (step === 'phone') {
        setStep('details');
        return true; // Prevent default back action
      }
      return false; // Allow default back action for details step
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [step]);

  /**
   * Handles phone number input changes with country-specific formatting and network detection
   * @param {string} text - The input text
   */
  const handlePhoneNumberChange = (text) => {
    const countryConfig = authService.getCountryConfig(selectedCountryCode);
    if (!countryConfig) return;

    // Remove any non-digit characters and limit to country-specific length
    const cleaned = text.replace(/\D/g, '').substring(0, countryConfig.phoneLength);
    setPhoneNumber(cleaned);

    // Format with selected country code
    const formatted = authService.formatPhoneNumber(cleaned, selectedCountryCode);
    setFormattedValue(formatted);

    // Detect network provider for the selected country
    if (cleaned.length >= countryConfig.phoneLength - 1) {
      const provider = authService.detectNetworkProvider(cleaned, selectedCountryCode);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  /**
   * Handles country selection change
   * @param {string} countryCode - Selected country code
   * @param {Object} country - Country configuration object
   */
  const handleCountrySelect = (countryCode, country) => {
    setSelectedCountryCode(countryCode);
    // Clear phone number and provider when country changes
    setPhoneNumber('');
    setFormattedValue('');
    setNetworkProvider('');
  };

  /**
   * Handle OTP input change from the OTP component
   * @param {Array} otpArray - Array of OTP digits
   */
  const handleOtpChange = (otpArray) => {
    setOtpCode(otpArray);
  };

  /**
   * Handle OTP completion (when all 6 digits are entered)
   * @param {string} completeOtp - Complete OTP string
   */
  const handleOtpComplete = async (completeOtp) => {
    if (isAutoVerifying.current || loading) return;

    isAutoVerifying.current = true;
    console.log('🔄 Auto-verifying OTP:', completeOtp);

    try {
      await verifyOTPAndProceed(completeOtp);
    } finally {
      isAutoVerifying.current = false;
    }
  };

  /**
   * Clear OTP inputs (used on error)
   */
  const clearOtpInputs = () => {
    setOtpCode(['', '', '', '', '', '']);
    if (otpInputRef.current?.clearInputs) {
      otpInputRef.current.clearInputs();
    }
  };

  const validateForm = () => {
    // Full name validation
    if (!fullName.trim() || fullName.trim().length < 2) {
      Alert.alert('Error', 'Please enter your full name (minimum 2 characters)');
      return false;
    }

    // Phone number validation for selected country
    const phoneValidation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!phoneValidation.isValid) {
      Alert.alert('Error', phoneValidation.error);
      return false;
    }

    // Password validation (match Supabase requirements exactly)
    if (!password || password.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return false;
    }

    // Password strength validation (Supabase requirements)
    const hasDigit = /\d/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};'\\:"|<>?,./`~]/.test(password);

    if (!hasDigit) {
      Alert.alert('Error', 'Password must contain at least one digit (0-9)');
      return false;
    }
    if (!hasLowercase) {
      Alert.alert('Error', 'Password must contain at least one lowercase letter (a-z)');
      return false;
    }
    if (!hasUppercase) {
      Alert.alert('Error', 'Password must contain at least one uppercase letter (A-Z)');
      return false;
    }
    if (!hasSymbol) {
      Alert.alert('Error', 'Password must contain at least one symbol (!@#$%^&*()_+-=[]{};\'\\:"|<>?,./`~)');
      return false;
    }

    // Confirm password validation
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    return true;
  };

  const proceedToPhoneVerification = () => {
    if (validateForm()) {
      setStep('phone');
    }
  };

  const sendOTP = async () => {
    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!validation.isValid) {
      Alert.alert('Error', validation.error);
      return;
    }

    setLoading(true);
    try {
      // Use registration-specific OTP method with password
      const result = await authService.sendRegistrationOTP(phoneNumber, password, selectedCountryCode);

      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown

        // Show appropriate message based on environment
        if (result.data?.message?.includes('Development mode')) {
          Alert.alert('🔧 Development Mode', result.data.message);
        } else {
          Alert.alert('Registration OTP Sent', 'Please check your phone for the verification code to complete your account setup');
        }
      } else {
        Alert.alert('Registration Error', result.error || 'Failed to send registration OTP');
      }
    } catch (error) {
      console.error('❌ Registration OTP error:', error);
      Alert.alert('Registration Error', 'Something went wrong during registration. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const verifyOTPAndProceed = async (providedOtp = null) => {
    const otpToVerify = providedOtp || otpCode.join('');

    if (!otpToVerify || otpToVerify.length !== 6) {
      Alert.alert('Error', 'Please enter the complete OTP');
      return;
    }

    setLoading(true);
    try {
      console.log('🔐 Verifying OTP:', otpToVerify, 'for phone:', phoneNumber);

      // Use actual OTP verification
      const result = await authService.verifyOTP(phoneNumber, otpToVerify, selectedCountryCode);

      if (result.success) {
        console.log('✅ OTP verified successfully, user:', result.data?.user);

        // PRODUCTION FIX: Access user from result.data.user, not result.user
        if (result.data?.user) {
          // Create user profile directly after successful OTP verification
          const profileData = {
            fullName: fullName.trim(),
            phoneNumber: phoneNumber,
            countryCode: selectedCountryCode
          };

          // Create profile in database
          const profileResult = await authService.createUserProfile(result.data.user.id, profileData);

          if (profileResult.success) {
            console.log('✅ User profile created successfully');
          } else {
            console.warn('⚠️ Profile creation failed, but user account exists');
          }
        } else {
          console.warn('⚠️ User data not available in verification result');
        }

        // Don't navigate manually - let the auth state change handle it
        console.log('✅ Registration successful, auth state will handle navigation');

      } else {
        console.error('❌ Registration OTP verification failed:', result.error);

        // Provide more specific error messages for registration
        let errorMessage = result.error || 'Invalid OTP. Please try again.';
        if (errorMessage.includes('expired')) {
          errorMessage = 'Registration OTP has expired. Please request a new code.';
        } else if (errorMessage.includes('invalid')) {
          errorMessage = 'Invalid registration OTP. Please check and try again.';
        }

        Alert.alert(
          'Registration Verification Failed',
          errorMessage,
          [
            {
              text: 'Try Again',
              onPress: () => clearOtpInputs(),
              style: 'default'
            },
            {
              text: 'Resend OTP',
              onPress: () => {
                clearOtpInputs();
                resendOTP();
              },
              style: 'default'
            },
            {
              text: 'Go Back',
              onPress: () => setStep('phone'),
              style: 'cancel'
            }
          ]
        );
      }
    } catch (error) {
      console.error('❌ Registration OTP verification error:', error);
      Alert.alert(
        'Registration Error',
        'Network error during registration. Please check your connection and try again.',
        [
          {
            text: 'Retry',
            onPress: () => clearOtpInputs(),
            style: 'default'
          },
          {
            text: 'Go Back',
            onPress: () => setStep('phone'),
            style: 'cancel'
          }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (resendTimer > 0) return;

    setLoading(true);
    try {
      // Use registration-specific OTP method for resend as well with password
      const result = await authService.sendRegistrationOTP(phoneNumber, password, selectedCountryCode);

      if (result.success) {
        setResendTimer(60);

        // Show appropriate message based on environment
        if (result.data?.message?.includes('Development mode')) {
          Alert.alert('🔧 Development Mode', result.data.message);
        } else {
          Alert.alert('Registration OTP Resent', 'A new verification code has been sent to your phone');
        }
      } else {
        Alert.alert('Error', result.error || 'Failed to resend registration OTP');
      }
    } catch (error) {
      console.error('❌ Resend registration OTP error:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderDetailsStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.title}>Create Account</Text>
      <Text style={styles.subtitle}>Enter your details to get started</Text>

      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          value={fullName}
          onChangeText={setFullName}
          placeholder="Full Name"
          autoCapitalize="words"
          autoCorrect={false}
        />
      </View>

      {/* Country Selector */}
      <CountrySelector
        selectedCountryCode={selectedCountryCode}
        onCountrySelect={handleCountrySelect}
      />

      <View style={styles.inputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>
            {authService.getCountryConfig(selectedCountryCode)?.code || '+256'}
          </Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder={`Enter phone number (e.g., ${authService.getCountryConfig(selectedCountryCode)?.example || '777123456'})`}
            keyboardType="phone-pad"
            maxLength={authService.getCountryConfig(selectedCountryCode)?.phoneLength || 10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              Network: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.passwordInputContainer}>
          <TextInput
            style={styles.passwordInput}
            value={password}
            onChangeText={setPassword}
            placeholder="Create Password"
            secureTextEntry={!showPassword}
            autoCapitalize="none"
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setShowPassword(!showPassword)}
          >
            <Ionicons
              name={showPassword ? "eye-off-outline" : "eye-outline"}
              size={20}
              color={Colors.neutral.warmGray}
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.passwordInputContainer}>
          <TextInput
            style={styles.passwordInput}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm Password"
            secureTextEntry={!showConfirmPassword}
            autoCapitalize="none"
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            <Ionicons
              name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
              size={20}
              color={Colors.neutral.warmGray}
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.passwordRequirements}>
        <Text style={styles.requirementsTitle}>Password Requirements (Supabase):</Text>
        <Text style={[styles.requirement, password.length >= 8 && styles.requirementMet]}>
          • At least 8 characters
        </Text>
        <Text style={[styles.requirement, /\d/.test(password) && styles.requirementMet]}>
          • Contains at least one digit (0-9)
        </Text>
        <Text style={[styles.requirement, /[a-z]/.test(password) && styles.requirementMet]}>
          • Contains at least one lowercase letter (a-z)
        </Text>
        <Text style={[styles.requirement, /[A-Z]/.test(password) && styles.requirementMet]}>
          • Contains at least one uppercase letter (A-Z)
        </Text>
        <Text style={[styles.requirement, /[!@#$%^&*()_+\-=\[\]{};'\\:"|<>?,./`~]/.test(password) && styles.requirementMet]}>
          • Contains at least one symbol (!@#$%^&*()_+-=[]{};'\:"|&lt;&gt;?,./`~)
        </Text>
        <Text style={[styles.requirement, password === confirmPassword && password.length > 0 && styles.requirementMet]}>
          • Passwords match
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={proceedToPhoneVerification}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Continue</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.loginLink}
        onPress={() => navigation.navigate('Login')}
      >
        <Text style={styles.loginText}>
          Already have an account? <Text style={styles.loginTextBold}>Sign In</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('details')}
      >
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>

      <Text style={styles.title}>Verify Phone Number</Text>
      <Text style={styles.subtitle}>We'll send an OTP to {formattedValue || '+256' + phoneNumber}</Text>

      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>+256</Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
            maxLength={10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              Network: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={sendOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Send OTP</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.loginLink}
        onPress={() => navigation.navigate('Login')}
      >
        <Text style={styles.loginText}>
          Already have an account? <Text style={styles.loginTextBold}>Sign In</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>

      <Text style={styles.title}>Verify Phone Number</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || phoneNumber}
      </Text>

      <View style={styles.otpContainer}>
        <OTPInput
          ref={otpInputRef}
          length={6}
          value={otpCode}
          onChangeText={handleOtpChange}
          onComplete={handleOtpComplete}
          disabled={loading}
          autoFocus={true}
        />
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTPAndProceed}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Verify & Continue</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'details' ? renderDetailsStep() :
         step === 'phone' ? renderPhoneStep() : renderOTPStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  inputContainer: {
    marginBottom: 20,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    height: 60,
    fontSize: 16,
    color: theme.colors.text,
  },
  phoneInputContainer: {
    marginBottom: 20,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  countryCode: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '600',
    marginRight: 10,
  },
  phoneTextInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    height: '100%',
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 12,
    backgroundColor: theme.colors.surface,
    paddingHorizontal: 16,
    height: 60,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    height: '100%',
  },
  eyeButton: {
    padding: 4,
  },
  passwordRequirements: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 8,
  },
  requirement: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  requirementMet: {
    color: Colors.secondary.savanna,
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  providerName: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  button: {
    backgroundColor: Colors.primary.main,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.primary.dark,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginLink: {
    alignItems: 'center',
  },
  loginText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  loginTextBold: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  otpContainer: {
    width: '100%',
    marginBottom: 30,
    paddingHorizontal: 10,
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: theme.colors.textSecondary,
  },
});

export default RegisterScreen;
