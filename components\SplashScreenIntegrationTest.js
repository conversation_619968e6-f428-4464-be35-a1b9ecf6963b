import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import EnhancedSplashScreen from './EnhancedSplashScreen';

/**
 * Integration test component for the EnhancedSplashScreen
 * This component helps verify that the splash screen works correctly
 * with the app's theme system and handles all edge cases properly.
 */
const SplashScreenIntegrationTest = () => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const [showSplash, setShowSplash] = useState(false);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTest, setIsRunningTest] = useState(false);

  const addTestResult = (testName, passed, details = '') => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      testName,
      passed,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runSplashTest = (testType) => {
    setIsRunningTest(true);
    setTestResults([]);
    
    const startTime = Date.now();
    
    const handleSplashComplete = () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      setShowSplash(false);
      setIsRunningTest(false);
      
      // Test duration (should be around 5.3 seconds)
      const expectedDuration = 5300; // 5.3 seconds
      const tolerance = 1000; // 1 second tolerance
      const durationPassed = Math.abs(duration - expectedDuration) <= tolerance;
      
      addTestResult(
        'Animation Duration',
        durationPassed,
        `Expected: ~${expectedDuration}ms, Actual: ${duration}ms`
      );
      
      addTestResult(
        'Completion Callback',
        true,
        'onAnimationComplete callback executed successfully'
      );
      
      addTestResult(
        `Theme Integration (${isDarkMode ? 'Dark' : 'Light'} Mode)`,
        true,
        'Splash screen adapted to current theme'
      );
      
      Alert.alert(
        'Test Complete',
        `Splash screen test completed in ${(duration / 1000).toFixed(1)} seconds`,
        [{ text: 'OK' }]
      );
    };
    
    // Start the splash screen
    setShowSplash(true);
    
    // Set up timeout test
    setTimeout(() => {
      if (showSplash) {
        addTestResult(
          'Timeout Handling',
          false,
          'Splash screen did not complete within expected time'
        );
        setShowSplash(false);
        setIsRunningTest(false);
      }
    }, 8000);
    
    // Return the completion handler
    return handleSplashComplete;
  };

  const testScenarios = [
    {
      name: 'Light Theme Test',
      description: 'Test splash screen in light theme',
      action: () => {
        if (isDarkMode) toggleTheme();
        setTimeout(() => runSplashTest('light'), 100);
      }
    },
    {
      name: 'Dark Theme Test',
      description: 'Test splash screen in dark theme',
      action: () => {
        if (!isDarkMode) toggleTheme();
        setTimeout(() => runSplashTest('dark'), 100);
      }
    },
    {
      name: 'Current Theme Test',
      description: `Test splash screen in current ${isDarkMode ? 'dark' : 'light'} theme`,
      action: () => runSplashTest('current')
    }
  ];

  if (showSplash) {
    return (
      <EnhancedSplashScreen 
        onAnimationComplete={runSplashTest()}
      />
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Ionicons 
            name="flask" 
            size={32} 
            color={theme.colors.primary} 
            style={styles.headerIcon}
          />
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Splash Screen Integration Test
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
            Verify splash screen functionality and theme integration
          </Text>
        </View>

        {/* Current Theme Info */}
        <View style={[styles.infoCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Current Theme: {isDarkMode ? 'Dark' : 'Light'} Mode
          </Text>
          <TouchableOpacity
            style={[styles.themeButton, { borderColor: theme.colors.primary }]}
            onPress={toggleTheme}
          >
            <Ionicons 
              name={isDarkMode ? 'sunny' : 'moon'} 
              size={20} 
              color={theme.colors.primary} 
            />
            <Text style={[styles.themeButtonText, { color: theme.colors.primary }]}>
              Switch to {isDarkMode ? 'Light' : 'Dark'} Theme
            </Text>
          </TouchableOpacity>
        </View>

        {/* Test Scenarios */}
        <View style={styles.testsSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Test Scenarios
          </Text>
          
          {testScenarios.map((scenario, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.testButton,
                { 
                  backgroundColor: theme.colors.primary,
                  opacity: isRunningTest ? 0.5 : 1
                }
              ]}
              onPress={scenario.action}
              disabled={isRunningTest}
            >
              <Text style={styles.testButtonText}>{scenario.name}</Text>
              <Text style={styles.testButtonDescription}>{scenario.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Test Results */}
        {testResults.length > 0 && (
          <View style={styles.resultsSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Test Results
            </Text>
            
            {testResults.map((result) => (
              <View
                key={result.id}
                style={[
                  styles.resultItem,
                  { 
                    backgroundColor: theme.colors.card,
                    borderLeftColor: result.passed ? '#22c55e' : '#ef4444'
                  }
                ]}
              >
                <View style={styles.resultHeader}>
                  <Ionicons
                    name={result.passed ? 'checkmark-circle' : 'close-circle'}
                    size={20}
                    color={result.passed ? '#22c55e' : '#ef4444'}
                  />
                  <Text style={[styles.resultTitle, { color: theme.colors.text }]}>
                    {result.testName}
                  </Text>
                  <Text style={[styles.resultTime, { color: theme.colors.textSecondary }]}>
                    {result.timestamp}
                  </Text>
                </View>
                {result.details && (
                  <Text style={[styles.resultDetails, { color: theme.colors.textSecondary }]}>
                    {result.details}
                  </Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Instructions */}
        <View style={[styles.instructionsCard, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.instructionsTitle, { color: theme.colors.text }]}>
            Testing Instructions
          </Text>
          <Text style={[styles.instructionsText, { color: theme.colors.textSecondary }]}>
            1. Test both light and dark themes{'\n'}
            2. Verify animation duration (~5.3 seconds){'\n'}
            3. Check cultural elements and trust indicators{'\n'}
            4. Ensure smooth transition to main app{'\n'}
            5. Test on different device sizes{'\n'}
            6. Verify network condition handling
          </Text>
        </View>

        {/* Status */}
        {isRunningTest && (
          <View style={[styles.statusCard, { backgroundColor: theme.colors.primary }]}>
            <Text style={styles.statusText}>
              Running splash screen test... Please wait for completion.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerIcon: {
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 22,
  },
  infoCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  themeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
  },
  themeButtonText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  testsSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  testButton: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  testButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  testButtonDescription: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
  },
  resultsSection: {
    marginBottom: 24,
  },
  resultItem: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 4,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resultTitle: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  resultTime: {
    fontSize: 12,
  },
  resultDetails: {
    fontSize: 12,
    marginTop: 4,
    marginLeft: 28,
  },
  instructionsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  instructionsText: {
    fontSize: 14,
    lineHeight: 20,
  },
  statusCard: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default SplashScreenIntegrationTest;
