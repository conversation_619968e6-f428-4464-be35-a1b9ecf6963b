# JiraniPay Splash Screen Timeout Debug Guide

## 🚨 **ISSUE**: App Stuck on Splash Screen

The app is not progressing past the splash screen after implementing the database-only solution.

---

## 🔍 **DEBUGGING STEPS**

### **Step 1: Check Console Logs**

Look for these specific log messages in your console:

#### **Expected Startup Sequence:**
```
🚀 App useEffect triggered - starting initialization
🚀 Initializing JiraniPay app...
🔐 Auth state changed, user: not authenticated
🔧 Initializing basic services...
✅ Basic services initialized
🎨 Splash screen animation completed
```

#### **Problem Indicators:**
- ❌ **Missing "Auth state changed"** - Auth service not working
- ❌ **Missing "Basic services initialized"** - Service initialization hanging
- ❌ **"Session validation timeout"** - Database connection issues
- ❌ **"App stuck in loading state"** - Emergency timeout triggered

### **Step 2: Identify Where It's Stuck**

#### **If you see "Session validation timeout":**
- Database connection is slow/failing
- Supabase credentials might be incorrect
- Network connectivity issues

#### **If you see "Auth service initialization timeout":**
- AuthService.initialize() is hanging
- Supabase client initialization issues

#### **If you see "Emergency timeout triggered":**
- App initialization completely failed
- Multiple services are hanging

---

## 🛠️ **IMMEDIATE FIXES**

### **Fix 1: Bypass Session Validation (Temporary)**

Add this to the top of `initializeApp()` in `App.js`:

```javascript
// TEMPORARY: Skip session validation for debugging
const SKIP_SESSION_VALIDATION = true;

if (SKIP_SESSION_VALIDATION) {
  console.log('🔧 DEBUG: Skipping session validation');
  setIsAuthenticated(false);
  setIsLoading(false);
  initializeBasicServices();
  return;
}
```

### **Fix 2: Simplify AuthService Initialization**

In `services/authService.js`, temporarily simplify the `initialize()` method:

```javascript
async initialize() {
  console.log('🔧 Initializing auth service (simplified)...');
  try {
    this.supabaseClient = supabase;
    console.log('✅ Auth service initialized (simplified)');
    return { success: true };
  } catch (error) {
    console.error('❌ Auth service init failed:', error);
    return { success: false, error: error.message };
  }
}
```

### **Fix 3: Force Splash Screen Completion**

Reduce splash screen timeout in `App.js`:

```javascript
// Change from 6000 to 3000
}, 3000); // 3 seconds for faster recovery
```

---

## 🧪 **TESTING APPROACH**

### **Test 1: Minimal App**
1. Apply Fix 1 (bypass session validation)
2. Start the app
3. Check if it progresses past splash screen

### **Test 2: Database Connection**
1. Check Supabase credentials in `.env.development`
2. Verify database tables exist
3. Test connection manually in Supabase dashboard

### **Test 3: Service by Service**
1. Comment out all services in `initializeServicesInBackground()`
2. Add them back one by one
3. Identify which service is causing the hang

---

## 🎯 **EXPECTED RESULTS**

After applying fixes:

### **✅ Success Indicators:**
- App progresses past splash screen in under 6 seconds
- Login screen appears
- Console shows complete initialization sequence
- No timeout warnings in logs

### **❌ Still Failing:**
- Apply more aggressive fixes below

---

## 🚨 **AGGRESSIVE FIXES** (If Above Doesn't Work)

### **Fix A: Disable All Database Operations**

Temporarily modify `WalletService` methods to return mock data:

```javascript
async getWalletBalance(userId) {
  console.log('🔧 DEBUG: Returning mock wallet balance');
  return {
    success: true,
    balance: 50000,
    currency: 'UGX'
  };
}
```

### **Fix B: Skip All Service Initialization**

In `App.js`, comment out service initialization:

```javascript
const initializeBasicServices = async () => {
  console.log('🔧 DEBUG: Skipping service initialization');
  // authService.initialize(); // COMMENTED OUT
};
```

### **Fix C: Force Immediate Completion**

Add this at the start of `initializeApp()`:

```javascript
// EMERGENCY: Force immediate completion
console.log('🚨 EMERGENCY: Forcing immediate app completion');
setIsAuthenticated(false);
setIsLoading(false);
return;
```

---

## 📊 **DIAGNOSTIC CHECKLIST**

- [ ] Console shows "App useEffect triggered"
- [ ] Console shows "Initializing JiraniPay app"
- [ ] Console shows "Auth state changed"
- [ ] Console shows "Basic services initialized"
- [ ] Splash screen completes within 6 seconds
- [ ] Login screen appears
- [ ] No timeout warnings in console
- [ ] No database connection errors

---

## 🎯 **NEXT STEPS**

1. **Apply Fix 1** (bypass session validation)
2. **Test the app** - does it progress past splash?
3. **If yes**: Gradually re-enable features
4. **If no**: Apply Fix 2 and Fix 3
5. **Still failing**: Use aggressive fixes

**The goal is to get the app working first, then gradually add back the database functionality.**
