import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import * as Device from 'expo-device';
import * as Application from 'expo-application';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import deviceManagementService from '../services/deviceManagementService';
import { isProductionMode } from '../config/environment';

const TrustedDevicesScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [user, setUser] = useState(null);
  const [currentDevice, setCurrentDevice] = useState(null);
  const [trustedDevices, setTrustedDevices] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadDeviceData();
  }, []);

  const loadDeviceData = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        setError(t('errors.userNotAuthenticated'));
        return;
      }

      setUser(currentUser);
      console.log('📱 Loading device data for user:', currentUser.id);

      // Get current device info
      const deviceInfo = await getCurrentDeviceInfo();
      setCurrentDevice(deviceInfo);

      if (isProductionMode()) {
        // PRODUCTION MODE: Load real trusted devices from database
        console.log('🔒 Production mode: Loading real trusted devices');

        const result = await deviceManagementService.getTrustedDevices(currentUser.id);

        if (result.success) {
          console.log(`✅ Loaded ${result.devices.length} trusted devices`);
          setTrustedDevices(result.devices);
        } else {
          console.error('❌ Failed to load trusted devices:', result.error);
          setError(result.error);
          setTrustedDevices([]);
        }
      } else {
        // Fallback: Show empty state for any configuration issues
        console.log('⚠️ Unable to load trusted devices');
        setTrustedDevices([]);
        setError(t('errors.unableToLoadTrustedDevices'));
      }
    } catch (error) {
      console.error('❌ Error loading device data:', error);
      setError(error.message || t('errors.failedToLoadTrustedDevices'));
      Alert.alert(t('error'), t('failedToLoadTrustedDevices') + error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const getCurrentDeviceInfo = async () => {
    try {
      const deviceName = Device.deviceName || t('common.unknownDevice');
      const deviceType = Device.deviceType === Device.DeviceType.PHONE ? 'mobile' : 
                        Device.deviceType === Device.DeviceType.TABLET ? 'tablet' : 'desktop';
      const osName = Platform.OS === 'ios' ? 'iOS' : 'Android';
      const osVersion = Device.osVersion || t('common.unknown');
      
      return {
        name: deviceName,
        type: deviceType,
        os: `${osName} ${osVersion}`,
        id: await Application.getAndroidId() || 'unknown-id',
      };
    } catch (error) {
      console.error('❌ Error getting device info:', error);
      return {
        name: t('common.currentDevice'),
        type: 'mobile',
        os: Platform.OS === 'ios' ? 'iOS' : 'Android',
        id: 'current-device',
      };
    }
  };

  const removeDevice = (deviceId, deviceName) => {
    Alert.alert(
      t('profile.removeTrustedDevice'),
      t('profile.confirmRemoveDevice', { deviceName }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('common.delete'),
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('🗑️ Removing device:', deviceId);
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

              // Proceed with device removal

              const result = await deviceManagementService.removeDevice(user.id, deviceId);

              if (result.success) {
                // Remove from local state
                setTrustedDevices(prev => prev.filter(device => device.device_id !== deviceId));

                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
                Alert.alert(t('success'), t('profile.deviceRemovedSuccess', { deviceName }));

                console.log('✅ Device removed successfully');
              } else {
                console.error('❌ Failed to remove device:', result.error);
                Alert.alert(t('error'), t('failedToRemoveDevice') + result.error);
              }
            } catch (error) {
              console.error('❌ Error removing device:', error);
              Alert.alert(t('error'), t('failedToRemoveDevice') + error.message);
            }
          },
        },
      ]
    );
  };

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'mobile':
        return 'phone-portrait';
      case 'tablet':
        return 'tablet-portrait';
      case 'desktop':
        return 'desktop';
      default:
        return 'hardware-chip';
    }
  };

  const formatLastActive = (dateString) => {
    try {
      if (!dateString) return t('common.unknown');

      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

      if (diffInHours < 1) return t('common.activeNow');
      if (diffInHours < 24) return t('common.hoursAgo', { hours: diffInHours });

      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) return t('common.yesterday');
      if (diffInDays < 7) return t('common.daysAgo', { days: diffInDays });

      return date.toLocaleDateString();
    } catch (error) {
      return t('common.unknown');
    }
  };

  const onRefresh = () => {
    loadDeviceData(true);
  };

  const trustCurrentDevice = async () => {
    try {
      // Proceed with device trust

      if (!currentDevice || !user) {
        Alert.alert(t('error'), t('deviceOrUserInformationNotAvailable'));
        return;
      }

      console.log('🔒 Trusting current device');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const deviceId = await deviceManagementService.getUniqueDeviceId();
      const result = await deviceManagementService.trustDevice(user.id, deviceId);

      if (result.success) {
        // Refresh the device list to show updated trust status
        await loadDeviceData();

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert(
          t('success'),
          t('profile.deviceTrustedSuccess')
        );

        console.log('✅ Device trusted successfully');
      } else {
        console.error('❌ Failed to trust device:', result.error);
        Alert.alert(t('error'), t('failedToTrustDevice') + result.error);
      }
    } catch (error) {
      console.error('❌ Error trusting device:', error);
      Alert.alert(t('error'), t('failedToTrustDevice') + error.message);
    }
  };

  const renderDevice = (device) => (
    <View key={device.id} style={[styles.deviceCard, device.isCurrent && styles.currentDeviceCard]}>
      <View style={styles.deviceHeader}>
        <View style={styles.deviceLeft}>
          <View style={[styles.deviceIcon, { backgroundColor: device.isCurrent ? Colors.primary.main + '20' : Colors.neutral.lightGray }]}>
            <Ionicons
              name={getDeviceIcon(device.device_type)}
              size={24}
              color={device.isCurrent ? Colors.primary.main : Colors.neutral.warmGray}
            />
          </View>
          <View style={styles.deviceInfo}>
            <View style={styles.deviceNameRow}>
              <Text style={styles.deviceName}>{device.display_name || device.device_name}</Text>
              {device.isCurrent && (
                <View style={styles.currentBadge}>
                  <Text style={styles.currentBadgeText}>{t('current')}</Text>
                </View>
              )}
              {device.is_trusted && (
                <View style={styles.trustedBadge}>
                  <Ionicons name="shield-checkmark" size={12} color={Colors.status.success} />
                  <Text style={styles.trustedBadgeText}>{t('trusted')}</Text>
                </View>
              )}
            </View>
            <Text style={styles.deviceOS}>{device.os_display || `${device.platform} ${device.os_version || ''}`.trim()}</Text>
            <Text style={styles.deviceLocation}>
              <Ionicons name="location" size={12} color={Colors.neutral.warmGray} />
              {' '}{device.location_name || t('common.unknownLocation')}
            </Text>
          </View>
        </View>
        <View style={styles.deviceActions}>
          {!device.is_trusted && device.isCurrent && (
            <TouchableOpacity
              style={styles.trustButton}
              onPress={trustCurrentDevice}
              activeOpacity={0.7}
            >
              <Ionicons name="shield-checkmark" size={16} color={Colors.status.success} />
              <Text style={styles.trustButtonText}>{t('trust')}</Text>
            </TouchableOpacity>
          )}
          {!device.isCurrent && (
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => removeDevice(device.device_id, device.display_name || device.device_name)}
              activeOpacity={0.7}
            >
              <Ionicons name="trash" size={20} color={Colors.status.error} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.deviceFooter}>
        <Text style={styles.lastActive}>
          Last active: {formatLastActive(device.last_active)}
        </Text>
        {device.isCurrent && (
          <View style={styles.activeIndicator}>
            <View style={styles.activeDot} />
            <Text style={styles.activeText}>{t('activeNow')}</Text>
          </View>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('loadingTrustedDevices')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('trustedDevices')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('manageDevicesThatCanAccessYourAccount')}
        </Text>
      </LinearGradient>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary.main]}
            tintColor={Colors.primary.main}
          />
        }
      >
        {/* Device Count */}
        <View style={styles.section}>
          <View style={styles.countCard}>
            <View style={styles.countIcon}>
              <Ionicons name="shield-checkmark" size={32} color={Colors.status.success} />
            </View>
            <View style={styles.countText}>
              <Text style={styles.countNumber}>{trustedDevices.length}</Text>
              <Text style={styles.countLabel}>{t('trustedDevices')}</Text>
              <Text style={styles.countDescription}>
                {t('devicesThatCanAccessYourAccountWithoutAdditionalVe')}
              </Text>
            </View>
          </View>
        </View>

        {/* Devices List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('yourDevices')}</Text>
          <Text style={styles.sectionDescription}>
            {isProductionMode()
              ? 'These devices are trusted and can access your account'
              : 'Trusted devices feature is only available in production mode'
            }
          </Text>

          {error && (
            <View style={styles.errorCard}>
              <Ionicons name="warning" size={24} color={Colors.status.warning} />
              <View style={styles.errorText}>
                <Text style={styles.errorTitle}>{t('unableToLoadDevices')}</Text>
                <Text style={styles.errorDescription}>{error}</Text>
                {!isProductionMode() && (
                  <Text style={styles.errorHint}>
                    {t('switchToProductionModeToUseTrustedDevices')}
                  </Text>
                )}
                {error.includes('Database not properly configured') && (
                  <Text style={styles.errorHint}>
                    {t('databaseSetupRequiredPleaseContactSupportOrRunTheD')}
                  </Text>
                )}
              </View>
            </View>
          )}

          {!error && trustedDevices.length === 0 && isProductionMode() && (
            <View style={styles.emptyCard}>
              <Ionicons name="phone-portrait" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.emptyTitle}>{t('noDevicesFound')}</Text>
              <Text style={styles.emptyDescription}>
                {t('yourDevicesWillAppearHereAfterYouLogInFromThem')}
              </Text>
            </View>
          )}

          {!error && trustedDevices.length > 0 && trustedDevices.map(renderDevice)}
        </View>

        {/* Security Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('securityInformation')}</Text>
          
          <View style={styles.infoCard}>
            <Ionicons name="information-circle" size={24} color={Colors.primary.main} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('deviceTrust')}</Text>
              <Text style={styles.infoDescription}>
                {t('trustedDevicesCanAccessYourAccountWithoutAdditiona')}
              </Text>
            </View>
          </View>

          <View style={styles.infoCard}>
            <Ionicons name="warning" size={24} color={Colors.accent.gold} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('removeSuspiciousDevices')}</Text>
              <Text style={styles.infoDescription}>
                {t('ifYouSeeADeviceYouDontRecognizeRemoveItImmediately')}
              </Text>
            </View>
          </View>

          <View style={styles.infoCard}>
            <Ionicons name="refresh" size={24} color={Colors.status.success} />
            <View style={styles.infoText}>
              <Text style={styles.infoTitle}>{t('regularReview')}</Text>
              <Text style={styles.infoDescription}>
                {t('reviewYourTrustedDevicesRegularlyAndRemoveOldOrUnu')}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  countCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 20,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  countIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.status.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  countText: {
    flex: 1,
  },
  countNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: Colors.status.success,
    marginBottom: 4,
  },
  countLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  countDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  deviceCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  currentDeviceCard: {
    borderColor: Colors.primary.main,
    backgroundColor: Colors.primary.main + '05',
  },
  deviceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  deviceLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  deviceIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  deviceInfo: {
    flex: 1,
  },
  deviceNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginRight: 8,
  },
  currentBadge: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  currentBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.neutral.white,
  },
  deviceOS: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 4,
  },
  deviceLocation: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  removeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.status.error + '10',
  },
  deviceFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.lightGray,
  },
  lastActive: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  activeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.status.success,
    marginRight: 6,
  },
  activeText: {
    fontSize: 12,
    color: Colors.status.success,
    fontWeight: '600',
  },
  deviceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  trustButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.status.success + '15',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 4,
  },
  trustButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.status.success,
  },
  trustedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.status.success + '15',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 2,
    marginLeft: 8,
  },
  trustedBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.status.success,
  },
  errorCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.status.warning + '10',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.status.warning + '30',
  },
  errorText: {
    flex: 1,
    marginLeft: 12,
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.status.warning,
    marginBottom: 4,
  },
  errorDescription: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    lineHeight: 20,
    marginBottom: 4,
  },
  errorHint: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    fontStyle: 'italic',
  },
  emptyCard: {
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    padding: 32,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.neutral.lightGray,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  infoText: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
});

export default TrustedDevicesScreen;
