/**
 * Bill Payment Service
 * Handles bill payment operations, provider management, and validation for Uganda utilities
 */

const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../utils/logger');
const databaseService = require('./database');
const redisService = require('./redis');
const walletService = require('./walletService');
const transactionService = require('./transactionService');
const auditService = require('./auditService');

class BillPaymentService {
  constructor() {
    this.billTypes = {
      ELECTRICITY: 'electricity',
      WATER: 'water',
      INTERNET: 'internet',
      TV: 'tv',
      AIRTIME: 'airtime',
      DATA: 'data',
      SCHOOL_FEES: 'school_fees',
      INSURANCE: 'insurance'
    };

    this.paymentStatuses = {
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETED: 'completed',
      FAILED: 'failed',
      CANCELLED: 'cancelled',
      REFUNDED: 'refunded'
    };

    // Uganda utility providers
    this.providers = {
      // Electricity
      umeme: {
        id: 'umeme',
        name: 'UMEME',
        type: this.billTypes.ELECTRICITY,
        country: 'UG',
        apiEndpoint: config.utilityApis.umeme.baseUrl,
        apiKey: config.utilityApis.umeme.apiKey,
        merchantId: config.utilityApis.umeme.merchantId,
        accountNumberFormat: /^\d{10,12}$/,
        minAmount: 1000,
        maxAmount: 1000000,
        currency: 'UGX',
        isActive: true
      },
      
      // Water
      nwsc: {
        id: 'nwsc',
        name: 'National Water and Sewerage Corporation (NWSC)',
        type: this.billTypes.WATER,
        country: 'UG',
        apiEndpoint: config.utilityApis.nwsc.baseUrl,
        apiKey: config.utilityApis.nwsc.apiKey,
        merchantId: config.utilityApis.nwsc.merchantId,
        accountNumberFormat: /^\d{8,10}$/,
        minAmount: 500,
        maxAmount: 500000,
        currency: 'UGX',
        isActive: true
      },

      // Airtime providers
      mtn_airtime: {
        id: 'mtn_airtime',
        name: 'MTN Airtime',
        type: this.billTypes.AIRTIME,
        country: 'UG',
        accountNumberFormat: /^256\d{9}$/,
        minAmount: 500,
        maxAmount: 500000,
        currency: 'UGX',
        isActive: true
      },

      airtel_airtime: {
        id: 'airtel_airtime',
        name: 'Airtel Airtime',
        type: this.billTypes.AIRTIME,
        country: 'UG',
        accountNumberFormat: /^256\d{9}$/,
        minAmount: 500,
        maxAmount: 500000,
        currency: 'UGX',
        isActive: true
      },

      // Data bundles
      mtn_data: {
        id: 'mtn_data',
        name: 'MTN Data Bundles',
        type: this.billTypes.DATA,
        country: 'UG',
        accountNumberFormat: /^256\d{9}$/,
        minAmount: 1000,
        maxAmount: 200000,
        currency: 'UGX',
        isActive: true,
        packages: [
          { id: 'daily_100mb', name: 'Daily 100MB', amount: 1000, validity: '24 hours' },
          { id: 'weekly_1gb', name: 'Weekly 1GB', amount: 5000, validity: '7 days' },
          { id: 'monthly_5gb', name: 'Monthly 5GB', amount: 20000, validity: '30 days' }
        ]
      },

      // TV subscriptions
      dstv: {
        id: 'dstv',
        name: 'DStv',
        type: this.billTypes.TV,
        country: 'UG',
        accountNumberFormat: /^\d{10}$/,
        minAmount: 15000,
        maxAmount: 200000,
        currency: 'UGX',
        isActive: true
      },

      gotv: {
        id: 'gotv',
        name: 'GOtv',
        type: this.billTypes.TV,
        country: 'UG',
        accountNumberFormat: /^\d{10}$/,
        minAmount: 5000,
        maxAmount: 50000,
        currency: 'UGX',
        isActive: true
      }
    };
  }

  /**
   * Get all available bill providers
   */
  async getBillProviders(type = null, country = 'UG') {
    try {
      let providers = Object.values(this.providers).filter(provider => 
        provider.isActive && provider.country === country
      );

      if (type) {
        providers = providers.filter(provider => provider.type === type);
      }

      // Remove sensitive information
      return providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        country: provider.country,
        minAmount: provider.minAmount,
        maxAmount: provider.maxAmount,
        currency: provider.currency,
        accountNumberFormat: provider.accountNumberFormat.toString(),
        packages: provider.packages || null
      }));
    } catch (error) {
      logger.error('Failed to get bill providers:', error);
      throw error;
    }
  }

  /**
   * Validate bill account
   */
  async validateBillAccount(providerId, accountNumber) {
    try {
      const provider = this.providers[providerId];
      if (!provider) {
        throw new Error('Invalid provider');
      }

      // Validate account number format
      if (!provider.accountNumberFormat.test(accountNumber)) {
        throw new Error('Invalid account number format');
      }

      // For simulation, return mock customer data
      // In production, this would call the actual provider API
      const mockCustomerData = await this.getMockCustomerData(providerId, accountNumber);

      logger.audit('Bill account validated', {
        providerId,
        accountNumber: accountNumber.substring(0, 4) + '****',
        customerName: mockCustomerData.customerName
      });

      return {
        isValid: true,
        customerName: mockCustomerData.customerName,
        accountBalance: mockCustomerData.accountBalance,
        lastPaymentDate: mockCustomerData.lastPaymentDate,
        provider: {
          id: provider.id,
          name: provider.name,
          type: provider.type
        }
      };
    } catch (error) {
      logger.error('Bill account validation failed:', error);
      throw error;
    }
  }

  /**
   * Process bill payment
   */
  async processBillPayment(paymentData, userId) {
    try {
      const {
        providerId,
        accountNumber,
        amount,
        customerName,
        packageId = null,
        description = ''
      } = paymentData;

      // Validate provider
      const provider = this.providers[providerId];
      if (!provider) {
        throw new Error('Invalid provider');
      }

      // Validate amount
      if (amount < provider.minAmount || amount > provider.maxAmount) {
        throw new Error(`Amount must be between ${provider.minAmount} and ${provider.maxAmount}`);
      }

      // Validate account
      const accountValidation = await this.validateBillAccount(providerId, accountNumber);
      if (!accountValidation.isValid) {
        throw new Error('Invalid account number');
      }

      // Check user wallet balance
      const wallet = await walletService.getWallet(userId);
      if (!wallet || wallet.available_balance < amount) {
        throw new Error('Insufficient wallet balance');
      }

      // Create transaction
      const transactionData = {
        type: 'bill_payment',
        fromUserId: userId,
        amount,
        currency: provider.currency,
        description: description || `${provider.name} bill payment`,
        metadata: {
          providerId,
          accountNumber,
          customerName,
          packageId,
          billType: provider.type
        },
        provider: providerId
      };

      const transaction = await transactionService.createTransaction(transactionData);

      // Process the payment
      const paymentResult = await this.executePayment(transaction, provider, {
        accountNumber,
        amount,
        customerName,
        packageId
      });

      // Update transaction status
      if (paymentResult.success) {
        await transactionService.completeTransaction(transaction.id, paymentResult);
        
        // Debit user wallet
        await walletService.updateBalance(
          userId,
          amount,
          'debit',
          `Bill payment: ${provider.name}`,
          transaction.id
        );
      } else {
        await transactionService.failTransaction(transaction.id, paymentResult.error);
      }

      // Log audit event
      await auditService.logUserAction(
        'bill_payment',
        userId,
        'bill_payment',
        transaction.id,
        {
          providerId,
          amount,
          currency: provider.currency,
          status: paymentResult.success ? 'completed' : 'failed'
        }
      );

      return {
        transactionId: transaction.id,
        reference: transaction.transaction_reference,
        status: paymentResult.success ? 'completed' : 'failed',
        amount,
        currency: provider.currency,
        provider: {
          id: provider.id,
          name: provider.name
        },
        customerName,
        paymentResult
      };
    } catch (error) {
      logger.error('Bill payment processing failed:', error);
      throw error;
    }
  }

  /**
   * Execute payment with provider
   */
  async executePayment(transaction, provider, paymentDetails) {
    try {
      // For simulation purposes, we'll mock the payment execution
      // In production, this would integrate with actual provider APIs

      const { accountNumber, amount, customerName, packageId } = paymentDetails;

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate success/failure (95% success rate)
      const isSuccess = Math.random() > 0.05;

      if (isSuccess) {
        const paymentReference = this.generatePaymentReference(provider.id);
        
        logger.info('Bill payment executed successfully', {
          transactionId: transaction.id,
          providerId: provider.id,
          amount,
          paymentReference
        });

        return {
          success: true,
          paymentReference,
          providerTransactionId: `${provider.id.toUpperCase()}-${Date.now()}`,
          timestamp: new Date().toISOString(),
          customerName,
          accountNumber: accountNumber.substring(0, 4) + '****',
          amount,
          currency: provider.currency
        };
      } else {
        const errorMessage = 'Payment failed at provider';
        
        logger.error('Bill payment execution failed', {
          transactionId: transaction.id,
          providerId: provider.id,
          error: errorMessage
        });

        return {
          success: false,
          error: errorMessage,
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      logger.error('Payment execution failed:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get bill payment history
   */
  async getBillPaymentHistory(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        providerId = null,
        billType = null,
        startDate = null,
        endDate = null
      } = options;

      const offset = (page - 1) * limit;

      const supabase = databaseService.getSupabase();
      let query = supabase
        .from('transactions')
        .select('*')
        .eq('from_user_id', userId)
        .eq('type', 'bill_payment')
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (startDate) {
        query = query.gte('created_at', startDate);
      }

      if (endDate) {
        query = query.lte('created_at', endDate);
      }

      const { data: payments, error } = await query;

      if (error) {
        logger.error('Failed to get bill payment history:', error);
        throw new Error('Failed to retrieve bill payment history');
      }

      // Filter by provider or bill type if specified
      let filteredPayments = payments;
      if (providerId || billType) {
        filteredPayments = payments.filter(payment => {
          const metadata = payment.metadata ? JSON.parse(payment.metadata) : {};
          if (providerId && metadata.providerId !== providerId) return false;
          if (billType && metadata.billType !== billType) return false;
          return true;
        });
      }

      // Format payments for response
      const formattedPayments = filteredPayments.map(payment => {
        const metadata = payment.metadata ? JSON.parse(payment.metadata) : {};
        const provider = this.providers[metadata.providerId];

        return {
          id: payment.id,
          reference: payment.transaction_reference,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
          provider: provider ? {
            id: provider.id,
            name: provider.name,
            type: provider.type
          } : null,
          customerName: metadata.customerName,
          accountNumber: metadata.accountNumber ? 
            metadata.accountNumber.substring(0, 4) + '****' : null,
          createdAt: payment.created_at,
          completedAt: payment.completed_at
        };
      });

      return {
        payments: formattedPayments,
        pagination: {
          page,
          limit,
          total: filteredPayments.length,
          hasNext: filteredPayments.length === limit,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      logger.error('Failed to get bill payment history:', error);
      throw error;
    }
  }

  /**
   * Get mock customer data for validation
   */
  async getMockCustomerData(providerId, accountNumber) {
    // In production, this would call the actual provider API
    const mockData = {
      customerName: `Customer ${accountNumber.substring(-4)}`,
      accountBalance: Math.floor(Math.random() * 100000),
      lastPaymentDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    return mockData;
  }

  /**
   * Generate payment reference
   */
  generatePaymentReference(providerId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${providerId.toUpperCase()}-${timestamp}-${random}`;
  }

  /**
   * Get provider by ID
   */
  getProvider(providerId) {
    return this.providers[providerId] || null;
  }

  /**
   * Check if provider supports amount
   */
  validateProviderAmount(providerId, amount) {
    const provider = this.providers[providerId];
    if (!provider) {
      return { valid: false, error: 'Invalid provider' };
    }

    if (amount < provider.minAmount) {
      return { 
        valid: false, 
        error: `Minimum amount is ${provider.minAmount} ${provider.currency}` 
      };
    }

    if (amount > provider.maxAmount) {
      return { 
        valid: false, 
        error: `Maximum amount is ${provider.maxAmount} ${provider.currency}` 
      };
    }

    return { valid: true };
  }
}

// Create singleton instance
const billPaymentService = new BillPaymentService();

module.exports = billPaymentService;
