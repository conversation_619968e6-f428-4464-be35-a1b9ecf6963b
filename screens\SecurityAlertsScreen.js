/**
 * Security Alerts Screen
 * Displays security alerts, fraud notifications, and suspicious activity reports
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  Alert,
  FlatList
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import securityAlertService from '../services/securityAlertService';
import { formatRelativeTime } from '../utils/dateUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SecurityAlertsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const [alerts, setAlerts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState('all'); // all, unresolved, high

  useEffect(() => {
    loadSecurityAlerts();
  }, [filter]);

  const loadSecurityAlerts = async () => {
    try {
      setLoading(true);
      // Get current user ID from auth context
      const userId = 'current-user-id'; // Replace with actual user ID from auth
      const alertsData = await securityAlertService.getUserSecurityAlerts(userId, 100);
      
      // Apply filter
      let filteredAlerts = alertsData;
      if (filter === 'unresolved') {
        filteredAlerts = alertsData.filter(alert => !alert.resolved);
      } else if (filter === 'high') {
        filteredAlerts = alertsData.filter(alert => 
          alert.severity === 'high' || alert.severity === 'critical'
        );
      }
      
      setAlerts(filteredAlerts);
    } catch (error) {
      console.error('❌ Error loading security alerts:', error);
      Alert.alert('Error', 'Failed to load security alerts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadSecurityAlerts();
    setRefreshing(false);
  };

  const handleResolveAlert = async (alertId) => {
    try {
      const userId = 'current-user-id'; // Replace with actual user ID
      await securityAlertService.resolveAlert(alertId, userId);
      
      // Update local state
      setAlerts(prevAlerts => 
        prevAlerts.map(alert => 
          alert.id === alertId 
            ? { ...alert, resolved: true, resolvedAt: new Date().toISOString() }
            : alert
        )
      );
      
      Alert.alert('Success', 'Alert marked as resolved');
    } catch (error) {
      console.error('❌ Error resolving alert:', error);
      Alert.alert('Error', 'Failed to resolve alert. Please try again.');
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return '#dc3545';
      case 'high': return '#fd7e14';
      case 'medium': return '#ffc107';
      case 'low': return '#17a2b8';
      default: return theme.colors.textSecondary;
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return 'alert-circle';
      case 'high': return 'warning';
      case 'medium': return 'information-circle';
      case 'low': return 'checkmark-circle';
      default: return 'information-circle';
    }
  };

  const getAlertTypeIcon = (type) => {
    switch (type) {
      case 'fraud_alert': return 'shield-outline';
      case 'suspicious_login': return 'log-in-outline';
      case 'security_alert': return 'alert-circle-outline';
      case 'limit_violation': return 'card-outline';
      case 'new_device': return 'phone-portrait-outline';
      default: return 'notifications-outline';
    }
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      {['all', 'unresolved', 'high'].map((filterType) => (
        <TouchableOpacity
          key={filterType}
          style={[
            styles.filterButton,
            filter === filterType && styles.filterButtonActive
          ]}
          onPress={() => setFilter(filterType)}
        >
          <Text style={[
            styles.filterButtonText,
            filter === filterType && styles.filterButtonTextActive
          ]}>
            {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderAlertItem = ({ item: alert }) => (
    <View style={[
      styles.alertCard,
      alert.resolved && styles.alertCardResolved
    ]}>
      <View style={styles.alertHeader}>
        <View style={styles.alertIconContainer}>
          <Ionicons 
            name={getAlertTypeIcon(alert.type)} 
            size={20} 
            color={getSeverityColor(alert.severity)} 
          />
        </View>
        
        <View style={styles.alertInfo}>
          <Text style={styles.alertMessage} numberOfLines={2}>
            {alert.message}
          </Text>
          <Text style={styles.alertTime}>
            {formatRelativeTime(alert.createdAt)}
          </Text>
        </View>
        
        <View style={styles.alertSeverity}>
          <Ionicons 
            name={getSeverityIcon(alert.severity)} 
            size={16} 
            color={getSeverityColor(alert.severity)} 
          />
          <Text style={[
            styles.alertSeverityText,
            { color: getSeverityColor(alert.severity) }
          ]}>
            {alert.severity.toUpperCase()}
          </Text>
        </View>
      </View>

      {alert.details && Object.keys(alert.details).length > 0 && (
        <View style={styles.alertDetails}>
          {Object.entries(alert.details).slice(0, 3).map(([key, value]) => (
            <Text key={key} style={styles.alertDetailText}>
              <Text style={styles.alertDetailKey}>
                {key.replace(/_/g, ' ').toUpperCase()}:
              </Text> {value}
            </Text>
          ))}
        </View>
      )}

      <View style={styles.alertActions}>
        {alert.resolved ? (
          <View style={styles.resolvedContainer}>
            <Ionicons name="checkmark-circle" size={16} color={theme.colors.success} />
            <Text style={styles.resolvedText}>
              Resolved {formatRelativeTime(alert.resolvedAt)}
            </Text>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.resolveButton}
            onPress={() => handleResolveAlert(alert.id)}
          >
            <Ionicons name="checkmark" size={16} color={theme.colors.white} />
            <Text style={styles.resolveButtonText}>Mark Resolved</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={styles.detailsButton}
          onPress={() => navigation.navigate('AlertDetails', { alertId: alert.id })}
        >
          <Text style={styles.detailsButtonText}>Details</Text>
          <Ionicons name="chevron-forward" size={16} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="shield-checkmark" size={64} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Security Alerts</Text>
      <Text style={styles.emptyDescription}>
        {filter === 'all' 
          ? 'Your account is secure. No alerts to display.'
          : filter === 'unresolved'
          ? 'All alerts have been resolved.'
          : 'No high-priority alerts found.'
        }
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Security Alerts</Text>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.navigate('SecuritySettings')}
        >
          <Ionicons name="settings-outline" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Filter Buttons */}
      {renderFilterButtons()}

      {/* Alerts List */}
      <FlatList
        data={alerts}
        renderItem={renderAlertItem}
        keyExtractor={(item) => item.id}
        style={styles.alertsList}
        contentContainerStyle={alerts.length === 0 ? styles.emptyListContainer : null}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      {/* Security Tips */}
      <View style={styles.tipsCard}>
        <View style={styles.tipsHeader}>
          <Ionicons name="bulb-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.tipsTitle}>Security Tip</Text>
        </View>
        <Text style={styles.tipsText}>
          Enable two-factor authentication and regularly review your security alerts 
          to keep your account safe.
        </Text>
        <TouchableOpacity 
          style={styles.tipsButton}
          onPress={() => navigation.navigate('SecuritySettings')}
        >
          <Text style={styles.tipsButtonText}>Security Settings</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    padding: 5,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: theme.colors.white,
  },
  alertsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  alertCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  alertCardResolved: {
    opacity: 0.7,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  alertIconContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  alertInfo: {
    flex: 1,
  },
  alertMessage: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 4,
  },
  alertTime: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  alertSeverity: {
    alignItems: 'center',
  },
  alertSeverityText: {
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  alertDetails: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  alertDetailText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  alertDetailKey: {
    fontWeight: '600',
    color: theme.colors.text,
  },
  alertActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resolvedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resolvedText: {
    fontSize: 12,
    color: theme.colors.success,
    marginLeft: 4,
    fontWeight: '500',
  },
  resolveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  resolveButtonText: {
    color: theme.colors.white,
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailsButtonText: {
    color: theme.colors.primary,
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 40,
  },
  tipsCard: {
    backgroundColor: theme.colors.surface,
    margin: 20,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  tipsText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
    marginBottom: 12,
  },
  tipsButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  tipsButtonText: {
    color: theme.colors.primary,
    fontSize: 12,
    fontWeight: '600',
  },
});

export default SecurityAlertsScreen;
