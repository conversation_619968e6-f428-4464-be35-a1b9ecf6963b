/**
 * Global Error Handler Middleware
 * Centralized error handling for the JiraniPay backend
 */

const logger = require('../utils/logger');
const config = require('../config/config');

/**
 * Custom error classes
 */
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.errors = errors;
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

class PaymentError extends AppError {
  constructor(message, code = 'PAYMENT_ERROR') {
    super(message, 402, code);
  }
}

class ExternalServiceError extends AppError {
  constructor(message, service) {
    super(message, 503, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
  }
}

/**
 * Error handler middleware
 */
const errorHandler = (error, req, res, next) => {
  let err = { ...error };
  err.message = error.message;

  // Log error with context
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous',
    requestId: req.id || 'unknown'
  };

  // Log different error types with appropriate levels
  if (err.statusCode >= 500) {
    logger.errorWithContext(error, errorContext);
  } else if (err.statusCode >= 400) {
    logger.warn(error.message, errorContext);
  } else {
    logger.info(error.message, errorContext);
  }

  // Handle specific error types
  if (error.name === 'ValidationError') {
    err = handleValidationError(error);
  } else if (error.name === 'CastError') {
    err = handleCastError(error);
  } else if (error.code === 11000) {
    err = handleDuplicateFieldError(error);
  } else if (error.name === 'JsonWebTokenError') {
    err = handleJWTError(error);
  } else if (error.name === 'TokenExpiredError') {
    err = handleJWTExpiredError(error);
  } else if (error.code === 'ECONNREFUSED') {
    err = handleConnectionError(error);
  } else if (error.code === 'ENOTFOUND') {
    err = handleDNSError(error);
  } else if (error.code === 'ETIMEDOUT') {
    err = handleTimeoutError(error);
  }

  // Default to 500 server error
  if (!err.statusCode) {
    err.statusCode = 500;
    err.code = 'INTERNAL_SERVER_ERROR';
  }

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      code: err.code || 'UNKNOWN_ERROR',
      message: err.message || 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      requestId: req.id || 'unknown'
    }
  };

  // Add validation errors if present
  if (err.errors && Array.isArray(err.errors)) {
    errorResponse.error.details = err.errors;
  }

  // Add stack trace in development
  if (config.server.nodeEnv === 'development') {
    errorResponse.error.stack = error.stack;
  }

  // Add retry information for certain errors
  if (err.statusCode === 429) {
    errorResponse.error.retryAfter = 60; // seconds
  }

  // Security: Don't expose sensitive information in production
  if (config.server.nodeEnv === 'production') {
    // Generic message for 500 errors
    if (err.statusCode === 500) {
      errorResponse.error.message = 'Internal server error';
    }
    
    // Remove any potentially sensitive data
    delete errorResponse.error.stack;
  }

  res.status(err.statusCode).json(errorResponse);
};

/**
 * Handle Mongoose validation errors
 */
const handleValidationError = (error) => {
  const errors = Object.values(error.errors).map(val => ({
    field: val.path,
    message: val.message,
    value: val.value
  }));
  
  return new ValidationError('Validation failed', errors);
};

/**
 * Handle Mongoose cast errors
 */
const handleCastError = (error) => {
  const message = `Invalid ${error.path}: ${error.value}`;
  return new ValidationError(message);
};

/**
 * Handle duplicate field errors
 */
const handleDuplicateFieldError = (error) => {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];
  const message = `${field} '${value}' already exists`;
  return new ConflictError(message);
};

/**
 * Handle JWT errors
 */
const handleJWTError = (error) => {
  return new AuthenticationError('Invalid token');
};

/**
 * Handle JWT expired errors
 */
const handleJWTExpiredError = (error) => {
  return new AuthenticationError('Token expired');
};

/**
 * Handle connection errors
 */
const handleConnectionError = (error) => {
  return new ExternalServiceError('Service temporarily unavailable', 'database');
};

/**
 * Handle DNS errors
 */
const handleDNSError = (error) => {
  return new ExternalServiceError('External service not found', 'external');
};

/**
 * Handle timeout errors
 */
const handleTimeoutError = (error) => {
  return new ExternalServiceError('Request timeout', 'external');
};

/**
 * Async error wrapper
 */
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * 404 handler for undefined routes
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler,
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  PaymentError,
  ExternalServiceError
};
