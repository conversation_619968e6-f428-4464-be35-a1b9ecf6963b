-- =====================================================
-- JiraniPay Wallet Creation RLS Fix
-- =====================================================
-- This script fixes wallet creation issues including:
-- 1. RLS policy violations for payment_accounts
-- 2. Phone number storage and retrieval
-- 3. Account number generation
-- Run this in your Supabase SQL Editor

-- Step 1: Temporarily disable RLS to clean up existing policies
ALTER TABLE public.payment_accounts DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop existing policies that might be causing conflicts
DROP POLICY IF EXISTS "Users can view own payment accounts" ON public.payment_accounts;
DROP POLICY IF EXISTS "Users can insert own payment accounts" ON public.payment_accounts;
DROP POLICY IF EXISTS "Users can update own payment accounts" ON public.payment_accounts;
DROP POLICY IF EXISTS "Users can delete own payment accounts" ON public.payment_accounts;
DROP POLICY IF EXISTS "Users can manage own payment accounts" ON public.payment_accounts;

-- Step 3: Grant comprehensive permissions to authenticated users
GRANT ALL ON public.payment_accounts TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Step 4: Create simple, permissive RLS policies for payment_accounts
-- Allow authenticated users to manage their own payment accounts
CREATE POLICY "payment_accounts_select_policy" ON public.payment_accounts
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "payment_accounts_insert_policy" ON public.payment_accounts
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "payment_accounts_update_policy" ON public.payment_accounts
    FOR UPDATE 
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "payment_accounts_delete_policy" ON public.payment_accounts
    FOR DELETE 
    TO authenticated
    USING (auth.uid() = user_id);

-- Step 5: Re-enable RLS
ALTER TABLE public.payment_accounts ENABLE ROW LEVEL SECURITY;

-- Step 6: Create function to safely create wallet with proper error handling
CREATE OR REPLACE FUNCTION public.create_user_wallet_safe(
    p_user_id UUID,
    p_phone_number TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_account_number TEXT;
    v_wallet_data JSON;
    v_existing_wallet RECORD;
BEGIN
    -- Check if wallet already exists
    SELECT * INTO v_existing_wallet 
    FROM public.payment_accounts 
    WHERE user_id = p_user_id 
    AND account_type = 'wallet' 
    AND is_primary = true;
    
    IF FOUND THEN
        -- Return existing wallet
        SELECT json_build_object(
            'success', true,
            'data', row_to_json(v_existing_wallet),
            'message', 'Wallet already exists'
        ) INTO v_wallet_data;
        RETURN v_wallet_data;
    END IF;
    
    -- Generate account number from phone or fallback
    IF p_phone_number IS NOT NULL AND p_phone_number != '' THEN
        -- Use phone number as account number (remove +256 and +)
        v_account_number := REPLACE(REPLACE(p_phone_number, '+256', '0'), '+', '');
    ELSE
        -- Fallback to JP number
        v_account_number := 'JP' || EXTRACT(EPOCH FROM NOW())::BIGINT::TEXT || LPAD((RANDOM() * 999)::INT::TEXT, 3, '0');
    END IF;
    
    -- Insert new wallet
    INSERT INTO public.payment_accounts (
        user_id,
        account_type,
        provider_name,
        account_number,
        account_name,
        currency,
        is_primary,
        is_active,
        balance,
        metadata
    ) VALUES (
        p_user_id,
        'wallet',
        'JiraniPay',
        v_account_number,
        'JiraniPay Wallet',
        'UGX',
        true,
        true,
        0.00,
        json_build_object(
            'created_via', 'app',
            'wallet_type', 'primary',
            'environment', 'production'
        )
    ) RETURNING * INTO v_existing_wallet;
    
    -- Return success with wallet data
    SELECT json_build_object(
        'success', true,
        'data', row_to_json(v_existing_wallet),
        'message', 'Wallet created successfully'
    ) INTO v_wallet_data;
    
    RETURN v_wallet_data;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error details
    SELECT json_build_object(
        'success', false,
        'error', SQLERRM,
        'code', SQLSTATE,
        'message', 'Failed to create wallet'
    ) INTO v_wallet_data;
    
    RETURN v_wallet_data;
END;
$$;

-- Step 7: Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.create_user_wallet_safe(UUID, TEXT) TO authenticated;

-- Step 8: Verify the setup
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'payment_accounts'
ORDER BY policyname;

-- Success message
SELECT 'Wallet creation RLS policies have been successfully updated!' as status;
