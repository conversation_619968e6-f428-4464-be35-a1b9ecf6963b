import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import authService from '../services/authService';

/**
 * ManualRequestRecipientScreen - Manual phone number entry for money requests
 */
const ManualRequestRecipientScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [countryCode, setCountryCode] = useState('+256');

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('❌ Error loading user data:', error);
    }
  };

  const formatPhoneNumber = (text) => {
    // Remove all non-numeric characters
    const cleaned = text.replace(/\D/g, '');
    
    // Format as XXX XXX XXX for Uganda numbers
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
    } else {
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)}`;
    }
  };

  const handlePhoneNumberChange = (text) => {
    const formatted = formatPhoneNumber(text);
    setPhoneNumber(formatted);
  };

  const validatePhoneNumber = (phone) => {
    // Remove spaces and check if it's a valid Uganda number
    const cleaned = phone.replace(/\s/g, '');
    
    // Uganda mobile numbers: 70X, 71X, 72X, 73X, 74X, 75X, 76X, 77X, 78X, 79X
    const ugandaPattern = /^7[0-9]{8}$/;
    
    return ugandaPattern.test(cleaned);
  };

  const handleContinue = async () => {
    if (!phoneNumber.trim()) {
      Alert.alert('Phone Number Required', 'Please enter a phone number');
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      Alert.alert(
        'Invalid Phone Number', 
        'Please enter a valid Uganda mobile number (e.g., 703 089 916)'
      );
      return;
    }

    if (!recipientName.trim()) {
      Alert.alert('Name Required', 'Please enter the recipient\'s name');
      return;
    }

    try {
      setLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Create contact object for manual entry
      const manualContact = {
        id: `manual_${Date.now()}`,
        name: recipientName.trim(),
        phoneNumber: `${countryCode}${phoneNumber.replace(/\s/g, '')}`,
        avatar: recipientName.charAt(0).toUpperCase(),
        isManual: true
      };

      // Navigate to amount screen with manual contact
      navigation.navigate('RequestAmount', {
        contact: manualContact,
        user
      });

    } catch (error) {
      console.error('❌ Error processing manual recipient:', error);
      Alert.alert('Error', 'Failed to process recipient information. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Enter Phone Number</Text>
        <Text style={styles.headerSubtitle}>Request money from anyone</Text>
      </View>
      <View style={styles.headerRight} />
    </View>
  );

  const renderCountrySelector = () => (
    <View style={styles.countrySection}>
      <Text style={styles.sectionTitle}>Country</Text>
      <View style={styles.countrySelector}>
        <View style={styles.flagContainer}>
          <Text style={styles.flag}>🇺🇬</Text>
        </View>
        <View style={styles.countryInfo}>
          <Text style={styles.countryName}>Uganda</Text>
          <Text style={styles.countryCode}>{countryCode}</Text>
        </View>
        <Ionicons name="checkmark-circle" size={20} color={Colors.status.success} />
      </View>
    </View>
  );

  const renderPhoneInput = () => (
    <View style={styles.phoneSection}>
      <Text style={styles.sectionTitle}>Phone Number</Text>
      <View style={styles.phoneInputContainer}>
        <View style={styles.countryCodeContainer}>
          <Text style={styles.countryCodeText}>{countryCode}</Text>
        </View>
        <TextInput
          style={styles.phoneInput}
          value={phoneNumber}
          onChangeText={handlePhoneNumberChange}
          placeholder="703 089 916"
          placeholderTextColor={Colors.neutral.warmGray}
          keyboardType="phone-pad"
          maxLength={11} // XXX XXX XXX format
        />
      </View>
      <Text style={styles.phoneHint}>
        Enter a Uganda mobile number (MTN, Airtel, UTL)
      </Text>
    </View>
  );

  const renderNameInput = () => (
    <View style={styles.nameSection}>
      <Text style={styles.sectionTitle}>Recipient Name</Text>
      <TextInput
        style={styles.nameInput}
        value={recipientName}
        onChangeText={setRecipientName}
        placeholder="Enter recipient's name"
        placeholderTextColor={Colors.neutral.warmGray}
        maxLength={50}
      />
      <Text style={styles.nameHint}>
        This helps you identify the request later
      </Text>
    </View>
  );

  const renderContinueButton = () => (
    <TouchableOpacity
      style={[
        styles.continueButton,
        (!phoneNumber || !recipientName || loading) && styles.continueButtonDisabled
      ]}
      onPress={handleContinue}
      disabled={!phoneNumber || !recipientName || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator size="small" color={Colors.neutral.white} />
      ) : (
        <>
          <Text style={styles.continueButtonText}>Continue</Text>
          <Ionicons name="arrow-forward" size={20} color={Colors.neutral.white} />
        </>
      )}
    </TouchableOpacity>
  );

  const renderInfoCard = () => (
    <View style={styles.infoCard}>
      <View style={styles.infoHeader}>
        <Ionicons name="information-circle" size={24} color={Colors.primary.main} />
        <Text style={styles.infoTitle}>Request Money</Text>
      </View>
      <Text style={styles.infoText}>
        Send a money request to anyone with a Uganda mobile number. They'll receive an SMS notification and can approve or decline your request through JiraniPay.
      </Text>
      <View style={styles.infoFeatures}>
        <View style={styles.infoFeature}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.infoFeatureText}>SMS notifications</Text>
        </View>
        <View style={styles.infoFeature}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.infoFeatureText}>Secure requests</Text>
        </View>
        <View style={styles.infoFeature}>
          <Ionicons name="checkmark-circle" size={16} color={Colors.status.success} />
          <Text style={styles.infoFeatureText}>Request tracking</Text>
        </View>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.neutral.cream} />
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCountrySelector()}
        {renderPhoneInput()}
        {renderNameInput()}
        {renderInfoCard()}
      </ScrollView>

      <View style={styles.footer}>
        {renderContinueButton()}
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  countrySection: {
    marginTop: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 12,
  },
  countrySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  flagContainer: {
    marginRight: 12,
  },
  flag: {
    fontSize: 24,
  },
  countryInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  countryCode: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  phoneSection: {
    marginBottom: 24,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    overflow: 'hidden',
  },
  countryCodeContainer: {
    backgroundColor: Colors.neutral.creamDark,
    paddingHorizontal: 16,
    paddingVertical: 16,
    justifyContent: 'center',
  },
  countryCodeText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  phoneHint: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 8,
  },
  nameSection: {
    marginBottom: 24,
  },
  nameInput: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  nameHint: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 8,
  },
  infoCard: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    marginBottom: 24,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
    marginBottom: 16,
  },
  infoFeatures: {
    gap: 8,
  },
  infoFeature: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoFeatureText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    marginLeft: 8,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary.main,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  continueButtonDisabled: {
    backgroundColor: Colors.neutral.warmGray,
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginRight: 8,
  },
});

export default ManualRequestRecipientScreen;
