/**
 * Comprehensive Syntax Validation for JiraniPay
 * 
 * Performs thorough syntax checking and validation of all JavaScript files
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 COMPREHENSIVE SYNTAX VALIDATION FOR JIRANIPAY\n');

// Files to check
const filesToCheck = [
  'services/authService.js',
  'services/walletService.js',
  'services/supabaseClient.js',
  'services/notificationService.js',
  'services/productionDataService.js',
  'services/spendingLimitService.js',
  'config/environment.js',
  'App.js'
];

let totalErrors = 0;
let totalWarnings = 0;
let filesChecked = 0;

// Syntax patterns to check
const syntaxPatterns = [
  {
    name: 'Duplicate const declarations',
    pattern: /const.*=.*const/g,
    severity: 'ERROR'
  },
  {
    name: 'Malformed await statements',
    pattern: /= await.*= await/g,
    severity: 'ERROR'
  },
  {
    name: 'Missing semicolons after statements',
    pattern: /\}\s*\n\s*[a-zA-Z]/g,
    severity: 'WARNING'
  },
  {
    name: 'Unmatched brackets',
    pattern: /\{[^}]*$/gm,
    severity: 'ERROR'
  },
  {
    name: 'Invalid import statements',
    pattern: /import.*from\s*['""]['""];?/g,
    severity: 'ERROR'
  },
  {
    name: 'Trailing commas in objects',
    pattern: /,\s*\}/g,
    severity: 'WARNING'
  }
];

// Import/Export validation
const importExportPatterns = [
  {
    name: 'Missing file extensions in imports',
    pattern: /import.*from\s*['"]\.\/[^'"]*(?<!\.js)['"]/g,
    severity: 'WARNING'
  },
  {
    name: 'Circular import potential',
    pattern: /import.*from\s*['"]\.\.?\//g,
    severity: 'INFO'
  }
];

function checkFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return { errors: 0, warnings: 0 };
  }

  console.log(`📄 Checking: ${filePath}`);
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const lines = content.split('\n');
  
  let fileErrors = 0;
  let fileWarnings = 0;

  // Check syntax patterns
  syntaxPatterns.forEach(({ name, pattern, severity }) => {
    const matches = content.match(pattern);
    if (matches && matches.length > 0) {
      matches.forEach(match => {
        const lineNumber = findLineNumber(content, match);
        console.log(`   ${severity === 'ERROR' ? '❌' : '⚠️'} ${severity}: ${name}`);
        console.log(`      Line ${lineNumber}: ${match.trim()}`);
        
        if (severity === 'ERROR') fileErrors++;
        else fileWarnings++;
      });
    }
  });

  // Check import/export patterns
  importExportPatterns.forEach(({ name, pattern, severity }) => {
    const matches = content.match(pattern);
    if (matches && matches.length > 0) {
      matches.forEach(match => {
        const lineNumber = findLineNumber(content, match);
        console.log(`   ${severity === 'ERROR' ? '❌' : severity === 'WARNING' ? '⚠️' : 'ℹ️'} ${severity}: ${name}`);
        console.log(`      Line ${lineNumber}: ${match.trim()}`);
        
        if (severity === 'ERROR') fileErrors++;
        else if (severity === 'WARNING') fileWarnings++;
      });
    }
  });

  // Check for basic JavaScript syntax using try/catch
  try {
    // Basic syntax validation (this won't catch all issues but helps)
    const bracketCount = (content.match(/\{/g) || []).length - (content.match(/\}/g) || []).length;
    const parenCount = (content.match(/\(/g) || []).length - (content.match(/\)/g) || []).length;
    const squareBracketCount = (content.match(/\[/g) || []).length - (content.match(/\]/g) || []).length;

    if (bracketCount !== 0) {
      console.log(`   ❌ ERROR: Unmatched curly brackets (${bracketCount > 0 ? 'missing closing' : 'extra closing'})`);
      fileErrors++;
    }
    if (parenCount !== 0) {
      console.log(`   ❌ ERROR: Unmatched parentheses (${parenCount > 0 ? 'missing closing' : 'extra closing'})`);
      fileErrors++;
    }
    if (squareBracketCount !== 0) {
      console.log(`   ❌ ERROR: Unmatched square brackets (${squareBracketCount > 0 ? 'missing closing' : 'extra closing'})`);
      fileErrors++;
    }
  } catch (error) {
    console.log(`   ❌ ERROR: Basic syntax validation failed: ${error.message}`);
    fileErrors++;
  }

  if (fileErrors === 0 && fileWarnings === 0) {
    console.log(`   ✅ No syntax issues found`);
  }

  console.log('');
  return { errors: fileErrors, warnings: fileWarnings };
}

function findLineNumber(content, searchText) {
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchText.trim())) {
      return i + 1;
    }
  }
  return 'unknown';
}

// Check dependency compatibility
function checkDependencies() {
  console.log('📦 Checking package.json dependencies...');
  
  const packageJsonPath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    // Check for known problematic combinations
    const checks = [
      {
        name: 'React Native and Expo compatibility',
        check: () => dependencies['react-native'] && dependencies['expo'],
        message: 'React Native and Expo versions should be compatible'
      },
      {
        name: 'Supabase client version',
        check: () => dependencies['@supabase/supabase-js'],
        message: `Supabase client version: ${dependencies['@supabase/supabase-js']}`
      }
    ];

    checks.forEach(({ name, check, message }) => {
      if (check()) {
        console.log(`   ✅ ${name}: ${message}`);
      }
    });
  }
  console.log('');
}

// Run comprehensive validation
async function runValidation() {
  console.log('Starting comprehensive syntax validation...\n');

  // Check all files
  for (const file of filesToCheck) {
    const result = checkFile(file);
    totalErrors += result.errors;
    totalWarnings += result.warnings;
    filesChecked++;
  }

  // Check dependencies
  checkDependencies();

  // Summary
  console.log('=' .repeat(60));
  console.log('📊 COMPREHENSIVE SYNTAX VALIDATION SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Files checked: ${filesChecked}`);
  console.log(`Total errors: ${totalErrors} ❌`);
  console.log(`Total warnings: ${totalWarnings} ⚠️`);

  if (totalErrors === 0) {
    console.log('\n🎉 SUCCESS: No syntax errors found!');
    console.log('✅ All files passed syntax validation');
    console.log('✅ Ready for Metro bundler');
  } else {
    console.log('\n❌ FAILED: Syntax errors found');
    console.log('🔧 Fix all errors before proceeding');
  }

  if (totalWarnings > 0) {
    console.log(`\n⚠️  ${totalWarnings} warnings found - consider addressing these`);
  }

  console.log('\n📋 NEXT STEPS:');
  if (totalErrors === 0) {
    console.log('1. Restart Metro bundler: npx expo start --clear');
    console.log('2. Test app loading on device/web');
    console.log('3. Verify authentication flow works');
  } else {
    console.log('1. Fix all syntax errors listed above');
    console.log('2. Re-run this validation script');
    console.log('3. Only proceed when all errors are resolved');
  }

  return totalErrors === 0;
}

// Run the validation
runValidation().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('❌ Validation script failed:', error.message);
  process.exit(1);
});
