import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
  SafeAreaView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import contactService from '../services/contactService';
import { Colors } from '../constants/Colors';
import { getDisplayFormat } from '../utils/phoneValidation';

/**
 * ContactPicker Component
 * Provides contact selection interface with search, favorites, and device contacts
 */
const ContactPicker = ({ 
  visible, 
  onClose, 
  onSelectContact, 
  title = "Select Contact",
  showFavoritesFirst = true 
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [contacts, setContacts] = useState([]);
  const [favoriteContacts, setFavoriteContacts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [permissionDenied, setPermissionDenied] = useState(false);

  // Initialize contacts when component mounts or becomes visible
  useEffect(() => {
    if (visible) {
      initializeContacts();
    }
  }, [visible]);

  /**
   * Initialize contact service and load contacts
   */
  const initializeContacts = async () => {
    try {
      setLoading(true);
      setPermissionDenied(false);

      console.log('📱 ContactPicker: Initializing contacts...');
      
      const result = await contactService.initialize();
      
      if (result.success) {
        setContacts(contactService.getAllContacts());
        setFavoriteContacts(contactService.getFavoriteContacts());
        console.log(`📱 ContactPicker: Loaded ${result.contactCount} contacts, ${result.favoriteCount} favorites`);
      } else {
        console.log('⚠️ ContactPicker: Failed to load contacts:', result.error);
        if (result.error?.includes('permission')) {
          setPermissionDenied(true);
        }
      }
    } catch (error) {
      console.error('❌ ContactPicker: Error initializing contacts:', error);
      Alert.alert('Error', 'Failed to load contacts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Request contacts permission
   */
  const requestPermission = async () => {
    try {
      setLoading(true);
      const result = await contactService.requestContactsPermission();
      
      if (result.success) {
        setPermissionDenied(false);
        await initializeContacts();
      } else {
        Alert.alert(
          'Permission Required',
          'JiraniPay needs access to your contacts to help you send money quickly. Please enable contacts permission in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => {/* Open settings */} }
          ]
        );
      }
    } catch (error) {
      console.error('❌ Error requesting permission:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Filter contacts based on search query
   */
  const filteredContacts = useMemo(() => {
    if (!searchQuery.trim()) {
      return contacts;
    }
    return contactService.searchContacts(searchQuery);
  }, [contacts, searchQuery]);

  /**
   * Handle contact selection
   */
  const handleSelectContact = async (contact) => {
    try {
      console.log('📱 ContactPicker: Contact selected:', contact.name);
      
      // Add to favorites (increment frequency)
      await contactService.addToFavorites(contact);
      
      // Update local state
      setFavoriteContacts(contactService.getFavoriteContacts());
      setContacts(contactService.getAllContacts());
      
      // Call parent callback
      onSelectContact(contact);
      
      // Close modal
      onClose();
    } catch (error) {
      console.error('❌ Error selecting contact:', error);
      Alert.alert('Error', 'Failed to select contact. Please try again.');
    }
  };

  /**
   * Toggle favorite status
   */
  const toggleFavorite = async (contact) => {
    try {
      if (contact.isFavorite) {
        await contactService.removeFromFavorites(contact.phoneNumber);
      } else {
        await contactService.addToFavorites(contact);
      }
      
      // Update local state
      setFavoriteContacts(contactService.getFavoriteContacts());
      setContacts(contactService.getAllContacts());
    } catch (error) {
      console.error('❌ Error toggling favorite:', error);
    }
  };

  /**
   * Render contact item
   */
  const renderContactItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.contactItem,
        item.isFavorite && styles.favoriteContactItem
      ]}
      onPress={() => handleSelectContact(item)}
    >
      <View style={styles.contactInfo}>
        <View style={styles.contactHeader}>
          <Text style={styles.contactName}>{item.name}</Text>
          {item.isFavorite && (
            <Ionicons name="star" size={16} color={Colors.primary.main} />
          )}
        </View>
        
        <View style={styles.phoneInfo}>
          <Text style={styles.phoneNumber}>
            {getDisplayFormat(item.phoneNumber, false)}
          </Text>
          {item.networkProvider && (
            <View style={[
              styles.networkBadge,
              { backgroundColor: item.networkProvider.color }
            ]}>
              <Text style={styles.networkText}>
                {item.networkProvider.provider}
              </Text>
            </View>
          )}
        </View>
      </View>
      
      <TouchableOpacity
        style={styles.favoriteButton}
        onPress={() => toggleFavorite(item)}
      >
        <Ionicons
          name={item.isFavorite ? "star" : "star-outline"}
          size={20}
          color={item.isFavorite ? Colors.primary.main : Colors.text.secondary}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  /**
   * Render permission denied state
   */
  const renderPermissionDenied = () => (
    <View style={styles.permissionContainer}>
      <Ionicons name="contacts-outline" size={64} color={Colors.text.secondary} />
      <Text style={styles.permissionTitle}>Contacts Permission Required</Text>
      <Text style={styles.permissionMessage}>
        To help you send money quickly, JiraniPay needs access to your contacts.
      </Text>
      <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
        <Text style={styles.permissionButtonText}>Grant Permission</Text>
      </TouchableOpacity>
    </View>
  );

  /**
   * Render loading state
   */
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={Colors.primary.main} />
      <Text style={styles.loadingText}>Loading contacts...</Text>
    </View>
  );

  /**
   * Render empty state
   */
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="people-outline" size={64} color={Colors.text.secondary} />
      <Text style={styles.emptyTitle}>No Contacts Found</Text>
      <Text style={styles.emptyMessage}>
        {searchQuery ? 'Try a different search term' : 'No contacts available'}
      </Text>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={Colors.text.secondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search contacts..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCapitalize="words"
            autoCorrect={false}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={Colors.text.secondary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Content */}
        <View style={styles.content}>
          {loading ? (
            renderLoading()
          ) : permissionDenied ? (
            renderPermissionDenied()
          ) : filteredContacts.length === 0 ? (
            renderEmpty()
          ) : (
            <FlatList
              data={filteredContacts}
              renderItem={renderContactItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContainer}
            />
          )}
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: Colors.text.primary,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 4,
    backgroundColor: Colors.background.secondary,
    borderRadius: 8,
  },
  favoriteContactItem: {
    borderLeftWidth: 3,
    borderLeftColor: Colors.primary.main,
  },
  contactInfo: {
    flex: 1,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text.primary,
    marginRight: 8,
  },
  phoneInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  phoneNumber: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginRight: 8,
  },
  networkBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  networkText: {
    fontSize: 10,
    fontWeight: '600',
    color: 'white',
  },
  favoriteButton: {
    padding: 8,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  permissionMessage: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.text.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyMessage: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
  },
});

export default ContactPicker;
