import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';

/**
 * RecentTransactions Component
 * Displays a list of recent transactions
 */
const RecentTransactions = ({ transactions = [], onViewAll, onTransactionPress }) => {
  const getTransactionIcon = (type, category) => {
    switch (type) {
      case 'bill_payment':
        switch (category) {
          case 'electricity':
            return 'flash-outline';
          case 'water':
            return 'water-outline';
          case 'internet':
            return 'wifi-outline';
          case 'tv':
            return 'tv-outline';
          default:
            return 'receipt-outline';
        }
      case 'airtime':
        return 'phone-portrait-outline';
      case 'transfer':
        return 'send-outline';
      case 'deposit':
        return 'add-circle-outline';
      case 'withdrawal':
        return 'remove-circle-outline';
      default:
        return 'card-outline';
    }
  };

  const getTransactionColor = (type, status) => {
    if (status === 'failed') return Colors.accent.coral;
    if (status === 'pending') return Colors.neutral.warmGray;
    
    switch (type) {
      case 'deposit':
        return Colors.secondary.savanna;
      case 'withdrawal':
      case 'transfer':
      case 'bill_payment':
      case 'airtime':
        return Colors.accent.coral;
      default:
        return Colors.primary.main;
    }
  };

  const formatAmount = (amount, currency = 'UGX', type) => {
    try {
      const currencyService = require('../services/currencyService').default;
      const numAmount = parseFloat(amount) || 0;
      const sign = ['deposit'].includes(type) ? '+' : '-';

      // Convert from UGX to user's preferred currency
      const formatted = currencyService.convertFromUGXAndFormat(numAmount, currency, {
        showSymbol: true,
        showFlag: false,
        compact: false
      });

      return `${sign}${formatted}`;
    } catch (error) {
      console.error('❌ Error formatting amount in RecentTransactions:', error);
      // Fallback formatting
      const numAmount = parseFloat(amount) || 0;
      const sign = ['deposit'].includes(type) ? '+' : '-';

      switch (currency) {
        case 'UGX':
          return `${sign}UGX ${numAmount.toLocaleString('en-UG', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
          })}`;
        case 'KES':
          return `${sign}KSh ${numAmount.toLocaleString('en-KE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
          })}`;
        default:
          return `${sign}${currency} ${numAmount.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })}`;
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: { color: Colors.secondary.savanna, text: 'Completed' },
      pending: { color: Colors.neutral.warmGray, text: 'Pending' },
      failed: { color: Colors.accent.coral, text: 'Failed' },
      cancelled: { color: Colors.neutral.warmGray, text: 'Cancelled' },
    };

    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <View style={[styles.statusBadge, { backgroundColor: config.color }]}>
        <Text style={styles.statusText}>{config.text}</Text>
      </View>
    );
  };

  const renderTransaction = ({ item }) => (
    <TouchableOpacity
      style={styles.transactionItem}
      onPress={() => onTransactionPress?.(item)}
      activeOpacity={0.7}
    >
      <View style={styles.transactionLeft}>
        <View style={[
          styles.iconContainer,
          { backgroundColor: getTransactionColor(item.transaction_type, item.status) + '20' }
        ]}>
          <Ionicons
            name={getTransactionIcon(item.transaction_type, item.category)}
            size={20}
            color={getTransactionColor(item.transaction_type, item.status)}
          />
        </View>
        <View style={styles.transactionDetails}>
          <Text style={styles.transactionTitle}>
            {item.description || `${item.transaction_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`}
          </Text>
          <Text style={styles.transactionSubtitle}>
            {formatDate(item.created_at)} • {item.reference_number}
          </Text>
        </View>
      </View>
      
      <View style={styles.transactionRight}>
        <Text style={[
          styles.transactionAmount,
          { color: getTransactionColor(item.transaction_type, item.status) }
        ]}>
          {formatAmount(item.amount, item.currency, item.transaction_type)}
        </Text>
        {getStatusBadge(item.status)}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="receipt-outline" size={48} color={Colors.neutral.warmGrayLight} />
      <Text style={styles.emptyTitle}>No transactions yet</Text>
      <Text style={styles.emptySubtitle}>
        Your recent transactions will appear here
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.sectionTitle}>Recent Transactions</Text>
        {transactions.length > 0 && (
          <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        )}
      </View>

      {transactions.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={transactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          style={styles.transactionsList}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginBottom: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  viewAllText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  transactionsList: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    paddingVertical: 8,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.warmGrayLight + '30',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  transactionSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 10,
    color: Colors.neutral.white,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  emptyState: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default RecentTransactions;
