/**
 * Production Data Service
 * Manages different data behavior between development and production modes
 */

import { isProductionMode } from '../config/environment';
import supabase from './supabaseClient';
import authService from './authService';

class ProductionDataService {
  constructor() {
    this.productionTransactions = [];
    this.productionWallets = {};
  }

  /**
   * Get wallet data based on environment mode
   */
  async getWalletData(userId) {
    if (isProductionMode()) {
      // PRODUCTION MODE: Real wallet data from database
      try {
        const { data, error } = await supabase
          .from('payment_accounts')
          .select('*')
          .eq('user_id', userId)
          .eq('account_type', 'wallet')
          .eq('is_primary', true)
          .single();

        if (error && error.code !== 'PGRST116') {
          throw error;
        }

        if (!data) {
          // Create new production wallet with 0 balance
          return this.createProductionWallet(userId);
        }

        return { success: true, data };
      } catch (error) {
        console.error('❌ Production wallet error:', error);
        return { success: false, error: error.message };
      }
    } else {
      // Fallback: Create basic wallet structure if no production data available
      const user = authService.getCurrentUser();
      const userPhone = user?.user_metadata?.phone || user?.phone;

      const basicWallet = {
        id: `wallet_${userId}`,
        user_id: userId,
        account_type: 'wallet',
        provider_name: 'JiraniPay',
        account_number: this.generateAccountNumber(userPhone),
        account_name: 'JiraniPay Wallet',
        currency: 'UGX',
        is_primary: true,
        is_active: true,
        balance: 0, // Start with zero balance
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metadata: {
          created_via: 'app',
          wallet_type: 'primary'
        }
      };

      return { success: true, data: basicWallet };
    }
  }

  /**
   * Create production wallet with 0 balance (PRODUCTION FIX)
   */
  async createProductionWallet(userId) {
    const user = authService.getCurrentUser();

    // Get phone number from multiple sources
    let userPhone = user?.user_metadata?.phone || user?.phone;

    // If no phone in auth metadata, check user profile
    if (!userPhone) {
      console.log('📱 No phone in auth metadata, checking user profile...');
      try {
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('phone_number')
          .eq('user_id', userId)
          .single();

        userPhone = profile?.phone_number;
        console.log('📱 Phone from profile:', userPhone);
      } catch (error) {
        console.log('⚠️ Could not fetch phone from profile:', error.message);
      }
    }

    console.log('📱 Final phone number for production wallet creation:', userPhone);

    // PRODUCTION FIX: Use the same safe database function as walletService
    try {
      console.log('🏦 Using database function to create production wallet safely...');
      const { data, error } = await supabase.rpc('create_user_wallet_safe', {
        p_user_id: userId,
        p_phone_number: userPhone
      });

      if (error) {
        console.error('❌ Database function error:', error);
        throw error;
      }

      console.log('🏦 Production database function response:', data);

      if (data.success) {
        console.log('✅ Production wallet created successfully via database function');
        return { success: true, data: data.data };
      } else {
        console.error('❌ Production database function returned error:', data);
        throw new Error(data.error || 'Failed to create production wallet');
      }
    } catch (error) {
      console.error('❌ Error creating production wallet:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user transactions (alias for getTransactionData for compatibility)
   */
  async getUserTransactions(userId, limit = 10) {
    return await this.getTransactionData(userId, limit);
  }

  /**
   * Get transaction data based on environment mode
   */
  async getTransactionData(userId, limit = 10) {
    console.log(`📊 ProductionDataService: Getting transaction data for user ${userId}, limit: ${limit}`);

    if (isProductionMode()) {
      // PRODUCTION MODE: Real transactions from database only
      console.log('🔒 Production mode: Fetching real transactions only');
      try {
        const { data, error } = await supabase
          .from('transactions')
          .select(`
            *,
            payment_accounts!payment_method_id(provider_name, account_name)
          `)
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(limit);

        if (error) throw error;

        console.log(`📊 Production: Found ${data?.length || 0} real transactions`);
        return { success: true, data: data || [] };
      } catch (error) {
        console.error('❌ Production transaction error:', error);
        return { success: false, error: error.message };
      }
    } else {
      // DEVELOPMENT MODE: Try real transactions first, no automatic mock generation
      console.log('🔧 Development mode: Trying real transactions first');
      try {
        const { data, error } = await supabase
          .from('transactions')
          .select(`
            *,
            payment_accounts!payment_method_id(provider_name, account_name)
          `)
          .eq('user_id', userId)
          .order('created_at', { ascending: false })
          .limit(limit);

        if (error) throw error;

        console.log(`📊 Development: Found ${data?.length || 0} real transactions from database`);
        return { success: true, data: data || [] };
      } catch (error) {
        console.log('⚠️ Development: Database error, returning empty array:', error.message);
        return { success: true, data: [] };
      }
    }
  }

  /**
   * Create sample transactions for empty state
   */
  createSampleTransactions(userId) {
    // Return empty array - no sample transactions in production mode
    return [];
  }

  /**
   * Generate account number
   */
  generateAccountNumber(userPhone = null) {
    if (userPhone) {
      return userPhone.replace('+256', '0').replace('+', '');
    }
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `JP${timestamp}${random}`;
  }

  /**
   * Get environment-specific message for empty states
   */
  getEmptyStateMessage(type) {
    if (isProductionMode()) {
      switch (type) {
        case 'transactions':
          return {
            title: 'No transactions yet',
            subtitle: 'Start using JiraniPay to see your transaction history',
            icon: 'receipt-outline'
          };
        case 'wallet':
          return {
            title: 'Wallet ready',
            subtitle: 'Top up your wallet to start making payments',
            icon: 'wallet-outline'
          };
        default:
          return {
            title: 'No data available',
            subtitle: 'Data will appear here as you use the app',
            icon: 'information-circle-outline'
          };
      }
    } else {
      switch (type) {
        case 'transactions':
          return {
            title: 'Development Mode',
            subtitle: 'Mock transactions are being generated for testing',
            icon: 'code-slash-outline'
          };
        case 'wallet':
          return {
            title: 'Development Wallet',
            subtitle: 'Test wallet with mock balance for development',
            icon: 'construct-outline'
          };
        default:
          return {
            title: 'Development Mode',
            subtitle: 'Mock data is being used for testing purposes',
            icon: 'code-slash-outline'
          };
      }
    }
  }

  /**
   * Check if feature is available in current mode
   */
  isFeatureAvailable(feature) {
    const productionFeatures = {
      realPayments: isProductionMode(),
      mockData: !isProductionMode(),
      biometricSecurity: isProductionMode(),
      developmentTools: !isProductionMode(),
      realSMS: isProductionMode(),
      testCredentials: !isProductionMode()
    };

    return productionFeatures[feature] || false;
  }

  /**
   * Get mode-specific configuration
   */
  getModeConfig() {
    return {
      mode: isProductionMode() ? 'production' : 'development',
      features: {
        realTransactions: isProductionMode(),
        mockData: !isProductionMode(),
        securityEnforcement: isProductionMode(),
        debugTools: !isProductionMode()
      },
      limits: {
        maxTransactionAmount: isProductionMode() ? 10000000 : 999999999,
        dailyLimit: isProductionMode() ? 50000000 : 999999999,
        requiresKYC: isProductionMode()
      }
    };
  }
}

export default new ProductionDataService();
