<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JiraniPay Admin Panel - Test Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #E67E22, #F39C12);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .auth-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .auth-section h2 {
            color: #E67E22;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #E67E22;
        }

        .btn {
            background: #E67E22;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin-right: 10px;
        }

        .btn:hover {
            background: #D35400;
        }

        .btn-secondary {
            background: #95A5A6;
        }

        .btn-secondary:hover {
            background: #7F8C8D;
        }

        .api-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .api-section h2 {
            color: #E67E22;
            margin-bottom: 20px;
        }

        .endpoint-group {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #E67E22;
        }

        .endpoint-group h3 {
            color: #2C3E50;
            margin-bottom: 15px;
        }

        .endpoint {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #ddd;
        }

        .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 12px;
            margin-right: 10px;
            min-width: 60px;
            text-align: center;
        }

        .method.get { background: #27AE60; color: white; }
        .method.post { background: #3498DB; color: white; }
        .method.put { background: #F39C12; color: white; }
        .method.delete { background: #E74C3C; color: white; }

        .endpoint-url {
            flex: 1;
            font-family: 'Courier New', monospace;
            color: #2C3E50;
        }

        .test-btn {
            padding: 6px 12px;
            font-size: 14px;
            background: #3498DB;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .test-btn:hover {
            background: #2980B9;
        }

        .response-area {
            margin-top: 20px;
            padding: 15px;
            background: #2C3E50;
            color: #ECF0F1;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            display: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #27AE60; }
        .status-error { background: #E74C3C; }
        .status-warning { background: #F39C12; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #E67E22;
            margin-bottom: 15px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 JiraniPay Admin Panel</h1>
            <p>Test Interface for Administrative Operations</p>
        </div>

        <!-- Authentication Section -->
        <div class="auth-section">
            <h2>🔐 Admin Authentication</h2>
            <div class="form-group">
                <label for="adminEmail">Admin Email:</label>
                <input type="email" id="adminEmail" placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="adminPassword">Password:</label>
                <input type="password" id="adminPassword" placeholder="Enter admin password" value="AdminPassword123!">
            </div>
            <div class="form-group">
                <label for="baseUrl">API Base URL:</label>
                <input type="text" id="baseUrl" placeholder="http://localhost:3000" value="http://localhost:3000">
            </div>
            <button class="btn" onclick="loginAdmin()">🔑 Login as Admin</button>
            <button class="btn btn-secondary" onclick="clearAuth()">🚪 Logout</button>
            <div id="authStatus" style="margin-top: 15px; font-weight: bold;"></div>
        </div>

        <!-- API Testing Section -->
        <div class="api-section" id="apiSection" style="display: none;">
            <h2>🛠️ Admin API Endpoints</h2>

            <!-- Dashboard Endpoints -->
            <div class="endpoint-group">
                <h3>📊 Dashboard</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/dashboard</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/dashboard')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/dashboard/quick-actions</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/dashboard/quick-actions')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/dashboard/notifications</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/dashboard/notifications')">Test</button>
                </div>
            </div>

            <!-- User Management Endpoints -->
            <div class="endpoint-group">
                <h3>👥 User Management</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/users</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/users?page=1&limit=10')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/users/stats/overview</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/users/stats/overview')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/users/{userId}</span>
                    <button class="test-btn" onclick="testUserDetails()">Test with Sample ID</button>
                </div>
            </div>

            <!-- Transaction Management Endpoints -->
            <div class="endpoint-group">
                <h3>💳 Transaction Management</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/transactions</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/transactions?page=1&limit=10')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/transactions/stats/overview</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/transactions/stats/overview')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/transactions/analytics/trends</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/transactions/analytics/trends?days=7')">Test</button>
                </div>
            </div>

            <!-- Monitoring Endpoints -->
            <div class="endpoint-group">
                <h3>📈 System Monitoring</h3>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/monitoring/dashboard</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/monitoring/dashboard')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/monitoring/health</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/monitoring/health')">Test</button>
                </div>
                <div class="endpoint">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/api/v1/admin/monitoring/real-time</span>
                    <button class="test-btn" onclick="testEndpoint('GET', '/api/v1/admin/monitoring/real-time')">Test</button>
                </div>
            </div>

            <!-- Response Area -->
            <div id="responseArea" class="response-area"></div>
        </div>

        <!-- Quick Stats Grid -->
        <div class="grid" id="statsGrid" style="display: none;">
            <div class="card">
                <h3>📊 Dashboard Overview</h3>
                <p>Test the main admin dashboard to see system statistics, recent transactions, and quick actions.</p>
            </div>
            <div class="card">
                <h3>👥 User Management</h3>
                <p>View and manage user accounts, including verification status, wallet balances, and account actions.</p>
            </div>
            <div class="card">
                <h3>💳 Transaction Monitoring</h3>
                <p>Monitor all transactions, view analytics, and manage transaction statuses with admin overrides.</p>
            </div>
            <div class="card">
                <h3>📈 System Health</h3>
                <p>Monitor system performance, view real-time metrics, and check service health status.</p>
            </div>
        </div>
    </div>

    <script>
        let authToken = null;
        let baseUrl = 'http://localhost:3000';

        function updateAuthStatus(message, isSuccess = false) {
            const statusEl = document.getElementById('authStatus');
            statusEl.innerHTML = `<span class="status-indicator ${isSuccess ? 'status-success' : 'status-error'}"></span>${message}`;
        }

        async function loginAdmin() {
            const email = document.getElementById('adminEmail').value;
            const password = document.getElementById('adminPassword').value;
            baseUrl = document.getElementById('baseUrl').value;

            try {
                // Note: This is a simplified login for testing
                // In a real implementation, you'd have a proper admin login endpoint
                updateAuthStatus('⏳ Attempting admin login...', false);
                
                // For testing purposes, we'll simulate a successful login
                // In production, replace this with actual admin authentication
                authToken = 'test-admin-token-' + Date.now();
                
                updateAuthStatus('✅ Admin login successful!', true);
                document.getElementById('apiSection').style.display = 'block';
                document.getElementById('statsGrid').style.display = 'grid';
                
                // Test the dashboard endpoint to verify connection
                await testEndpoint('GET', '/api/v1/admin/dashboard');
                
            } catch (error) {
                updateAuthStatus('❌ Login failed: ' + error.message, false);
            }
        }

        function clearAuth() {
            authToken = null;
            updateAuthStatus('🚪 Logged out', false);
            document.getElementById('apiSection').style.display = 'none';
            document.getElementById('statsGrid').style.display = 'none';
            document.getElementById('responseArea').style.display = 'none';
        }

        async function testEndpoint(method, endpoint, body = null) {
            if (!authToken) {
                updateAuthStatus('❌ Please login first', false);
                return;
            }

            const responseArea = document.getElementById('responseArea');
            responseArea.style.display = 'block';
            responseArea.textContent = '⏳ Loading...';

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                };

                if (body && (method === 'POST' || method === 'PUT')) {
                    options.body = JSON.stringify(body);
                }

                const response = await fetch(baseUrl + endpoint, options);
                const data = await response.json();

                const statusIcon = response.ok ? '✅' : '❌';
                const formattedResponse = `${statusIcon} ${method} ${endpoint}
Status: ${response.status} ${response.statusText}
Response:
${JSON.stringify(data, null, 2)}`;

                responseArea.textContent = formattedResponse;
            } catch (error) {
                responseArea.textContent = `❌ Error testing ${method} ${endpoint}:
${error.message}

Note: Make sure the JiraniPay backend server is running on ${baseUrl}
and that CORS is properly configured for this domain.`;
            }
        }

        async function testUserDetails() {
            // For testing, we'll use a sample UUID
            const sampleUserId = '123e4567-e89b-12d3-a456-************';
            await testEndpoint('GET', `/api/v1/admin/users/${sampleUserId}`);
        }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', function() {
            updateAuthStatus('🔐 Please login to access admin features', false);
        });
    </script>
</body>
</html>
