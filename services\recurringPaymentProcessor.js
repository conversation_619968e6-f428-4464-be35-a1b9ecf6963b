/**
 * Recurring Payment Processor
 * Automated background service for processing recurring payments
 * with retry mechanisms, failure handling, and status tracking
 */

import { supabase } from './supabaseClient';
import billPaymentService from './billPaymentService';
import recurringPaymentsService from './recurringPaymentsService';
import enhancedNotificationService from './enhancedNotificationService';
import { formatCurrency } from '../utils/currencyUtils';
import { formatDateTime } from '../utils/dateUtils';

class RecurringPaymentProcessor {
  constructor() {
    this.isProcessing = false;
    this.processingQueue = new Map();
    this.retryQueue = new Map();
    this.maxRetryAttempts = 3;
    this.retryDelayHours = 24;
    this.batchSize = 10;
    this.processingInterval = 60 * 60 * 1000; // 1 hour
    
    // Start the processor
    this.startProcessor();
  }

  /**
   * Start the recurring payment processor
   */
  startProcessor() {
    console.log('🔄 Starting recurring payment processor');
    
    // Process immediately on startup
    this.processRecurringPayments();
    
    // Set up interval processing
    setInterval(() => {
      this.processRecurringPayments();
    }, this.processingInterval);
  }

  /**
   * Process all due recurring payments
   */
  async processRecurringPayments() {
    if (this.isProcessing) {
      console.log('⏳ Recurring payment processing already in progress');
      return;
    }

    try {
      this.isProcessing = true;
      console.log('🔄 Processing recurring payments...');

      // Get due payments
      const duePayments = await this.getDuePayments();
      console.log(`📋 Found ${duePayments.length} due payments`);

      if (duePayments.length === 0) {
        return;
      }

      // Process payments in batches
      const batches = this.createBatches(duePayments, this.batchSize);
      
      for (const batch of batches) {
        await this.processBatch(batch);
        // Small delay between batches to prevent overwhelming the system
        await this.delay(1000);
      }

      console.log('✅ Recurring payment processing completed');
    } catch (error) {
      console.error('❌ Error in recurring payment processor:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get all due recurring payments
   */
  async getDuePayments() {
    try {
      const now = new Date().toISOString();
      
      const { data: duePayments, error } = await supabase
        .from('recurring_bill_payments')
        .select(`
          *,
          biller:billers(
            id,
            display_name,
            is_available,
            maintenance_mode
          )
        `)
        .eq('is_active', true)
        .eq('is_paused', false)
        .lte('next_payment_date', now)
        .order('next_payment_date', { ascending: true });

      if (error) {
        console.error('❌ Error fetching due payments:', error);
        return [];
      }

      // Filter out payments with unavailable billers
      return duePayments.filter(payment => 
        payment.biller && 
        payment.biller.is_available && 
        !payment.biller.maintenance_mode
      );
    } catch (error) {
      console.error('❌ Error getting due payments:', error);
      return [];
    }
  }

  /**
   * Process a batch of payments
   */
  async processBatch(payments) {
    const promises = payments.map(payment => this.processPayment(payment));
    await Promise.allSettled(promises);
  }

  /**
   * Process individual recurring payment
   */
  async processPayment(recurringPayment) {
    const paymentId = recurringPayment.id;
    
    try {
      console.log(`🔄 Processing recurring payment: ${paymentId}`);

      // Check if already processing
      if (this.processingQueue.has(paymentId)) {
        console.log(`⏳ Payment ${paymentId} already being processed`);
        return;
      }

      // Add to processing queue
      this.processingQueue.set(paymentId, {
        startTime: Date.now(),
        status: 'processing'
      });

      // Create payment execution record
      const executionId = await this.createPaymentExecution(recurringPayment);
      
      // Process the payment
      const paymentResult = await this.executePayment(recurringPayment, executionId);
      
      if (paymentResult.success) {
        await this.handlePaymentSuccess(recurringPayment, paymentResult, executionId);
      } else {
        await this.handlePaymentFailure(recurringPayment, paymentResult, executionId);
      }

    } catch (error) {
      console.error(`❌ Error processing payment ${paymentId}:`, error);
      await this.handlePaymentError(recurringPayment, error);
    } finally {
      // Remove from processing queue
      this.processingQueue.delete(paymentId);
    }
  }

  /**
   * Create payment execution record
   */
  async createPaymentExecution(recurringPayment) {
    try {
      const { data: execution, error } = await supabase
        .from('recurring_payment_executions')
        .insert({
          recurring_payment_id: recurringPayment.id,
          user_id: recurringPayment.user_id,
          biller_id: recurringPayment.biller_id,
          amount: recurringPayment.amount,
          currency: recurringPayment.currency,
          account_number: recurringPayment.account_number,
          payment_method: recurringPayment.payment_method,
          scheduled_date: recurringPayment.next_payment_date,
          status: 'pending',
          attempt_number: 1,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return execution.id;
    } catch (error) {
      console.error('❌ Error creating payment execution:', error);
      throw error;
    }
  }

  /**
   * Execute the actual payment
   */
  async executePayment(recurringPayment, executionId) {
    try {
      // Update execution status
      await this.updateExecutionStatus(executionId, 'processing');

      // Prepare payment data
      const paymentData = {
        billerId: recurringPayment.biller_id,
        accountNumber: recurringPayment.account_number,
        accountName: recurringPayment.account_name,
        amount: recurringPayment.amount,
        currency: recurringPayment.currency,
        paymentMethod: recurringPayment.payment_method || 'wallet',
        isRecurring: true,
        recurringPaymentId: recurringPayment.id,
        executionId
      };

      // Process payment through bill payment service
      const result = await billPaymentService.processBillPayment(
        recurringPayment.user_id,
        paymentData
      );

      return result;
    } catch (error) {
      console.error('❌ Error executing payment:', error);
      return {
        success: false,
        error: error.message || 'Payment execution failed'
      };
    }
  }

  /**
   * Handle successful payment
   */
  async handlePaymentSuccess(recurringPayment, paymentResult, executionId) {
    try {
      console.log(`✅ Payment successful for recurring payment: ${recurringPayment.id}`);

      // Update execution record
      await this.updateExecutionStatus(executionId, 'completed', {
        payment_id: paymentResult.payment.id,
        payment_reference: paymentResult.payment.reference,
        completed_at: new Date().toISOString()
      });

      // Calculate next payment date
      const nextPaymentDate = this.calculateNextPaymentDate(
        recurringPayment.next_payment_date,
        recurringPayment.frequency,
        recurringPayment.interval_value || 1
      );

      // Update recurring payment record
      await supabase
        .from('recurring_bill_payments')
        .update({
          last_payment_date: new Date().toISOString(),
          next_payment_date: nextPaymentDate.toISOString(),
          payment_count: (recurringPayment.payment_count || 0) + 1,
          failed_attempts: 0,
          updated_at: new Date().toISOString()
        })
        .eq('id', recurringPayment.id);

      // Send success notification
      await this.sendPaymentNotification(recurringPayment, 'success', {
        amount: recurringPayment.amount,
        reference: paymentResult.payment.reference,
        nextPaymentDate
      });

    } catch (error) {
      console.error('❌ Error handling payment success:', error);
    }
  }

  /**
   * Handle payment failure
   */
  async handlePaymentFailure(recurringPayment, paymentResult, executionId) {
    try {
      console.log(`❌ Payment failed for recurring payment: ${recurringPayment.id}`);

      const failedAttempts = (recurringPayment.failed_attempts || 0) + 1;
      const shouldRetry = failedAttempts < this.maxRetryAttempts;

      // Update execution record
      await this.updateExecutionStatus(executionId, 'failed', {
        error_message: paymentResult.error,
        failed_at: new Date().toISOString()
      });

      if (shouldRetry) {
        // Schedule retry
        const retryDate = new Date();
        retryDate.setHours(retryDate.getHours() + this.retryDelayHours);

        await supabase
          .from('recurring_bill_payments')
          .update({
            failed_attempts: failedAttempts,
            next_payment_date: retryDate.toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', recurringPayment.id);

        // Send retry notification
        await this.sendPaymentNotification(recurringPayment, 'retry', {
          attempt: failedAttempts,
          maxAttempts: this.maxRetryAttempts,
          retryDate,
          error: paymentResult.error
        });
      } else {
        // Max retries reached, pause the recurring payment
        await supabase
          .from('recurring_bill_payments')
          .update({
            is_paused: true,
            failed_attempts: failedAttempts,
            pause_reason: 'max_retries_reached',
            updated_at: new Date().toISOString()
          })
          .eq('id', recurringPayment.id);

        // Send failure notification
        await this.sendPaymentNotification(recurringPayment, 'failed', {
          attempts: failedAttempts,
          error: paymentResult.error
        });
      }

    } catch (error) {
      console.error('❌ Error handling payment failure:', error);
    }
  }

  /**
   * Handle payment processing error
   */
  async handlePaymentError(recurringPayment, error) {
    try {
      console.error(`❌ Payment error for recurring payment: ${recurringPayment.id}`, error);

      // Pause the payment to prevent further issues
      await supabase
        .from('recurring_bill_payments')
        .update({
          is_paused: true,
          pause_reason: 'processing_error',
          updated_at: new Date().toISOString()
        })
        .eq('id', recurringPayment.id);

      // Send error notification
      await this.sendPaymentNotification(recurringPayment, 'error', {
        error: error.message
      });

    } catch (updateError) {
      console.error('❌ Error handling payment error:', updateError);
    }
  }

  /**
   * Update execution status
   */
  async updateExecutionStatus(executionId, status, additionalData = {}) {
    try {
      await supabase
        .from('recurring_payment_executions')
        .update({
          status,
          ...additionalData,
          updated_at: new Date().toISOString()
        })
        .eq('id', executionId);
    } catch (error) {
      console.error('❌ Error updating execution status:', error);
    }
  }

  /**
   * Send payment notification
   */
  async sendPaymentNotification(recurringPayment, type, data) {
    try {
      const notifications = {
        success: {
          title: 'Payment Successful',
          content: `Your recurring payment of ${formatCurrency(data.amount)} was processed successfully. Next payment: ${formatDateTime(data.nextPaymentDate)}`,
          type: 'recurring_payment_success'
        },
        retry: {
          title: 'Payment Retry Scheduled',
          content: `Payment failed (attempt ${data.attempt}/${data.maxAttempts}). Retrying on ${formatDateTime(data.retryDate)}`,
          type: 'recurring_payment_retry'
        },
        failed: {
          title: 'Recurring Payment Failed',
          content: `Your recurring payment has been paused after ${data.attempts} failed attempts. Please check your payment method.`,
          type: 'recurring_payment_failed'
        },
        error: {
          title: 'Payment Processing Error',
          content: 'Your recurring payment has been paused due to a processing error. Please contact support.',
          type: 'recurring_payment_error'
        }
      };

      const notification = notifications[type];
      if (notification) {
        await enhancedNotificationService.sendNotification(recurringPayment.user_id, {
          ...notification,
          data: {
            recurringPaymentId: recurringPayment.id,
            ...data
          }
        });
      }
    } catch (error) {
      console.error('❌ Error sending payment notification:', error);
    }
  }

  /**
   * Calculate next payment date
   */
  calculateNextPaymentDate(currentDate, frequency, intervalValue = 1) {
    const date = new Date(currentDate);
    
    switch (frequency) {
      case 'daily':
        date.setDate(date.getDate() + intervalValue);
        break;
      case 'weekly':
        date.setDate(date.getDate() + (7 * intervalValue));
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + intervalValue);
        break;
      case 'quarterly':
        date.setMonth(date.getMonth() + (3 * intervalValue));
        break;
      case 'yearly':
        date.setFullYear(date.getFullYear() + intervalValue);
        break;
      default:
        date.setMonth(date.getMonth() + 1); // Default to monthly
    }
    
    return date;
  }

  /**
   * Create batches from array
   */
  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Delay utility
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get processing status
   */
  getProcessingStatus() {
    return {
      isProcessing: this.isProcessing,
      queueSize: this.processingQueue.size,
      retryQueueSize: this.retryQueue.size
    };
  }

  /**
   * Stop the processor
   */
  stop() {
    console.log('🛑 Stopping recurring payment processor');
    this.isProcessing = false;
    this.processingQueue.clear();
    this.retryQueue.clear();
  }
}

export default new RecurringPaymentProcessor();
