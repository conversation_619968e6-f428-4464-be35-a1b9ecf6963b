import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NavigationUtils from '../utils/navigationUtils';
import { Colors } from '../constants/Colors';

/**
 * Unified Back Button Component
 * Provides consistent back button behavior across all screens
 */
const UnifiedBackButton = ({ 
  navigation, 
  style, 
  iconColor = Colors.neutral.white,
  iconSize = 24,
  backgroundColor = 'rgba(255, 255, 255, 0.2)',
  onPress,
  disabled = false,
  ...props 
}) => {
  const handlePress = () => {
    if (disabled) return;
    
    // If custom onPress is provided, use it
    if (onPress) {
      onPress();
      return;
    }

    // Otherwise use unified navigation behavior
    NavigationUtils.safeGoBack(navigation);
  };

  return (
    <TouchableOpacity
      style={[
        styles.backButton,
        { backgroundColor },
        style,
        disabled && styles.disabled
      ]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
      {...props}
    >
      <Ionicons 
        name="arrow-back" 
        size={iconSize} 
        color={disabled ? Colors.neutral.warmGray : iconColor} 
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
});

export default UnifiedBackButton;
