import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet, Dimensions } from 'react-native';
import { Colors } from '../constants/Colors';

const { width } = Dimensions.get('window');

const SkeletonLoader = ({ 
  width: customWidth = width - 40, 
  height = 20, 
  borderRadius = 8,
  style = {},
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();

    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [Colors.neutral.creamDark, Colors.neutral.warmGrayLight],
  });

  return (
    <Animated.View
      style={[
        styles.skeleton,
        {
          width: customWidth,
          height,
          borderRadius,
          backgroundColor,
        },
        style,
      ]}
    />
  );
};

// Predefined skeleton components for common use cases
export const WalletCardSkeleton = () => (
  <View style={styles.walletCardSkeleton}>
    <View style={styles.walletHeaderSkeleton}>
      <View>
        <SkeletonLoader width={120} height={16} style={{ marginBottom: 8 }} />
        <SkeletonLoader width={80} height={12} />
      </View>
      <View style={styles.walletActionsSkeleton}>
        <SkeletonLoader width={32} height={32} borderRadius={16} />
        <SkeletonLoader width={32} height={32} borderRadius={16} />
      </View>
    </View>
    <SkeletonLoader width={200} height={36} style={{ marginVertical: 20 }} />
    <View style={styles.walletButtonsSkeleton}>
      <SkeletonLoader width={80} height={40} borderRadius={12} />
      <SkeletonLoader width={80} height={40} borderRadius={12} />
      <SkeletonLoader width={80} height={40} borderRadius={12} />
    </View>
  </View>
);

export const QuickActionsSkeleton = () => (
  <View style={styles.quickActionsSkeleton}>
    <SkeletonLoader width={150} height={20} style={{ marginBottom: 20 }} />
    <View style={styles.actionsGridSkeleton}>
      {Array.from({ length: 8 }).map((_, index) => (
        <View key={index} style={styles.actionCardSkeleton}>
          <SkeletonLoader width={48} height={48} borderRadius={24} style={{ marginBottom: 8 }} />
          <SkeletonLoader width={60} height={12} />
        </View>
      ))}
    </View>
  </View>
);

export const TransactionsSkeleton = () => (
  <View style={styles.transactionsSkeleton}>
    <View style={styles.sectionHeaderSkeleton}>
      <SkeletonLoader width={150} height={20} />
      <SkeletonLoader width={60} height={16} />
    </View>
    {Array.from({ length: 3 }).map((_, index) => (
      <View key={index} style={styles.transactionItemSkeleton}>
        <SkeletonLoader width={40} height={40} borderRadius={20} />
        <View style={styles.transactionDetailsSkeleton}>
          <SkeletonLoader width={120} height={16} style={{ marginBottom: 4 }} />
          <SkeletonLoader width={80} height={12} />
        </View>
        <SkeletonLoader width={80} height={16} />
      </View>
    ))}
  </View>
);

const styles = StyleSheet.create({
  skeleton: {
    backgroundColor: Colors.neutral.creamDark,
  },
  walletCardSkeleton: {
    backgroundColor: Colors.primary.main,
    borderRadius: 20,
    padding: 24,
    marginBottom: 32,
  },
  walletHeaderSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  walletActionsSkeleton: {
    flexDirection: 'row',
    gap: 12,
  },
  walletButtonsSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickActionsSkeleton: {
    marginBottom: 32,
  },
  actionsGridSkeleton: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  actionCardSkeleton: {
    width: (width - 72) / 4,
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  transactionsSkeleton: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 16,
  },
  sectionHeaderSkeleton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  transactionItemSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  transactionDetailsSkeleton: {
    flex: 1,
    marginLeft: 12,
  },
});

export default SkeletonLoader;
