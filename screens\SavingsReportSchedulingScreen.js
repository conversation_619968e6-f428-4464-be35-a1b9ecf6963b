/**
 * Savings Report Scheduling Screen
 * Screen for configuring automatic report generation and delivery
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  Switch,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { getCurrentUserId } from '../utils/userUtils';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsReportSchedulingScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { t } = useLanguage();
  const styles = createStyles(theme);

  const { currentPeriod, summary, accounts } = route.params || {};

  // State
  const [schedulingEnabled, setSchedulingEnabled] = useState(false);
  const [selectedFrequency, setSelectedFrequency] = useState('monthly');
  const [selectedDay, setSelectedDay] = useState(1);
  const [selectedTime, setSelectedTime] = useState('09:00');
  const [deliveryMethod, setDeliveryMethod] = useState('notification');
  const [reportFormat, setReportFormat] = useState('pdf');
  const [emailAddress, setEmailAddress] = useState('');
  const [showFrequencyModal, setShowFrequencyModal] = useState(false);
  const [showDayModal, setShowDayModal] = useState(false);
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const frequencies = [
    { key: 'weekly', label: 'Weekly', description: 'Every week on selected day' },
    { key: 'monthly', label: 'Monthly', description: 'Every month on selected date' },
    { key: 'quarterly', label: 'Quarterly', description: 'Every 3 months' }
  ];

  const deliveryMethods = [
    { key: 'notification', label: 'Push Notification', icon: 'notifications', description: 'In-app notification with report link' },
    { key: 'email', label: 'Email', icon: 'mail', description: 'Email with report attachment' },
    { key: 'both', label: 'Both', icon: 'duplicate', description: 'Notification and email' }
  ];

  const reportFormats = [
    { key: 'pdf', label: 'PDF Report', icon: 'document-text' },
    { key: 'excel', label: 'Excel/CSV', icon: 'grid' },
    { key: 'summary', label: 'Summary Only', icon: 'list' }
  ];

  const weekDays = [
    'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
  ];

  const monthDays = Array.from({ length: 28 }, (_, i) => i + 1);

  const timeSlots = [
    '06:00', '07:00', '08:00', '09:00', '10:00', '11:00', '12:00',
    '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00'
  ];

  useEffect(() => {
    loadSchedulingSettings();
    requestNotificationPermissions();
  }, []);

  const loadSchedulingSettings = async () => {
    try {
      const userId = await getCurrentUserId();
      const settings = await AsyncStorage.getItem(`savings_report_schedule_${userId}`);
      
      if (settings) {
        const parsed = JSON.parse(settings);
        setSchedulingEnabled(parsed.enabled || false);
        setSelectedFrequency(parsed.frequency || 'monthly');
        setSelectedDay(parsed.day || 1);
        setSelectedTime(parsed.time || '09:00');
        setDeliveryMethod(parsed.deliveryMethod || 'notification');
        setReportFormat(parsed.format || 'pdf');
        setEmailAddress(parsed.email || '');
      }
    } catch (error) {
      console.error('❌ Error loading scheduling settings:', error);
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Notification Permission',
          'Please enable notifications to receive scheduled reports',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('❌ Error requesting notification permissions:', error);
    }
  };

  const saveSchedulingSettings = async () => {
    try {
      setLoading(true);
      const userId = await getCurrentUserId();
      
      const settings = {
        enabled: schedulingEnabled,
        frequency: selectedFrequency,
        day: selectedDay,
        time: selectedTime,
        deliveryMethod: deliveryMethod,
        format: reportFormat,
        email: emailAddress,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await AsyncStorage.setItem(`savings_report_schedule_${userId}`, JSON.stringify(settings));

      if (schedulingEnabled) {
        await scheduleNotifications();
      } else {
        await cancelScheduledNotifications();
      }

      Alert.alert(
        'Settings Saved',
        schedulingEnabled 
          ? `Reports scheduled ${selectedFrequency} at ${selectedTime}`
          : 'Report scheduling disabled',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );

    } catch (error) {
      console.error('❌ Error saving scheduling settings:', error);
      Alert.alert('Error', 'Failed to save scheduling settings');
    } finally {
      setLoading(false);
    }
  };

  const scheduleNotifications = async () => {
    try {
      // Cancel existing notifications
      await Notifications.cancelAllScheduledNotificationsAsync();

      if (!schedulingEnabled) return;

      // Calculate next trigger date
      const now = new Date();
      const [hours, minutes] = selectedTime.split(':').map(Number);
      
      let triggerDate = new Date();
      triggerDate.setHours(hours, minutes, 0, 0);

      if (selectedFrequency === 'weekly') {
        // Schedule for selected day of week
        const dayDiff = selectedDay - now.getDay();
        triggerDate.setDate(now.getDate() + (dayDiff >= 0 ? dayDiff : dayDiff + 7));
      } else if (selectedFrequency === 'monthly') {
        // Schedule for selected day of month
        triggerDate.setDate(selectedDay);
        if (triggerDate <= now) {
          triggerDate.setMonth(triggerDate.getMonth() + 1);
        }
      } else if (selectedFrequency === 'quarterly') {
        // Schedule for quarterly
        triggerDate.setMonth(triggerDate.getMonth() + 3);
      }

      // Schedule the notification
      await Notifications.scheduleNotificationAsync({
        content: {
          title: 'JiraniPay Savings Report',
          body: 'Your scheduled savings report is ready!',
          data: { type: 'savings_report', format: reportFormat }
        },
        trigger: {
          date: triggerDate,
          repeats: true
        }
      });

    } catch (error) {
      console.error('❌ Error scheduling notifications:', error);
    }
  };

  const cancelScheduledNotifications = async () => {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('❌ Error canceling notifications:', error);
    }
  };

  const renderFrequencySelector = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Frequency</Text>
      <TouchableOpacity 
        style={styles.selectorButton}
        onPress={() => setShowFrequencyModal(true)}
      >
        <View style={styles.selectorContent}>
          <Text style={styles.selectorText}>
            {frequencies.find(f => f.key === selectedFrequency)?.label}
          </Text>
          <Text style={styles.selectorDescription}>
            {frequencies.find(f => f.key === selectedFrequency)?.description}
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
      </TouchableOpacity>

      <Modal
        visible={showFrequencyModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFrequencyModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFrequencyModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Frequency</Text>
            <TouchableOpacity onPress={() => setShowFrequencyModal(false)}>
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalContent}>
            {frequencies.map((frequency) => (
              <TouchableOpacity
                key={frequency.key}
                style={[
                  styles.modalOption,
                  selectedFrequency === frequency.key && styles.modalOptionSelected
                ]}
                onPress={() => {
                  setSelectedFrequency(frequency.key);
                  setShowFrequencyModal(false);
                }}
              >
                <View style={styles.modalOptionContent}>
                  <Text style={styles.modalOptionTitle}>{frequency.label}</Text>
                  <Text style={styles.modalOptionDescription}>{frequency.description}</Text>
                </View>
                {selectedFrequency === frequency.key && (
                  <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </View>
  );

  const renderDeliveryMethodSelector = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Delivery Method</Text>
      {deliveryMethods.map((method) => (
        <TouchableOpacity
          key={method.key}
          style={[
            styles.optionButton,
            deliveryMethod === method.key && styles.optionButtonSelected
          ]}
          onPress={() => setDeliveryMethod(method.key)}
        >
          <View style={styles.optionContent}>
            <Ionicons 
              name={method.icon} 
              size={20} 
              color={deliveryMethod === method.key ? theme.colors.primary : theme.colors.textSecondary} 
            />
            <View style={styles.optionText}>
              <Text style={[
                styles.optionTitle,
                deliveryMethod === method.key && styles.optionTitleSelected
              ]}>
                {method.label}
              </Text>
              <Text style={styles.optionDescription}>{method.description}</Text>
            </View>
          </View>
          {deliveryMethod === method.key && (
            <Ionicons name="checkmark-circle" size={20} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderReportFormatSelector = () => (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>Report Format</Text>
      <View style={styles.formatGrid}>
        {reportFormats.map((format) => (
          <TouchableOpacity
            key={format.key}
            style={[
              styles.formatButton,
              reportFormat === format.key && styles.formatButtonSelected
            ]}
            onPress={() => setReportFormat(format.key)}
          >
            <Ionicons 
              name={format.icon} 
              size={24} 
              color={reportFormat === format.key ? theme.colors.primary : theme.colors.textSecondary} 
            />
            <Text style={[
              styles.formatText,
              reportFormat === format.key && styles.formatTextSelected
            ]}>
              {format.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Schedule Reports</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enable/Disable Toggle */}
        <View style={styles.toggleContainer}>
          <View style={styles.toggleContent}>
            <Text style={styles.toggleTitle}>Automatic Reports</Text>
            <Text style={styles.toggleDescription}>
              Receive savings reports automatically at scheduled intervals
            </Text>
          </View>
          <Switch
            value={schedulingEnabled}
            onValueChange={setSchedulingEnabled}
            trackColor={{ false: theme.colors.border, true: theme.colors.primary + '40' }}
            thumbColor={schedulingEnabled ? theme.colors.primary : theme.colors.textSecondary}
          />
        </View>

        {schedulingEnabled && (
          <>
            {renderFrequencySelector()}
            {renderDeliveryMethodSelector()}
            {renderReportFormatSelector()}

            {/* Schedule Summary */}
            <View style={styles.summaryContainer}>
              <Text style={styles.summaryTitle}>Schedule Summary</Text>
              <View style={styles.summaryContent}>
                <Text style={styles.summaryText}>
                  📅 Frequency: {frequencies.find(f => f.key === selectedFrequency)?.label}
                </Text>
                <Text style={styles.summaryText}>
                  🕐 Time: {selectedTime}
                </Text>
                <Text style={styles.summaryText}>
                  📧 Delivery: {deliveryMethods.find(m => m.key === deliveryMethod)?.label}
                </Text>
                <Text style={styles.summaryText}>
                  📄 Format: {reportFormats.find(f => f.key === reportFormat)?.label}
                </Text>
              </View>
            </View>
          </>
        )}
      </ScrollView>

      {/* Save Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.saveButton, loading && styles.saveButtonDisabled]}
          onPress={saveSchedulingSettings}
          disabled={loading}
        >
          <Text style={styles.saveButtonText}>
            {loading ? 'Saving...' : 'Save Schedule'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  toggleContent: {
    flex: 1,
    marginRight: 16,
  },
  toggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  toggleDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  selectorButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectorContent: {
    flex: 1,
  },
  selectorText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  selectorDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  optionButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionButtonSelected: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '10',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    marginLeft: 12,
    flex: 1,
  },
  optionTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  optionTitleSelected: {
    color: theme.colors.primary,
  },
  optionDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  formatGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formatButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: 4,
  },
  formatButtonSelected: {
    borderWidth: 1,
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '10',
  },
  formatText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  formatTextSelected: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  summaryContainer: {
    backgroundColor: theme.colors.primary + '10',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  summaryContent: {
    gap: 8,
  },
  summaryText: {
    fontSize: 14,
    color: theme.colors.text,
  },
  buttonContainer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  saveButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  modalDoneText: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: theme.colors.surface,
  },
  modalOptionSelected: {
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  modalOptionContent: {
    flex: 1,
  },
  modalOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 2,
  },
  modalOptionDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
});

export default SavingsReportSchedulingScreen;
