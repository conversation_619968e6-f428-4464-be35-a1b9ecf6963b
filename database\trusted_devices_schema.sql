-- Trusted Devices Database Schema for JiraniPay
-- Run this in your Supabase SQL editor to create the necessary tables

-- User Devices Table (Enhanced)
CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL, -- Unique device identifier
    device_name TEXT NOT NULL,
    device_type TEXT NOT NULL, -- 'mobile', 'tablet', 'desktop'
    platform TEXT NOT NULL, -- 'ios', 'android', 'web'
    os_version TEXT,
    app_version TEXT,
    brand TEXT,
    model TEXT,
    is_device BOOLEAN DEFAULT TRUE,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_trusted BOOLEAN DEFAULT FALSE,
    push_token TEXT, -- For push notifications
    location_data JSONB, -- Last known location (if permitted)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_last_active ON public.user_devices(last_active);
CREATE INDEX IF NOT EXISTS idx_user_devices_is_trusted ON public.user_devices(is_trusted);

-- Audit Logs Table (Enhanced for device events)
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- 'device_registered', 'device_trusted', 'device_removed', etc.
    resource_type TEXT NOT NULL, -- 'device', 'security', etc.
    resource_id TEXT, -- device_id or other resource identifier
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for audit logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON public.audit_logs(resource_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- Row Level Security (RLS) Policies

-- Enable RLS on user_devices table
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own devices
CREATE POLICY "Users can view their own devices" ON public.user_devices
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own devices
CREATE POLICY "Users can insert their own devices" ON public.user_devices
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own devices
CREATE POLICY "Users can update their own devices" ON public.user_devices
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own devices
CREATE POLICY "Users can delete their own devices" ON public.user_devices
    FOR DELETE USING (auth.uid() = user_id);

-- Enable RLS on audit_logs table
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own audit logs
CREATE POLICY "Users can view their own audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: System can insert audit logs (for service accounts)
CREATE POLICY "System can insert audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on user_devices
CREATE TRIGGER update_user_devices_updated_at 
    BEFORE UPDATE ON public.user_devices 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up old devices (optional - for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_devices()
RETURNS void AS $$
BEGIN
    -- Remove devices that haven't been active for more than 90 days
    DELETE FROM public.user_devices 
    WHERE last_active < NOW() - INTERVAL '90 days'
    AND is_trusted = false;
    
    -- Log the cleanup action
    INSERT INTO public.audit_logs (user_id, action, resource_type, new_values)
    SELECT DISTINCT user_id, 'device_cleanup', 'device', 
           jsonb_build_object('cleaned_devices', 'inactive_devices_removed')
    FROM public.user_devices 
    WHERE last_active < NOW() - INTERVAL '90 days'
    AND is_trusted = false;
END;
$$ language 'plpgsql';

-- Grant necessary permissions
GRANT ALL ON public.user_devices TO authenticated;
GRANT ALL ON public.audit_logs TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Insert some sample data for testing (optional - remove in production)
-- This will only work if you have test users in your auth.users table
/*
INSERT INTO public.user_devices (user_id, device_id, device_name, device_type, platform, os_version, app_version, is_trusted)
VALUES 
    ((SELECT id FROM auth.users LIMIT 1), 'test-device-1', 'Test iPhone', 'mobile', 'ios', '16.1', '1.0.0', true),
    ((SELECT id FROM auth.users LIMIT 1), 'test-device-2', 'Test Android', 'mobile', 'android', '13.0', '1.0.0', false);
*/

-- Comments for documentation
COMMENT ON TABLE public.user_devices IS 'Stores information about user devices for trusted device management';
COMMENT ON COLUMN public.user_devices.device_id IS 'Unique identifier for the device (from expo-application)';
COMMENT ON COLUMN public.user_devices.is_trusted IS 'Whether this device is trusted and can skip additional verification';
COMMENT ON COLUMN public.user_devices.location_data IS 'JSON object containing location information if available';

COMMENT ON TABLE public.audit_logs IS 'Audit trail for security-related actions including device management';
COMMENT ON COLUMN public.audit_logs.action IS 'The action performed (e.g., device_registered, device_trusted)';
COMMENT ON COLUMN public.audit_logs.resource_type IS 'Type of resource affected (e.g., device, security)';
COMMENT ON COLUMN public.audit_logs.new_values IS 'JSON object containing the new values or metadata about the action';

-- Success message
SELECT 'Trusted Devices schema created successfully!' as message;
