/**
 * East African Cultural Color Palette for JiraniPay
 * Inspired by the vibrant heritage, landscapes, and values of East Africa
 */

export const Colors = {
  // Primary Brand Colors - Inspired by African Sunset
  primary: {
    // Main brand color - African Sunset Orange
    main: '#E67E22',
    light: '#F39C12', // Golden sunrise
    dark: '#D35400',  // Deep sunset
    contrast: '#FFFFFF', // White text on primary
  },

  // Secondary Colors - East African Heritage
  secondary: {
    // Earth Brown - Rich African soil and stability
    earth: '#8B4513',
    earthLight: '#A0522D',
    earthDark: '#654321',
    
    // Savanna Green - Growth and prosperity
    savanna: '#27AE60',
    savannaLight: '#2ECC71',
    savannaDark: '#1E8449',
    
    // Heritage Red - Strength and courage
    heritage: '#C0392B',
    heritageLight: '#E74C3C',
    heritageDark: '#922B21',
    
    // Lake Blue - Great lakes and trust
    lake: '#3498DB',
    lakeLight: '#5DADE2',
    lakeDark: '#2874A6',
  },

  // Accent Colors
  accent: {
    // Golden Yellow - Wealth and success
    gold: '#F39C12',
    goldLight: '#F7DC6F',
    goldDark: '#B7950B',
    
    // Warm Coral - Friendliness and approachability
    coral: '#FF6B6B',
    coralLight: '#FF8E8E',
    coralDark: '#E55555',
  },

  // Neutral Colors - Professional and clean
  neutral: {
    // Charcoal - Professional text
    charcoal: '#2C3E50',
    charcoalLight: '#34495E',
    charcoalDark: '#1B2631',

    // Warm Gray - Secondary elements
    warmGray: '#95A5A6',
    warmGrayLight: '#BDC3C7',
    warmGrayDark: '#7F8C8D',

    // Cream - Clean backgrounds
    cream: '#F8F9FA',
    creamDark: '#E9ECEF',

    // App Background - Warm off-white
    appBackground: '#fcf7f0',
    inputBackground: '#f5f0e8',

    // Pure colors
    white: '#FFFFFF',
    black: '#000000',
  },

  // Status Colors - Maintaining universal understanding
  status: {
    success: '#27AE60', // Savanna Green
    warning: '#F39C12', // Golden Yellow
    error: '#C0392B',   // Heritage Red
    info: '#3498DB',    // Lake Blue
  },

  // Gradient Combinations - For modern UI elements
  gradients: {
    sunset: ['#E67E22', '#F39C12'], // Orange to Gold
    earth: ['#8B4513', '#A0522D'],  // Earth tones
    prosperity: ['#27AE60', '#F39C12'], // Green to Gold
    heritage: ['#C0392B', '#E67E22'], // Red to Orange
    trust: ['#3498DB', '#27AE60'], // Blue to Green
  },

  // Shadow Colors - Subtle depth
  shadow: {
    light: 'rgba(44, 62, 80, 0.1)',   // Light charcoal
    medium: 'rgba(44, 62, 80, 0.2)',  // Medium charcoal
    dark: 'rgba(44, 62, 80, 0.3)',    // Dark charcoal
  },

  // Overlay Colors - For modals and overlays
  overlay: {
    light: 'rgba(0, 0, 0, 0.3)',
    medium: 'rgba(0, 0, 0, 0.5)',
    dark: 'rgba(0, 0, 0, 0.7)',
  },
};

// Legacy color mapping for easy migration
// Add nested structure for component compatibility
Colors.text = {
  primary: Colors.neutral.charcoal,
  secondary: Colors.neutral.warmGray,
  tertiary: Colors.neutral.warmGrayLight,
  inverse: Colors.neutral.white,
};

Colors.background = {
  primary: Colors.neutral.cream,
  secondary: Colors.neutral.white,
  tertiary: Colors.neutral.warmGrayLight,
  surface: Colors.neutral.white,
};

Colors.border = {
  light: Colors.neutral.warmGrayLight,
  medium: Colors.neutral.warmGray,
  dark: Colors.neutral.charcoal,
};

export const LegacyColors = {
  // Old purple primary
  oldPrimary: '#5B37B7',

  // Mapping old colors to new cultural colors
  primary: Colors.primary.main,
  primaryLight: Colors.primary.light,
  primaryDark: Colors.primary.dark,

  secondary: Colors.secondary.savanna,
  accent: Colors.accent.gold,

  text: Colors.neutral.charcoal,
  textSecondary: Colors.neutral.warmGray,
  background: Colors.neutral.cream,
  surface: Colors.neutral.white,

  success: Colors.status.success,
  warning: Colors.status.warning,
  error: Colors.status.error,
  info: Colors.status.info,
};

// Color utility functions
export const ColorUtils = {
  /**
   * Get color with opacity
   * @param {string} color - Hex color
   * @param {number} opacity - Opacity (0-1)
   * @returns {string} - RGBA color
   */
  withOpacity: (color, opacity) => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  },

  /**
   * Get appropriate text color for background
   * @param {string} backgroundColor - Background color
   * @returns {string} - Text color (white or charcoal)
   */
  getTextColor: (backgroundColor) => {
    // Simple luminance check - can be enhanced
    const lightColors = [
      Colors.neutral.cream,
      Colors.neutral.white,
      Colors.accent.goldLight,
      Colors.secondary.savannaLight,
    ];
    
    return lightColors.includes(backgroundColor) 
      ? Colors.neutral.charcoal 
      : Colors.neutral.white;
  },

  /**
   * Get gradient string for React Native
   * @param {Array} colors - Array of colors
   * @returns {Array} - Colors array for gradient
   */
  getGradient: (gradientName) => {
    return Colors.gradients[gradientName] || Colors.gradients.sunset;
  },
};

export default Colors;
