import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';

const BillSuccessScreen = ({ navigation, route }) => {
  const { category, provider, billDetails, amount, transaction, reference } = route.params;
  const { theme } = useTheme();
  const styles = createStyles(theme);
  
  // Animation values
  const scaleAnim = new Animated.Value(0);
  const fadeAnim = new Animated.Value(0);

  useEffect(() => {
    // Success haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Start animations
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleBackToHome = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Navigate back to main app (home tab)
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainApp' }],
    });
  };

  const handleMakeAnotherPayment = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    // Navigate back to bill payment main screen
    navigation.reset({
      index: 0,
      routes: [
        { name: 'MainApp' },
        { name: 'BillPayment' }
      ],
    });
  };

  const formatAmount = (amount) => {
    return amount.toLocaleString();
  };

  const formatDate = () => {
    return new Date().toLocaleDateString('en-UG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#27AE60"
        translucent
      />

      {/* Success Header */}
      <LinearGradient
        colors={['#27AE60', '#2ECC71']}
        style={styles.header}
      >
        <Animated.View 
          style={[
            styles.successIcon,
            { transform: [{ scale: scaleAnim }] }
          ]}
        >
          <Ionicons name="checkmark-circle" size={80} color={Colors.neutral.white} />
        </Animated.View>
        <Animated.View style={{ opacity: fadeAnim }}>
          <Text style={styles.successTitle}>BILL PAID SUCCESSFULLY</Text>
          <Text style={styles.successSubtitle}>Your payment has been processed</Text>
        </Animated.View>
      </LinearGradient>

      <View style={styles.content}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Receipt */}
          <Animated.View style={[styles.receipt, { opacity: fadeAnim }]}>
            {/* Receipt Header */}
            <View style={styles.receiptHeader}>
              <Text style={styles.receiptTitle}>Payment Receipt</Text>
              <Text style={styles.receiptDate}>{formatDate()}</Text>
            </View>

            {/* Transaction Details */}
            <View style={styles.receiptSection}>
              <Text style={styles.sectionTitle}>TRANSACTION DETAILS</Text>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Reference</Text>
                <Text style={styles.receiptValue}>{reference}</Text>
              </View>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Status</Text>
                <View style={styles.statusContainer}>
                  <Ionicons name="checkmark-circle" size={16} color="#27AE60" />
                  <Text style={styles.statusText}>Completed</Text>
                </View>
              </View>
            </View>

            {/* Account Details */}
            <View style={styles.receiptSection}>
              <Text style={styles.sectionTitle}>ACCOUNT DETAILS</Text>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Provider</Text>
                <Text style={styles.receiptValue}>{provider.name}</Text>
              </View>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Service</Text>
                <Text style={styles.receiptValue}>{category.title}</Text>
              </View>
              {provider?.type === 'mobile' && billDetails?.serviceType && (
                <View style={styles.receiptRow}>
                  <Text style={styles.receiptLabel}>Type</Text>
                  <Text style={styles.receiptValue}>
                    {billDetails.serviceType === 'airtime' ? 'Airtime Top-up' : 'Data Bundles'}
                  </Text>
                </View>
              )}
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Account</Text>
                <Text style={styles.receiptValue}>{billDetails.accountNumber}</Text>
              </View>
              {billDetails.customerName && (
                <View style={styles.receiptRow}>
                  <Text style={styles.receiptLabel}>Customer</Text>
                  <Text style={styles.receiptValue}>{billDetails.customerName}</Text>
                </View>
              )}
            </View>

            {/* Payment Summary */}
            <View style={styles.receiptSection}>
              <Text style={styles.sectionTitle}>PAYMENT SUMMARY</Text>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Amount</Text>
                <Text style={styles.receiptValue}>UGX {formatAmount(amount)}</Text>
              </View>
              <View style={styles.receiptRow}>
                <Text style={styles.receiptLabel}>Service Charge</Text>
                <Text style={styles.receiptValue}>UGX 0</Text>
              </View>
              <View style={[styles.receiptRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total Paid</Text>
                <Text style={styles.totalValue}>UGX {formatAmount(amount)}</Text>
              </View>
            </View>

            {/* JiraniPay Branding */}
            <View style={styles.brandingSection}>
              <Text style={styles.brandingText}>Powered by JiraniPay</Text>
              <Text style={styles.brandingSubtext}>Connecting East Africa</Text>
            </View>
          </Animated.View>
        </ScrollView>

        {/* Action Buttons */}
        <Animated.View style={[styles.buttonContainer, { opacity: fadeAnim }]}>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleMakeAnotherPayment}
            activeOpacity={0.8}
          >
            <Text style={styles.secondaryButtonText}>Make Another Payment</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleBackToHome}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#E67E22', '#D35400']}
              style={styles.primaryButtonGradient}
            >
              <Text style={styles.primaryButtonText}>Back to Home</Text>
              <Ionicons name="home" size={20} color={Colors.neutral.white} />
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 40,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    textAlign: 'center',
    marginBottom: 8,
  },
  successSubtitle: {
    fontSize: 16,
    color: Colors.neutral.white,
    textAlign: 'center',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    backgroundColor: theme.colors.background,
    marginTop: -20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 140,
  },
  receipt: {
    margin: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    padding: 20,
    elevation: 4,
    shadowColor: theme.colors.shadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  receiptHeader: {
    alignItems: 'center',
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    marginBottom: 20,
  },
  receiptTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  receiptDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  receiptSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
    letterSpacing: 0.5,
    marginBottom: 12,
  },
  receiptRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  totalRow: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    marginBottom: 0,
  },
  receiptLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    flex: 1,
  },
  receiptValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'right',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#27AE60',
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
  },
  totalValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27AE60',
    flex: 1,
    textAlign: 'right',
  },
  brandingSection: {
    alignItems: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  brandingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#E67E22',
    marginBottom: 2,
  },
  brandingSubtext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: 40,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    gap: 12,
  },
  secondaryButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E67E22',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E67E22',
  },
  primaryButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#E67E22',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  primaryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
    gap: 12,
  },
  primaryButtonText: {
    fontSize: 17,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
});

export default BillSuccessScreen;
