-- Debug and Fix Trusted Devices Database Issues
-- Run this in Supabase SQL editor to diagnose and fix the problems

-- 1. Check if tables exist
SELECT 
    schemaname,
    tablename,
    tableowner,
    hasindexes,
    hasrules,
    hastriggers
FROM pg_tables 
WHERE tablename IN ('user_devices', 'audit_logs')
ORDER BY tablename;

-- 2. Check if the public schema exists and has proper permissions
SELECT schema_name, schema_owner 
FROM information_schema.schemata 
WHERE schema_name = 'public';

-- 3. Check current user permissions
SELECT current_user, session_user;

-- 4. Check if uuid extension is enabled
SELECT * FROM pg_extension WHERE extname = 'uuid-ossp';

-- 5. If tables don't exist, create them with proper error handling
DO $$
BEGIN
    -- Enable UUID extension if not already enabled
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'uuid-ossp') THEN
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        RAISE NOTICE 'UUID extension enabled';
    END IF;

    -- Create user_devices table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'user_devices' AND schemaname = 'public') THEN
        CREATE TABLE public.user_devices (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            device_id TEXT NOT NULL,
            device_name TEXT NOT NULL,
            device_type TEXT NOT NULL,
            platform TEXT NOT NULL,
            os_version TEXT,
            app_version TEXT,
            brand TEXT,
            model TEXT,
            is_device BOOLEAN DEFAULT TRUE,
            last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_trusted BOOLEAN DEFAULT FALSE,
            push_token TEXT,
            location_data JSONB,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(user_id, device_id)
        );
        
        RAISE NOTICE 'user_devices table created successfully';
    ELSE
        RAISE NOTICE 'user_devices table already exists';
    END IF;

    -- Create audit_logs table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'audit_logs' AND schemaname = 'public') THEN
        CREATE TABLE public.audit_logs (
            id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            action TEXT NOT NULL,
            resource_type TEXT NOT NULL,
            resource_id TEXT,
            old_values JSONB,
            new_values JSONB,
            ip_address INET,
            user_agent TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE 'audit_logs table created successfully';
    ELSE
        RAISE NOTICE 'audit_logs table already exists';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating tables: %', SQLERRM;
END $$;

-- 6. Create indexes for performance
DO $$
BEGIN
    -- Indexes for user_devices
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_devices_user_id') THEN
        CREATE INDEX idx_user_devices_user_id ON public.user_devices(user_id);
        RAISE NOTICE 'Index idx_user_devices_user_id created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_devices_device_id') THEN
        CREATE INDEX idx_user_devices_device_id ON public.user_devices(device_id);
        RAISE NOTICE 'Index idx_user_devices_device_id created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_devices_last_active') THEN
        CREATE INDEX idx_user_devices_last_active ON public.user_devices(last_active);
        RAISE NOTICE 'Index idx_user_devices_last_active created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_user_devices_is_trusted') THEN
        CREATE INDEX idx_user_devices_is_trusted ON public.user_devices(is_trusted);
        RAISE NOTICE 'Index idx_user_devices_is_trusted created';
    END IF;

    -- Indexes for audit_logs
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_audit_logs_user_id') THEN
        CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
        RAISE NOTICE 'Index idx_audit_logs_user_id created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_audit_logs_action') THEN
        CREATE INDEX idx_audit_logs_action ON public.audit_logs(action);
        RAISE NOTICE 'Index idx_audit_logs_action created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_audit_logs_resource_type') THEN
        CREATE INDEX idx_audit_logs_resource_type ON public.audit_logs(resource_type);
        RAISE NOTICE 'Index idx_audit_logs_resource_type created';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_audit_logs_created_at') THEN
        CREATE INDEX idx_audit_logs_created_at ON public.audit_logs(created_at);
        RAISE NOTICE 'Index idx_audit_logs_created_at created';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating indexes: %', SQLERRM;
END $$;

-- 7. Set up Row Level Security (RLS)
DO $$
BEGIN
    -- Enable RLS on user_devices
    ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE 'RLS enabled on user_devices';

    -- Enable RLS on audit_logs
    ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
    RAISE NOTICE 'RLS enabled on audit_logs';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error enabling RLS: %', SQLERRM;
END $$;

-- 8. Create RLS policies
DO $$
BEGIN
    -- Drop existing policies if they exist to avoid conflicts
    DROP POLICY IF EXISTS "Users can view their own devices" ON public.user_devices;
    DROP POLICY IF EXISTS "Users can insert their own devices" ON public.user_devices;
    DROP POLICY IF EXISTS "Users can update their own devices" ON public.user_devices;
    DROP POLICY IF EXISTS "Users can delete their own devices" ON public.user_devices;
    DROP POLICY IF EXISTS "Users can view their own audit logs" ON public.audit_logs;
    DROP POLICY IF EXISTS "System can insert audit logs" ON public.audit_logs;

    -- Create policies for user_devices
    CREATE POLICY "Users can view their own devices" ON public.user_devices
        FOR SELECT USING (auth.uid() = user_id);

    CREATE POLICY "Users can insert their own devices" ON public.user_devices
        FOR INSERT WITH CHECK (auth.uid() = user_id);

    CREATE POLICY "Users can update their own devices" ON public.user_devices
        FOR UPDATE USING (auth.uid() = user_id);

    CREATE POLICY "Users can delete their own devices" ON public.user_devices
        FOR DELETE USING (auth.uid() = user_id);

    -- Create policies for audit_logs
    CREATE POLICY "Users can view their own audit logs" ON public.audit_logs
        FOR SELECT USING (auth.uid() = user_id);

    CREATE POLICY "System can insert audit logs" ON public.audit_logs
        FOR INSERT WITH CHECK (true);

    RAISE NOTICE 'RLS policies created successfully';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating RLS policies: %', SQLERRM;
END $$;

-- 9. Grant permissions
DO $$
BEGIN
    -- Grant permissions to authenticated users
    GRANT ALL ON public.user_devices TO authenticated;
    GRANT ALL ON public.audit_logs TO authenticated;
    GRANT USAGE ON SCHEMA public TO authenticated;
    
    RAISE NOTICE 'Permissions granted successfully';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error granting permissions: %', SQLERRM;
END $$;

-- 10. Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 11. Create trigger for user_devices
DO $$
BEGIN
    DROP TRIGGER IF EXISTS update_user_devices_updated_at ON public.user_devices;
    
    CREATE TRIGGER update_user_devices_updated_at 
        BEFORE UPDATE ON public.user_devices 
        FOR EACH ROW 
        EXECUTE FUNCTION update_updated_at_column();
    
    RAISE NOTICE 'Trigger created successfully';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error creating trigger: %', SQLERRM;
END $$;

-- 12. Final verification
SELECT 
    'user_devices' as table_name,
    COUNT(*) as row_count,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'user_devices') as index_count
FROM public.user_devices
UNION ALL
SELECT 
    'audit_logs' as table_name,
    COUNT(*) as row_count,
    (SELECT COUNT(*) FROM pg_indexes WHERE tablename = 'audit_logs') as index_count
FROM public.audit_logs;

-- Success message
SELECT 'Trusted Devices database setup completed successfully!' as status;
