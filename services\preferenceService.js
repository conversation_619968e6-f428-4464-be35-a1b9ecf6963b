import AsyncStorage from '@react-native-async-storage/async-storage';
import databaseService from './databaseService';
import enhancedNetworkService from './enhancedNetworkService';
import { notificationService } from './notificationService';

/**
 * Preference Management Service
 * Centralized service for managing user preferences with AsyncStorage persistence
 * and database synchronization
 */
class PreferenceService {
  constructor() {
    this.preferences = {};
    this.isInitialized = false;
    this.listeners = new Set();
    
    // Default preferences
    this.defaultPreferences = {
      // Theme preferences
      dark_mode_enabled: false,
      theme_preference: 'light',
      
      // Notification preferences
      notifications_enabled: true,
      push_notifications: true,
      sms_notifications: true,
      email_notifications: true,
      transaction_notifications: true,
      security_notifications: true,
      
      // Language and region
      preferred_language: 'en',
      preferred_currency: 'UGX',
      
      // Security preferences
      biometric_enabled: false,
      auto_logout_minutes: 30,
      security_pin_enabled: false,
      
      // Transaction preferences
      transaction_limit_daily: 1000000,
      transaction_limit_monthly: 10000000,
      
      // App preferences
      haptic_feedback: true,
      sound_enabled: true,
      
      // Privacy preferences
      analytics_enabled: true,
      crash_reporting: true,
    };
  }

  /**
   * Initialize preference service
   */
  async initialize(userId = null) {
    try {
      console.log('🔧 Initializing preference service...');
      
      // Load preferences from AsyncStorage first (faster)
      await this.loadFromStorage();
      
      // If user is logged in, try to sync with database
      if (userId) {
        await this.syncWithDatabase(userId);
      }
      
      this.isInitialized = true;
      console.log('✅ Preference service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing preference service:', error);
      // Use defaults if initialization fails
      this.preferences = { ...this.defaultPreferences };
      this.isInitialized = true;
      return { success: false, error: error.message };
    }
  }

  /**
   * Load preferences from AsyncStorage
   */
  async loadFromStorage() {
    try {
      const stored = await AsyncStorage.getItem('user_preferences');
      if (stored) {
        const parsedPreferences = JSON.parse(stored);
        this.preferences = { ...this.defaultPreferences, ...parsedPreferences };
      } else {
        this.preferences = { ...this.defaultPreferences };
      }
    } catch (error) {
      console.error('❌ Error loading preferences from storage:', error);
      this.preferences = { ...this.defaultPreferences };
    }
  }

  /**
   * Save preferences to AsyncStorage
   */
  async saveToStorage() {
    try {
      await AsyncStorage.setItem('user_preferences', JSON.stringify(this.preferences));
      return { success: true };
    } catch (error) {
      console.error('❌ Error saving preferences to storage:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Sync preferences with database using enhanced network service
   */
  async syncWithDatabase(userId) {
    try {
      console.log('🔄 Syncing preferences with database for user:', userId);

      // Try to get preferences from database using enhanced network service
      const result = await enhancedNetworkService.getUserPreferences(userId);

      if (result.success && result.data) {
        console.log('✅ Preferences loaded from database');
        // Merge database preferences with local ones
        this.preferences = { ...this.defaultPreferences, ...result.data };
        await this.saveToStorage();
      } else if (result.needsCreation) {
        console.log('⚠️ No preferences found, creating defaults');
        // Save current preferences to database
        await this.saveToDatabase(userId);
      } else {
        console.log('⚠️ Database sync failed, using local preferences:', result.error);
        // Fallback to database service
        const fallbackResult = await databaseService.getUserPreferences(userId);
        if (fallbackResult.success && fallbackResult.data) {
          this.preferences = { ...this.defaultPreferences, ...fallbackResult.data };
          await this.saveToStorage();
        }
      }
    } catch (error) {
      console.log('⚠️ Database sync failed, using local preferences:', error.message);
    }
  }

  /**
   * Save preferences to database using enhanced network service
   */
  async saveToDatabase(userId) {
    try {
      if (!userId) return { success: false, error: 'No user ID provided' };

      console.log('💾 Saving preferences to database for user:', userId);

      // Try enhanced network service first
      const result = await enhancedNetworkService.updateUserPreferences(userId, this.preferences);

      if (result.success) {
        console.log('✅ Preferences saved to database successfully');
        return { success: true };
      } else {
        console.log('⚠️ Enhanced network service failed, trying fallback:', result.error);
        // Fallback to database service
        await databaseService.updateUserPreferences(userId, this.preferences);
        return { success: true };
      }
    } catch (error) {
      console.error('❌ Error saving preferences to database:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get a specific preference value
   */
  get(key) {
    return this.preferences[key] ?? this.defaultPreferences[key];
  }

  /**
   * Get all preferences
   */
  getAll() {
    return { ...this.preferences };
  }

  /**
   * Set a specific preference value
   */
  async set(key, value, userId = null) {
    try {
      const oldValue = this.preferences[key];
      this.preferences[key] = value;
      
      // Save to storage immediately
      await this.saveToStorage();
      
      // Save to database if user is logged in
      if (userId) {
        await this.saveToDatabase(userId);
      }
      
      // Handle special preference changes
      await this.handlePreferenceChange(key, value, oldValue);
      
      // Notify listeners
      this.notifyListeners(key, value, oldValue);
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting preference:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Set multiple preferences at once
   */
  async setMultiple(preferences, userId = null) {
    try {
      const changes = [];
      
      for (const [key, value] of Object.entries(preferences)) {
        const oldValue = this.preferences[key];
        this.preferences[key] = value;
        changes.push({ key, value, oldValue });
      }
      
      // Save to storage
      await this.saveToStorage();
      
      // Save to database if user is logged in
      if (userId) {
        await this.saveToDatabase(userId);
      }
      
      // Handle special preference changes
      for (const { key, value, oldValue } of changes) {
        await this.handlePreferenceChange(key, value, oldValue);
        this.notifyListeners(key, value, oldValue);
      }
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error setting multiple preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle special actions when certain preferences change
   */
  async handlePreferenceChange(key, newValue, oldValue) {
    try {
      switch (key) {
        case 'notifications_enabled':
        case 'push_notifications':
          // Update notification service
          await notificationService.setNotificationEnabled(newValue);
          break;
          
        case 'preferred_language':
          // Language change will be handled by LanguageContext
          console.log('🌐 Language preference changed to:', newValue);
          break;
          
        case 'dark_mode_enabled':
        case 'theme_preference':
          // Theme change will be handled by ThemeContext
          console.log('🎨 Theme preference changed to:', newValue);
          break;
          
        case 'preferred_currency':
          console.log('💱 Currency preference changed to:', newValue);
          break;
      }
    } catch (error) {
      console.error('❌ Error handling preference change:', error);
    }
  }

  /**
   * Add a listener for preference changes
   */
  addListener(callback) {
    this.listeners.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(callback);
    };
  }

  /**
   * Notify all listeners of preference changes
   */
  notifyListeners(key, newValue, oldValue) {
    this.listeners.forEach(callback => {
      try {
        callback(key, newValue, oldValue);
      } catch (error) {
        console.error('❌ Error in preference listener:', error);
      }
    });
  }

  /**
   * Reset preferences to defaults
   */
  async reset(userId = null) {
    try {
      this.preferences = { ...this.defaultPreferences };
      await this.saveToStorage();
      
      if (userId) {
        await this.saveToDatabase(userId);
      }
      
      // Notify listeners
      Object.keys(this.defaultPreferences).forEach(key => {
        this.notifyListeners(key, this.defaultPreferences[key], null);
      });
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error resetting preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if service is initialized
   */
  isReady() {
    return this.isInitialized;
  }
}

// Create and export singleton instance
export const preferenceService = new PreferenceService();
export default preferenceService;
