import React from 'react';
import { Text } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';

/**
 * TranslatedText Component
 * Automatically translates text using the current language context
 * Provides a simple way to add translations throughout the app
 * 
 * @param {Object} props - Component props
 * @param {string} props.translationKey - Translation key (e.g., 'auth.login')
 * @param {Object} props.params - Parameters for string interpolation
 * @param {Object} props.style - Text styles
 * @param {string} props.fallback - Fallback text if translation not found
 * @param {Object} props.textProps - Additional Text component props
 */
const TranslatedText = ({ 
  translationKey, 
  params = {}, 
  style = {}, 
  fallback = '',
  children,
  ...textProps 
}) => {
  const { t } = useLanguage();
  
  // If children are provided, use them as fallback
  const text = translationKey ? t(translationKey, params) : (children || fallback);
  
  return (
    <Text style={style} {...textProps}>
      {text}
    </Text>
  );
};

export default TranslatedText;
