-- =====================================================
-- Test Database Function for Wallet Creation
-- =====================================================
-- Run these queries in Supabase SQL Editor to test the wallet creation function

-- Test 1: Check if the function exists
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name = 'create_user_wallet_safe';

-- Test 2: Check RLS policies are properly set
SELECT 
    schemaname, 
    tablename, 
    policyname, 
    permissive, 
    roles, 
    cmd
FROM pg_policies 
WHERE tablename = 'payment_accounts'
ORDER BY policyname;

-- Test 3: Test function with sample data (replace with actual user ID)
-- IMPORTANT: Replace 'your-actual-user-id-here' with a real user ID from auth.users
SELECT * FROM create_user_wallet_safe(
    'your-actual-user-id-here'::UUID,
    '+************'
);

-- Test 4: Check if wallet was created
-- IMPORTANT: Replace 'your-actual-user-id-here' with the same user ID
SELECT 
    id,
    user_id,
    account_type,
    account_number,
    account_name,
    currency,
    balance,
    is_primary,
    is_active,
    created_at
FROM public.payment_accounts 
WHERE user_id = 'your-actual-user-id-here'::UUID
AND account_type = 'wallet';

-- Test 5: Test function with existing wallet (should return existing)
-- IMPORTANT: Replace 'your-actual-user-id-here' with the same user ID
SELECT * FROM create_user_wallet_safe(
    'your-actual-user-id-here'::UUID,
    '+************'
);

-- Test 6: Test function without phone number (should create JP account number)
-- IMPORTANT: Replace 'another-user-id-here' with a different user ID
SELECT * FROM create_user_wallet_safe(
    'another-user-id-here'::UUID,
    NULL
);

-- Test 7: Check all wallets created
SELECT 
    user_id,
    account_number,
    account_name,
    balance,
    created_at
FROM public.payment_accounts 
WHERE account_type = 'wallet'
ORDER BY created_at DESC;
