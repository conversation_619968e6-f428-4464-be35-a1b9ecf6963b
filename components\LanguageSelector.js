import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SUPPORTED_LANGUAGES, getCurrentLanguage, changeLanguage, getLanguagesForCountry, t } from '../utils/i18n';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Language Selector Component
 * Provides dropdown functionality for selecting app language
 * Shows ALL 8 East African languages for professional multilingual experience
 *
 * @param {Object} props - Component props
 * @param {string} props.selectedCountryCode - Currently selected country code (optional, for prioritization)
 * @param {Function} props.onLanguageChange - Callback when language is changed
 * @param {Object} props.style - Additional styles for the container
 * @param {boolean} props.compact - Whether to show compact version (for header)
 * @param {boolean} props.showAllLanguages - Whether to show all languages (default: true)
 */
const LanguageSelector = ({
  selectedCountryCode = 'UG',
  onLanguageChange,
  style = {},
  compact = false,
  showAllLanguages = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentLang, setCurrentLang] = useState(getCurrentLanguage());
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  // Get all available languages for professional multilingual experience
  // Prioritize country-specific languages but show all 8 East African languages
  const getAllLanguagesSorted = () => {
    const allLanguages = Object.values(SUPPORTED_LANGUAGES);

    if (!showAllLanguages) {
      // Legacy behavior: filter by country
      return getLanguagesForCountry(selectedCountryCode);
    }

    // Professional behavior: show all languages, prioritizing country-specific ones
    const countryLanguages = getLanguagesForCountry(selectedCountryCode);
    const otherLanguages = allLanguages.filter(lang =>
      !countryLanguages.some(countryLang => countryLang.code === lang.code)
    );

    // Return country languages first, then others, sorted by priority
    return [
      ...countryLanguages.sort((a, b) => (a.priority || 99) - (b.priority || 99)),
      ...otherLanguages.sort((a, b) => (a.priority || 99) - (b.priority || 99))
    ];
  };

  const availableLanguages = getAllLanguagesSorted();
  const currentLanguageConfig = SUPPORTED_LANGUAGES[currentLang];

  /**
   * Opens the language selector modal with animation
   */
  const openSelector = () => {
    setIsVisible(true);
    
    // Animate modal slide up
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Closes the language selector modal with animation
   */
  const closeSelector = () => {
    Animated.timing(slideAnim, {
      toValue: screenHeight,
      duration: 250,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  /**
   * Handles language selection
   * @param {Object} language - Selected language object
   */
  const handleLanguageSelect = async (language) => {
    setCurrentLang(language.code);
    await changeLanguage(language.code);
    onLanguageChange && onLanguageChange(language.code, language);
    closeSelector();
  };

  /**
   * Renders individual language item in the list
   * @param {Object} item - Language object
   * @returns {JSX.Element} - Language item component
   */
  const renderLanguageItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        currentLang === item.code && styles.selectedLanguageItem
      ]}
      onPress={() => handleLanguageSelect(item)}
    >
      <View style={styles.languageItemContent}>
        <Text style={styles.languageFlag}>{item.flag}</Text>
        <View style={styles.languageInfo}>
          <Text style={styles.languageName}>{item.nativeName}</Text>
          <Text style={styles.languageNameSecondary}>{item.name}</Text>
        </View>
        {currentLang === item.code && (
          <Ionicons name="checkmark" size={20} color="#5B37B7" />
        )}
      </View>
    </TouchableOpacity>
  );

  if (compact) {
    // Compact version for header/top placement
    return (
      <View style={[styles.compactContainer, style]}>
        <TouchableOpacity
          style={styles.compactButton}
          onPress={openSelector}
        >
          <Text style={styles.compactFlag}>{currentLanguageConfig?.flag}</Text>
          <Text style={styles.compactCode}>{currentLang.toUpperCase()}</Text>
          <Ionicons name="chevron-down" size={12} color="#666" />
        </TouchableOpacity>

        {/* Language Selector Modal */}
        <Modal
          visible={isVisible}
          transparent={true}
          animationType="none"
          onRequestClose={closeSelector}
        >
          <View style={styles.modalOverlay}>
            <TouchableOpacity 
              style={styles.modalBackground} 
              onPress={closeSelector}
              activeOpacity={1}
            />
            
            <Animated.View 
              style={[
                styles.modalContent,
                { transform: [{ translateY: slideAnim }] }
              ]}
            >
              {/* Modal Header */}
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>{t('settings.language')}</Text>
                <TouchableOpacity onPress={closeSelector}>
                  <Ionicons name="close" size={24} color="#333" />
                </TouchableOpacity>
              </View>

              {/* Languages List */}
              <FlatList
                data={availableLanguages}
                renderItem={renderLanguageItem}
                keyExtractor={(item) => item.code}
                style={styles.languagesList}
                showsVerticalScrollIndicator={true}
                contentContainerStyle={styles.languagesListContent}
              />
            </Animated.View>
          </View>
        </Modal>
      </View>
    );
  }

  // Full version for main placement
  return (
    <View style={[styles.container, style]}>
      {/* Language Selector Button */}
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={openSelector}
      >
        <View style={styles.selectedLanguageContent}>
          <Text style={styles.selectedFlag}>{currentLanguageConfig?.flag}</Text>
          <Text style={styles.selectedLanguageName}>{currentLanguageConfig?.nativeName}</Text>
        </View>
        <Ionicons name="chevron-down" size={20} color="#666" />
      </TouchableOpacity>

      {/* Language Selector Modal */}
      <Modal
        visible={isVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeSelector}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackground} 
            onPress={closeSelector}
            activeOpacity={1}
          />
          
          <Animated.View 
            style={[
              styles.modalContent,
              { transform: [{ translateY: slideAnim }] }
            ]}
          >
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Language</Text>
              <TouchableOpacity onPress={closeSelector}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Languages List */}
            <FlatList
              data={availableLanguages}
              renderItem={renderLanguageItem}
              keyExtractor={(item) => item.code}
              style={styles.languagesList}
              showsVerticalScrollIndicator={true}
              contentContainerStyle={styles.languagesListContent}
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  // Compact version styles
  compactContainer: {
    marginRight: 10,
  },
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    minWidth: 60,
  },
  compactFlag: {
    fontSize: 16,
    marginRight: 4,
  },
  compactCode: {
    fontSize: 12,
    color: '#333',
    fontWeight: '600',
    marginRight: 4,
  },
  
  // Full version styles
  container: {
    marginBottom: 15,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 50,
  },
  selectedLanguageContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedFlag: {
    fontSize: 20,
    marginRight: 10,
  },
  selectedLanguageName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalBackground: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: screenHeight * 0.6,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  languagesList: {
    flex: 1,
  },
  languagesListContent: {
    paddingBottom: 20,
  },
  languageItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  selectedLanguageItem: {
    backgroundColor: '#F0F0FF',
    borderBottomColor: '#E0E0FF',
  },
  languageItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 16,
    width: 32,
    textAlign: 'center',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginBottom: 2,
  },
  languageNameSecondary: {
    fontSize: 14,
    color: '#666',
  },
});

export default LanguageSelector;
