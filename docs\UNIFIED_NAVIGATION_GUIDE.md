# Unified Navigation System Implementation Guide

## Overview
This guide explains how to implement the unified navigation behavior across all JiraniPay screens to ensure consistent back button behavior, proper navigation stack preservation, and prevent accidental app exits.

## Navigation Hierarchy Principles
1. **Sequential Back Navigation**: Back buttons navigate to the immediately previous screen in the navigation stack
2. **Navigation Stack Preservation**: Maintain full navigation history for proper user flow
3. **Exit Confirmation Only at Root**: Exit dialog only appears when at root home screen with no navigation stack
4. **Example Flow**: Home → Bills → Data → Provider → Details → Amount
   - Back from Amount → Details → Provider → Data → Bills → Home → Exit Dialog

## System Components

### 1. NavigationUtils (`utils/navigationUtils.js`)
- Central navigation management system
- Handles hardware back button events
- Provides exit confirmation dialog
- Manages navigation state

### 2. UnifiedBackButton (`components/UnifiedBackButton.js`)
- Reusable back button component
- Consistent styling and behavior
- Automatic navigation handling

### 3. App.js Integration
- Navigation reference setup
- Hardware back button initialization
- Global navigation state management

## Implementation Steps for Each Screen

### Step 1: Import the UnifiedBackButton Component
```javascript
import UnifiedBackButton from '../components/UnifiedBackButton';
```

### Step 2: Replace Existing Back Buttons
**Before:**
```javascript
<TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
  <Ionicons name="arrow-back" size={24} color="white" />
</TouchableOpacity>
```

**After:**
```javascript
<UnifiedBackButton 
  navigation={navigation}
  style={styles.backButton}
  iconColor="white"
  iconSize={24}
/>
```

### Step 3: Remove Custom Back Button Logic
Remove any custom back button handling code like:
```javascript
// Remove this type of code
onPress={() => {
  if (navigation?.goBack) {
    navigation.goBack();
  } else if (navigation?.navigateToTab) {
    navigation.navigateToTab('home');
  } else {
    console.log('Navigation fallback: returning to home');
  }
}}
```

## Screen-Specific Implementation

### 1. Header-Based Screens
For screens with custom headers (like TransactionHistoryScreen, BillPaymentScreen):
```javascript
<UnifiedBackButton 
  navigation={navigation}
  style={styles.modernBackButton}
  iconColor={Colors.neutral.white}
  iconSize={24}
/>
```

### 2. QR Scanner Screen
```javascript
import UnifiedBackButton from '../components/UnifiedBackButton';

// In the header/overlay
<UnifiedBackButton 
  navigation={navigation}
  style={styles.qrBackButton}
  iconColor="white"
  iconSize={28}
/>
```

### 3. Profile Screens
```javascript
<UnifiedBackButton 
  navigation={navigation}
  style={styles.profileBackButton}
  iconColor={Colors.neutral.charcoal}
  iconSize={24}
/>
```

### 4. Modal/Overlay Screens
For screens that appear as modals or overlays:
```javascript
<UnifiedBackButton 
  navigation={navigation}
  style={styles.modalBackButton}
  iconColor="white"
  iconSize={24}
  backgroundColor="rgba(0, 0, 0, 0.3)"
/>
```

## Screens to Update

### High Priority (Immediate Update Required)
1. **QRScannerScreen.js** - Currently exits app immediately
2. **ProfileScreen.js** - User profile and settings
3. **WalletScreen.js** - Wallet management
4. **SendMoneyScreen.js** - Money transfer flow
5. **TopUpScreen.js** - Wallet top-up

### Medium Priority
6. **AnalyticsScreen.js** - Financial analytics
7. **BudgetInsightsScreen.js** - Budget insights
8. **SavingsScreen.js** - Savings management
9. **AutomaticSavingsScreen.js** - Automatic savings
10. **WalletSettingsScreen.js** - Wallet settings

### Lower Priority
11. **TransferAmountScreen.js** - Transfer amount input
12. **TransferConfirmationScreen.js** - Transfer confirmation
13. **TransferSuccessScreen.js** - Transfer success
14. **ManualRecipientScreen.js** - Manual recipient entry
15. **RequestMoneyScreen.js** - Money request flow

## Testing Checklist

### Navigation Flow Testing:
- [ ] **Sequential Navigation**: Test complete user flows (Home → Bills → Category → Provider → Details → Amount)
- [ ] **Back Button Behavior**: Each back press goes to immediately previous screen
- [ ] **Navigation Stack Preservation**: Can navigate back through entire journey
- [ ] **No Unexpected Jumps**: Back button never jumps directly to home (except from root)
- [ ] **Exit Confirmation**: Only appears when at root home screen with no navigation stack

### For Each Updated Screen:
- [ ] Back button navigates to previous screen (not app exit)
- [ ] Hardware back button works consistently
- [ ] Visual styling matches existing design
- [ ] No navigation loops or broken flows
- [ ] Navigation debugger shows correct state (in development)

### Home Screen Testing:
- [ ] Hardware back button shows exit confirmation only when no navigation stack
- [ ] Exit confirmation has proper styling
- [ ] "Stay" button keeps user in app
- [ ] "Exit App" button closes app properly
- [ ] Haptic feedback works correctly

### Multi-Screen Flow Testing:
1. **Bill Payment Flow**: Home → Bills → Electricity → UMEME → Account Details → Amount → Confirmation
   - Test back navigation at each step
   - Verify each back press goes to previous screen
   - Confirm no unexpected home jumps

2. **Money Transfer Flow**: Home → Send Money → Recipient → Amount → Confirmation → Success
   - Test complete flow navigation
   - Verify proper stack preservation
   - Test hardware back button at each step

3. **QR Scanner Flow**: Home → QR Scanner → Manual Entry → Back to Scanner → Back to Home
   - Test QR scanner back button
   - Verify proper navigation hierarchy

### Debug Tools:
Use NavigationDebugger component to verify:
- Current route name
- Navigation stack length
- Can go back status
- Root home screen detection

## Customization Options

### UnifiedBackButton Props:
- `navigation` - Navigation object (required)
- `style` - Custom button styling
- `iconColor` - Icon color (default: white)
- `iconSize` - Icon size (default: 24)
- `backgroundColor` - Button background color
- `onPress` - Custom press handler (overrides default)
- `disabled` - Disable button interaction

### Example Custom Implementation:
```javascript
<UnifiedBackButton 
  navigation={navigation}
  style={[styles.backButton, { marginLeft: 10 }]}
  iconColor={Colors.primary.main}
  iconSize={26}
  backgroundColor="rgba(229, 62, 62, 0.1)"
  onPress={() => {
    // Custom behavior
    console.log('Custom back action');
    NavigationUtils.safeGoBack(navigation);
  }}
/>
```

## Troubleshooting

### Common Issues:
1. **Navigation not working**: Ensure navigation prop is passed correctly
2. **Styling issues**: Check that custom styles don't conflict
3. **Exit dialog not showing**: Verify NavigationUtils is initialized in App.js
4. **Hardware back button not working**: Check BackHandler initialization

### Debug Tools:
```javascript
// Check current route
console.log('Current route:', NavigationUtils.getCurrentRoute());

// Check if navigation is ready
console.log('Navigation ready:', NavigationUtils.isNavigationReady());
```

## Benefits

1. **Consistent UX**: All screens behave the same way
2. **No Accidental Exits**: Users can only exit from home screen
3. **Better Navigation Flow**: Logical back navigation throughout app
4. **Maintainable Code**: Centralized navigation logic
5. **East African Design**: Beautiful exit confirmation dialog
6. **Accessibility**: Consistent interaction patterns

## Next Steps

1. Update QRScannerScreen.js (highest priority)
2. Update remaining screens following this guide
3. Test navigation flows thoroughly
4. Update any custom navigation logic
5. Document any screen-specific requirements
