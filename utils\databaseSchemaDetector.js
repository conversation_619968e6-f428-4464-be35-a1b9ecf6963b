import supabase from '../services/supabaseClient';

class DatabaseSchemaDetector {
  constructor() {
    this.schemaCache = null;
    this.detectionPromise = null;
  }

  /**
   * Detect which database schema is being used
   * Returns: 'new' (user_id field) or 'old' (id field)
   */
  async detectSchema() {
    // Return cached result if available
    if (this.schemaCache) {
      return this.schemaCache;
    }

    // Return existing promise if detection is in progress
    if (this.detectionPromise) {
      return this.detectionPromise;
    }

    // Start detection
    this.detectionPromise = this._performDetection();
    const result = await this.detectionPromise;
    this.schemaCache = result;
    this.detectionPromise = null;
    
    return result;
  }

  async _performDetection() {
    try {
      console.log('🔍 Detecting database schema structure...');

      // Try to get table structure information
      const { data: columns, error } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'user_profiles')
        .eq('table_schema', 'public');

      if (error) {
        console.log('⚠️ Could not query schema info, using fallback detection');
        return await this._fallbackDetection();
      }

      const columnNames = columns.map(col => col.column_name);
      console.log('📋 Found columns in user_profiles:', columnNames);

      // Check if user_id column exists (new schema)
      if (columnNames.includes('user_id')) {
        console.log('✅ Detected NEW schema (user_id field)');
        return 'new';
      } else {
        console.log('✅ Detected OLD schema (id field)');
        return 'old';
      }

    } catch (error) {
      console.log('⚠️ Schema detection failed, using fallback:', error.message);
      return await this._fallbackDetection();
    }
  }

  async _fallbackDetection() {
    try {
      // Try to insert a test record with user_id field
      const testUserId = '00000000-0000-0000-0000-000000000000';
      
      const { error: newSchemaError } = await supabase
        .from('user_profiles')
        .select('user_id')
        .eq('user_id', testUserId)
        .limit(1);

      if (!newSchemaError || newSchemaError.code !== '42703') {
        console.log('✅ Fallback: Detected NEW schema (user_id field works)');
        return 'new';
      }

      console.log('✅ Fallback: Detected OLD schema (user_id field not found)');
      return 'old';

    } catch (error) {
      console.log('⚠️ Fallback detection failed, defaulting to OLD schema');
      return 'old';
    }
  }

  /**
   * Get the correct field name for user reference
   */
  async getUserFieldName() {
    const schema = await this.detectSchema();
    return schema === 'new' ? 'user_id' : 'id';
  }

  /**
   * Execute a query with automatic schema detection
   */
  async queryUserProfile(userId, operation = 'select', data = null) {
    const fieldName = await this.getUserFieldName();
    console.log(`🔍 Using ${fieldName} field for user profile query`);

    let query = supabase.from('user_profiles');

    switch (operation) {
      case 'select':
        return query.select('*').eq(fieldName, userId).single();
      
      case 'update':
        return query.update(data).eq(fieldName, userId).select().single();
      
      case 'upsert':
        const upsertData = { ...data };
        upsertData[fieldName] = userId;
        return query.upsert(upsertData).select().single();
      
      case 'delete':
        return query.delete().eq(fieldName, userId);
      
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }
  }

  /**
   * Clear cache (useful for testing)
   */
  clearCache() {
    this.schemaCache = null;
    this.detectionPromise = null;
  }
}

// Export singleton instance
const schemaDetector = new DatabaseSchemaDetector();
export default schemaDetector;
