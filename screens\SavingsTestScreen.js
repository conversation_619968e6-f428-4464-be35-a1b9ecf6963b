/**
 * Savings Test Screen
 * Simple test screen to verify the savings integration is working
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';

const SavingsTestScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const testFeatures = [
    {
      title: 'Savings Dashboard',
      description: 'Main savings overview with accounts and summary',
      screen: 'SavingsDashboard',
      icon: 'wallet',
      color: '#4ECDC4'
    },
    {
      title: 'Create Savings Account',
      description: 'Create new savings account with goals',
      screen: 'SavingsAccountCreation',
      icon: 'add-circle',
      color: '#45B7D1'
    },
    {
      title: 'Investment Dashboard',
      description: 'Investment portfolios and market data',
      screen: 'InvestmentDashboard',
      icon: 'trending-up',
      color: '#FF6B35'
    },
    {
      title: 'Financial Planning',
      description: 'Budgets, goals, and recommendations',
      screen: 'FinancialPlanningDashboard',
      icon: 'calculator',
      color: '#6C5CE7'
    }
  ];

  const handleFeaturePress = (screen) => {
    try {
      navigation.navigate(screen);
    } catch (error) {
      console.error('Navigation error:', error);
      alert(`Navigation to ${screen} failed. Make sure the screen is properly registered.`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle={theme.statusBar} backgroundColor={theme.colors.background} />
      
      {/* Header */}
      <View style={styles.header}>
        <UnifiedBackButton navigation={navigation} />
        <Text style={styles.headerTitle}>Savings & Investment Test</Text>
        <View style={styles.headerButton} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.infoCard}>
          <Ionicons name="information-circle" size={24} color={theme.colors.primary} />
          <Text style={styles.infoText}>
            Test the new Savings & Investment features. Each button will navigate to the corresponding screen.
          </Text>
        </View>

        {testFeatures.map((feature, index) => (
          <TouchableOpacity
            key={index}
            style={styles.featureCard}
            onPress={() => handleFeaturePress(feature.screen)}
          >
            <View style={[styles.featureIcon, { backgroundColor: feature.color }]}>
              <Ionicons name={feature.icon} size={24} color="#FFFFFF" />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>{feature.title}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        ))}

        <View style={styles.statusCard}>
          <Text style={styles.statusTitle}>Implementation Status</Text>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.statusText}>Database Schema Deployed</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.statusText}>Services Implemented</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.statusText}>UI Screens Created</Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
            <Text style={styles.statusText}>Navigation Integrated</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  headerButton: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  infoCard: {
    flexDirection: 'row',
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 12,
    lineHeight: 20,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
  statusCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 12,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusText: {
    fontSize: 14,
    color: theme.colors.text,
    marginLeft: 8,
  },
});

export default SavingsTestScreen;
