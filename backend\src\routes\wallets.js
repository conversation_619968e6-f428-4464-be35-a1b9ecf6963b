/**
 * Wallet Routes
 * Handles wallet operations, balance management, and wallet transactions
 */

const express = require('express');
const { body, query, validationResult } = require('express-validator');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError,
  AuthorizationError
} = require('../middleware/errorHandler');
const { validationMiddleware } = require('../middleware/validation');
const walletService = require('../services/walletService');
const currencyService = require('../services/currencyService');
const config = require('../config/config');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   GET /api/v1/wallets
 * @desc    Get user wallet
 * @access  Private
 */
router.get('/', asyncHandler(async (req, res) => {
  try {
    let wallet = await walletService.getWallet(req.user.user_id);

    // Create wallet if it doesn't exist
    if (!wallet) {
      wallet = await walletService.createWallet(req.user.user_id);
    }

    res.json({
      success: true,
      data: {
        wallet: {
          id: wallet.id,
          balance: wallet.balance,
          availableBalance: wallet.available_balance,
          pendingBalance: wallet.pending_balance,
          currency: wallet.currency,
          isActive: wallet.is_active,
          dailyLimit: wallet.daily_limit,
          monthlyLimit: wallet.monthly_limit,
          createdAt: wallet.created_at,
          updatedAt: wallet.updated_at
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get wallet:', error);
    throw new Error('Failed to retrieve wallet information');
  }
}));

/**
 * @route   GET /api/v1/wallets/balance
 * @desc    Get wallet balance
 * @access  Private
 */
router.get('/balance', asyncHandler(async (req, res) => {
  try {
    const balance = await walletService.getBalance(req.user.user_id);

    res.json({
      success: true,
      data: {
        balance: balance.balance,
        availableBalance: balance.availableBalance,
        pendingBalance: balance.pendingBalance,
        currency: balance.currency,
        formatted: currencyService.formatCurrency(balance.balance, balance.currency),
        lastUpdated: balance.lastUpdated
      }
    });
  } catch (error) {
    logger.error('Failed to get balance:', error);
    throw new Error('Failed to retrieve balance');
  }
}));

/**
 * @route   POST /api/v1/wallets/topup
 * @desc    Top up wallet
 * @access  Private
 */
router.post('/topup', validationMiddleware.walletTopup, asyncHandler(async (req, res) => {
  const { amount, paymentMethod, provider } = req.body;

  try {
    // Validate amount
    const wallet = await walletService.getWallet(req.user.user_id);
    if (!wallet) {
      throw new NotFoundError('Wallet not found');
    }

    const validation = currencyService.validateAmount(amount, wallet.currency);
    if (!validation.valid) {
      throw new ValidationError(validation.error);
    }

    // TODO: Integrate with actual payment providers (MTN, Airtel, Banks)
    // For now, simulate the top-up process

    const transactionId = require('uuid').v4();

    // In production, this would:
    // 1. Call payment provider API
    // 2. Handle webhook responses
    // 3. Update wallet balance on confirmation

    logger.audit('Wallet top-up initiated', {
      userId: req.user.user_id,
      amount: validation.amount,
      currency: wallet.currency,
      paymentMethod,
      provider,
      transactionId
    });

    res.json({
      success: true,
      message: 'Wallet top-up initiated successfully',
      data: {
        transactionId,
        amount: validation.amount,
        currency: wallet.currency,
        formatted: validation.formatted,
        paymentMethod,
        provider,
        status: 'pending',
        estimatedCompletion: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
      }
    });
  } catch (error) {
    logger.error('Wallet top-up failed:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/wallets/transfer
 * @desc    Transfer money to another user
 * @access  Private
 */
router.post('/transfer', validationMiddleware.moneyTransfer, asyncHandler(async (req, res) => {
  const { amount, phoneNumber, description } = req.body;

  try {
    // Find recipient by phone number
    const supabase = require('../services/database').getSupabase();
    const { data: recipient, error } = await supabase
      .from('user_profiles')
      .select('user_id, full_name, phone_number')
      .eq('phone_number', phoneNumber)
      .single();

    if (error || !recipient) {
      throw new NotFoundError('Recipient not found');
    }

    if (recipient.user_id === req.user.user_id) {
      throw new ValidationError('Cannot transfer money to yourself');
    }

    // Perform transfer
    const transfer = await walletService.transferMoney(
      req.user.user_id,
      recipient.user_id,
      amount,
      description
    );

    res.json({
      success: true,
      message: 'Money transfer completed successfully',
      data: {
        transferId: transfer.transferId,
        amount: transfer.amount,
        convertedAmount: transfer.convertedAmount,
        fromCurrency: transfer.fromCurrency,
        toCurrency: transfer.toCurrency,
        exchangeRate: transfer.exchangeRate,
        recipient: {
          name: recipient.full_name,
          phoneNumber: recipient.phone_number
        },
        description,
        status: transfer.status,
        timestamp: transfer.timestamp
      }
    });
  } catch (error) {
    logger.error('Money transfer failed:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/wallets/transactions
 * @desc    Get wallet transaction history
 * @access  Private
 */
router.get('/transactions', [
  ...validationMiddleware.transactionHistory
], asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    type,
    status,
    startDate,
    endDate
  } = req.query;

  try {
    const history = await walletService.getTransactionHistory(req.user.user_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      status,
      startDate,
      endDate
    });

    // Format transactions for response
    const formattedTransactions = history.transactions.map(tx => ({
      id: tx.id,
      type: tx.type,
      direction: tx.direction,
      amount: tx.amount,
      currency: tx.currency,
      formatted: currencyService.formatCurrency(tx.amount, tx.currency),
      description: tx.description,
      status: tx.status,
      recipient: tx.direction === 'outgoing' ? tx.to_user : null,
      sender: tx.direction === 'incoming' ? tx.from_user : null,
      createdAt: tx.created_at,
      updatedAt: tx.updated_at
    }));

    res.json({
      success: true,
      data: {
        transactions: formattedTransactions,
        pagination: history.pagination
      }
    });
  } catch (error) {
    logger.error('Failed to get transaction history:', error);
    throw new Error('Failed to retrieve transaction history');
  }
}));

/**
 * @route   GET /api/v1/wallets/limits
 * @desc    Get wallet transaction limits
 * @access  Private
 */
router.get('/limits', asyncHandler(async (req, res) => {
  try {
    const wallet = await walletService.getWallet(req.user.user_id);
    if (!wallet) {
      throw new NotFoundError('Wallet not found');
    }

    const dailySpent = await walletService.getDailySpent(req.user.user_id);
    const dailyRemaining = Math.max(0, wallet.daily_limit - dailySpent);

    res.json({
      success: true,
      data: {
        dailyLimit: wallet.daily_limit,
        monthlyLimit: wallet.monthly_limit,
        dailySpent,
        dailyRemaining,
        currency: wallet.currency,
        formatted: {
          dailyLimit: currencyService.formatCurrency(wallet.daily_limit, wallet.currency),
          monthlyLimit: currencyService.formatCurrency(wallet.monthly_limit, wallet.currency),
          dailySpent: currencyService.formatCurrency(dailySpent, wallet.currency),
          dailyRemaining: currencyService.formatCurrency(dailyRemaining, wallet.currency)
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get wallet limits:', error);
    throw error;
  }
}));

/**
 * @route   PUT /api/v1/wallets/limits
 * @desc    Update wallet transaction limits (admin only)
 * @access  Private (Admin)
 */
router.put('/limits', [
  body('dailyLimit').optional().isNumeric().withMessage('Daily limit must be numeric'),
  body('monthlyLimit').optional().isNumeric().withMessage('Monthly limit must be numeric')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  // Check if user is admin (this would be handled by admin middleware in production)
  if (!req.user.is_admin) {
    throw new AuthorizationError('Admin access required');
  }

  const { dailyLimit, monthlyLimit } = req.body;

  try {
    // TODO: Implement limit update logic
    res.json({
      success: true,
      message: 'Wallet limits updated successfully',
      data: {
        dailyLimit,
        monthlyLimit
      }
    });
  } catch (error) {
    logger.error('Failed to update wallet limits:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/wallets/freeze
 * @desc    Freeze wallet (admin only)
 * @access  Private (Admin)
 */
router.post('/freeze', [
  body('userId').isUUID().withMessage('Valid user ID is required'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  if (!req.user.is_admin) {
    throw new AuthorizationError('Admin access required');
  }

  const { userId, reason = 'Administrative action' } = req.body;

  try {
    const wallet = await walletService.freezeWallet(userId, reason);

    res.json({
      success: true,
      message: 'Wallet frozen successfully',
      data: {
        walletId: wallet.id,
        userId: wallet.user_id,
        reason: wallet.frozen_reason,
        frozenAt: wallet.frozen_at
      }
    });
  } catch (error) {
    logger.error('Failed to freeze wallet:', error);
    throw error;
  }
}));

/**
 * @route   POST /api/v1/wallets/unfreeze
 * @desc    Unfreeze wallet (admin only)
 * @access  Private (Admin)
 */
router.post('/unfreeze', [
  body('userId').isUUID().withMessage('Valid user ID is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  if (!req.user.is_admin) {
    throw new AuthorizationError('Admin access required');
  }

  const { userId } = req.body;

  try {
    const wallet = await walletService.unfreezeWallet(userId);

    res.json({
      success: true,
      message: 'Wallet unfrozen successfully',
      data: {
        walletId: wallet.id,
        userId: wallet.user_id,
        isActive: wallet.is_active
      }
    });
  } catch (error) {
    logger.error('Failed to unfreeze wallet:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/wallets/currencies
 * @desc    Get supported currencies and exchange rates
 * @access  Private
 */
router.get('/currencies', asyncHandler(async (req, res) => {
  try {
    const currencies = currencyService.getSupportedCurrencies();
    const exchangeRates = await currencyService.getExchangeRates();

    res.json({
      success: true,
      data: {
        currencies,
        exchangeRates: {
          base: exchangeRates.base,
          rates: exchangeRates.rates,
          timestamp: exchangeRates.timestamp,
          source: exchangeRates.source
        }
      }
    });
  } catch (error) {
    logger.error('Failed to get currencies:', error);
    throw new Error('Failed to retrieve currency information');
  }
}));

/**
 * @route   POST /api/v1/wallets/convert
 * @desc    Convert currency amount (preview)
 * @access  Private
 */
router.post('/convert', [
  body('amount').isNumeric().withMessage('Valid amount is required'),
  body('fromCurrency').isIn(config.business.supportedCurrencies).withMessage('Invalid source currency'),
  body('toCurrency').isIn(config.business.supportedCurrencies).withMessage('Invalid target currency')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { amount, fromCurrency, toCurrency } = req.body;

  try {
    const conversion = await currencyService.convertCurrency(amount, fromCurrency, toCurrency);

    res.json({
      success: true,
      data: {
        originalAmount: conversion.originalAmount,
        convertedAmount: conversion.convertedAmount,
        fromCurrency: conversion.fromCurrency,
        toCurrency: conversion.toCurrency,
        exchangeRate: conversion.exchangeRate,
        formatted: {
          original: currencyService.formatCurrency(conversion.originalAmount, fromCurrency),
          converted: currencyService.formatCurrency(conversion.convertedAmount, toCurrency)
        },
        timestamp: conversion.timestamp
      }
    });
  } catch (error) {
    logger.error('Currency conversion failed:', error);
    throw error;
  }
}));

module.exports = router;
