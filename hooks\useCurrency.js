import { useState, useEffect, useCallback } from 'react';
import currencyService from '../services/currencyService';
import authService from '../services/authService';

/**
 * Universal Currency Hook for Real-time Currency Conversion
 * 
 * This hook provides:
 * - Real-time currency conversion for any amount
 * - Automatic updates when user changes preferred currency
 * - Consistent formatting across the entire app
 * - Error handling with UGX fallback
 * - Performance optimization with memoization
 */
export const useCurrency = () => {
  const [user, setUser] = useState(null);
  const [userCurrency, setUserCurrency] = useState('UGX');
  const [exchangeRates, setExchangeRates] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Load user's preferred currency and exchange rates
  const loadCurrencyData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Initialize currency service if needed
      if (!currencyService.isInitialized) {
        await currencyService.initialize();
      }

      // Get user's preferred currency
      const preferredCurrency = await currencyService.getUserPreferredCurrency();
      setUserCurrency(preferredCurrency);

      // Get all exchange rates
      const rates = currencyService.getAllExchangeRates();
      setExchangeRates(rates);
      setLastUpdate(new Date());

      console.log('💱 useCurrency: Loaded currency data', {
        userCurrency: preferredCurrency,
        ratesCount: Object.keys(rates).length
      });
    } catch (error) {
      console.error('❌ useCurrency: Error loading currency data:', error);
      // Fallback to UGX
      setUserCurrency('UGX');
      setExchangeRates({ UGX: 1 });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get current user
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error('❌ useCurrency: Error getting current user:', error);
      }
    };
    getCurrentUser();
  }, []);

  // Listen for currency preference changes
  useEffect(() => {
    if (user) {
      loadCurrencyData();
    }

    // Add listener for currency changes
    const unsubscribe = currencyService.addCurrencyChangeListener((newCurrency) => {
      console.log('💱 useCurrency: Currency changed to:', newCurrency);
      setUserCurrency(newCurrency);
      loadCurrencyData(); // Reload to get fresh exchange rates
    });

    return unsubscribe;
  }, [loadCurrencyData]);

  // Convert amount from UGX to user's preferred currency
  const convertFromUGX = useCallback((ugxAmount, targetCurrency = null) => {
    try {
      const target = targetCurrency || userCurrency;
      const amount = parseFloat(ugxAmount) || 0;
      
      if (target === 'UGX') {
        return amount;
      }

      const rate = exchangeRates[target];
      if (!rate) {
        console.warn(`⚠️ useCurrency: No exchange rate for ${target}, using UGX`);
        return amount;
      }

      return amount * rate;
    } catch (error) {
      console.error('❌ useCurrency: Error converting from UGX:', error);
      return parseFloat(ugxAmount) || 0;
    }
  }, [userCurrency, exchangeRates]);

  // Convert between any two currencies
  const convert = useCallback((amount, fromCurrency, toCurrency = null) => {
    try {
      const target = toCurrency || userCurrency;
      const numAmount = parseFloat(amount) || 0;

      if (fromCurrency === target) {
        return numAmount;
      }

      // Convert through UGX as base currency
      const fromRate = exchangeRates[fromCurrency] || 1;
      const toRate = exchangeRates[target] || 1;
      
      // Convert to UGX first, then to target currency
      const ugxAmount = fromCurrency === 'UGX' ? numAmount : numAmount / fromRate;
      const convertedAmount = target === 'UGX' ? ugxAmount : ugxAmount * toRate;

      return convertedAmount;
    } catch (error) {
      console.error('❌ useCurrency: Error converting currencies:', error);
      return parseFloat(amount) || 0;
    }
  }, [userCurrency, exchangeRates]);

  // Format amount with currency symbol
  const formatAmount = useCallback((amount, currency = null, options = {}) => {
    try {
      const targetCurrency = currency || userCurrency;
      const numAmount = parseFloat(amount) || 0;
      
      return currencyService.formatAmountWithCurrency(numAmount, targetCurrency, {
        showSymbol: true,
        showFlag: false,
        compact: false,
        ...options
      });
    } catch (error) {
      console.error('❌ useCurrency: Error formatting amount:', error);
      const fallbackSymbol = currency || userCurrency || 'UGX';
      return `${fallbackSymbol} ${parseFloat(amount).toLocaleString()}`;
    }
  }, [userCurrency]);

  // Convert from UGX and format in one step
  const convertAndFormat = useCallback((ugxAmount, targetCurrency = null, options = {}) => {
    const convertedAmount = convertFromUGX(ugxAmount, targetCurrency);
    return formatAmount(convertedAmount, targetCurrency || userCurrency, options);
  }, [convertFromUGX, formatAmount, userCurrency]);

  // Get currency symbol
  const getCurrencySymbol = useCallback((currency = null) => {
    try {
      const targetCurrency = currency || userCurrency;
      const currencyInfo = currencyService.getCurrencyInfo(targetCurrency);
      return currencyInfo?.symbol || targetCurrency;
    } catch (error) {
      console.error('❌ useCurrency: Error getting currency symbol:', error);
      return currency || userCurrency || 'UGX';
    }
  }, [userCurrency]);

  // Get currency info
  const getCurrencyInfo = useCallback((currency = null) => {
    try {
      const targetCurrency = currency || userCurrency;
      return currencyService.getCurrencyInfo(targetCurrency);
    } catch (error) {
      console.error('❌ useCurrency: Error getting currency info:', error);
      return { symbol: targetCurrency, name: targetCurrency, flag: '' };
    }
  }, [userCurrency]);

  // Get exchange rate between currencies
  const getExchangeRate = useCallback((fromCurrency, toCurrency = null) => {
    try {
      const target = toCurrency || userCurrency;
      return currencyService.getExchangeRate(fromCurrency, target);
    } catch (error) {
      console.error('❌ useCurrency: Error getting exchange rate:', error);
      return 1;
    }
  }, [userCurrency]);

  // Refresh exchange rates
  const refreshRates = useCallback(async () => {
    try {
      await currencyService.updateExchangeRates();
      await loadCurrencyData();
      console.log('💱 useCurrency: Exchange rates refreshed');
    } catch (error) {
      console.error('❌ useCurrency: Error refreshing rates:', error);
    }
  }, [loadCurrencyData]);

  return {
    // Current state
    userCurrency,
    exchangeRates,
    isLoading,
    lastUpdate,

    // Conversion functions
    convertFromUGX,
    convert,
    formatAmount,
    convertAndFormat,

    // Utility functions
    getCurrencySymbol,
    getCurrencyInfo,
    getExchangeRate,
    refreshRates,

    // Helper for checking if currency is available
    isCurrencySupported: (currency) => !!exchangeRates[currency],
    
    // Get all supported currencies
    getSupportedCurrencies: () => Object.keys(exchangeRates)
  };
};

export default useCurrency;
