import { supabase } from './supabaseClient';
import * as Device from 'expo-device';
import * as Application from 'expo-application';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { isProductionMode } from '../config/environment';

/**
 * Device Management Service for JiraniPay
 * Handles device tracking, trusted devices, and device-based security
 */
class DeviceManagementService {
  constructor() {
    this.tableName = 'user_devices';
    this.auditTableName = 'audit_logs';
  }

  /**
   * Get comprehensive device information
   */
  async getDeviceInfo() {
    try {
      const deviceInfo = {
        device_id: await this.getUniqueDeviceId(),
        device_name: Device.deviceName || 'Unknown Device',
        device_type: this.getDeviceType(),
        platform: Platform.OS,
        os_version: Device.osVersion || 'Unknown',
        app_version: Application.nativeApplicationVersion || '1.0.0',
        brand: Device.brand || 'Unknown',
        model: Device.modelName || 'Unknown',
        is_device: Device.isDevice,
        timestamp: new Date().toISOString()
      };

      // Location is optional - skip for now to avoid dependency issues
      deviceInfo.location_name = 'Location not available';

      console.log('📱 Device info collected:', {
        id: deviceInfo.device_id,
        name: deviceInfo.device_name,
        type: deviceInfo.device_type,
        platform: deviceInfo.platform,
        location: deviceInfo.location_name
      });

      return deviceInfo;
    } catch (error) {
      console.error('❌ Error getting device info:', error);
      
      // Return basic fallback info
      return {
        device_id: 'unknown-device',
        device_name: 'Current Device',
        device_type: 'mobile',
        platform: Platform.OS,
        os_version: 'Unknown',
        app_version: '1.0.0',
        location_name: 'Unknown',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get unique device identifier
   */
  async getUniqueDeviceId() {
    try {
      // Try to get stored device ID first
      const storedId = await SecureStore.getItemAsync('device_id');
      if (storedId) {
        return storedId;
      }

      // Generate new device ID
      let deviceId;
      
      if (Platform.OS === 'android') {
        deviceId = await Application.getAndroidId();
      } else if (Platform.OS === 'ios') {
        deviceId = await Application.getIosIdForVendorAsync();
      }
      
      // Fallback to generated ID if platform-specific ID fails
      if (!deviceId) {
        deviceId = `${Platform.OS}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      }

      // Store the device ID securely
      await SecureStore.setItemAsync('device_id', deviceId);
      
      return deviceId;
    } catch (error) {
      console.error('❌ Error getting device ID:', error);
      return `fallback-${Platform.OS}-${Date.now()}`;
    }
  }

  /**
   * Get device type classification
   */
  getDeviceType() {
    if (Device.deviceType === Device.DeviceType.PHONE) {
      return 'mobile';
    } else if (Device.deviceType === Device.DeviceType.TABLET) {
      return 'tablet';
    } else if (Device.deviceType === Device.DeviceType.DESKTOP) {
      return 'desktop';
    } else {
      return 'unknown';
    }
  }

  /**
   * Register or update device on login
   */
  async registerDevice(userId) {
    if (!isProductionMode()) {
      console.log('🔧 Development mode: Skipping device registration');
      return { success: true, device: null };
    }

    try {
      console.log('📱 Registering device for user:', userId);

      const deviceInfo = await this.getDeviceInfo();

      // Check if device already exists
      const { data: existingDevice, error: fetchError } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .eq('device_id', deviceInfo.device_id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 = no rows returned
        // Check if it's a table not found error
        if (fetchError.code === '42P01') {
          console.error('❌ Database table not found. Please run the database schema setup.');
          return {
            success: false,
            error: 'Database not properly configured. Please contact support.',
            needsSetup: true
          };
        }
        throw fetchError;
      }

      let deviceRecord;

      if (existingDevice) {
        // Update existing device
        console.log('📱 Updating existing device:', deviceInfo.device_id);
        
        const { data, error } = await supabase
          .from(this.tableName)
          .update({
            device_name: deviceInfo.device_name,
            platform: deviceInfo.platform,
            os_version: deviceInfo.os_version,
            app_version: deviceInfo.app_version,
            last_active: new Date().toISOString(),
            location_data: deviceInfo.location_data || null,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingDevice.id)
          .select()
          .single();

        if (error) throw error;
        deviceRecord = data;
        
        // Log device update
        await this.logDeviceEvent(userId, 'device_updated', {
          device_id: deviceInfo.device_id,
          device_name: deviceInfo.device_name
        });
      } else {
        // Create new device record
        console.log('📱 Creating new device record:', deviceInfo.device_id);
        
        const { data, error } = await supabase
          .from(this.tableName)
          .insert({
            user_id: userId,
            device_id: deviceInfo.device_id,
            device_name: deviceInfo.device_name,
            device_type: deviceInfo.device_type,
            platform: deviceInfo.platform,
            os_version: deviceInfo.os_version,
            app_version: deviceInfo.app_version,
            last_active: new Date().toISOString(),
            is_trusted: false, // New devices are not trusted by default
            location_data: deviceInfo.location_data || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (error) throw error;
        deviceRecord = data;
        
        // Log new device registration
        await this.logDeviceEvent(userId, 'device_registered', {
          device_id: deviceInfo.device_id,
          device_name: deviceInfo.device_name,
          device_type: deviceInfo.device_type,
          location: deviceInfo.location_name
        });
      }

      console.log('✅ Device registered successfully:', deviceRecord.id);
      
      return {
        success: true,
        device: deviceRecord,
        isNewDevice: !existingDevice
      };
    } catch (error) {
      console.error('❌ Error registering device:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get all trusted devices for a user
   */
  async getTrustedDevices(userId) {
    if (!isProductionMode()) {
      console.log('🔧 Development mode: Returning empty trusted devices');
      return { success: true, devices: [] };
    }

    try {
      console.log('📱 Getting trusted devices for user:', userId);

      const { data, error } = await supabase
        .from(this.tableName)
        .select('*')
        .eq('user_id', userId)
        .order('last_active', { ascending: false });

      if (error) {
        // Check if it's a table not found error
        if (error.code === '42P01') {
          console.error('❌ Database table not found. Please run the database schema setup.');
          return {
            success: false,
            error: 'Database not properly configured. Please run the database setup script.',
            devices: [],
            needsSetup: true
          };
        }
        throw error;
      }

      // Get current device ID to mark it
      const currentDeviceId = await this.getUniqueDeviceId();
      
      // Process devices and add display information
      const processedDevices = data.map(device => ({
        ...device,
        isCurrent: device.device_id === currentDeviceId,
        location_name: this.getLocationName(device.location_data),
        display_name: device.device_name || 'Unknown Device',
        os_display: `${device.platform === 'ios' ? 'iOS' : 'Android'} ${device.os_version || ''}`.trim()
      }));

      console.log(`✅ Found ${processedDevices.length} devices for user`);
      
      return {
        success: true,
        devices: processedDevices
      };
    } catch (error) {
      console.error('❌ Error getting trusted devices:', error);
      return {
        success: false,
        error: error.message,
        devices: []
      };
    }
  }

  /**
   * Extract location name from location data
   */
  getLocationName(locationData) {
    if (!locationData) return 'Unknown location';
    
    if (locationData.city && locationData.country) {
      return `${locationData.city}, ${locationData.country}`;
    }
    
    if (typeof locationData === 'string') {
      return locationData;
    }
    
    return 'Unknown location';
  }

  /**
   * Trust a device
   */
  async trustDevice(userId, deviceId) {
    try {
      console.log('🔒 Trusting device:', deviceId, 'for user:', userId);
      
      const { data, error } = await supabase
        .from(this.tableName)
        .update({
          is_trusted: true,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('device_id', deviceId)
        .select()
        .single();

      if (error) throw error;

      // Log device trust event
      await this.logDeviceEvent(userId, 'device_trusted', {
        device_id: deviceId,
        device_name: data.device_name
      });

      console.log('✅ Device trusted successfully');
      
      return { success: true, device: data };
    } catch (error) {
      console.error('❌ Error trusting device:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Remove a trusted device
   */
  async removeDevice(userId, deviceId) {
    try {
      console.log('🗑️ Removing device:', deviceId, 'for user:', userId);
      
      // Get device info before deletion for logging
      const { data: deviceInfo } = await supabase
        .from(this.tableName)
        .select('device_name')
        .eq('user_id', userId)
        .eq('device_id', deviceId)
        .single();

      const { error } = await supabase
        .from(this.tableName)
        .delete()
        .eq('user_id', userId)
        .eq('device_id', deviceId);

      if (error) throw error;

      // Log device removal
      await this.logDeviceEvent(userId, 'device_removed', {
        device_id: deviceId,
        device_name: deviceInfo?.device_name || 'Unknown Device'
      });

      console.log('✅ Device removed successfully');
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error removing device:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if current device is trusted
   */
  async isCurrentDeviceTrusted(userId) {
    try {
      const deviceId = await this.getUniqueDeviceId();
      
      const { data, error } = await supabase
        .from(this.tableName)
        .select('is_trusted')
        .eq('user_id', userId)
        .eq('device_id', deviceId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return {
        success: true,
        isTrusted: data?.is_trusted || false,
        deviceExists: !!data
      };
    } catch (error) {
      console.error('❌ Error checking device trust:', error);
      return { success: false, isTrusted: false, deviceExists: false };
    }
  }

  /**
   * Log device-related security events
   */
  async logDeviceEvent(userId, action, metadata = {}) {
    try {
      const { error } = await supabase
        .from(this.auditTableName)
        .insert({
          user_id: userId,
          action,
          resource_type: 'device',
          new_values: metadata,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    } catch (error) {
      console.error('❌ Error logging device event:', error);
    }
  }
}

// Create and export singleton instance
const deviceManagementService = new DeviceManagementService();
export default deviceManagementService;
