-- Fix Money Requests Function - Type Mismatch Issue
-- This fixes the "structure of query does not match function result type" error

-- Drop and recreate the function with correct return types
DROP FUNCTION IF EXISTS get_user_money_requests(UUID, INTEGER, INTEGER, TEXT);

-- Create function to get user's money request history with correct types
CREATE OR REPLACE FUNCTION get_user_money_requests(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_status TEXT DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    type TEXT,
    contact_name TEXT,
    contact_phone TEXT,
    amount DECIMAL,
    currency VARCHAR(3),
    purpose VARCHAR(100),
    note TEXT,
    status VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    approved_at TIMESTAMP WITH TIME ZONE,
    declined_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        mr.id,
        CASE 
            WHEN mr.requester_id = p_user_id THEN 'sent'::TEXT
            ELSE 'received'::TEXT
        END as type,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_name
            ELSE up.full_name
        END as contact_name,
        CASE 
            WHEN mr.requester_id = p_user_id THEN mr.recipient_phone
            ELSE up.phone_number
        END as contact_phone,
        mr.amount,
        mr.currency,
        mr.purpose,
        mr.note,
        mr.status,
        mr.created_at,
        mr.updated_at,
        mr.expires_at,
        mr.approved_at,
        mr.declined_at
    FROM public.money_requests mr
    LEFT JOIN public.user_profiles up ON mr.requester_id = up.user_id
    WHERE 
        (mr.requester_id = p_user_id OR mr.recipient_id = p_user_id OR 
         mr.recipient_phone IN (
             SELECT phone_number FROM public.user_profiles 
             WHERE user_id = p_user_id
         ))
        AND (p_status IS NULL OR mr.status = p_status)
    ORDER BY mr.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_money_requests TO authenticated;
