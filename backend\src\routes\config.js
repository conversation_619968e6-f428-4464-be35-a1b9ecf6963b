/**
 * Secure Configuration Routes for JiraniPay
 * 
 * Provides secure configuration endpoints for mobile app initialization
 */

const express = require('express');
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for configuration endpoints
const configRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many configuration requests',
    retryAfter: 900 // 15 minutes in seconds
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all config routes
router.use(configRateLimit);

// Middleware to validate device fingerprint
const validateDeviceFingerprint = (req, res, next) => {
  const deviceId = req.headers['x-device-id'];
  const appVersion = req.headers['x-app-version'];
  const platform = req.headers['x-platform'];

  if (!deviceId || !appVersion || !platform) {
    return res.status(400).json({
      success: false,
      error: 'Missing required device headers',
      required: ['X-Device-ID', 'X-App-Version', 'X-Platform']
    });
  }

  // Store device info for logging
  req.deviceInfo = { deviceId, appVersion, platform };
  next();
};

// Middleware to validate request body
const validateConfigRequest = (req, res, next) => {
  const { environment, timestamp, deviceFingerprint } = req.body;

  if (!environment || !timestamp || !deviceFingerprint) {
    return res.status(400).json({
      success: false,
      error: 'Missing required request parameters',
      required: ['environment', 'timestamp', 'deviceFingerprint']
    });
  }

  // Validate timestamp (within last 5 minutes)
  const now = Date.now();
  const requestTime = parseInt(timestamp);
  const timeDiff = Math.abs(now - requestTime);
  
  if (timeDiff > 5 * 60 * 1000) { // 5 minutes
    return res.status(400).json({
      success: false,
      error: 'Request timestamp too old',
      maxAge: '5 minutes'
    });
  }

  req.configRequest = { environment, timestamp, deviceFingerprint };
  next();
};

/**
 * GET /api/v1/config/supabase
 * Returns Supabase configuration for the mobile app
 */
router.post('/supabase', validateDeviceFingerprint, validateConfigRequest, async (req, res) => {
  try {
    const { environment } = req.configRequest;
    const { deviceId, appVersion, platform } = req.deviceInfo;

    logger.info('Supabase config request', {
      environment,
      deviceId: deviceId.substring(0, 8) + '...',
      appVersion,
      platform,
      ip: req.ip
    });

    // Get environment-specific Supabase configuration
    const supabaseConfig = getSupabaseConfig(environment);

    // Log successful configuration delivery
    logger.info('Supabase config delivered successfully', {
      environment,
      deviceId: deviceId.substring(0, 8) + '...',
      configVersion: supabaseConfig.version
    });

    res.json({
      success: true,
      config: supabaseConfig,
      timestamp: new Date().toISOString(),
      version: '2.0'
    });

  } catch (error) {
    logger.error('Error delivering Supabase config', {
      error: error.message,
      stack: error.stack,
      deviceId: req.deviceInfo?.deviceId?.substring(0, 8) + '...'
    });

    res.status(500).json({
      success: false,
      error: 'Configuration service temporarily unavailable',
      retryAfter: 60 // 1 minute
    });
  }
});

/**
 * POST /api/v1/config/api_endpoints
 * Returns API endpoints configuration
 */
router.post('/api_endpoints', validateDeviceFingerprint, validateConfigRequest, async (req, res) => {
  try {
    const { environment } = req.configRequest;
    const { deviceId, appVersion, platform } = req.deviceInfo;

    logger.info('API endpoints config request', {
      environment,
      deviceId: deviceId.substring(0, 8) + '...',
      appVersion,
      platform,
      ip: req.ip
    });

    // Get environment-specific API endpoints
    const apiConfig = getApiEndpointsConfig(environment);

    res.json({
      success: true,
      config: apiConfig,
      timestamp: new Date().toISOString(),
      version: '2.0'
    });

  } catch (error) {
    logger.error('Error delivering API endpoints config', {
      error: error.message,
      deviceId: req.deviceInfo?.deviceId?.substring(0, 8) + '...'
    });

    res.status(500).json({
      success: false,
      error: 'Configuration service temporarily unavailable',
      retryAfter: 60
    });
  }
});

/**
 * POST /api/v1/config/hsm
 * Returns HSM configuration
 */
router.post('/hsm', validateDeviceFingerprint, validateConfigRequest, async (req, res) => {
  try {
    const { environment } = req.configRequest;
    const { deviceId, appVersion, platform } = req.deviceInfo;

    logger.info('HSM config request', {
      environment,
      deviceId: deviceId.substring(0, 8) + '...',
      appVersion,
      platform,
      ip: req.ip
    });

    // Get environment-specific HSM configuration
    const hsmConfig = getHSMConfig(environment);

    res.json({
      success: true,
      config: hsmConfig,
      timestamp: new Date().toISOString(),
      version: '2.0'
    });

  } catch (error) {
    logger.error('Error delivering HSM config', {
      error: error.message,
      deviceId: req.deviceInfo?.deviceId?.substring(0, 8) + '...'
    });

    res.status(500).json({
      success: false,
      error: 'Configuration service temporarily unavailable',
      retryAfter: 60
    });
  }
});

/**
 * GET /api/v1/config/health
 * Health check endpoint for configuration service
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0',
    uptime: process.uptime()
  });
});

// Configuration helper functions

function getSupabaseConfig(environment) {
  const isProduction = environment === 'production';
  
  if (isProduction) {
    // Production Supabase configuration - MUST use environment variables
    const url = process.env.SUPABASE_URL;
    const anonKey = process.env.SUPABASE_ANON_KEY;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!url || !anonKey || !serviceRoleKey) {
      throw new Error('Missing required production Supabase environment variables: SUPABASE_URL, SUPABASE_ANON_KEY, and SUPABASE_SERVICE_ROLE_KEY');
    }

    return {
      url,
      anonKey,
      serviceRoleKey,
      version: '2.0',
      environment: 'production'
    };
  } else {
    // Development Supabase configuration - MUST use environment variables
    const url = process.env.SUPABASE_URL;
    const anonKey = process.env.SUPABASE_ANON_KEY;

    if (!url || !anonKey) {
      throw new Error('Missing required Supabase environment variables: SUPABASE_URL and SUPABASE_ANON_KEY');
    }

    return {
      url,
      anonKey,
      version: '2.0',
      environment: 'development'
    };
  }
}

function getApiEndpointsConfig(environment) {
  const isProduction = environment === 'production';
  
  return {
    baseUrl: isProduction ? 'https://api.jiranipay.com' : 'http://localhost:3001',
    version: 'v1',
    timeout: 30000,
    retryAttempts: 3,
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      wallets: '/api/v1/wallets',
      transactions: '/api/v1/transactions',
      bills: '/api/v1/bills',
      config: '/api/v1/config'
    },
    environment: environment
  };
}

function getHSMConfig(environment) {
  const isProduction = environment === 'production';
  
  return {
    enabled: isProduction,
    provider: isProduction ? 'aws-cloudhsm' : 'local-secure',
    keyRotationInterval: 24 * 60 * 60 * 1000, // 24 hours
    backupEnabled: true,
    encryptionAlgorithm: 'AES-256-GCM',
    keyDerivationIterations: 100000,
    environment: environment
  };
}

module.exports = router;
