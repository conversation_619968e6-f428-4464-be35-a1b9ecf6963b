/**
 * Admin Dashboard Routes
 * Main dashboard with overview statistics and quick actions
 */

const express = require('express');
const { async<PERSON>and<PERSON> } = require('../../middleware/errorHandler');
const { adminAuthMiddleware, requirePermission } = require('../../middleware/adminAuth');
const databaseService = require('../../services/database');
const monitoringService = require('../../services/monitoringService');
const analyticsService = require('../../services/analyticsService');
const redisService = require('../../services/redis');
const logger = require('../../utils/logger');

const router = express.Router();

// Apply admin authentication to all routes
router.use(adminAuthMiddleware);

/**
 * @route   GET /api/v1/admin/dashboard
 * @desc    Get admin dashboard overview
 * @access  Admin
 */
router.get('/', [
  requirePermission('dashboard:read')
], asyncHandler(async (req, res) => {
  try {
    const supabase = databaseService.getSupabase();
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get basic statistics
    const [
      { count: totalUsers },
      { count: totalTransactions },
      { count: todayTransactions },
      { count: activeWallets }
    ] = await Promise.all([
      supabase.from('user_profiles').select('*', { count: 'exact', head: true }),
      supabase.from('transactions').select('*', { count: 'exact', head: true }),
      supabase.from('transactions').select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString()),
      supabase.from('wallets').select('*', { count: 'exact', head: true })
        .eq('is_active', true)
    ]);

    // Get transaction volume for today
    const { data: todayVolume } = await supabase
      .from('transactions')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', today.toISOString());

    const totalVolumeToday = todayVolume?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

    // Get monthly volume
    const { data: monthlyVolume } = await supabase
      .from('transactions')
      .select('amount')
      .eq('status', 'completed')
      .gte('created_at', thisMonth.toISOString());

    const totalVolumeMonth = monthlyVolume?.reduce((sum, tx) => sum + tx.amount, 0) || 0;

    // Get recent transactions
    const { data: recentTransactions } = await supabase
      .from('transactions')
      .select(`
        id,
        transaction_reference,
        type,
        amount,
        currency,
        status,
        created_at,
        from_user:user_profiles!from_user_id(full_name, phone_number),
        to_user:user_profiles!to_user_id(full_name, phone_number)
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    // Get system health
    const systemHealth = await monitoringService.getHealthStatus();

    // Get recent alerts
    const recentAlerts = await redisService.lrange('recent_alerts', 0, 4) || [];

    // Get user growth (last 7 days)
    const { data: userGrowth } = await supabase
      .from('user_profiles')
      .select('created_at')
      .gte('created_at', new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: true });

    // Process user growth data
    const growthByDay = {};
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateKey = date.toISOString().split('T')[0];
      growthByDay[dateKey] = 0;
    }

    userGrowth?.forEach(user => {
      const dateKey = user.created_at.split('T')[0];
      if (growthByDay[dateKey] !== undefined) {
        growthByDay[dateKey]++;
      }
    });

    // Get transaction success rate
    const { data: transactionStats } = await supabase
      .from('transactions')
      .select('status')
      .gte('created_at', today.toISOString());

    const successfulToday = transactionStats?.filter(tx => tx.status === 'completed').length || 0;
    const successRate = todayTransactions > 0 ? ((successfulToday / todayTransactions) * 100).toFixed(1) : 100;

    const dashboardData = {
      overview: {
        totalUsers: totalUsers || 0,
        totalTransactions: totalTransactions || 0,
        todayTransactions: todayTransactions || 0,
        activeWallets: activeWallets || 0,
        totalVolumeToday,
        totalVolumeMonth,
        successRate: parseFloat(successRate),
        lastUpdated: now.toISOString()
      },
      recentTransactions: (recentTransactions || []).map(tx => ({
        id: tx.id,
        reference: tx.transaction_reference,
        type: tx.type,
        amount: tx.amount,
        currency: tx.currency,
        status: tx.status,
        fromUser: tx.from_user?.full_name || 'Unknown',
        toUser: tx.to_user?.full_name || 'System',
        createdAt: tx.created_at
      })),
      systemHealth: {
        overall: systemHealth.overall,
        services: Object.keys(systemHealth.services || {}).map(service => ({
          name: service,
          status: systemHealth.services[service].status
        }))
      },
      alerts: recentAlerts.map(alert => ({
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp
      })),
      userGrowth: Object.entries(growthByDay).map(([date, count]) => ({
        date,
        count
      })),
      quickStats: {
        newUsersToday: userGrowth?.filter(user => 
          user.created_at >= today.toISOString()
        ).length || 0,
        pendingTransactions: transactionStats?.filter(tx => 
          tx.status === 'pending'
        ).length || 0,
        failedTransactions: transactionStats?.filter(tx => 
          tx.status === 'failed'
        ).length || 0
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Failed to get dashboard data:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/dashboard/quick-actions
 * @desc    Get available quick actions for admin
 * @access  Admin
 */
router.get('/quick-actions', [
  requirePermission('dashboard:read')
], asyncHandler(async (req, res) => {
  try {
    const quickActions = [
      {
        id: 'view_users',
        title: 'Manage Users',
        description: 'View and manage user accounts',
        icon: 'users',
        url: '/admin/users',
        permission: 'users:read'
      },
      {
        id: 'view_transactions',
        title: 'Monitor Transactions',
        description: 'View and manage transactions',
        icon: 'transactions',
        url: '/admin/transactions',
        permission: 'transactions:read'
      },
      {
        id: 'system_monitoring',
        title: 'System Health',
        description: 'Monitor system performance',
        icon: 'monitor',
        url: '/admin/monitoring',
        permission: 'monitoring:read'
      },
      {
        id: 'analytics',
        title: 'Analytics',
        description: 'View business analytics',
        icon: 'analytics',
        url: '/admin/analytics',
        permission: 'analytics:read'
      },
      {
        id: 'reports',
        title: 'Generate Reports',
        description: 'Create business reports',
        icon: 'reports',
        url: '/admin/reports',
        permission: 'reports:create'
      },
      {
        id: 'settings',
        title: 'System Settings',
        description: 'Configure system settings',
        icon: 'settings',
        url: '/admin/settings',
        permission: 'settings:update'
      }
    ];

    // Filter actions based on user permissions
    const userPermissions = req.admin.permissions;
    const availableActions = quickActions.filter(action => 
      userPermissions.includes(action.permission) || userPermissions.includes('*')
    );

    res.json({
      success: true,
      data: {
        actions: availableActions
      }
    });
  } catch (error) {
    logger.error('Failed to get quick actions:', error);
    throw error;
  }
}));

/**
 * @route   GET /api/v1/admin/dashboard/notifications
 * @desc    Get admin notifications and alerts
 * @access  Admin
 */
router.get('/notifications', [
  requirePermission('dashboard:read')
], asyncHandler(async (req, res) => {
  try {
    // Get recent alerts from Redis
    const alerts = await redisService.lrange('recent_alerts', 0, 19) || [];
    
    // Get system notifications
    const systemNotifications = [
      {
        id: 'system_health',
        type: 'info',
        title: 'System Status',
        message: 'All systems operational',
        timestamp: new Date().toISOString(),
        read: false
      }
    ];

    // Get pending admin tasks
    const supabase = databaseService.getSupabase();
    
    // Check for pending KYC verifications
    const { count: pendingKyc } = await supabase
      .from('user_profiles')
      .select('*', { count: 'exact', head: true })
      .eq('kyc_status', 'pending');

    // Check for failed transactions needing review
    const { count: failedTransactions } = await supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'failed')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    const adminTasks = [];
    
    if (pendingKyc > 0) {
      adminTasks.push({
        id: 'pending_kyc',
        type: 'warning',
        title: 'KYC Verifications Pending',
        message: `${pendingKyc} users waiting for KYC verification`,
        timestamp: new Date().toISOString(),
        action: '/admin/users?kyc_status=pending',
        read: false
      });
    }

    if (failedTransactions > 0) {
      adminTasks.push({
        id: 'failed_transactions',
        type: 'error',
        title: 'Failed Transactions',
        message: `${failedTransactions} transactions failed in the last 24 hours`,
        timestamp: new Date().toISOString(),
        action: '/admin/transactions?status=failed',
        read: false
      });
    }

    const allNotifications = [
      ...systemNotifications,
      ...adminTasks,
      ...alerts.map(alert => ({
        id: `alert_${Date.now()}_${Math.random()}`,
        type: alert.severity || 'info',
        title: alert.type || 'System Alert',
        message: alert.message,
        timestamp: alert.timestamp,
        read: false
      }))
    ].slice(0, 20); // Limit to 20 notifications

    res.json({
      success: true,
      data: {
        notifications: allNotifications,
        unreadCount: allNotifications.filter(n => !n.read).length
      }
    });
  } catch (error) {
    logger.error('Failed to get admin notifications:', error);
    throw error;
  }
}));

module.exports = router;
