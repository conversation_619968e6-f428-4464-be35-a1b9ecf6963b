#!/usr/bin/env node

/**
 * Network Fixes Validation Script for JiraniPay
 * 
 * This script validates that all network request issues have been resolved:
 * - Enhanced network service integration
 * - Production mode compatibility
 * - Authentication token handling
 * - Error recovery mechanisms
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 JiraniPay Network Fixes Validation');
console.log('====================================\n');

// File paths to check
const filesToCheck = [
  {
    path: 'services/enhancedNetworkService.js',
    description: 'Enhanced Network Service',
    required: true,
    checks: [
      'class EnhancedNetworkService',
      'refreshAuthToken',
      'makeSupabaseRequest',
      'getUserPreferences',
      'uploadFile'
    ]
  },
  {
    path: 'services/preferenceService.js',
    description: 'Preference Service',
    required: true,
    checks: [
      'import enhancedNetworkService',
      'enhancedNetworkService.getUserPreferences',
      'enhancedNetworkService.updateUserPreferences'
    ]
  },
  {
    path: 'services/profileManagementService.js',
    description: 'Profile Management Service',
    required: true,
    checks: [
      'import enhancedNetworkService',
      'enhancedNetworkService.uploadFile'
    ]
  },
  {
    path: 'services/databaseService.js',
    description: 'Database Service',
    required: true,
    checks: [
      '!__DEV__' // Should NOT contain __DEV__ checks
    ]
  },
  {
    path: 'App.js',
    description: 'Main App Component',
    required: true,
    checks: [
      'import enhancedNetworkService',
      'enhancedNetworkService.initialize'
    ]
  }
];

// Documentation files to check
const docsToCheck = [
  'docs/NETWORK_REQUEST_TROUBLESHOOTING.md',
  'scripts/diagnose-network-issues.js',
  'scripts/validate-network-fixes.js'
];

// Package.json scripts to check
const scriptsToCheck = [
  'test-network',
  'test-init',
  'validate-production',
  'validate-security'
];

/**
 * Check if file exists and contains required content
 */
function validateFile(fileInfo) {
  const filePath = path.join(process.cwd(), fileInfo.path);
  
  console.log(`📁 Checking ${fileInfo.description}...`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`   ❌ File not found: ${fileInfo.path}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const results = [];
  
  for (const check of fileInfo.checks) {
    if (check.startsWith('!')) {
      // Negative check - should NOT contain
      const pattern = check.substring(1);
      const found = content.includes(pattern);
      results.push({
        check: `Should NOT contain: ${pattern}`,
        passed: !found
      });
    } else {
      // Positive check - should contain
      const found = content.includes(check);
      results.push({
        check: `Should contain: ${check}`,
        passed: found
      });
    }
  }
  
  const allPassed = results.every(r => r.passed);
  
  results.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`   ${status} ${result.check}`);
  });
  
  if (allPassed) {
    console.log(`   ✅ ${fileInfo.description} validation passed\n`);
  } else {
    console.log(`   ❌ ${fileInfo.description} validation failed\n`);
  }
  
  return allPassed;
}

/**
 * Check documentation files
 */
function validateDocumentation() {
  console.log('📚 Checking Documentation...');
  
  let allPassed = true;
  
  for (const docPath of docsToCheck) {
    const fullPath = path.join(process.cwd(), docPath);
    if (fs.existsSync(fullPath)) {
      console.log(`   ✅ ${docPath} exists`);
    } else {
      console.log(`   ❌ ${docPath} missing`);
      allPassed = false;
    }
  }
  
  console.log(allPassed ? '   ✅ Documentation validation passed\n' : '   ❌ Documentation validation failed\n');
  return allPassed;
}

/**
 * Check package.json scripts
 */
function validatePackageScripts() {
  console.log('📦 Checking Package Scripts...');
  
  const packagePath = path.join(process.cwd(), 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('   ❌ package.json not found\n');
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  const scripts = packageJson.scripts || {};
  
  let allPassed = true;
  
  for (const script of scriptsToCheck) {
    if (scripts[script]) {
      console.log(`   ✅ Script "${script}" exists`);
    } else {
      console.log(`   ❌ Script "${script}" missing`);
      allPassed = false;
    }
  }
  
  console.log(allPassed ? '   ✅ Package scripts validation passed\n' : '   ❌ Package scripts validation failed\n');
  return allPassed;
}

/**
 * Check environment configuration
 */
function validateEnvironment() {
  console.log('🔧 Checking Environment Configuration...');
  
  const envFiles = [
    '.env.production.local',
    '.env.development.local'
  ];
  
  let allPassed = true;
  
  for (const envFile of envFiles) {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      console.log(`   ✅ ${envFile} exists`);
      
      const content = fs.readFileSync(envPath, 'utf8');
      const hasSupabaseUrl = content.includes('SUPABASE_URL');
      const hasSupabaseKey = content.includes('SUPABASE_ANON_KEY');
      
      if (hasSupabaseUrl && hasSupabaseKey) {
        console.log(`   ✅ ${envFile} has Supabase configuration`);
      } else {
        console.log(`   ⚠️ ${envFile} missing Supabase configuration`);
      }
    } else {
      console.log(`   ❌ ${envFile} missing`);
      allPassed = false;
    }
  }
  
  console.log(allPassed ? '   ✅ Environment validation passed\n' : '   ❌ Environment validation failed\n');
  return allPassed;
}

/**
 * Run comprehensive validation
 */
async function runValidation() {
  console.log('🚀 Starting comprehensive network fixes validation...\n');
  
  const results = {
    files: [],
    documentation: false,
    scripts: false,
    environment: false
  };
  
  // Validate files
  console.log('📁 VALIDATING FILES');
  console.log('===================');
  for (const fileInfo of filesToCheck) {
    const passed = validateFile(fileInfo);
    results.files.push({ name: fileInfo.description, passed });
  }
  
  // Validate documentation
  console.log('📚 VALIDATING DOCUMENTATION');
  console.log('===========================');
  results.documentation = validateDocumentation();
  
  // Validate package scripts
  console.log('📦 VALIDATING PACKAGE SCRIPTS');
  console.log('==============================');
  results.scripts = validatePackageScripts();
  
  // Validate environment
  console.log('🔧 VALIDATING ENVIRONMENT');
  console.log('=========================');
  results.environment = validateEnvironment();
  
  // Summary
  console.log('📊 VALIDATION SUMMARY');
  console.log('=====================');
  
  const filesPassed = results.files.filter(f => f.passed).length;
  const filesTotal = results.files.length;
  
  console.log(`📁 Files: ${filesPassed}/${filesTotal} passed`);
  console.log(`📚 Documentation: ${results.documentation ? 'PASSED' : 'FAILED'}`);
  console.log(`📦 Scripts: ${results.scripts ? 'PASSED' : 'FAILED'}`);
  console.log(`🔧 Environment: ${results.environment ? 'PASSED' : 'FAILED'}`);
  
  const allPassed = filesPassed === filesTotal && 
                   results.documentation && 
                   results.scripts && 
                   results.environment;
  
  console.log(`\n🎯 OVERALL RESULT: ${allPassed ? '✅ ALL VALIDATIONS PASSED' : '❌ SOME VALIDATIONS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 NETWORK FIXES SUCCESSFULLY IMPLEMENTED!');
    console.log('==========================================');
    console.log('✅ Enhanced network service integrated');
    console.log('✅ Production mode compatibility ensured');
    console.log('✅ Authentication token handling improved');
    console.log('✅ Error recovery mechanisms implemented');
    console.log('✅ Comprehensive documentation provided');
    console.log('✅ Diagnostic tools available');
    
    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Test the app: npm start');
    console.log('2. Run diagnostics: npm run test-network');
    console.log('3. Validate production: npm run validate-production');
    console.log('4. Monitor network requests in the app console');
    
  } else {
    console.log('\n⚠️ ISSUES FOUND:');
    console.log('================');
    
    if (filesPassed < filesTotal) {
      console.log('❌ Some files failed validation - check the details above');
    }
    if (!results.documentation) {
      console.log('❌ Documentation files missing or incomplete');
    }
    if (!results.scripts) {
      console.log('❌ Package.json scripts missing');
    }
    if (!results.environment) {
      console.log('❌ Environment configuration issues');
    }
    
    console.log('\n🔧 RECOMMENDED ACTIONS:');
    console.log('1. Fix the failed validations above');
    console.log('2. Re-run this script: npm run validate-network-fixes');
    console.log('3. Test network functionality: npm run test-network');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the validation
runValidation().catch(error => {
  console.error('❌ Validation script failed:', error);
  process.exit(1);
});
