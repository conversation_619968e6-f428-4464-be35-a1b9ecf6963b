/**
 * Production Services Configuration
 * This file contains all production-ready service configurations
 * for real API integrations and financial services
 */

import { isProductionMode } from './environment';

// Production API Keys and Credentials (to be set in environment variables)
const PRODUCTION_CREDENTIALS = {
  // MTN Mobile Money API
  mtn: {
    apiKey: process.env.MTN_API_KEY || '',
    subscriptionKey: process.env.MTN_SUBSCRIPTION_KEY || '',
    environment: 'production', // 'sandbox' for testing
    baseUrl: 'https://api.mtn.com/v1',
  },
  
  // Airtel Money API
  airtel: {
    clientId: process.env.AIRTEL_CLIENT_ID || '',
    clientSecret: process.env.AIRTEL_CLIENT_SECRET || '',
    environment: 'production',
    baseUrl: 'https://api.airtel.com/v1',
  },
  
  // UMEME (Electricity) API
  umeme: {
    apiKey: process.env.UMEME_API_KEY || '',
    merchantId: process.env.UMEME_MERCHANT_ID || '',
    baseUrl: 'https://api.umeme.co.ug/v1',
  },
  
  // DStv API
  dstv: {
    apiKey: process.env.DSTV_API_KEY || '',
    merchantCode: process.env.DSTV_MERCHANT_CODE || '',
    baseUrl: 'https://api.dstv.com/v1',
  },
  
  // Banking APIs
  banking: {
    // Stanbic Bank API
    stanbic: {
      clientId: process.env.STANBIC_CLIENT_ID || '',
      clientSecret: process.env.STANBIC_CLIENT_SECRET || '',
      baseUrl: 'https://api.stanbicbank.co.ug/v1',
    },
    
    // Centenary Bank API
    centenary: {
      apiKey: process.env.CENTENARY_API_KEY || '',
      baseUrl: 'https://api.centenarybank.co.ug/v1',
    },
  },
  
  // Currency Exchange API
  exchangeRate: {
    apiKey: process.env.EXCHANGE_RATE_API_KEY || '',
    baseUrl: 'https://api.exchangerate-api.com/v4',
  },
  
  // KYC and Compliance
  kyc: {
    apiKey: process.env.KYC_API_KEY || '',
    baseUrl: 'https://api.jiranipay.com/kyc',
  },
  
  // Fraud Detection
  fraudDetection: {
    apiKey: process.env.FRAUD_DETECTION_API_KEY || '',
    baseUrl: 'https://api.jiranipay.com/fraud',
  },
};

// Production Service Endpoints
export const PRODUCTION_ENDPOINTS = {
  // Authentication
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    verify: '/auth/verify',
    refresh: '/auth/refresh',
    logout: '/auth/logout',
    biometric: '/auth/biometric',
  },
  
  // User Management
  user: {
    profile: '/user/profile',
    kyc: '/user/kyc',
    documents: '/user/documents',
    verification: '/user/verification',
  },
  
  // Wallet Operations
  wallet: {
    balance: '/wallet/balance',
    transactions: '/wallet/transactions',
    transfer: '/wallet/transfer',
    topup: '/wallet/topup',
    withdraw: '/wallet/withdraw',
  },
  
  // Bill Payments
  bills: {
    providers: '/bills/providers',
    validate: '/bills/validate',
    pay: '/bills/pay',
    history: '/bills/history',
    receipt: '/bills/receipt',
  },
  
  // Mobile Money
  mobileMoney: {
    mtn: {
      requestToPay: '/mobile-money/mtn/request-to-pay',
      paymentStatus: '/mobile-money/mtn/payment-status',
      balance: '/mobile-money/mtn/balance',
    },
    airtel: {
      requestToPay: '/mobile-money/airtel/request-to-pay',
      paymentStatus: '/mobile-money/airtel/payment-status',
      balance: '/mobile-money/airtel/balance',
    },
  },
  
  // Banking
  banking: {
    accountVerification: '/banking/verify-account',
    bankTransfer: '/banking/transfer',
    accountBalance: '/banking/balance',
    transactionHistory: '/banking/transactions',
  },
  
  // Compliance and Reporting
  compliance: {
    amlCheck: '/compliance/aml-check',
    sanctionScreening: '/compliance/sanction-screening',
    transactionReport: '/compliance/transaction-report',
    auditTrail: '/compliance/audit-trail',
  },
};

// Production Security Configuration
export const PRODUCTION_SECURITY = {
  encryption: {
    algorithm: 'AES-256-GCM',
    keyLength: 32,
    ivLength: 16,
  },
  
  jwt: {
    algorithm: 'RS256',
    expiresIn: '15m',
    refreshExpiresIn: '7d',
  },
  
  biometric: {
    enableFaceID: true,
    enableTouchID: true,
    enableFingerprint: true,
    fallbackToPin: true,
  },
  
  transaction: {
    requireBiometricAbove: 500000, // UGX 500K
    requireOTPAbove: 1000000, // UGX 1M
    maxDailyLimit: ********, // UGX 50M
    maxSingleTransaction: ********, // UGX 10M
  },
};

// Production Validation Rules
export const PRODUCTION_VALIDATION = {
  phoneNumber: {
    mtn: /^256(77|78|76)\d{7}$/,
    airtel: /^256(70|75|74)\d{7}$/,
    utl: /^256(71|72)\d{7}$/,
  },
  
  accountNumber: {
    minLength: 8,
    maxLength: 20,
    allowedCharacters: /^[0-9A-Za-z]+$/,
  },
  
  amount: {
    min: 1000, // UGX 1,000
    max: ********, // UGX 10M
    currency: 'UGX',
  },
  
  kyc: {
    requiredDocuments: ['nationalId', 'passport'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedFormats: ['jpg', 'jpeg', 'png', 'pdf'],
  },
};

// Function to get production credentials
export const getProductionCredentials = (service) => {
  if (!isProductionMode()) {
    console.warn('⚠️ Attempting to access production credentials in development mode');
    return null;
  }
  
  return PRODUCTION_CREDENTIALS[service] || null;
};

// Function to validate production configuration (PRODUCTION FIX)
export const validateProductionConfig = () => {
  if (!isProductionMode()) {
    return { isValid: true, warnings: [], errors: [] }; // Skip validation in development
  }

  const errors = [];
  const warnings = [];

  // Check required environment variables
  const requiredEnvVars = [
    'MTN_API_KEY',
    'AIRTEL_CLIENT_ID',
    'UMEME_API_KEY',
    'DSTV_API_KEY',
    'EXCHANGE_RATE_API_KEY',
  ];

  requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      warnings.push(`Missing environment variable: ${envVar}`);
    }
  });

  if (warnings.length > 0) {
    console.error('🚨 PRODUCTION CONFIGURATION WARNINGS:');
    warnings.forEach(warning => console.error(`- ${warning}`));
    console.log('⚠️ Some API integrations may not work without proper credentials');
    console.log('💡 Wallet creation and basic features will still work');
  }

  // Only throw error for critical missing configurations
  // For now, we'll allow the app to run with warnings
  return {
    isValid: true,
    warnings,
    errors,
    hasWarnings: warnings.length > 0,
    message: warnings.length > 0 ? 'Some API credentials missing but core features available' : 'All configurations valid'
  };
};

export default {
  credentials: PRODUCTION_CREDENTIALS,
  endpoints: PRODUCTION_ENDPOINTS,
  security: PRODUCTION_SECURITY,
  validation: PRODUCTION_VALIDATION,
  getCredentials: getProductionCredentials,
  validate: validateProductionConfig,
};
