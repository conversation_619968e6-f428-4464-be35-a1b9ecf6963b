import contactService from '../services/contactService';
import { validatePhoneNumber, formatPhoneNumber } from '../utils/phoneValidation';

// Mock dependencies
jest.mock('../services/supabaseClient', () => ({
  from: jest.fn(() => ({
    select: jest.fn(() => ({
      eq: jest.fn(() => ({
        order: jest.fn(() => ({
          data: [],
          error: null
        }))
      }))
    })),
    insert: jest.fn(() => ({
      select: jest.fn(() => ({
        single: jest.fn(() => ({
          data: { id: 'test-id', contact_name: 'Test Contact' },
          error: null
        }))
      }))
    })),
    update: jest.fn(() => ({
      eq: jest.fn(() => ({
        data: { id: 'test-id' },
        error: null
      }))
    })),
    delete: jest.fn(() => ({
      eq: jest.fn(() => ({
        eq: jest.fn(() => ({
          error: null
        }))
      }))
    }))
  }))
}));

jest.mock('../services/authService', () => ({
  getCurrentUser: jest.fn(() => ({ id: 'test-user-id' }))
}));

jest.mock('expo-contacts', () => ({
  requestPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getContactsAsync: jest.fn(() => ({
    data: [
      {
        id: '1',
        name: 'John Doe',
        phoneNumbers: [{ number: '+256777123456' }]
      },
      {
        id: '2',
        name: 'Jane Smith',
        phoneNumbers: [{ number: '0701234567' }]
      }
    ]
  })),
  Fields: {
    Name: 'name',
    PhoneNumbers: 'phoneNumbers',
    ID: 'id'
  },
  SortTypes: {
    FirstName: 'firstName'
  }
}));

describe('ContactService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    contactService.deviceContacts = [];
    contactService.favoriteContacts = [];
    contactService.contactsLoaded = false;
    contactService.permissionGranted = false;
  });

  describe('requestContactsPermission', () => {
    it('should request and grant contacts permission', async () => {
      const result = await contactService.requestContactsPermission();
      
      expect(result.success).toBe(true);
      expect(result.status).toBe('granted');
      expect(contactService.permissionGranted).toBe(true);
    });

    it('should handle permission denial', async () => {
      const Contacts = require('expo-contacts');
      Contacts.requestPermissionsAsync.mockResolvedValueOnce({ status: 'denied' });
      
      const result = await contactService.requestContactsPermission();
      
      expect(result.success).toBe(false);
      expect(result.status).toBe('denied');
      expect(contactService.permissionGranted).toBe(false);
    });
  });

  describe('loadDeviceContacts', () => {
    it('should load and process device contacts', async () => {
      contactService.permissionGranted = true;
      
      const result = await contactService.loadDeviceContacts();
      
      expect(result.success).toBe(true);
      expect(result.contacts).toHaveLength(2);
      expect(result.contacts[0].name).toBe('Jane Smith'); // Sorted alphabetically
      expect(result.contacts[1].name).toBe('John Doe');
      expect(contactService.contactsLoaded).toBe(true);
    });

    it('should request permission if not granted', async () => {
      contactService.permissionGranted = false;
      
      const result = await contactService.loadDeviceContacts();
      
      expect(result.success).toBe(true);
      expect(contactService.permissionGranted).toBe(true);
    });
  });

  describe('processDeviceContacts', () => {
    it('should process contacts with valid phone numbers', () => {
      const rawContacts = [
        {
          id: '1',
          name: 'Test User',
          phoneNumbers: [
            { number: '+256777123456' },
            { number: 'invalid-number' }
          ]
        }
      ];
      
      const processed = contactService.processDeviceContacts(rawContacts);
      
      expect(processed).toHaveLength(1);
      expect(processed[0].name).toBe('Test User');
      expect(processed[0].phoneNumber).toBe('+256777123456');
    });

    it('should skip contacts without phone numbers', () => {
      const rawContacts = [
        { id: '1', name: 'No Phone' },
        { id: '2', name: 'Empty Phones', phoneNumbers: [] }
      ];
      
      const processed = contactService.processDeviceContacts(rawContacts);
      
      expect(processed).toHaveLength(0);
    });

    it('should remove duplicate phone numbers', () => {
      const rawContacts = [
        {
          id: '1',
          name: 'User 1',
          phoneNumbers: [{ number: '+256777123456' }]
        },
        {
          id: '2',
          name: 'User 2',
          phoneNumbers: [{ number: '+256777123456' }]
        }
      ];
      
      const processed = contactService.processDeviceContacts(rawContacts);
      
      expect(processed).toHaveLength(1);
    });
  });

  describe('searchContacts', () => {
    beforeEach(() => {
      contactService.deviceContacts = [
        {
          id: '1',
          name: 'John Doe',
          phoneNumber: '+256777123456',
          originalPhone: '0777123456'
        },
        {
          id: '2',
          name: 'Jane Smith',
          phoneNumber: '+256701234567',
          originalPhone: '0701234567'
        }
      ];
    });

    it('should return all contacts for empty query', () => {
      const results = contactService.searchContacts('');
      expect(results).toHaveLength(2);
    });

    it('should search by name', () => {
      const results = contactService.searchContacts('john');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('John Doe');
    });

    it('should search by phone number', () => {
      const results = contactService.searchContacts('777');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('John Doe');
    });

    it('should be case insensitive', () => {
      const results = contactService.searchContacts('JANE');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('Jane Smith');
    });
  });

  describe('addToFavorites', () => {
    const mockContact = {
      id: '1',
      name: 'Test Contact',
      phoneNumber: '+256777123456'
    };

    it('should add new contact to favorites', async () => {
      const result = await contactService.addToFavorites(mockContact);
      
      expect(result.success).toBe(true);
      expect(contactService.favoriteContacts).toHaveLength(1);
    });

    it('should update frequency for existing favorite', async () => {
      // Add existing favorite
      contactService.favoriteContacts = [{
        id: 'existing-id',
        contact_phone: '+256777123456',
        contact_name: 'Test Contact',
        frequency_count: 1
      }];
      
      const result = await contactService.addToFavorites(mockContact);
      
      expect(result.success).toBe(true);
    });
  });

  describe('removeFromFavorites', () => {
    beforeEach(() => {
      contactService.favoriteContacts = [{
        id: 'test-id',
        contact_phone: '+256777123456',
        contact_name: 'Test Contact'
      }];
      contactService.deviceContacts = [{
        id: '1',
        phoneNumber: '+256777123456',
        isFavorite: true
      }];
    });

    it('should remove contact from favorites', async () => {
      const result = await contactService.removeFromFavorites('+256777123456');
      
      expect(result.success).toBe(true);
      expect(contactService.favoriteContacts).toHaveLength(0);
    });
  });

  describe('getAllContacts', () => {
    beforeEach(() => {
      contactService.deviceContacts = [
        { id: '1', name: 'Regular Contact', isFavorite: false },
        { id: '2', name: 'Favorite Contact', isFavorite: true },
        { id: '3', name: 'Another Regular', isFavorite: false }
      ];
    });

    it('should sort favorites first', () => {
      const contacts = contactService.getAllContacts();
      
      expect(contacts[0].name).toBe('Favorite Contact');
      expect(contacts[0].isFavorite).toBe(true);
    });

    it('should sort non-favorites alphabetically', () => {
      const contacts = contactService.getAllContacts();
      
      expect(contacts[1].name).toBe('Another Regular');
      expect(contacts[2].name).toBe('Regular Contact');
    });
  });

  describe('initialize', () => {
    it('should initialize both device and favorite contacts', async () => {
      contactService.permissionGranted = true;
      
      const result = await contactService.initialize();
      
      expect(result.success).toBe(true);
      expect(result.contactsLoaded).toBe(true);
      expect(result.favoritesLoaded).toBe(true);
    });
  });
});
