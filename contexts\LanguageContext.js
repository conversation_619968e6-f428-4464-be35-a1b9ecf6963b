import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import {
  initializeI18n,
  changeLanguage,
  getCurrentLanguage,
  getCurrentLanguageConfig,
  getTimeBasedGreeting,
  isRTL,
  TRANSLATIONS,
  SUPPORTED_LANGUAGES
} from '../utils/i18n';

/**
 * Language Context for managing app-wide language state
 * Provides translation functions and language management
 */
const LanguageContext = createContext({
  currentLanguage: 'en',
  currentLanguageConfig: null,
  isRTL: false,
  t: () => '',
  getTimeBasedGreeting: () => '',
  changeLanguage: () => {},
  initializeLanguage: () => {},
});

/**
 * Language Provider Component
 * Wraps the app to provide language context to all components
 */
export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [currentLanguageConfig, setCurrentLanguageConfig] = useState(null);
  const [isRTLMode, setIsRTLMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentTranslations, setCurrentTranslations] = useState(TRANSLATIONS.en);

  /**
   * Initialize language system
   * @param {string} selectedCountryCode - Country code to determine default language
   */
  const initializeLanguage = async (selectedCountryCode = 'UG') => {
    try {
      console.log(`🌍 LanguageContext: Initializing language system for country ${selectedCountryCode}`);

      const language = await initializeI18n(selectedCountryCode);
      setCurrentLanguage(language);
      setCurrentLanguageConfig(getCurrentLanguageConfig());
      setIsRTLMode(isRTL());

      // Set initial translations
      const initialTranslations = TRANSLATIONS[language] || TRANSLATIONS.en;
      setCurrentTranslations(initialTranslations);

      setIsInitialized(true);
      console.log(`✅ LanguageContext: Initialized with language ${language}`);
    } catch (error) {
      console.error('❌ Error initializing language:', error);
      // Fallback to English
      setCurrentLanguage('en');
      setCurrentTranslations(TRANSLATIONS.en);
      setIsRTLMode(false);
      setIsInitialized(true);
    }
  };

  /**
   * Change app language
   * @param {string} languageCode - New language code
   */
  const handleLanguageChange = async (languageCode) => {
    try {
      console.log(`🌍 LanguageContext: Changing language to ${languageCode}`);

      await changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
      setCurrentLanguageConfig(getCurrentLanguageConfig());
      setIsRTLMode(isRTL());

      // Update translations state to trigger re-renders
      const newTranslations = TRANSLATIONS[languageCode] || TRANSLATIONS.en;
      setCurrentTranslations(newTranslations);

      console.log(`✅ LanguageContext: Language changed successfully to ${languageCode}`);
    } catch (error) {
      console.error('❌ Error changing language:', error);
    }
  };

  /**
   * Reactive translation function that triggers re-renders
   * @param {string} key - Translation key (e.g., 'auth.login', 'common.continue')
   * @param {Object} params - Parameters for string interpolation
   * @returns {string} - Translated string
   */
  const t = useCallback((key, params = {}) => {
    if (!key || typeof key !== 'string') {
      console.warn(`⚠️ Invalid translation key:`, key);
      return String(key || '');
    }

    const keys = key.split('.');
    let translation = currentTranslations;

    // Navigate through nested keys
    for (const k of keys) {
      if (translation && typeof translation === 'object') {
        translation = translation[k];
      } else {
        translation = undefined;
        break;
      }
    }

    // Fallback to English if translation not found
    if (translation === undefined || translation === null) {
      let fallback = TRANSLATIONS.en;
      for (const k of keys) {
        if (fallback && typeof fallback === 'object') {
          fallback = fallback[k];
        } else {
          fallback = undefined;
          break;
        }
      }
      translation = fallback;
    }

    // Ensure we always return a string, never an object
    if (typeof translation === 'object' && translation !== null) {
      console.warn(`⚠️ Translation key "${key}" returned an object instead of string. Keys:`, Object.keys(translation));
      return key; // Return the key as fallback
    }

    // Handle string interpolation
    if (typeof translation === 'string' && params && Object.keys(params).length > 0) {
      return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] !== undefined ? String(params[paramKey]) : match;
      });
    }

    // Ensure we return a string
    return typeof translation === 'string' ? translation : key;
  }, [currentTranslations]);

  /**
   * Get localized time-based greeting
   * @param {string} name - Optional user name
   * @returns {string} - Localized greeting
   */
  const getLocalizedGreeting = useCallback((name = null) => {
    return getTimeBasedGreeting(name);
  }, [currentLanguage]);

  // Initialize language on mount
  useEffect(() => {
    initializeLanguage();
  }, []);

  const contextValue = {
    currentLanguage,
    currentLanguageConfig,
    isRTL: isRTLMode,
    isInitialized,
    t,
    getTimeBasedGreeting: getLocalizedGreeting,
    changeLanguage: handleLanguageChange,
    initializeLanguage,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

/**
 * Hook to use language context
 * @returns {Object} - Language context value
 */
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

/**
 * Higher-order component to inject language props
 * @param {Component} WrappedComponent - Component to wrap
 * @returns {Component} - Enhanced component with language props
 */
export const withLanguage = (WrappedComponent) => {
  return function LanguageEnhancedComponent(props) {
    const languageContext = useLanguage();
    return <WrappedComponent {...props} {...languageContext} />;
  };
};

export default LanguageContext;
