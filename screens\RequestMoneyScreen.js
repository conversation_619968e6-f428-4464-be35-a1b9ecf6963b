import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import * as Contacts from 'expo-contacts';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import sendMoneyService from '../services/sendMoneyService';
import moneyRequestService from '../services/moneyRequestService';
import contactService from '../services/contactService';
import authService from '../services/authService';

/**
 * RequestMoneyScreen - Request money from contacts
 * Features contact selection, search, recent requests, and amount specification
 */
const RequestMoneyScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [contacts, setContacts] = useState([]);
  const [recentContacts, setRecentContacts] = useState([]);
  const [favoriteContacts, setFavoriteContacts] = useState([]);
  const [filteredContacts, setFilteredContacts] = useState([]);
  const [selectedTab, setSelectedTab] = useState('all'); // all, recent, favorites
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    filterContacts();
  }, [searchQuery, contacts, selectedTab]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);

      // Load contacts and request history
      await Promise.all([
        loadContacts(),
        loadRecentContacts(),
        loadFavoriteContacts()
      ]);
    } catch (error) {
      console.error('❌ Error loading request money data:', error);
      Alert.alert('Error', 'Failed to load contacts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loadContacts = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === 'granted') {
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers],
        });

        // Filter contacts with phone numbers and format them
        const formattedContacts = data
          .filter(contact => contact.phoneNumbers && contact.phoneNumbers.length > 0)
          .map(contact => ({
            id: contact.id,
            name: contact.name || 'Unknown',
            phoneNumber: contact.phoneNumbers[0].number,
            avatar: contact.name ? contact.name.charAt(0).toUpperCase() : '?',
            isContact: true
          }))
          .slice(0, 100); // Limit to 100 contacts for performance

        setContacts(formattedContacts);
      } else {
        // No permission granted
        console.log('⚠️ Contacts permission denied');
        setContacts([]);
      }
    } catch (error) {
      console.error('❌ Error loading contacts:', error);
      setContacts([]);
    }
  };

  const loadRecentContacts = async () => {
    try {
      console.log('📋 Loading recent contacts from request history...');

      // Load real recent contacts from money request history
      const recentContacts = await moneyRequestService.getRecentContacts(10);
      setRecentContacts(recentContacts);

      console.log(`✅ Loaded ${recentContacts.length} recent contacts`);
    } catch (error) {
      console.error('❌ Error loading recent contacts:', error);
      setRecentContacts([]);
    }
  };

  const loadFavoriteContacts = async () => {
    try {
      console.log('⭐ Loading favorite contacts...');

      // Load real favorite contacts from contactService
      const favoriteContacts = await contactService.getFavoriteContacts();
      setFavoriteContacts(favoriteContacts);

      console.log(`✅ Loaded ${favoriteContacts.length} favorite contacts`);
    } catch (error) {
      console.error('❌ Error loading favorite contacts:', error);
      setFavoriteContacts([]);
    }
  };

  const filterContacts = () => {
    let contactsToFilter = [];
    
    switch (selectedTab) {
      case 'recent':
        contactsToFilter = recentContacts;
        break;
      case 'favorites':
        contactsToFilter = favoriteContacts;
        break;
      default:
        contactsToFilter = contacts;
    }

    if (searchQuery.trim() === '') {
      setFilteredContacts(contactsToFilter);
    } else {
      const filtered = contactsToFilter.filter(contact =>
        contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        contact.phoneNumber.includes(searchQuery)
      );
      setFilteredContacts(filtered);
    }
  };

  const handleContactSelect = (contact) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    // Navigate to request amount screen
    navigation.navigate('RequestAmount', {
      contact,
      user
    });
  };

  const handleManualEntry = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    navigation.navigate('ManualRequestRecipient');
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
      </TouchableOpacity>
      <View style={styles.headerCenter}>
        <Text style={styles.headerTitle}>Request Money</Text>
        <Text style={styles.headerSubtitle}>Choose who to request from</Text>
      </View>
      <TouchableOpacity onPress={handleManualEntry} style={styles.manualButton}>
        <Ionicons name="person-add-outline" size={24} color={Colors.primary.main} />
      </TouchableOpacity>
    </View>
  );

  const renderSearchBar = () => (
    <View style={styles.searchContainer}>
      <View style={styles.searchBar}>
        <Ionicons name="search" size={20} color={Colors.neutral.warmGray} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search contacts..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor={Colors.neutral.warmGray}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={Colors.neutral.warmGray} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderTabs = () => (
    <View style={styles.tabsContainer}>
      {[
        { id: 'all', title: 'All Contacts', count: contacts.length },
        { id: 'recent', title: 'Recent', count: recentContacts.length },
        { id: 'favorites', title: 'Favorites', count: favoriteContacts.length }
      ].map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={[styles.tab, selectedTab === tab.id && styles.activeTab]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setSelectedTab(tab.id);
          }}
        >
          <Text style={[styles.tabText, selectedTab === tab.id && styles.activeTabText]}>
            {tab.title}
          </Text>
          {tab.count > 0 && (
            <View style={[styles.tabBadge, selectedTab === tab.id && styles.activeTabBadge]}>
              <Text style={[styles.tabBadgeText, selectedTab === tab.id && styles.activeTabBadgeText]}>
                {tab.count}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderContactItem = (contact) => (
    <TouchableOpacity
      key={contact.id}
      style={styles.contactItem}
      onPress={() => handleContactSelect(contact)}
      activeOpacity={0.7}
    >
      <View style={styles.contactAvatar}>
        <Text style={styles.contactAvatarText}>{contact.avatar}</Text>
      </View>
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{contact.name}</Text>
        <Text style={styles.contactPhone}>{contact.phoneNumber}</Text>
        {contact.lastRequestDate && (
          <Text style={styles.lastRequestDate}>
            Last request: {contact.lastRequestDate.toLocaleDateString()}
          </Text>
        )}
      </View>
      <View style={styles.contactActions}>
        {contact.isFavorite && (
          <Ionicons name="heart" size={16} color={Colors.status.error} />
        )}
        <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor={Colors.neutral.cream} />
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary.main} />
          <Text style={styles.loadingText}>Loading contacts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.neutral.cream} />
      {renderHeader()}
      
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {renderSearchBar()}
        {renderTabs()}
        
        <View style={styles.contactsList}>
          {filteredContacts.length > 0 ? (
            filteredContacts.map(renderContactItem)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="people-outline" size={64} color={Colors.neutral.warmGray} />
              <Text style={styles.emptyStateTitle}>No contacts found</Text>
              <Text style={styles.emptyStateSubtitle}>
                {searchQuery ? 'Try a different search term' : 'Add contacts to request money from them'}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
  manualButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: Colors.neutral.white,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  activeTab: {
    backgroundColor: Colors.primary.main,
    borderColor: Colors.primary.main,
  },
  tabText: {
    fontSize: 14,
    color: Colors.neutral.charcoal,
    fontWeight: '500',
  },
  activeTabText: {
    color: Colors.neutral.white,
  },
  tabBadge: {
    marginLeft: 8,
    backgroundColor: Colors.neutral.creamDark,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  activeTabBadge: {
    backgroundColor: Colors.neutral.white,
  },
  tabBadgeText: {
    fontSize: 12,
    color: Colors.neutral.charcoal,
    fontWeight: 'bold',
  },
  activeTabBadgeText: {
    color: Colors.primary.main,
  },
  contactsList: {
    paddingHorizontal: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  contactAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  contactAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  contactPhone: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  lastRequestDate: {
    fontSize: 12,
    color: Colors.accent.amber,
    marginTop: 2,
  },
  contactActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default RequestMoneyScreen;
