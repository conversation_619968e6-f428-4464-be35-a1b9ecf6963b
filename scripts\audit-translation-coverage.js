#!/usr/bin/env node

/**
 * Translation Coverage Audit Script
 * 
 * This script audits the JiraniPay app for untranslated text elements
 * and identifies hardcoded strings that need translation keys.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 JiraniPay Translation Coverage Audit');
console.log('======================================\n');

/**
 * Find hardcoded text in files
 */
function findHardcodedText(filePath, content) {
  const hardcodedPatterns = [
    // Alert.alert with hardcoded strings
    /Alert\.alert\s*\(\s*['"`]([^'"`]+)['"`]/g,
    // Text components with hardcoded strings
    /<Text[^>]*>([^<{]+)</g,
    // Button titles
    /title\s*=\s*['"`]([^'"`]+)['"`]/g,
    // Placeholder text
    /placeholder\s*=\s*['"`]([^'"`]+)['"`]/g,
    // Direct string literals in JSX
    />\s*([A-Z][a-zA-Z\s&]+)\s*</g,
  ];

  const findings = [];
  
  hardcodedPatterns.forEach((pattern, index) => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1];
      
      // Skip if it's already using translation function
      if (text.includes('t(') || text.includes('${') || text.length < 3) {
        continue;
      }
      
      // Skip common JSX patterns that aren't text
      if (text.match(/^(View|TouchableOpacity|ScrollView|SafeAreaView)$/)) {
        continue;
      }
      
      findings.push({
        type: getPatternType(index),
        text: text.trim(),
        line: getLineNumber(content, match.index),
        context: getContext(content, match.index)
      });
    }
  });
  
  return findings;
}

function getPatternType(index) {
  const types = ['Alert', 'Text Component', 'Button Title', 'Placeholder', 'JSX Text'];
  return types[index] || 'Unknown';
}

function getLineNumber(content, index) {
  return content.substring(0, index).split('\n').length;
}

function getContext(content, index) {
  const lines = content.split('\n');
  const lineNum = getLineNumber(content, index) - 1;
  const start = Math.max(0, lineNum - 1);
  const end = Math.min(lines.length, lineNum + 2);
  return lines.slice(start, end).join('\n');
}

/**
 * Scan directory for files
 */
function scanDirectory(dirPath, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
  const results = [];
  
  function scanRecursive(currentPath) {
    const items = fs.readdirSync(currentPath);
    
    items.forEach(item => {
      const itemPath = path.join(currentPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!item.startsWith('.') && item !== 'node_modules' && item !== 'android' && item !== 'ios') {
          scanRecursive(itemPath);
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        results.push(itemPath);
      }
    });
  }
  
  scanRecursive(dirPath);
  return results;
}

/**
 * Main audit function
 */
function auditTranslationCoverage() {
  const projectRoot = path.join(__dirname, '..');
  const filesToScan = scanDirectory(projectRoot);
  
  console.log(`📁 Scanning ${filesToScan.length} files...\n`);
  
  const auditResults = {
    totalFiles: 0,
    filesWithIssues: 0,
    totalIssues: 0,
    issuesByType: {},
    issuesByFile: {}
  };
  
  filesToScan.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const findings = findHardcodedText(filePath, content);
      
      auditResults.totalFiles++;
      
      if (findings.length > 0) {
        auditResults.filesWithIssues++;
        auditResults.totalIssues += findings.length;
        
        const relativePath = path.relative(projectRoot, filePath);
        auditResults.issuesByFile[relativePath] = findings;
        
        findings.forEach(finding => {
          auditResults.issuesByType[finding.type] = 
            (auditResults.issuesByType[finding.type] || 0) + 1;
        });
      }
    } catch (error) {
      console.warn(`⚠️ Could not read file: ${filePath}`);
    }
  });
  
  return auditResults;
}

/**
 * Generate report
 */
function generateReport(auditResults) {
  console.log('📊 AUDIT RESULTS');
  console.log('================');
  console.log(`Total Files Scanned: ${auditResults.totalFiles}`);
  console.log(`Files with Issues: ${auditResults.filesWithIssues}`);
  console.log(`Total Translation Issues: ${auditResults.totalIssues}`);
  console.log('');
  
  console.log('📋 Issues by Type:');
  Object.entries(auditResults.issuesByType).forEach(([type, count]) => {
    console.log(`  ${type}: ${count}`);
  });
  console.log('');
  
  console.log('🔍 Top Files with Most Issues:');
  const sortedFiles = Object.entries(auditResults.issuesByFile)
    .sort(([,a], [,b]) => b.length - a.length)
    .slice(0, 10);
    
  sortedFiles.forEach(([file, issues]) => {
    console.log(`  ${file}: ${issues.length} issues`);
  });
  console.log('');
  
  console.log('🎯 PRIORITY FIXES NEEDED:');
  console.log('=========================');
  
  // Show detailed issues for top problematic files
  sortedFiles.slice(0, 3).forEach(([file, issues]) => {
    console.log(`\n📄 ${file}:`);
    issues.slice(0, 5).forEach(issue => {
      console.log(`  Line ${issue.line}: "${issue.text}" (${issue.type})`);
    });
    if (issues.length > 5) {
      console.log(`  ... and ${issues.length - 5} more issues`);
    }
  });
}

/**
 * Generate translation keys suggestions
 */
function generateTranslationKeys(auditResults) {
  console.log('\n🔧 SUGGESTED TRANSLATION KEYS:');
  console.log('==============================');
  
  const suggestions = new Set();
  
  Object.values(auditResults.issuesByFile).forEach(issues => {
    issues.forEach(issue => {
      const key = generateKeyFromText(issue.text);
      if (key) {
        suggestions.add(`${key}: '${issue.text}'`);
      }
    });
  });
  
  Array.from(suggestions).slice(0, 20).forEach(suggestion => {
    console.log(`  ${suggestion}`);
  });
  
  if (suggestions.size > 20) {
    console.log(`  ... and ${suggestions.size - 20} more suggestions`);
  }
}

function generateKeyFromText(text) {
  if (!text || text.length < 3) return null;
  
  // Convert text to camelCase key
  return text
    .toLowerCase()
    .replace(/[^a-zA-Z0-9\s]/g, '')
    .split(/\s+/)
    .filter(word => word.length > 0)
    .map((word, index) => index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
    .slice(0, 50); // Limit key length
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting translation coverage audit...\n');
  
  const auditResults = auditTranslationCoverage();
  generateReport(auditResults);
  generateTranslationKeys(auditResults);
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('==============');
  console.log('1. Add missing translation keys to locales/en.js');
  console.log('2. Translate keys to all supported languages (sw, fr, ar, am)');
  console.log('3. Replace hardcoded strings with t() function calls');
  console.log('4. Test language switching to ensure 100% coverage');
  console.log('');
  console.log('📝 Use the suggested translation keys above as a starting point.');
  console.log('🔄 Run this script again after fixes to track progress.');
  
  // Exit with error code if issues found
  process.exit(auditResults.totalIssues > 0 ? 1 : 0);
}

// Run the audit
main();
