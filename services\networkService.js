import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Network Service for East African Network Conditions
 * Optimized for poor connectivity, 2G/3G networks, and intermittent connections
 */
class NetworkService {
  constructor() {
    this.isConnected = true;
    this.connectionType = 'unknown';
    this.connectionQuality = 'good'; // poor, fair, good, excellent
    this.listeners = [];
    this.retryQueue = [];
    this.isProcessingQueue = false;
    
    this.init();
  }

  async init() {
    try {
      // Initialize network state
      const state = await NetInfo.fetch();
      this.updateNetworkState(state);
      
      // Listen for network changes
      NetInfo.addEventListener(this.handleNetworkChange.bind(this));
      
      console.log('🌐 NetworkService: Initialized for East African conditions');
    } catch (error) {
      console.error('❌ NetworkService: Initialization failed:', error);
    }
  }

  handleNetworkChange = (state) => {
    const wasConnected = this.isConnected;
    this.updateNetworkState(state);
    
    // Notify listeners
    this.listeners.forEach(listener => {
      try {
        listener(this.getNetworkStatus());
      } catch (error) {
        console.error('❌ NetworkService: Listener error:', error);
      }
    });

    // Process retry queue when connection is restored
    if (!wasConnected && this.isConnected) {
      console.log('🌐 NetworkService: Connection restored, processing retry queue');
      this.processRetryQueue();
    }
  };

  updateNetworkState(state) {
    this.isConnected = state.isConnected;
    this.connectionType = state.type;
    
    // Determine connection quality based on East African network conditions
    this.connectionQuality = this.assessConnectionQuality(state);
    
    console.log(`🌐 NetworkService: Status - Connected: ${this.isConnected}, Type: ${this.connectionType}, Quality: ${this.connectionQuality}`);
  }

  assessConnectionQuality(state) {
    if (!state.isConnected) return 'none';
    
    // East African network quality assessment
    const { type, details } = state;
    
    if (type === 'wifi') {
      return details?.strength > 70 ? 'excellent' : 'good';
    }
    
    if (type === 'cellular') {
      const cellularGeneration = details?.cellularGeneration;
      switch (cellularGeneration) {
        case '5g': return 'excellent';
        case '4g': return 'good';
        case '3g': return 'fair';
        case '2g': return 'poor';
        default: return 'fair';
      }
    }
    
    return 'fair';
  }

  getNetworkStatus() {
    return {
      isConnected: this.isConnected,
      connectionType: this.connectionType,
      connectionQuality: this.connectionQuality,
      isSlowConnection: ['poor', 'fair'].includes(this.connectionQuality),
      canMakeRequests: this.isConnected && this.connectionQuality !== 'none'
    };
  }

  // Network status listener management
  addNetworkListener(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // Optimized request with retry logic for East African conditions
  async makeOptimizedRequest(url, options = {}, retryConfig = {}) {
    const {
      maxRetries = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      timeoutMs = 15000, // Longer timeout for slow connections
      enableCompression = true,
      priority = 'normal' // low, normal, high
    } = retryConfig;

    const requestOptions = {
      ...options,
      timeout: timeoutMs,
      headers: {
        ...options.headers,
        ...(enableCompression && { 'Accept-Encoding': 'gzip, deflate' }),
        'Cache-Control': 'max-age=300', // 5 minutes cache for slow connections
      }
    };

    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Check network status before attempt
        if (!this.isConnected) {
          throw new Error('No network connection');
        }

        console.log(`🌐 NetworkService: Request attempt ${attempt + 1}/${maxRetries + 1} to ${url}`);
        
        // Add request to queue if connection is poor and not high priority
        if (this.connectionQuality === 'poor' && priority === 'low') {
          return this.queueRequest(url, requestOptions, retryConfig);
        }

        const response = await this.executeRequest(url, requestOptions);
        
        console.log(`✅ NetworkService: Request successful on attempt ${attempt + 1}`);
        return response;
        
      } catch (error) {
        lastError = error;
        console.log(`⚠️ NetworkService: Request failed on attempt ${attempt + 1}:`, error.message);
        
        // Don't retry on certain errors
        if (this.isNonRetryableError(error) || attempt === maxRetries) {
          break;
        }
        
        // Exponential backoff with jitter for East African conditions
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
          maxDelay
        );
        
        console.log(`⏳ NetworkService: Retrying in ${delay}ms...`);
        await this.delay(delay);
      }
    }
    
    // If all retries failed, queue for later if appropriate
    if (this.shouldQueueFailedRequest(lastError, priority)) {
      console.log('📥 NetworkService: Queueing failed request for retry when connection improves');
      return this.queueRequest(url, requestOptions, retryConfig);
    }
    
    throw lastError;
  }

  async executeRequest(url, options) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout);
    
    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  isNonRetryableError(error) {
    // Don't retry on client errors (4xx) except 408, 429
    if (error.message.includes('HTTP 4')) {
      const status = parseInt(error.message.match(/HTTP (\d+)/)?.[1]);
      return ![408, 429].includes(status);
    }
    return false;
  }

  shouldQueueFailedRequest(error, priority) {
    // Queue high priority requests and network-related failures
    return priority === 'high' || 
           error.message.includes('network') || 
           error.message.includes('timeout') ||
           error.message.includes('No network connection');
  }

  // Request queuing for offline/poor connection scenarios
  async queueRequest(url, options, retryConfig) {
    const queueItem = {
      id: Date.now() + Math.random(),
      url,
      options,
      retryConfig,
      timestamp: Date.now(),
      attempts: 0
    };
    
    this.retryQueue.push(queueItem);
    await this.saveQueueToStorage();
    
    console.log(`📥 NetworkService: Request queued (${this.retryQueue.length} items in queue)`);
    
    return {
      queued: true,
      queueId: queueItem.id,
      message: 'Request queued for when connection improves'
    };
  }

  async processRetryQueue() {
    if (this.isProcessingQueue || !this.isConnected) return;
    
    this.isProcessingQueue = true;
    console.log(`🔄 NetworkService: Processing retry queue (${this.retryQueue.length} items)`);
    
    const processedItems = [];
    
    for (const item of this.retryQueue) {
      try {
        if (!this.isConnected) break;
        
        console.log(`🔄 NetworkService: Processing queued request to ${item.url}`);
        await this.executeRequest(item.url, item.options);
        processedItems.push(item.id);
        
      } catch (error) {
        console.log(`⚠️ NetworkService: Queued request failed:`, error.message);
        item.attempts++;
        
        // Remove items that have failed too many times
        if (item.attempts >= 5) {
          processedItems.push(item.id);
          console.log(`❌ NetworkService: Removing failed request after 5 attempts`);
        }
      }
    }
    
    // Remove processed items
    this.retryQueue = this.retryQueue.filter(item => !processedItems.includes(item.id));
    await this.saveQueueToStorage();
    
    this.isProcessingQueue = false;
    console.log(`✅ NetworkService: Queue processing complete (${this.retryQueue.length} items remaining)`);
  }

  async saveQueueToStorage() {
    try {
      await AsyncStorage.setItem('network_retry_queue', JSON.stringify(this.retryQueue));
    } catch (error) {
      console.error('❌ NetworkService: Failed to save queue to storage:', error);
    }
  }

  async loadQueueFromStorage() {
    try {
      const queueData = await AsyncStorage.getItem('network_retry_queue');
      if (queueData) {
        this.retryQueue = JSON.parse(queueData);
        console.log(`📥 NetworkService: Loaded ${this.retryQueue.length} items from queue`);
      }
    } catch (error) {
      console.error('❌ NetworkService: Failed to load queue from storage:', error);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Get connection-appropriate timeout values
  getTimeoutConfig() {
    switch (this.connectionQuality) {
      case 'poor': return { timeout: 30000, retries: 5 };
      case 'fair': return { timeout: 20000, retries: 3 };
      case 'good': return { timeout: 15000, retries: 2 };
      case 'excellent': return { timeout: 10000, retries: 1 };
      default: return { timeout: 15000, retries: 3 };
    }
  }

  // Clear retry queue (for testing or manual intervention)
  clearRetryQueue() {
    this.retryQueue = [];
    this.saveQueueToStorage();
    console.log('🧹 NetworkService: Retry queue cleared');
  }
}

export default new NetworkService();
