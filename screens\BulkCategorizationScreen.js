/**
 * Bulk Categorization Screen
 * Provides interface for bulk transaction categorization operations
 * Supports review, edit, and bulk update functionality
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  FlatList,
  ActivityIndicator
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import authService from '../services/authService';
import bulkCategorizationService from '../services/bulkCategorizationService';
import categoryManagementService from '../services/categoryManagementService';
import { createStyles } from '../styles/BulkCategorizationStyles';

const BulkCategorizationScreen = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { formatAmount } = useCurrencyContext();
  const [user, setUser] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [selectedTransactions, setSelectedTransactions] = useState(new Set());
  const [filter, setFilter] = useState('uncategorized');
  const [stats, setStats] = useState(null);

  const styles = createStyles(theme);

  // Get mode from route params
  const mode = route?.params?.mode || 'review';
  const fromCategory = route?.params?.fromCategory;

  /**
   * Get current user on mount
   */
  useEffect(() => {
    const currentUser = authService.getCurrentUser();
    setUser(currentUser);
  }, []);

  /**
   * Load data when user is available
   */
  useEffect(() => {
    if (user?.id) {
      loadData();
    }
  }, [user?.id, filter]);

  /**
   * Load transactions and categories
   */
  const loadData = useCallback(async () => {
    try {
      setLoading(true);

      // Load categories
      const categoriesResult = await categoryManagementService.getUserCategories(user.id);
      if (categoriesResult.success) {
        setCategories(categoriesResult.data);
      }

      // Load transactions based on filter
      let transactionsResult;
      if (filter === 'uncategorized') {
        transactionsResult = await bulkCategorizationService.getUncategorizedTransactions(user.id, {
          limit: 100
        });
      } else {
        transactionsResult = await bulkCategorizationService.getTransactionsByCategory(user.id, filter, {
          limit: 100,
          confidenceThreshold: 0.7
        });
      }

      if (transactionsResult.success) {
        setTransactions(transactionsResult.data.transactions || []);
      }

      // Load categorization statistics
      const statsResult = await bulkCategorizationService.getCategorizationStats(user.id);
      if (statsResult.success) {
        setStats(statsResult.data);
      }

    } catch (error) {
      console.error('❌ Error loading data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  }, [user?.id, filter]);

  /**
   * Handle refresh
   */
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  }, [loadData]);

  /**
   * Handle transaction selection
   */
  const toggleTransactionSelection = useCallback((transactionId) => {
    setSelectedTransactions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(transactionId)) {
        newSet.delete(transactionId);
      } else {
        newSet.add(transactionId);
      }
      return newSet;
    });
  }, []);

  /**
   * Select all transactions
   */
  const selectAllTransactions = useCallback(() => {
    setSelectedTransactions(new Set(transactions.map(tx => tx.id)));
  }, [transactions]);

  /**
   * Clear selection
   */
  const clearSelection = useCallback(() => {
    setSelectedTransactions(new Set());
  }, []);

  /**
   * Handle bulk categorization
   */
  const handleBulkCategorization = useCallback(async () => {
    if (selectedTransactions.size === 0) {
      Alert.alert('No Selection', 'Please select transactions to categorize');
      return;
    }

    try {
      setProcessing(true);

      const selectedIds = Array.from(selectedTransactions);
      const result = await bulkCategorizationService.processBulkCategorization(
        user.id,
        selectedIds,
        {
          autoApply: false,
          confidenceThreshold: 0.7,
          onProgress: (progress) => {
            console.log('Progress:', progress);
          }
        }
      );

      if (result.success) {
        Alert.alert(
          'Categorization Complete',
          `Processed ${result.data.summary.total} transactions. ${result.data.summary.successful} successful.`,
          [
            {
              text: 'Review Results',
              onPress: () => navigation.navigate('CategorizationResults', {
                results: result.data.results
              })
            },
            { text: 'OK' }
          ]
        );

        // Refresh data
        await loadData();
        clearSelection();
      } else {
        Alert.alert('Error', result.error || 'Failed to process categorization');
      }
    } catch (error) {
      console.error('❌ Error in bulk categorization:', error);
      Alert.alert('Error', 'Failed to process categorization');
    } finally {
      setProcessing(false);
    }
  }, [selectedTransactions, user?.id, loadData, clearSelection, navigation]);

  /**
   * Handle apply suggestion
   */
  const handleApplySuggestion = useCallback(async (transactionId, categoryId, confidence) => {
    try {
      const result = await bulkCategorizationService.applyCategorization(
        user.id,
        transactionId,
        categoryId,
        confidence
      );

      if (result.success) {
        // Update transaction in local state
        setTransactions(prev => prev.filter(tx => tx.id !== transactionId));
        
        // Show success feedback
        Alert.alert('Success', 'Category applied successfully');
      } else {
        Alert.alert('Error', result.error || 'Failed to apply category');
      }
    } catch (error) {
      console.error('❌ Error applying suggestion:', error);
      Alert.alert('Error', 'Failed to apply category');
    }
  }, [user?.id]);

  /**
   * Render transaction item
   */
  const renderTransactionItem = ({ item: transaction }) => {
    const isSelected = selectedTransactions.has(transaction.id);
    const suggestion = transaction.suggestions?.primary;
    const confidence = transaction.suggestions?.confidence || 0;
    
    return (
      <TouchableOpacity
        style={[styles.transactionItem, isSelected && styles.transactionItemSelected]}
        onPress={() => toggleTransactionSelection(transaction.id)}
      >
        <View style={styles.transactionHeader}>
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? 'checkbox' : 'square-outline'}
              size={24}
              color={isSelected ? theme.colors.primary : theme.colors.textSecondary}
            />
          </View>

          <View style={styles.transactionInfo}>
            <Text style={styles.transactionDescription}>
              {transaction.description || transaction.merchant_name || 'Unknown Transaction'}
            </Text>
            <Text style={styles.transactionAmount}>
              {formatAmount(Math.abs(transaction.amount))}
            </Text>
            <Text style={styles.transactionDate}>
              {new Date(transaction.created_at).toLocaleDateString()}
            </Text>
          </View>
        </View>

        {suggestion && (
          <View style={styles.suggestionContainer}>
            <View style={styles.suggestionHeader}>
              <Text style={styles.suggestionLabel}>Suggested Category:</Text>
              <View style={[styles.confidenceIndicator, { backgroundColor: getConfidenceColor(confidence) }]}>
                <Text style={styles.confidenceText}>{Math.round(confidence * 100)}%</Text>
              </View>
            </View>

            <View style={styles.suggestionContent}>
              <View style={styles.categoryInfo}>
                <Ionicons
                  name={getCategoryIcon(suggestion)}
                  size={20}
                  color={getCategoryColor(suggestion)}
                />
                <Text style={styles.categoryName}>{getCategoryName(suggestion)}</Text>
              </View>

              <TouchableOpacity
                style={styles.applyButton}
                onPress={() => handleApplySuggestion(transaction.id, suggestion, confidence)}
              >
                <Text style={styles.applyButtonText}>Apply</Text>
              </TouchableOpacity>
            </View>

            {transaction.suggestions?.alternatives?.length > 0 && (
              <View style={styles.alternativesContainer}>
                <Text style={styles.alternativesLabel}>Alternatives:</Text>
                <View style={styles.alternativesList}>
                  {transaction.suggestions.alternatives.slice(0, 3).map((alt, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.alternativeChip}
                      onPress={() => handleApplySuggestion(transaction.id, alt.category, alt.confidence)}
                    >
                      <Text style={styles.alternativeText}>{getCategoryName(alt.category)}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  /**
   * Helper functions
   */
  const getCategoryIcon = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.icon || 'folder';
  };

  const getCategoryColor = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.color || theme.colors.textSecondary;
  };

  const getCategoryName = (categoryId) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category?.name || categoryId;
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return theme.colors.success;
    if (confidence >= 0.6) return theme.colors.warning;
    return theme.colors.error;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>Loading transactions...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>Bulk Categorization</Text>

        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => {/* Show filter menu */}}
        >
          <Ionicons name="filter" size={24} color={theme.colors.text} />
        </TouchableOpacity>
      </View>

      {/* Statistics */}
      {stats && (
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.uncategorized}</Text>
            <Text style={styles.statLabel}>Uncategorized</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{stats.lowConfidence}</Text>
            <Text style={styles.statLabel}>Low Confidence</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{Math.round((stats.categorized / stats.total) * 100)}%</Text>
            <Text style={styles.statLabel}>Categorized</Text>
          </View>
        </View>
      )}

      {/* Selection Controls */}
      {selectedTransactions.size > 0 && (
        <View style={styles.selectionControls}>
          <Text style={styles.selectionCount}>
            {selectedTransactions.size} selected
          </Text>

          <View style={styles.selectionActions}>
            <TouchableOpacity
              style={styles.selectionButton}
              onPress={clearSelection}
            >
              <Text style={styles.selectionButtonText}>Clear</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.selectionButton, styles.primaryButton]}
              onPress={handleBulkCategorization}
              disabled={processing}
            >
              {processing ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.primaryButtonText}>Categorize</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={selectAllTransactions}
        >
          <Ionicons name="checkmark-done" size={16} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Select All</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => setFilter('uncategorized')}
        >
          <Ionicons name="help-circle" size={16} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Uncategorized</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('CategoryInsights')}
        >
          <Ionicons name="analytics" size={16} color={theme.colors.primary} />
          <Text style={styles.quickActionText}>Insights</Text>
        </TouchableOpacity>
      </View>

      {/* Transactions List */}
      <FlatList
        data={transactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="checkmark-circle" size={64} color={theme.colors.success} />
            <Text style={styles.emptyTitle}>All Caught Up!</Text>
            <Text style={styles.emptyMessage}>
              {filter === 'uncategorized'
                ? 'All transactions are categorized'
                : 'No transactions need review'
              }
            </Text>
          </View>
        }
      />
    </View>
  );
};

export default BulkCategorizationScreen;
