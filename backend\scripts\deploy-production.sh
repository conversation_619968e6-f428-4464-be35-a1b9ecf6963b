#!/bin/bash

# JiraniPay Production Deployment Script
# Comprehensive production deployment with health checks and rollback capabilities

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-production}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Error handling
cleanup() {
    local exit_code=$?
    if [[ $exit_code -ne 0 ]]; then
        log_error "Deployment failed with exit code $exit_code"
        if [[ "$ROLLBACK_ON_FAILURE" == "true" ]]; then
            log_warning "Initiating rollback..."
            rollback_deployment
        fi
    fi
    exit $exit_code
}

trap cleanup EXIT

# Help function
show_help() {
    cat << EOF
🏭 JiraniPay Production Deployment Script

Usage: $0 [OPTIONS]

Options:
    -e, --environment ENV       Deployment environment (default: production)
    -t, --timeout SECONDS      Health check timeout (default: 300)
    -r, --no-rollback          Disable automatic rollback on failure
    -s, --skip-tests           Skip pre-deployment tests
    -f, --force                Force deployment without confirmations
    -h, --help                 Show this help message

Environment Variables:
    DEPLOYMENT_ENV             Deployment environment
    HEALTH_CHECK_TIMEOUT       Health check timeout in seconds
    ROLLBACK_ON_FAILURE        Enable/disable automatic rollback
    DOCKER_REGISTRY            Docker registry URL
    IMAGE_TAG                  Docker image tag to deploy

Examples:
    $0 --environment production
    $0 --timeout 600 --no-rollback
    DEPLOYMENT_ENV=staging $0 --skip-tests

EOF
}

# Parse command line arguments
SKIP_TESTS=false
FORCE_DEPLOYMENT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            DEPLOYMENT_ENV="$2"
            shift 2
            ;;
        -t|--timeout)
            HEALTH_CHECK_TIMEOUT="$2"
            shift 2
            ;;
        -r|--no-rollback)
            ROLLBACK_ON_FAILURE="false"
            shift
            ;;
        -s|--skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        -f|--force)
            FORCE_DEPLOYMENT="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate prerequisites
validate_prerequisites() {
    log_step "Validating deployment prerequisites..."
    
    # Check required tools
    local required_tools=("docker" "kubectl" "helm" "jq" "curl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check environment file
    local env_file="$PROJECT_ROOT/.env.$DEPLOYMENT_ENV.local"
    if [[ ! -f "$env_file" ]]; then
        log_error "Environment file not found: $env_file"
        log_info "Run: node scripts/setup-production-env.js"
        exit 1
    fi
    
    # Check Docker registry access
    if [[ -n "${DOCKER_REGISTRY:-}" ]]; then
        if ! docker info &> /dev/null; then
            log_error "Docker daemon not running"
            exit 1
        fi
    fi
    
    # Check Kubernetes access
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Kubernetes cluster not accessible"
        exit 1
    fi
    
    log_success "Prerequisites validated"
}

# Pre-deployment security checks
security_checks() {
    log_step "Running security checks..."
    
    # Check for secrets in code
    if command -v git &> /dev/null; then
        if git log --oneline -1 &> /dev/null; then
            local secrets_found=$(git secrets --scan || echo "git-secrets not installed")
            if [[ "$secrets_found" != "git-secrets not installed" ]] && [[ -n "$secrets_found" ]]; then
                log_error "Secrets detected in code repository"
                exit 1
            fi
        fi
    fi
    
    # Validate environment variables
    source "$PROJECT_ROOT/.env.$DEPLOYMENT_ENV.local"
    
    local required_vars=(
        "JWT_SECRET"
        "ENCRYPTION_KEY"
        "SUPABASE_URL"
        "SUPABASE_SERVICE_ROLE_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable not set: $var"
            exit 1
        fi
        
        # Check for placeholder values
        if [[ "${!var}" == *"CHANGE_THIS"* ]] || [[ "${!var}" == *"your-"* ]]; then
            log_error "Placeholder value detected for $var"
            exit 1
        fi
    done
    
    # Check JWT secret strength
    if [[ ${#JWT_SECRET} -lt 64 ]]; then
        log_error "JWT_SECRET must be at least 64 characters long"
        exit 1
    fi
    
    log_success "Security checks passed"
}

# Run tests
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_warning "Skipping tests as requested"
        return 0
    fi
    
    log_step "Running pre-deployment tests..."
    
    cd "$PROJECT_ROOT"
    
    # Install dependencies
    npm ci --production=false
    
    # Run linting
    npm run lint
    
    # Run unit tests
    npm run test:unit
    
    # Run integration tests
    npm run test:integration
    
    # Run security audit
    npm audit --audit-level=high
    
    # Check for outdated dependencies
    npm outdated --depth=0 || true
    
    log_success "All tests passed"
}

# Build and push Docker image
build_and_push_image() {
    log_step "Building and pushing Docker image..."
    
    cd "$PROJECT_ROOT"
    
    local image_tag="${IMAGE_TAG:-$(date +%Y%m%d-%H%M%S)}"
    local image_name="${DOCKER_REGISTRY}/jiranipay:${image_tag}"
    
    # Build production image
    docker build \
        --target production \
        --build-arg NODE_ENV=production \
        --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
        --build-arg VCS_REF="$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
        --tag "$image_name" \
        --tag "${DOCKER_REGISTRY}/jiranipay:latest" \
        .
    
    # Security scan
    if command -v trivy &> /dev/null; then
        trivy image --exit-code 1 --severity HIGH,CRITICAL "$image_name"
    fi
    
    # Push to registry
    docker push "$image_name"
    docker push "${DOCKER_REGISTRY}/jiranipay:latest"
    
    export DEPLOYED_IMAGE="$image_name"
    log_success "Image built and pushed: $image_name"
}

# Deploy to Kubernetes
deploy_to_kubernetes() {
    log_step "Deploying to Kubernetes..."
    
    local namespace="jiranipay-$DEPLOYMENT_ENV"
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # Apply secrets
    kubectl create secret generic jiranipay-secrets \
        --from-env-file="$PROJECT_ROOT/.env.$DEPLOYMENT_ENV.local" \
        --namespace="$namespace" \
        --dry-run=client -o yaml | kubectl apply -f -
    
    # Deploy using Helm
    helm upgrade --install jiranipay \
        "$PROJECT_ROOT/helm/jiranipay" \
        --namespace="$namespace" \
        --set image.repository="${DOCKER_REGISTRY}/jiranipay" \
        --set image.tag="${IMAGE_TAG:-latest}" \
        --set environment="$DEPLOYMENT_ENV" \
        --set replicaCount=3 \
        --set autoscaling.enabled=true \
        --set ingress.enabled=true \
        --set ingress.hosts[0].host="api.jiranipay.com" \
        --set monitoring.enabled=true \
        --wait \
        --timeout=10m
    
    log_success "Deployment completed"
}

# Health checks
perform_health_checks() {
    log_step "Performing health checks..."
    
    local namespace="jiranipay-$DEPLOYMENT_ENV"
    local service_url="https://api.jiranipay.com"
    
    # Wait for pods to be ready
    kubectl wait --for=condition=ready pod \
        --selector=app=jiranipay \
        --namespace="$namespace" \
        --timeout=300s
    
    # Check service endpoints
    local endpoints=(
        "/health"
        "/api/v1/health"
        "/api/v1/admin/monitoring/health"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local url="$service_url$endpoint"
        local max_attempts=30
        local attempt=1
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f -s "$url" > /dev/null; then
                log_success "Health check passed: $endpoint"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "Health check failed: $endpoint"
                return 1
            fi
            
            log_info "Health check attempt $attempt/$max_attempts for $endpoint"
            sleep 10
            ((attempt++))
        done
    done
    
    # Check database connectivity
    kubectl exec -n "$namespace" deployment/jiranipay -- \
        node -e "
            require('dotenv').config();
            const { createClient } = require('@supabase/supabase-js');
            const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
            supabase.from('user_profiles').select('count').limit(1)
                .then(() => { console.log('Database connection: OK'); process.exit(0); })
                .catch(() => { console.log('Database connection: FAILED'); process.exit(1); });
        "
    
    log_success "All health checks passed"
}

# Smoke tests
run_smoke_tests() {
    log_step "Running smoke tests..."
    
    local api_url="https://api.jiranipay.com/api/v1"
    
    # Test authentication endpoint
    local auth_response=$(curl -s -w "%{http_code}" -o /dev/null "$api_url/auth/health")
    if [[ "$auth_response" != "200" ]]; then
        log_error "Authentication endpoint smoke test failed (HTTP $auth_response)"
        return 1
    fi
    
    # Test admin endpoint (should require authentication)
    local admin_response=$(curl -s -w "%{http_code}" -o /dev/null "$api_url/admin/dashboard")
    if [[ "$admin_response" != "401" ]]; then
        log_error "Admin endpoint smoke test failed (HTTP $admin_response)"
        return 1
    fi
    
    log_success "Smoke tests passed"
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    local namespace="jiranipay-$DEPLOYMENT_ENV"
    
    # Rollback using Helm
    helm rollback jiranipay --namespace="$namespace"
    
    # Wait for rollback to complete
    kubectl rollout status deployment/jiranipay --namespace="$namespace" --timeout=300s
    
    log_success "Rollback completed"
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local color="good"
        if [[ "$status" == "failure" ]]; then
            color="danger"
        elif [[ "$status" == "warning" ]]; then
            color="warning"
        fi
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{
                \"attachments\": [{
                    \"color\": \"$color\",
                    \"title\": \"JiraniPay Production Deployment\",
                    \"text\": \"$message\",
                    \"fields\": [
                        {\"title\": \"Environment\", \"value\": \"$DEPLOYMENT_ENV\", \"short\": true},
                        {\"title\": \"Image\", \"value\": \"${DEPLOYED_IMAGE:-unknown}\", \"short\": true},
                        {\"title\": \"Timestamp\", \"value\": \"$(date -u)\", \"short\": true}
                    ]
                }]
            }" \
            "$SLACK_WEBHOOK_URL" > /dev/null
    fi
}

# Main deployment function
main() {
    log_info "🏭 Starting JiraniPay Production Deployment"
    log_info "Environment: $DEPLOYMENT_ENV"
    log_info "Timestamp: $(date -u)"
    
    # Confirmation prompt
    if [[ "$FORCE_DEPLOYMENT" != "true" ]]; then
        echo -e "${YELLOW}⚠️  You are about to deploy to $DEPLOYMENT_ENV environment.${NC}"
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Deployment cancelled by user"
            exit 0
        fi
    fi
    
    # Execute deployment steps
    validate_prerequisites
    security_checks
    run_tests
    build_and_push_image
    deploy_to_kubernetes
    perform_health_checks
    run_smoke_tests
    
    log_success "🎉 Production deployment completed successfully!"
    send_notification "success" "Production deployment completed successfully"
    
    # Display deployment summary
    echo
    log_info "📋 Deployment Summary:"
    log_info "  Environment: $DEPLOYMENT_ENV"
    log_info "  Image: ${DEPLOYED_IMAGE:-unknown}"
    log_info "  Namespace: jiranipay-$DEPLOYMENT_ENV"
    log_info "  API URL: https://api.jiranipay.com"
    log_info "  Admin Panel: https://admin.jiranipay.com"
    log_info "  Monitoring: https://monitoring.jiranipay.com"
    echo
    log_info "🔍 Next Steps:"
    log_info "  1. Monitor application metrics"
    log_info "  2. Check error logs and alerts"
    log_info "  3. Verify business functionality"
    log_info "  4. Update documentation"
    log_info "  5. Notify stakeholders"
}

# Run main function
main "$@"
