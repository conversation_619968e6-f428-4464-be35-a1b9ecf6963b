import supabase from './supabaseClient';
import databaseService from './databaseService';
import profileAutoCreationService from './profileAutoCreationService';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import { AppState } from 'react-native';
import {
  detectNetworkProvider as detectProvider,
  formatPhoneNumber as formatPhone,
  validatePhoneNumber,
  getCountryByCode
} from '../utils/countriesConfig';
import { isProductionMode, getEnvironmentName } from '../config/environment';
import productionServices from '../config/productionServices';
import deviceManagementService from './deviceManagementService';
import deviceUserRecognitionService from './deviceUserRecognitionService';

// Removed error handling system imports to restore stability

class AuthService {
  constructor() {
    this.currentUser = null;
    this.authStateListeners = [];
    this.supabaseClient = null; // Will be set during initialization

    // Session timeout management
    this.sessionTimeoutTimer = null;
    this.sessionTimeoutMinutes = 30; // Default timeout
    this.lastActivityTime = Date.now();
    this.appStateSubscription = null;
    this.backgroundTime = null;

    // Initialize auth state listener
    this.initialize();
    this.setupSessionTimeoutManager();
  }

  // Initialize authentication state listener
  async initialize() {
    console.log('🔧 Initializing auth service...');

    // Use the main Supabase client (simplified configuration)
    this.supabaseClient = supabase;
    console.log('✅ Using Supabase client');

    // Set up auth state listener
    this.setupAuthStateListener();

    // Validate configuration with timeout (production-ready)
    try {
      console.log('🔍 Validating configuration...');

      // Add timeout to prevent hanging
      const validationPromise = new Promise((resolve) => {
        try {
          const validation = productionServices.validate();
          resolve(validation);
        } catch (error) {
          resolve({ isValid: false, hasWarnings: true, errors: [error.message] });
        }
      });

      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          resolve({ isValid: true, hasWarnings: true, message: 'Validation timeout - proceeding with core features' });
        }, 2000); // 2 second timeout
      });

      const validation = await Promise.race([validationPromise, timeoutPromise]);

      if (validation.isValid) {
        if (validation.hasWarnings) {
          console.log('⚠️ Configuration has warnings but is functional');
          console.log(`💡 ${validation.message || 'Some optional services may not be available'}`);
        } else {
          console.log('✅ Configuration validated successfully');
        }
      } else {
        console.error('❌ Configuration validation failed');
        if (validation.errors) {
          validation.errors.forEach(error => console.error(`- ${error}`));
        }
        console.log('💡 Continuing with core features available');
      }
    } catch (error) {
      console.error('❌ Configuration validation failed:', error);
      console.log('💡 Continuing with core features available');
    }

    // Clear any cached authentication data for fresh start with timeout
    try {
      console.log('🧹 Clearing cached authentication data...');

      // Add timeout to SecureStore operations
      const clearDataPromise = Promise.all([
        SecureStore.deleteItemAsync('pending_phone'),
        SecureStore.deleteItemAsync('pending_registration_phone')
      ]);

      const timeoutPromise = new Promise((resolve) => {
        setTimeout(() => {
          console.log('⚠️ SecureStore cleanup timeout - proceeding anyway');
          resolve();
        }, 1000); // 1 second timeout
      });

      await Promise.race([clearDataPromise, timeoutPromise]);
      console.log('✅ Authentication data cleanup completed');
    } catch (error) {
      console.warn('⚠️ Error during authentication data cleanup:', error.message);
      // Continue anyway - not critical
    }

    console.log('✅ Auth service initialization completed');

  }

  // Set up auth state listener
  setupAuthStateListener() {
    if (this.supabaseClient) {
      this.supabaseClient.auth.onAuthStateChange(async (event, session) => {
        console.log('🔐 Auth state changed:', event, session ? 'with session' : 'no session');

        if (session?.user) {
          this.currentUser = session.user;
          console.log('✅ User authenticated:', session.user.id);

          // Register device on successful authentication with timeout
          try {
            console.log('📱 Registering device...');

            const devicePromise = deviceManagementService.registerDevice(session.user.id);
            const timeoutPromise = new Promise((resolve) => {
              setTimeout(() => {
                console.log('⚠️ Device registration timeout - proceeding without device registration');
                resolve({ success: false, timeout: true });
              }, 2000); // 2 second timeout
            });

            const deviceResult = await Promise.race([devicePromise, timeoutPromise]);

            if (deviceResult.success) {
              console.log('📱 Device registered successfully');
              if (deviceResult.isNewDevice) {
                console.log('🆕 New device detected - may require additional verification');
              }
            } else if (deviceResult.timeout) {
              console.log('⚠️ Device registration skipped due to timeout');
            }
          } catch (error) {
            console.error('❌ Error registering device:', error);
          }

          // Store user recognition data for device-based greetings
          try {
            console.log('💾 Storing user recognition data...');

            // Get user profile for recognition storage
            const profileResult = await this.getUserProfile(session.user.id);
            const userInfo = {
              fullName: profileResult.data?.full_name || session.user.user_metadata?.full_name || session.user.user_metadata?.name,
              phone: profileResult.data?.phone || session.user.phone,
              email: profileResult.data?.email || session.user.email,
              avatarUrl: profileResult.data?.avatar_url
            };

            await deviceUserRecognitionService.storeUserInfo(session.user.id, userInfo);
            console.log('✅ User recognition data stored');
          } catch (error) {
            console.error('❌ Error storing user recognition data:', error);
          }

          // Start session timeout when user logs in
          this.loadSessionTimeoutPreference().then(() => {
            this.resetSessionTimeout();
          });
        } else {
          this.currentUser = null;
          console.log('ℹ️ User signed out');

          // Clear session timeout when user logs out
          this.clearSessionTimeout();
        }

        // Notify listeners
        this.authStateListeners.forEach(listener => {
          listener(this.currentUser);
        });
      });
    }
  }

  // Add auth state listener
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
  }

  // Remove auth state listener
  removeAuthStateListener(listener) {
    this.authStateListeners = this.authStateListeners.filter(l => l !== listener);
  }

  // Notify all listeners of auth state changes
  notifyAuthStateListeners(event, session) {
    this.authStateListeners.forEach(listener => listener(event, session));
  }

  // Send OTP to phone number
  async sendOTP(phoneNumber, countryCode = 'UG') {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('📱 Sending OTP to:', formattedPhone);
      console.log('🔧 Using production-ready OTP service');
      console.log('🌍 Environment:', getEnvironmentName());

      // Send OTP via SMS
      const { data, error } = await this.supabaseClient.auth.signInWithOtp({
        phone: formattedPhone,
        options: {
          channel: 'sms',
        }
      });

      if (error) {
        throw error;
      }

      // Store the phone number for OTP verification
      await SecureStore.setItemAsync('pending_phone', formattedPhone);

      return {
        success: true,
        data: {
          phone: formattedPhone,
          message: 'OTP sent to your phone number'
        }
      };

    } catch (error) {
      console.error('❌ Error sending OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Verify OTP and sign in
  async verifyOTP(phoneNumber, otp, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('🔐 Verifying OTP for:', formattedPhone);

      // Verify OTP with Supabase
      const { data, error } = await this.supabaseClient.auth.verifyOtp({
        phone: formattedPhone,
        token: otp,
        type: 'sms'
      });

      if (error) {
        // Handle specific Supabase error types
        if (error.message?.includes('expired')) {
          throw new Error('OTP has expired. Please request a new code.');
        } else if (error.message?.includes('invalid')) {
          throw new Error('Invalid OTP. Please check and try again.');
        } else if (error.message?.includes('too_many_requests')) {
          throw new Error('Too many attempts. Please wait before trying again.');
        } else {
          throw new Error(error.message || 'OTP verification failed');
        }
      }

      if (data.session && data.user) {
        await this.storeSession(data.session);
        this.currentUser = data.user;

        // Notify auth state listeners to trigger navigation
        this.authStateListeners.forEach(listener => {
          listener(data.user);
        });

        // Clean up pending phone
        await SecureStore.deleteItemAsync('pending_phone');

        return { success: true, data };
      }

      throw new Error('OTP verification failed - no session created');



    } catch (error) {
      console.error('❌ Error verifying OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with password
  async registerUserWithPassword(phoneNumber, password, profileData, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('👤 Registering user with password:', formattedPhone);

      // Create user account with Supabase Auth
      console.log('🔐 Creating user with metadata:', {
        phone: formattedPhone,
        fullName: profileData.fullName,
        countryCode: countryCode,
        preferredLanguage: profileData.preferredLanguage || 'en'
      });

      const { data: authData, error: authError } = await this.supabaseClient.auth.signUp({
        phone: formattedPhone,
        password: password,
        options: {
          data: {
            phone: formattedPhone,
            full_name: profileData.fullName, // ✅ Ensure this is set
            name: profileData.fullName, // ✅ Also set 'name' as backup
            country_code: countryCode,
            preferred_language: profileData.preferredLanguage || 'en'
          }
        }
      });

      console.log('🔐 Auth signup result:', {
        success: !authError,
        userId: authData?.user?.id,
        userMetadata: authData?.user?.user_metadata,
        error: authError?.message
      });

      if (authError) throw authError;

      if (authData.user) {
        // Create user profile in database
        const profileResult = await databaseService.createUserProfile({
          userId: authData.user.id,
          fullName: profileData.fullName,
          phoneNumber: formattedPhone,
          countryCode: countryCode,
          preferredLanguage: profileData.preferredLanguage || 'en',
        });

        if (!profileResult.success) {
          console.warn('⚠️ Profile creation failed, but user account created');
        }

        // Store session if available
        if (authData.session) {
          await this.storeSession(authData.session);
          this.currentUser = authData.user;

          // Notify auth state listeners to trigger navigation
          this.authStateListeners.forEach(listener => {
            listener(authData.user);
          });
        }

        console.log('✅ User registered successfully');
        return { success: true, data: authData };
      }

      throw new Error('User creation failed');
    } catch (error) {
      console.error('❌ Error registering user with password:', error);
      return { success: false, error: error.message };
    }
  }

  // Login method (alias for loginWithPassword for compatibility)
  async login(phoneNumber, password, countryCode = 'UG') {
    return this.loginWithPassword(phoneNumber, password, countryCode);
  }

  // Login with password (PRODUCTION FIX) - Enhanced with comprehensive debugging
  async loginWithPassword(phoneNumber, password, countryCode = 'UG') {
    try {
      console.log('🔐 === PASSWORD LOGIN DEBUG START ===');
      console.log('🔐 Input validation - Phone:', phoneNumber, 'Password length:', password?.length, 'Country:', countryCode);
      console.log('🔐 Using production-ready authentication');
      console.log('🔐 Supabase client available:', !!this.supabaseClient);

      // Basic validation
      if (!phoneNumber || !password) {
        console.log('❌ Validation failed: Missing phone or password');
        return {
          success: false,
          error: 'Phone number and password are required'
        };
      }

      if (password.length < 6) {
        console.log('❌ Validation failed: Password too short');
        return {
          success: false,
          error: 'Password must be at least 6 characters'
        };
      }

      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);
      console.log('🔐 Formatted phone number:', formattedPhone);

      // ALWAYS use real Supabase authentication (production-ready)
      console.log('🏭 PRODUCTION-READY: Attempting Supabase authentication');

      // Test Supabase client connectivity first
      try {
        console.log('🔐 Testing Supabase connectivity...');
        const { data: testData, error: testError } = await this.supabaseClient.auth.getSession();
        console.log('🔐 Supabase connectivity test result:', { hasData: !!testData, error: testError?.message });
      } catch (connectivityError) {
        console.error('❌ Supabase connectivity test failed:', connectivityError);
        return {
          success: false,
          error: 'Unable to connect to authentication service. Please check your internet connection.'
        };
      }

        console.log('🔐 Calling Supabase signInWithPassword...');
        const { data, error } = await this.supabaseClient.auth.signInWithPassword({
          phone: formattedPhone,
          password: password,
        });

        console.log('🔐 Supabase response received');
        console.log('🔐 Data:', data ? { user: !!data.user, session: !!data.session } : 'null');
        console.log('🔐 Error:', error ? { message: error.message, status: error.status } : 'null');

        if (error) {
          console.error('❌ Supabase authentication error:', {
            message: error.message,
            status: error.status,
            name: error.name
          });

          // Handle case where user exists but has no password
          if (error.message?.includes('Invalid login credentials')) {
            console.log('🔍 Checking if user exists without password...');
            try {
              const userExistsResult = await this.checkUserExists(formattedPhone);
              console.log('🔍 User exists check result:', userExistsResult);

              if (userExistsResult.exists && !userExistsResult.hasPassword) {
                return {
                  success: false,
                  error: 'Your account was created with phone verification only. Please set up a password first or continue with OTP login.',
                  requiresPasswordSetup: true
                };
              }
            } catch (checkError) {
              console.error('❌ Error checking user existence:', checkError);
            }
          }

          return {
            success: false,
            error: error.message?.includes('Invalid login credentials')
              ? 'Invalid phone number or password'
              : `Authentication failed: ${error.message}`
          };
        }

        if (data.session) {
          console.log('✅ Session created successfully');
          console.log('🔐 User ID:', data.user?.id);
          console.log('🔐 Session expires at:', data.session.expires_at);

          await this.storeSession(data.session);
          this.currentUser = data.user;

          // Ensure user profile exists after login
          console.log('🔧 Ensuring user profile exists after login...');
          try {
            const profileResult = await profileAutoCreationService.ensureProfileExists(data.user);
            if (profileResult.success) {
              console.log('✅ User profile verified/created after login');
            } else {
              console.warn('⚠️ Could not ensure profile exists:', profileResult.error);
            }
          } catch (profileError) {
            console.warn('⚠️ Profile auto-creation error after login:', profileError.message);
          }

          // Notify auth state listeners to trigger navigation
          console.log('🔐 Notifying', this.authStateListeners.length, 'auth state listeners');
          this.authStateListeners.forEach(listener => {
            try {
              listener(data.user);
            } catch (listenerError) {
              console.error('❌ Auth state listener error:', listenerError);
            }
          });

          console.log('✅ Password login successful');

          return {
            success: true,
            data: data,
            message: 'Login successful'
          };
        }

      console.log('❌ No session created despite no error');
      return {
        success: false,
        error: 'Login failed - no session created'
      };

    } catch (error) {
      console.error('❌ Login error:', error);
      return {
        success: false,
        error: error.message || 'Login failed'
      };
    }
  }

  // Check if user exists and has password (PRODUCTION FIX)
  async checkUserExists(phoneNumber) {
    try {
      console.log('🔍 Checking if user exists:', phoneNumber);

      // Check our user_profiles table to see if user exists
      const { data: profileData } = await supabase
        .from('user_profiles')
        .select('id, phone_number')
        .eq('phone_number', phoneNumber.replace(/^\+256/, '0'))
        .single();

      if (profileData) {
        console.log('✅ User exists in profiles table');
        return {
          exists: true,
          hasPassword: false, // Assume no password for OTP-only users
          userId: profileData.id
        };
      }

      console.log('⚠️ User not found');
      return { exists: false, hasPassword: false };
    } catch (error) {
      console.error('❌ Error checking user existence:', error);
      return { exists: false, hasPassword: false };
    }
  }

  // Set password for existing OTP-only user (PRODUCTION FIX)
  async setPasswordForUser(phoneNumber, password, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('🔑 Setting password for existing user:', formattedPhone);

      // Validate password
      if (!password || password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // First, send OTP to verify user identity
      const otpResult = await this.sendOTP(phoneNumber, countryCode);
      if (!otpResult.success) {
        throw new Error('Failed to send verification OTP');
      }

      return {
        success: true,
        requiresOTPVerification: true,
        message: 'Please verify your identity with the OTP sent to your phone, then we\'ll set up your password.',
        pendingPassword: password // Store temporarily for verification
      };
    } catch (error) {
      console.error('❌ Error setting password:', error);
      return { success: false, error: error.message };
    }
  }

  // Forgot password - reset password with new password
  async forgotPassword(phoneNumber, newPassword, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('🔄 Resetting password for:', formattedPhone);

      // Validate password
      if (newPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Use Supabase password reset (production-ready)
      const { error } = await this.supabaseClient.auth.resetPasswordForEmail(
        `${phoneNumber.replace(/\D/g, '')}@jiranipay.com` // Use consistent email format
      );

      if (error) {
        console.error('❌ Password reset error:', error);
        throw new Error('Failed to send password reset. Please try again.');
      }

      console.log('✅ Password reset email sent');
      return {
        success: true,
        message: 'Password reset instructions sent to your registered email.'
      };
    } catch (error) {
      console.error('❌ Error resetting password:', error);
      return { success: false, error: error.message };
    }
  }

  // Create user profile in database
  async createUserProfile(userId, profileData) {
    try {
      const formattedPhone = this.formatPhoneNumber(profileData.phoneNumber, profileData.countryCode);

      console.log('📝 Creating user profile with data:', {
        userId,
        fullName: profileData.fullName,
        phoneNumber: formattedPhone,
        countryCode: profileData.countryCode
      });

      const result = await databaseService.createUserProfile({
        userId: userId,
        fullName: profileData.fullName,
        phoneNumber: formattedPhone,
        countryCode: profileData.countryCode,
        preferredLanguage: profileData.preferredLanguage || 'en',
      });

      // ✅ CRITICAL FIX: Update user metadata after profile creation
      if (result.success && profileData.fullName) {
        console.log('🔄 Updating user metadata with real name:', profileData.fullName);

        try {
          const { error: updateError } = await this.supabaseClient.auth.admin.updateUserById(userId, {
            user_metadata: {
              full_name: profileData.fullName,
              name: profileData.fullName,
              country_code: profileData.countryCode,
              preferred_language: profileData.preferredLanguage || 'en'
            }
          });

          if (updateError) {
            console.error('⚠️ Failed to update user metadata:', updateError);
          } else {
            console.log('✅ User metadata updated successfully');
          }
        } catch (metadataError) {
          console.error('⚠️ Error updating user metadata:', metadataError);
        }
      }

      console.log('✅ User profile created successfully');
      return result;
    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Production SMS configuration validator
  async validateSMSConfiguration() {
    console.log('🔍 Validating Production SMS Configuration...');

    try {
      // Test SMS configuration with a test phone number
      const testResult = await this.supabaseClient.auth.signInWithOtp({
        phone: '+**********', // Test number to check configuration
      });

      if (testResult.error) {
        const errorMsg = testResult.error.message?.toLowerCase() || '';

        // Check for SMS provider configuration issues
        if (errorMsg.includes('sms') ||
            errorMsg.includes('provider') ||
            errorMsg.includes('not configured') ||
            errorMsg.includes('twilio') ||
            errorMsg.includes('messagebird')) {

          console.error('❌ SMS Provider Not Configured');
          return {
            configured: false,
            error: 'SMS_PROVIDER_NOT_CONFIGURED',
            message: 'SMS provider not configured in Supabase project',
            details: testResult.error.message,
            solution: 'Configure Twilio, MessageBird, or another SMS provider in Supabase Authentication settings'
          };
        }

        // Check for authentication/permission issues
        if (errorMsg.includes('unauthorized') || errorMsg.includes('permission')) {
          return {
            configured: false,
            error: 'SMS_PROVIDER_AUTH_ERROR',
            message: 'SMS provider authentication failed',
            details: testResult.error.message,
            solution: 'Check SMS provider credentials in Supabase settings'
          };
        }
      }

      // If we get here, SMS configuration is likely working
      console.log('✅ SMS Configuration appears to be working');
      return {
        configured: true,
        message: 'SMS provider is configured and accessible'
      };

    } catch (error) {
      console.error('❌ SMS configuration validation failed:', error);
      return {
        configured: false,
        error: 'SMS_VALIDATION_ERROR',
        message: 'Unable to validate SMS configuration',
        details: error.message,
        solution: 'Check network connection and Supabase project settings'
      };
    }
  }

  // Send OTP for new user registration (creates account and sends OTP)
  async sendRegistrationOTP(phoneNumber, password, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('📱 Sending registration OTP to:', formattedPhone);

      // Production mode logging (removed problematic SMS validation)
      console.log('🔧 Using production-ready registration service');
      console.log('🌍 Environment:', getEnvironmentName());

      // Validate password before sending to Supabase (match Supabase requirements exactly)
      if (!password || password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Check for required character types (Supabase requirements)
      const hasDigit = /\d/.test(password);
      const hasLowercase = /[a-z]/.test(password);
      const hasUppercase = /[A-Z]/.test(password);
      const hasSymbol = /[!@#$%^&*()_+\-=\[\]{};'\\:"|<>?,./`~]/.test(password);

      if (!hasDigit) {
        throw new Error('Password must contain at least one digit (0-9)');
      }
      if (!hasLowercase) {
        throw new Error('Password must contain at least one lowercase letter (a-z)');
      }
      if (!hasUppercase) {
        throw new Error('Password must contain at least one uppercase letter (A-Z)');
      }
      if (!hasSymbol) {
        throw new Error('Password must contain at least one symbol (!@#$%^&*()_+-=[]{};\'\\:"|<>?,./`~)');
      }

      // Use Supabase signUp to create user and send OTP (production-ready)
      console.log('🔐 Attempting Supabase signUp with phone and password...');
      console.log('📱 Phone:', formattedPhone);
      console.log('🔑 Password length:', password.length);

      const { data, error } = await this.supabaseClient.auth.signUp({
          phone: formattedPhone,
          password: password, // ✅ NOW PASSING PASSWORD TO SUPABASE
          options: {
            channel: 'sms',
            data: {
              phone: formattedPhone,
              country_code: countryCode,
              // ✅ Add placeholder for name - will be updated after profile creation
              full_name: 'Pending Registration',
              name: 'Pending Registration'
            }
            // User will be created but not confirmed until OTP is verified
          }
        });

        if (error) {
          console.error('❌ Registration OTP error:', error);
          console.error('❌ Error details:', JSON.stringify(error, null, 2));

          // Handle specific registration errors
          if (error.message?.includes('already registered')) {
            throw new Error('This phone number is already registered. Please try logging in instead.');
          } else if (error.message?.includes('invalid phone')) {
            throw new Error('Invalid phone number format. Please check and try again.');
          } else if (error.message?.includes('password')) {
            // Handle password-related errors from Supabase
            if (error.message?.includes('weak') || error.message?.includes('strength')) {
              throw new Error('Password does not meet security requirements. Please ensure it has 8+ characters, digits, lowercase, uppercase, and symbols.');
            } else if (error.message?.includes('valid password')) {
              throw new Error('Invalid password format. Please check password requirements.');
            } else {
              throw new Error(error.message || 'Password validation failed');
            }
          } else if (error.message?.includes('SMS') || error.message?.includes('provider')) {
            // Handle SMS provider configuration issues
            throw new Error('SMS service is not configured. Please contact support or try again later.');
          } else {
            throw new Error(error.message || 'Failed to send registration OTP');
          }
        }

        // Log successful response details
        console.log('✅ Supabase signUp response:', {
          user: data.user ? {
            id: data.user.id,
            phone: data.user.phone,
            confirmed_at: data.user.confirmed_at,
            email_confirmed_at: data.user.email_confirmed_at,
            phone_confirmed_at: data.user.phone_confirmed_at
          } : null,
          session: data.session ? 'Session created' : 'No session (awaiting confirmation)'
        });

        // Check if SMS was actually sent (user should be unconfirmed)
        if (data.user && !data.user.phone_confirmed_at) {
          console.log('✅ User created successfully, awaiting phone confirmation');
          console.log('📱 SMS OTP should be sent to:', formattedPhone);

          return {
            success: true,
            data: {
              phone: formattedPhone,
              message: 'Registration OTP sent to your phone number',
              userId: data.user.id,
              requiresConfirmation: true
            }
          };
        } else if (data.user && data.user.phone_confirmed_at) {
          console.warn('⚠️ User already confirmed - this should not happen for new registrations');
          throw new Error('Phone number already verified. Please try logging in instead.');
      } else {
        console.error('❌ Unexpected response from Supabase signUp');
        throw new Error('Registration failed - unexpected response from server');
      }

    } catch (error) {
      console.error('❌ Error sending registration OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with additional profile data (OTP method)
  async registerUser(phoneNumber, otp, profileData, countryCode = 'UG') {
    try {
      // First verify OTP
      const verifyResult = await this.verifyOTP(phoneNumber, otp, countryCode);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // Create user profile in database if user was created
      if (verifyResult.data?.user) {
        const profileResult = await this.createUserProfile(verifyResult.data.user.id, {
          fullName: profileData.fullName,
          phoneNumber: phoneNumber,
          countryCode: countryCode,
          preferredLanguage: profileData.preferredLanguage || 'en',
        });

        if (!profileResult.success) {
          console.warn('⚠️ Profile creation failed, but user account exists');
        }
      }

      console.log('✅ User registration with OTP successful');
      return { success: true, data: verifyResult.data };
    } catch (error) {
      console.error('❌ Error registering user:', error);
      return { success: false, error: error.message };
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable() {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
      };
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return { available: false, types: [] };
    }
  }

  // Enable biometric authentication for user
  async enableBiometric() {
    try {
      const biometricCheck = await this.isBiometricAvailable();
      
      if (!biometricCheck.available) {
        throw new Error('Biometric authentication not available');
      }

      // Store biometric preference
      await SecureStore.setItemAsync('biometric_enabled', 'true');
      
      return { success: true };
    } catch (error) {
      console.error('Error enabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Authenticate with biometrics
  async authenticateWithBiometric() {
    try {
      const biometricEnabled = await SecureStore.getItemAsync('biometric_enabled');
      
      if (biometricEnabled !== 'true') {
        throw new Error('Biometric authentication not enabled');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access your account',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        // Get stored session and restore it
        const storedSession = await this.getStoredSession();
        if (storedSession) {
          await this.supabaseClient.auth.setSession(storedSession);
          return { success: true };
        }
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Error with biometric authentication:', error);
      return { success: false, error: error.message };
    }
  }

  // Store session securely
  async storeSession(session) {
    try {
      await SecureStore.setItemAsync('user_session', JSON.stringify(session));
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  // Get stored session
  async getStoredSession() {
    try {
      const sessionString = await SecureStore.getItemAsync('user_session');
      return sessionString ? JSON.parse(sessionString) : null;
    } catch (error) {
      console.error('Error getting stored session:', error);
      return null;
    }
  }

  // Clear stored session
  async clearStoredSession() {
    try {
      await SecureStore.deleteItemAsync('user_session');
      console.log('🗑️ Stored session cleared');
    } catch (error) {
      console.error('Error clearing stored session:', error);
    }
  }

  // Sign out user
  async signOut() {
    try {
      console.log('🚪 Signing out user...');

      // Clear session timeout first
      this.clearSessionTimeout();

      // Clear current user
      this.currentUser = null;

      // Clear stored session and biometric data
      await SecureStore.deleteItemAsync('user_session');
      await SecureStore.deleteItemAsync('biometric_enabled');

      // Sign out from Supabase
      await this.supabaseClient.auth.signOut();

      // Manually notify auth state listeners of logout
      this.authStateListeners.forEach(listener => {
        listener(null); // null user = logged out
      });

      console.log('✅ User signed out successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error signing out:', error);

      // Even if Supabase signOut fails, clear local state
      this.clearSessionTimeout();
      this.currentUser = null;
      this.authStateListeners.forEach(listener => {
        listener(null);
      });

      return { success: false, error: error.message };
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get user profile with auto-creation
  async getUserProfile(userId) {
    try {
      console.log('📋 Getting profile for user:', userId);

      const result = await databaseService.getUserProfile(userId);

      // If profile not found, try to auto-create it
      if (!result.success && result.code === 'PROFILE_NOT_FOUND') {
        console.log('⚠️ User profile not found, attempting auto-creation...');

        // Get current user from Supabase auth
        const { data: { user }, error: userError } = await supabase.auth.getUser();

        if (userError || !user || user.id !== userId) {
          console.log('❌ Cannot auto-create profile: user not authenticated or ID mismatch');
          return result; // Return original result
        }

        // Try to auto-create the profile
        const autoCreateResult = await profileAutoCreationService.ensureProfileExists(user);

        if (autoCreateResult.success) {
          console.log('✅ Profile auto-created successfully');
          return { success: true, data: autoCreateResult.data };
        } else {
          console.log('❌ Profile auto-creation failed:', autoCreateResult.error);
          return result; // Return original result
        }
      }

      return result;
    } catch (error) {
      console.error('❌ Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Format phone number to international format for any East African country
  formatPhoneNumber(phoneNumber, countryCode = 'UG') {
    return formatPhone(phoneNumber, countryCode);
  }

  // Detect mobile network provider for any East African country
  detectNetworkProvider(phoneNumber, countryCode = 'UG') {
    const provider = detectProvider(phoneNumber, countryCode);
    return provider ? provider.name : 'Unknown';
  }

  // Validate phone number for any East African country
  validatePhoneNumber(phoneNumber, countryCode = 'UG') {
    return validatePhoneNumber(phoneNumber, countryCode);
  }

  // Get country configuration
  getCountryConfig(countryCode) {
    return getCountryByCode(countryCode);
  }

  // ===== SESSION TIMEOUT MANAGEMENT =====

  /**
   * Setup session timeout manager with app state handling
   */
  setupSessionTimeoutManager() {
    console.log('🕐 Setting up session timeout manager...');

    // Listen for app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange.bind(this));

    // Load user's timeout preference
    this.loadSessionTimeoutPreference();
  }

  /**
   * Load user's session timeout preference from database
   */
  async loadSessionTimeoutPreference() {
    try {
      if (this.currentUser?.id) {
        const securityManagementService = require('./securityManagementService').default;
        const result = await securityManagementService.getSecuritySettings(this.currentUser.id);

        if (result.success && result.data?.session_timeout_minutes) {
          this.sessionTimeoutMinutes = result.data.session_timeout_minutes;
          console.log(`🕐 Loaded session timeout: ${this.sessionTimeoutMinutes} minutes`);
        }
      }
    } catch (error) {
      console.error('❌ Error loading session timeout preference:', error);
    }
  }

  /**
   * Handle app state changes (foreground/background)
   */
  handleAppStateChange(nextAppState) {
    console.log(`🔄 App state changed to: ${nextAppState}`);

    if (nextAppState === 'background' || nextAppState === 'inactive') {
      // App going to background - record the time
      this.backgroundTime = Date.now();
      this.clearSessionTimeout();
      console.log('📱 App backgrounded, pausing session timeout');
    } else if (nextAppState === 'active') {
      // App coming to foreground - check if session expired
      if (this.backgroundTime && this.currentUser) {
        const backgroundDuration = Date.now() - this.backgroundTime;
        const timeoutMs = this.sessionTimeoutMinutes * 60 * 1000;

        console.log(`📱 App foregrounded after ${Math.round(backgroundDuration / 1000)}s`);

        if (backgroundDuration >= timeoutMs) {
          console.log('⏰ Session expired while in background, logging out...');
          this.handleSessionTimeout();
          return;
        }
      }

      // Reset activity time and restart timeout
      this.resetSessionTimeout();
    }
  }

  /**
   * Reset session timeout timer
   */
  resetSessionTimeout() {
    if (!this.currentUser) return;

    this.lastActivityTime = Date.now();
    this.clearSessionTimeout();
    this.startSessionTimeout();
  }

  /**
   * Start session timeout timer
   */
  startSessionTimeout() {
    if (!this.currentUser) return;

    const timeoutMs = this.sessionTimeoutMinutes * 60 * 1000;

    this.sessionTimeoutTimer = setTimeout(() => {
      console.log('⏰ Session timeout reached, logging out...');
      this.handleSessionTimeout();
    }, timeoutMs);

    console.log(`🕐 Session timeout started: ${this.sessionTimeoutMinutes} minutes`);
  }

  /**
   * Clear session timeout timer
   */
  clearSessionTimeout() {
    if (this.sessionTimeoutTimer) {
      clearTimeout(this.sessionTimeoutTimer);
      this.sessionTimeoutTimer = null;
    }
  }

  /**
   * Handle session timeout - automatically log out user
   */
  async handleSessionTimeout() {
    console.log('⏰ Handling session timeout...');

    try {
      // Clear timeout timer
      this.clearSessionTimeout();

      // Log security event
      if (this.currentUser?.id) {
        const securityManagementService = require('./securityManagementService').default;
        await securityManagementService.logSecurityEvent(
          this.currentUser.id,
          'session_timeout',
          { timeout_minutes: this.sessionTimeoutMinutes }
        );
      }

      // Sign out user
      await this.signOut();

      console.log('✅ User logged out due to session timeout');
    } catch (error) {
      console.error('❌ Error handling session timeout:', error);
    }
  }

  /**
   * Update session timeout setting
   */
  updateSessionTimeout(minutes) {
    console.log(`🕐 Updating session timeout to ${minutes} minutes`);
    this.sessionTimeoutMinutes = minutes;

    // Restart timeout with new duration if user is logged in
    if (this.currentUser) {
      this.resetSessionTimeout();
    }
  }

  /**
   * Track user activity to reset timeout
   */
  trackUserActivity() {
    if (this.currentUser) {
      this.resetSessionTimeout();
    }
  }

  /**
   * Cleanup session timeout manager
   */
  cleanupSessionTimeout() {
    this.clearSessionTimeout();

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;

