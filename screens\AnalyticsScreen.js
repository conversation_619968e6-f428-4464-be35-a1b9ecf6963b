import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import { useCurrencyContext } from '../contexts/CurrencyContext';
import analyticsService from '../services/analyticsService';
import currencyService from '../services/currencyService';

const { width } = Dimensions.get('window');

const AnalyticsScreen = ({ navigation }) => {
  // Use theme and currency contexts
  const { theme } = useTheme();
  const { convertAndFormat } = useCurrencyContext();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [spendingByCategory, setSpendingByCategory] = useState([]);
  const [spendingTrend, setSpendingTrend] = useState([]);
  const [insights, setInsights] = useState([]);
  const [budgetRecommendations, setBudgetRecommendations] = useState([]);

  const periods = [
    { id: 'week', name: 'Week', icon: 'calendar-outline' },
    { id: 'month', name: 'Month', icon: 'calendar-outline' },
    { id: 'year', name: 'Year', icon: 'calendar-outline' }
  ];

  useEffect(() => {
    loadAnalytics();
  }, [selectedPeriod]);



  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      // Ensure analytics service is initialized
      if (!analyticsService.isInitialized) {
        await analyticsService.initialize();
      }
      
      // Load analytics data with appropriate intervals
      const categoryData = analyticsService.getSpendingByCategory(selectedPeriod);

      let intervals = 6;
      if (selectedPeriod === 'week') {
        intervals = 8;
      } else if (selectedPeriod === 'year') {
        intervals = 3;
      }

      const trendData = analyticsService.getSpendingTrend(selectedPeriod, intervals);
      const insightsData = analyticsService.getFinancialInsights();
      const budgetData = analyticsService.getBudgetRecommendations();

      setSpendingByCategory(categoryData);
      setSpendingTrend(trendData);
      setInsights(insightsData);
      setBudgetRecommendations(budgetData);
      
    } catch (error) {
      console.error('❌ Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePeriodSelect = (period) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedPeriod(period);
  };

  const formatCurrency = (amount) => {
    try {
      return convertAndFormat(amount);
    } catch (error) {
      return convertAndFormat(amount);
    }
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {periods.map((period) => (
        <TouchableOpacity
          key={period.id}
          style={[
            styles.periodButton,
            selectedPeriod === period.id && styles.selectedPeriodButton
          ]}
          onPress={() => handlePeriodSelect(period.id)}
        >
          <Ionicons
            name={period.icon}
            size={16}
            color={selectedPeriod === period.id ? theme.colors.white : theme.colors.textSecondary}
          />
          <Text style={[
            styles.periodText,
            selectedPeriod === period.id && styles.selectedPeriodText
          ]}>
            {period.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderInsightCard = (insight) => (
    <View key={insight.type} style={styles.insightCard}>
      <View style={[styles.insightIcon, { backgroundColor: insight.color + '20' }]}>
        <Ionicons name={insight.icon} size={24} color={insight.color} />
      </View>
      <View style={styles.insightContent}>
        <Text style={styles.insightTitle}>{insight.title}</Text>
        <Text style={styles.insightDescription}>{insight.description}</Text>
        <Text style={styles.insightAmount}>{formatCurrency(insight.amount)}</Text>
      </View>
    </View>
  );

  const renderCategoryChart = () => {
    const totalSpent = spendingByCategory.reduce((sum, cat) => sum + cat.amount, 0);
    
    return (
      <View style={styles.chartContainer}>
        <Text style={styles.sectionTitle}>Spending by Category</Text>
        
        {spendingByCategory.slice(0, 5).map((category, index) => {
          const percentage = totalSpent > 0 ? (category.amount / totalSpent) * 100 : 0;
          
          return (
            <View key={category.category} style={styles.categoryItem}>
              <View style={styles.categoryLeft}>
                <View style={[styles.categoryIcon, { backgroundColor: category.color + '20' }]}>
                  <Ionicons name={category.icon} size={20} color={category.color} />
                </View>
                <View style={styles.categoryDetails}>
                  <Text style={styles.categoryName}>{category.name}</Text>
                  <Text style={styles.categoryCount}>{category.count} transactions</Text>
                </View>
              </View>
              <View style={styles.categoryRight}>
                <Text style={styles.categoryAmount}>{formatCurrency(category.amount)}</Text>
                <Text style={styles.categoryPercentage}>{percentage.toFixed(1)}%</Text>
              </View>
            </View>
          );
        })}
        
        {spendingByCategory.length === 0 && (
          <View style={styles.emptyState}>
            <Ionicons name="analytics-outline" size={48} color={theme.colors.textSecondary} />
            <Text style={styles.emptyText}>No spending data available</Text>
          </View>
        )}
      </View>
    );
  };

  const renderSpendingTrend = () => {
    if (spendingTrend.length === 0) return null;
    
    const maxAmount = Math.max(...spendingTrend.map(t => Math.max(t.income, t.expenses)));
    
    return (
      <View style={styles.chartContainer}>
        <Text style={styles.sectionTitle}>Spending Trend</Text>
        
        <View style={styles.trendChart}>
          {spendingTrend.map((period, index) => {
            const expenseHeight = maxAmount > 0 ? (period.expenses / maxAmount) * 100 : 0;
            const incomeHeight = maxAmount > 0 ? (period.income / maxAmount) * 100 : 0;
            
            return (
              <View key={index} style={styles.trendBar}>
                <View style={styles.trendBarContainer}>
                  <View 
                    style={[
                      styles.trendBarExpense, 
                      { height: `${expenseHeight}%` }
                    ]} 
                  />
                  <View 
                    style={[
                      styles.trendBarIncome, 
                      { height: `${incomeHeight}%` }
                    ]} 
                  />
                </View>
                <Text style={styles.trendLabel}>{period.period}</Text>
              </View>
            );
          })}
        </View>
        
        <View style={styles.trendLegend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: Colors.accent.coral }]} />
            <Text style={styles.legendText}>Expenses</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: Colors.status.success }]} />
            <Text style={styles.legendText}>Income</Text>
          </View>
        </View>
      </View>
    );
  };



  const renderBudgetRecommendations = () => (
    <View style={styles.chartContainer}>
      <Text style={styles.sectionTitle}>Quick Budget Tips</Text>

      {budgetRecommendations.slice(0, 3).map((budget) => (
        <View key={budget.category} style={styles.budgetItem}>
          <View style={styles.budgetLeft}>
            <View style={[styles.budgetIcon, { backgroundColor: budget.color + '20' }]}>
              <Ionicons name={budget.icon} size={16} color={budget.color} />
            </View>
            <Text style={styles.budgetName}>{budget.name}</Text>
          </View>
          <View style={styles.budgetRight}>
            <Text style={styles.budgetCurrent}>{formatCurrency(budget.currentSpending)}</Text>
            <Text style={styles.budgetRecommended}>
              Suggested: {formatCurrency(budget.recommendedBudget)}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>Spending Analytics</Text>
        </View>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPeriodSelector()}
        
        {/* Financial Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Financial Insights</Text>
          {insights.map(renderInsightCard)}
        </View>

        {/* Category Spending Chart */}
        {renderCategoryChart()}

        {/* Spending Trend */}
        {renderSpendingTrend()}

        {/* Quick Budget Tips */}
        {renderBudgetRecommendations()}

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    padding: 8,
  },
  headerCenter: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: 12,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginVertical: 12,
    borderRadius: 12,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  selectedPeriodButton: {
    backgroundColor: theme.colors.primary,
  },
  periodText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: 4,
    fontWeight: '500',
  },
  selectedPeriodText: {
    color: theme.colors.white,
    fontWeight: '600',
  },
  section: {
    backgroundColor: theme.colors.surface,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  insightCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.neutral.creamLight,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  insightIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 4,
  },
  insightAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  chartContainer: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryDetails: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  categoryCount: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  categoryRight: {
    alignItems: 'flex-end',
  },
  categoryAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  categoryPercentage: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    marginTop: 12,
  },
  trendChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    height: 120,
    marginBottom: 16,
  },
  trendBar: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  trendBarContainer: {
    width: '80%',
    height: 100,
    justifyContent: 'flex-end',
    position: 'relative',
  },
  trendBarExpense: {
    backgroundColor: Colors.accent.coral,
    width: '50%',
    borderRadius: 2,
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  trendBarIncome: {
    backgroundColor: Colors.status.success,
    width: '50%',
    borderRadius: 2,
    position: 'absolute',
    bottom: 0,
    right: 0,
  },
  trendLabel: {
    fontSize: 10,
    color: Colors.neutral.warmGray,
    marginTop: 4,
    textAlign: 'center',
  },
  trendLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 12,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  budgetItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  budgetLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  budgetIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  budgetName: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
  },
  budgetRight: {
    alignItems: 'flex-end',
  },
  budgetCurrent: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  budgetRecommended: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default AnalyticsScreen;
