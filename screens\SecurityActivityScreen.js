import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import { useTheme } from '../contexts/ThemeContext';
import UnifiedBackButton from '../components/UnifiedBackButton';
import authService from '../services/authService';
import securityManagementService from '../services/securityManagementService';
import deviceManagementService from '../services/deviceManagementService';
import { supabase } from '../services/supabaseClient';
import { isProductionMode } from '../config/environment';

// Helper functions for transforming audit logs
const getEventType = (action) => {
  if (action.includes('login') || action.includes('auth')) return 'login';
  if (action.includes('device') || action.includes('trusted')) return 'device';
  if (action.includes('privacy') || action.includes('data')) return 'privacy';
  return 'security';
};

const formatActionName = (action) => {
  const actionMap = {
    'biometric_enabled': 'Biometric Authentication Enabled',
    'biometric_disabled': 'Biometric Authentication Disabled',
    'pin_setup': 'Security PIN Created',
    'pin_changed': 'Security PIN Changed',
    'device_registered': 'New Device Registered',
    'device_trusted': 'Device Trusted',
    'device_removed': 'Device Removed',
    'account_locked': 'Account Locked',
    'login_success': 'Successful Login',
    'login_failed': 'Failed Login Attempt',
    'password_changed': 'Password Changed',
    'two_factor_enabled': 'Two-Factor Authentication Enabled',
    'two_factor_disabled': 'Two-Factor Authentication Disabled'
  };

  return actionMap[action] || action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const getEventDescription = (action, metadata) => {
  const descriptions = {
    'biometric_enabled': 'Biometric authentication was enabled for secure login',
    'biometric_disabled': 'Biometric authentication was disabled',
    'pin_setup': 'A new security PIN was created',
    'pin_changed': 'Security PIN was updated',
    'device_registered': `New device "${metadata?.device_name || 'Unknown'}" was registered`,
    'device_trusted': `Device "${metadata?.device_name || 'Unknown'}" was marked as trusted`,
    'device_removed': `Device "${metadata?.device_name || 'Unknown'}" was removed`,
    'account_locked': `Account locked due to ${metadata?.reason || 'security reasons'}`,
    'login_success': 'Successfully logged into account',
    'login_failed': 'Failed login attempt detected',
    'password_changed': 'Account password was changed',
    'two_factor_enabled': 'Two-factor authentication was enabled',
    'two_factor_disabled': 'Two-factor authentication was disabled'
  };

  return descriptions[action] || `Security action: ${action}`;
};

const getLocationFromMetadata = (metadata) => {
  if (metadata?.location) return metadata.location;
  if (metadata?.location_name) return metadata.location_name;
  return 'Unknown location';
};

const getDeviceFromMetadata = (metadata) => {
  if (metadata?.device_info?.deviceName) return metadata.device_info.deviceName;
  if (metadata?.device_name) return metadata.device_name;
  return 'Unknown device';
};

const getEventStatus = (action) => {
  if (action.includes('failed') || action.includes('locked')) return 'warning';
  if (action.includes('disabled') || action.includes('removed')) return 'info';
  return 'success';
};

const SecurityActivityScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [user, setUser] = useState(null);
  const [securityEvents, setSecurityEvents] = useState([]);
  const [filter, setFilter] = useState('all'); // all, login, security, privacy, device

  useEffect(() => {
    loadSecurityActivity();
  }, []);

  const loadSecurityActivity = async () => {
    try {
      setLoading(true);

      const currentUser = authService.getCurrentUser();
      if (!currentUser) {
        setLoading(false);
        return;
      }

      setUser(currentUser);
      console.log('🔍 Loading security activity for user:', currentUser.id);

      if (isProductionMode()) {
        // PRODUCTION MODE: Load real audit logs from database
        console.log('🔒 Production mode: Loading real security activity');

        try {
          const { data: auditLogs, error } = await supabase
            .from('audit_logs')
            .select('*')
            .eq('user_id', currentUser.id)
            .order('created_at', { ascending: false })
            .limit(50);

          if (error) {
            console.error('❌ Error loading audit logs:', error);

            // If table doesn't exist, show informative message
            if (error.code === '42P01') {
              setSecurityEvents([]);
              Alert.alert(t('databaseSetupRequired'), t('securityActivityTrackingRequiresDatabaseSetupPleas')
              );
              return;
            }

            throw error;
          }

          // Transform audit logs to security events format
          const transformedEvents = auditLogs.map(log => ({
            id: log.id,
            type: getEventType(log.action),
            action: formatActionName(log.action),
            description: getEventDescription(log.action, log.new_values),
            timestamp: log.created_at,
            location: getLocationFromMetadata(log.new_values),
            device: getDeviceFromMetadata(log.new_values),
            ipAddress: log.ip_address || 'Unknown',
            status: getEventStatus(log.action),
            metadata: log.new_values
          }));

          console.log(`✅ Loaded ${transformedEvents.length} real security events`);
          setSecurityEvents(transformedEvents);

        } catch (error) {
          console.error('❌ Error loading security activity:', error);
          setSecurityEvents([]);
          Alert.alert(t('error'), t('failedToLoadSecurityActivity') + error.message);
        }
      } else {
        // DEVELOPMENT MODE: Show informative message
        console.log('🔧 Development mode: Security activity requires production mode');
        setSecurityEvents([]);
        Alert.alert(t('developmentMode'), t('securityActivityTrackingIsOnlyAvailableInProductio')
        );
      }
    } catch (error) {
      console.error('❌ Error loading security activity:', error);
      Alert.alert(t('error'), t('failedToLoadSecurityActivity'));
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadSecurityActivity();
  };

  const getEventIcon = (type, status) => {
    if (status === 'warning') return 'warning';

    switch (type) {
      case 'login':
        return 'log-in';
      case 'security':
        return 'shield-checkmark';
      case 'privacy':
        return 'eye';
      case 'device':
        return 'phone-portrait';
      default:
        return 'information-circle';
    }
  };

  const getEventColor = (status) => {
    switch (status) {
      case 'success':
        return Colors.status.success;
      case 'warning':
        return Colors.accent.gold;
      case 'error':
        return Colors.status.error;
      default:
        return Colors.primary.main;
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    
    return date.toLocaleDateString();
  };

  const filteredEvents = securityEvents.filter(event => 
    filter === 'all' || event.type === filter
  );

  const renderFilterButton = (filterType, label) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.activeFilterButton
      ]}
      onPress={() => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        setFilter(filterType);
      }}
      activeOpacity={0.7}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterType && styles.activeFilterButtonText
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderSecurityEvent = (event) => (
    <TouchableOpacity
      key={event.id}
      style={styles.eventCard}
      onPress={() => {
        Alert.alert(t('eventDetails'), t('actionEventactionndescriptionEventdescriptionndevi'),
          [{ text: 'OK' }]
        );
      }}
      activeOpacity={0.7}
    >
      <View style={styles.eventLeft}>
        <View style={[styles.eventIcon, { backgroundColor: getEventColor(event.status) + '20' }]}>
          <Ionicons 
            name={getEventIcon(event.type, event.status)} 
            size={20} 
            color={getEventColor(event.status)} 
          />
        </View>
        <View style={styles.eventInfo}>
          <Text style={styles.eventAction}>{event.action}</Text>
          <Text style={styles.eventDescription}>{event.description}</Text>
          <View style={styles.eventMeta}>
            <Text style={styles.eventDevice}>
              <Ionicons name="hardware-chip" size={12} color={Colors.neutral.warmGray} />
              {' '}{event.device}
            </Text>
            <Text style={styles.eventLocation}>
              <Ionicons name="location" size={12} color={Colors.neutral.warmGray} />
              {' '}{event.location}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.eventRight}>
        <Text style={styles.eventTime}>{formatTimestamp(event.timestamp)}</Text>
        <Ionicons name="chevron-forward" size={16} color={Colors.neutral.warmGray} />
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>{t('loadingSecurityActivity')}</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Colors.gradients.sunset}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <UnifiedBackButton 
            navigation={navigation}
            style={styles.backButton}
            iconColor={Colors.neutral.white}
            iconSize={24}
          />
          <Text style={styles.headerTitle}>{t('securityActivity')}</Text>
          <View style={styles.placeholder} />
        </View>
        <Text style={styles.headerSubtitle}>
          {t('monitorYourAccountSecurityEvents')}
        </Text>
      </LinearGradient>

      <ScrollView 
        style={styles.content} 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[Colors.primary.main]}
            tintColor={Colors.primary.main}
          />
        }
      >
        {/* Summary */}
        <View style={styles.section}>
          <View style={styles.summaryCard}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryNumber}>{securityEvents.length}</Text>
              <Text style={styles.summaryLabel}>{t('totalEvents')}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryNumber, { color: Colors.status.success }]}>
                {securityEvents.filter(e => e.status === 'success').length}
              </Text>
              <Text style={styles.summaryLabel}>{t('successful')}</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.summaryItem}>
              <Text style={[styles.summaryNumber, { color: Colors.accent.gold }]}>
                {securityEvents.filter(e => e.status === 'warning').length}
              </Text>
              <Text style={styles.summaryLabel}>{t('warnings')}</Text>
            </View>
          </View>
        </View>

        {/* Filters */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('filterEvents')}</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
            {renderFilterButton('all', 'All Events')}
            {renderFilterButton('login', 'Login')}
            {renderFilterButton('security', 'Security')}
            {renderFilterButton('privacy', 'Privacy')}
          </ScrollView>
        </View>

        {/* Events List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('recentActivity')}</Text>
          <Text style={styles.sectionDescription}>
            {filteredEvents.length} {filter === 'all' ? 'events' : `${filter} events`} found
          </Text>

          {filteredEvents.length > 0 ? (
            filteredEvents.map(renderSecurityEvent)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="shield-checkmark" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.emptyTitle}>{t('noEventsFound')}</Text>
              <Text style={styles.emptyDescription}>
                {t('noSecurityEventsMatchYourCurrentFilter')}
              </Text>
            </View>
          )}
        </View>

        {/* Security Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('securityTips')}</Text>
          
          <View style={styles.tipCard}>
            <Ionicons name="eye" size={24} color={Colors.primary.main} />
            <View style={styles.tipText}>
              <Text style={styles.tipTitle}>{t('monitorRegularly')}</Text>
              <Text style={styles.tipDescription}>
                {t('checkYourSecurityActivityRegularlyForAnySuspicious')}
              </Text>
            </View>
          </View>

          <View style={styles.tipCard}>
            <Ionicons name="warning" size={24} color={Colors.accent.gold} />
            <View style={styles.tipText}>
              <Text style={styles.tipTitle}>{t('reportSuspiciousActivity')}</Text>
              <Text style={styles.tipDescription}>
                {t('contactSupportImmediatelyIfYouSeeAnyUnauthorizedAc')}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  header: {
    paddingTop: 20,
    paddingBottom: 30,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  backButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.white,
  },
  placeholder: {
    width: 40,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.neutral.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
    marginTop: -15,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
  },
  section: {
    marginBottom: 30,
    paddingHorizontal: 20,
  },
  summaryCard: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  summaryDivider: {
    width: 1,
    backgroundColor: Colors.neutral.lightGray,
    marginHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 15,
    lineHeight: 20,
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.neutral.lightGray,
    marginRight: 12,
  },
  activeFilterButton: {
    backgroundColor: Colors.primary.main,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.warmGray,
  },
  activeFilterButtonText: {
    color: Colors.neutral.white,
  },
  eventCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  eventLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  eventIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  eventInfo: {
    flex: 1,
  },
  eventAction: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  eventDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginBottom: 6,
  },
  eventMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventDevice: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginRight: 12,
  },
  eventLocation: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  eventRight: {
    alignItems: 'flex-end',
  },
  eventTime: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    marginBottom: 4,
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    lineHeight: 20,
  },
  tipCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: Colors.neutral.charcoal,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tipText: {
    flex: 1,
    marginLeft: 12,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  tipDescription: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    lineHeight: 20,
  },
});

export default SecurityActivityScreen;
