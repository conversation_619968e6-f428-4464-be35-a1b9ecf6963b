import { useEffect, useRef } from 'react';
import { PanResponder } from 'react-native';
import authService from '../services/authService';

/**
 * Hook to track user activity and reset session timeout
 * This hook should be used in the main App component or navigation container
 */
export const useUserActivity = () => {
  const lastActivityRef = useRef(Date.now());
  const panResponderRef = useRef(null);

  useEffect(() => {
    // Track activity function
    const trackActivity = () => {
      const now = Date.now();
      // Only track if it's been more than 1 second since last activity
      // This prevents excessive calls during continuous interactions
      if (now - lastActivityRef.current > 1000) {
        lastActivityRef.current = now;
        authService.trackUserActivity();
      }
    };

    // Create PanResponder to detect touch events
    panResponderRef.current = PanResponder.create({
      onStartShouldSetPanResponder: () => {
        // Track activity on any touch
        trackActivity();
        return false; // Don't capture the touch, let it pass through
      },
      onMoveShouldSetPanResponder: () => {
        // Track activity on any movement
        trackActivity();
        return false;
      },
    });

    // Cleanup function
    return () => {
      // No cleanup needed for PanResponder
    };
  }, []);

  // Return the pan responder handlers to be spread on the root View
  return panResponderRef.current?.panHandlers || {};
};

/**
 * Hook to manually track specific user activities
 * Use this for specific actions like button presses, navigation, etc.
 */
export const useActivityTracker = () => {
  const trackActivity = () => {
    authService.trackUserActivity();
  };

  return { trackActivity };
};

export default useUserActivity;
