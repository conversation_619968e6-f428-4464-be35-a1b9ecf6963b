import AsyncStorage from '@react-native-async-storage/async-storage';
import { isProductionMode } from '../config/environment';
import consentEnforcementService from './consentEnforcementService';

class AnalyticsService {
  constructor() {
    this.isInitialized = false;
    this.transactions = [];
    this.categories = {
      'bills': { name: 'Bills & Utilities', color: '#E67E22', icon: 'receipt-outline' },
      'transfer': { name: 'Money Transfers', color: '#3498DB', icon: 'send-outline' },
      'topup': { name: 'Wallet Top-ups', color: '#27AE60', icon: 'add-circle-outline' },
      'airtime': { name: 'Airtime & Data', color: '#9B59B6', icon: 'phone-portrait-outline' },
      'shopping': { name: 'Shopping', color: '#E74C3C', icon: 'bag-outline' },
      'food': { name: 'Food & Dining', color: '#F39C12', icon: 'restaurant-outline' },
      'transport': { name: 'Transport', color: '#1ABC9C', icon: 'car-outline' },
      'entertainment': { name: 'Entertainment', color: '#E91E63', icon: 'musical-notes-outline' },
      'other': { name: 'Other', color: '#95A5A6', icon: 'ellipsis-horizontal-outline' }
    };
  }

  async initialize() {
    try {
      console.log('📊 Initializing analytics service...');
      
      // Load stored transactions
      await this.loadTransactions();
      
      this.isInitialized = true;
      console.log('✅ Analytics service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing analytics service:', error);
      return { success: false, error: error.message };
    }
  }

  async loadTransactions() {
    try {
      // Load real transaction data from storage or start with empty array
      console.log('📊 Loading transaction data for analytics');

      const stored = await AsyncStorage.getItem('analytics_transactions');
      if (stored) {
        this.transactions = JSON.parse(stored);
        console.log('📊 Loaded', this.transactions.length, 'transactions for analytics');
      } else {
        // Start with empty transactions array
        this.transactions = [];
        console.log('📊 Starting with empty transaction data');
      }
    } catch (error) {
      console.error('❌ Error loading transactions:', error);
      // Fallback to empty array
      this.transactions = [];
    }
  }

  async saveTransactions() {
    try {
      await AsyncStorage.setItem('analytics_transactions', JSON.stringify(this.transactions));
    } catch (error) {
      console.error('❌ Error saving transactions:', error);
    }
  }

  // Mock data generation methods removed for production mode

  async addTransaction(transaction) {
    try {
      // Check analytics consent before processing
      const userId = transaction.user_id;
      if (userId) {
        const hasConsent = await consentEnforcementService.hasConsent(userId, 'analytics');
        if (!hasConsent) {
          console.log('🚫 Analytics blocked - no consent for transaction tracking');
          return;
        }
      }

      this.transactions.unshift({
        ...transaction,
        timestamp: new Date(transaction.timestamp)
      });

      // Keep only last 1000 transactions for performance
      if (this.transactions.length > 1000) {
        this.transactions = this.transactions.slice(0, 1000);
      }

      await this.saveTransactions();
      console.log('📊 Transaction added to analytics');

      // Track analytics event
      if (userId) {
        await consentEnforcementService.trackAnalytics(userId, 'transaction_processed', {
          type: transaction.type,
          amount: transaction.amount,
          category: transaction.category
        });
      }
    } catch (error) {
      console.error('❌ Error adding transaction to analytics:', error);
    }
  }

  getSpendingByCategory(period = 'month') {
    const now = new Date();
    let startDate;
    
    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }
    
    const filteredTransactions = this.transactions.filter(t => 
      t.type === 'debit' && 
      new Date(t.timestamp) >= startDate &&
      t.status === 'completed'
    );
    
    const categoryTotals = {};
    
    filteredTransactions.forEach(transaction => {
      const category = transaction.category || 'other';
      if (!categoryTotals[category]) {
        categoryTotals[category] = {
          ...this.categories[category],
          amount: 0,
          count: 0,
          transactions: []
        };
      }
      categoryTotals[category].amount += transaction.amount;
      categoryTotals[category].count += 1;
      categoryTotals[category].transactions.push(transaction);
    });
    
    return Object.entries(categoryTotals)
      .map(([key, data]) => ({ category: key, ...data }))
      .sort((a, b) => b.amount - a.amount);
  }

  getSpendingTrend(period = 'month', intervals = 6) {
    const now = new Date();
    const trend = [];

    // Adjust intervals based on period
    let actualIntervals = intervals;
    if (period === 'year') {
      actualIntervals = Math.min(intervals, 3); // Show max 3 years
    } else if (period === 'week') {
      actualIntervals = Math.min(intervals, 8); // Show max 8 weeks
    }

    for (let i = actualIntervals - 1; i >= 0; i--) {
      let startDate, endDate, label;

      if (period === 'month') {
        startDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        endDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
        label = startDate.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      } else if (period === 'week') {
        const weekStart = new Date(now.getTime() - (i * 7 * 24 * 60 * 60 * 1000));
        startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() - weekStart.getDay());
        endDate = new Date(startDate.getTime() + (6 * 24 * 60 * 60 * 1000));
        label = `Week ${actualIntervals - i}`;
      } else if (period === 'year') {
        startDate = new Date(now.getFullYear() - i, 0, 1);
        endDate = new Date(now.getFullYear() - i, 11, 31);
        label = (now.getFullYear() - i).toString();
      }

      const periodTransactions = this.transactions.filter(t => {
        const transactionDate = new Date(t.timestamp);
        return transactionDate >= startDate &&
               transactionDate <= endDate &&
               t.status === 'completed';
      });

      const income = periodTransactions
        .filter(t => t.type === 'credit')
        .reduce((sum, t) => sum + t.amount, 0);

      const expenses = periodTransactions
        .filter(t => t.type === 'debit')
        .reduce((sum, t) => sum + t.amount, 0);

      trend.push({
        period: label,
        income,
        expenses,
        net: income - expenses,
        startDate,
        endDate
      });
    }

    return trend;
  }

  getFinancialInsights() {
    const currentMonth = this.getSpendingByCategory('month');
    const previousMonth = this.getSpendingByCategory('month'); // Would need proper date filtering
    const trend = this.getSpendingTrend('month', 3);
    
    const totalSpent = currentMonth.reduce((sum, cat) => sum + cat.amount, 0);
    const avgTransaction = totalSpent / currentMonth.reduce((sum, cat) => sum + cat.count, 0) || 0;
    
    const insights = [];
    
    // Top spending category
    if (currentMonth.length > 0) {
      const topCategory = currentMonth[0];
      insights.push({
        type: 'top_category',
        title: 'Top Spending Category',
        description: `You spent the most on ${topCategory.name} this month`,
        amount: topCategory.amount,
        icon: topCategory.icon,
        color: topCategory.color
      });
    }
    
    // Spending trend
    if (trend.length >= 2) {
      const current = trend[trend.length - 1];
      const previous = trend[trend.length - 2];
      const change = ((current.expenses - previous.expenses) / previous.expenses) * 100;
      
      insights.push({
        type: 'spending_trend',
        title: change > 0 ? 'Spending Increased' : 'Spending Decreased',
        description: `Your spending ${change > 0 ? 'increased' : 'decreased'} by ${Math.abs(change).toFixed(1)}% this month`,
        amount: current.expenses,
        change: change,
        icon: change > 0 ? 'trending-up-outline' : 'trending-down-outline',
        color: change > 0 ? '#E74C3C' : '#27AE60'
      });
    }
    
    // Average transaction
    insights.push({
      type: 'avg_transaction',
      title: 'Average Transaction',
      description: `Your average transaction amount this month`,
      amount: avgTransaction,
      icon: 'analytics-outline',
      color: '#3498DB'
    });
    
    return insights;
  }

  getBudgetRecommendations() {
    const spending = this.getSpendingByCategory('month');
    const totalSpent = spending.reduce((sum, cat) => sum + cat.amount, 0);

    return spending.map(category => ({
      category: category.category,
      name: category.name,
      currentSpending: category.amount,
      recommendedBudget: Math.ceil(category.amount * 1.1 / 10000) * 10000, // Round up to nearest 10k
      percentage: (category.amount / totalSpent) * 100,
      color: category.color,
      icon: category.icon
    }));
  }

  getBudgetInsights() {
    const currentMonth = this.getSpendingByCategory('month');
    const previousMonth = this.getPreviousMonthSpending();
    const trend = this.getSpendingTrend('month', 3);

    const totalSpent = currentMonth.reduce((sum, cat) => sum + cat.amount, 0);
    const totalIncome = this.getCurrentMonthIncome();
    const savingsRate = totalIncome > 0 ? ((totalIncome - totalSpent) / totalIncome) * 100 : 0;

    // Budget categories with recommended allocations
    const budgetCategories = [
      { category: 'bills', name: 'Bills & Utilities', recommendedPercentage: 25, priority: 'high' },
      { category: 'food', name: 'Food & Dining', recommendedPercentage: 15, priority: 'high' },
      { category: 'transport', name: 'Transport', recommendedPercentage: 10, priority: 'medium' },
      { category: 'shopping', name: 'Shopping', recommendedPercentage: 10, priority: 'medium' },
      { category: 'entertainment', name: 'Entertainment', recommendedPercentage: 5, priority: 'low' },
      { category: 'airtime', name: 'Airtime & Data', recommendedPercentage: 5, priority: 'medium' },
      { category: 'other', name: 'Other', recommendedPercentage: 10, priority: 'low' },
      { category: 'savings', name: 'Savings', recommendedPercentage: 20, priority: 'high' }
    ];

    // Calculate budget vs actual for each category
    const budgetAnalysis = budgetCategories.map(budgetCat => {
      const actualSpending = currentMonth.find(cat => cat.category === budgetCat.category);
      const actualAmount = actualSpending ? actualSpending.amount : 0;
      const recommendedAmount = (totalIncome * budgetCat.recommendedPercentage) / 100;
      const variance = actualAmount - recommendedAmount;
      const variancePercentage = recommendedAmount > 0 ? (variance / recommendedAmount) * 100 : 0;

      return {
        ...budgetCat,
        actualAmount,
        recommendedAmount,
        variance,
        variancePercentage,
        status: variance > recommendedAmount * 0.1 ? 'over' : variance < -recommendedAmount * 0.1 ? 'under' : 'good',
        color: this.categories[budgetCat.category]?.color || '#95A5A6',
        icon: this.categories[budgetCat.category]?.icon || 'ellipsis-horizontal-outline'
      };
    });

    // Generate insights and recommendations
    const insights = [];

    // Savings rate insight
    insights.push({
      type: 'savings_rate',
      title: 'Savings Rate',
      description: savingsRate >= 20 ? 'Great job! You\'re saving well' : savingsRate >= 10 ? 'Good savings rate, try to increase it' : 'Consider increasing your savings',
      value: savingsRate,
      target: 20,
      status: savingsRate >= 20 ? 'excellent' : savingsRate >= 10 ? 'good' : 'needs_improvement',
      icon: 'trending-up-outline',
      color: savingsRate >= 20 ? '#27AE60' : savingsRate >= 10 ? '#F39C12' : '#E74C3C'
    });

    // Budget adherence
    const overBudgetCategories = budgetAnalysis.filter(cat => cat.status === 'over' && cat.priority === 'high');
    if (overBudgetCategories.length > 0) {
      const topOverspend = overBudgetCategories.sort((a, b) => b.variance - a.variance)[0];
      insights.push({
        type: 'overspending',
        title: 'Budget Alert',
        description: `You're overspending on ${topOverspend.name} by ${Math.abs(topOverspend.variancePercentage).toFixed(1)}%`,
        category: topOverspend.category,
        amount: topOverspend.variance,
        icon: 'warning-outline',
        color: '#E74C3C'
      });
    }

    // Spending efficiency
    const essentialSpending = budgetAnalysis
      .filter(cat => cat.priority === 'high' && cat.category !== 'savings')
      .reduce((sum, cat) => sum + cat.actualAmount, 0);
    const essentialPercentage = totalSpent > 0 ? (essentialSpending / totalSpent) * 100 : 0;

    insights.push({
      type: 'spending_efficiency',
      title: 'Essential Spending',
      description: `${essentialPercentage.toFixed(1)}% of your spending goes to essentials`,
      value: essentialPercentage,
      target: 60,
      status: essentialPercentage <= 60 ? 'good' : 'needs_improvement',
      icon: 'pie-chart-outline',
      color: essentialPercentage <= 60 ? '#27AE60' : '#F39C12'
    });

    return {
      budgetAnalysis,
      insights,
      totalIncome,
      totalSpent,
      savingsRate,
      monthlyTrend: trend
    };
  }

  getPreviousMonthSpending() {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endDate = new Date(now.getFullYear(), now.getMonth(), 0);

    const filteredTransactions = this.transactions.filter(t =>
      t.type === 'debit' &&
      new Date(t.timestamp) >= startDate &&
      new Date(t.timestamp) <= endDate &&
      t.status === 'completed'
    );

    return filteredTransactions.reduce((sum, t) => sum + t.amount, 0);
  }

  getCurrentMonthIncome() {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), 1);

    const incomeTransactions = this.transactions.filter(t =>
      t.type === 'credit' &&
      new Date(t.timestamp) >= startDate &&
      t.status === 'completed'
    );

    return incomeTransactions.reduce((sum, t) => sum + t.amount, 0);
  }

  getCategories() {
    return this.categories;
  }
}

// Create and export singleton instance
const analyticsService = new AnalyticsService();
export default analyticsService;
