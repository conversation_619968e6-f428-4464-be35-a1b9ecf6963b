import AsyncStorage from '@react-native-async-storage/async-storage';
import savingsService from './savingsService';
import walletService from './walletService';
import notificationService from './notificationService';

class AutomaticSavingsService {
  constructor() {
    this.isInitialized = false;
    this.automaticSavingsPlans = [];
    this.reminders = [];
    this.storageKey = 'automatic_savings_plans';
    this.remindersKey = 'savings_reminders';
  }

  async initialize() {
    try {
      console.log('🤖 Initializing automatic savings service...');
      
      // Load existing plans and reminders
      await this.loadAutomaticSavingsPlans();
      await this.loadReminders();
      
      this.isInitialized = true;
      console.log('✅ Automatic savings service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing automatic savings service:', error);
      return { success: false, error: error.message };
    }
  }

  async loadAutomaticSavingsPlans() {
    try {
      const stored = await AsyncStorage.getItem(this.storageKey);
      if (stored) {
        this.automaticSavingsPlans = JSON.parse(stored);
        console.log('📋 Loaded automatic savings plans:', this.automaticSavingsPlans.length);
      }
    } catch (error) {
      console.error('❌ Error loading automatic savings plans:', error);
      this.automaticSavingsPlans = [];
    }
  }

  async saveAutomaticSavingsPlans() {
    try {
      await AsyncStorage.setItem(this.storageKey, JSON.stringify(this.automaticSavingsPlans));
    } catch (error) {
      console.error('❌ Error saving automatic savings plans:', error);
    }
  }

  async loadReminders() {
    try {
      const stored = await AsyncStorage.getItem(this.remindersKey);
      if (stored) {
        this.reminders = JSON.parse(stored);
        console.log('🔔 Loaded savings reminders:', this.reminders.length);
      }
    } catch (error) {
      console.error('❌ Error loading reminders:', error);
      this.reminders = [];
    }
  }

  async saveReminders() {
    try {
      await AsyncStorage.setItem(this.remindersKey, JSON.stringify(this.reminders));
    } catch (error) {
      console.error('❌ Error saving reminders:', error);
    }
  }

  /**
   * Create an automatic savings plan
   */
  async createAutomaticSavingsPlan(planData) {
    try {
      const {
        name,
        description,
        amount,
        frequency, // 'weekly', 'monthly', 'custom'
        customInterval, // for custom frequency (in days)
        targetSavingsAccountId,
        startDate,
        endDate = null,
        isActive = true,
        reminderEnabled = true,
        reminderMinutes = 60 // 1 hour before
      } = planData;

      const plan = {
        id: `auto_savings_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        amount: parseFloat(amount),
        frequency,
        customInterval: customInterval || null,
        targetSavingsAccountId,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        nextExecutionDate: this.calculateNextExecutionDate(new Date(startDate), frequency, customInterval),
        isActive,
        reminderEnabled,
        reminderMinutes,
        createdAt: new Date(),
        lastExecuted: null,
        executionCount: 0,
        totalSaved: 0,
        failureCount: 0
      };

      this.automaticSavingsPlans.push(plan);
      await this.saveAutomaticSavingsPlans();

      console.log('✅ Automatic savings plan created:', plan.name);
      return { success: true, data: plan };
    } catch (error) {
      console.error('❌ Error creating automatic savings plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a savings reminder (without automatic execution)
   */
  async createSavingsReminder(reminderData) {
    try {
      const {
        title,
        description,
        recommendedAmount,
        frequency, // 'daily', 'weekly', 'monthly'
        reminderTime, // time of day to remind (e.g., '09:00')
        isActive = true,
        recommendationId = null // Link to budget insight recommendation
      } = reminderData;

      const reminder = {
        id: `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title,
        description,
        recommendedAmount: parseFloat(recommendedAmount),
        frequency,
        reminderTime,
        isActive,
        recommendationId,
        createdAt: new Date(),
        lastSent: null,
        nextReminderDate: this.calculateNextReminderDate(frequency, reminderTime)
      };

      this.reminders.push(reminder);
      await this.saveReminders();

      console.log('✅ Savings reminder created:', reminder.title);
      return { success: true, data: reminder };
    } catch (error) {
      console.error('❌ Error creating savings reminder:', error);
      return { success: false, error: error.message };
    }
  }

  calculateNextExecutionDate(startDate, frequency, customInterval = null) {
    const nextDate = new Date(startDate);
    
    switch (frequency) {
      case 'weekly':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'custom':
        if (customInterval) {
          nextDate.setDate(nextDate.getDate() + customInterval);
        }
        break;
      default:
        nextDate.setDate(nextDate.getDate() + 7); // Default to weekly
    }
    
    return nextDate;
  }

  calculateNextReminderDate(frequency, reminderTime) {
    const now = new Date();
    const nextDate = new Date();
    
    // Parse reminder time (e.g., '09:00')
    const [hours, minutes] = reminderTime.split(':').map(Number);
    nextDate.setHours(hours, minutes, 0, 0);
    
    // If time has passed today, start from tomorrow
    if (nextDate <= now) {
      nextDate.setDate(nextDate.getDate() + 1);
    }
    
    switch (frequency) {
      case 'daily':
        // Already set for tomorrow if needed
        break;
      case 'weekly':
        // Set to next week same day
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'monthly':
        // Set to next month same date
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
    }
    
    return nextDate;
  }

  /**
   * Execute automatic savings transfers
   */
  async executeAutomaticSavings() {
    const now = new Date();
    
    for (const plan of this.automaticSavingsPlans) {
      if (!plan.isActive) continue;
      
      // Check if execution is due
      if (plan.nextExecutionDate <= now) {
        await this.executeSavingsPlan(plan);
      }
      
      // Check if reminder should be sent
      if (plan.reminderEnabled) {
        const reminderTime = new Date(plan.nextExecutionDate.getTime() - (plan.reminderMinutes * 60 * 1000));
        if (reminderTime <= now && reminderTime > new Date(now.getTime() - 5 * 60 * 1000)) {
          await this.sendSavingsReminder(plan);
        }
      }
    }
  }

  async executeSavingsPlan(plan) {
    try {
      console.log('🤖 Executing automatic savings plan:', plan.name);
      
      // Transfer money to savings
      const result = await savingsService.transferToSavings(
        plan.targetSavingsAccountId,
        plan.amount,
        `Automatic savings: ${plan.name}`
      );
      
      if (result.success) {
        // Update plan statistics
        plan.lastExecuted = new Date();
        plan.executionCount++;
        plan.totalSaved += plan.amount;
        plan.nextExecutionDate = this.calculateNextExecutionDate(
          plan.nextExecutionDate,
          plan.frequency,
          plan.customInterval
        );
        
        // Send success notification
        await notificationService.scheduleTransactionNotification({
          id: `auto_savings_${plan.id}`,
          type: 'credit',
          amount: plan.amount,
          description: `Automatic savings: ${plan.name}`,
          status: 'completed'
        });
        
        console.log('✅ Automatic savings executed successfully');
      } else {
        plan.failureCount++;
        console.log('❌ Automatic savings failed:', result.error);
        
        // Send failure notification
        await notificationService.scheduleTransactionNotification({
          id: `auto_savings_failed_${plan.id}`,
          type: 'debit',
          amount: plan.amount,
          description: `Failed automatic savings: ${plan.name}`,
          status: 'failed'
        });
      }
      
      await this.saveAutomaticSavingsPlans();
    } catch (error) {
      console.error('❌ Error executing savings plan:', error);
    }
  }

  async sendSavingsReminder(plan) {
    try {
      await notificationService.scheduleTransactionNotification({
        id: `reminder_${plan.id}`,
        type: 'debit',
        amount: plan.amount,
        description: `Reminder: Automatic savings "${plan.name}" will execute soon`,
        status: 'pending'
      });
      
      console.log('🔔 Savings reminder sent for:', plan.name);
    } catch (error) {
      console.error('❌ Error sending savings reminder:', error);
    }
  }

  /**
   * Process savings reminders (non-automatic)
   */
  async processSavingsReminders() {
    const now = new Date();
    
    for (const reminder of this.reminders) {
      if (!reminder.isActive) continue;
      
      if (reminder.nextReminderDate <= now) {
        await this.sendReminderNotification(reminder);
        
        // Update next reminder date
        reminder.lastSent = new Date();
        reminder.nextReminderDate = this.calculateNextReminderDate(
          reminder.frequency,
          reminder.reminderTime
        );
      }
    }
    
    await this.saveReminders();
  }

  async sendReminderNotification(reminder) {
    try {
      await notificationService.scheduleTransactionNotification({
        id: `savings_reminder_${reminder.id}`,
        type: 'debit',
        amount: reminder.recommendedAmount,
        description: `💰 ${reminder.title}: ${reminder.description}`,
        status: 'pending'
      });
      
      console.log('🔔 Savings reminder notification sent:', reminder.title);
    } catch (error) {
      console.error('❌ Error sending reminder notification:', error);
    }
  }

  // Getter methods
  getAutomaticSavingsPlans() {
    return this.automaticSavingsPlans.filter(plan => plan.isActive);
  }

  getSavingsReminders() {
    return this.reminders.filter(reminder => reminder.isActive);
  }

  async updatePlan(planId, updates) {
    try {
      const planIndex = this.automaticSavingsPlans.findIndex(plan => plan.id === planId);
      if (planIndex === -1) {
        return { success: false, error: 'Plan not found' };
      }

      this.automaticSavingsPlans[planIndex] = {
        ...this.automaticSavingsPlans[planIndex],
        ...updates,
        updatedAt: new Date()
      };

      await this.saveAutomaticSavingsPlans();
      return { success: true, data: this.automaticSavingsPlans[planIndex] };
    } catch (error) {
      console.error('❌ Error updating plan:', error);
      return { success: false, error: error.message };
    }
  }

  async deletePlan(planId) {
    try {
      this.automaticSavingsPlans = this.automaticSavingsPlans.filter(plan => plan.id !== planId);
      await this.saveAutomaticSavingsPlans();
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting plan:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
const automaticSavingsService = new AutomaticSavingsService();
export default automaticSavingsService;
