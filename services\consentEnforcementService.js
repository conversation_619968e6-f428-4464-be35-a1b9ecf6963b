/**
 * Consent Enforcement Service for JiraniPay
 * 
 * Production-ready service that enforces user consent choices across all app features.
 * Ensures compliance with East African data protection regulations and GDPR.
 */

import privacyManagementService from './privacyManagementService';
import { isProductionMode } from '../config/environment';
import * as SecureStore from 'expo-secure-store';

class ConsentEnforcementService {
  constructor() {
    this.consentCache = new Map();
    this.analyticsQueue = [];
    this.marketingQueue = [];
    this.initialized = false;
  }

  /**
   * Initialize consent enforcement
   */
  async initialize(userId) {
    try {
      console.log('🔒 Initializing consent enforcement for user:', userId);

      // ✅ FIX: Handle undefined or invalid user ID
      if (!userId || userId === 'undefined' || typeof userId !== 'string') {
        console.warn('⚠️ Skipping consent enforcement initialization - invalid user ID:', userId);
        this.initialized = true; // Mark as initialized to prevent blocking
        return;
      }

      // Load current consent settings
      const settings = await privacyManagementService.getPrivacySettings(userId);

      if (settings.success) {
        this.consentCache.set(userId, settings.data.consent_given);
        console.log('✅ Consent settings loaded:', settings.data.consent_given);
      } else {
        console.warn('⚠️ Could not load consent settings:', settings.error);
      }

      this.initialized = true;

      // Process any queued events
      await this.processQueuedEvents(userId);

    } catch (error) {
      console.error('❌ Error initializing consent enforcement:', error);
      // Mark as initialized even on error to prevent blocking
      this.initialized = true;
    }
  }

  /**
   * Check if user has given consent for specific type
   */
  async hasConsent(userId, consentType) {
    try {
      // Check cache first
      const cachedConsent = this.consentCache.get(userId);
      if (cachedConsent) {
        return cachedConsent[consentType] || false;
      }

      // Fallback to database
      return await privacyManagementService.hasConsent(userId, consentType);
    } catch (error) {
      console.error('❌ Error checking consent:', error);
      // Default to false for non-essential consent
      return consentType === 'essential';
    }
  }

  /**
   * Track analytics event (only if consent given)
   */
  async trackAnalytics(userId, eventName, eventData = {}) {
    try {
      const hasAnalyticsConsent = await this.hasConsent(userId, 'analytics');
      
      if (!hasAnalyticsConsent) {
        console.log('🚫 Analytics tracking blocked - no consent');
        return { success: false, reason: 'no_consent' };
      }

      // In production, integrate with actual analytics service
      if (isProductionMode()) {
        return await this.sendToAnalyticsService(userId, eventName, eventData);
      } else {
        console.log('📊 Analytics Event (Dev):', { eventName, eventData, userId });
        return { success: true, mode: 'development' };
      }
    } catch (error) {
      console.error('❌ Error tracking analytics:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Send marketing communication (only if consent given)
   */
  async sendMarketing(userId, type, content) {
    try {
      const hasMarketingConsent = await this.hasConsent(userId, 'marketing');
      
      if (!hasMarketingConsent) {
        console.log('🚫 Marketing communication blocked - no consent');
        return { success: false, reason: 'no_consent' };
      }

      // Check specific communication preferences
      const settings = await privacyManagementService.getPrivacySettings(userId);
      if (settings.success) {
        const { marketing_emails, marketing_sms } = settings.data;
        
        if (type === 'email' && !marketing_emails) {
          console.log('🚫 Email marketing blocked - preference disabled');
          return { success: false, reason: 'email_disabled' };
        }
        
        if (type === 'sms' && !marketing_sms) {
          console.log('🚫 SMS marketing blocked - preference disabled');
          return { success: false, reason: 'sms_disabled' };
        }
      }

      // In production, integrate with actual marketing service
      if (isProductionMode()) {
        return await this.sendToMarketingService(userId, type, content);
      } else {
        console.log('📧 Marketing Message (Dev):', { type, content, userId });
        return { success: true, mode: 'development' };
      }
    } catch (error) {
      console.error('❌ Error sending marketing:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Share data with partners (only if consent given)
   */
  async shareData(userId, partnerId, dataType, data) {
    try {
      const hasDataSharingConsent = await this.hasConsent(userId, 'dataSharing');
      
      if (!hasDataSharingConsent) {
        console.log('🚫 Data sharing blocked - no consent');
        return { success: false, reason: 'no_consent' };
      }

      // Log data sharing event for audit
      await privacyManagementService.logPrivacyEvent(userId, 'data_shared', {
        partner_id: partnerId,
        data_type: dataType,
        timestamp: new Date().toISOString()
      });

      // In production, implement actual data sharing
      if (isProductionMode()) {
        return await this.shareWithPartner(userId, partnerId, dataType, data);
      } else {
        console.log('🔗 Data Sharing (Dev):', { partnerId, dataType, userId });
        return { success: true, mode: 'development' };
      }
    } catch (error) {
      console.error('❌ Error sharing data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Track location (only if consent given)
   */
  async trackLocation(userId, locationData) {
    try {
      const hasLocationConsent = await this.hasConsent(userId, 'locationTracking');
      
      if (!hasLocationConsent) {
        console.log('🚫 Location tracking blocked - no consent');
        return { success: false, reason: 'no_consent' };
      }

      // In production, store location data securely
      if (isProductionMode()) {
        return await this.storeLocationData(userId, locationData);
      } else {
        console.log('📍 Location Tracking (Dev):', { locationData, userId });
        return { success: true, mode: 'development' };
      }
    } catch (error) {
      console.error('❌ Error tracking location:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update consent and clear relevant caches
   */
  async updateConsent(userId, consentType, granted) {
    try {
      // Update in privacy service
      const result = await privacyManagementService.updateConsent(userId, consentType, granted);
      
      if (result.success) {
        // Update cache
        const currentConsent = this.consentCache.get(userId) || {};
        currentConsent[consentType] = granted;
        this.consentCache.set(userId, currentConsent);
        
        // If consent was revoked, clear related data
        if (!granted) {
          await this.handleConsentRevocation(userId, consentType);
        }
        
        console.log(`✅ Consent updated: ${consentType} = ${granted}`);
      }
      
      return result;
    } catch (error) {
      console.error('❌ Error updating consent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle consent revocation - clean up data
   */
  async handleConsentRevocation(userId, consentType) {
    try {
      console.log(`🧹 Handling consent revocation: ${consentType}`);
      
      switch (consentType) {
        case 'analytics':
          // Clear analytics data
          await this.clearAnalyticsData(userId);
          break;
          
        case 'marketing':
          // Unsubscribe from marketing
          await this.unsubscribeMarketing(userId);
          break;
          
        case 'dataSharing':
          // Revoke data sharing agreements
          await this.revokeDataSharing(userId);
          break;
          
        case 'locationTracking':
          // Clear location data
          await this.clearLocationData(userId);
          break;
      }
      
      // Log the revocation
      await privacyManagementService.logPrivacyEvent(userId, 'consent_revoked', {
        consent_type: consentType,
        timestamp: new Date().toISOString()
      });
      
    } catch (error) {
      console.error('❌ Error handling consent revocation:', error);
    }
  }

  // Production integration methods (to be implemented with actual services)
  async sendToAnalyticsService(userId, eventName, eventData) {
    // TODO: Integrate with actual analytics service (e.g., Mixpanel, Amplitude)
    console.log('📊 Would send to analytics service:', { userId, eventName, eventData });
    return { success: true, service: 'analytics' };
  }

  async sendToMarketingService(userId, type, content) {
    // TODO: Integrate with actual marketing service (e.g., SendGrid, Mailchimp)
    console.log('📧 Would send to marketing service:', { userId, type, content });
    return { success: true, service: 'marketing' };
  }

  async shareWithPartner(userId, partnerId, dataType, data) {
    // TODO: Implement secure data sharing with partners
    console.log('🔗 Would share with partner:', { userId, partnerId, dataType });
    return { success: true, service: 'data_sharing' };
  }

  async storeLocationData(userId, locationData) {
    // TODO: Implement secure location data storage
    console.log('📍 Would store location data:', { userId, locationData });
    return { success: true, service: 'location' };
  }

  async clearAnalyticsData(userId) {
    console.log('🧹 Clearing analytics data for user:', userId);
    // TODO: Implement analytics data cleanup
  }

  async unsubscribeMarketing(userId) {
    console.log('🧹 Unsubscribing from marketing for user:', userId);
    // TODO: Implement marketing unsubscribe
  }

  async revokeDataSharing(userId) {
    console.log('🧹 Revoking data sharing for user:', userId);
    // TODO: Implement data sharing revocation
  }

  async clearLocationData(userId) {
    console.log('🧹 Clearing location data for user:', userId);
    // TODO: Implement location data cleanup
  }

  async processQueuedEvents(userId) {
    // Process any events that were queued before consent was loaded
    console.log('📋 Processing queued events for user:', userId);
  }
}

export default new ConsentEnforcementService();
