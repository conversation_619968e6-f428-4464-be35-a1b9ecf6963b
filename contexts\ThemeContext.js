import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StatusBar } from 'expo-status-bar';
import Colors from '../constants/Colors';

/**
 * Theme Context for managing app-wide theme state
 * Provides dark/light mode functionality with persistence
 */
export const ThemeContext = createContext({
  isDarkMode: false,
  theme: {
    colors: {
      background: '#FFFFFF',
      text: '#000000',
      primary: '#E67E22',
      secondary: '#2C3E50',
      accent: '#F39C12',
      success: '#27AE60',
      warning: '#F39C12',
      error: '#C0392B',
      info: '#3498DB',
    },
    statusBar: 'dark',
  },
  toggleTheme: () => {},
  setTheme: () => {},
  isInitialized: true,
});

/**
 * Light Theme Configuration
 */
const lightTheme = {
  mode: 'light',
  colors: {
    // Primary colors
    primary: Colors.primary.main || '#E67E22',
    primaryLight: Colors.primary.light || '#F39C12',
    primaryDark: Colors.primary.dark || '#D35400',
    
    // Background colors
    background: '#fcf7f0',
    surface: '#fcf7f0',
    card: '#fcf7f0',
    
    // Text colors
    text: '#2C3E50',
    textSecondary: '#95A5A6',
    textMuted: '#BDC3C7',
    
    // Border colors
    border: '#BDC3C7',
    divider: '#F8F9FA',
    
    // Status colors
    success: Colors.status?.success || '#27AE60',
    warning: Colors.status?.warning || '#F39C12',
    error: Colors.status?.error || '#C0392B',
    info: Colors.primary?.light || '#F39C12',
    
    // Accent colors
    accent: Colors.accent?.gold || '#F39C12',
    accentSecondary: Colors.accent.coral,
    
    // Navigation
    tabBarBackground: Colors.neutral.appBackground,
    tabBarActive: Colors.primary?.main || '#E67E22',
    tabBarInactive: Colors.neutral.warmGray,
    
    // Input colors
    inputBackground: Colors.neutral.inputBackground,
    inputBorder: Colors.neutral.warmGrayLight,
    inputText: Colors.neutral.charcoal,
    placeholder: Colors.neutral.warmGray,

    // Shadow
    shadow: Colors.shadow.light,

    // Tab Bar colors
    tabBarBackground: Colors.neutral.appBackground,
    tabBarActive: Colors.primary.main,
    tabBarInactive: Colors.neutral.warmGray,
  },
  statusBar: 'dark',
};

/**
 * Dark Theme Configuration
 */
const darkTheme = {
  mode: 'dark',
  colors: {
    // Primary colors (slightly adjusted for dark mode)
    primary: Colors.primary?.light || '#F39C12',
    primaryLight: Colors.primary?.main || '#E67E22',
    primaryDark: Colors.primary?.dark || '#D35400',

    // Background colors
    background: '#121212',
    surface: '#1E1E1E',
    card: '#2D2D2D',

    // Text colors
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textMuted: '#808080',

    // Border colors
    border: '#404040',
    divider: '#333333',

    // Status colors
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: Colors.primary?.light || '#F39C12',

    // Accent colors
    accent: Colors.accent?.gold || '#F39C12',
    accentSecondary: Colors.accent?.coral || '#FF6B6B',
    
    // Navigation
    tabBarBackground: '#1E1E1E',
    tabBarActive: Colors.primary.light,
    tabBarInactive: '#808080',
    
    // Input colors
    inputBackground: '#2D2D2D',
    inputBorder: '#404040',
    inputText: '#FFFFFF',
    placeholder: '#808080',

    // Shadow
    shadow: '#000000',

    // Tab Bar colors
    tabBarBackground: '#1E1E1E',
    tabBarActive: Colors.primary.light,
    tabBarInactive: '#808080',
  },
  statusBar: 'light',
};

/**
 * Theme Provider Component
 * Wraps the app to provide theme context to all components
 */
export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load theme preference from storage
  const loadThemePreference = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('theme_preference');
      if (savedTheme !== null) {
        const isDark = savedTheme === 'dark';
        setIsDarkMode(isDark);
      }
      setIsInitialized(true);
    } catch (error) {
      console.error('Error loading theme preference:', error);
      setIsInitialized(true);
    }
  };

  // Save theme preference to storage
  const saveThemePreference = async (isDark) => {
    try {
      await AsyncStorage.setItem('theme_preference', isDark ? 'dark' : 'light');
    } catch (error) {
      console.error('Error saving theme preference:', error);
    }
  };

  // Toggle between dark and light theme
  const toggleTheme = async () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    await saveThemePreference(newTheme);
  };

  // Set specific theme mode
  const setTheme = async (isDark) => {
    setIsDarkMode(isDark);
    await saveThemePreference(isDark);
  };

  // Initialize theme on mount
  useEffect(() => {
    loadThemePreference();
  }, []);

  // Get current theme object
  const currentTheme = isDarkMode ? darkTheme : lightTheme;

  const contextValue = {
    isDarkMode,
    theme: currentTheme,
    toggleTheme,
    setTheme,
    isInitialized,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <StatusBar style={currentTheme.statusBar} />
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Hook to use theme context
 * @returns {Object} - Theme context value
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

/**
 * HOC to inject theme props into components
 */
export const withTheme = (Component) => {
  return (props) => {
    const theme = useTheme();
    return <Component {...props} theme={theme} />;
  };
};

export default ThemeContext;
