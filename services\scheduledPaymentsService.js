import AsyncStorage from '@react-native-async-storage/async-storage';
import notificationService from './notificationService';

class ScheduledPaymentsService {
  constructor() {
    this.isInitialized = false;
    this.scheduledPayments = [];
    this.autoSaveGoals = [];
    this.checkInterval = null;
  }

  async initialize() {
    try {
      console.log('⏰ Initializing scheduled payments service...');
      
      // Load stored data
      await this.loadScheduledPayments();
      await this.loadAutoSaveGoals();
      
      // Start background checking
      this.startBackgroundChecking();
      
      this.isInitialized = true;
      console.log('✅ Scheduled payments service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Error initializing scheduled payments service:', error);
      return { success: false, error: error.message };
    }
  }

  async loadScheduledPayments() {
    try {
      const stored = await AsyncStorage.getItem('scheduled_payments');
      if (stored) {
        this.scheduledPayments = JSON.parse(stored).map(payment => ({
          ...payment,
          nextDueDate: new Date(payment.nextDueDate),
          createdAt: new Date(payment.createdAt),
          lastExecuted: payment.lastExecuted ? new Date(payment.lastExecuted) : null
        }));
        console.log('⏰ Loaded', this.scheduledPayments.length, 'scheduled payments');
      }
    } catch (error) {
      console.error('❌ Error loading scheduled payments:', error);
      this.scheduledPayments = [];
    }
  }

  async loadAutoSaveGoals() {
    try {
      const stored = await AsyncStorage.getItem('auto_save_goals');
      if (stored) {
        this.autoSaveGoals = JSON.parse(stored).map(goal => ({
          ...goal,
          createdAt: new Date(goal.createdAt),
          targetDate: goal.targetDate ? new Date(goal.targetDate) : null,
          lastSaved: goal.lastSaved ? new Date(goal.lastSaved) : null
        }));
        console.log('💰 Loaded', this.autoSaveGoals.length, 'auto-save goals');
      }
    } catch (error) {
      console.error('❌ Error loading auto-save goals:', error);
      this.autoSaveGoals = [];
    }
  }

  async saveScheduledPayments() {
    try {
      await AsyncStorage.setItem('scheduled_payments', JSON.stringify(this.scheduledPayments));
    } catch (error) {
      console.error('❌ Error saving scheduled payments:', error);
    }
  }

  async saveAutoSaveGoals() {
    try {
      await AsyncStorage.setItem('auto_save_goals', JSON.stringify(this.autoSaveGoals));
    } catch (error) {
      console.error('❌ Error saving auto-save goals:', error);
    }
  }

  async createScheduledPayment(paymentData) {
    try {
      const {
        name,
        description,
        amount,
        recipient,
        paymentType, // 'bill', 'transfer', 'airtime'
        frequency, // 'daily', 'weekly', 'monthly', 'yearly'
        startDate,
        endDate = null,
        isActive = true,
        reminderMinutes = 60 // Remind 1 hour before
      } = paymentData;

      const payment = {
        id: `scheduled_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        amount,
        recipient,
        paymentType,
        frequency,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        nextDueDate: this.calculateNextDueDate(new Date(startDate), frequency),
        isActive,
        reminderMinutes,
        createdAt: new Date(),
        lastExecuted: null,
        executionCount: 0,
        failureCount: 0
      };

      this.scheduledPayments.push(payment);
      await this.saveScheduledPayments();

      console.log('⏰ Scheduled payment created:', payment.name);
      return { success: true, payment };
    } catch (error) {
      console.error('❌ Error creating scheduled payment:', error);
      return { success: false, error: error.message };
    }
  }

  async createAutoSaveGoal(goalData) {
    try {
      const {
        name,
        description,
        targetAmount,
        currentAmount = 0,
        autoSaveAmount,
        frequency, // 'daily', 'weekly', 'monthly'
        targetDate = null,
        isActive = true
      } = goalData;

      const goal = {
        id: `autosave_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        targetAmount,
        currentAmount,
        autoSaveAmount,
        frequency,
        targetDate: targetDate ? new Date(targetDate) : null,
        isActive,
        createdAt: new Date(),
        lastSaved: null,
        nextSaveDate: this.calculateNextDueDate(new Date(), frequency)
      };

      this.autoSaveGoals.push(goal);
      await this.saveAutoSaveGoals();

      console.log('💰 Auto-save goal created:', goal.name);
      return { success: true, goal };
    } catch (error) {
      console.error('❌ Error creating auto-save goal:', error);
      return { success: false, error: error.message };
    }
  }

  calculateNextDueDate(startDate, frequency) {
    const date = new Date(startDate);
    
    switch (frequency) {
      case 'daily':
        date.setDate(date.getDate() + 1);
        break;
      case 'weekly':
        date.setDate(date.getDate() + 7);
        break;
      case 'monthly':
        date.setMonth(date.getMonth() + 1);
        break;
      case 'yearly':
        date.setFullYear(date.getFullYear() + 1);
        break;
      default:
        date.setDate(date.getDate() + 1);
    }
    
    return date;
  }

  startBackgroundChecking() {
    // Check every 5 minutes for due payments/savings
    this.checkInterval = setInterval(() => {
      this.checkDuePayments();
      this.checkDueSavings();
    }, 5 * 60 * 1000);
  }

  stopBackgroundChecking() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  async checkDuePayments() {
    const now = new Date();
    
    for (const payment of this.scheduledPayments) {
      if (!payment.isActive) continue;
      
      // Check if payment is due
      if (payment.nextDueDate <= now) {
        await this.executeScheduledPayment(payment);
      }
      
      // Check if reminder should be sent
      const reminderTime = new Date(payment.nextDueDate.getTime() - (payment.reminderMinutes * 60 * 1000));
      if (reminderTime <= now && reminderTime > new Date(now.getTime() - 5 * 60 * 1000)) {
        await this.sendPaymentReminder(payment);
      }
    }
  }

  async checkDueSavings() {
    const now = new Date();
    
    for (const goal of this.autoSaveGoals) {
      if (!goal.isActive) continue;
      if (goal.currentAmount >= goal.targetAmount) continue;
      
      // Check if saving is due
      if (goal.nextSaveDate <= now) {
        await this.executeAutoSave(goal);
      }
    }
  }

  async executeScheduledPayment(payment) {
    try {
      console.log('⏰ Executing scheduled payment:', payment.name);
      
      // In a real app, this would integrate with the payment system
      // For now, we'll simulate the payment
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        payment.lastExecuted = new Date();
        payment.executionCount += 1;
        payment.nextDueDate = this.calculateNextDueDate(payment.nextDueDate, payment.frequency);
        
        // Send success notification
        await notificationService.scheduleTransactionNotification({
          id: `scheduled_${payment.id}`,
          type: 'debit',
          amount: payment.amount,
          description: `Scheduled payment: ${payment.name}`,
          status: 'completed'
        });
        
        console.log('✅ Scheduled payment executed successfully');
      } else {
        payment.failureCount += 1;
        
        // Send failure notification
        await notificationService.scheduleTransactionNotification({
          id: `scheduled_${payment.id}`,
          type: 'debit',
          amount: payment.amount,
          description: `Scheduled payment failed: ${payment.name}`,
          status: 'failed'
        });
        
        console.log('❌ Scheduled payment failed');
      }
      
      await this.saveScheduledPayments();
    } catch (error) {
      console.error('❌ Error executing scheduled payment:', error);
    }
  }

  async executeAutoSave(goal) {
    try {
      console.log('💰 Executing auto-save:', goal.name);
      
      const saveAmount = Math.min(goal.autoSaveAmount, goal.targetAmount - goal.currentAmount);
      
      // In a real app, this would transfer money to savings
      // For now, we'll simulate the save
      const success = Math.random() > 0.05; // 95% success rate
      
      if (success) {
        goal.currentAmount += saveAmount;
        goal.lastSaved = new Date();
        goal.nextSaveDate = this.calculateNextDueDate(goal.nextSaveDate, goal.frequency);
        
        // Check if goal is completed
        if (goal.currentAmount >= goal.targetAmount) {
          goal.isActive = false;
          
          // Send goal completion notification
          await notificationService.scheduleTransactionNotification({
            id: `goal_completed_${goal.id}`,
            type: 'credit',
            amount: goal.targetAmount,
            description: `Savings goal completed: ${goal.name}`,
            status: 'completed'
          });
        } else {
          // Send regular save notification
          await notificationService.scheduleTransactionNotification({
            id: `autosave_${goal.id}`,
            type: 'debit',
            amount: saveAmount,
            description: `Auto-save: ${goal.name}`,
            status: 'completed'
          });
        }
        
        console.log('✅ Auto-save executed successfully');
      } else {
        console.log('❌ Auto-save failed');
      }
      
      await this.saveAutoSaveGoals();
    } catch (error) {
      console.error('❌ Error executing auto-save:', error);
    }
  }

  async sendPaymentReminder(payment) {
    try {
      await notificationService.scheduleTransactionNotification({
        id: `reminder_${payment.id}`,
        type: 'debit',
        amount: payment.amount,
        description: `Reminder: ${payment.name} is due soon`,
        status: 'pending'
      });
      
      console.log('🔔 Payment reminder sent for:', payment.name);
    } catch (error) {
      console.error('❌ Error sending payment reminder:', error);
    }
  }

  getScheduledPayments() {
    return this.scheduledPayments.filter(p => p.isActive);
  }

  getAutoSaveGoals() {
    return this.autoSaveGoals.filter(g => g.isActive);
  }

  async updateScheduledPayment(paymentId, updates) {
    try {
      const index = this.scheduledPayments.findIndex(p => p.id === paymentId);
      if (index === -1) {
        throw new Error('Scheduled payment not found');
      }
      
      this.scheduledPayments[index] = {
        ...this.scheduledPayments[index],
        ...updates
      };
      
      await this.saveScheduledPayments();
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating scheduled payment:', error);
      return { success: false, error: error.message };
    }
  }

  async updateAutoSaveGoal(goalId, updates) {
    try {
      const index = this.autoSaveGoals.findIndex(g => g.id === goalId);
      if (index === -1) {
        throw new Error('Auto-save goal not found');
      }
      
      this.autoSaveGoals[index] = {
        ...this.autoSaveGoals[index],
        ...updates
      };
      
      await this.saveAutoSaveGoals();
      return { success: true };
    } catch (error) {
      console.error('❌ Error updating auto-save goal:', error);
      return { success: false, error: error.message };
    }
  }

  async deleteScheduledPayment(paymentId) {
    try {
      this.scheduledPayments = this.scheduledPayments.filter(p => p.id !== paymentId);
      await this.saveScheduledPayments();
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting scheduled payment:', error);
      return { success: false, error: error.message };
    }
  }

  async deleteAutoSaveGoal(goalId) {
    try {
      this.autoSaveGoals = this.autoSaveGoals.filter(g => g.id !== goalId);
      await this.saveAutoSaveGoals();
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting auto-save goal:', error);
      return { success: false, error: error.message };
    }
  }

  cleanup() {
    this.stopBackgroundChecking();
  }
}

// Create and export singleton instance
const scheduledPaymentsService = new ScheduledPaymentsService();
export default scheduledPaymentsService;
