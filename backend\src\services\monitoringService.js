/**
 * Monitoring and Observability Service
 * Comprehensive system monitoring, metrics collection, and alerting
 */

const os = require('os');
const process = require('process');
const config = require('../config/config');
const logger = require('../utils/logger');
const redisService = require('./redis');
const databaseService = require('./database');

class MonitoringService {
  constructor() {
    this.metrics = {
      system: {},
      application: {},
      business: {},
      errors: {},
      performance: {}
    };
    
    this.alerts = {
      thresholds: {
        cpuUsage: 80, // 80%
        memoryUsage: 85, // 85%
        diskUsage: 90, // 90%
        responseTime: 2000, // 2 seconds
        errorRate: 5, // 5%
        transactionFailureRate: 2 // 2%
      },
      channels: ['email', 'slack', 'sms']
    };
    
    this.healthChecks = new Map();
    this.isMonitoring = false;
  }

  /**
   * Initialize monitoring service
   */
  async initialize() {
    try {
      // Start system metrics collection
      await this.startSystemMonitoring();
      
      // Initialize health checks
      await this.initializeHealthChecks();
      
      // Start business metrics collection
      await this.startBusinessMetricsCollection();
      
      // Set up alerting
      await this.setupAlerting();
      
      this.isMonitoring = true;
      logger.info('Monitoring service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize monitoring service:', error);
      throw error;
    }
  }

  /**
   * Start system metrics monitoring
   */
  async startSystemMonitoring() {
    try {
      // Collect system metrics every 30 seconds
      setInterval(async () => {
        await this.collectSystemMetrics();
      }, 30000);
      
      // Collect application metrics every 60 seconds
      setInterval(async () => {
        await this.collectApplicationMetrics();
      }, 60000);
      
      logger.info('System monitoring started');
    } catch (error) {
      logger.error('Failed to start system monitoring:', error);
    }
  }

  /**
   * Collect system metrics
   */
  async collectSystemMetrics() {
    try {
      const systemMetrics = {
        timestamp: new Date().toISOString(),
        cpu: {
          usage: await this.getCPUUsage(),
          loadAverage: os.loadavg(),
          cores: os.cpus().length
        },
        memory: {
          total: os.totalmem(),
          free: os.freemem(),
          used: os.totalmem() - os.freemem(),
          usage: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
          process: process.memoryUsage()
        },
        disk: await this.getDiskUsage(),
        network: await this.getNetworkStats(),
        uptime: {
          system: os.uptime(),
          process: process.uptime()
        }
      };
      
      this.metrics.system = systemMetrics;
      
      // Cache metrics for dashboard
      await redisService.set('system_metrics', systemMetrics, 300);
      
      // Check for alerts
      await this.checkSystemAlerts(systemMetrics);
      
    } catch (error) {
      logger.error('Failed to collect system metrics:', error);
    }
  }

  /**
   * Collect application metrics
   */
  async collectApplicationMetrics() {
    try {
      const appMetrics = {
        timestamp: new Date().toISOString(),
        requests: await this.getRequestMetrics(),
        responses: await this.getResponseMetrics(),
        errors: await this.getErrorMetrics(),
        database: await this.getDatabaseMetrics(),
        cache: await this.getCacheMetrics(),
        queues: await this.getQueueMetrics()
      };
      
      this.metrics.application = appMetrics;
      
      // Cache metrics
      await redisService.set('app_metrics', appMetrics, 300);
      
      // Check for alerts
      await this.checkApplicationAlerts(appMetrics);
      
    } catch (error) {
      logger.error('Failed to collect application metrics:', error);
    }
  }

  /**
   * Get CPU usage percentage
   */
  async getCPUUsage() {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = process.hrtime();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = process.hrtime(startTime);
        
        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000; // microseconds
        const cpuTime = (endUsage.user + endUsage.system); // microseconds
        
        const cpuPercent = (cpuTime / totalTime) * 100;
        resolve(Math.min(100, Math.max(0, cpuPercent)));
      }, 100);
    });
  }

  /**
   * Get disk usage
   */
  async getDiskUsage() {
    try {
      const fs = require('fs').promises;
      const stats = await fs.stat('.');
      
      // This is a simplified version - in production, use a proper disk usage library
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0
      };
    } catch (error) {
      return { total: 0, used: 0, free: 0, usage: 0 };
    }
  }

  /**
   * Get network statistics
   */
  async getNetworkStats() {
    try {
      // Get network interface statistics
      const interfaces = os.networkInterfaces();
      const stats = {};
      
      Object.keys(interfaces).forEach(name => {
        const iface = interfaces[name];
        stats[name] = {
          addresses: iface.map(addr => ({
            address: addr.address,
            family: addr.family,
            internal: addr.internal
          }))
        };
      });
      
      return stats;
    } catch (error) {
      return {};
    }
  }

  /**
   * Get request metrics
   */
  async getRequestMetrics() {
    try {
      const requestStats = await redisService.get('request_stats') || {
        total: 0,
        perMinute: 0,
        perHour: 0,
        byEndpoint: {},
        byMethod: {}
      };
      
      return requestStats;
    } catch (error) {
      return { total: 0, perMinute: 0, perHour: 0, byEndpoint: {}, byMethod: {} };
    }
  }

  /**
   * Get response metrics
   */
  async getResponseMetrics() {
    try {
      const responseStats = await redisService.get('response_stats') || {
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        statusCodes: {},
        slowRequests: []
      };
      
      return responseStats;
    } catch (error) {
      return { averageResponseTime: 0, p95ResponseTime: 0, p99ResponseTime: 0, statusCodes: {}, slowRequests: [] };
    }
  }

  /**
   * Get error metrics
   */
  async getErrorMetrics() {
    try {
      const errorStats = await redisService.get('error_stats') || {
        total: 0,
        rate: 0,
        byType: {},
        recent: []
      };
      
      return errorStats;
    } catch (error) {
      return { total: 0, rate: 0, byType: {}, recent: [] };
    }
  }

  /**
   * Get database metrics
   */
  async getDatabaseMetrics() {
    try {
      const dbStats = await redisService.get('db_pool_stats') || {};
      const slowQueries = await redisService.get('slow_queries') || [];
      
      return {
        connectionPool: dbStats,
        slowQueries: slowQueries.length,
        queryPerformance: {
          averageQueryTime: 0,
          slowestQuery: null
        }
      };
    } catch (error) {
      return { connectionPool: {}, slowQueries: 0, queryPerformance: {} };
    }
  }

  /**
   * Get cache metrics
   */
  async getCacheMetrics() {
    try {
      const cacheStats = await redisService.getStats();
      
      return {
        hitRate: cacheStats.hitRate || 0,
        missRate: cacheStats.missRate || 0,
        keyCount: cacheStats.keyCount || 0,
        memoryUsage: cacheStats.memoryUsage || 0
      };
    } catch (error) {
      return { hitRate: 0, missRate: 0, keyCount: 0, memoryUsage: 0 };
    }
  }

  /**
   * Get queue metrics
   */
  async getQueueMetrics() {
    try {
      // Placeholder for queue metrics (Redis queues, Bull queues, etc.)
      return {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };
    } catch (error) {
      return { pending: 0, processing: 0, completed: 0, failed: 0 };
    }
  }

  /**
   * Initialize health checks
   */
  async initializeHealthChecks() {
    try {
      // Database health check
      this.healthChecks.set('database', async () => {
        try {
          const result = await databaseService.healthCheck();
          return { status: 'healthy', details: result };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      });
      
      // Redis health check
      this.healthChecks.set('redis', async () => {
        try {
          const result = await redisService.healthCheck();
          return { status: 'healthy', details: result };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      });
      
      // External services health check
      this.healthChecks.set('external_services', async () => {
        try {
          // Check SMS service, payment providers, etc.
          return { status: 'healthy', details: {} };
        } catch (error) {
          return { status: 'unhealthy', error: error.message };
        }
      });
      
      logger.info('Health checks initialized');
    } catch (error) {
      logger.error('Failed to initialize health checks:', error);
    }
  }

  /**
   * Start business metrics collection
   */
  async startBusinessMetricsCollection() {
    try {
      // Collect business metrics every 5 minutes
      setInterval(async () => {
        await this.collectBusinessMetrics();
      }, 5 * 60 * 1000);
      
      logger.info('Business metrics collection started');
    } catch (error) {
      logger.error('Failed to start business metrics collection:', error);
    }
  }

  /**
   * Collect business metrics
   */
  async collectBusinessMetrics() {
    try {
      const supabase = databaseService.getSupabase();
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      // Get transaction metrics
      const { data: transactionStats } = await supabase.rpc('get_transaction_stats', {
        start_date: today.toISOString()
      }).catch(() => ({ data: {} }));
      
      // Get user metrics
      const { data: userStats } = await supabase.rpc('get_user_stats', {
        start_date: today.toISOString()
      }).catch(() => ({ data: {} }));
      
      const businessMetrics = {
        timestamp: new Date().toISOString(),
        transactions: transactionStats || {},
        users: userStats || {},
        revenue: {
          daily: 0,
          monthly: 0,
          fees: 0
        },
        wallets: {
          totalBalance: 0,
          activeWallets: 0
        }
      };
      
      this.metrics.business = businessMetrics;
      
      // Cache metrics
      await redisService.set('business_metrics', businessMetrics, 300);
      
    } catch (error) {
      logger.error('Failed to collect business metrics:', error);
    }
  }

  /**
   * Setup alerting system
   */
  async setupAlerting() {
    try {
      // Check for alerts every minute
      setInterval(async () => {
        await this.checkAllAlerts();
      }, 60000);
      
      logger.info('Alerting system setup completed');
    } catch (error) {
      logger.error('Failed to setup alerting:', error);
    }
  }

  /**
   * Check system alerts
   */
  async checkSystemAlerts(metrics) {
    try {
      const alerts = [];
      
      // CPU usage alert
      if (metrics.cpu.usage > this.alerts.thresholds.cpuUsage) {
        alerts.push({
          type: 'system',
          severity: 'warning',
          message: `High CPU usage: ${metrics.cpu.usage.toFixed(2)}%`,
          threshold: this.alerts.thresholds.cpuUsage,
          value: metrics.cpu.usage
        });
      }
      
      // Memory usage alert
      if (metrics.memory.usage > this.alerts.thresholds.memoryUsage) {
        alerts.push({
          type: 'system',
          severity: 'warning',
          message: `High memory usage: ${metrics.memory.usage.toFixed(2)}%`,
          threshold: this.alerts.thresholds.memoryUsage,
          value: metrics.memory.usage
        });
      }
      
      // Send alerts if any
      if (alerts.length > 0) {
        await this.sendAlerts(alerts);
      }
      
    } catch (error) {
      logger.error('Failed to check system alerts:', error);
    }
  }

  /**
   * Check application alerts
   */
  async checkApplicationAlerts(metrics) {
    try {
      const alerts = [];
      
      // Response time alert
      if (metrics.responses.averageResponseTime > this.alerts.thresholds.responseTime) {
        alerts.push({
          type: 'application',
          severity: 'warning',
          message: `High response time: ${metrics.responses.averageResponseTime}ms`,
          threshold: this.alerts.thresholds.responseTime,
          value: metrics.responses.averageResponseTime
        });
      }
      
      // Error rate alert
      if (metrics.errors.rate > this.alerts.thresholds.errorRate) {
        alerts.push({
          type: 'application',
          severity: 'critical',
          message: `High error rate: ${metrics.errors.rate}%`,
          threshold: this.alerts.thresholds.errorRate,
          value: metrics.errors.rate
        });
      }
      
      // Send alerts if any
      if (alerts.length > 0) {
        await this.sendAlerts(alerts);
      }
      
    } catch (error) {
      logger.error('Failed to check application alerts:', error);
    }
  }

  /**
   * Check all alerts
   */
  async checkAllAlerts() {
    try {
      if (this.metrics.system.timestamp) {
        await this.checkSystemAlerts(this.metrics.system);
      }
      
      if (this.metrics.application.timestamp) {
        await this.checkApplicationAlerts(this.metrics.application);
      }
      
    } catch (error) {
      logger.error('Failed to check all alerts:', error);
    }
  }

  /**
   * Send alerts
   */
  async sendAlerts(alerts) {
    try {
      for (const alert of alerts) {
        logger.alert(alert.message, alert);
        
        // Store alert for dashboard
        await redisService.lpush('recent_alerts', {
          ...alert,
          timestamp: new Date().toISOString()
        });
        
        // Keep only last 100 alerts
        await redisService.ltrim('recent_alerts', 0, 99);
      }
    } catch (error) {
      logger.error('Failed to send alerts:', error);
    }
  }

  /**
   * Get comprehensive health status
   */
  async getHealthStatus() {
    try {
      const healthStatus = {
        overall: 'healthy',
        timestamp: new Date().toISOString(),
        services: {},
        metrics: {
          system: this.metrics.system,
          application: this.metrics.application,
          business: this.metrics.business
        }
      };
      
      // Run all health checks
      for (const [service, healthCheck] of this.healthChecks) {
        try {
          healthStatus.services[service] = await healthCheck();
        } catch (error) {
          healthStatus.services[service] = {
            status: 'unhealthy',
            error: error.message
          };
        }
      }
      
      // Determine overall health
      const unhealthyServices = Object.values(healthStatus.services)
        .filter(service => service.status === 'unhealthy');
      
      if (unhealthyServices.length > 0) {
        healthStatus.overall = 'unhealthy';
      }
      
      return healthStatus;
    } catch (error) {
      return {
        overall: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get monitoring dashboard data
   */
  async getDashboardData() {
    try {
      const recentAlerts = await redisService.lrange('recent_alerts', 0, 9);
      
      return {
        health: await this.getHealthStatus(),
        metrics: {
          system: this.metrics.system,
          application: this.metrics.application,
          business: this.metrics.business
        },
        alerts: {
          recent: recentAlerts || [],
          thresholds: this.alerts.thresholds
        },
        isMonitoring: this.isMonitoring,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      return {
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const monitoringService = new MonitoringService();

module.exports = monitoringService;
