/**
 * Production Mode Indicator
 * Shows the current environment mode (Development/Production) in the app
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { isProductionMode, getEnvironmentName } from '../config/environment';
import { useTheme } from '../contexts/ThemeContext';

const ProductionModeIndicator = ({ style }) => {
  const { theme } = useTheme();
  const styles = createStyles(theme);
  const currentMode = getEnvironmentName();
  const isProduction = isProductionMode();

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.indicator, isProduction ? styles.productionMode : styles.developmentMode]}>
        <Ionicons 
          name={isProduction ? "shield-checkmark" : "code-slash"} 
          size={16} 
          color={isProduction ? "#E53E3E" : "#38A169"} 
        />
        <Text style={[styles.text, { color: isProduction ? "#E53E3E" : "#38A169" }]}>
          {currentMode.toUpperCase()} MODE
        </Text>
      </View>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 8,
  },
  indicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
  },
  productionMode: {
    backgroundColor: '#FED7D7',
    borderColor: '#E53E3E',
  },
  developmentMode: {
    backgroundColor: '#C6F6D5',
    borderColor: '#38A169',
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },
});

export default ProductionModeIndicator;
