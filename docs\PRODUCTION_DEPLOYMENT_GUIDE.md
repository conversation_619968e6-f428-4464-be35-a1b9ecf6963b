# JiraniPay Production Deployment - Complete Guide

## 🎯 Overview

This comprehensive guide consolidates all production deployment procedures, configurations, and checklists for the JiraniPay application. It replaces multiple scattered documentation files with a single authoritative deployment guide.

## 🚀 Quick Start - Enabling Production Mode

### **Step 1: Set Production Mode Flag**
```javascript
// In config/environment.js
const PRODUCTION_MODE = true; // Change from false to true
```

### **Step 2: Configure Production Services**
```javascript
// In config/productionServices.js
export default {
  supabase: {
    url: 'https://your-production-project.supabase.co',
    anonKey: 'your_production_anon_key',
  },
  apis: {
    mtn: {
      baseUrl: 'https://api.mtn.com',
      apiKey: process.env.MTN_API_KEY,
    },
    airtel: {
      baseUrl: 'https://api.airtel.africa',
      clientId: process.env.AIRTEL_CLIENT_ID,
    }
  }
};
```

### **Step 3: Verify Production Readiness**
```bash
# Run production readiness check
node scripts/verifyProductionReadiness.js
```

## 📋 Pre-Deployment Checklist

### **🔧 Configuration**
- [ ] Set `PRODUCTION_MODE = true` in environment.js
- [ ] Configure production Supabase project
- [ ] Set up production API credentials (MTN, Airtel, UMEME)
- [ ] Configure SMS provider (Twilio production account)
- [ ] Set up email service (SendGrid production)
- [ ] Configure push notification certificates

### **🔒 Security**
- [ ] Remove all development/testing code
- [ ] Verify no hardcoded credentials in source code
- [ ] Enable SSL/TLS for all API communications
- [ ] Configure proper CORS settings
- [ ] Set up rate limiting and DDoS protection
- [ ] Enable audit logging

### **📊 Monitoring & Analytics**
- [ ] Set up error tracking (Sentry, Bugsnag)
- [ ] Configure performance monitoring
- [ ] Enable analytics tracking
- [ ] Set up health check endpoints
- [ ] Configure alerting for critical failures

### **💾 Database**
- [ ] Run production database migrations
- [ ] Set up database backups
- [ ] Configure read replicas if needed
- [ ] Verify RLS (Row Level Security) policies
- [ ] Test database connection pooling

## 🏗️ Infrastructure Setup

### **1. Supabase Production Configuration**

#### **Database Setup**
```sql
-- Run these migrations in production Supabase
-- 1. Create production tables
\i database/complete_setup.sql

-- 2. Set up security policies
\i database/comprehensive_rls_fix.sql

-- 3. Configure indexes for performance
\i database/step2_indexes_and_security.sql
```

#### **Authentication Configuration**
```javascript
// Supabase Auth Settings (in Supabase Dashboard)
{
  "site_url": "https://jiranipay.com",
  "redirect_urls": ["https://jiranipay.com/auth/callback"],
  "jwt_expiry": 3600,
  "enable_signup": true,
  "enable_email_confirmations": true,
  "enable_phone_confirmations": true
}
```

### **2. API Integration Setup**

#### **MTN Mobile Money**
```javascript
// Production MTN configuration
const mtnConfig = {
  environment: 'production',
  baseUrl: 'https://proxy.momoapi.mtn.com',
  subscriptionKey: process.env.MTN_SUBSCRIPTION_KEY,
  apiKey: process.env.MTN_API_KEY,
  targetEnvironment: 'mtnuganda',
  callbackUrl: 'https://api.jiranipay.com/webhooks/mtn'
};
```

#### **Airtel Money**
```javascript
// Production Airtel configuration
const airtelConfig = {
  environment: 'production',
  baseUrl: 'https://openapiuat.airtel.africa',
  clientId: process.env.AIRTEL_CLIENT_ID,
  clientSecret: process.env.AIRTEL_CLIENT_SECRET,
  grantType: 'client_credentials',
  callbackUrl: 'https://api.jiranipay.com/webhooks/airtel'
};
```

### **3. SMS & Email Services**

#### **Twilio SMS Configuration**
```javascript
// Production Twilio setup
const twilioConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  messagingServiceSid: process.env.TWILIO_MESSAGING_SERVICE_SID,
  webhookUrl: 'https://api.jiranipay.com/webhooks/twilio'
};
```

#### **SendGrid Email Configuration**
```javascript
// Production SendGrid setup
const sendGridConfig = {
  apiKey: process.env.SENDGRID_API_KEY,
  fromEmail: '<EMAIL>',
  fromName: 'JiraniPay',
  templates: {
    welcome: 'd-1234567890abcdef',
    otp: 'd-abcdef1234567890',
    transaction: 'd-567890abcdef1234'
  }
};
```

## 🔐 Environment Variables

### **Required Production Environment Variables**
```bash
# Core Configuration
NODE_ENV=production
EXPO_PUBLIC_ENVIRONMENT=production

# Supabase
SUPABASE_URL=https://your-production-project.supabase.co
SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Mobile Money APIs
MTN_API_KEY=your_mtn_production_key
MTN_SUBSCRIPTION_KEY=your_mtn_subscription_key
AIRTEL_CLIENT_ID=your_airtel_client_id
AIRTEL_CLIENT_SECRET=your_airtel_client_secret

# Utility APIs
UMEME_API_KEY=your_umeme_api_key
DSTV_API_KEY=your_dstv_api_key

# Communication Services
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
SENDGRID_API_KEY=your_sendgrid_api_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
ANALYTICS_KEY=your_analytics_key
```

## 🚢 Deployment Process

### **1. Pre-Deployment**
```bash
# 1. Run tests
npm test

# 2. Build production bundle
npm run build:production

# 3. Run security audit
npm audit --audit-level=high

# 4. Verify production configuration
node scripts/verifyProductionConfig.js
```

### **2. Database Migration**
```bash
# Run production migrations
npm run migrate:production

# Verify migration success
npm run verify:database
```

### **3. Application Deployment**

#### **Mobile App Deployment**
```bash
# Build for iOS
eas build --platform ios --profile production

# Build for Android
eas build --platform android --profile production

# Submit to app stores
eas submit --platform all --profile production
```

#### **Backend Deployment**
```bash
# Deploy backend services
docker build -t jiranipay-backend:latest .
docker push your-registry/jiranipay-backend:latest

# Deploy to Kubernetes
kubectl apply -f k8s/production/
```

### **4. Post-Deployment Verification**
```bash
# Health check
curl https://api.jiranipay.com/health

# API functionality test
npm run test:production-api

# End-to-end testing
npm run test:e2e:production
```

## 📊 Monitoring & Maintenance

### **1. Health Monitoring**

#### **Application Health Checks**
```javascript
// Health check endpoint
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseHealth(),
      supabase: await checkSupabaseHealth(),
      mtn: await checkMTNHealth(),
      airtel: await checkAirtelHealth()
    }
  };
  
  res.json(health);
});
```

#### **Key Metrics to Monitor**
- API response times
- Transaction success rates
- SMS delivery rates
- Database connection pool usage
- Error rates by service
- User authentication success rates

### **2. Alerting Configuration**

#### **Critical Alerts**
- API downtime > 1 minute
- Transaction failure rate > 5%
- SMS delivery failure rate > 10%
- Database connection failures
- High error rates (> 1% of requests)

#### **Warning Alerts**
- API response time > 2 seconds
- High memory/CPU usage
- Approaching rate limits
- Low wallet balances for operations

### **3. Backup & Recovery**

#### **Database Backups**
```bash
# Daily automated backups
0 2 * * * pg_dump $DATABASE_URL > /backups/jiranipay_$(date +%Y%m%d).sql

# Weekly full backups with retention
0 1 * * 0 /scripts/full_backup.sh
```

#### **Disaster Recovery Plan**
1. **RTO (Recovery Time Objective)**: 4 hours
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Backup locations**: Primary + 2 geographic regions
4. **Recovery procedures**: Documented and tested monthly

## 🔍 Troubleshooting

### **Common Production Issues**

#### **1. SMS Delivery Failures**
```bash
# Check Twilio status
curl -X GET "https://status.twilio.com/api/v2/status.json"

# Verify webhook endpoints
curl -X POST "https://api.jiranipay.com/webhooks/twilio/test"
```

#### **2. API Integration Issues**
```bash
# Test MTN connectivity
curl -X GET "https://proxy.momoapi.mtn.com/health"

# Verify API credentials
node scripts/testApiCredentials.js
```

#### **3. Database Performance Issues**
```sql
-- Check slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check connection usage
SELECT count(*) as connections, state 
FROM pg_stat_activity 
GROUP BY state;
```

## 📈 Performance Optimization

### **1. Database Optimization**
- Connection pooling (max 20 connections)
- Query optimization with proper indexes
- Read replicas for analytics queries
- Regular VACUUM and ANALYZE operations

### **2. API Optimization**
- Response caching for static data
- Request rate limiting
- Connection keep-alive
- Gzip compression

### **3. Mobile App Optimization**
- Code splitting and lazy loading
- Image optimization and caching
- Offline functionality for core features
- Background sync for non-critical operations

## 🎯 Success Metrics

### **Technical KPIs**
- **Uptime**: > 99.9%
- **API Response Time**: < 500ms (95th percentile)
- **Transaction Success Rate**: > 99%
- **SMS Delivery Rate**: > 95%
- **App Crash Rate**: < 0.1%

### **Business KPIs**
- **User Registration Success Rate**: > 90%
- **Transaction Completion Rate**: > 95%
- **User Retention (30-day)**: > 70%
- **Customer Support Tickets**: < 5% of active users

---

## 📚 Related Documentation

- [Security Implementation Guide](SECURITY_PRIVACY_SETUP.md)
- [API Integration Guide](API_INTEGRATION_GUIDE.md)
- [Monitoring & Alerting](MONITORING_GUIDE.md)
- [Disaster Recovery Plan](DISASTER_RECOVERY.md)

The JiraniPay application is now ready for production deployment! 🚀
