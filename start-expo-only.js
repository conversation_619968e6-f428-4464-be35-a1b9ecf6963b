#!/usr/bin/env node

/**
 * Simple Expo Startup Script with Environment Configuration
 * 
 * This script properly configures the environment and starts only the Expo app
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get environment from command line argument or default to production
const environment = process.argv[2] || 'production';

console.log(`🚀 Starting JiraniPay Expo App in ${environment} mode...\n`);

// Simple environment file parser
function loadEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️ Environment file not found: ${filePath}`);
    return;
  }
  
  console.log(`📁 Loading environment file: ${filePath}`);
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let loadedVars = 0;
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        process.env[key.trim()] = value;
        loadedVars++;
      }
    }
  }
  console.log(`✅ Loaded ${loadedVars} environment variables`);
}

// Set environment variables based on the mode
if (environment === 'production') {
  process.env.EXPO_PUBLIC_ENVIRONMENT = 'production';
  process.env.NODE_ENV = 'production';
  
  // Load production environment file if it exists
  const prodEnvFile = path.join(__dirname, '.env.production.local');
  loadEnvFile(prodEnvFile);
  
} else if (environment === 'development') {
  process.env.EXPO_PUBLIC_ENVIRONMENT = 'development';
  process.env.NODE_ENV = 'development';
  
  // Load development environment file if it exists
  const devEnvFile = path.join(__dirname, '.env.development.local');
  loadEnvFile(devEnvFile);
  
} else if (environment === 'staging') {
  process.env.EXPO_PUBLIC_ENVIRONMENT = 'staging';
  process.env.NODE_ENV = 'staging';
  
  // Load staging environment file if it exists
  const stagingEnvFile = path.join(__dirname, '.env.staging.local');
  loadEnvFile(stagingEnvFile);
}

console.log('\n🔧 Environment Configuration:');
console.log('- EXPO_PUBLIC_ENVIRONMENT:', process.env.EXPO_PUBLIC_ENVIRONMENT);
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- EXPO_PUBLIC_SUPABASE_URL:', process.env.EXPO_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_SUPABASE_ANON_KEY:', process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_PROD_SUPABASE_URL:', process.env.EXPO_PUBLIC_PROD_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('- EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY:', process.env.EXPO_PUBLIC_PROD_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');

console.log('\n✅ Environment configured successfully!');
console.log('\n🚀 READY TO START EXPO:');
console.log('The environment variables have been set correctly.');
console.log('Now run the following command in this same terminal:');
console.log('');
console.log('   expo start');
console.log('');
console.log('Or if expo is not globally installed:');
console.log('');
console.log('   npx expo start');
console.log('');

console.log('\n📋 Expected Behavior:');
console.log('✅ Login screen should NOT show development mode indicator');
console.log('✅ User greeting should show actual username after login');
console.log('✅ App should behave in production mode');
console.log('\n💡 Tips:');
console.log('- Press Ctrl+C to stop the server');
console.log('- Use QR code or type "a" for Android, "i" for iOS');
console.log('- Check the console for any configuration errors');
console.log('\n🔧 Troubleshooting:');
console.log('- If development mode still shows: Check secure configuration');
console.log('- If username missing: Check user profile data in Supabase');
console.log('- For validation: npm run validate-security');
console.log('- For debugging: npm run debug-env');
