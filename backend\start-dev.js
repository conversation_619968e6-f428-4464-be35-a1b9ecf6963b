#!/usr/bin/env node

/**
 * Development Server Startup Script for JiraniPay Backend
 * 
 * This script starts the backend configuration service for local development
 */

const path = require('path');
const fs = require('fs');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '.env.development') });

console.log('🚀 Starting JiraniPay Backend Configuration Service...\n');

// Check if required dependencies are installed
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found. Please run this script from the backend directory.');
  process.exit(1);
}

// Check if node_modules exists
const nodeModulesPath = path.join(__dirname, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installing dependencies...');
  const { execSync } = require('child_process');
  try {
    execSync('npm install', { stdio: 'inherit', cwd: __dirname });
    console.log('✅ Dependencies installed successfully\n');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
}

// Set development environment
process.env.NODE_ENV = 'development';
process.env.PORT = process.env.PORT || '3001';

console.log('🔧 Configuration:');
console.log(`   - Environment: ${process.env.NODE_ENV}`);
console.log(`   - Port: ${process.env.PORT}`);
console.log(`   - API Version: ${process.env.API_VERSION || 'v1'}`);
console.log(`   - Supabase URL: ${process.env.SUPABASE_URL ? 'Configured' : 'Not configured'}`);
console.log('');

// Start the server
try {
  console.log('🔄 Starting server...\n');
  require('./src/server.js');
} catch (error) {
  console.error('❌ Failed to start server:', error.message);
  console.error('\n🔧 Troubleshooting:');
  console.error('1. Make sure all dependencies are installed: npm install');
  console.error('2. Check that the .env.development file exists');
  console.error('3. Verify that port 3001 is not already in use');
  console.error('4. Check the server logs above for specific errors');
  process.exit(1);
}
