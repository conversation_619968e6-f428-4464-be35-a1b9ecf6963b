import { useLanguage } from '../contexts/LanguageContext';

/**
 * Custom hook for translations
 * Provides easy access to translation functions and language state
 * 
 * @returns {Object} - Translation utilities
 */
export const useTranslation = () => {
  const { t, currentLanguage, isRTL, currentLanguageConfig } = useLanguage();
  
  /**
   * Translate with automatic fallback
   * @param {string} key - Translation key
   * @param {Object} params - Parameters for interpolation
   * @param {string} fallback - Fallback text
   * @returns {string} - Translated text
   */
  const translate = (key, params = {}, fallback = key) => {
    const translated = t(key, params);
    return translated === key ? fallback : translated;
  };
  
  /**
   * Get navigation title translation
   * @param {string} screenName - Screen name
   * @returns {string} - Translated title
   */
  const getScreenTitle = (screenName) => {
    return translate(`navigation.${screenName}`, {}, screenName);
  };
  
  /**
   * Get button text translation
   * @param {string} buttonName - Button name
   * @returns {string} - Translated button text
   */
  const getButtonText = (buttonName) => {
    return translate(`buttons.${buttonName}`, {}, buttonName);
  };
  
  /**
   * Get error message translation
   * @param {string} errorKey - Error key
   * @returns {string} - Translated error message
   */
  const getErrorMessage = (errorKey) => {
    return translate(`errors.${errorKey}`, {}, 'An error occurred');
  };
  
  /**
   * Get success message translation
   * @param {string} successKey - Success key
   * @returns {string} - Translated success message
   */
  const getSuccessMessage = (successKey) => {
    return translate(`success.${successKey}`, {}, 'Operation successful');
  };
  
  /**
   * Format currency with translation
   * @param {number} amount - Amount to format
   * @param {string} currency - Currency code
   * @returns {string} - Formatted currency string
   */
  const formatCurrency = (amount, currency) => {
    const currencyName = translate(`currency.${currency.toLowerCase()}`, {}, currency);
    return `${currencyName} ${amount.toLocaleString()}`;
  };
  
  return {
    t: translate,
    currentLanguage,
    isRTL,
    currentLanguageConfig,
    getScreenTitle,
    getButtonText,
    getErrorMessage,
    getSuccessMessage,
    formatCurrency
  };
};

export default useTranslation;
